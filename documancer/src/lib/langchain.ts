import { ChatOpenAI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { RunnableSequence } from '@langchain/core/runnables';

// Initialize DeepSeek model (using OpenAI-compatible API)
export const createDeepSeekModel = () => {
  return new ChatOpenAI({
    model: 'deepseek-chat',
    apiKey: process.env.DEEPSEEK_API_KEY,
    configuration: {
      baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1',
    },
    temperature: 0.1,
    maxTokens: 4000,
  });
};

// Prompt templates for different analysis types
export const PROMPTS = {
  SUMMARIZE: PromptTemplate.fromTemplate(`
    You are an expert academic paper analyst. Please provide a comprehensive summary of the following research paper.

    Paper Content:
    {content}

    Please provide:
    1. A concise abstract summary (2-3 sentences)
    2. Main research question and objectives
    3. Key methodology used
    4. Primary findings and contributions
    5. Significance and implications

    Format your response in clear, structured sections.
  `),

  EXTRACT_KEY_FINDINGS: PromptTemplate.fromTemplate(`
    Analyze the following research paper and extract the key findings and contributions.

    Paper Content:
    {content}

    Please identify and list:
    1. Main research findings (numbered list)
    2. Novel contributions to the field
    3. Experimental results and their significance
    4. Theoretical insights or frameworks introduced
    5. Practical applications or implications

    Be specific and cite relevant sections when possible.
  `),

  EXPLAIN_METHODOLOGY: PromptTemplate.fromTemplate(`
    Analyze the methodology section of this research paper and provide a clear explanation.

    Paper Content:
    {content}

    Please explain:
    1. Research design and approach
    2. Data collection methods
    3. Analysis techniques used
    4. Tools and technologies employed
    5. Experimental setup (if applicable)
    6. Validation methods
    7. Limitations of the methodology

    Make the explanation accessible to researchers in related fields.
  `),

  EXTRACT_CONCEPTS: PromptTemplate.fromTemplate(`
    Identify and explain the key concepts, terms, and technical vocabulary from this research paper.

    Paper Content:
    {content}

    For each important concept, provide:
    1. Term/Concept name
    2. Clear definition in context
    3. Importance level (High/Medium/Low)
    4. Related terms or concepts
    5. How it's used in this specific paper

    Focus on domain-specific terminology and novel concepts introduced.
  `),

  ANSWER_QUESTION: PromptTemplate.fromTemplate(`
    You are an AI assistant helping researchers understand academic papers. Answer the following question based on the paper content provided.

    Paper Content:
    {content}

    Question: {question}

    Please provide a comprehensive answer that:
    1. Directly addresses the question
    2. References specific sections of the paper when relevant
    3. Provides context and background if needed
    4. Mentions any limitations or uncertainties
    5. Suggests related questions or areas for further exploration

    If the question cannot be answered from the provided content, clearly state this and explain why.
  `),

  COMPARE_PAPERS: PromptTemplate.fromTemplate(`
    Compare and analyze the following research papers. Identify similarities, differences, and relationships between them.

    Paper 1:
    {paper1}

    Paper 2:
    {paper2}

    Please provide:
    1. Common themes and research areas
    2. Methodological similarities and differences
    3. Complementary findings or conflicting results
    4. How the papers build upon or relate to each other
    5. Gaps that could be addressed by combining insights
    6. Recommendations for researchers interested in this area

    Structure your comparison clearly with specific examples from both papers.
  `),

  ANALYZE_CITATIONS: PromptTemplate.fromTemplate(`
    Analyze the citations and references in this research paper to understand its academic context.

    Paper Content:
    {content}

    Please identify:
    1. Key foundational works cited
    2. Recent developments referenced
    3. Main research communities or schools of thought
    4. Gaps in the literature identified by the authors
    5. How this work positions itself relative to existing research
    6. Potential future research directions suggested

    Focus on understanding the academic landscape and research trajectory.
  `),
};

// Create analysis chains
export const createAnalysisChain = (promptTemplate: PromptTemplate) => {
  const model = createDeepSeekModel();
  const outputParser = new StringOutputParser();

  return RunnableSequence.from([
    promptTemplate,
    model,
    outputParser,
  ]);
};

// Specific analysis functions
export const summarizePaper = async (content: string) => {
  const chain = createAnalysisChain(PROMPTS.SUMMARIZE);
  return await chain.invoke({ content });
};

export const extractKeyFindings = async (content: string) => {
  const chain = createAnalysisChain(PROMPTS.EXTRACT_KEY_FINDINGS);
  return await chain.invoke({ content });
};

export const explainMethodology = async (content: string) => {
  const chain = createAnalysisChain(PROMPTS.EXPLAIN_METHODOLOGY);
  return await chain.invoke({ content });
};

export const extractConcepts = async (content: string) => {
  const chain = createAnalysisChain(PROMPTS.EXTRACT_CONCEPTS);
  return await chain.invoke({ content });
};

export const answerQuestion = async (content: string, question: string) => {
  const chain = createAnalysisChain(PROMPTS.ANSWER_QUESTION);
  return await chain.invoke({ content, question });
};

export const comparePapers = async (paper1: string, paper2: string) => {
  const chain = createAnalysisChain(PROMPTS.COMPARE_PAPERS);
  return await chain.invoke({ paper1, paper2 });
};

export const analyzeCitations = async (content: string) => {
  const chain = createAnalysisChain(PROMPTS.ANALYZE_CITATIONS);
  return await chain.invoke({ content });
};

'use client';

import React, { useState } from 'react';
import { Upload, Button, Progress, message, Card, Typography, Space } from 'antd';
import { InboxOutlined, UploadOutlined, FileTextOutlined } from '@ant-design/icons';
import { useFileUpload, useDragAndDrop } from '@/hooks/useFileUpload';
import { FileUtils } from '@/lib/file-utils';
import { APP_CONFIG } from '@/lib/constants';

const { Dragger } = Upload;
const { Title, Text } = Typography;

interface FileUploadProps {
  onUploadSuccess?: (paperId: string) => void;
  className?: string;
  showProgress?: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onUploadSuccess,
  className = '',
  showProgress = true,
}) => {
  const { uploadFile, uploadProgress, isUploading, error, clearError } = useFileUpload();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = async (file: File) => {
    // Validate file
    const validation = FileUtils.validateFile(file);
    if (!validation.isValid) {
      message.error(validation.error);
      return false;
    }

    setSelectedFile(file);
    clearError();

    // Upload file
    const paper = await uploadFile(file);
    if (paper) {
      message.success('File uploaded successfully!');
      onUploadSuccess?.(paper.id);
      setSelectedFile(null);
    }

    return false; // Prevent default upload behavior
  };

  const { isDragOver, dragHandlers } = useDragAndDrop((files) => {
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  });

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: APP_CONFIG.allowedFileTypes.join(','),
    beforeUpload: handleFileSelect,
    showUploadList: false,
    ...dragHandlers,
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <Card className={`${isDragOver ? 'border-blue-400 bg-blue-50' : ''} transition-all duration-200`}>
        <Dragger {...uploadProps} className="border-dashed">
          <div className="py-8">
            <p className="ant-upload-drag-icon">
              <InboxOutlined className="text-4xl text-blue-500" />
            </p>
            <Title level={4} className="ant-upload-text">
              Click or drag PDF files to upload
            </Title>
            <Text className="ant-upload-hint text-gray-500">
              Support for academic papers in PDF format. Maximum file size: {FileUtils.formatFileSize(APP_CONFIG.maxFileSize)}
            </Text>
          </div>
        </Dragger>
      </Card>

      {/* Upload Progress */}
      {showProgress && uploadProgress && (
        <Card className="fade-in">
          <Space direction="vertical" className="w-full">
            <div className="flex items-center space-x-3">
              <FileTextOutlined className="text-blue-500" />
              <div className="flex-1">
                <Text strong>{uploadProgress.fileName}</Text>
                <div className="text-sm text-gray-500 capitalize">
                  {uploadProgress.status.replace('_', ' ')}
                </div>
              </div>
            </div>
            
            <Progress
              percent={uploadProgress.progress}
              status={
                uploadProgress.status === 'error' 
                  ? 'exception' 
                  : uploadProgress.status === 'completed' 
                    ? 'success' 
                    : 'active'
              }
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
            
            {uploadProgress.error && (
              <Text type="danger" className="text-sm">
                {uploadProgress.error}
              </Text>
            )}
          </Space>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <Text type="danger">{error}</Text>
        </Card>
      )}

      {/* Alternative Upload Button */}
      <div className="text-center">
        <Upload {...uploadProps}>
          <Button 
            icon={<UploadOutlined />} 
            loading={isUploading}
            size="large"
            type="primary"
          >
            Choose File
          </Button>
        </Upload>
      </div>

      {/* File Info */}
      <div className="text-center text-sm text-gray-500">
        <div>Supported formats: {APP_CONFIG.supportedFormats.join(', ')}</div>
        <div>Maximum size: {FileUtils.formatFileSize(APP_CONFIG.maxFileSize)}</div>
      </div>
    </div>
  );
};

export default FileUpload;

'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Document, Page } from 'react-pdf';
import { Spin, Alert } from 'antd';
import { pdfOptions } from '@/lib/pdf-config';

// Import CSS for react-pdf
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

interface PDFDocumentProps {
  file: string;
  pageNumber: number;
  scale?: number;
  onLoadSuccess: (data: { numPages: number }) => void;
  onLoadError: (error: Error) => void;
  onPageLoadSuccess?: () => void;
  onPageLoadError?: (error: Error) => void;
}

const PDFDocument: React.FC<PDFDocumentProps> = ({
  file,
  pageNumber,
  scale = 1.0,
  onLoadSuccess,
  onLoadError,
  onPageLoadSuccess,
  onPageLoadError,
}) => {
  const [error, setError] = useState<string | null>(null);

  const handleDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setError(null);
    onLoadSuccess({ numPages });
  }, [onLoadSuccess]);

  const handleDocumentLoadError = useCallback((error: Error) => {
    setError(error.message);
    onLoadError(error);
  }, [onLoadError]);

  const handlePageLoadSuccess = useCallback(() => {
    onPageLoadSuccess?.();
  }, [onPageLoadSuccess]);

  const handlePageLoadError = useCallback((error: Error) => {
    onPageLoadError?.(error);
  }, [onPageLoadError]);

  if (error) {
    return (
      <Alert
        message="Failed to load PDF"
        description={error}
        type="error"
        showIcon
        className="m-4"
      />
    );
  }

  return (
    <div className="pdf-document-container">
      <Document
        file={file}
        onLoadSuccess={handleDocumentLoadSuccess}
        onLoadError={handleDocumentLoadError}
        loading={
          <div className="flex justify-center items-center h-96">
            <Spin size="large" />
          </div>
        }
        error={
          <Alert
            message="Failed to load PDF"
            description="The PDF file could not be loaded. Please check the file and try again."
            type="error"
            showIcon
            className="m-4"
          />
        }
        options={pdfOptions}
      >
        <Page
          pageNumber={pageNumber}
          scale={scale}
          onLoadSuccess={handlePageLoadSuccess}
          onLoadError={handlePageLoadError}
          loading={
            <div className="flex justify-center items-center h-96">
              <Spin size="small" />
            </div>
          }
          error={
            <Alert
              message="Failed to load page"
              description={`Page ${pageNumber} could not be loaded.`}
              type="warning"
              showIcon
              className="m-4"
            />
          }
          renderTextLayer={true}
          renderAnnotationLayer={true}
          className="pdf-page mx-auto shadow-lg"
        />
      </Document>
    </div>
  );
};

export default PDFDocument;

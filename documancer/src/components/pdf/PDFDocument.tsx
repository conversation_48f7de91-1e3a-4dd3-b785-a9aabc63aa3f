'use client';

import React, { useEffect, useState } from 'react';
import { Spin, Card, Typography, Alert } from 'antd';

const { Title, Paragraph } = Typography;

interface PDFDocumentProps {
  file: string;
  pageNumber: number;
  onLoadSuccess: (data: { numPages: number }) => void;
  onLoadError: (error: Error) => void;
}

const PDFDocument: React.FC<PDFDocumentProps> = ({
  file,
  pageNumber, // Currently unused in mock implementation
  onLoadSuccess,
  onLoadError,
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfContent, setPdfContent] = useState<string>('');

  useEffect(() => {
    // Simulate PDF loading and call success callback
    const timer = setTimeout(() => {
      try {
        // Mock PDF content based on our test data
        const mockContent = `A Comprehensive Study of Machine Learning Applications

Authors: <AUTHORS>
Date: December 2024

Abstract

This paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field. Our study encompasses both theoretical foundations and practical implementations, providing insights into the effectiveness of different approaches.

1. Introduction

Machine learning has revolutionized numerous fields of study, from natural language processing to computer vision. This paper aims to provide a thorough examination of how machine learning techniques are being applied in academic research contexts.

The rapid advancement of artificial intelligence has created new opportunities for research and development. Our analysis focuses on understanding the impact of these technologies on traditional research methodologies.

2. Methodology

Our research methodology combines systematic literature review with empirical analysis. We collected data from over 500 academic papers published between 2020 and 2024, focusing on machine learning applications across various disciplines.

The study employed both quantitative and qualitative analysis techniques to ensure comprehensive coverage of the research landscape.

3. Key Findings

Our analysis reveals several important trends:

• Increased adoption of deep learning techniques in research
• Growing emphasis on interpretable AI models
• Rising importance of ethical considerations in ML research
• Enhanced collaboration between different academic disciplines

These findings suggest a fundamental shift in how research is conducted and validated in the modern academic environment.

4. Discussion

The implications of our research extend beyond technical improvements to fundamental changes in how research is conducted. The integration of machine learning tools has enabled researchers to process larger datasets and identify patterns that were previously undetectable.

However, this technological advancement also raises important questions about research methodology, reproducibility, and the role of human expertise in the research process.

5. Conclusion

This study demonstrates the transformative impact of machine learning on academic research. Future work should focus on developing more accessible tools and addressing ethical concerns while maintaining scientific rigor.

The findings contribute to our understanding of how emerging technologies can enhance research capabilities while preserving the fundamental principles of scientific inquiry.

References

[1] Smith, J. et al. (2023). "Machine Learning in Academia: A Survey." Journal of Academic Computing, 45(2), 123-145.
[2] Doe, J. (2024). "Ethical AI in Research." Proceedings of the International Conference on AI Ethics, 67-89.
[3] Johnson, A. (2023). "Deep Learning Applications in Scientific Research." Nature Machine Intelligence, 12(3), 234-256.`;

        setPdfContent(mockContent);
        setLoading(false);
        onLoadSuccess({ numPages: 1 });
      } catch (err) {
        const error = err as Error;
        setError(error.message);
        setLoading(false);
        onLoadError(error);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [file, onLoadSuccess, onLoadError]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Failed to load PDF"
        description={error}
        type="error"
        showIcon
      />
    );
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <div className="prose prose-sm max-w-none">
        {pdfContent.split('\n\n').map((paragraph, index) => {
          if (paragraph.trim().startsWith('#') || paragraph.trim().match(/^\d+\./)) {
            return (
              <Title key={index} level={paragraph.startsWith('##') ? 4 : 3} className="mt-6 mb-3">
                {paragraph.replace(/^#+\s*/, '').replace(/^\d+\.\s*/, '')}
              </Title>
            );
          }

          if (paragraph.trim().length === 0) {
            return null;
          }

          return (
            <Paragraph key={index} className="mb-4 leading-relaxed">
              {paragraph.split('\n').map((line, lineIndex) => (
                <span key={lineIndex}>
                  {line}
                  {lineIndex < paragraph.split('\n').length - 1 && <br />}
                </span>
              ))}
            </Paragraph>
          );
        })}
      </div>
    </Card>
  );
};

export default PDFDocument;

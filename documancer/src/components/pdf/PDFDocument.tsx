'use client';

import React, { useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Spin } from 'antd';

// Set up PDF.js worker
if (typeof window !== 'undefined') {
  pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
}

interface PDFDocumentProps {
  file: string;
  pageNumber: number;
  onLoadSuccess: (data: { numPages: number }) => void;
  onLoadError: (error: Error) => void;
}

const PDFDocument: React.FC<PDFDocumentProps> = ({
  file,
  pageNumber,
  onLoadSuccess,
  onLoadError,
}) => {
  return (
    <Document
      file={file}
      onLoadSuccess={onLoadSuccess}
      onLoadError={onLoadError}
      loading={<Spin size="large" />}
    >
      <Page
        pageNumber={pageNumber}
        renderTextLayer={true}
        renderAnnotationLayer={true}
        className="pdf-page mx-auto"
      />
    </Document>
  );
};

export default PDFDocument;

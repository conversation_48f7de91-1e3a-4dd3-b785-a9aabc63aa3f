'use client';

import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import { Card, Button, Input, Slider, Space, Tooltip, Spin, Typography } from 'antd';
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  LeftOutlined,
  RightOutlined,
  FullscreenOutlined,
  DownloadOutlined,
  SearchOutlined,
  HighlightOutlined,
  EditOutlined,
  BookOutlined,
} from '@ant-design/icons';
import { Paper, Annotation } from '@/lib/types';
import { useAppStore } from '@/store/useAppStore';

// Dynamic import for PDF component to avoid SSR issues
const PDFDocument = dynamic(() => import('./PDFDocument'), {
  ssr: false,
  loading: () => <Spin size="large" />,
});

const { Text } = Typography;

interface PDFViewerProps {
  paper: Paper;
  className?: string;
  onAnnotationCreate?: (annotation: Annotation) => void;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  paper,
  className = '',
  onAnnotationCreate,
}) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [searchText, setSearchText] = useState<string>('');
  const [annotationMode, setAnnotationMode] = useState<'none' | 'highlight' | 'note' | 'bookmark'>('none');
  const [selectedText, setSelectedText] = useState<string>('');
  
  const { annotations, addAnnotation } = useAppStore();
  const containerRef = useRef<HTMLDivElement>(null);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setIsLoading(false);
  };

  const onDocumentLoadError = (error: Error) => {
    console.error('Error loading PDF:', error);
    setIsLoading(false);
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= numPages) {
      setCurrentPage(page);
    }
  };

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.2, 3.0));
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.2, 0.5));
  };

  const handleScaleChange = (value: number) => {
    setScale(value);
  };

  const handleFullscreen = () => {
    if (containerRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        containerRef.current.requestFullscreen();
      }
    }
  };

  const handleDownload = () => {
    // In a real implementation, you would download the PDF file
    const link = document.createElement('a');
    link.href = paper.filePath;
    link.download = `${paper.title}.pdf`;
    link.click();
  };

  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString());
      
      if (annotationMode === 'highlight') {
        createAnnotation('highlight', selection.toString());
      }
    }
  };

  const createAnnotation = (type: 'highlight' | 'note' | 'bookmark', content: string) => {
    const annotation: Annotation = {
      id: `annotation_${Date.now()}`,
      paperId: paper.id,
      pageNumber: currentPage,
      position: {
        x: 0, // In a real implementation, you'd get actual coordinates
        y: 0,
        width: 0,
        height: 0,
      },
      content,
      type,
      createdAt: new Date(),
    };

    addAnnotation(annotation);
    onAnnotationCreate?.(annotation);
  };

  const paperAnnotations = annotations.filter(a => a.paperId === paper.id);
  const currentPageAnnotations = paperAnnotations.filter(a => a.pageNumber === currentPage);

  return (
    <div className={`pdf-viewer ${className}`} ref={containerRef}>
      {/* Toolbar */}
      <Card className="mb-4" styles={{ body: { padding: '12px 16px' } }}>
        <div className="flex items-center justify-between">
          <Space>
            {/* Navigation */}
            <Button
              icon={<LeftOutlined />}
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage <= 1}
            />
            <Input
              value={currentPage}
              onChange={(e) => handlePageChange(parseInt(e.target.value) || 1)}
              className="w-16 text-center"
              size="small"
            />
            <Text>of {numPages}</Text>
            <Button
              icon={<RightOutlined />}
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= numPages}
            />
          </Space>

          <Space>
            {/* Zoom Controls */}
            <Button icon={<ZoomOutOutlined />} onClick={handleZoomOut} />
            <Slider
              min={0.5}
              max={3.0}
              step={0.1}
              value={scale}
              onChange={handleScaleChange}
              className="w-24"
            />
            <Button icon={<ZoomInOutlined />} onClick={handleZoomIn} />
            <Text className="w-12 text-center">{Math.round(scale * 100)}%</Text>
          </Space>

          <Space>
            {/* Annotation Tools */}
            <Tooltip title="Highlight">
              <Button
                icon={<HighlightOutlined />}
                type={annotationMode === 'highlight' ? 'primary' : 'default'}
                onClick={() => setAnnotationMode(annotationMode === 'highlight' ? 'none' : 'highlight')}
              />
            </Tooltip>
            <Tooltip title="Add Note">
              <Button
                icon={<EditOutlined />}
                type={annotationMode === 'note' ? 'primary' : 'default'}
                onClick={() => setAnnotationMode(annotationMode === 'note' ? 'none' : 'note')}
              />
            </Tooltip>
            <Tooltip title="Bookmark">
              <Button
                icon={<BookOutlined />}
                onClick={() => createAnnotation('bookmark', `Page ${currentPage}`)}
              />
            </Tooltip>
          </Space>

          <Space>
            {/* Search */}
            <Input
              placeholder="Search in document..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="w-48"
              size="small"
            />
            
            {/* Actions */}
            <Button icon={<FullscreenOutlined />} onClick={handleFullscreen} />
            <Button icon={<DownloadOutlined />} onClick={handleDownload} />
          </Space>
        </div>
      </Card>

      {/* PDF Content */}
      <Card className="flex-1 overflow-auto" styles={{ body: { padding: 0 } }}>
        {isLoading && (
          <div className="flex items-center justify-center h-64">
            <Spin size="large" />
          </div>
        )}
        
        <div
          className="pdf-content"
          onMouseUp={handleTextSelection}
          style={{
            transform: `scale(${scale})`,
            transformOrigin: 'top center',
            transition: 'transform 0.2s ease',
          }}
        >
          <PDFDocument
            file={paper.filePath}
            pageNumber={currentPage}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
          />
        </div>

        {/* Annotations Overlay */}
        {currentPageAnnotations.length > 0 && (
          <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
            {currentPageAnnotations.map((annotation) => (
              <div
                key={annotation.id}
                className={`absolute pointer-events-auto ${
                  annotation.type === 'highlight' 
                    ? 'bg-yellow-200 bg-opacity-50' 
                    : annotation.type === 'note'
                    ? 'bg-blue-200 bg-opacity-50'
                    : 'bg-red-200 bg-opacity-50'
                }`}
                style={{
                  left: annotation.position.x,
                  top: annotation.position.y,
                  width: annotation.position.width,
                  height: annotation.position.height,
                }}
                title={annotation.content}
              />
            ))}
          </div>
        )}
      </Card>

      {/* Status Bar */}
      <div className="flex items-center justify-between mt-2 text-sm text-gray-600">
        <div>
          {paperAnnotations.length} annotations
        </div>
        <div>
          Page {currentPage} of {numPages}
        </div>
        <div>
          {selectedText && `Selected: "${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}"`}
        </div>
      </div>
    </div>
  );
};

export default PDFViewer;

@import "tailwindcss";

/* Custom CSS for DocuMancer */
:root {
  --primary-color: #1890ff;
  --secondary-color: #722ed1;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-disabled: #bfbfbf;
  --background-primary: #ffffff;
  --background-secondary: #fafafa;
  --background-tertiary: #f5f5f5;
  --border-color: #d9d9d9;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.15);
}

@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #d9d9d9;
    --text-disabled: #595959;
    --background-primary: #141414;
    --background-secondary: #1f1f1f;
    --background-tertiary: #262626;
    --border-color: #434343;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  color: var(--text-primary);
  background: var(--background-primary);
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--background-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-disabled);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Utility classes */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Custom Ant Design overrides */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  box-shadow: var(--shadow-light);
}

.ant-menu-item-selected {
  background-color: rgba(24, 144, 255, 0.1) !important;
}

.ant-btn-primary {
  background: linear-gradient(135deg, var(--primary-color), #40a9ff);
  border: none;
  box-shadow: var(--shadow-light);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff, var(--primary-color));
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.ant-card {
  box-shadow: var(--shadow-light);
  border-radius: 8px;
  transition: box-shadow 0.3s ease;
}

.ant-card:hover {
  box-shadow: var(--shadow-medium);
}

.ant-upload-drag {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.ant-upload-drag:hover {
  border-color: var(--primary-color);
  background-color: rgba(24, 144, 255, 0.02);
}

.ant-progress-circle .ant-progress-text {
  font-weight: 600;
}

/* PDF viewer styles */
.pdf-viewer {
  height: calc(100vh - 64px);
  overflow: auto;
}

.pdf-page {
  margin: 16px auto;
  box-shadow: var(--shadow-medium);
  border-radius: 4px;
}

/* Chat interface styles */
.chat-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.chat-input {
  border-top: 1px solid var(--border-color);
  padding: 16px;
  background: var(--background-primary);
}

.message-bubble {
  max-width: 80%;
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
}

.message-user {
  background: var(--primary-color);
  color: white;
  margin-left: auto;
}

.message-assistant {
  background: var(--background-tertiary);
  color: var(--text-primary);
  margin-right: auto;
}

/* Loading animations */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    color: rgba(0,0,0,0);
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  40% {
    color: var(--text-primary);
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  60% {
    text-shadow:
      .25em 0 0 var(--text-primary),
      .5em 0 0 rgba(0,0,0,0);
  }
  80%, 100% {
    text-shadow:
      .25em 0 0 var(--text-primary),
      .5em 0 0 var(--text-primary);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 1000;
    left: -240px;
    transition: left 0.3s ease;
  }

  .ant-layout-sider.ant-layout-sider-collapsed {
    left: -80px;
  }

  .ant-layout-sider.mobile-open {
    left: 0 !important;
  }

  .ant-layout-content {
    margin-left: 0 !important;
  }

  .message-bubble {
    max-width: 90%;
  }

  .pdf-viewer {
    height: calc(100vh - 120px);
  }

  .chat-container {
    height: calc(100vh - 140px);
  }
}

@media (max-width: 576px) {
  .ant-card-actions {
    display: flex;
    flex-wrap: wrap;
  }

  .ant-card-actions > li {
    flex: 1 1 50%;
    min-width: 50%;
  }

  .message-bubble {
    max-width: 95%;
    padding: 8px 12px;
  }

  .pdf-viewer .ant-card-body {
    padding: 8px;
  }
}

/* Paper card enhancements */
.paper-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.paper-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.paper-card .ant-card-body {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.paper-card .ant-card-actions {
  border-top: 1px solid var(--border-color);
  background: var(--background-secondary);
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Upload area enhancements */
.ant-upload-drag {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border: 2px dashed #d9d9d9;
  transition: all 0.3s ease;
}

.ant-upload-drag:hover {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, #f0f2ff 0%, #e6f0ff 100%);
}

.ant-upload-drag.ant-upload-drag-hover {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, #e6f0ff 0%, #d6e4ff 100%);
}

/* Analysis modal enhancements */
.analysis-modal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
}

.analysis-modal .ant-modal-header {
  background: linear-gradient(135deg, var(--primary-color), #40a9ff);
  color: white;
  border-bottom: none;
}

.analysis-modal .ant-modal-title {
  color: white;
}

.analysis-modal .ant-modal-close {
  color: white;
}

.analysis-modal .ant-modal-close:hover {
  color: rgba(255, 255, 255, 0.8);
}

/* Sidebar enhancements */
.ant-layout-sider-trigger {
  background: var(--primary-color);
  color: white;
  transition: all 0.3s ease;
}

.ant-layout-sider-trigger:hover {
  background: #40a9ff;
}

/* Menu enhancements */
.ant-menu-item {
  border-radius: 8px;
  margin: 4px 8px;
  width: calc(100% - 16px);
}

.ant-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.06);
}

.ant-menu-item-selected {
  background-color: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
  font-weight: 600;
}

.ant-menu-item-selected::after {
  display: none;
}

/* Button enhancements */
.ant-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  background: linear-gradient(135deg, var(--primary-color), #40a9ff);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff, #1890ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.ant-btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* Input enhancements */
.ant-input,
.ant-input-affix-wrapper {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.ant-input:focus,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Tag enhancements */
.ant-tag {
  border-radius: 12px;
  border: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Progress enhancements */
.ant-progress-line {
  border-radius: 10px;
}

.ant-progress-bg {
  border-radius: 10px;
}

/* Tooltip enhancements */
.ant-tooltip {
  border-radius: 8px;
}

.ant-tooltip-inner {
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(10px);
}

/* Dropdown enhancements */
.ant-dropdown {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
}

.ant-dropdown-menu {
  border-radius: 12px;
  padding: 8px;
}

.ant-dropdown-menu-item {
  border-radius: 8px;
  margin: 2px 0;
  transition: all 0.3s ease;
}

.ant-dropdown-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.06);
}

/* Notification enhancements */
.ant-notification {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
}

/* Modal enhancements */
.ant-modal {
  border-radius: 12px;
}

.ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
}

.ant-modal-header {
  border-radius: 12px 12px 0 0;
}

/* Drawer enhancements */
.ant-drawer-content {
  background: var(--background-primary);
}

.ant-drawer-header {
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
}

/* Table enhancements */
.ant-table {
  border-radius: 12px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background: var(--background-secondary);
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background: rgba(24, 144, 255, 0.02);
}

/* Tabs enhancements */
.ant-tabs-tab {
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
}

.ant-tabs-tab:hover {
  color: var(--primary-color);
}

.ant-tabs-tab-active {
  background: var(--background-primary);
  color: var(--primary-color);
  font-weight: 600;
}

/* Steps enhancements */
.ant-steps-item-process .ant-steps-item-icon {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-steps-item-finish .ant-steps-item-icon {
  background: var(--success-color);
  border-color: var(--success-color);
}

/* Collapse enhancements */
.ant-collapse {
  border-radius: 12px;
  overflow: hidden;
}

.ant-collapse-item {
  border-radius: 0;
}

.ant-collapse-header {
  background: var(--background-secondary);
  transition: all 0.3s ease;
}

.ant-collapse-header:hover {
  background: rgba(24, 144, 255, 0.04);
}

/* Alert enhancements */
.ant-alert {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-alert-success {
  background: linear-gradient(135deg, #f6ffed, #f0f9ff);
  border-left: 4px solid var(--success-color);
}

.ant-alert-info {
  background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
  border-left: 4px solid var(--primary-color);
}

.ant-alert-warning {
  background: linear-gradient(135deg, #fffbe6, #fff7e6);
  border-left: 4px solid var(--warning-color);
}

.ant-alert-error {
  background: linear-gradient(135deg, #fff2f0, #fff1f0);
  border-left: 4px solid var(--error-color);
}

/* React PDF styles */
.react-pdf__Page {
  margin: 0 auto 20px auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.react-pdf__Page__canvas {
  display: block;
  max-width: 100%;
  height: auto;
}

.react-pdf__Page__textContent {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
}

.react-pdf__Page__textContent > span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
}

.react-pdf__Page__annotations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.react-pdf__Document {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* PDF Viewer specific styles */
.pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.pdf-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
  background: #f5f5f5;
}

.pdf-page {
  margin: 0 auto 20px auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.pdf-document-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* Text selection styles for PDF */
.react-pdf__Page__textContent > span:hover {
  background-color: rgba(255, 255, 0, 0.1);
}

.react-pdf__Page__textContent > span::selection {
  background-color: rgba(255, 255, 0, 0.3);
}

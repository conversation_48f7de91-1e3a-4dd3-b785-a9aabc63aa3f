{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/pdf-config.ts"], "sourcesContent": ["import { pdfjs } from 'react-pdf';\n\n// Configure PDF.js worker - use CDN for compatibility\nif (typeof window !== 'undefined') {\n  // Use CDN worker that matches the installed react-pdf version\n  pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;\n}\n\n// PDF.js configuration options\nexport const pdfOptions = {\n  cMapUrl: `//unpkg.com/pdfjs-dist@${pdfjs.version}/cmaps/`,\n  cMapPacked: true,\n  standardFontDataUrl: `//unpkg.com/pdfjs-dist@${pdfjs.version}/standard_fonts/`,\n};\n\n// Export pdfjs for use in components\nexport { pdfjs };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,sDAAsD;AACtD,wCAAmC;IACjC,8DAA8D;IAC9D,kLAAA,CAAA,QAAK,CAAC,mBAAmB,CAAC,SAAS,GAAG,AAAC,0BAAuC,OAAd,kLAAA,CAAA,QAAK,CAAC,OAAO,EAAC;AAChF;AAGO,MAAM,aAAa;IACxB,SAAS,AAAC,0BAAuC,OAAd,kLAAA,CAAA,QAAK,CAAC,OAAO,EAAC;IACjD,YAAY;IACZ,qBAAqB,AAAC,0BAAuC,OAAd,kLAAA,CAAA,QAAK,CAAC,OAAO,EAAC;AAC/D", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/pdf/PDFDocument.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState, useCallback } from 'react';\nimport { Document, Page } from 'react-pdf';\nimport { Spin, Alert } from 'antd';\nimport { pdfOptions } from '@/lib/pdf-config';\n\n// Import CSS for react-pdf\nimport 'react-pdf/dist/Page/AnnotationLayer.css';\nimport 'react-pdf/dist/Page/TextLayer.css';\n\ninterface PDFDocumentProps {\n  file: string;\n  pageNumber: number;\n  scale?: number;\n  onLoadSuccess: (data: { numPages: number }) => void;\n  onLoadError: (error: Error) => void;\n  onPageLoadSuccess?: () => void;\n  onPageLoadError?: (error: Error) => void;\n}\n\nconst PDFDocument: React.FC<PDFDocumentProps> = ({\n  file,\n  pageNumber,\n  scale = 1.0,\n  onLoadSuccess,\n  onLoadError,\n  onPageLoadSuccess,\n  onPageLoadError,\n}) => {\n  const [error, setError] = useState<string | null>(null);\n\n  const handleDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {\n    setError(null);\n    onLoadSuccess({ numPages });\n  }, [onLoadSuccess]);\n\n  const handleDocumentLoadError = useCallback((error: Error) => {\n    setError(error.message);\n    onLoadError(error);\n  }, [onLoadError]);\n\n  const handlePageLoadSuccess = useCallback(() => {\n    onPageLoadSuccess?.();\n  }, [onPageLoadSuccess]);\n\n  const handlePageLoadError = useCallback((error: Error) => {\n    onPageLoadError?.(error);\n  }, [onPageLoadError]);\n\n  if (error) {\n    return (\n      <Alert\n        message=\"Failed to load PDF\"\n        description={error}\n        type=\"error\"\n        showIcon\n        className=\"m-4\"\n      />\n    );\n  }\n\n  return (\n    <div className=\"pdf-document-container\">\n      <Document\n        file={file}\n        onLoadSuccess={handleDocumentLoadSuccess}\n        onLoadError={handleDocumentLoadError}\n        loading={\n          <div className=\"flex justify-center items-center h-96\">\n            <Spin size=\"large\" />\n          </div>\n        }\n        error={\n          <Alert\n            message=\"Failed to load PDF\"\n            description=\"The PDF file could not be loaded. Please check the file and try again.\"\n            type=\"error\"\n            showIcon\n            className=\"m-4\"\n          />\n        }\n        options={pdfOptions}\n      >\n        <Page\n          pageNumber={pageNumber}\n          scale={scale}\n          onLoadSuccess={handlePageLoadSuccess}\n          onLoadError={handlePageLoadError}\n          loading={\n            <div className=\"flex justify-center items-center h-96\">\n              <Spin size=\"small\" />\n            </div>\n          }\n          error={\n            <Alert\n              message=\"Failed to load page\"\n              description={`Page ${pageNumber} could not be loaded.`}\n              type=\"warning\"\n              showIcon\n              className=\"m-4\"\n            />\n          }\n          renderTextLayer={true}\n          renderAnnotationLayer={true}\n          className=\"pdf-page mx-auto shadow-lg\"\n        />\n      </Document>\n    </div>\n  );\n};\n\nexport default PDFDocument;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;AALA;;;;;;;AAqBA,MAAM,cAA0C;QAAC,EAC/C,IAAI,EACJ,UAAU,EACV,QAAQ,GAAG,EACX,aAAa,EACb,WAAW,EACX,iBAAiB,EACjB,eAAe,EAChB;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;gBAAC,EAAE,QAAQ,EAAwB;YAC/E,SAAS;YACT,cAAc;gBAAE;YAAS;QAC3B;6DAAG;QAAC;KAAc;IAElB,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YAC3C,SAAS,MAAM,OAAO;YACtB,YAAY;QACd;2DAAG;QAAC;KAAY;IAEhB,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACxC,8BAAA,wCAAA;QACF;yDAAG;QAAC;KAAkB;IAEtB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACvC,4BAAA,sCAAA,gBAAkB;QACpB;uDAAG;QAAC;KAAgB;IAEpB,IAAI,OAAO;QACT,qBACE,6LAAC,mLAAA,CAAA,QAAK;YACJ,SAAQ;YACR,aAAa;YACb,MAAK;YACL,QAAQ;YACR,WAAU;;;;;;IAGhB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,iMAAA,CAAA,WAAQ;YACP,MAAM;YACN,eAAe;YACf,aAAa;YACb,uBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAK;;;;;;;;;;;YAGf,qBACE,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;YAGd,SAAS,8IAAA,CAAA,aAAU;sBAEnB,cAAA,6LAAC,yLAAA,CAAA,OAAI;gBACH,YAAY;gBACZ,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,uBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;wBAAC,MAAK;;;;;;;;;;;gBAGf,qBACE,6LAAC,mLAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,aAAa,AAAC,QAAkB,OAAX,YAAW;oBAChC,MAAK;oBACL,QAAQ;oBACR,WAAU;;;;;;gBAGd,iBAAiB;gBACjB,uBAAuB;gBACvB,WAAU;;;;;;;;;;;;;;;;AAKpB;GAzFM;KAAA;uCA2FS", "debugId": null}}]}
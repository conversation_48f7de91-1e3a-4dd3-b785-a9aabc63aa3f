{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/TabContext.js"], "sourcesContent": ["import { createContext } from 'react';\nexport default /*#__PURE__*/createContext(null);"], "names": [], "mappings": ";;;AAAA;;uCACe,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/hooks/useIndicator.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport raf from \"rc-util/es/raf\";\nimport React, { useEffect, useRef, useState } from 'react';\nvar useIndicator = function useIndicator(options) {\n  var activeTabOffset = options.activeTabOffset,\n    horizontal = options.horizontal,\n    rtl = options.rtl,\n    _options$indicator = options.indicator,\n    indicator = _options$indicator === void 0 ? {} : _options$indicator;\n  var size = indicator.size,\n    _indicator$align = indicator.align,\n    align = _indicator$align === void 0 ? 'center' : _indicator$align;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    inkStyle = _useState2[0],\n    setInkStyle = _useState2[1];\n  var inkBarRafRef = useRef();\n  var getLength = React.useCallback(function (origin) {\n    if (typeof size === 'function') {\n      return size(origin);\n    }\n    if (typeof size === 'number') {\n      return size;\n    }\n    return origin;\n  }, [size]);\n\n  // Delay set ink style to avoid remove tab blink\n  function cleanInkBarRaf() {\n    raf.cancel(inkBarRafRef.current);\n  }\n  useEffect(function () {\n    var newInkStyle = {};\n    if (activeTabOffset) {\n      if (horizontal) {\n        newInkStyle.width = getLength(activeTabOffset.width);\n        var key = rtl ? 'right' : 'left';\n        if (align === 'start') {\n          newInkStyle[key] = activeTabOffset[key];\n        }\n        if (align === 'center') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width / 2;\n          newInkStyle.transform = rtl ? 'translateX(50%)' : 'translateX(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width;\n          newInkStyle.transform = 'translateX(-100%)';\n        }\n      } else {\n        newInkStyle.height = getLength(activeTabOffset.height);\n        if (align === 'start') {\n          newInkStyle.top = activeTabOffset.top;\n        }\n        if (align === 'center') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height / 2;\n          newInkStyle.transform = 'translateY(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height;\n          newInkStyle.transform = 'translateY(-100%)';\n        }\n      }\n    }\n    cleanInkBarRaf();\n    inkBarRafRef.current = raf(function () {\n      // Avoid jitter caused by tiny numerical differences\n      // fix https://github.com/ant-design/ant-design/issues/53378\n      var isEqual = inkStyle && newInkStyle && Object.keys(newInkStyle).every(function (key) {\n        var newValue = newInkStyle[key];\n        var oldValue = inkStyle[key];\n        return typeof newValue === 'number' && typeof oldValue === 'number' ? Math.round(newValue) === Math.round(oldValue) : newValue === oldValue;\n      });\n      if (!isEqual) {\n        setInkStyle(newInkStyle);\n      }\n    });\n    return cleanInkBarRaf;\n  }, [JSON.stringify(activeTabOffset), horizontal, rtl, align, getLength]);\n  return {\n    style: inkStyle\n  };\n};\nexport default useIndicator;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,IAAI,eAAe,SAAS,aAAa,OAAO;IAC9C,IAAI,kBAAkB,QAAQ,eAAe,EAC3C,aAAa,QAAQ,UAAU,EAC/B,MAAM,QAAQ,GAAG,EACjB,qBAAqB,QAAQ,SAAS,EACtC,YAAY,uBAAuB,KAAK,IAAI,CAAC,IAAI;IACnD,IAAI,OAAO,UAAU,IAAI,EACvB,mBAAmB,UAAU,KAAK,EAClC,QAAQ,qBAAqB,KAAK,IAAI,WAAW;IACnD,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,KACrB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACvC,WAAW,UAAU,CAAC,EAAE,EACxB,cAAc,UAAU,CAAC,EAAE;IAC7B,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACxB,IAAI,YAAY,6JAAA,CAAA,UAAK,CAAC,WAAW;+CAAC,SAAU,MAAM;YAChD,IAAI,OAAO,SAAS,YAAY;gBAC9B,OAAO,KAAK;YACd;YACA,IAAI,OAAO,SAAS,UAAU;gBAC5B,OAAO;YACT;YACA,OAAO;QACT;8CAAG;QAAC;KAAK;IAET,gDAAgD;IAChD,SAAS;QACP,0IAAA,CAAA,UAAG,CAAC,MAAM,CAAC,aAAa,OAAO;IACjC;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,cAAc,CAAC;YACnB,IAAI,iBAAiB;gBACnB,IAAI,YAAY;oBACd,YAAY,KAAK,GAAG,UAAU,gBAAgB,KAAK;oBACnD,IAAI,MAAM,MAAM,UAAU;oBAC1B,IAAI,UAAU,SAAS;wBACrB,WAAW,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI;oBACzC;oBACA,IAAI,UAAU,UAAU;wBACtB,WAAW,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI,GAAG,gBAAgB,KAAK,GAAG;wBAClE,YAAY,SAAS,GAAG,MAAM,oBAAoB;oBACpD;oBACA,IAAI,UAAU,OAAO;wBACnB,WAAW,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI,GAAG,gBAAgB,KAAK;wBAC/D,YAAY,SAAS,GAAG;oBAC1B;gBACF,OAAO;oBACL,YAAY,MAAM,GAAG,UAAU,gBAAgB,MAAM;oBACrD,IAAI,UAAU,SAAS;wBACrB,YAAY,GAAG,GAAG,gBAAgB,GAAG;oBACvC;oBACA,IAAI,UAAU,UAAU;wBACtB,YAAY,GAAG,GAAG,gBAAgB,GAAG,GAAG,gBAAgB,MAAM,GAAG;wBACjE,YAAY,SAAS,GAAG;oBAC1B;oBACA,IAAI,UAAU,OAAO;wBACnB,YAAY,GAAG,GAAG,gBAAgB,GAAG,GAAG,gBAAgB,MAAM;wBAC9D,YAAY,SAAS,GAAG;oBAC1B;gBACF;YACF;YACA;YACA,aAAa,OAAO,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAG,AAAD;0CAAE;oBACzB,oDAAoD;oBACpD,4DAA4D;oBAC5D,IAAI,UAAU,YAAY,eAAe,OAAO,IAAI,CAAC,aAAa,KAAK;kDAAC,SAAU,GAAG;4BACnF,IAAI,WAAW,WAAW,CAAC,IAAI;4BAC/B,IAAI,WAAW,QAAQ,CAAC,IAAI;4BAC5B,OAAO,OAAO,aAAa,YAAY,OAAO,aAAa,WAAW,KAAK,KAAK,CAAC,cAAc,KAAK,KAAK,CAAC,YAAY,aAAa;wBACrI;;oBACA,IAAI,CAAC,SAAS;wBACZ,YAAY;oBACd;gBACF;;YACA,OAAO;QACT;iCAAG;QAAC,KAAK,SAAS,CAAC;QAAkB;QAAY;QAAK;QAAO;KAAU;IACvE,OAAO;QACL,OAAO;IACT;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/hooks/useOffsets.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0\n};\nexport default function useOffsets(tabs, tabSizes, holderScrollWidth) {\n  return useMemo(function () {\n    var _tabs$;\n    var map = new Map();\n    var lastOffset = tabSizes.get((_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key) || DEFAULT_SIZE;\n    var rightOffset = lastOffset.left + lastOffset.width;\n    for (var i = 0; i < tabs.length; i += 1) {\n      var key = tabs[i].key;\n      var data = tabSizes.get(key);\n\n      // Reuse last one when not exist yet\n      if (!data) {\n        var _tabs;\n        data = tabSizes.get((_tabs = tabs[i - 1]) === null || _tabs === void 0 ? void 0 : _tabs.key) || DEFAULT_SIZE;\n      }\n      var entity = map.get(key) || _objectSpread({}, data);\n\n      // Right\n      entity.right = rightOffset - entity.left - entity.width;\n\n      // Update entity\n      map.set(key, entity);\n    }\n    return map;\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), tabSizes, holderScrollWidth]);\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,IAAI,eAAe;IACjB,OAAO;IACP,QAAQ;IACR,MAAM;IACN,KAAK;AACP;AACe,SAAS,WAAW,IAAI,EAAE,QAAQ,EAAE,iBAAiB;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8BAAE;YACb,IAAI;YACJ,IAAI,MAAM,IAAI;YACd,IAAI,aAAa,SAAS,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,EAAE,MAAM,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,GAAG,KAAK;YACzG,IAAI,cAAc,WAAW,IAAI,GAAG,WAAW,KAAK;YACpD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;gBACvC,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG;gBACrB,IAAI,OAAO,SAAS,GAAG,CAAC;gBAExB,oCAAoC;gBACpC,IAAI,CAAC,MAAM;oBACT,IAAI;oBACJ,OAAO,SAAS,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,KAAK;gBAClG;gBACA,IAAI,SAAS,IAAI,GAAG,CAAC,QAAQ,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG;gBAE/C,QAAQ;gBACR,OAAO,KAAK,GAAG,cAAc,OAAO,IAAI,GAAG,OAAO,KAAK;gBAEvD,gBAAgB;gBAChB,IAAI,GAAG,CAAC,KAAK;YACf;YACA,OAAO;QACT;6BAAG;QAAC,KAAK,GAAG;kCAAC,SAAU,GAAG;gBACxB,OAAO,IAAI,GAAG;YAChB;iCAAG,IAAI,CAAC;QAAM;QAAU;KAAkB;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/hooks/useSyncState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useSyncState(defaultState, onChange) {\n  var stateRef = React.useRef(defaultState);\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  function setState(updater) {\n    var newValue = typeof updater === 'function' ? updater(stateRef.current) : updater;\n    if (newValue !== stateRef.current) {\n      onChange(newValue, stateRef.current);\n    }\n    stateRef.current = newValue;\n    forceUpdate({});\n  }\n  return [stateRef.current, setState];\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AACe,SAAS,aAAa,YAAY,EAAE,QAAQ;IACzD,IAAI,WAAW,6JAAA,CAAA,SAAY,CAAC;IAC5B,IAAI,kBAAkB,6JAAA,CAAA,WAAc,CAAC,CAAC,IACpC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,cAAc,gBAAgB,CAAC,EAAE;IACnC,SAAS,SAAS,OAAO;QACvB,IAAI,WAAW,OAAO,YAAY,aAAa,QAAQ,SAAS,OAAO,IAAI;QAC3E,IAAI,aAAa,SAAS,OAAO,EAAE;YACjC,SAAS,UAAU,SAAS,OAAO;QACrC;QACA,SAAS,OAAO,GAAG;QACnB,YAAY,CAAC;IACf;IACA,OAAO;QAAC,SAAS,OAAO;QAAE;KAAS;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/hooks/useTouchMove.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nvar MIN_SWIPE_DISTANCE = 0.1;\nvar STOP_SWIPE_DISTANCE = 0.01;\nvar REFRESH_INTERVAL = 20;\nvar SPEED_OFF_MULTIPLE = Math.pow(0.995, REFRESH_INTERVAL);\n\n// ================================= Hook =================================\nexport default function useTouchMove(ref, onOffset) {\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    touchPosition = _useState2[0],\n    setTouchPosition = _useState2[1];\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    lastTimestamp = _useState4[0],\n    setLastTimestamp = _useState4[1];\n  var _useState5 = useState(0),\n    _useState6 = _slicedToArray(_useState5, 2),\n    lastTimeDiff = _useState6[0],\n    setLastTimeDiff = _useState6[1];\n  var _useState7 = useState(),\n    _useState8 = _slicedToArray(_useState7, 2),\n    lastOffset = _useState8[0],\n    setLastOffset = _useState8[1];\n  var motionRef = useRef();\n\n  // ========================= Events =========================\n  // >>> Touch events\n  function onTouchStart(e) {\n    var _e$touches$ = e.touches[0],\n      screenX = _e$touches$.screenX,\n      screenY = _e$touches$.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    window.clearInterval(motionRef.current);\n  }\n  function onTouchMove(e) {\n    if (!touchPosition) return;\n\n    // e.preventDefault();\n    var _e$touches$2 = e.touches[0],\n      screenX = _e$touches$2.screenX,\n      screenY = _e$touches$2.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    var offsetX = screenX - touchPosition.x;\n    var offsetY = screenY - touchPosition.y;\n    onOffset(offsetX, offsetY);\n    var now = Date.now();\n    setLastTimestamp(now);\n    setLastTimeDiff(now - lastTimestamp);\n    setLastOffset({\n      x: offsetX,\n      y: offsetY\n    });\n  }\n  function onTouchEnd() {\n    if (!touchPosition) return;\n    setTouchPosition(null);\n    setLastOffset(null);\n\n    // Swipe if needed\n    if (lastOffset) {\n      var distanceX = lastOffset.x / lastTimeDiff;\n      var distanceY = lastOffset.y / lastTimeDiff;\n      var absX = Math.abs(distanceX);\n      var absY = Math.abs(distanceY);\n\n      // Skip swipe if low distance\n      if (Math.max(absX, absY) < MIN_SWIPE_DISTANCE) return;\n      var currentX = distanceX;\n      var currentY = distanceY;\n      motionRef.current = window.setInterval(function () {\n        if (Math.abs(currentX) < STOP_SWIPE_DISTANCE && Math.abs(currentY) < STOP_SWIPE_DISTANCE) {\n          window.clearInterval(motionRef.current);\n          return;\n        }\n        currentX *= SPEED_OFF_MULTIPLE;\n        currentY *= SPEED_OFF_MULTIPLE;\n        onOffset(currentX * REFRESH_INTERVAL, currentY * REFRESH_INTERVAL);\n      }, REFRESH_INTERVAL);\n    }\n  }\n\n  // >>> Wheel event\n  var lastWheelDirectionRef = useRef();\n  function onWheel(e) {\n    var deltaX = e.deltaX,\n      deltaY = e.deltaY;\n\n    // Convert both to x & y since wheel only happened on PC\n    var mixed = 0;\n    var absX = Math.abs(deltaX);\n    var absY = Math.abs(deltaY);\n    if (absX === absY) {\n      mixed = lastWheelDirectionRef.current === 'x' ? deltaX : deltaY;\n    } else if (absX > absY) {\n      mixed = deltaX;\n      lastWheelDirectionRef.current = 'x';\n    } else {\n      mixed = deltaY;\n      lastWheelDirectionRef.current = 'y';\n    }\n    if (onOffset(-mixed, -mixed)) {\n      e.preventDefault();\n    }\n  }\n\n  // ========================= Effect =========================\n  var touchEventsRef = useRef(null);\n  touchEventsRef.current = {\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd,\n    onWheel: onWheel\n  };\n  React.useEffect(function () {\n    function onProxyTouchStart(e) {\n      touchEventsRef.current.onTouchStart(e);\n    }\n    function onProxyTouchMove(e) {\n      touchEventsRef.current.onTouchMove(e);\n    }\n    function onProxyTouchEnd(e) {\n      touchEventsRef.current.onTouchEnd(e);\n    }\n    function onProxyWheel(e) {\n      touchEventsRef.current.onWheel(e);\n    }\n    document.addEventListener('touchmove', onProxyTouchMove, {\n      passive: false\n    });\n    document.addEventListener('touchend', onProxyTouchEnd, {\n      passive: true\n    });\n\n    // No need to clean up since element removed\n    ref.current.addEventListener('touchstart', onProxyTouchStart, {\n      passive: true\n    });\n    ref.current.addEventListener('wheel', onProxyWheel, {\n      passive: false\n    });\n    return function () {\n      document.removeEventListener('touchmove', onProxyTouchMove);\n      document.removeEventListener('touchend', onProxyTouchEnd);\n    };\n  }, []);\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAEA,IAAI,qBAAqB;AACzB,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,qBAAqB,KAAK,GAAG,CAAC,OAAO;AAG1B,SAAS,aAAa,GAAG,EAAE,QAAQ;IAChD,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,KACrB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACvC,gBAAgB,UAAU,CAAC,EAAE,EAC7B,mBAAmB,UAAU,CAAC,EAAE;IAClC,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IACxB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACxC,gBAAgB,UAAU,CAAC,EAAE,EAC7B,mBAAmB,UAAU,CAAC,EAAE;IAClC,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IACxB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACxC,eAAe,UAAU,CAAC,EAAE,EAC5B,kBAAkB,UAAU,CAAC,EAAE;IACjC,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,KACtB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACxC,aAAa,UAAU,CAAC,EAAE,EAC1B,gBAAgB,UAAU,CAAC,EAAE;IAC/B,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAErB,6DAA6D;IAC7D,mBAAmB;IACnB,SAAS,aAAa,CAAC;QACrB,IAAI,cAAc,EAAE,OAAO,CAAC,EAAE,EAC5B,UAAU,YAAY,OAAO,EAC7B,UAAU,YAAY,OAAO;QAC/B,iBAAiB;YACf,GAAG;YACH,GAAG;QACL;QACA,OAAO,aAAa,CAAC,UAAU,OAAO;IACxC;IACA,SAAS,YAAY,CAAC;QACpB,IAAI,CAAC,eAAe;QAEpB,sBAAsB;QACtB,IAAI,eAAe,EAAE,OAAO,CAAC,EAAE,EAC7B,UAAU,aAAa,OAAO,EAC9B,UAAU,aAAa,OAAO;QAChC,iBAAiB;YACf,GAAG;YACH,GAAG;QACL;QACA,IAAI,UAAU,UAAU,cAAc,CAAC;QACvC,IAAI,UAAU,UAAU,cAAc,CAAC;QACvC,SAAS,SAAS;QAClB,IAAI,MAAM,KAAK,GAAG;QAClB,iBAAiB;QACjB,gBAAgB,MAAM;QACtB,cAAc;YACZ,GAAG;YACH,GAAG;QACL;IACF;IACA,SAAS;QACP,IAAI,CAAC,eAAe;QACpB,iBAAiB;QACjB,cAAc;QAEd,kBAAkB;QAClB,IAAI,YAAY;YACd,IAAI,YAAY,WAAW,CAAC,GAAG;YAC/B,IAAI,YAAY,WAAW,CAAC,GAAG;YAC/B,IAAI,OAAO,KAAK,GAAG,CAAC;YACpB,IAAI,OAAO,KAAK,GAAG,CAAC;YAEpB,6BAA6B;YAC7B,IAAI,KAAK,GAAG,CAAC,MAAM,QAAQ,oBAAoB;YAC/C,IAAI,WAAW;YACf,IAAI,WAAW;YACf,UAAU,OAAO,GAAG,OAAO,WAAW,CAAC;gBACrC,IAAI,KAAK,GAAG,CAAC,YAAY,uBAAuB,KAAK,GAAG,CAAC,YAAY,qBAAqB;oBACxF,OAAO,aAAa,CAAC,UAAU,OAAO;oBACtC;gBACF;gBACA,YAAY;gBACZ,YAAY;gBACZ,SAAS,WAAW,kBAAkB,WAAW;YACnD,GAAG;QACL;IACF;IAEA,kBAAkB;IAClB,IAAI,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACjC,SAAS,QAAQ,CAAC;QAChB,IAAI,SAAS,EAAE,MAAM,EACnB,SAAS,EAAE,MAAM;QAEnB,wDAAwD;QACxD,IAAI,QAAQ;QACZ,IAAI,OAAO,KAAK,GAAG,CAAC;QACpB,IAAI,OAAO,KAAK,GAAG,CAAC;QACpB,IAAI,SAAS,MAAM;YACjB,QAAQ,sBAAsB,OAAO,KAAK,MAAM,SAAS;QAC3D,OAAO,IAAI,OAAO,MAAM;YACtB,QAAQ;YACR,sBAAsB,OAAO,GAAG;QAClC,OAAO;YACL,QAAQ;YACR,sBAAsB,OAAO,GAAG;QAClC;QACA,IAAI,SAAS,CAAC,OAAO,CAAC,QAAQ;YAC5B,EAAE,cAAc;QAClB;IACF;IAEA,6DAA6D;IAC7D,IAAI,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,eAAe,OAAO,GAAG;QACvB,cAAc;QACd,aAAa;QACb,YAAY;QACZ,SAAS;IACX;IACA,6JAAA,CAAA,YAAe;kCAAC;YACd,SAAS,kBAAkB,CAAC;gBAC1B,eAAe,OAAO,CAAC,YAAY,CAAC;YACtC;YACA,SAAS,iBAAiB,CAAC;gBACzB,eAAe,OAAO,CAAC,WAAW,CAAC;YACrC;YACA,SAAS,gBAAgB,CAAC;gBACxB,eAAe,OAAO,CAAC,UAAU,CAAC;YACpC;YACA,SAAS,aAAa,CAAC;gBACrB,eAAe,OAAO,CAAC,OAAO,CAAC;YACjC;YACA,SAAS,gBAAgB,CAAC,aAAa,kBAAkB;gBACvD,SAAS;YACX;YACA,SAAS,gBAAgB,CAAC,YAAY,iBAAiB;gBACrD,SAAS;YACX;YAEA,4CAA4C;YAC5C,IAAI,OAAO,CAAC,gBAAgB,CAAC,cAAc,mBAAmB;gBAC5D,SAAS;YACX;YACA,IAAI,OAAO,CAAC,gBAAgB,CAAC,SAAS,cAAc;gBAClD,SAAS;YACX;YACA;0CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,mBAAmB,CAAC,YAAY;gBAC3C;;QACF;iCAAG,EAAE;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/hooks/useUpdate.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport { useRef, useState } from 'react';\n\n/**\n * Help to merge callback with `useLayoutEffect`.\n * One time will only trigger once.\n */\nexport default function useUpdate(callback) {\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    count = _useState2[0],\n    setCount = _useState2[1];\n  var effectRef = useRef(0);\n  var callbackRef = useRef();\n  callbackRef.current = callback;\n\n  // Trigger on `useLayoutEffect`\n  useLayoutUpdateEffect(function () {\n    var _callbackRef$current;\n    (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 || _callbackRef$current.call(callbackRef);\n  }, [count]);\n\n  // Trigger to update count\n  return function () {\n    if (effectRef.current !== count) {\n      return;\n    }\n    effectRef.current += 1;\n    setCount(effectRef.current);\n  };\n}\nexport function useUpdateState(defaultState) {\n  var batchRef = useRef([]);\n  var _useState3 = useState({}),\n    _useState4 = _slicedToArray(_useState3, 2),\n    forceUpdate = _useState4[1];\n  var state = useRef(typeof defaultState === 'function' ? defaultState() : defaultState);\n  var flushUpdate = useUpdate(function () {\n    var current = state.current;\n    batchRef.current.forEach(function (callback) {\n      current = callback(current);\n    });\n    batchRef.current = [];\n    state.current = current;\n    forceUpdate({});\n  });\n  function updater(callback) {\n    batchRef.current.push(callback);\n    flushUpdate();\n  }\n  return [state.current, updater];\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAMe,SAAS,UAAU,QAAQ;IACxC,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IACvB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACvC,QAAQ,UAAU,CAAC,EAAE,EACrB,WAAW,UAAU,CAAC,EAAE;IAC1B,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,IAAI,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACvB,YAAY,OAAO,GAAG;IAEtB,+BAA+B;IAC/B,CAAA,GAAA,+JAAA,CAAA,wBAAqB,AAAD;2CAAE;YACpB,IAAI;YACJ,CAAC,uBAAuB,YAAY,OAAO,MAAM,QAAQ,yBAAyB,KAAK,KAAK,qBAAqB,IAAI,CAAC;QACxH;0CAAG;QAAC;KAAM;IAEV,0BAA0B;IAC1B,OAAO;QACL,IAAI,UAAU,OAAO,KAAK,OAAO;YAC/B;QACF;QACA,UAAU,OAAO,IAAI;QACrB,SAAS,UAAU,OAAO;IAC5B;AACF;AACO,SAAS,eAAe,YAAY;IACzC,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IACxB,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,IACzB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACxC,cAAc,UAAU,CAAC,EAAE;IAC7B,IAAI,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,OAAO,iBAAiB,aAAa,iBAAiB;IACzE,IAAI,cAAc;iDAAU;YAC1B,IAAI,UAAU,MAAM,OAAO;YAC3B,SAAS,OAAO,CAAC,OAAO;yDAAC,SAAU,QAAQ;oBACzC,UAAU,SAAS;gBACrB;;YACA,SAAS,OAAO,GAAG,EAAE;YACrB,MAAM,OAAO,GAAG;YAChB,YAAY,CAAC;QACf;;IACA,SAAS,QAAQ,QAAQ;QACvB,SAAS,OAAO,CAAC,IAAI,CAAC;QACtB;IACF;IACA,OAAO;QAAC,MAAM,OAAO;QAAE;KAAQ;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/hooks/useVisibleRange.js"], "sourcesContent": ["import { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0,\n  right: 0\n};\nexport default function useVisibleRange(tabOffsets, visibleTabContentValue, transform, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, _ref) {\n  var tabs = _ref.tabs,\n    tabPosition = _ref.tabPosition,\n    rtl = _ref.rtl;\n  var charUnit;\n  var position;\n  var transformSize;\n  if (['top', 'bottom'].includes(tabPosition)) {\n    charUnit = 'width';\n    position = rtl ? 'right' : 'left';\n    transformSize = Math.abs(transform);\n  } else {\n    charUnit = 'height';\n    position = 'top';\n    transformSize = -transform;\n  }\n  return useMemo(function () {\n    if (!tabs.length) {\n      return [0, 0];\n    }\n    var len = tabs.length;\n    var endIndex = len;\n    for (var i = 0; i < len; i += 1) {\n      var offset = tabOffsets.get(tabs[i].key) || DEFAULT_SIZE;\n      if (Math.floor(offset[position] + offset[charUnit]) > Math.floor(transformSize + visibleTabContentValue)) {\n        endIndex = i - 1;\n        break;\n      }\n    }\n    var startIndex = 0;\n    for (var _i = len - 1; _i >= 0; _i -= 1) {\n      var _offset = tabOffsets.get(tabs[_i].key) || DEFAULT_SIZE;\n      if (_offset[position] < transformSize) {\n        startIndex = _i + 1;\n        break;\n      }\n    }\n    return startIndex >= endIndex ? [0, 0] : [startIndex, endIndex];\n  }, [tabOffsets, visibleTabContentValue, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, transformSize, tabPosition, tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), rtl]);\n}"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI,eAAe;IACjB,OAAO;IACP,QAAQ;IACR,MAAM;IACN,KAAK;IACL,OAAO;AACT;AACe,SAAS,gBAAgB,UAAU,EAAE,sBAAsB,EAAE,SAAS,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,IAAI;IACxJ,IAAI,OAAO,KAAK,IAAI,EAClB,cAAc,KAAK,WAAW,EAC9B,MAAM,KAAK,GAAG;IAChB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;QAAC;QAAO;KAAS,CAAC,QAAQ,CAAC,cAAc;QAC3C,WAAW;QACX,WAAW,MAAM,UAAU;QAC3B,gBAAgB,KAAK,GAAG,CAAC;IAC3B,OAAO;QACL,WAAW;QACX,WAAW;QACX,gBAAgB,CAAC;IACnB;IACA,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mCAAE;YACb,IAAI,CAAC,KAAK,MAAM,EAAE;gBAChB,OAAO;oBAAC;oBAAG;iBAAE;YACf;YACA,IAAI,MAAM,KAAK,MAAM;YACrB,IAAI,WAAW;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;gBAC/B,IAAI,SAAS,WAAW,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK;gBAC5C,IAAI,KAAK,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,KAAK,KAAK,CAAC,gBAAgB,yBAAyB;oBACxG,WAAW,IAAI;oBACf;gBACF;YACF;YACA,IAAI,aAAa;YACjB,IAAK,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,EAAG;gBACvC,IAAI,UAAU,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK;gBAC9C,IAAI,OAAO,CAAC,SAAS,GAAG,eAAe;oBACrC,aAAa,KAAK;oBAClB;gBACF;YACF;YACA,OAAO,cAAc,WAAW;gBAAC;gBAAG;aAAE,GAAG;gBAAC;gBAAY;aAAS;QACjE;kCAAG;QAAC;QAAY;QAAwB;QAAqB;QAAkB;QAAwB;QAAe;QAAa,KAAK,GAAG;uCAAC,SAAU,GAAG;gBACvJ,OAAO,IAAI,GAAG;YAChB;sCAAG,IAAI,CAAC;QAAM;KAAI;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/util.js"], "sourcesContent": ["/**\n * We trade Map as deps which may change with same value but different ref object.\n * We should make it as hash for deps\n * */\nexport function stringify(obj) {\n  var tgt;\n  if (obj instanceof Map) {\n    tgt = {};\n    obj.forEach(function (v, k) {\n      tgt[k] = v;\n    });\n  } else {\n    tgt = obj;\n  }\n  return JSON.stringify(tgt);\n}\nvar RC_TABS_DOUBLE_QUOTE = 'TABS_DQ';\nexport function genDataNodeKey(key) {\n  return String(key).replace(/\"/g, RC_TABS_DOUBLE_QUOTE);\n}\nexport function getRemovable(closable, closeIcon, editable, disabled) {\n  if (\n  // Only editable tabs can be removed\n  !editable ||\n  // Tabs cannot be removed when disabled\n  disabled ||\n  // closable is false\n  closable === false ||\n  // If closable is undefined, the remove button should be hidden when closeIcon is null or false\n  closable === undefined && (closeIcon === false || closeIcon === null)) {\n    return false;\n  }\n  return true;\n}"], "names": [], "mappings": "AAAA;;;GAGG;;;;;AACI,SAAS,UAAU,GAAG;IAC3B,IAAI;IACJ,IAAI,eAAe,KAAK;QACtB,MAAM,CAAC;QACP,IAAI,OAAO,CAAC,SAAU,CAAC,EAAE,CAAC;YACxB,GAAG,CAAC,EAAE,GAAG;QACX;IACF,OAAO;QACL,MAAM;IACR;IACA,OAAO,KAAK,SAAS,CAAC;AACxB;AACA,IAAI,uBAAuB;AACpB,SAAS,eAAe,GAAG;IAChC,OAAO,OAAO,KAAK,OAAO,CAAC,MAAM;AACnC;AACO,SAAS,aAAa,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ;IAClE,IACA,oCAAoC;IACpC,CAAC,YACD,uCAAuC;IACvC,YACA,oBAAoB;IACpB,aAAa,SACb,+FAA+F;IAC/F,aAAa,aAAa,CAAC,cAAc,SAAS,cAAc,IAAI,GAAG;QACrE,OAAO;IACT;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/TabNavList/AddButton.js"], "sourcesContent": ["import * as React from 'react';\nvar AddButton = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    editable = props.editable,\n    locale = props.locale,\n    style = props.style;\n  if (!editable || editable.showAdd === false) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-add\"),\n    style: style,\n    \"aria-label\": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',\n    onClick: function onClick(event) {\n      editable.onEdit('add', {\n        event: event\n      });\n    }\n  }, editable.addIcon || '+');\n});\nexport default AddButton;"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI,YAAY,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IAChE,IAAI,YAAY,MAAM,SAAS,EAC7B,WAAW,MAAM,QAAQ,EACzB,SAAS,MAAM,MAAM,EACrB,QAAQ,MAAM,KAAK;IACrB,IAAI,CAAC,YAAY,SAAS,OAAO,KAAK,OAAO;QAC3C,OAAO;IACT;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,UAAU;QAChD,KAAK;QACL,MAAM;QACN,WAAW,GAAG,MAAM,CAAC,WAAW;QAChC,OAAO;QACP,cAAc,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,YAAY,KAAK;QACvF,SAAS,SAAS,QAAQ,KAAK;YAC7B,SAAS,MAAM,CAAC,OAAO;gBACrB,OAAO;YACT;QACF;IACF,GAAG,SAAS,OAAO,IAAI;AACzB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/TabNavList/ExtraContent.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nvar ExtraContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var position = props.position,\n    prefixCls = props.prefixCls,\n    extra = props.extra;\n  if (!extra) {\n    return null;\n  }\n  var content;\n\n  // Parse extra\n  var assertExtra = {};\n  if (_typeof(extra) === 'object' && ! /*#__PURE__*/React.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n  return content ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\"),\n    ref: ref\n  }, content) : null;\n});\nif (process.env.NODE_ENV !== 'production') {\n  ExtraContent.displayName = 'ExtraContent';\n}\nexport default ExtraContent;"], "names": [], "mappings": ";;;AA6BI;AA7BJ;AACA;;;AACA,IAAI,eAAe,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IACnE,IAAI,WAAW,MAAM,QAAQ,EAC3B,YAAY,MAAM,SAAS,EAC3B,QAAQ,MAAM,KAAK;IACrB,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,IAAI;IAEJ,cAAc;IACd,IAAI,cAAc,CAAC;IACnB,IAAI,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,WAAW,YAAY,CAAE,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,QAAQ;QAC7E,cAAc;IAChB,OAAO;QACL,YAAY,KAAK,GAAG;IACtB;IACA,IAAI,aAAa,SAAS;QACxB,UAAU,YAAY,KAAK;IAC7B;IACA,IAAI,aAAa,QAAQ;QACvB,UAAU,YAAY,IAAI;IAC5B;IACA,OAAO,UAAU,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACvD,WAAW,GAAG,MAAM,CAAC,WAAW;QAChC,KAAK;IACP,GAAG,WAAW;AAChB;AACA,wCAA2C;IACzC,aAAa,WAAW,GAAG;AAC7B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/TabNavList/OperationNode.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport Dropdown from 'rc-dropdown';\nimport Menu, { MenuItem } from 'rc-menu';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { useEffect, useState } from 'react';\nimport { getRemovable } from \"../util\";\nimport AddButton from \"./AddButton\";\nvar OperationNode = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    tabs = props.tabs,\n    locale = props.locale,\n    mobile = props.mobile,\n    _props$more = props.more,\n    moreProps = _props$more === void 0 ? {} : _props$more,\n    style = props.style,\n    className = props.className,\n    editable = props.editable,\n    tabBarGutter = props.tabBarGutter,\n    rtl = props.rtl,\n    removeAriaLabel = props.removeAriaLabel,\n    onTabClick = props.onTabClick,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName;\n  // ======================== Dropdown ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedKey = _useState4[0],\n    setSelectedKey = _useState4[1];\n  var _moreProps$icon = moreProps.icon,\n    moreIcon = _moreProps$icon === void 0 ? 'More' : _moreProps$icon;\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var menu = /*#__PURE__*/React.createElement(Menu, {\n    onClick: function onClick(_ref) {\n      var key = _ref.key,\n        domEvent = _ref.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    prefixCls: \"\".concat(dropdownPrefix, \"-menu\"),\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var closable = tab.closable,\n      disabled = tab.disabled,\n      closeIcon = tab.closeIcon,\n      key = tab.key,\n      label = tab.label;\n    var removable = getRemovable(closable, closeIcon, editable, disabled);\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: key,\n      id: \"\".concat(popupId, \"-\").concat(key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n      disabled: disabled\n    }, /*#__PURE__*/React.createElement(\"span\", null, label), removable && /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, key);\n      }\n    }, closeIcon || editable.removeIcon || '×'));\n  }));\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n  function onKeyDown(e) {\n    var which = e.which;\n    if (!open) {\n      if ([KeyCode.DOWN, KeyCode.SPACE, KeyCode.ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n      return;\n    }\n    switch (which) {\n      case KeyCode.UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n      case KeyCode.DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n      case KeyCode.ESC:\n        setOpen(false);\n        break;\n      case KeyCode.SPACE:\n      case KeyCode.ENTER:\n        if (selectedKey !== null) {\n          onTabClick(selectedKey, e);\n        }\n        break;\n    }\n  }\n\n  // ========================= Effect =========================\n  useEffect(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  useEffect(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]);\n\n  // ========================= Render =========================\n  var moreStyle = _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n  var overlayClassName = classNames(_defineProperty({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/React.createElement(Dropdown, _extends({\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    visible: tabs.length ? open : false,\n    onVisibleChange: setOpen,\n    overlayClassName: classNames(overlayClassName, popupClassName),\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    getPopupContainer: getPopupContainer\n  }, moreProps), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/React.createElement(AddButton, {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n});\nexport default /*#__PURE__*/React.memo(OperationNode, function (_, next) {\n  return (\n    // https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;AACA,IAAI,gBAAgB,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IACpE,IAAI,YAAY,MAAM,SAAS,EAC7B,KAAK,MAAM,EAAE,EACb,OAAO,MAAM,IAAI,EACjB,SAAS,MAAM,MAAM,EACrB,SAAS,MAAM,MAAM,EACrB,cAAc,MAAM,IAAI,EACxB,YAAY,gBAAgB,KAAK,IAAI,CAAC,IAAI,aAC1C,QAAQ,MAAM,KAAK,EACnB,YAAY,MAAM,SAAS,EAC3B,WAAW,MAAM,QAAQ,EACzB,eAAe,MAAM,YAAY,EACjC,MAAM,MAAM,GAAG,EACf,kBAAkB,MAAM,eAAe,EACvC,aAAa,MAAM,UAAU,EAC7B,oBAAoB,MAAM,iBAAiB,EAC3C,iBAAiB,MAAM,cAAc;IACvC,6DAA6D;IAC7D,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QACvB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACvC,OAAO,UAAU,CAAC,EAAE,EACpB,UAAU,UAAU,CAAC,EAAE;IACzB,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OACxB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACxC,cAAc,UAAU,CAAC,EAAE,EAC3B,iBAAiB,UAAU,CAAC,EAAE;IAChC,IAAI,kBAAkB,UAAU,IAAI,EAClC,WAAW,oBAAoB,KAAK,IAAI,SAAS;IACnD,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI;IAC5B,IAAI,iBAAiB,GAAG,MAAM,CAAC,WAAW;IAC1C,IAAI,iBAAiB,gBAAgB,OAAO,GAAG,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,eAAe;IAC1F,IAAI,oBAAoB,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB;IAChG,SAAS,YAAY,KAAK,EAAE,GAAG;QAC7B,MAAM,cAAc;QACpB,MAAM,eAAe;QACrB,SAAS,MAAM,CAAC,UAAU;YACxB,KAAK;YACL,OAAO;QACT;IACF;IACA,IAAI,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,UAAI,EAAE;QAChD,SAAS,SAAS,QAAQ,IAAI;YAC5B,IAAI,MAAM,KAAK,GAAG,EAChB,WAAW,KAAK,QAAQ;YAC1B,WAAW,KAAK;YAChB,QAAQ;QACV;QACA,WAAW,GAAG,MAAM,CAAC,gBAAgB;QACrC,IAAI;QACJ,UAAU,CAAC;QACX,MAAM;QACN,yBAAyB;QACzB,cAAc;YAAC;SAAY;QAC3B,cAAc,sBAAsB,YAAY,oBAAoB;IACtE,GAAG,KAAK,GAAG,CAAC,SAAU,GAAG;QACvB,IAAI,WAAW,IAAI,QAAQ,EACzB,WAAW,IAAI,QAAQ,EACvB,YAAY,IAAI,SAAS,EACzB,MAAM,IAAI,GAAG,EACb,QAAQ,IAAI,KAAK;QACnB,IAAI,YAAY,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,UAAU,WAAW,UAAU;QAC5D,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,sLAAA,CAAA,WAAQ,EAAE;YAChD,KAAK;YACL,IAAI,GAAG,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC;YACnC,MAAM;YACN,iBAAiB,MAAM,GAAG,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC;YACvD,UAAU;QACZ,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ,MAAM,QAAQ,aAAa,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,UAAU;YAChH,MAAM;YACN,cAAc,mBAAmB;YACjC,UAAU;YACV,WAAW,GAAG,MAAM,CAAC,gBAAgB;YACrC,SAAS,SAAS,QAAQ,CAAC;gBACzB,EAAE,eAAe;gBACjB,YAAY,GAAG;YACjB;QACF,GAAG,aAAa,SAAS,UAAU,IAAI;IACzC;IACA,SAAS,aAAa,MAAM;QAC1B,IAAI,cAAc,KAAK,MAAM,CAAC,SAAU,GAAG;YACzC,OAAO,CAAC,IAAI,QAAQ;QACtB;QACA,IAAI,gBAAgB,YAAY,SAAS,CAAC,SAAU,GAAG;YACrD,OAAO,IAAI,GAAG,KAAK;QACrB,MAAM;QACN,IAAI,MAAM,YAAY,MAAM;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;YAC/B,gBAAgB,CAAC,gBAAgB,SAAS,GAAG,IAAI;YACjD,IAAI,MAAM,WAAW,CAAC,cAAc;YACpC,IAAI,CAAC,IAAI,QAAQ,EAAE;gBACjB,eAAe,IAAI,GAAG;gBACtB;YACF;QACF;IACF;IACA,SAAS,UAAU,CAAC;QAClB,IAAI,QAAQ,EAAE,KAAK;QACnB,IAAI,CAAC,MAAM;YACT,IAAI;gBAAC,8IAAA,CAAA,UAAO,CAAC,IAAI;gBAAE,8IAAA,CAAA,UAAO,CAAC,KAAK;gBAAE,8IAAA,CAAA,UAAO,CAAC,KAAK;aAAC,CAAC,QAAQ,CAAC,QAAQ;gBAChE,QAAQ;gBACR,EAAE,cAAc;YAClB;YACA;QACF;QACA,OAAQ;YACN,KAAK,8IAAA,CAAA,UAAO,CAAC,EAAE;gBACb,aAAa,CAAC;gBACd,EAAE,cAAc;gBAChB;YACF,KAAK,8IAAA,CAAA,UAAO,CAAC,IAAI;gBACf,aAAa;gBACb,EAAE,cAAc;gBAChB;YACF,KAAK,8IAAA,CAAA,UAAO,CAAC,GAAG;gBACd,QAAQ;gBACR;YACF,KAAK,8IAAA,CAAA,UAAO,CAAC,KAAK;YAClB,KAAK,8IAAA,CAAA,UAAO,CAAC,KAAK;gBAChB,IAAI,gBAAgB,MAAM;oBACxB,WAAW,aAAa;gBAC1B;gBACA;QACJ;IACF;IAEA,6DAA6D;IAC7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,0DAA0D;YAC1D,IAAI,MAAM,SAAS,cAAc,CAAC;YAClC,IAAI,OAAO,IAAI,cAAc,EAAE;gBAC7B,IAAI,cAAc,CAAC;YACrB;QACF;kCAAG;QAAC;KAAY;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,MAAM;gBACT,eAAe;YACjB;QACF;kCAAG;QAAC;KAAK;IAET,6DAA6D;IAC7D,IAAI,YAAY,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,MAAM,gBAAgB,cAAc;IACxE,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,UAAU,UAAU,GAAG;QACvB,UAAU,KAAK,GAAG;IACpB;IACA,IAAI,mBAAmB,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,gBAAgB,SAAS;IACzF,IAAI,WAAW,SAAS,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,gJAAA,CAAA,UAAQ,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACjF,WAAW;QACX,SAAS;QACT,SAAS,KAAK,MAAM,GAAG,OAAO;QAC9B,iBAAiB;QACjB,kBAAkB,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,kBAAkB;QAC/C,iBAAiB;QACjB,iBAAiB;QACjB,mBAAmB;IACrB,GAAG,YAAY,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,UAAU;QACxD,MAAM;QACN,WAAW,GAAG,MAAM,CAAC,WAAW;QAChC,OAAO;QACP,iBAAiB;QACjB,iBAAiB;QACjB,IAAI,GAAG,MAAM,CAAC,IAAI;QAClB,iBAAiB;QACjB,WAAW;IACb,GAAG;IACH,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAC7C,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,MAAM,CAAC,WAAW,oBAAoB;QAC/D,OAAO;QACP,KAAK;IACP,GAAG,UAAU,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,8JAAA,CAAA,UAAS,EAAE;QACvD,WAAW;QACX,QAAQ;QACR,UAAU;IACZ;AACF;uCACe,WAAW,GAAE,6JAAA,CAAA,OAAU,CAAC,eAAe,SAAU,CAAC,EAAE,IAAI;IACrE,OACE,wDAAwD;IACxD,4EAA4E;IAC5E,KAAK,SAAS;AAElB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/TabNavList/TabNode.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { genDataNodeKey, getRemovable } from \"../util\";\nvar TabNode = function TabNode(props) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    active = props.active,\n    focus = props.focus,\n    _props$tab = props.tab,\n    key = _props$tab.key,\n    label = _props$tab.label,\n    disabled = _props$tab.disabled,\n    closeIcon = _props$tab.closeIcon,\n    icon = _props$tab.icon,\n    closable = props.closable,\n    renderWrapper = props.renderWrapper,\n    removeAriaLabel = props.removeAriaLabel,\n    editable = props.editable,\n    onClick = props.onClick,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onKeyDown = props.onKeyDown,\n    onMouseDown = props.onMouseDown,\n    onMouseUp = props.onMouseUp,\n    style = props.style,\n    tabCount = props.tabCount,\n    currentPosition = props.currentPosition;\n  var tabPrefix = \"\".concat(prefixCls, \"-tab\");\n  var removable = getRemovable(closable, closeIcon, editable, disabled);\n  function onInternalClick(e) {\n    if (disabled) {\n      return;\n    }\n    onClick(e);\n  }\n  function onRemoveTab(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var labelNode = React.useMemo(function () {\n    return icon && typeof label === 'string' ? /*#__PURE__*/React.createElement(\"span\", null, label) : label;\n  }, [label, icon]);\n  var btnRef = React.useRef(null);\n  React.useEffect(function () {\n    if (focus && btnRef.current) {\n      btnRef.current.focus();\n    }\n  }, [focus]);\n  var node = /*#__PURE__*/React.createElement(\"div\", {\n    key: key,\n    \"data-node-key\": genDataNodeKey(key),\n    className: classNames(tabPrefix, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(tabPrefix, \"-with-remove\"), removable), \"\".concat(tabPrefix, \"-active\"), active), \"\".concat(tabPrefix, \"-disabled\"), disabled), \"\".concat(tabPrefix, \"-focus\"), focus)),\n    style: style,\n    onClick: onInternalClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: btnRef,\n    role: \"tab\",\n    \"aria-selected\": active,\n    id: id && \"\".concat(id, \"-tab-\").concat(key),\n    className: \"\".concat(tabPrefix, \"-btn\"),\n    \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n    \"aria-disabled\": disabled,\n    tabIndex: disabled ? null : active ? 0 : -1,\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onInternalClick(e);\n    },\n    onKeyDown: onKeyDown,\n    onMouseDown: onMouseDown,\n    onMouseUp: onMouseUp,\n    onFocus: onFocus,\n    onBlur: onBlur\n  }, focus && /*#__PURE__*/React.createElement(\"div\", {\n    \"aria-live\": \"polite\",\n    style: {\n      width: 0,\n      height: 0,\n      position: 'absolute',\n      overflow: 'hidden',\n      opacity: 0\n    }\n  }, \"Tab \".concat(currentPosition, \" of \").concat(tabCount)), icon && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(tabPrefix, \"-icon\")\n  }, icon), label && labelNode), removable && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    role: \"tab\",\n    \"aria-label\": removeAriaLabel || 'remove',\n    tabIndex: active ? 0 : -1,\n    className: \"\".concat(tabPrefix, \"-remove\"),\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onRemoveTab(e);\n    }\n  }, closeIcon || editable.removeIcon || '×'));\n  return renderWrapper ? renderWrapper(node) : node;\n};\nexport default TabNode;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,IAAI,UAAU,SAAS,QAAQ,KAAK;IAClC,IAAI,YAAY,MAAM,SAAS,EAC7B,KAAK,MAAM,EAAE,EACb,SAAS,MAAM,MAAM,EACrB,QAAQ,MAAM,KAAK,EACnB,aAAa,MAAM,GAAG,EACtB,MAAM,WAAW,GAAG,EACpB,QAAQ,WAAW,KAAK,EACxB,WAAW,WAAW,QAAQ,EAC9B,YAAY,WAAW,SAAS,EAChC,OAAO,WAAW,IAAI,EACtB,WAAW,MAAM,QAAQ,EACzB,gBAAgB,MAAM,aAAa,EACnC,kBAAkB,MAAM,eAAe,EACvC,WAAW,MAAM,QAAQ,EACzB,UAAU,MAAM,OAAO,EACvB,UAAU,MAAM,OAAO,EACvB,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,cAAc,MAAM,WAAW,EAC/B,YAAY,MAAM,SAAS,EAC3B,QAAQ,MAAM,KAAK,EACnB,WAAW,MAAM,QAAQ,EACzB,kBAAkB,MAAM,eAAe;IACzC,IAAI,YAAY,GAAG,MAAM,CAAC,WAAW;IACrC,IAAI,YAAY,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,UAAU,WAAW,UAAU;IAC5D,SAAS,gBAAgB,CAAC;QACxB,IAAI,UAAU;YACZ;QACF;QACA,QAAQ;IACV;IACA,SAAS,YAAY,KAAK;QACxB,MAAM,cAAc;QACpB,MAAM,eAAe;QACrB,SAAS,MAAM,CAAC,UAAU;YACxB,KAAK;YACL,OAAO;QACT;IACF;IACA,IAAI,YAAY,6JAAA,CAAA,UAAa;sCAAC;YAC5B,OAAO,QAAQ,OAAO,UAAU,WAAW,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ,MAAM,SAAS;QACrG;qCAAG;QAAC;QAAO;KAAK;IAChB,IAAI,SAAS,6JAAA,CAAA,SAAY,CAAC;IAC1B,6JAAA,CAAA,YAAe;6BAAC;YACd,IAAI,SAAS,OAAO,OAAO,EAAE;gBAC3B,OAAO,OAAO,CAAC,KAAK;YACtB;QACF;4BAAG;QAAC;KAAM;IACV,IAAI,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACjD,KAAK;QACL,iBAAiB,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE;QAChC,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,WAAW,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,iBAAiB,YAAY,GAAG,MAAM,CAAC,WAAW,YAAY,SAAS,GAAG,MAAM,CAAC,WAAW,cAAc,WAAW,GAAG,MAAM,CAAC,WAAW,WAAW;QAC/Q,OAAO;QACP,SAAS;IACX,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACzC,KAAK;QACL,MAAM;QACN,iBAAiB;QACjB,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC;QACxC,WAAW,GAAG,MAAM,CAAC,WAAW;QAChC,iBAAiB,MAAM,GAAG,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC;QACvD,iBAAiB;QACjB,UAAU,WAAW,OAAO,SAAS,IAAI,CAAC;QAC1C,SAAS,SAAS,QAAQ,CAAC;YACzB,EAAE,eAAe;YACjB,gBAAgB;QAClB;QACA,WAAW;QACX,aAAa;QACb,WAAW;QACX,SAAS;QACT,QAAQ;IACV,GAAG,SAAS,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAClD,aAAa;QACb,OAAO;YACL,OAAO;YACP,QAAQ;YACR,UAAU;YACV,UAAU;YACV,SAAS;QACX;IACF,GAAG,OAAO,MAAM,CAAC,iBAAiB,QAAQ,MAAM,CAAC,YAAY,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC5G,WAAW,GAAG,MAAM,CAAC,WAAW;IAClC,GAAG,OAAO,SAAS,YAAY,aAAa,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,UAAU;QACrF,MAAM;QACN,MAAM;QACN,cAAc,mBAAmB;QACjC,UAAU,SAAS,IAAI,CAAC;QACxB,WAAW,GAAG,MAAM,CAAC,WAAW;QAChC,SAAS,SAAS,QAAQ,CAAC;YACzB,EAAE,eAAe;YACjB,YAAY;QACd;IACF,GAAG,aAAa,SAAS,UAAU,IAAI;IACvC,OAAO,gBAAgB,cAAc,QAAQ;AAC/C;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/TabNavList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable react-hooks/exhaustive-deps */\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport TabContext from \"../TabContext\";\nimport useIndicator from \"../hooks/useIndicator\";\nimport useOffsets from \"../hooks/useOffsets\";\nimport useSyncState from \"../hooks/useSyncState\";\nimport useTouchMove from \"../hooks/useTouchMove\";\nimport useUpdate, { useUpdateState } from \"../hooks/useUpdate\";\nimport useVisibleRange from \"../hooks/useVisibleRange\";\nimport { genDataNodeKey, getRemovable, stringify } from \"../util\";\nimport AddButton from \"./AddButton\";\nimport ExtraContent from \"./ExtraContent\";\nimport OperationNode from \"./OperationNode\";\nimport TabNode from \"./TabNode\";\nvar getTabSize = function getTabSize(tab, containerRect) {\n  // tabListRef\n  var offsetWidth = tab.offsetWidth,\n    offsetHeight = tab.offsetHeight,\n    offsetTop = tab.offsetTop,\n    offsetLeft = tab.offsetLeft;\n  var _tab$getBoundingClien = tab.getBoundingClientRect(),\n    width = _tab$getBoundingClien.width,\n    height = _tab$getBoundingClien.height,\n    left = _tab$getBoundingClien.left,\n    top = _tab$getBoundingClien.top;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (Math.abs(width - offsetWidth) < 1) {\n    return [width, height, left - containerRect.left, top - containerRect.top];\n  }\n  return [offsetWidth, offsetHeight, offsetLeft, offsetTop];\n};\nvar getSize = function getSize(refObj) {\n  var _ref = refObj.current || {},\n    _ref$offsetWidth = _ref.offsetWidth,\n    offsetWidth = _ref$offsetWidth === void 0 ? 0 : _ref$offsetWidth,\n    _ref$offsetHeight = _ref.offsetHeight,\n    offsetHeight = _ref$offsetHeight === void 0 ? 0 : _ref$offsetHeight;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (refObj.current) {\n    var _refObj$current$getBo = refObj.current.getBoundingClientRect(),\n      width = _refObj$current$getBo.width,\n      height = _refObj$current$getBo.height;\n    if (Math.abs(width - offsetWidth) < 1) {\n      return [width, height];\n    }\n  }\n  return [offsetWidth, offsetHeight];\n};\n\n/**\n * Convert `SizeInfo` to unit value. Such as [123, 456] with `top` position get `123`\n */\nvar getUnitValue = function getUnitValue(size, tabPositionTopOrBottom) {\n  return size[tabPositionTopOrBottom ? 0 : 1];\n};\nvar TabNavList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    style = props.style,\n    id = props.id,\n    animated = props.animated,\n    activeKey = props.activeKey,\n    rtl = props.rtl,\n    extra = props.extra,\n    editable = props.editable,\n    locale = props.locale,\n    tabPosition = props.tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    children = props.children,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    indicator = props.indicator;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var containerRef = useRef(null);\n  var extraLeftRef = useRef(null);\n  var extraRightRef = useRef(null);\n  var tabsWrapperRef = useRef(null);\n  var tabListRef = useRef(null);\n  var operationsRef = useRef(null);\n  var innerAddButtonRef = useRef(null);\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n  var _useSyncState = useSyncState(0, function (next, prev) {\n      if (tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'left' : 'right'\n        });\n      }\n    }),\n    _useSyncState2 = _slicedToArray(_useSyncState, 2),\n    transformLeft = _useSyncState2[0],\n    setTransformLeft = _useSyncState2[1];\n  var _useSyncState3 = useSyncState(0, function (next, prev) {\n      if (!tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'top' : 'bottom'\n        });\n      }\n    }),\n    _useSyncState4 = _slicedToArray(_useSyncState3, 2),\n    transformTop = _useSyncState4[0],\n    setTransformTop = _useSyncState4[1];\n  var _useState = useState([0, 0]),\n    _useState2 = _slicedToArray(_useState, 2),\n    containerExcludeExtraSize = _useState2[0],\n    setContainerExcludeExtraSize = _useState2[1];\n  var _useState3 = useState([0, 0]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    tabContentSize = _useState4[0],\n    setTabContentSize = _useState4[1];\n  var _useState5 = useState([0, 0]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    addSize = _useState6[0],\n    setAddSize = _useState6[1];\n  var _useState7 = useState([0, 0]),\n    _useState8 = _slicedToArray(_useState7, 2),\n    operationSize = _useState8[0],\n    setOperationSize = _useState8[1];\n  var _useUpdateState = useUpdateState(new Map()),\n    _useUpdateState2 = _slicedToArray(_useUpdateState, 2),\n    tabSizes = _useUpdateState2[0],\n    setTabSizes = _useUpdateState2[1];\n  var tabOffsets = useOffsets(tabs, tabSizes, tabContentSize[0]);\n\n  // ========================== Unit =========================\n  var containerExcludeExtraSizeValue = getUnitValue(containerExcludeExtraSize, tabPositionTopOrBottom);\n  var tabContentSizeValue = getUnitValue(tabContentSize, tabPositionTopOrBottom);\n  var addSizeValue = getUnitValue(addSize, tabPositionTopOrBottom);\n  var operationSizeValue = getUnitValue(operationSize, tabPositionTopOrBottom);\n  var needScroll = Math.floor(containerExcludeExtraSizeValue) < Math.floor(tabContentSizeValue + addSizeValue);\n  var visibleTabContentValue = needScroll ? containerExcludeExtraSizeValue - operationSizeValue : containerExcludeExtraSizeValue - addSizeValue;\n\n  // ========================== Util =========================\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, tabContentSizeValue - visibleTabContentValue);\n  } else {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  }\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n    if (value > transformMax) {\n      return transformMax;\n    }\n    return value;\n  }\n\n  // ========================= Mobile ========================\n  var touchMovingRef = useRef(null);\n  var _useState9 = useState(),\n    _useState10 = _slicedToArray(_useState9, 2),\n    lockAnimation = _useState10[0],\n    setLockAnimation = _useState10[1];\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n  function clearTouchMoving() {\n    if (touchMovingRef.current) {\n      clearTimeout(touchMovingRef.current);\n    }\n  }\n  useTouchMove(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    }\n\n    // Skip scroll if place is enough\n    if (!needScroll) {\n      return false;\n    }\n    if (tabPositionTopOrBottom) {\n      doMove(setTransformLeft, offsetX);\n    } else {\n      doMove(setTransformTop, offsetY);\n    }\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  useEffect(function () {\n    clearTouchMoving();\n    if (lockAnimation) {\n      touchMovingRef.current = setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n    return clearTouchMoving;\n  }, [lockAnimation]);\n\n  // ===================== Visible Range =====================\n  // Render tab node & collect tab offset\n  var _useVisibleRange = useVisibleRange(tabOffsets,\n    // Container\n    visibleTabContentValue,\n    // Transform\n    tabPositionTopOrBottom ? transformLeft : transformTop,\n    // Tabs\n    tabContentSizeValue,\n    // Add\n    addSizeValue,\n    // Operation\n    operationSizeValue, _objectSpread(_objectSpread({}, props), {}, {\n      tabs: tabs\n    })),\n    _useVisibleRange2 = _slicedToArray(_useVisibleRange, 2),\n    visibleStart = _useVisibleRange2[0],\n    visibleEnd = _useVisibleRange2[1];\n\n  // ========================= Scroll ========================\n  var scrollToTab = useEvent(function () {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft;\n\n      // RTL\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + visibleTabContentValue) {\n          newTransform = tabOffset.right + tabOffset.width - visibleTabContentValue;\n        }\n      }\n      // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + visibleTabContentValue) {\n        newTransform = -(tabOffset.left + tabOffset.width - visibleTabContentValue);\n      }\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + visibleTabContentValue) {\n        _newTransform = -(tabOffset.top + tabOffset.height - visibleTabContentValue);\n      }\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  });\n\n  // ========================= Focus =========================\n  var _useState11 = useState(),\n    _useState12 = _slicedToArray(_useState11, 2),\n    focusKey = _useState12[0],\n    setFocusKey = _useState12[1];\n  var _useState13 = useState(false),\n    _useState14 = _slicedToArray(_useState13, 2),\n    isMouse = _useState14[0],\n    setIsMouse = _useState14[1];\n  var enabledTabs = tabs.filter(function (tab) {\n    return !tab.disabled;\n  }).map(function (tab) {\n    return tab.key;\n  });\n  var onOffset = function onOffset(offset) {\n    var currentIndex = enabledTabs.indexOf(focusKey || activeKey);\n    var len = enabledTabs.length;\n    var nextIndex = (currentIndex + offset + len) % len;\n    var newKey = enabledTabs[nextIndex];\n    setFocusKey(newKey);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    var code = e.code;\n    var isRTL = rtl && tabPositionTopOrBottom;\n    var firstEnabledTab = enabledTabs[0];\n    var lastEnabledTab = enabledTabs[enabledTabs.length - 1];\n    switch (code) {\n      // LEFT\n      case 'ArrowLeft':\n        {\n          if (tabPositionTopOrBottom) {\n            onOffset(isRTL ? 1 : -1);\n          }\n          break;\n        }\n\n      // RIGHT\n      case 'ArrowRight':\n        {\n          if (tabPositionTopOrBottom) {\n            onOffset(isRTL ? -1 : 1);\n          }\n          break;\n        }\n\n      // UP\n      case 'ArrowUp':\n        {\n          e.preventDefault();\n          if (!tabPositionTopOrBottom) {\n            onOffset(-1);\n          }\n          break;\n        }\n\n      // DOWN\n      case 'ArrowDown':\n        {\n          e.preventDefault();\n          if (!tabPositionTopOrBottom) {\n            onOffset(1);\n          }\n          break;\n        }\n\n      // HOME\n      case 'Home':\n        {\n          e.preventDefault();\n          setFocusKey(firstEnabledTab);\n          break;\n        }\n\n      // END\n      case 'End':\n        {\n          e.preventDefault();\n          setFocusKey(lastEnabledTab);\n          break;\n        }\n\n      // Enter & Space\n      case 'Enter':\n      case 'Space':\n        {\n          e.preventDefault();\n          onTabClick(focusKey !== null && focusKey !== void 0 ? focusKey : activeKey, e);\n          break;\n        }\n      // Backspace\n      case 'Backspace':\n      case 'Delete':\n        {\n          var removeIndex = enabledTabs.indexOf(focusKey);\n          var removeTab = tabs.find(function (tab) {\n            return tab.key === focusKey;\n          });\n          var removable = getRemovable(removeTab === null || removeTab === void 0 ? void 0 : removeTab.closable, removeTab === null || removeTab === void 0 ? void 0 : removeTab.closeIcon, editable, removeTab === null || removeTab === void 0 ? void 0 : removeTab.disabled);\n          if (removable) {\n            e.preventDefault();\n            e.stopPropagation();\n            editable.onEdit('remove', {\n              key: focusKey,\n              event: e\n            });\n            // when remove last tab, focus previous tab\n            if (removeIndex === enabledTabs.length - 1) {\n              onOffset(-1);\n            } else {\n              onOffset(1);\n            }\n          }\n          break;\n        }\n    }\n  };\n\n  // ========================== Tab ==========================\n  var tabNodeStyle = {};\n  if (tabPositionTopOrBottom) {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/React.createElement(TabNode, {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */,\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      focus: key === focusKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      tabCount: enabledTabs.length,\n      currentPosition: i + 1,\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onKeyDown: handleKeyDown,\n      onFocus: function onFocus() {\n        if (!isMouse) {\n          setFocusKey(key);\n        }\n        scrollToTab(key);\n        doLockAnimation();\n        if (!tabsWrapperRef.current) {\n          return;\n        }\n        // Focus element will make scrollLeft change which we should reset back\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n        tabsWrapperRef.current.scrollTop = 0;\n      },\n      onBlur: function onBlur() {\n        setFocusKey(undefined);\n      },\n      onMouseDown: function onMouseDown() {\n        setIsMouse(true);\n      },\n      onMouseUp: function onMouseUp() {\n        setIsMouse(false);\n      }\n    });\n  });\n\n  // Update buttons records\n  var updateTabSizes = function updateTabSizes() {\n    return setTabSizes(function () {\n      var _tabListRef$current;\n      var newSizes = new Map();\n      var listRect = (_tabListRef$current = tabListRef.current) === null || _tabListRef$current === void 0 ? void 0 : _tabListRef$current.getBoundingClientRect();\n      tabs.forEach(function (_ref2) {\n        var _tabListRef$current2;\n        var key = _ref2.key;\n        var btnNode = (_tabListRef$current2 = tabListRef.current) === null || _tabListRef$current2 === void 0 ? void 0 : _tabListRef$current2.querySelector(\"[data-node-key=\\\"\".concat(genDataNodeKey(key), \"\\\"]\"));\n        if (btnNode) {\n          var _getTabSize = getTabSize(btnNode, listRect),\n            _getTabSize2 = _slicedToArray(_getTabSize, 4),\n            width = _getTabSize2[0],\n            height = _getTabSize2[1],\n            left = _getTabSize2[2],\n            top = _getTabSize2[3];\n          newSizes.set(key, {\n            width: width,\n            height: height,\n            left: left,\n            top: top\n          });\n        }\n      });\n      return newSizes;\n    });\n  };\n  useEffect(function () {\n    updateTabSizes();\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_')]);\n  var onListHolderResize = useUpdate(function () {\n    // Update wrapper records\n    var containerSize = getSize(containerRef);\n    var extraLeftSize = getSize(extraLeftRef);\n    var extraRightSize = getSize(extraRightRef);\n    setContainerExcludeExtraSize([containerSize[0] - extraLeftSize[0] - extraRightSize[0], containerSize[1] - extraLeftSize[1] - extraRightSize[1]]);\n    var newAddSize = getSize(innerAddButtonRef);\n    setAddSize(newAddSize);\n    var newOperationSize = getSize(operationsRef);\n    setOperationSize(newOperationSize);\n\n    // Which includes add button size\n    var tabContentFullSize = getSize(tabListRef);\n    setTabContentSize([tabContentFullSize[0] - newAddSize[0], tabContentFullSize[1] - newAddSize[1]]);\n\n    // Update buttons records\n    updateTabSizes();\n  });\n\n  // ======================== Dropdown =======================\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat(_toConsumableArray(startHiddenTabs), _toConsumableArray(endHiddenTabs));\n\n  // =================== Link & Operations ===================\n  var activeTabOffset = tabOffsets.get(activeKey);\n  var _useIndicator = useIndicator({\n      activeTabOffset: activeTabOffset,\n      horizontal: tabPositionTopOrBottom,\n      indicator: indicator,\n      rtl: rtl\n    }),\n    indicatorStyle = _useIndicator.style;\n\n  // ========================= Effect ========================\n  useEffect(function () {\n    scrollToTab();\n  }, [activeKey, transformMin, transformMax, stringify(activeTabOffset), stringify(tabOffsets), tabPositionTopOrBottom]);\n\n  // Should recalculate when rtl changed\n  useEffect(function () {\n    onListHolderResize();\n    // eslint-disable-next-line\n  }, [rtl]);\n\n  // ========================= Render ========================\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft !== transformMax;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = transformLeft !== transformMin;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = transformTop !== transformMin;\n  }\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: useComposeRef(ref, containerRef),\n    role: \"tablist\",\n    \"aria-orientation\": tabPositionTopOrBottom ? 'horizontal' : 'vertical',\n    className: classNames(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraLeftRef,\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapPrefix, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/React.createElement(AddButton, {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: _objectSpread(_objectSpread({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-ink-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: indicatorStyle\n  }))))), /*#__PURE__*/React.createElement(OperationNode, _extends({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraRightRef,\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  })));\n  /* eslint-enable */\n});\nexport default TabNavList;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA,8CAA8C,GAC9C;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAI,aAAa,SAAS,WAAW,GAAG,EAAE,aAAa;IACrD,aAAa;IACb,IAAI,cAAc,IAAI,WAAW,EAC/B,eAAe,IAAI,YAAY,EAC/B,YAAY,IAAI,SAAS,EACzB,aAAa,IAAI,UAAU;IAC7B,IAAI,wBAAwB,IAAI,qBAAqB,IACnD,QAAQ,sBAAsB,KAAK,EACnC,SAAS,sBAAsB,MAAM,EACrC,OAAO,sBAAsB,IAAI,EACjC,MAAM,sBAAsB,GAAG;IAEjC,wDAAwD;IACxD,IAAI,KAAK,GAAG,CAAC,QAAQ,eAAe,GAAG;QACrC,OAAO;YAAC;YAAO;YAAQ,OAAO,cAAc,IAAI;YAAE,MAAM,cAAc,GAAG;SAAC;IAC5E;IACA,OAAO;QAAC;QAAa;QAAc;QAAY;KAAU;AAC3D;AACA,IAAI,UAAU,SAAS,QAAQ,MAAM;IACnC,IAAI,OAAO,OAAO,OAAO,IAAI,CAAC,GAC5B,mBAAmB,KAAK,WAAW,EACnC,cAAc,qBAAqB,KAAK,IAAI,IAAI,kBAChD,oBAAoB,KAAK,YAAY,EACrC,eAAe,sBAAsB,KAAK,IAAI,IAAI;IAEpD,wDAAwD;IACxD,IAAI,OAAO,OAAO,EAAE;QAClB,IAAI,wBAAwB,OAAO,OAAO,CAAC,qBAAqB,IAC9D,QAAQ,sBAAsB,KAAK,EACnC,SAAS,sBAAsB,MAAM;QACvC,IAAI,KAAK,GAAG,CAAC,QAAQ,eAAe,GAAG;YACrC,OAAO;gBAAC;gBAAO;aAAO;QACxB;IACF;IACA,OAAO;QAAC;QAAa;KAAa;AACpC;AAEA;;CAEC,GACD,IAAI,eAAe,SAAS,aAAa,IAAI,EAAE,sBAAsB;IACnE,OAAO,IAAI,CAAC,yBAAyB,IAAI,EAAE;AAC7C;AACA,IAAI,aAAa,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IACjE,IAAI,YAAY,MAAM,SAAS,EAC7B,QAAQ,MAAM,KAAK,EACnB,KAAK,MAAM,EAAE,EACb,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,MAAM,MAAM,GAAG,EACf,QAAQ,MAAM,KAAK,EACnB,WAAW,MAAM,QAAQ,EACzB,SAAS,MAAM,MAAM,EACrB,cAAc,MAAM,WAAW,EAC/B,eAAe,MAAM,YAAY,EACjC,WAAW,MAAM,QAAQ,EACzB,aAAa,MAAM,UAAU,EAC7B,cAAc,MAAM,WAAW,EAC/B,YAAY,MAAM,SAAS;IAC7B,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,iJAAA,CAAA,UAAU,GACjD,YAAY,kBAAkB,SAAS,EACvC,OAAO,kBAAkB,IAAI;IAC/B,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,IAAI,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,IAAI,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,IAAI,yBAAyB,gBAAgB,SAAS,gBAAgB;IACtE,IAAI,gBAAgB,CAAA,GAAA,4JAAA,CAAA,UAAY,AAAD,EAAE;kDAAG,SAAU,IAAI,EAAE,IAAI;YACpD,IAAI,0BAA0B,aAAa;gBACzC,YAAY;oBACV,WAAW,OAAO,OAAO,SAAS;gBACpC;YACF;QACF;kDACA,iBAAiB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,eAAe,IAC/C,gBAAgB,cAAc,CAAC,EAAE,EACjC,mBAAmB,cAAc,CAAC,EAAE;IACtC,IAAI,iBAAiB,CAAA,GAAA,4JAAA,CAAA,UAAY,AAAD,EAAE;mDAAG,SAAU,IAAI,EAAE,IAAI;YACrD,IAAI,CAAC,0BAA0B,aAAa;gBAC1C,YAAY;oBACV,WAAW,OAAO,OAAO,QAAQ;gBACnC;YACF;QACF;mDACA,iBAAiB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,gBAAgB,IAChD,eAAe,cAAc,CAAC,EAAE,EAChC,kBAAkB,cAAc,CAAC,EAAE;IACrC,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAG;KAAE,GAC7B,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACvC,4BAA4B,UAAU,CAAC,EAAE,EACzC,+BAA+B,UAAU,CAAC,EAAE;IAC9C,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAG;KAAE,GAC9B,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACxC,iBAAiB,UAAU,CAAC,EAAE,EAC9B,oBAAoB,UAAU,CAAC,EAAE;IACnC,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAG;KAAE,GAC9B,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACxC,UAAU,UAAU,CAAC,EAAE,EACvB,aAAa,UAAU,CAAC,EAAE;IAC5B,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAG;KAAE,GAC9B,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACxC,gBAAgB,UAAU,CAAC,EAAE,EAC7B,mBAAmB,UAAU,CAAC,EAAE;IAClC,IAAI,kBAAkB,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,QACvC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,WAAW,gBAAgB,CAAC,EAAE,EAC9B,cAAc,gBAAgB,CAAC,EAAE;IACnC,IAAI,aAAa,CAAA,GAAA,0JAAA,CAAA,UAAU,AAAD,EAAE,MAAM,UAAU,cAAc,CAAC,EAAE;IAE7D,4DAA4D;IAC5D,IAAI,iCAAiC,aAAa,2BAA2B;IAC7E,IAAI,sBAAsB,aAAa,gBAAgB;IACvD,IAAI,eAAe,aAAa,SAAS;IACzC,IAAI,qBAAqB,aAAa,eAAe;IACrD,IAAI,aAAa,KAAK,KAAK,CAAC,kCAAkC,KAAK,KAAK,CAAC,sBAAsB;IAC/F,IAAI,yBAAyB,aAAa,iCAAiC,qBAAqB,iCAAiC;IAEjI,4DAA4D;IAC5D,IAAI,4BAA4B,GAAG,MAAM,CAAC,WAAW;IACrD,IAAI,eAAe;IACnB,IAAI,eAAe;IACnB,IAAI,CAAC,wBAAwB;QAC3B,eAAe,KAAK,GAAG,CAAC,GAAG,yBAAyB;QACpD,eAAe;IACjB,OAAO,IAAI,KAAK;QACd,eAAe;QACf,eAAe,KAAK,GAAG,CAAC,GAAG,sBAAsB;IACnD,OAAO;QACL,eAAe,KAAK,GAAG,CAAC,GAAG,yBAAyB;QACpD,eAAe;IACjB;IACA,SAAS,aAAa,KAAK;QACzB,IAAI,QAAQ,cAAc;YACxB,OAAO;QACT;QACA,IAAI,QAAQ,cAAc;YACxB,OAAO;QACT;QACA,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAI,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,KACtB,cAAc,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACzC,gBAAgB,WAAW,CAAC,EAAE,EAC9B,mBAAmB,WAAW,CAAC,EAAE;IACnC,SAAS;QACP,iBAAiB,KAAK,GAAG;IAC3B;IACA,SAAS;QACP,IAAI,eAAe,OAAO,EAAE;YAC1B,aAAa,eAAe,OAAO;QACrC;IACF;IACA,CAAA,GAAA,4JAAA,CAAA,UAAY,AAAD,EAAE;mCAAgB,SAAU,OAAO,EAAE,OAAO;YACrD,SAAS,OAAO,QAAQ,EAAE,MAAM;gBAC9B;sDAAS,SAAU,KAAK;wBACtB,IAAI,WAAW,aAAa,QAAQ;wBACpC,OAAO;oBACT;;YACF;YAEA,iCAAiC;YACjC,IAAI,CAAC,YAAY;gBACf,OAAO;YACT;YACA,IAAI,wBAAwB;gBAC1B,OAAO,kBAAkB;YAC3B,OAAO;gBACL,OAAO,iBAAiB;YAC1B;YACA;YACA;YACA,OAAO;QACT;;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;YACA,IAAI,eAAe;gBACjB,eAAe,OAAO,GAAG;4CAAW;wBAClC,iBAAiB;oBACnB;2CAAG;YACL;YACA,OAAO;QACT;+BAAG;QAAC;KAAc;IAElB,4DAA4D;IAC5D,uCAAuC;IACvC,IAAI,mBAAmB,CAAA,GAAA,+JAAA,CAAA,UAAe,AAAD,EAAE,YACrC,YAAY;IACZ,wBACA,YAAY;IACZ,yBAAyB,gBAAgB,cACzC,OAAO;IACP,qBACA,MAAM;IACN,cACA,YAAY;IACZ,oBAAoB,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,QAAQ,CAAC,GAAG;QAC9D,MAAM;IACR,KACA,oBAAoB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACrD,eAAe,iBAAiB,CAAC,EAAE,EACnC,aAAa,iBAAiB,CAAC,EAAE;IAEnC,4DAA4D;IAC5D,IAAI,cAAc,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD;4CAAE;YACzB,IAAI,MAAM,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;YAC9E,IAAI,YAAY,WAAW,GAAG,CAAC,QAAQ;gBACrC,OAAO;gBACP,QAAQ;gBACR,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;YACA,IAAI,wBAAwB;gBAC1B,oDAAoD;gBACpD,IAAI,eAAe;gBAEnB,MAAM;gBACN,IAAI,KAAK;oBACP,IAAI,UAAU,KAAK,GAAG,eAAe;wBACnC,eAAe,UAAU,KAAK;oBAChC,OAAO,IAAI,UAAU,KAAK,GAAG,UAAU,KAAK,GAAG,gBAAgB,wBAAwB;wBACrF,eAAe,UAAU,KAAK,GAAG,UAAU,KAAK,GAAG;oBACrD;gBACF,OAEK,IAAI,UAAU,IAAI,GAAG,CAAC,eAAe;oBACxC,eAAe,CAAC,UAAU,IAAI;gBAChC,OAAO,IAAI,UAAU,IAAI,GAAG,UAAU,KAAK,GAAG,CAAC,gBAAgB,wBAAwB;oBACrF,eAAe,CAAC,CAAC,UAAU,IAAI,GAAG,UAAU,KAAK,GAAG,sBAAsB;gBAC5E;gBACA,gBAAgB;gBAChB,iBAAiB,aAAa;YAChC,OAAO;gBACL,oDAAoD;gBACpD,IAAI,gBAAgB;gBACpB,IAAI,UAAU,GAAG,GAAG,CAAC,cAAc;oBACjC,gBAAgB,CAAC,UAAU,GAAG;gBAChC,OAAO,IAAI,UAAU,GAAG,GAAG,UAAU,MAAM,GAAG,CAAC,eAAe,wBAAwB;oBACpF,gBAAgB,CAAC,CAAC,UAAU,GAAG,GAAG,UAAU,MAAM,GAAG,sBAAsB;gBAC7E;gBACA,iBAAiB;gBACjB,gBAAgB,aAAa;YAC/B;QACF;;IAEA,4DAA4D;IAC5D,IAAI,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,KACvB,cAAc,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,aAAa,IAC1C,WAAW,WAAW,CAAC,EAAE,EACzB,cAAc,WAAW,CAAC,EAAE;IAC9B,IAAI,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QACzB,cAAc,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,aAAa,IAC1C,UAAU,WAAW,CAAC,EAAE,EACxB,aAAa,WAAW,CAAC,EAAE;IAC7B,IAAI,cAAc,KAAK,MAAM,CAAC,SAAU,GAAG;QACzC,OAAO,CAAC,IAAI,QAAQ;IACtB,GAAG,GAAG,CAAC,SAAU,GAAG;QAClB,OAAO,IAAI,GAAG;IAChB;IACA,IAAI,WAAW,SAAS,SAAS,MAAM;QACrC,IAAI,eAAe,YAAY,OAAO,CAAC,YAAY;QACnD,IAAI,MAAM,YAAY,MAAM;QAC5B,IAAI,YAAY,CAAC,eAAe,SAAS,GAAG,IAAI;QAChD,IAAI,SAAS,WAAW,CAAC,UAAU;QACnC,YAAY;IACd;IACA,IAAI,gBAAgB,SAAS,cAAc,CAAC;QAC1C,IAAI,OAAO,EAAE,IAAI;QACjB,IAAI,QAAQ,OAAO;QACnB,IAAI,kBAAkB,WAAW,CAAC,EAAE;QACpC,IAAI,iBAAiB,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;QACxD,OAAQ;YACN,OAAO;YACP,KAAK;gBACH;oBACE,IAAI,wBAAwB;wBAC1B,SAAS,QAAQ,IAAI,CAAC;oBACxB;oBACA;gBACF;YAEF,QAAQ;YACR,KAAK;gBACH;oBACE,IAAI,wBAAwB;wBAC1B,SAAS,QAAQ,CAAC,IAAI;oBACxB;oBACA;gBACF;YAEF,KAAK;YACL,KAAK;gBACH;oBACE,EAAE,cAAc;oBAChB,IAAI,CAAC,wBAAwB;wBAC3B,SAAS,CAAC;oBACZ;oBACA;gBACF;YAEF,OAAO;YACP,KAAK;gBACH;oBACE,EAAE,cAAc;oBAChB,IAAI,CAAC,wBAAwB;wBAC3B,SAAS;oBACX;oBACA;gBACF;YAEF,OAAO;YACP,KAAK;gBACH;oBACE,EAAE,cAAc;oBAChB,YAAY;oBACZ;gBACF;YAEF,MAAM;YACN,KAAK;gBACH;oBACE,EAAE,cAAc;oBAChB,YAAY;oBACZ;gBACF;YAEF,gBAAgB;YAChB,KAAK;YACL,KAAK;gBACH;oBACE,EAAE,cAAc;oBAChB,WAAW,aAAa,QAAQ,aAAa,KAAK,IAAI,WAAW,WAAW;oBAC5E;gBACF;YACF,YAAY;YACZ,KAAK;YACL,KAAK;gBACH;oBACE,IAAI,cAAc,YAAY,OAAO,CAAC;oBACtC,IAAI,YAAY,KAAK,IAAI,CAAC,SAAU,GAAG;wBACrC,OAAO,IAAI,GAAG,KAAK;oBACrB;oBACA,IAAI,YAAY,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,QAAQ,EAAE,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,SAAS,EAAE,UAAU,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,QAAQ;oBACpQ,IAAI,WAAW;wBACb,EAAE,cAAc;wBAChB,EAAE,eAAe;wBACjB,SAAS,MAAM,CAAC,UAAU;4BACxB,KAAK;4BACL,OAAO;wBACT;wBACA,2CAA2C;wBAC3C,IAAI,gBAAgB,YAAY,MAAM,GAAG,GAAG;4BAC1C,SAAS,CAAC;wBACZ,OAAO;4BACL,SAAS;wBACX;oBACF;oBACA;gBACF;QACJ;IACF;IAEA,4DAA4D;IAC5D,IAAI,eAAe,CAAC;IACpB,IAAI,wBAAwB;QAC1B,YAAY,CAAC,MAAM,gBAAgB,aAAa,GAAG;IACrD,OAAO;QACL,aAAa,SAAS,GAAG;IAC3B;IACA,IAAI,WAAW,KAAK,GAAG,CAAC,SAAU,GAAG,EAAE,CAAC;QACtC,IAAI,MAAM,IAAI,GAAG;QACjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,UAAO,EAAE;YAC/C,IAAI;YACJ,WAAW;YACX,KAAK;YACL,KAAK;YAEL,OAAO,MAAM,IAAI,YAAY;YAC7B,UAAU,IAAI,QAAQ;YACtB,UAAU;YACV,QAAQ,QAAQ;YAChB,OAAO,QAAQ;YACf,eAAe;YACf,iBAAiB,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,eAAe;YACvF,UAAU,YAAY,MAAM;YAC5B,iBAAiB,IAAI;YACrB,SAAS,SAAS,QAAQ,CAAC;gBACzB,WAAW,KAAK;YAClB;YACA,WAAW;YACX,SAAS,SAAS;gBAChB,IAAI,CAAC,SAAS;oBACZ,YAAY;gBACd;gBACA,YAAY;gBACZ;gBACA,IAAI,CAAC,eAAe,OAAO,EAAE;oBAC3B;gBACF;gBACA,uEAAuE;gBACvE,IAAI,CAAC,KAAK;oBACR,eAAe,OAAO,CAAC,UAAU,GAAG;gBACtC;gBACA,eAAe,OAAO,CAAC,SAAS,GAAG;YACrC;YACA,QAAQ,SAAS;gBACf,YAAY;YACd;YACA,aAAa,SAAS;gBACpB,WAAW;YACb;YACA,WAAW,SAAS;gBAClB,WAAW;YACb;QACF;IACF;IAEA,yBAAyB;IACzB,IAAI,iBAAiB,SAAS;QAC5B,OAAO,YAAY;YACjB,IAAI;YACJ,IAAI,WAAW,IAAI;YACnB,IAAI,WAAW,CAAC,sBAAsB,WAAW,OAAO,MAAM,QAAQ,wBAAwB,KAAK,IAAI,KAAK,IAAI,oBAAoB,qBAAqB;YACzJ,KAAK,OAAO,CAAC,SAAU,KAAK;gBAC1B,IAAI;gBACJ,IAAI,MAAM,MAAM,GAAG;gBACnB,IAAI,UAAU,CAAC,uBAAuB,WAAW,OAAO,MAAM,QAAQ,yBAAyB,KAAK,IAAI,KAAK,IAAI,qBAAqB,aAAa,CAAC,oBAAoB,MAAM,CAAC,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;gBACpM,IAAI,SAAS;oBACX,IAAI,cAAc,WAAW,SAAS,WACpC,eAAe,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,aAAa,IAC3C,QAAQ,YAAY,CAAC,EAAE,EACvB,SAAS,YAAY,CAAC,EAAE,EACxB,OAAO,YAAY,CAAC,EAAE,EACtB,MAAM,YAAY,CAAC,EAAE;oBACvB,SAAS,GAAG,CAAC,KAAK;wBAChB,OAAO;wBACP,QAAQ;wBACR,MAAM;wBACN,KAAK;oBACP;gBACF;YACF;YACA,OAAO;QACT;IACF;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC,KAAK,GAAG;oCAAC,SAAU,GAAG;gBACxB,OAAO,IAAI,GAAG;YAChB;mCAAG,IAAI,CAAC;KAAK;IACb,IAAI,qBAAqB,CAAA,GAAA,yJAAA,CAAA,UAAS,AAAD;oDAAE;YACjC,yBAAyB;YACzB,IAAI,gBAAgB,QAAQ;YAC5B,IAAI,gBAAgB,QAAQ;YAC5B,IAAI,iBAAiB,QAAQ;YAC7B,6BAA6B;gBAAC,aAAa,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE;gBAAE,aAAa,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE;aAAC;YAC/I,IAAI,aAAa,QAAQ;YACzB,WAAW;YACX,IAAI,mBAAmB,QAAQ;YAC/B,iBAAiB;YAEjB,iCAAiC;YACjC,IAAI,qBAAqB,QAAQ;YACjC,kBAAkB;gBAAC,kBAAkB,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;gBAAE,kBAAkB,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;aAAC;YAEhG,yBAAyB;YACzB;QACF;;IAEA,4DAA4D;IAC5D,IAAI,kBAAkB,KAAK,KAAK,CAAC,GAAG;IACpC,IAAI,gBAAgB,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,aAAa,EAAE,CAAC,MAAM,CAAC,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,kBAAkB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;IAEnF,4DAA4D;IAC5D,IAAI,kBAAkB,WAAW,GAAG,CAAC;IACrC,IAAI,gBAAgB,CAAA,GAAA,4JAAA,CAAA,UAAY,AAAD,EAAE;QAC7B,iBAAiB;QACjB,YAAY;QACZ,WAAW;QACX,KAAK;IACP,IACA,iBAAiB,cAAc,KAAK;IAEtC,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC;QAAW;QAAc;QAAc,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE;QAAkB,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE;QAAa;KAAuB;IAErH,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACA,2BAA2B;QAC7B;+BAAG;QAAC;KAAI;IAER,4DAA4D;IAC5D,IAAI,cAAc,CAAC,CAAC,WAAW,MAAM;IACrC,IAAI,aAAa,GAAG,MAAM,CAAC,WAAW;IACtC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,wBAAwB;QAC1B,IAAI,KAAK;YACP,YAAY,gBAAgB;YAC5B,WAAW,kBAAkB;QAC/B,OAAO;YACL,WAAW,gBAAgB;YAC3B,YAAY,kBAAkB;QAChC;IACF,OAAO;QACL,UAAU,eAAe;QACzB,aAAa,iBAAiB;IAChC;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,0KAAA,CAAA,UAAc,EAAE;QACtD,UAAU;IACZ,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACzC,KAAK,CAAA,GAAA,0IAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;QACxB,MAAM;QACN,oBAAoB,yBAAyB,eAAe;QAC5D,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,MAAM,CAAC,WAAW,SAAS;QACpD,OAAO;QACP,WAAW,SAAS;YAClB,sCAAsC;YACtC;QACF;IACF,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,iKAAA,CAAA,UAAY,EAAE;QAChD,KAAK;QACL,UAAU;QACV,OAAO;QACP,WAAW;IACb,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,0KAAA,CAAA,UAAc,EAAE;QACnD,UAAU;IACZ,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACzC,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,YAAY,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,YAAY,eAAe,WAAW,GAAG,MAAM,CAAC,YAAY,gBAAgB,YAAY,GAAG,MAAM,CAAC,YAAY,cAAc,UAAU,GAAG,MAAM,CAAC,YAAY,iBAAiB;QAC7R,KAAK;IACP,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,0KAAA,CAAA,UAAc,EAAE;QAClD,UAAU;IACZ,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACzC,KAAK;QACL,WAAW,GAAG,MAAM,CAAC,WAAW;QAChC,OAAO;YACL,WAAW,aAAa,MAAM,CAAC,eAAe,QAAQ,MAAM,CAAC,cAAc;YAC3E,YAAY,gBAAgB,SAAS;QACvC;IACF,GAAG,UAAU,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,8JAAA,CAAA,UAAS,EAAE;QACvD,KAAK;QACL,WAAW;QACX,QAAQ;QACR,UAAU;QACV,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,SAAS,MAAM,KAAK,IAAI,YAAY,eAAe,CAAC,GAAG;YAC5F,YAAY,cAAc,WAAW;QACvC;IACF,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAC1C,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,MAAM,CAAC,WAAW,aAAa,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,sBAAsB,SAAS,MAAM;QACtI,OAAO;IACT,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,kKAAA,CAAA,UAAa,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QAC1E,iBAAiB,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,eAAe;QACvF,KAAK;QACL,WAAW;QACX,MAAM;QACN,WAAW,CAAC,eAAe;QAC3B,WAAW,CAAC,CAAC;IACf,KAAK,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,iKAAA,CAAA,UAAY,EAAE;QAClD,KAAK;QACL,UAAU;QACV,OAAO;QACP,WAAW;IACb;AACA,iBAAiB,GACnB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1476, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/TabPanelList/TabPane.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nvar TabPane = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    id = props.id,\n    active = props.active,\n    tabKey = props.tabKey,\n    children = props.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: style,\n    className: classNames(prefixCls, active && \"\".concat(prefixCls, \"-active\"), className),\n    ref: ref\n  }, children);\n});\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'TabPane';\n}\nexport default TabPane;"], "names": [], "mappings": ";;;AAqBI;AArBJ;AACA;;;AACA,IAAI,UAAU,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IAC9D,IAAI,YAAY,MAAM,SAAS,EAC7B,YAAY,MAAM,SAAS,EAC3B,QAAQ,MAAM,KAAK,EACnB,KAAK,MAAM,EAAE,EACb,SAAS,MAAM,MAAM,EACrB,SAAS,MAAM,MAAM,EACrB,WAAW,MAAM,QAAQ;IAC3B,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAC7C,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC;QAC1C,MAAM;QACN,UAAU,SAAS,IAAI,CAAC;QACxB,mBAAmB,MAAM,GAAG,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC;QACvD,eAAe,CAAC;QAChB,OAAO;QACP,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,WAAW,UAAU,GAAG,MAAM,CAAC,WAAW,YAAY;QAC5E,KAAK;IACP,GAAG;AACL;AACA,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1505, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/TabNavList/Wrapper.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"renderTabBar\"],\n  _excluded2 = [\"label\", \"key\"];\n// zombieJ: To compatible with `renderTabBar` usage.\n\nimport * as React from 'react';\nimport TabNavList from '.';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"../TabPanelList/TabPane\";\n// We have to create a TabNavList components.\nvar TabNavListWrapper = function TabNavListWrapper(_ref) {\n  var renderTabBar = _ref.renderTabBar,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _React$useContext = React.useContext(TabContext),\n    tabs = _React$useContext.tabs;\n  if (renderTabBar) {\n    var tabNavBarProps = _objectSpread(_objectSpread({}, restProps), {}, {\n      // Legacy support. We do not use this actually\n      panes: tabs.map(function (_ref2) {\n        var label = _ref2.label,\n          key = _ref2.key,\n          restTabProps = _objectWithoutProperties(_ref2, _excluded2);\n        return /*#__PURE__*/React.createElement(TabPane, _extends({\n          tab: label,\n          key: key,\n          tabKey: key\n        }, restTabProps));\n      })\n    });\n    return renderTabBar(tabNavBarProps, TabNavList);\n  }\n  return /*#__PURE__*/React.createElement(TabNavList, restProps);\n};\nif (process.env.NODE_ENV !== 'production') {\n  TabNavListWrapper.displayName = 'TabNavListWrapper';\n}\nexport default TabNavListWrapper;"], "names": [], "mappings": ";;;AAmCI;AAnCJ;AACA;AACA;AAGA,oDAAoD;AAEpD;AACA;AACA;AACA;;;;AAPA,IAAI,YAAY;IAAC;CAAe,EAC9B,aAAa;IAAC;IAAS;CAAM;;;;;AAO/B,6CAA6C;AAC7C,IAAI,oBAAoB,SAAS,kBAAkB,IAAI;IACrD,IAAI,eAAe,KAAK,YAAY,EAClC,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,MAAM;IAC7C,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,iJAAA,CAAA,UAAU,GACjD,OAAO,kBAAkB,IAAI;IAC/B,IAAI,cAAc;QAChB,IAAI,iBAAiB,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,YAAY,CAAC,GAAG;YACnE,8CAA8C;YAC9C,OAAO,KAAK,GAAG,CAAC,SAAU,KAAK;gBAC7B,IAAI,QAAQ,MAAM,KAAK,EACrB,MAAM,MAAM,GAAG,EACf,eAAe,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,OAAO;gBACjD,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,8JAAA,CAAA,UAAO,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;oBACxD,KAAK;oBACL,KAAK;oBACL,QAAQ;gBACV,GAAG;YACL;QACF;QACA,OAAO,aAAa,gBAAgB,0JAAA,CAAA,UAAU;IAChD;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,0JAAA,CAAA,UAAU,EAAE;AACtD;AACA,wCAA2C;IACzC,kBAAkB,WAAW,GAAG;AAClC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/TabPanelList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"key\", \"forceRender\", \"style\", \"className\", \"destroyInactiveTabPane\"];\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"./TabPane\";\nvar TabPanelList = function TabPanelList(props) {\n  var id = props.id,\n    activeKey = props.activeKey,\n    animated = props.animated,\n    tabPosition = props.tabPosition,\n    destroyInactiveTabPane = props.destroyInactiveTabPane;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var tabPaneAnimated = animated.tabPane;\n  var tabPanePrefixCls = \"\".concat(prefixCls, \"-tabpane\");\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), _defineProperty({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated))\n  }, tabs.map(function (item) {\n    var key = item.key,\n      forceRender = item.forceRender,\n      paneStyle = item.style,\n      paneClassName = item.className,\n      itemDestroyInactiveTabPane = item.destroyInactiveTabPane,\n      restTabProps = _objectWithoutProperties(item, _excluded);\n    var active = key === activeKey;\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      key: key,\n      visible: active,\n      forceRender: forceRender,\n      removeOnLeave: !!(destroyInactiveTabPane || itemDestroyInactiveTabPane),\n      leavedClassName: \"\".concat(tabPanePrefixCls, \"-hidden\")\n    }, animated.tabPaneMotion), function (_ref, ref) {\n      var motionStyle = _ref.style,\n        motionClassName = _ref.className;\n      return /*#__PURE__*/React.createElement(TabPane, _extends({}, restTabProps, {\n        prefixCls: tabPanePrefixCls,\n        id: id,\n        tabKey: key,\n        animated: tabPaneAnimated,\n        active: active,\n        style: _objectSpread(_objectSpread({}, paneStyle), motionStyle),\n        className: classNames(paneClassName, motionClassName),\n        ref: ref\n      }));\n    });\n  })));\n};\nexport default TabPanelList;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;;;;;AALA,IAAI,YAAY;IAAC;IAAO;IAAe;IAAS;IAAa;CAAyB;;;;;;AAMtF,IAAI,eAAe,SAAS,aAAa,KAAK;IAC5C,IAAI,KAAK,MAAM,EAAE,EACf,YAAY,MAAM,SAAS,EAC3B,WAAW,MAAM,QAAQ,EACzB,cAAc,MAAM,WAAW,EAC/B,yBAAyB,MAAM,sBAAsB;IACvD,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,iJAAA,CAAA,UAAU,GACjD,YAAY,kBAAkB,SAAS,EACvC,OAAO,kBAAkB,IAAI;IAC/B,IAAI,kBAAkB,SAAS,OAAO;IACtC,IAAI,mBAAmB,GAAG,MAAM,CAAC,WAAW;IAC5C,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAC7C,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,MAAM,CAAC,WAAW;IAC7C,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACzC,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,MAAM,CAAC,WAAW,aAAa,GAAG,MAAM,CAAC,WAAW,aAAa,MAAM,CAAC,cAAc,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,sBAAsB;IAChL,GAAG,KAAK,GAAG,CAAC,SAAU,IAAI;QACxB,IAAI,MAAM,KAAK,GAAG,EAChB,cAAc,KAAK,WAAW,EAC9B,YAAY,KAAK,KAAK,EACtB,gBAAgB,KAAK,SAAS,EAC9B,6BAA6B,KAAK,sBAAsB,EACxD,eAAe,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,MAAM;QAChD,IAAI,SAAS,QAAQ;QACrB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,8JAAA,CAAA,UAAS,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;YAC1D,KAAK;YACL,SAAS;YACT,aAAa;YACb,eAAe,CAAC,CAAC,CAAC,0BAA0B,0BAA0B;YACtE,iBAAiB,GAAG,MAAM,CAAC,kBAAkB;QAC/C,GAAG,SAAS,aAAa,GAAG,SAAU,IAAI,EAAE,GAAG;YAC7C,IAAI,cAAc,KAAK,KAAK,EAC1B,kBAAkB,KAAK,SAAS;YAClC,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,8JAAA,CAAA,UAAO,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,cAAc;gBAC1E,WAAW;gBACX,IAAI;gBACJ,QAAQ;gBACR,UAAU;gBACV,QAAQ;gBACR,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,YAAY;gBACnD,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,eAAe;gBACrC,KAAK;YACP;QACF;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1625, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/hooks/useAnimateConfig.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nexport default function useAnimateConfig() {\n  var animated = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    inkBar: true,\n    tabPane: false\n  };\n  var mergedAnimated;\n  if (animated === false) {\n    mergedAnimated = {\n      inkBar: false,\n      tabPane: false\n    };\n  } else if (animated === true) {\n    mergedAnimated = {\n      inkBar: true,\n      tabPane: false\n    };\n  } else {\n    mergedAnimated = _objectSpread({\n      inkBar: true\n    }, _typeof(animated) === 'object' ? animated : {});\n  }\n\n  // Enable tabPane animation if provide motion\n  if (mergedAnimated.tabPaneMotion && mergedAnimated.tabPane === undefined) {\n    mergedAnimated.tabPane = true;\n  }\n  if (!mergedAnimated.tabPaneMotion && mergedAnimated.tabPane) {\n    if (process.env.NODE_ENV !== 'production') {\n      warning(false, '`animated.tabPane` is true but `animated.tabPaneMotion` is not provided. Motion will not work.');\n    }\n    mergedAnimated.tabPane = false;\n  }\n  return mergedAnimated;\n}"], "names": [], "mappings": ";;;AA8BQ;AA9BR;AACA;AACA;;;;AACe,SAAS;IACtB,IAAI,WAAW,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QACjF,QAAQ;QACR,SAAS;IACX;IACA,IAAI;IACJ,IAAI,aAAa,OAAO;QACtB,iBAAiB;YACf,QAAQ;YACR,SAAS;QACX;IACF,OAAO,IAAI,aAAa,MAAM;QAC5B,iBAAiB;YACf,QAAQ;YACR,SAAS;QACX;IACF,OAAO;QACL,iBAAiB,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE;YAC7B,QAAQ;QACV,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,WAAW,WAAW,CAAC;IAClD;IAEA,6CAA6C;IAC7C,IAAI,eAAe,aAAa,IAAI,eAAe,OAAO,KAAK,WAAW;QACxE,eAAe,OAAO,GAAG;IAC3B;IACA,IAAI,CAAC,eAAe,aAAa,IAAI,eAAe,OAAO,EAAE;QAC3D,wCAA2C;YACzC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QACjB;QACA,eAAe,OAAO,GAAG;IAC3B;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1672, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/Tabs.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"items\", \"direction\", \"activeKey\", \"defaultActiveKey\", \"editable\", \"animated\", \"tabPosition\", \"tabBarGutter\", \"tabBarStyle\", \"tabBarExtraContent\", \"locale\", \"more\", \"destroyInactiveTabPane\", \"renderTabBar\", \"onChange\", \"onTabClick\", \"onTabScroll\", \"getPopupContainer\", \"popupClassName\", \"indicator\"];\n// Accessibility https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/Tab_Role\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport * as React from 'react';\nimport { useEffect, useState } from 'react';\nimport TabContext from \"./TabContext\";\nimport TabNavListWrapper from \"./TabNavList/Wrapper\";\nimport TabPanelList from \"./TabPanelList\";\nimport useAnimateConfig from \"./hooks/useAnimateConfig\";\n/**\n * Should added antd:\n * - type\n *\n * Removed:\n * - onNextClick\n * - onPrevClick\n * - keyboard\n */\n\n// Used for accessibility\nvar uuid = 0;\nvar Tabs = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tabs' : _props$prefixCls,\n    className = props.className,\n    items = props.items,\n    direction = props.direction,\n    activeKey = props.activeKey,\n    defaultActiveKey = props.defaultActiveKey,\n    editable = props.editable,\n    animated = props.animated,\n    _props$tabPosition = props.tabPosition,\n    tabPosition = _props$tabPosition === void 0 ? 'top' : _props$tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    tabBarStyle = props.tabBarStyle,\n    tabBarExtraContent = props.tabBarExtraContent,\n    locale = props.locale,\n    more = props.more,\n    destroyInactiveTabPane = props.destroyInactiveTabPane,\n    renderTabBar = props.renderTabBar,\n    onChange = props.onChange,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName,\n    indicator = props.indicator,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var tabs = React.useMemo(function () {\n    return (items || []).filter(function (item) {\n      return item && _typeof(item) === 'object' && 'key' in item;\n    });\n  }, [items]);\n  var rtl = direction === 'rtl';\n  var mergedAnimated = useAnimateConfig(animated);\n\n  // ======================== Mobile ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    mobile = _useState2[0],\n    setMobile = _useState2[1];\n  useEffect(function () {\n    // Only update on the client side\n    setMobile(isMobile());\n  }, []);\n\n  // ====================== Active Key ======================\n  var _useMergedState = useMergedState(function () {\n      var _tabs$;\n      return (_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key;\n    }, {\n      value: activeKey,\n      defaultValue: defaultActiveKey\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedActiveKey = _useMergedState2[0],\n    setMergedActiveKey = _useMergedState2[1];\n  var _useState3 = useState(function () {\n      return tabs.findIndex(function (tab) {\n        return tab.key === mergedActiveKey;\n      });\n    }),\n    _useState4 = _slicedToArray(_useState3, 2),\n    activeIndex = _useState4[0],\n    setActiveIndex = _useState4[1];\n\n  // Reset active key if not exist anymore\n  useEffect(function () {\n    var newActiveIndex = tabs.findIndex(function (tab) {\n      return tab.key === mergedActiveKey;\n    });\n    if (newActiveIndex === -1) {\n      var _tabs$newActiveIndex;\n      newActiveIndex = Math.max(0, Math.min(activeIndex, tabs.length - 1));\n      setMergedActiveKey((_tabs$newActiveIndex = tabs[newActiveIndex]) === null || _tabs$newActiveIndex === void 0 ? void 0 : _tabs$newActiveIndex.key);\n    }\n    setActiveIndex(newActiveIndex);\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), mergedActiveKey, activeIndex]);\n\n  // ===================== Accessibility ====================\n  var _useMergedState3 = useMergedState(null, {\n      value: id\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedId = _useMergedState4[0],\n    setMergedId = _useMergedState4[1];\n\n  // Async generate id to avoid ssr mapping failed\n  useEffect(function () {\n    if (!id) {\n      setMergedId(\"rc-tabs-\".concat(process.env.NODE_ENV === 'test' ? 'test' : uuid));\n      uuid += 1;\n    }\n  }, []);\n\n  // ======================== Events ========================\n  function onInternalTabClick(key, e) {\n    onTabClick === null || onTabClick === void 0 || onTabClick(key, e);\n    var isActiveChanged = key !== mergedActiveKey;\n    setMergedActiveKey(key);\n    if (isActiveChanged) {\n      onChange === null || onChange === void 0 || onChange(key);\n    }\n  }\n\n  // ======================== Render ========================\n  var sharedProps = {\n    id: mergedId,\n    activeKey: mergedActiveKey,\n    animated: mergedAnimated,\n    tabPosition: tabPosition,\n    rtl: rtl,\n    mobile: mobile\n  };\n  var tabNavBarProps = _objectSpread(_objectSpread({}, sharedProps), {}, {\n    editable: editable,\n    locale: locale,\n    more: more,\n    tabBarGutter: tabBarGutter,\n    onTabClick: onInternalTabClick,\n    onTabScroll: onTabScroll,\n    extra: tabBarExtraContent,\n    style: tabBarStyle,\n    panes: null,\n    getPopupContainer: getPopupContainer,\n    popupClassName: popupClassName,\n    indicator: indicator\n  });\n  return /*#__PURE__*/React.createElement(TabContext.Provider, {\n    value: {\n      tabs: tabs,\n      prefixCls: prefixCls\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    id: id,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(tabPosition), _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-mobile\"), mobile), \"\".concat(prefixCls, \"-editable\"), editable), \"\".concat(prefixCls, \"-rtl\"), rtl), className)\n  }, restProps), /*#__PURE__*/React.createElement(TabNavListWrapper, _extends({}, tabNavBarProps, {\n    renderTabBar: renderTabBar\n  })), /*#__PURE__*/React.createElement(TabPanelList, _extends({\n    destroyInactiveTabPane: destroyInactiveTabPane\n  }, sharedProps, {\n    animated: mergedAnimated\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Tabs.displayName = 'Tabs';\n}\nexport default Tabs;"], "names": [], "mappings": ";;;AAwHoC;AAxHpC;AACA;AACA;AACA;AACA;AACA;AAEA,+FAA+F;AAC/F;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;AAVA,IAAI,YAAY;IAAC;IAAM;IAAa;IAAa;IAAS;IAAa;IAAa;IAAoB;IAAY;IAAY;IAAe;IAAgB;IAAe;IAAsB;IAAU;IAAQ;IAA0B;IAAgB;IAAY;IAAc;IAAe;IAAqB;IAAkB;CAAY;;;;;;;;;;AAW5V;;;;;;;;CAQC,GAED,yBAAyB;AACzB,IAAI,OAAO;AACX,IAAI,OAAO,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IAC3D,IAAI,KAAK,MAAM,EAAE,EACf,mBAAmB,MAAM,SAAS,EAClC,YAAY,qBAAqB,KAAK,IAAI,YAAY,kBACtD,YAAY,MAAM,SAAS,EAC3B,QAAQ,MAAM,KAAK,EACnB,YAAY,MAAM,SAAS,EAC3B,YAAY,MAAM,SAAS,EAC3B,mBAAmB,MAAM,gBAAgB,EACzC,WAAW,MAAM,QAAQ,EACzB,WAAW,MAAM,QAAQ,EACzB,qBAAqB,MAAM,WAAW,EACtC,cAAc,uBAAuB,KAAK,IAAI,QAAQ,oBACtD,eAAe,MAAM,YAAY,EACjC,cAAc,MAAM,WAAW,EAC/B,qBAAqB,MAAM,kBAAkB,EAC7C,SAAS,MAAM,MAAM,EACrB,OAAO,MAAM,IAAI,EACjB,yBAAyB,MAAM,sBAAsB,EACrD,eAAe,MAAM,YAAY,EACjC,WAAW,MAAM,QAAQ,EACzB,aAAa,MAAM,UAAU,EAC7B,cAAc,MAAM,WAAW,EAC/B,oBAAoB,MAAM,iBAAiB,EAC3C,iBAAiB,MAAM,cAAc,EACrC,YAAY,MAAM,SAAS,EAC3B,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,OAAO;IAC9C,IAAI,OAAO,6JAAA,CAAA,UAAa;8BAAC;YACvB,OAAO,CAAC,SAAS,EAAE,EAAE,MAAM;sCAAC,SAAU,IAAI;oBACxC,OAAO,QAAQ,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,YAAY,SAAS;gBACxD;;QACF;6BAAG;QAAC;KAAM;IACV,IAAI,MAAM,cAAc;IACxB,IAAI,iBAAiB,CAAA,GAAA,gKAAA,CAAA,UAAgB,AAAD,EAAE;IAEtC,2DAA2D;IAC3D,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QACvB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACvC,SAAS,UAAU,CAAC,EAAE,EACtB,YAAY,UAAU,CAAC,EAAE;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,iCAAiC;YACjC,UAAU,CAAA,GAAA,+IAAA,CAAA,UAAQ,AAAD;QACnB;yBAAG,EAAE;IAEL,2DAA2D;IAC3D,IAAI,kBAAkB,CAAA,GAAA,8JAAA,CAAA,UAAc,AAAD;gDAAE;YACjC,IAAI;YACJ,OAAO,CAAC,SAAS,IAAI,CAAC,EAAE,MAAM,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,GAAG;QAC/E;+CAAG;QACD,OAAO;QACP,cAAc;IAChB,IACA,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,kBAAkB,gBAAgB,CAAC,EAAE,EACrC,qBAAqB,gBAAgB,CAAC,EAAE;IAC1C,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;qCAAE;YACtB,OAAO,KAAK,SAAS;6CAAC,SAAU,GAAG;oBACjC,OAAO,IAAI,GAAG,KAAK;gBACrB;;QACF;qCACA,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACxC,cAAc,UAAU,CAAC,EAAE,EAC3B,iBAAiB,UAAU,CAAC,EAAE;IAEhC,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,iBAAiB,KAAK,SAAS;iDAAC,SAAU,GAAG;oBAC/C,OAAO,IAAI,GAAG,KAAK;gBACrB;;YACA,IAAI,mBAAmB,CAAC,GAAG;gBACzB,IAAI;gBACJ,iBAAiB,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,MAAM,GAAG;gBACjE,mBAAmB,CAAC,uBAAuB,IAAI,CAAC,eAAe,MAAM,QAAQ,yBAAyB,KAAK,IAAI,KAAK,IAAI,qBAAqB,GAAG;YAClJ;YACA,eAAe;QACjB;yBAAG;QAAC,KAAK,GAAG;8BAAC,SAAU,GAAG;gBACxB,OAAO,IAAI,GAAG;YAChB;6BAAG,IAAI,CAAC;QAAM;QAAiB;KAAY;IAE3C,2DAA2D;IAC3D,IAAI,mBAAmB,CAAA,GAAA,8JAAA,CAAA,UAAc,AAAD,EAAE,MAAM;QACxC,OAAO;IACT,IACA,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,WAAW,gBAAgB,CAAC,EAAE,EAC9B,cAAc,gBAAgB,CAAC,EAAE;IAEnC,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,IAAI;gBACP,YAAY,WAAW,MAAM,CAAC,sCAAkC,0BAAS;gBACzE,QAAQ;YACV;QACF;yBAAG,EAAE;IAEL,2DAA2D;IAC3D,SAAS,mBAAmB,GAAG,EAAE,CAAC;QAChC,eAAe,QAAQ,eAAe,KAAK,KAAK,WAAW,KAAK;QAChE,IAAI,kBAAkB,QAAQ;QAC9B,mBAAmB;QACnB,IAAI,iBAAiB;YACnB,aAAa,QAAQ,aAAa,KAAK,KAAK,SAAS;QACvD;IACF;IAEA,2DAA2D;IAC3D,IAAI,cAAc;QAChB,IAAI;QACJ,WAAW;QACX,UAAU;QACV,aAAa;QACb,KAAK;QACL,QAAQ;IACV;IACA,IAAI,iBAAiB,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,cAAc,CAAC,GAAG;QACrE,UAAU;QACV,QAAQ;QACR,MAAM;QACN,cAAc;QACd,YAAY;QACZ,aAAa;QACb,OAAO;QACP,OAAO;QACP,OAAO;QACP,mBAAmB;QACnB,gBAAgB;QAChB,WAAW;IACb;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,iJAAA,CAAA,UAAU,CAAC,QAAQ,EAAE;QAC3D,OAAO;YACL,MAAM;YACN,WAAW;QACb;IACF,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAClD,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,WAAW,GAAG,MAAM,CAAC,WAAW,KAAK,MAAM,CAAC,cAAc,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,YAAY,SAAS,GAAG,MAAM,CAAC,WAAW,cAAc,WAAW,GAAG,MAAM,CAAC,WAAW,SAAS,MAAM;IAClQ,GAAG,YAAY,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,UAAiB,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,gBAAgB;QAC9F,cAAc;IAChB,KAAK,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,UAAY,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC3D,wBAAwB;IAC1B,GAAG,aAAa;QACd,UAAU;IACZ;AACF;AACA,wCAA2C;IACzC,KAAK,WAAW,GAAG;AACrB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1877, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-tabs/es/index.js"], "sourcesContent": ["import Tabs from \"./Tabs\";\nexport default Tabs;"], "names": [], "mappings": ";;;AAAA;;uCACe,2IAAA,CAAA,UAAI", "ignoreList": [0], "debugId": null}}]}
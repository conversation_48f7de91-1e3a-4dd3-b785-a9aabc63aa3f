{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/pdf/PDFDocument.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { Document, Page, pdfjs } from 'react-pdf';\nimport { Spin } from 'antd';\n\n// Set up PDF.js worker\nif (typeof window !== 'undefined') {\n  pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;\n}\n\ninterface PDFDocumentProps {\n  file: string;\n  pageNumber: number;\n  onLoadSuccess: (data: { numPages: number }) => void;\n  onLoadError: (error: Error) => void;\n}\n\nconst PDFDocument: React.FC<PDFDocumentProps> = ({\n  file,\n  pageNumber,\n  onLoadSuccess,\n  onLoadError,\n}) => {\n  return (\n    <Document\n      file={file}\n      onLoadSuccess={onLoadSuccess}\n      onLoadError={onLoadError}\n      loading={<Spin size=\"large\" />}\n    >\n      <Page\n        pageNumber={pageNumber}\n        renderTextLayer={true}\n        renderAnnotationLayer={true}\n        className=\"pdf-page mx-auto\"\n      />\n    </Document>\n  );\n};\n\nexport default PDFDocument;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAJA;;;;AAMA,uBAAuB;AACvB,wCAAmC;IACjC,kNAAA,CAAA,QAAK,CAAC,mBAAmB,CAAC,SAAS,GAAG,AAAC,2CAAwD,OAAd,kNAAA,CAAA,QAAK,CAAC,OAAO,EAAC;AACjG;AASA,MAAM,cAA0C;QAAC,EAC/C,IAAI,EACJ,UAAU,EACV,aAAa,EACb,WAAW,EACZ;IACC,qBACE,6LAAC,0LAAA,CAAA,WAAQ;QACP,MAAM;QACN,eAAe;QACf,aAAa;QACb,uBAAS,6LAAC,iLAAA,CAAA,OAAI;YAAC,MAAK;;;;;;kBAEpB,cAAA,6LAAC,kLAAA,CAAA,OAAI;YACH,YAAY;YACZ,iBAAiB;YACjB,uBAAuB;YACvB,WAAU;;;;;;;;;;;AAIlB;KArBM;uCAuBS", "debugId": null}}]}
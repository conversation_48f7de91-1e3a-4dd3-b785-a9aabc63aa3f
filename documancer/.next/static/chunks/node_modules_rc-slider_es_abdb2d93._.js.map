{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/util.js"], "sourcesContent": ["export function getOffset(value, min, max) {\n  return (value - min) / (max - min);\n}\nexport function getDirectionStyle(direction, value, min, max) {\n  var offset = getOffset(value, min, max);\n  var positionStyle = {};\n  switch (direction) {\n    case 'rtl':\n      positionStyle.right = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateX(50%)';\n      break;\n    case 'btt':\n      positionStyle.bottom = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateY(50%)';\n      break;\n    case 'ttb':\n      positionStyle.top = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateY(-50%)';\n      break;\n    default:\n      positionStyle.left = \"\".concat(offset * 100, \"%\");\n      positionStyle.transform = 'translateX(-50%)';\n      break;\n  }\n  return positionStyle;\n}\n\n/** Return index value if is list or return value directly */\nexport function getIndex(value, index) {\n  return Array.isArray(value) ? value[index] : value;\n}"], "names": [], "mappings": ";;;;;AAAO,SAAS,UAAU,KAAK,EAAE,GAAG,EAAE,GAAG;IACvC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG;AACnC;AACO,SAAS,kBAAkB,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;IAC1D,IAAI,SAAS,UAAU,OAAO,KAAK;IACnC,IAAI,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YACH,cAAc,KAAK,GAAG,GAAG,MAAM,CAAC,SAAS,KAAK;YAC9C,cAAc,SAAS,GAAG;YAC1B;QACF,KAAK;YACH,cAAc,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,KAAK;YAC/C,cAAc,SAAS,GAAG;YAC1B;QACF,KAAK;YACH,cAAc,GAAG,GAAG,GAAG,MAAM,CAAC,SAAS,KAAK;YAC5C,cAAc,SAAS,GAAG;YAC1B;QACF;YACE,cAAc,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,KAAK;YAC7C,cAAc,SAAS,GAAG;YAC1B;IACJ;IACA,OAAO;AACT;AAGO,SAAS,SAAS,KAAK,EAAE,KAAK;IACnC,OAAO,MAAM,OAAO,CAAC,SAAS,KAAK,CAAC,MAAM,GAAG;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/context.js"], "sourcesContent": ["import * as React from 'react';\nvar SliderContext = /*#__PURE__*/React.createContext({\n  min: 0,\n  max: 0,\n  direction: 'ltr',\n  step: 1,\n  includedStart: 0,\n  includedEnd: 0,\n  tabIndex: 0,\n  keyboard: true,\n  styles: {},\n  classNames: {}\n});\nexport default SliderContext;\n/** @private NOT PROMISE AVAILABLE. DO NOT USE IN PRODUCTION. */\nexport var UnstableContext = /*#__PURE__*/React.createContext({});"], "names": [], "mappings": ";;;;AAAA;;AACA,IAAI,gBAAgB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC;IACnD,KAAK;IACL,KAAK;IACL,WAAW;IACX,MAAM;IACN,eAAe;IACf,aAAa;IACb,UAAU;IACV,UAAU;IACV,QAAQ,CAAC;IACT,YAAY,CAAC;AACf;uCACe;AAER,IAAI,kBAAkB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/Handles/Handle.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"value\", \"valueIndex\", \"onStartMove\", \"onDelete\", \"style\", \"render\", \"dragging\", \"draggingDelete\", \"onOffsetChange\", \"onChangeComplete\", \"onFocus\", \"onMouseEnter\"];\nimport cls from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getDirectionStyle, getIndex } from \"../util\";\nvar Handle = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    valueIndex = props.valueIndex,\n    onStartMove = props.onStartMove,\n    onDelete = props.onDelete,\n    style = props.style,\n    render = props.render,\n    dragging = props.dragging,\n    draggingDelete = props.draggingDelete,\n    onOffsetChange = props.onOffsetChange,\n    onChangeComplete = props.onChangeComplete,\n    onFocus = props.onFocus,\n    onMouseEnter = props.onMouseEnter,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    disabled = _React$useContext.disabled,\n    keyboard = _React$useContext.keyboard,\n    range = _React$useContext.range,\n    tabIndex = _React$useContext.tabIndex,\n    ariaLabelForHandle = _React$useContext.ariaLabelForHandle,\n    ariaLabelledByForHandle = _React$useContext.ariaLabelledByForHandle,\n    ariaRequired = _React$useContext.ariaRequired,\n    ariaValueTextFormatterForHandle = _React$useContext.ariaValueTextFormatterForHandle,\n    styles = _React$useContext.styles,\n    classNames = _React$useContext.classNames;\n  var handlePrefixCls = \"\".concat(prefixCls, \"-handle\");\n\n  // ============================ Events ============================\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled) {\n      onStartMove(e, valueIndex);\n    }\n  };\n  var onInternalFocus = function onInternalFocus(e) {\n    onFocus === null || onFocus === void 0 || onFocus(e, valueIndex);\n  };\n  var onInternalMouseEnter = function onInternalMouseEnter(e) {\n    onMouseEnter(e, valueIndex);\n  };\n\n  // =========================== Keyboard ===========================\n  var onKeyDown = function onKeyDown(e) {\n    if (!disabled && keyboard) {\n      var offset = null;\n\n      // Change the value\n      switch (e.which || e.keyCode) {\n        case KeyCode.LEFT:\n          offset = direction === 'ltr' || direction === 'btt' ? -1 : 1;\n          break;\n        case KeyCode.RIGHT:\n          offset = direction === 'ltr' || direction === 'btt' ? 1 : -1;\n          break;\n\n        // Up is plus\n        case KeyCode.UP:\n          offset = direction !== 'ttb' ? 1 : -1;\n          break;\n\n        // Down is minus\n        case KeyCode.DOWN:\n          offset = direction !== 'ttb' ? -1 : 1;\n          break;\n        case KeyCode.HOME:\n          offset = 'min';\n          break;\n        case KeyCode.END:\n          offset = 'max';\n          break;\n        case KeyCode.PAGE_UP:\n          offset = 2;\n          break;\n        case KeyCode.PAGE_DOWN:\n          offset = -2;\n          break;\n        case KeyCode.BACKSPACE:\n        case KeyCode.DELETE:\n          onDelete(valueIndex);\n          break;\n      }\n      if (offset !== null) {\n        e.preventDefault();\n        onOffsetChange(offset, valueIndex);\n      }\n    }\n  };\n  var handleKeyUp = function handleKeyUp(e) {\n    switch (e.which || e.keyCode) {\n      case KeyCode.LEFT:\n      case KeyCode.RIGHT:\n      case KeyCode.UP:\n      case KeyCode.DOWN:\n      case KeyCode.HOME:\n      case KeyCode.END:\n      case KeyCode.PAGE_UP:\n      case KeyCode.PAGE_DOWN:\n        onChangeComplete === null || onChangeComplete === void 0 || onChangeComplete();\n        break;\n    }\n  };\n\n  // ============================ Offset ============================\n  var positionStyle = getDirectionStyle(direction, value, min, max);\n\n  // ============================ Render ============================\n  var divProps = {};\n  if (valueIndex !== null) {\n    var _getIndex;\n    divProps = {\n      tabIndex: disabled ? null : getIndex(tabIndex, valueIndex),\n      role: 'slider',\n      'aria-valuemin': min,\n      'aria-valuemax': max,\n      'aria-valuenow': value,\n      'aria-disabled': disabled,\n      'aria-label': getIndex(ariaLabelForHandle, valueIndex),\n      'aria-labelledby': getIndex(ariaLabelledByForHandle, valueIndex),\n      'aria-required': getIndex(ariaRequired, valueIndex),\n      'aria-valuetext': (_getIndex = getIndex(ariaValueTextFormatterForHandle, valueIndex)) === null || _getIndex === void 0 ? void 0 : _getIndex(value),\n      'aria-orientation': direction === 'ltr' || direction === 'rtl' ? 'horizontal' : 'vertical',\n      onMouseDown: onInternalStartMove,\n      onTouchStart: onInternalStartMove,\n      onFocus: onInternalFocus,\n      onMouseEnter: onInternalMouseEnter,\n      onKeyDown: onKeyDown,\n      onKeyUp: handleKeyUp\n    };\n  }\n  var handleNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    className: cls(handlePrefixCls, _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(handlePrefixCls, \"-\").concat(valueIndex + 1), valueIndex !== null && range), \"\".concat(handlePrefixCls, \"-dragging\"), dragging), \"\".concat(handlePrefixCls, \"-dragging-delete\"), draggingDelete), classNames.handle),\n    style: _objectSpread(_objectSpread(_objectSpread({}, positionStyle), style), styles.handle)\n  }, divProps, restProps));\n\n  // Customize\n  if (render) {\n    handleNode = render(handleNode, {\n      index: valueIndex,\n      prefixCls: prefixCls,\n      value: value,\n      dragging: dragging,\n      draggingDelete: draggingDelete\n    });\n  }\n  return handleNode;\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handle.displayName = 'Handle';\n}\nexport default Handle;"], "names": [], "mappings": ";;;AAgKI;AAhKJ;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;AALA,IAAI,YAAY;IAAC;IAAa;IAAS;IAAc;IAAe;IAAY;IAAS;IAAU;IAAY;IAAkB;IAAkB;IAAoB;IAAW;CAAe;;;;;;AAMjM,IAAI,SAAS,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IAC7D,IAAI,YAAY,MAAM,SAAS,EAC7B,QAAQ,MAAM,KAAK,EACnB,aAAa,MAAM,UAAU,EAC7B,cAAc,MAAM,WAAW,EAC/B,WAAW,MAAM,QAAQ,EACzB,QAAQ,MAAM,KAAK,EACnB,SAAS,MAAM,MAAM,EACrB,WAAW,MAAM,QAAQ,EACzB,iBAAiB,MAAM,cAAc,EACrC,iBAAiB,MAAM,cAAc,EACrC,mBAAmB,MAAM,gBAAgB,EACzC,UAAU,MAAM,OAAO,EACvB,eAAe,MAAM,YAAY,EACjC,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,OAAO;IAC9C,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,gJAAA,CAAA,UAAa,GACpD,MAAM,kBAAkB,GAAG,EAC3B,MAAM,kBAAkB,GAAG,EAC3B,YAAY,kBAAkB,SAAS,EACvC,WAAW,kBAAkB,QAAQ,EACrC,WAAW,kBAAkB,QAAQ,EACrC,QAAQ,kBAAkB,KAAK,EAC/B,WAAW,kBAAkB,QAAQ,EACrC,qBAAqB,kBAAkB,kBAAkB,EACzD,0BAA0B,kBAAkB,uBAAuB,EACnE,eAAe,kBAAkB,YAAY,EAC7C,kCAAkC,kBAAkB,+BAA+B,EACnF,SAAS,kBAAkB,MAAM,EACjC,aAAa,kBAAkB,UAAU;IAC3C,IAAI,kBAAkB,GAAG,MAAM,CAAC,WAAW;IAE3C,mEAAmE;IACnE,IAAI,sBAAsB,SAAS,oBAAoB,CAAC;QACtD,IAAI,CAAC,UAAU;YACb,YAAY,GAAG;QACjB;IACF;IACA,IAAI,kBAAkB,SAAS,gBAAgB,CAAC;QAC9C,YAAY,QAAQ,YAAY,KAAK,KAAK,QAAQ,GAAG;IACvD;IACA,IAAI,uBAAuB,SAAS,qBAAqB,CAAC;QACxD,aAAa,GAAG;IAClB;IAEA,mEAAmE;IACnE,IAAI,YAAY,SAAS,UAAU,CAAC;QAClC,IAAI,CAAC,YAAY,UAAU;YACzB,IAAI,SAAS;YAEb,mBAAmB;YACnB,OAAQ,EAAE,KAAK,IAAI,EAAE,OAAO;gBAC1B,KAAK,8IAAA,CAAA,UAAO,CAAC,IAAI;oBACf,SAAS,cAAc,SAAS,cAAc,QAAQ,CAAC,IAAI;oBAC3D;gBACF,KAAK,8IAAA,CAAA,UAAO,CAAC,KAAK;oBAChB,SAAS,cAAc,SAAS,cAAc,QAAQ,IAAI,CAAC;oBAC3D;gBAEF,aAAa;gBACb,KAAK,8IAAA,CAAA,UAAO,CAAC,EAAE;oBACb,SAAS,cAAc,QAAQ,IAAI,CAAC;oBACpC;gBAEF,gBAAgB;gBAChB,KAAK,8IAAA,CAAA,UAAO,CAAC,IAAI;oBACf,SAAS,cAAc,QAAQ,CAAC,IAAI;oBACpC;gBACF,KAAK,8IAAA,CAAA,UAAO,CAAC,IAAI;oBACf,SAAS;oBACT;gBACF,KAAK,8IAAA,CAAA,UAAO,CAAC,GAAG;oBACd,SAAS;oBACT;gBACF,KAAK,8IAAA,CAAA,UAAO,CAAC,OAAO;oBAClB,SAAS;oBACT;gBACF,KAAK,8IAAA,CAAA,UAAO,CAAC,SAAS;oBACpB,SAAS,CAAC;oBACV;gBACF,KAAK,8IAAA,CAAA,UAAO,CAAC,SAAS;gBACtB,KAAK,8IAAA,CAAA,UAAO,CAAC,MAAM;oBACjB,SAAS;oBACT;YACJ;YACA,IAAI,WAAW,MAAM;gBACnB,EAAE,cAAc;gBAChB,eAAe,QAAQ;YACzB;QACF;IACF;IACA,IAAI,cAAc,SAAS,YAAY,CAAC;QACtC,OAAQ,EAAE,KAAK,IAAI,EAAE,OAAO;YAC1B,KAAK,8IAAA,CAAA,UAAO,CAAC,IAAI;YACjB,KAAK,8IAAA,CAAA,UAAO,CAAC,KAAK;YAClB,KAAK,8IAAA,CAAA,UAAO,CAAC,EAAE;YACf,KAAK,8IAAA,CAAA,UAAO,CAAC,IAAI;YACjB,KAAK,8IAAA,CAAA,UAAO,CAAC,IAAI;YACjB,KAAK,8IAAA,CAAA,UAAO,CAAC,GAAG;YAChB,KAAK,8IAAA,CAAA,UAAO,CAAC,OAAO;YACpB,KAAK,8IAAA,CAAA,UAAO,CAAC,SAAS;gBACpB,qBAAqB,QAAQ,qBAAqB,KAAK,KAAK;gBAC5D;QACJ;IACF;IAEA,mEAAmE;IACnE,IAAI,gBAAgB,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,OAAO,KAAK;IAE7D,mEAAmE;IACnE,IAAI,WAAW,CAAC;IAChB,IAAI,eAAe,MAAM;QACvB,IAAI;QACJ,WAAW;YACT,UAAU,WAAW,OAAO,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;YAC/C,MAAM;YACN,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;YACjB,cAAc,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EAAE,oBAAoB;YAC3C,mBAAmB,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EAAE,yBAAyB;YACrD,iBAAiB,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;YACxC,kBAAkB,CAAC,YAAY,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EAAE,iCAAiC,WAAW,MAAM,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU;YAC5I,oBAAoB,cAAc,SAAS,cAAc,QAAQ,eAAe;YAChF,aAAa;YACb,cAAc;YACd,SAAS;YACT,cAAc;YACd,WAAW;YACX,SAAS;QACX;IACF;IACA,IAAI,aAAa,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAChE,KAAK;QACL,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAG,AAAD,EAAE,iBAAiB,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,iBAAiB,KAAK,MAAM,CAAC,aAAa,IAAI,eAAe,QAAQ,QAAQ,GAAG,MAAM,CAAC,iBAAiB,cAAc,WAAW,GAAG,MAAM,CAAC,iBAAiB,qBAAqB,iBAAiB,WAAW,MAAM;QACjT,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,gBAAgB,QAAQ,OAAO,MAAM;IAC5F,GAAG,UAAU;IAEb,YAAY;IACZ,IAAI,QAAQ;QACV,aAAa,OAAO,YAAY;YAC9B,OAAO;YACP,WAAW;YACX,OAAO;YACP,UAAU;YACV,gBAAgB;QAClB;IACF;IACA,OAAO;AACT;AACA,wCAA2C;IACzC,OAAO,WAAW,GAAG;AACvB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/Handles/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"style\", \"onStartMove\", \"onOffsetChange\", \"values\", \"handleRender\", \"activeHandleRender\", \"draggingIndex\", \"draggingDelete\", \"onFocus\"];\nimport * as React from 'react';\nimport { flushSync } from 'react-dom';\nimport { getIndex } from \"../util\";\nimport Handle from \"./Handle\";\nvar Handles = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    onStartMove = props.onStartMove,\n    onOffsetChange = props.onOffsetChange,\n    values = props.values,\n    handleRender = props.handleRender,\n    activeHandleRender = props.activeHandleRender,\n    draggingIndex = props.draggingIndex,\n    draggingDelete = props.draggingDelete,\n    onFocus = props.onFocus,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var handlesRef = React.useRef({});\n\n  // =========================== Active ===========================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeVisible = _React$useState2[0],\n    setActiveVisible = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    activeIndex = _React$useState4[0],\n    setActiveIndex = _React$useState4[1];\n  var onActive = function onActive(index) {\n    setActiveIndex(index);\n    setActiveVisible(true);\n  };\n  var onHandleFocus = function onHandleFocus(e, index) {\n    onActive(index);\n    onFocus === null || onFocus === void 0 || onFocus(e);\n  };\n  var onHandleMouseEnter = function onHandleMouseEnter(e, index) {\n    onActive(index);\n  };\n\n  // =========================== Render ===========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus(index) {\n        var _handlesRef$current$i;\n        (_handlesRef$current$i = handlesRef.current[index]) === null || _handlesRef$current$i === void 0 || _handlesRef$current$i.focus();\n      },\n      hideHelp: function hideHelp() {\n        flushSync(function () {\n          setActiveVisible(false);\n        });\n      }\n    };\n  });\n\n  // =========================== Render ===========================\n  // Handle Props\n  var handleProps = _objectSpread({\n    prefixCls: prefixCls,\n    onStartMove: onStartMove,\n    onOffsetChange: onOffsetChange,\n    render: handleRender,\n    onFocus: onHandleFocus,\n    onMouseEnter: onHandleMouseEnter\n  }, restProps);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, values.map(function (value, index) {\n    var dragging = draggingIndex === index;\n    return /*#__PURE__*/React.createElement(Handle, _extends({\n      ref: function ref(node) {\n        if (!node) {\n          delete handlesRef.current[index];\n        } else {\n          handlesRef.current[index] = node;\n        }\n      },\n      dragging: dragging,\n      draggingDelete: dragging && draggingDelete,\n      style: getIndex(style, index),\n      key: index,\n      value: value,\n      valueIndex: index\n    }, handleProps));\n  }), activeHandleRender && activeVisible && /*#__PURE__*/React.createElement(Handle, _extends({\n    key: \"a11y\"\n  }, handleProps, {\n    value: values[activeIndex],\n    valueIndex: null,\n    dragging: draggingIndex !== -1,\n    draggingDelete: draggingDelete,\n    render: activeHandleRender,\n    style: {\n      pointerEvents: 'none'\n    },\n    tabIndex: null,\n    \"aria-hidden\": true\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handles.displayName = 'Handles';\n}\nexport default Handles;"], "names": [], "mappings": ";;;AAqGI;AArGJ;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;AAJA,IAAI,YAAY;IAAC;IAAa;IAAS;IAAe;IAAkB;IAAU;IAAgB;IAAsB;IAAiB;IAAkB;CAAU;;;;;AAKrK,IAAI,UAAU,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IAC9D,IAAI,YAAY,MAAM,SAAS,EAC7B,QAAQ,MAAM,KAAK,EACnB,cAAc,MAAM,WAAW,EAC/B,iBAAiB,MAAM,cAAc,EACrC,SAAS,MAAM,MAAM,EACrB,eAAe,MAAM,YAAY,EACjC,qBAAqB,MAAM,kBAAkB,EAC7C,gBAAgB,MAAM,aAAa,EACnC,iBAAiB,MAAM,cAAc,EACrC,UAAU,MAAM,OAAO,EACvB,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,OAAO;IAC9C,IAAI,aAAa,6JAAA,CAAA,SAAY,CAAC,CAAC;IAE/B,iEAAiE;IACjE,IAAI,kBAAkB,6JAAA,CAAA,WAAc,CAAC,QACnC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,gBAAgB,gBAAgB,CAAC,EAAE,EACnC,mBAAmB,gBAAgB,CAAC,EAAE;IACxC,IAAI,mBAAmB,6JAAA,CAAA,WAAc,CAAC,CAAC,IACrC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,cAAc,gBAAgB,CAAC,EAAE,EACjC,iBAAiB,gBAAgB,CAAC,EAAE;IACtC,IAAI,WAAW,SAAS,SAAS,KAAK;QACpC,eAAe;QACf,iBAAiB;IACnB;IACA,IAAI,gBAAgB,SAAS,cAAc,CAAC,EAAE,KAAK;QACjD,SAAS;QACT,YAAY,QAAQ,YAAY,KAAK,KAAK,QAAQ;IACpD;IACA,IAAI,qBAAqB,SAAS,mBAAmB,CAAC,EAAE,KAAK;QAC3D,SAAS;IACX;IAEA,iEAAiE;IACjE,6JAAA,CAAA,sBAAyB,CAAC;uCAAK;YAC7B,OAAO;gBACL,OAAO,SAAS,MAAM,KAAK;oBACzB,IAAI;oBACJ,CAAC,wBAAwB,WAAW,OAAO,CAAC,MAAM,MAAM,QAAQ,0BAA0B,KAAK,KAAK,sBAAsB,KAAK;gBACjI;gBACA,UAAU,SAAS;oBACjB,CAAA,GAAA,oKAAA,CAAA,YAAS,AAAD;gEAAE;4BACR,iBAAiB;wBACnB;;gBACF;YACF;QACF;;IAEA,iEAAiE;IACjE,eAAe;IACf,IAAI,cAAc,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE;QAC9B,WAAW;QACX,aAAa;QACb,gBAAgB;QAChB,QAAQ;QACR,SAAS;QACT,cAAc;IAChB,GAAG;IACH,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MAAM,OAAO,GAAG,CAAC,SAAU,KAAK,EAAE,KAAK;QAC7F,IAAI,WAAW,kBAAkB;QACjC,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,0JAAA,CAAA,UAAM,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;YACvD,KAAK,SAAS,IAAI,IAAI;gBACpB,IAAI,CAAC,MAAM;oBACT,OAAO,WAAW,OAAO,CAAC,MAAM;gBAClC,OAAO;oBACL,WAAW,OAAO,CAAC,MAAM,GAAG;gBAC9B;YACF;YACA,UAAU;YACV,gBAAgB,YAAY;YAC5B,OAAO,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;YACvB,KAAK;YACL,OAAO;YACP,YAAY;QACd,GAAG;IACL,IAAI,sBAAsB,iBAAiB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,0JAAA,CAAA,UAAM,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC3F,KAAK;IACP,GAAG,aAAa;QACd,OAAO,MAAM,CAAC,YAAY;QAC1B,YAAY;QACZ,UAAU,kBAAkB,CAAC;QAC7B,gBAAgB;QAChB,QAAQ;QACR,OAAO;YACL,eAAe;QACjB;QACA,UAAU;QACV,eAAe;IACjB;AACF;AACA,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/Marks/Mark.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getDirectionStyle } from \"../util\";\nvar Mark = function Mark(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    children = props.children,\n    value = props.value,\n    _onClick = props.onClick;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    includedStart = _React$useContext.includedStart,\n    includedEnd = _React$useContext.includedEnd,\n    included = _React$useContext.included;\n  var textCls = \"\".concat(prefixCls, \"-text\");\n\n  // ============================ Offset ============================\n  var positionStyle = getDirectionStyle(direction, value, min, max);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(textCls, _defineProperty({}, \"\".concat(textCls, \"-active\"), included && includedStart <= value && value <= includedEnd)),\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: function onMouseDown(e) {\n      e.stopPropagation();\n    },\n    onClick: function onClick() {\n      _onClick(value);\n    }\n  }, children);\n};\nexport default Mark;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,OAAO,SAAS,KAAK,KAAK;IAC5B,IAAI,YAAY,MAAM,SAAS,EAC7B,QAAQ,MAAM,KAAK,EACnB,WAAW,MAAM,QAAQ,EACzB,QAAQ,MAAM,KAAK,EACnB,WAAW,MAAM,OAAO;IAC1B,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,gJAAA,CAAA,UAAa,GACpD,MAAM,kBAAkB,GAAG,EAC3B,MAAM,kBAAkB,GAAG,EAC3B,YAAY,kBAAkB,SAAS,EACvC,gBAAgB,kBAAkB,aAAa,EAC/C,cAAc,kBAAkB,WAAW,EAC3C,WAAW,kBAAkB,QAAQ;IACvC,IAAI,UAAU,GAAG,MAAM,CAAC,WAAW;IAEnC,mEAAmE;IACnE,IAAI,gBAAgB,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,OAAO,KAAK;IAC7D,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC9C,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,SAAS,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,YAAY,YAAY,iBAAiB,SAAS,SAAS;QACjI,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,gBAAgB;QACvD,aAAa,SAAS,YAAY,CAAC;YACjC,EAAE,eAAe;QACnB;QACA,SAAS,SAAS;YAChB,SAAS;QACX;IACF,GAAG;AACL;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/Marks/index.js"], "sourcesContent": ["import * as React from 'react';\nimport Mark from \"./Mark\";\nvar Marks = function Marks(props) {\n  var prefixCls = props.prefixCls,\n    marks = props.marks,\n    onClick = props.onClick;\n  var markPrefixCls = \"\".concat(prefixCls, \"-mark\");\n\n  // Not render mark if empty\n  if (!marks.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: markPrefixCls\n  }, marks.map(function (_ref) {\n    var value = _ref.value,\n      style = _ref.style,\n      label = _ref.label;\n    return /*#__PURE__*/React.createElement(Mark, {\n      key: value,\n      prefixCls: markPrefixCls,\n      style: style,\n      value: value,\n      onClick: onClick\n    }, label);\n  }));\n};\nexport default Marks;"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,IAAI,QAAQ,SAAS,MAAM,KAAK;IAC9B,IAAI,YAAY,MAAM,SAAS,EAC7B,QAAQ,MAAM,KAAK,EACnB,UAAU,MAAM,OAAO;IACzB,IAAI,gBAAgB,GAAG,MAAM,CAAC,WAAW;IAEzC,2BAA2B;IAC3B,IAAI,CAAC,MAAM,MAAM,EAAE;QACjB,OAAO;IACT;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAC7C,WAAW;IACb,GAAG,MAAM,GAAG,CAAC,SAAU,IAAI;QACzB,IAAI,QAAQ,KAAK,KAAK,EACpB,QAAQ,KAAK,KAAK,EAClB,QAAQ,KAAK,KAAK;QACpB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,sJAAA,CAAA,UAAI,EAAE;YAC5C,KAAK;YACL,WAAW;YACX,OAAO;YACP,OAAO;YACP,SAAS;QACX,GAAG;IACL;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/Steps/Dot.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getDirectionStyle } from \"../util\";\nvar Dot = function Dot(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    style = props.style,\n    activeStyle = props.activeStyle;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    included = _React$useContext.included,\n    includedStart = _React$useContext.includedStart,\n    includedEnd = _React$useContext.includedEnd;\n  var dotClassName = \"\".concat(prefixCls, \"-dot\");\n  var active = included && includedStart <= value && value <= includedEnd;\n\n  // ============================ Offset ============================\n  var mergedStyle = _objectSpread(_objectSpread({}, getDirectionStyle(direction, value, min, max)), typeof style === 'function' ? style(value) : style);\n  if (active) {\n    mergedStyle = _objectSpread(_objectSpread({}, mergedStyle), typeof activeStyle === 'function' ? activeStyle(value) : activeStyle);\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(dotClassName, _defineProperty({}, \"\".concat(dotClassName, \"-active\"), active)),\n    style: mergedStyle\n  });\n};\nexport default Dot;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,MAAM,SAAS,IAAI,KAAK;IAC1B,IAAI,YAAY,MAAM,SAAS,EAC7B,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,KAAK,EACnB,cAAc,MAAM,WAAW;IACjC,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,gJAAA,CAAA,UAAa,GACpD,MAAM,kBAAkB,GAAG,EAC3B,MAAM,kBAAkB,GAAG,EAC3B,YAAY,kBAAkB,SAAS,EACvC,WAAW,kBAAkB,QAAQ,EACrC,gBAAgB,kBAAkB,aAAa,EAC/C,cAAc,kBAAkB,WAAW;IAC7C,IAAI,eAAe,GAAG,MAAM,CAAC,WAAW;IACxC,IAAI,SAAS,YAAY,iBAAiB,SAAS,SAAS;IAE5D,mEAAmE;IACnE,IAAI,cAAc,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,OAAO,KAAK,OAAO,OAAO,UAAU,aAAa,MAAM,SAAS;IAC/I,IAAI,QAAQ;QACV,cAAc,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,cAAc,OAAO,gBAAgB,aAAa,YAAY,SAAS;IACvH;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC9C,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,cAAc,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,cAAc,YAAY;QAC5F,OAAO;IACT;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/Steps/index.js"], "sourcesContent": ["import * as React from 'react';\nimport SliderContext from \"../context\";\nimport Dot from \"./Dot\";\nvar Steps = function Steps(props) {\n  var prefixCls = props.prefixCls,\n    marks = props.marks,\n    dots = props.dots,\n    style = props.style,\n    activeStyle = props.activeStyle;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    step = _React$useContext.step;\n  var stepDots = React.useMemo(function () {\n    var dotSet = new Set();\n\n    // Add marks\n    marks.forEach(function (mark) {\n      dotSet.add(mark.value);\n    });\n\n    // Fill dots\n    if (dots && step !== null) {\n      var current = min;\n      while (current <= max) {\n        dotSet.add(current);\n        current += step;\n      }\n    }\n    return Array.from(dotSet);\n  }, [min, max, step, dots, marks]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-step\")\n  }, stepDots.map(function (dotValue) {\n    return /*#__PURE__*/React.createElement(Dot, {\n      prefixCls: prefixCls,\n      key: dotValue,\n      value: dotValue,\n      style: style,\n      activeStyle: activeStyle\n    });\n  }));\n};\nexport default Steps;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,IAAI,QAAQ,SAAS,MAAM,KAAK;IAC9B,IAAI,YAAY,MAAM,SAAS,EAC7B,QAAQ,MAAM,KAAK,EACnB,OAAO,MAAM,IAAI,EACjB,QAAQ,MAAM,KAAK,EACnB,cAAc,MAAM,WAAW;IACjC,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,gJAAA,CAAA,UAAa,GACpD,MAAM,kBAAkB,GAAG,EAC3B,MAAM,kBAAkB,GAAG,EAC3B,OAAO,kBAAkB,IAAI;IAC/B,IAAI,WAAW,6JAAA,CAAA,UAAa;mCAAC;YAC3B,IAAI,SAAS,IAAI;YAEjB,YAAY;YACZ,MAAM,OAAO;2CAAC,SAAU,IAAI;oBAC1B,OAAO,GAAG,CAAC,KAAK,KAAK;gBACvB;;YAEA,YAAY;YACZ,IAAI,QAAQ,SAAS,MAAM;gBACzB,IAAI,UAAU;gBACd,MAAO,WAAW,IAAK;oBACrB,OAAO,GAAG,CAAC;oBACX,WAAW;gBACb;YACF;YACA,OAAO,MAAM,IAAI,CAAC;QACpB;kCAAG;QAAC;QAAK;QAAK;QAAM;QAAM;KAAM;IAChC,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAC7C,WAAW,GAAG,MAAM,CAAC,WAAW;IAClC,GAAG,SAAS,GAAG,CAAC,SAAU,QAAQ;QAChC,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,qJAAA,CAAA,UAAG,EAAE;YAC3C,WAAW;YACX,KAAK;YACL,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/Tracks/Track.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport cls from 'classnames';\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getOffset } from \"../util\";\nvar Track = function Track(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    start = props.start,\n    end = props.end,\n    index = props.index,\n    onStartMove = props.onStartMove,\n    replaceCls = props.replaceCls;\n  var _React$useContext = React.useContext(SliderContext),\n    direction = _React$useContext.direction,\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    disabled = _React$useContext.disabled,\n    range = _React$useContext.range,\n    classNames = _React$useContext.classNames;\n  var trackPrefixCls = \"\".concat(prefixCls, \"-track\");\n  var offsetStart = getOffset(start, min, max);\n  var offsetEnd = getOffset(end, min, max);\n\n  // ============================ Events ============================\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled && onStartMove) {\n      onStartMove(e, -1);\n    }\n  };\n\n  // ============================ Render ============================\n  var positionStyle = {};\n  switch (direction) {\n    case 'rtl':\n      positionStyle.right = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    case 'btt':\n      positionStyle.bottom = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    case 'ttb':\n      positionStyle.top = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    default:\n      positionStyle.left = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n  }\n  var className = replaceCls || cls(trackPrefixCls, _defineProperty(_defineProperty({}, \"\".concat(trackPrefixCls, \"-\").concat(index + 1), index !== null && range), \"\".concat(prefixCls, \"-track-draggable\"), onStartMove), classNames.track);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove\n  });\n};\nexport default Track;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,QAAQ,SAAS,MAAM,KAAK;IAC9B,IAAI,YAAY,MAAM,SAAS,EAC7B,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,KAAK,EACnB,MAAM,MAAM,GAAG,EACf,QAAQ,MAAM,KAAK,EACnB,cAAc,MAAM,WAAW,EAC/B,aAAa,MAAM,UAAU;IAC/B,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,gJAAA,CAAA,UAAa,GACpD,YAAY,kBAAkB,SAAS,EACvC,MAAM,kBAAkB,GAAG,EAC3B,MAAM,kBAAkB,GAAG,EAC3B,WAAW,kBAAkB,QAAQ,EACrC,QAAQ,kBAAkB,KAAK,EAC/B,aAAa,kBAAkB,UAAU;IAC3C,IAAI,iBAAiB,GAAG,MAAM,CAAC,WAAW;IAC1C,IAAI,cAAc,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,OAAO,KAAK;IACxC,IAAI,YAAY,CAAA,GAAA,6IAAA,CAAA,YAAS,AAAD,EAAE,KAAK,KAAK;IAEpC,mEAAmE;IACnE,IAAI,sBAAsB,SAAS,oBAAoB,CAAC;QACtD,IAAI,CAAC,YAAY,aAAa;YAC5B,YAAY,GAAG,CAAC;QAClB;IACF;IAEA,mEAAmE;IACnE,IAAI,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YACH,cAAc,KAAK,GAAG,GAAG,MAAM,CAAC,cAAc,KAAK;YACnD,cAAc,KAAK,GAAG,GAAG,MAAM,CAAC,YAAY,MAAM,cAAc,KAAK;YACrE;QACF,KAAK;YACH,cAAc,MAAM,GAAG,GAAG,MAAM,CAAC,cAAc,KAAK;YACpD,cAAc,MAAM,GAAG,GAAG,MAAM,CAAC,YAAY,MAAM,cAAc,KAAK;YACtE;QACF,KAAK;YACH,cAAc,GAAG,GAAG,GAAG,MAAM,CAAC,cAAc,KAAK;YACjD,cAAc,MAAM,GAAG,GAAG,MAAM,CAAC,YAAY,MAAM,cAAc,KAAK;YACtE;QACF;YACE,cAAc,IAAI,GAAG,GAAG,MAAM,CAAC,cAAc,KAAK;YAClD,cAAc,KAAK,GAAG,GAAG,MAAM,CAAC,YAAY,MAAM,cAAc,KAAK;IACzE;IACA,IAAI,YAAY,cAAc,CAAA,GAAA,sIAAA,CAAA,UAAG,AAAD,EAAE,gBAAgB,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,gBAAgB,KAAK,MAAM,CAAC,QAAQ,IAAI,UAAU,QAAQ,QAAQ,GAAG,MAAM,CAAC,WAAW,qBAAqB,cAAc,WAAW,KAAK;IAC1O,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAC7C,WAAW;QACX,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,gBAAgB;QACvD,aAAa;QACb,cAAc;IAChB;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/Tracks/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport cls from 'classnames';\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getIndex } from \"../util\";\nimport Track from \"./Track\";\nvar Tracks = function Tracks(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    values = props.values,\n    startPoint = props.startPoint,\n    onStartMove = props.onStartMove;\n  var _React$useContext = React.useContext(SliderContext),\n    included = _React$useContext.included,\n    range = _React$useContext.range,\n    min = _React$useContext.min,\n    styles = _React$useContext.styles,\n    classNames = _React$useContext.classNames;\n\n  // =========================== List ===========================\n  var trackList = React.useMemo(function () {\n    if (!range) {\n      // null value do not have track\n      if (values.length === 0) {\n        return [];\n      }\n      var startValue = startPoint !== null && startPoint !== void 0 ? startPoint : min;\n      var endValue = values[0];\n      return [{\n        start: Math.min(startValue, endValue),\n        end: Math.max(startValue, endValue)\n      }];\n    }\n\n    // Multiple\n    var list = [];\n    for (var i = 0; i < values.length - 1; i += 1) {\n      list.push({\n        start: values[i],\n        end: values[i + 1]\n      });\n    }\n    return list;\n  }, [values, range, startPoint, min]);\n  if (!included) {\n    return null;\n  }\n\n  // ========================== Render ==========================\n  var tracksNode = trackList !== null && trackList !== void 0 && trackList.length && (classNames.tracks || styles.tracks) ? /*#__PURE__*/React.createElement(Track, {\n    index: null,\n    prefixCls: prefixCls,\n    start: trackList[0].start,\n    end: trackList[trackList.length - 1].end,\n    replaceCls: cls(classNames.tracks, \"\".concat(prefixCls, \"-tracks\")),\n    style: styles.tracks\n  }) : null;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, tracksNode, trackList.map(function (_ref, index) {\n    var start = _ref.start,\n      end = _ref.end;\n    return /*#__PURE__*/React.createElement(Track, {\n      index: index,\n      prefixCls: prefixCls,\n      style: _objectSpread(_objectSpread({}, getIndex(style, index)), styles.track),\n      start: start,\n      end: end,\n      key: index,\n      onStartMove: onStartMove\n    });\n  }));\n};\nexport default Tracks;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,SAAS,SAAS,OAAO,KAAK;IAChC,IAAI,YAAY,MAAM,SAAS,EAC7B,QAAQ,MAAM,KAAK,EACnB,SAAS,MAAM,MAAM,EACrB,aAAa,MAAM,UAAU,EAC7B,cAAc,MAAM,WAAW;IACjC,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,gJAAA,CAAA,UAAa,GACpD,WAAW,kBAAkB,QAAQ,EACrC,QAAQ,kBAAkB,KAAK,EAC/B,MAAM,kBAAkB,GAAG,EAC3B,SAAS,kBAAkB,MAAM,EACjC,aAAa,kBAAkB,UAAU;IAE3C,+DAA+D;IAC/D,IAAI,YAAY,6JAAA,CAAA,UAAa;qCAAC;YAC5B,IAAI,CAAC,OAAO;gBACV,+BAA+B;gBAC/B,IAAI,OAAO,MAAM,KAAK,GAAG;oBACvB,OAAO,EAAE;gBACX;gBACA,IAAI,aAAa,eAAe,QAAQ,eAAe,KAAK,IAAI,aAAa;gBAC7E,IAAI,WAAW,MAAM,CAAC,EAAE;gBACxB,OAAO;oBAAC;wBACN,OAAO,KAAK,GAAG,CAAC,YAAY;wBAC5B,KAAK,KAAK,GAAG,CAAC,YAAY;oBAC5B;iBAAE;YACJ;YAEA,WAAW;YACX,IAAI,OAAO,EAAE;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,GAAG,GAAG,KAAK,EAAG;gBAC7C,KAAK,IAAI,CAAC;oBACR,OAAO,MAAM,CAAC,EAAE;oBAChB,KAAK,MAAM,CAAC,IAAI,EAAE;gBACpB;YACF;YACA,OAAO;QACT;oCAAG;QAAC;QAAQ;QAAO;QAAY;KAAI;IACnC,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,+DAA+D;IAC/D,IAAI,aAAa,cAAc,QAAQ,cAAc,KAAK,KAAK,UAAU,MAAM,IAAI,CAAC,WAAW,MAAM,IAAI,OAAO,MAAM,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,wJAAA,CAAA,UAAK,EAAE;QAChK,OAAO;QACP,WAAW;QACX,OAAO,SAAS,CAAC,EAAE,CAAC,KAAK;QACzB,KAAK,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,CAAC,GAAG;QACxC,YAAY,CAAA,GAAA,sIAAA,CAAA,UAAG,AAAD,EAAE,WAAW,MAAM,EAAE,GAAG,MAAM,CAAC,WAAW;QACxD,OAAO,OAAO,MAAM;IACtB,KAAK;IACL,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MAAM,YAAY,UAAU,GAAG,CAAC,SAAU,IAAI,EAAE,KAAK;QAC3G,IAAI,QAAQ,KAAK,KAAK,EACpB,MAAM,KAAK,GAAG;QAChB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,wJAAA,CAAA,UAAK,EAAE;YAC7C,OAAO;YACP,WAAW;YACX,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,SAAS,OAAO,KAAK;YAC5E,OAAO;YACP,KAAK;YACL,KAAK;YACL,aAAa;QACf;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/hooks/useDrag.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { UnstableContext } from \"../context\";\n/** Drag to delete offset. It's a user experience number for dragging out */\nvar REMOVE_DIST = 130;\nfunction getPosition(e) {\n  var obj = 'targetTouches' in e ? e.targetTouches[0] : e;\n  return {\n    pageX: obj.pageX,\n    pageY: obj.pageY\n  };\n}\nfunction useDrag(containerRef, direction, rawValues, min, max, formatValue, triggerChange, finishChange, offsetValues, editable, minCount) {\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    draggingValue = _React$useState2[0],\n    setDraggingValue = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    draggingIndex = _React$useState4[0],\n    setDraggingIndex = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    draggingDelete = _React$useState6[0],\n    setDraggingDelete = _React$useState6[1];\n  var _React$useState7 = React.useState(rawValues),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    cacheValues = _React$useState8[0],\n    setCacheValues = _React$useState8[1];\n  var _React$useState9 = React.useState(rawValues),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    originValues = _React$useState10[0],\n    setOriginValues = _React$useState10[1];\n  var mouseMoveEventRef = React.useRef(null);\n  var mouseUpEventRef = React.useRef(null);\n  var touchEventTargetRef = React.useRef(null);\n  var _React$useContext = React.useContext(UnstableContext),\n    onDragStart = _React$useContext.onDragStart,\n    onDragChange = _React$useContext.onDragChange;\n  useLayoutEffect(function () {\n    if (draggingIndex === -1) {\n      setCacheValues(rawValues);\n    }\n  }, [rawValues, draggingIndex]);\n\n  // Clean up event\n  React.useEffect(function () {\n    return function () {\n      document.removeEventListener('mousemove', mouseMoveEventRef.current);\n      document.removeEventListener('mouseup', mouseUpEventRef.current);\n      if (touchEventTargetRef.current) {\n        touchEventTargetRef.current.removeEventListener('touchmove', mouseMoveEventRef.current);\n        touchEventTargetRef.current.removeEventListener('touchend', mouseUpEventRef.current);\n      }\n    };\n  }, []);\n  var flushValues = function flushValues(nextValues, nextValue, deleteMark) {\n    // Perf: Only update state when value changed\n    if (nextValue !== undefined) {\n      setDraggingValue(nextValue);\n    }\n    setCacheValues(nextValues);\n    var changeValues = nextValues;\n    if (deleteMark) {\n      changeValues = nextValues.filter(function (_, i) {\n        return i !== draggingIndex;\n      });\n    }\n    triggerChange(changeValues);\n    if (onDragChange) {\n      onDragChange({\n        rawValues: nextValues,\n        deleteIndex: deleteMark ? draggingIndex : -1,\n        draggingIndex: draggingIndex,\n        draggingValue: nextValue\n      });\n    }\n  };\n  var updateCacheValue = useEvent(function (valueIndex, offsetPercent, deleteMark) {\n    if (valueIndex === -1) {\n      // >>>> Dragging on the track\n      var startValue = originValues[0];\n      var endValue = originValues[originValues.length - 1];\n      var maxStartOffset = min - startValue;\n      var maxEndOffset = max - endValue;\n\n      // Get valid offset\n      var offset = offsetPercent * (max - min);\n      offset = Math.max(offset, maxStartOffset);\n      offset = Math.min(offset, maxEndOffset);\n\n      // Use first value to revert back of valid offset (like steps marks)\n      var formatStartValue = formatValue(startValue + offset);\n      offset = formatStartValue - startValue;\n      var cloneCacheValues = originValues.map(function (val) {\n        return val + offset;\n      });\n      flushValues(cloneCacheValues);\n    } else {\n      // >>>> Dragging on the handle\n      var offsetDist = (max - min) * offsetPercent;\n\n      // Always start with the valueIndex origin value\n      var cloneValues = _toConsumableArray(cacheValues);\n      cloneValues[valueIndex] = originValues[valueIndex];\n      var next = offsetValues(cloneValues, offsetDist, valueIndex, 'dist');\n      flushValues(next.values, next.value, deleteMark);\n    }\n  });\n  var onStartMove = function onStartMove(e, valueIndex, startValues) {\n    e.stopPropagation();\n\n    // 如果是点击 track 触发的，需要传入变化后的初始值，而不能直接用 rawValues\n    var initialValues = startValues || rawValues;\n    var originValue = initialValues[valueIndex];\n    setDraggingIndex(valueIndex);\n    setDraggingValue(originValue);\n    setOriginValues(initialValues);\n    setCacheValues(initialValues);\n    setDraggingDelete(false);\n    var _getPosition = getPosition(e),\n      startX = _getPosition.pageX,\n      startY = _getPosition.pageY;\n\n    // We declare it here since closure can't get outer latest value\n    var deleteMark = false;\n\n    // Internal trigger event\n    if (onDragStart) {\n      onDragStart({\n        rawValues: initialValues,\n        draggingIndex: valueIndex,\n        draggingValue: originValue\n      });\n    }\n\n    // Moving\n    var onMouseMove = function onMouseMove(event) {\n      event.preventDefault();\n      var _getPosition2 = getPosition(event),\n        moveX = _getPosition2.pageX,\n        moveY = _getPosition2.pageY;\n      var offsetX = moveX - startX;\n      var offsetY = moveY - startY;\n      var _containerRef$current = containerRef.current.getBoundingClientRect(),\n        width = _containerRef$current.width,\n        height = _containerRef$current.height;\n      var offSetPercent;\n      var removeDist;\n      switch (direction) {\n        case 'btt':\n          offSetPercent = -offsetY / height;\n          removeDist = offsetX;\n          break;\n        case 'ttb':\n          offSetPercent = offsetY / height;\n          removeDist = offsetX;\n          break;\n        case 'rtl':\n          offSetPercent = -offsetX / width;\n          removeDist = offsetY;\n          break;\n        default:\n          offSetPercent = offsetX / width;\n          removeDist = offsetY;\n      }\n\n      // Check if need mark remove\n      deleteMark = editable ? Math.abs(removeDist) > REMOVE_DIST && minCount < cacheValues.length : false;\n      setDraggingDelete(deleteMark);\n      updateCacheValue(valueIndex, offSetPercent, deleteMark);\n    };\n\n    // End\n    var onMouseUp = function onMouseUp(event) {\n      event.preventDefault();\n      document.removeEventListener('mouseup', onMouseUp);\n      document.removeEventListener('mousemove', onMouseMove);\n      if (touchEventTargetRef.current) {\n        touchEventTargetRef.current.removeEventListener('touchmove', mouseMoveEventRef.current);\n        touchEventTargetRef.current.removeEventListener('touchend', mouseUpEventRef.current);\n      }\n      mouseMoveEventRef.current = null;\n      mouseUpEventRef.current = null;\n      touchEventTargetRef.current = null;\n      finishChange(deleteMark);\n      setDraggingIndex(-1);\n      setDraggingDelete(false);\n    };\n    document.addEventListener('mouseup', onMouseUp);\n    document.addEventListener('mousemove', onMouseMove);\n    e.currentTarget.addEventListener('touchend', onMouseUp);\n    e.currentTarget.addEventListener('touchmove', onMouseMove);\n    mouseMoveEventRef.current = onMouseMove;\n    mouseUpEventRef.current = onMouseUp;\n    touchEventTargetRef.current = e.currentTarget;\n  };\n\n  // Only return cache value when it mapping with rawValues\n  var returnValues = React.useMemo(function () {\n    var sourceValues = _toConsumableArray(rawValues).sort(function (a, b) {\n      return a - b;\n    });\n    var targetValues = _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n    var counts = {};\n    targetValues.forEach(function (val) {\n      counts[val] = (counts[val] || 0) + 1;\n    });\n    sourceValues.forEach(function (val) {\n      counts[val] = (counts[val] || 0) - 1;\n    });\n    var maxDiffCount = editable ? 1 : 0;\n    var diffCount = Object.values(counts).reduce(function (prev, next) {\n      return prev + Math.abs(next);\n    }, 0);\n    return diffCount <= maxDiffCount ? cacheValues : rawValues;\n  }, [rawValues, cacheValues, editable]);\n  return [draggingIndex, draggingValue, draggingDelete, returnValues, onStartMove];\n}\nexport default useDrag;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,0EAA0E,GAC1E,IAAI,cAAc;AAClB,SAAS,YAAY,CAAC;IACpB,IAAI,MAAM,mBAAmB,IAAI,EAAE,aAAa,CAAC,EAAE,GAAG;IACtD,OAAO;QACL,OAAO,IAAI,KAAK;QAChB,OAAO,IAAI,KAAK;IAClB;AACF;AACA,SAAS,QAAQ,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ;IACvI,IAAI,kBAAkB,6JAAA,CAAA,WAAc,CAAC,OACnC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,gBAAgB,gBAAgB,CAAC,EAAE,EACnC,mBAAmB,gBAAgB,CAAC,EAAE;IACxC,IAAI,mBAAmB,6JAAA,CAAA,WAAc,CAAC,CAAC,IACrC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,gBAAgB,gBAAgB,CAAC,EAAE,EACnC,mBAAmB,gBAAgB,CAAC,EAAE;IACxC,IAAI,mBAAmB,6JAAA,CAAA,WAAc,CAAC,QACpC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,iBAAiB,gBAAgB,CAAC,EAAE,EACpC,oBAAoB,gBAAgB,CAAC,EAAE;IACzC,IAAI,mBAAmB,6JAAA,CAAA,WAAc,CAAC,YACpC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,cAAc,gBAAgB,CAAC,EAAE,EACjC,iBAAiB,gBAAgB,CAAC,EAAE;IACtC,IAAI,mBAAmB,6JAAA,CAAA,WAAc,CAAC,YACpC,oBAAoB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACrD,eAAe,iBAAiB,CAAC,EAAE,EACnC,kBAAkB,iBAAiB,CAAC,EAAE;IACxC,IAAI,oBAAoB,6JAAA,CAAA,SAAY,CAAC;IACrC,IAAI,kBAAkB,6JAAA,CAAA,SAAY,CAAC;IACnC,IAAI,sBAAsB,6JAAA,CAAA,SAAY,CAAC;IACvC,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,gJAAA,CAAA,kBAAe,GACtD,cAAc,kBAAkB,WAAW,EAC3C,eAAe,kBAAkB,YAAY;IAC/C,CAAA,GAAA,+JAAA,CAAA,UAAe,AAAD;mCAAE;YACd,IAAI,kBAAkB,CAAC,GAAG;gBACxB,eAAe;YACjB;QACF;kCAAG;QAAC;QAAW;KAAc;IAE7B,iBAAiB;IACjB,6JAAA,CAAA,YAAe;6BAAC;YACd;qCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa,kBAAkB,OAAO;oBACnE,SAAS,mBAAmB,CAAC,WAAW,gBAAgB,OAAO;oBAC/D,IAAI,oBAAoB,OAAO,EAAE;wBAC/B,oBAAoB,OAAO,CAAC,mBAAmB,CAAC,aAAa,kBAAkB,OAAO;wBACtF,oBAAoB,OAAO,CAAC,mBAAmB,CAAC,YAAY,gBAAgB,OAAO;oBACrF;gBACF;;QACF;4BAAG,EAAE;IACL,IAAI,cAAc,SAAS,YAAY,UAAU,EAAE,SAAS,EAAE,UAAU;QACtE,6CAA6C;QAC7C,IAAI,cAAc,WAAW;YAC3B,iBAAiB;QACnB;QACA,eAAe;QACf,IAAI,eAAe;QACnB,IAAI,YAAY;YACd,eAAe,WAAW,MAAM,CAAC,SAAU,CAAC,EAAE,CAAC;gBAC7C,OAAO,MAAM;YACf;QACF;QACA,cAAc;QACd,IAAI,cAAc;YAChB,aAAa;gBACX,WAAW;gBACX,aAAa,aAAa,gBAAgB,CAAC;gBAC3C,eAAe;gBACf,eAAe;YACjB;QACF;IACF;IACA,IAAI,mBAAmB,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD;8CAAE,SAAU,UAAU,EAAE,aAAa,EAAE,UAAU;YAC7E,IAAI,eAAe,CAAC,GAAG;gBACrB,6BAA6B;gBAC7B,IAAI,aAAa,YAAY,CAAC,EAAE;gBAChC,IAAI,WAAW,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;gBACpD,IAAI,iBAAiB,MAAM;gBAC3B,IAAI,eAAe,MAAM;gBAEzB,mBAAmB;gBACnB,IAAI,SAAS,gBAAgB,CAAC,MAAM,GAAG;gBACvC,SAAS,KAAK,GAAG,CAAC,QAAQ;gBAC1B,SAAS,KAAK,GAAG,CAAC,QAAQ;gBAE1B,oEAAoE;gBACpE,IAAI,mBAAmB,YAAY,aAAa;gBAChD,SAAS,mBAAmB;gBAC5B,IAAI,mBAAmB,aAAa,GAAG;2EAAC,SAAU,GAAG;wBACnD,OAAO,MAAM;oBACf;;gBACA,YAAY;YACd,OAAO;gBACL,8BAA8B;gBAC9B,IAAI,aAAa,CAAC,MAAM,GAAG,IAAI;gBAE/B,gDAAgD;gBAChD,IAAI,cAAc,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;gBACrC,WAAW,CAAC,WAAW,GAAG,YAAY,CAAC,WAAW;gBAClD,IAAI,OAAO,aAAa,aAAa,YAAY,YAAY;gBAC7D,YAAY,KAAK,MAAM,EAAE,KAAK,KAAK,EAAE;YACvC;QACF;;IACA,IAAI,cAAc,SAAS,YAAY,CAAC,EAAE,UAAU,EAAE,WAAW;QAC/D,EAAE,eAAe;QAEjB,+CAA+C;QAC/C,IAAI,gBAAgB,eAAe;QACnC,IAAI,cAAc,aAAa,CAAC,WAAW;QAC3C,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,kBAAkB;QAClB,IAAI,eAAe,YAAY,IAC7B,SAAS,aAAa,KAAK,EAC3B,SAAS,aAAa,KAAK;QAE7B,gEAAgE;QAChE,IAAI,aAAa;QAEjB,yBAAyB;QACzB,IAAI,aAAa;YACf,YAAY;gBACV,WAAW;gBACX,eAAe;gBACf,eAAe;YACjB;QACF;QAEA,SAAS;QACT,IAAI,cAAc,SAAS,YAAY,KAAK;YAC1C,MAAM,cAAc;YACpB,IAAI,gBAAgB,YAAY,QAC9B,QAAQ,cAAc,KAAK,EAC3B,QAAQ,cAAc,KAAK;YAC7B,IAAI,UAAU,QAAQ;YACtB,IAAI,UAAU,QAAQ;YACtB,IAAI,wBAAwB,aAAa,OAAO,CAAC,qBAAqB,IACpE,QAAQ,sBAAsB,KAAK,EACnC,SAAS,sBAAsB,MAAM;YACvC,IAAI;YACJ,IAAI;YACJ,OAAQ;gBACN,KAAK;oBACH,gBAAgB,CAAC,UAAU;oBAC3B,aAAa;oBACb;gBACF,KAAK;oBACH,gBAAgB,UAAU;oBAC1B,aAAa;oBACb;gBACF,KAAK;oBACH,gBAAgB,CAAC,UAAU;oBAC3B,aAAa;oBACb;gBACF;oBACE,gBAAgB,UAAU;oBAC1B,aAAa;YACjB;YAEA,4BAA4B;YAC5B,aAAa,WAAW,KAAK,GAAG,CAAC,cAAc,eAAe,WAAW,YAAY,MAAM,GAAG;YAC9F,kBAAkB;YAClB,iBAAiB,YAAY,eAAe;QAC9C;QAEA,MAAM;QACN,IAAI,YAAY,SAAS,UAAU,KAAK;YACtC,MAAM,cAAc;YACpB,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,aAAa;YAC1C,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,oBAAoB,OAAO,CAAC,mBAAmB,CAAC,aAAa,kBAAkB,OAAO;gBACtF,oBAAoB,OAAO,CAAC,mBAAmB,CAAC,YAAY,gBAAgB,OAAO;YACrF;YACA,kBAAkB,OAAO,GAAG;YAC5B,gBAAgB,OAAO,GAAG;YAC1B,oBAAoB,OAAO,GAAG;YAC9B,aAAa;YACb,iBAAiB,CAAC;YAClB,kBAAkB;QACpB;QACA,SAAS,gBAAgB,CAAC,WAAW;QACrC,SAAS,gBAAgB,CAAC,aAAa;QACvC,EAAE,aAAa,CAAC,gBAAgB,CAAC,YAAY;QAC7C,EAAE,aAAa,CAAC,gBAAgB,CAAC,aAAa;QAC9C,kBAAkB,OAAO,GAAG;QAC5B,gBAAgB,OAAO,GAAG;QAC1B,oBAAoB,OAAO,GAAG,EAAE,aAAa;IAC/C;IAEA,yDAAyD;IACzD,IAAI,eAAe,6JAAA,CAAA,UAAa;yCAAC;YAC/B,IAAI,eAAe,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,WAAW,IAAI;8DAAC,SAAU,CAAC,EAAE,CAAC;oBAClE,OAAO,IAAI;gBACb;;YACA,IAAI,eAAe,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,aAAa,IAAI;8DAAC,SAAU,CAAC,EAAE,CAAC;oBACpE,OAAO,IAAI;gBACb;;YACA,IAAI,SAAS,CAAC;YACd,aAAa,OAAO;iDAAC,SAAU,GAAG;oBAChC,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI;gBACrC;;YACA,aAAa,OAAO;iDAAC,SAAU,GAAG;oBAChC,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI;gBACrC;;YACA,IAAI,eAAe,WAAW,IAAI;YAClC,IAAI,YAAY,OAAO,MAAM,CAAC,QAAQ,MAAM;2DAAC,SAAU,IAAI,EAAE,IAAI;oBAC/D,OAAO,OAAO,KAAK,GAAG,CAAC;gBACzB;0DAAG;YACH,OAAO,aAAa,eAAe,cAAc;QACnD;wCAAG;QAAC;QAAW;QAAa;KAAS;IACrC,OAAO;QAAC;QAAe;QAAe;QAAgB;QAAc;KAAY;AAClF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/hooks/useOffset.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\n\n/** Format the value in the range of [min, max] */\n\n/** Format value align with step */\n\n/** Format value align with step & marks */\n\nexport default function useOffset(min, max, step, markList, allowCross, pushable) {\n  var formatRangeValue = React.useCallback(function (val) {\n    return Math.max(min, Math.min(max, val));\n  }, [min, max]);\n  var formatStepValue = React.useCallback(function (val) {\n    if (step !== null) {\n      var stepValue = min + Math.round((formatRangeValue(val) - min) / step) * step;\n\n      // Cut number in case to be like 0.30000000000000004\n      var getDecimal = function getDecimal(num) {\n        return (String(num).split('.')[1] || '').length;\n      };\n      var maxDecimal = Math.max(getDecimal(step), getDecimal(max), getDecimal(min));\n      var fixedValue = Number(stepValue.toFixed(maxDecimal));\n      return min <= fixedValue && fixedValue <= max ? fixedValue : null;\n    }\n    return null;\n  }, [step, min, max, formatRangeValue]);\n  var formatValue = React.useCallback(function (val) {\n    var formatNextValue = formatRangeValue(val);\n\n    // List align values\n    var alignValues = markList.map(function (mark) {\n      return mark.value;\n    });\n    if (step !== null) {\n      alignValues.push(formatStepValue(val));\n    }\n\n    // min & max\n    alignValues.push(min, max);\n\n    // Align with marks\n    var closeValue = alignValues[0];\n    var closeDist = max - min;\n    alignValues.forEach(function (alignValue) {\n      var dist = Math.abs(formatNextValue - alignValue);\n      if (dist <= closeDist) {\n        closeValue = alignValue;\n        closeDist = dist;\n      }\n    });\n    return closeValue;\n  }, [min, max, markList, step, formatRangeValue, formatStepValue]);\n\n  // ========================== Offset ==========================\n  // Single Value\n  var offsetValue = function offsetValue(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    if (typeof offset === 'number') {\n      var nextValue;\n      var originValue = values[valueIndex];\n\n      // Only used for `dist` mode\n      var targetDistValue = originValue + offset;\n\n      // Compare next step value & mark value which is best match\n      var potentialValues = [];\n      markList.forEach(function (mark) {\n        potentialValues.push(mark.value);\n      });\n\n      // Min & Max\n      potentialValues.push(min, max);\n\n      // In case origin value is align with mark but not with step\n      potentialValues.push(formatStepValue(originValue));\n\n      // Put offset step value also\n      var sign = offset > 0 ? 1 : -1;\n      if (mode === 'unit') {\n        potentialValues.push(formatStepValue(originValue + sign * step));\n      } else {\n        potentialValues.push(formatStepValue(targetDistValue));\n      }\n\n      // Find close one\n      potentialValues = potentialValues.filter(function (val) {\n        return val !== null;\n      })\n      // Remove reverse value\n      .filter(function (val) {\n        return offset < 0 ? val <= originValue : val >= originValue;\n      });\n      if (mode === 'unit') {\n        // `unit` mode can not contain itself\n        potentialValues = potentialValues.filter(function (val) {\n          return val !== originValue;\n        });\n      }\n      var compareValue = mode === 'unit' ? originValue : targetDistValue;\n      nextValue = potentialValues[0];\n      var valueDist = Math.abs(nextValue - compareValue);\n      potentialValues.forEach(function (potentialValue) {\n        var dist = Math.abs(potentialValue - compareValue);\n        if (dist < valueDist) {\n          nextValue = potentialValue;\n          valueDist = dist;\n        }\n      });\n\n      // Out of range will back to range\n      if (nextValue === undefined) {\n        return offset < 0 ? min : max;\n      }\n\n      // `dist` mode\n      if (mode === 'dist') {\n        return nextValue;\n      }\n\n      // `unit` mode may need another round\n      if (Math.abs(offset) > 1) {\n        var cloneValues = _toConsumableArray(values);\n        cloneValues[valueIndex] = nextValue;\n        return offsetValue(cloneValues, offset - sign, valueIndex, mode);\n      }\n      return nextValue;\n    } else if (offset === 'min') {\n      return min;\n    } else if (offset === 'max') {\n      return max;\n    }\n  };\n\n  /** Same as `offsetValue` but return `changed` mark to tell value changed */\n  var offsetChangedValue = function offsetChangedValue(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    var originValue = values[valueIndex];\n    var nextValue = offsetValue(values, offset, valueIndex, mode);\n    return {\n      value: nextValue,\n      changed: nextValue !== originValue\n    };\n  };\n  var needPush = function needPush(dist) {\n    return pushable === null && dist === 0 || typeof pushable === 'number' && dist < pushable;\n  };\n\n  // Values\n  var offsetValues = function offsetValues(values, offset, valueIndex) {\n    var mode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'unit';\n    var nextValues = values.map(formatValue);\n    var originValue = nextValues[valueIndex];\n    var nextValue = offsetValue(nextValues, offset, valueIndex, mode);\n    nextValues[valueIndex] = nextValue;\n    if (allowCross === false) {\n      // >>>>> Allow Cross\n      var pushNum = pushable || 0;\n\n      // ============ AllowCross ===============\n      if (valueIndex > 0 && nextValues[valueIndex - 1] !== originValue) {\n        nextValues[valueIndex] = Math.max(nextValues[valueIndex], nextValues[valueIndex - 1] + pushNum);\n      }\n      if (valueIndex < nextValues.length - 1 && nextValues[valueIndex + 1] !== originValue) {\n        nextValues[valueIndex] = Math.min(nextValues[valueIndex], nextValues[valueIndex + 1] - pushNum);\n      }\n    } else if (typeof pushable === 'number' || pushable === null) {\n      // >>>>> Pushable\n      // =============== Push ==================\n\n      // >>>>>> Basic push\n      // End values\n      for (var i = valueIndex + 1; i < nextValues.length; i += 1) {\n        var changed = true;\n        while (needPush(nextValues[i] - nextValues[i - 1]) && changed) {\n          var _offsetChangedValue = offsetChangedValue(nextValues, 1, i);\n          nextValues[i] = _offsetChangedValue.value;\n          changed = _offsetChangedValue.changed;\n        }\n      }\n\n      // Start values\n      for (var _i = valueIndex; _i > 0; _i -= 1) {\n        var _changed = true;\n        while (needPush(nextValues[_i] - nextValues[_i - 1]) && _changed) {\n          var _offsetChangedValue2 = offsetChangedValue(nextValues, -1, _i - 1);\n          nextValues[_i - 1] = _offsetChangedValue2.value;\n          _changed = _offsetChangedValue2.changed;\n        }\n      }\n\n      // >>>>> Revert back to safe push range\n      // End to Start\n      for (var _i2 = nextValues.length - 1; _i2 > 0; _i2 -= 1) {\n        var _changed2 = true;\n        while (needPush(nextValues[_i2] - nextValues[_i2 - 1]) && _changed2) {\n          var _offsetChangedValue3 = offsetChangedValue(nextValues, -1, _i2 - 1);\n          nextValues[_i2 - 1] = _offsetChangedValue3.value;\n          _changed2 = _offsetChangedValue3.changed;\n        }\n      }\n\n      // Start to End\n      for (var _i3 = 0; _i3 < nextValues.length - 1; _i3 += 1) {\n        var _changed3 = true;\n        while (needPush(nextValues[_i3 + 1] - nextValues[_i3]) && _changed3) {\n          var _offsetChangedValue4 = offsetChangedValue(nextValues, 1, _i3 + 1);\n          nextValues[_i3 + 1] = _offsetChangedValue4.value;\n          _changed3 = _offsetChangedValue4.changed;\n        }\n      }\n    }\n    return {\n      value: nextValues[valueIndex],\n      values: nextValues\n    };\n  };\n  return [formatValue, offsetValues];\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAQe,SAAS,UAAU,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ;IAC9E,IAAI,mBAAmB,6JAAA,CAAA,cAAiB;mDAAC,SAAU,GAAG;YACpD,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK;QACrC;kDAAG;QAAC;QAAK;KAAI;IACb,IAAI,kBAAkB,6JAAA,CAAA,cAAiB;kDAAC,SAAU,GAAG;YACnD,IAAI,SAAS,MAAM;gBACjB,IAAI,YAAY,MAAM,KAAK,KAAK,CAAC,CAAC,iBAAiB,OAAO,GAAG,IAAI,QAAQ;gBAEzE,oDAAoD;gBACpD,IAAI,aAAa,SAAS,WAAW,GAAG;oBACtC,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM;gBACjD;gBACA,IAAI,aAAa,KAAK,GAAG,CAAC,WAAW,OAAO,WAAW,MAAM,WAAW;gBACxE,IAAI,aAAa,OAAO,UAAU,OAAO,CAAC;gBAC1C,OAAO,OAAO,cAAc,cAAc,MAAM,aAAa;YAC/D;YACA,OAAO;QACT;iDAAG;QAAC;QAAM;QAAK;QAAK;KAAiB;IACrC,IAAI,cAAc,6JAAA,CAAA,cAAiB;8CAAC,SAAU,GAAG;YAC/C,IAAI,kBAAkB,iBAAiB;YAEvC,oBAAoB;YACpB,IAAI,cAAc,SAAS,GAAG;kEAAC,SAAU,IAAI;oBAC3C,OAAO,KAAK,KAAK;gBACnB;;YACA,IAAI,SAAS,MAAM;gBACjB,YAAY,IAAI,CAAC,gBAAgB;YACnC;YAEA,YAAY;YACZ,YAAY,IAAI,CAAC,KAAK;YAEtB,mBAAmB;YACnB,IAAI,aAAa,WAAW,CAAC,EAAE;YAC/B,IAAI,YAAY,MAAM;YACtB,YAAY,OAAO;sDAAC,SAAU,UAAU;oBACtC,IAAI,OAAO,KAAK,GAAG,CAAC,kBAAkB;oBACtC,IAAI,QAAQ,WAAW;wBACrB,aAAa;wBACb,YAAY;oBACd;gBACF;;YACA,OAAO;QACT;6CAAG;QAAC;QAAK;QAAK;QAAU;QAAM;QAAkB;KAAgB;IAEhE,+DAA+D;IAC/D,eAAe;IACf,IAAI,cAAc,SAAS,YAAY,MAAM,EAAE,MAAM,EAAE,UAAU;QAC/D,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAC/E,IAAI,OAAO,WAAW,UAAU;YAC9B,IAAI;YACJ,IAAI,cAAc,MAAM,CAAC,WAAW;YAEpC,4BAA4B;YAC5B,IAAI,kBAAkB,cAAc;YAEpC,2DAA2D;YAC3D,IAAI,kBAAkB,EAAE;YACxB,SAAS,OAAO,CAAC,SAAU,IAAI;gBAC7B,gBAAgB,IAAI,CAAC,KAAK,KAAK;YACjC;YAEA,YAAY;YACZ,gBAAgB,IAAI,CAAC,KAAK;YAE1B,4DAA4D;YAC5D,gBAAgB,IAAI,CAAC,gBAAgB;YAErC,6BAA6B;YAC7B,IAAI,OAAO,SAAS,IAAI,IAAI,CAAC;YAC7B,IAAI,SAAS,QAAQ;gBACnB,gBAAgB,IAAI,CAAC,gBAAgB,cAAc,OAAO;YAC5D,OAAO;gBACL,gBAAgB,IAAI,CAAC,gBAAgB;YACvC;YAEA,iBAAiB;YACjB,kBAAkB,gBAAgB,MAAM,CAAC,SAAU,GAAG;gBACpD,OAAO,QAAQ;YACjB,EACA,uBAAuB;aACtB,MAAM,CAAC,SAAU,GAAG;gBACnB,OAAO,SAAS,IAAI,OAAO,cAAc,OAAO;YAClD;YACA,IAAI,SAAS,QAAQ;gBACnB,qCAAqC;gBACrC,kBAAkB,gBAAgB,MAAM,CAAC,SAAU,GAAG;oBACpD,OAAO,QAAQ;gBACjB;YACF;YACA,IAAI,eAAe,SAAS,SAAS,cAAc;YACnD,YAAY,eAAe,CAAC,EAAE;YAC9B,IAAI,YAAY,KAAK,GAAG,CAAC,YAAY;YACrC,gBAAgB,OAAO,CAAC,SAAU,cAAc;gBAC9C,IAAI,OAAO,KAAK,GAAG,CAAC,iBAAiB;gBACrC,IAAI,OAAO,WAAW;oBACpB,YAAY;oBACZ,YAAY;gBACd;YACF;YAEA,kCAAkC;YAClC,IAAI,cAAc,WAAW;gBAC3B,OAAO,SAAS,IAAI,MAAM;YAC5B;YAEA,cAAc;YACd,IAAI,SAAS,QAAQ;gBACnB,OAAO;YACT;YAEA,qCAAqC;YACrC,IAAI,KAAK,GAAG,CAAC,UAAU,GAAG;gBACxB,IAAI,cAAc,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;gBACrC,WAAW,CAAC,WAAW,GAAG;gBAC1B,OAAO,YAAY,aAAa,SAAS,MAAM,YAAY;YAC7D;YACA,OAAO;QACT,OAAO,IAAI,WAAW,OAAO;YAC3B,OAAO;QACT,OAAO,IAAI,WAAW,OAAO;YAC3B,OAAO;QACT;IACF;IAEA,0EAA0E,GAC1E,IAAI,qBAAqB,SAAS,mBAAmB,MAAM,EAAE,MAAM,EAAE,UAAU;QAC7E,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAC/E,IAAI,cAAc,MAAM,CAAC,WAAW;QACpC,IAAI,YAAY,YAAY,QAAQ,QAAQ,YAAY;QACxD,OAAO;YACL,OAAO;YACP,SAAS,cAAc;QACzB;IACF;IACA,IAAI,WAAW,SAAS,SAAS,IAAI;QACnC,OAAO,aAAa,QAAQ,SAAS,KAAK,OAAO,aAAa,YAAY,OAAO;IACnF;IAEA,SAAS;IACT,IAAI,eAAe,SAAS,aAAa,MAAM,EAAE,MAAM,EAAE,UAAU;QACjE,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QAC/E,IAAI,aAAa,OAAO,GAAG,CAAC;QAC5B,IAAI,cAAc,UAAU,CAAC,WAAW;QACxC,IAAI,YAAY,YAAY,YAAY,QAAQ,YAAY;QAC5D,UAAU,CAAC,WAAW,GAAG;QACzB,IAAI,eAAe,OAAO;YACxB,oBAAoB;YACpB,IAAI,UAAU,YAAY;YAE1B,0CAA0C;YAC1C,IAAI,aAAa,KAAK,UAAU,CAAC,aAAa,EAAE,KAAK,aAAa;gBAChE,UAAU,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,aAAa,EAAE,GAAG;YACzF;YACA,IAAI,aAAa,WAAW,MAAM,GAAG,KAAK,UAAU,CAAC,aAAa,EAAE,KAAK,aAAa;gBACpF,UAAU,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,aAAa,EAAE,GAAG;YACzF;QACF,OAAO,IAAI,OAAO,aAAa,YAAY,aAAa,MAAM;YAC5D,iBAAiB;YACjB,0CAA0C;YAE1C,oBAAoB;YACpB,aAAa;YACb,IAAK,IAAI,IAAI,aAAa,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;gBAC1D,IAAI,UAAU;gBACd,MAAO,SAAS,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,EAAE,KAAK,QAAS;oBAC7D,IAAI,sBAAsB,mBAAmB,YAAY,GAAG;oBAC5D,UAAU,CAAC,EAAE,GAAG,oBAAoB,KAAK;oBACzC,UAAU,oBAAoB,OAAO;gBACvC;YACF;YAEA,eAAe;YACf,IAAK,IAAI,KAAK,YAAY,KAAK,GAAG,MAAM,EAAG;gBACzC,IAAI,WAAW;gBACf,MAAO,SAAS,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK,EAAE,KAAK,SAAU;oBAChE,IAAI,uBAAuB,mBAAmB,YAAY,CAAC,GAAG,KAAK;oBACnE,UAAU,CAAC,KAAK,EAAE,GAAG,qBAAqB,KAAK;oBAC/C,WAAW,qBAAqB,OAAO;gBACzC;YACF;YAEA,uCAAuC;YACvC,eAAe;YACf,IAAK,IAAI,MAAM,WAAW,MAAM,GAAG,GAAG,MAAM,GAAG,OAAO,EAAG;gBACvD,IAAI,YAAY;gBAChB,MAAO,SAAS,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,UAAW;oBACnE,IAAI,uBAAuB,mBAAmB,YAAY,CAAC,GAAG,MAAM;oBACpE,UAAU,CAAC,MAAM,EAAE,GAAG,qBAAqB,KAAK;oBAChD,YAAY,qBAAqB,OAAO;gBAC1C;YACF;YAEA,eAAe;YACf,IAAK,IAAI,MAAM,GAAG,MAAM,WAAW,MAAM,GAAG,GAAG,OAAO,EAAG;gBACvD,IAAI,YAAY;gBAChB,MAAO,SAAS,UAAU,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,IAAI,KAAK,UAAW;oBACnE,IAAI,uBAAuB,mBAAmB,YAAY,GAAG,MAAM;oBACnE,UAAU,CAAC,MAAM,EAAE,GAAG,qBAAqB,KAAK;oBAChD,YAAY,qBAAqB,OAAO;gBAC1C;YACF;QACF;QACA,OAAO;YACL,OAAO,UAAU,CAAC,WAAW;YAC7B,QAAQ;QACV;IACF;IACA,OAAO;QAAC;QAAa;KAAa;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/hooks/useRange.js"], "sourcesContent": ["import { warning } from \"rc-util/es/warning\";\nimport { useMemo } from 'react';\nexport default function useRange(range) {\n  return useMemo(function () {\n    if (range === true || !range) {\n      return [!!range, false, false, 0];\n    }\n    var editable = range.editable,\n      draggableTrack = range.draggableTrack,\n      minCount = range.minCount,\n      maxCount = range.maxCount;\n    if (process.env.NODE_ENV !== 'production') {\n      warning(!editable || !draggableTrack, '`editable` can not work with `draggableTrack`.');\n    }\n    return [true, editable, !editable && draggableTrack, minCount || 0, maxCount];\n  }, [range]);\n}"], "names": [], "mappings": ";;;AAWQ;AAXR;AACA;;;AACe,SAAS,SAAS,KAAK;IACpC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4BAAE;YACb,IAAI,UAAU,QAAQ,CAAC,OAAO;gBAC5B,OAAO;oBAAC,CAAC,CAAC;oBAAO;oBAAO;oBAAO;iBAAE;YACnC;YACA,IAAI,WAAW,MAAM,QAAQ,EAC3B,iBAAiB,MAAM,cAAc,EACrC,WAAW,MAAM,QAAQ,EACzB,WAAW,MAAM,QAAQ;YAC3B,wCAA2C;gBACzC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,YAAY,CAAC,gBAAgB;YACxC;YACA,OAAO;gBAAC;gBAAM;gBAAU,CAAC,YAAY;gBAAgB,YAAY;gBAAG;aAAS;QAC/E;2BAAG;QAAC;KAAM;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1147, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/Slider.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport cls from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport Handles from \"./Handles\";\nimport Marks from \"./Marks\";\nimport Steps from \"./Steps\";\nimport Tracks from \"./Tracks\";\nimport SliderContext from \"./context\";\nimport useDrag from \"./hooks/useDrag\";\nimport useOffset from \"./hooks/useOffset\";\nimport useRange from \"./hooks/useRange\";\n\n/**\n * New:\n * - click mark to update range value\n * - handleRender\n * - Fix handle with count not correct\n * - Fix pushable not work in some case\n * - No more FindDOMNode\n * - Move all position related style into inline style\n * - Key: up is plus, down is minus\n * - fix Key with step = null not align with marks\n * - Change range should not trigger onChange\n * - keyboard support pushable\n */\n\nvar Slider = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-slider' : _props$prefixCls,\n    className = props.className,\n    style = props.style,\n    classNames = props.classNames,\n    styles = props.styles,\n    id = props.id,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled,\n    _props$keyboard = props.keyboard,\n    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,\n    autoFocus = props.autoFocus,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    _props$min = props.min,\n    min = _props$min === void 0 ? 0 : _props$min,\n    _props$max = props.max,\n    max = _props$max === void 0 ? 100 : _props$max,\n    _props$step = props.step,\n    step = _props$step === void 0 ? 1 : _props$step,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    range = props.range,\n    count = props.count,\n    onChange = props.onChange,\n    onBeforeChange = props.onBeforeChange,\n    onAfterChange = props.onAfterChange,\n    onChangeComplete = props.onChangeComplete,\n    _props$allowCross = props.allowCross,\n    allowCross = _props$allowCross === void 0 ? true : _props$allowCross,\n    _props$pushable = props.pushable,\n    pushable = _props$pushable === void 0 ? false : _props$pushable,\n    reverse = props.reverse,\n    vertical = props.vertical,\n    _props$included = props.included,\n    included = _props$included === void 0 ? true : _props$included,\n    startPoint = props.startPoint,\n    trackStyle = props.trackStyle,\n    handleStyle = props.handleStyle,\n    railStyle = props.railStyle,\n    dotStyle = props.dotStyle,\n    activeDotStyle = props.activeDotStyle,\n    marks = props.marks,\n    dots = props.dots,\n    handleRender = props.handleRender,\n    activeHandleRender = props.activeHandleRender,\n    track = props.track,\n    _props$tabIndex = props.tabIndex,\n    tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,\n    ariaLabelForHandle = props.ariaLabelForHandle,\n    ariaLabelledByForHandle = props.ariaLabelledByForHandle,\n    ariaRequired = props.ariaRequired,\n    ariaValueTextFormatterForHandle = props.ariaValueTextFormatterForHandle;\n  var handlesRef = React.useRef(null);\n  var containerRef = React.useRef(null);\n  var direction = React.useMemo(function () {\n    if (vertical) {\n      return reverse ? 'ttb' : 'btt';\n    }\n    return reverse ? 'rtl' : 'ltr';\n  }, [reverse, vertical]);\n\n  // ============================ Range =============================\n  var _useRange = useRange(range),\n    _useRange2 = _slicedToArray(_useRange, 5),\n    rangeEnabled = _useRange2[0],\n    rangeEditable = _useRange2[1],\n    rangeDraggableTrack = _useRange2[2],\n    minCount = _useRange2[3],\n    maxCount = _useRange2[4];\n  var mergedMin = React.useMemo(function () {\n    return isFinite(min) ? min : 0;\n  }, [min]);\n  var mergedMax = React.useMemo(function () {\n    return isFinite(max) ? max : 100;\n  }, [max]);\n\n  // ============================= Step =============================\n  var mergedStep = React.useMemo(function () {\n    return step !== null && step <= 0 ? 1 : step;\n  }, [step]);\n\n  // ============================= Push =============================\n  var mergedPush = React.useMemo(function () {\n    if (typeof pushable === 'boolean') {\n      return pushable ? mergedStep : false;\n    }\n    return pushable >= 0 ? pushable : false;\n  }, [pushable, mergedStep]);\n\n  // ============================ Marks =============================\n  var markList = React.useMemo(function () {\n    return Object.keys(marks || {}).map(function (key) {\n      var mark = marks[key];\n      var markObj = {\n        value: Number(key)\n      };\n      if (mark && _typeof(mark) === 'object' && ! /*#__PURE__*/React.isValidElement(mark) && ('label' in mark || 'style' in mark)) {\n        markObj.style = mark.style;\n        markObj.label = mark.label;\n      } else {\n        markObj.label = mark;\n      }\n      return markObj;\n    }).filter(function (_ref) {\n      var label = _ref.label;\n      return label || typeof label === 'number';\n    }).sort(function (a, b) {\n      return a.value - b.value;\n    });\n  }, [marks]);\n\n  // ============================ Format ============================\n  var _useOffset = useOffset(mergedMin, mergedMax, mergedStep, markList, allowCross, mergedPush),\n    _useOffset2 = _slicedToArray(_useOffset, 2),\n    formatValue = _useOffset2[0],\n    offsetValues = _useOffset2[1];\n\n  // ============================ Values ============================\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var rawValues = React.useMemo(function () {\n    var valueList = mergedValue === null || mergedValue === undefined ? [] : Array.isArray(mergedValue) ? mergedValue : [mergedValue];\n    var _valueList = _slicedToArray(valueList, 1),\n      _valueList$ = _valueList[0],\n      val0 = _valueList$ === void 0 ? mergedMin : _valueList$;\n    var returnValues = mergedValue === null ? [] : [val0];\n\n    // Format as range\n    if (rangeEnabled) {\n      returnValues = _toConsumableArray(valueList);\n\n      // When count provided or value is `undefined`, we fill values\n      if (count || mergedValue === undefined) {\n        var pointCount = count >= 0 ? count + 1 : 2;\n        returnValues = returnValues.slice(0, pointCount);\n\n        // Fill with count\n        while (returnValues.length < pointCount) {\n          var _returnValues;\n          returnValues.push((_returnValues = returnValues[returnValues.length - 1]) !== null && _returnValues !== void 0 ? _returnValues : mergedMin);\n        }\n      }\n      returnValues.sort(function (a, b) {\n        return a - b;\n      });\n    }\n\n    // Align in range\n    returnValues.forEach(function (val, index) {\n      returnValues[index] = formatValue(val);\n    });\n    return returnValues;\n  }, [mergedValue, rangeEnabled, mergedMin, count, formatValue]);\n\n  // =========================== onChange ===========================\n  var getTriggerValue = function getTriggerValue(triggerValues) {\n    return rangeEnabled ? triggerValues : triggerValues[0];\n  };\n  var triggerChange = useEvent(function (nextValues) {\n    // Order first\n    var cloneNextValues = _toConsumableArray(nextValues).sort(function (a, b) {\n      return a - b;\n    });\n\n    // Trigger event if needed\n    if (onChange && !isEqual(cloneNextValues, rawValues, true)) {\n      onChange(getTriggerValue(cloneNextValues));\n    }\n\n    // We set this later since it will re-render component immediately\n    setValue(cloneNextValues);\n  });\n  var finishChange = useEvent(function (draggingDelete) {\n    // Trigger from `useDrag` will tell if it's a delete action\n    if (draggingDelete) {\n      handlesRef.current.hideHelp();\n    }\n    var finishValue = getTriggerValue(rawValues);\n    onAfterChange === null || onAfterChange === void 0 || onAfterChange(finishValue);\n    warning(!onAfterChange, '[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead.');\n    onChangeComplete === null || onChangeComplete === void 0 || onChangeComplete(finishValue);\n  });\n  var onDelete = function onDelete(index) {\n    if (disabled || !rangeEditable || rawValues.length <= minCount) {\n      return;\n    }\n    var cloneNextValues = _toConsumableArray(rawValues);\n    cloneNextValues.splice(index, 1);\n    onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(cloneNextValues));\n    triggerChange(cloneNextValues);\n    var nextFocusIndex = Math.max(0, index - 1);\n    handlesRef.current.hideHelp();\n    handlesRef.current.focus(nextFocusIndex);\n  };\n  var _useDrag = useDrag(containerRef, direction, rawValues, mergedMin, mergedMax, formatValue, triggerChange, finishChange, offsetValues, rangeEditable, minCount),\n    _useDrag2 = _slicedToArray(_useDrag, 5),\n    draggingIndex = _useDrag2[0],\n    draggingValue = _useDrag2[1],\n    draggingDelete = _useDrag2[2],\n    cacheValues = _useDrag2[3],\n    onStartDrag = _useDrag2[4];\n\n  /**\n   * When `rangeEditable` will insert a new value in the values array.\n   * Else it will replace the value in the values array.\n   */\n  var changeToCloseValue = function changeToCloseValue(newValue, e) {\n    if (!disabled) {\n      // Create new values\n      var cloneNextValues = _toConsumableArray(rawValues);\n      var valueIndex = 0;\n      var valueBeforeIndex = 0; // Record the index which value < newValue\n      var valueDist = mergedMax - mergedMin;\n      rawValues.forEach(function (val, index) {\n        var dist = Math.abs(newValue - val);\n        if (dist <= valueDist) {\n          valueDist = dist;\n          valueIndex = index;\n        }\n        if (val < newValue) {\n          valueBeforeIndex = index;\n        }\n      });\n      var focusIndex = valueIndex;\n      if (rangeEditable && valueDist !== 0 && (!maxCount || rawValues.length < maxCount)) {\n        cloneNextValues.splice(valueBeforeIndex + 1, 0, newValue);\n        focusIndex = valueBeforeIndex + 1;\n      } else {\n        cloneNextValues[valueIndex] = newValue;\n      }\n\n      // Fill value to match default 2 (only when `rawValues` is empty)\n      if (rangeEnabled && !rawValues.length && count === undefined) {\n        cloneNextValues.push(newValue);\n      }\n      var nextValue = getTriggerValue(cloneNextValues);\n      onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(nextValue);\n      triggerChange(cloneNextValues);\n      if (e) {\n        var _document$activeEleme, _document$activeEleme2;\n        (_document$activeEleme = document.activeElement) === null || _document$activeEleme === void 0 || (_document$activeEleme2 = _document$activeEleme.blur) === null || _document$activeEleme2 === void 0 || _document$activeEleme2.call(_document$activeEleme);\n        handlesRef.current.focus(focusIndex);\n        onStartDrag(e, focusIndex, cloneNextValues);\n      } else {\n        // https://github.com/ant-design/ant-design/issues/49997\n        onAfterChange === null || onAfterChange === void 0 || onAfterChange(nextValue);\n        warning(!onAfterChange, '[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead.');\n        onChangeComplete === null || onChangeComplete === void 0 || onChangeComplete(nextValue);\n      }\n    }\n  };\n\n  // ============================ Click =============================\n  var onSliderMouseDown = function onSliderMouseDown(e) {\n    e.preventDefault();\n    var _containerRef$current = containerRef.current.getBoundingClientRect(),\n      width = _containerRef$current.width,\n      height = _containerRef$current.height,\n      left = _containerRef$current.left,\n      top = _containerRef$current.top,\n      bottom = _containerRef$current.bottom,\n      right = _containerRef$current.right;\n    var clientX = e.clientX,\n      clientY = e.clientY;\n    var percent;\n    switch (direction) {\n      case 'btt':\n        percent = (bottom - clientY) / height;\n        break;\n      case 'ttb':\n        percent = (clientY - top) / height;\n        break;\n      case 'rtl':\n        percent = (right - clientX) / width;\n        break;\n      default:\n        percent = (clientX - left) / width;\n    }\n    var nextValue = mergedMin + percent * (mergedMax - mergedMin);\n    changeToCloseValue(formatValue(nextValue), e);\n  };\n\n  // =========================== Keyboard ===========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    keyboardValue = _React$useState2[0],\n    setKeyboardValue = _React$useState2[1];\n  var onHandleOffsetChange = function onHandleOffsetChange(offset, valueIndex) {\n    if (!disabled) {\n      var next = offsetValues(rawValues, offset, valueIndex);\n      onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(rawValues));\n      triggerChange(next.values);\n      setKeyboardValue(next.value);\n    }\n  };\n  React.useEffect(function () {\n    if (keyboardValue !== null) {\n      var valueIndex = rawValues.indexOf(keyboardValue);\n      if (valueIndex >= 0) {\n        handlesRef.current.focus(valueIndex);\n      }\n    }\n    setKeyboardValue(null);\n  }, [keyboardValue]);\n\n  // ============================= Drag =============================\n  var mergedDraggableTrack = React.useMemo(function () {\n    if (rangeDraggableTrack && mergedStep === null) {\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, '`draggableTrack` is not supported when `step` is `null`.');\n      }\n      return false;\n    }\n    return rangeDraggableTrack;\n  }, [rangeDraggableTrack, mergedStep]);\n  var onStartMove = useEvent(function (e, valueIndex) {\n    onStartDrag(e, valueIndex);\n    onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(rawValues));\n  });\n\n  // Auto focus for updated handle\n  var dragging = draggingIndex !== -1;\n  React.useEffect(function () {\n    if (!dragging) {\n      var valueIndex = rawValues.lastIndexOf(draggingValue);\n      handlesRef.current.focus(valueIndex);\n    }\n  }, [dragging]);\n\n  // =========================== Included ===========================\n  var sortedCacheValues = React.useMemo(function () {\n    return _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n  }, [cacheValues]);\n\n  // Provide a range values with included [min, max]\n  // Used for Track, Mark & Dot\n  var _React$useMemo = React.useMemo(function () {\n      if (!rangeEnabled) {\n        return [mergedMin, sortedCacheValues[0]];\n      }\n      return [sortedCacheValues[0], sortedCacheValues[sortedCacheValues.length - 1]];\n    }, [sortedCacheValues, rangeEnabled, mergedMin]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    includedStart = _React$useMemo2[0],\n    includedEnd = _React$useMemo2[1];\n\n  // ============================= Refs =============================\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        handlesRef.current.focus(0);\n      },\n      blur: function blur() {\n        var _containerRef$current2;\n        var _document = document,\n          activeElement = _document.activeElement;\n        if ((_containerRef$current2 = containerRef.current) !== null && _containerRef$current2 !== void 0 && _containerRef$current2.contains(activeElement)) {\n          activeElement === null || activeElement === void 0 || activeElement.blur();\n        }\n      }\n    };\n  });\n\n  // ========================== Auto Focus ==========================\n  React.useEffect(function () {\n    if (autoFocus) {\n      handlesRef.current.focus(0);\n    }\n  }, []);\n\n  // =========================== Context ============================\n  var context = React.useMemo(function () {\n    return {\n      min: mergedMin,\n      max: mergedMax,\n      direction: direction,\n      disabled: disabled,\n      keyboard: keyboard,\n      step: mergedStep,\n      included: included,\n      includedStart: includedStart,\n      includedEnd: includedEnd,\n      range: rangeEnabled,\n      tabIndex: tabIndex,\n      ariaLabelForHandle: ariaLabelForHandle,\n      ariaLabelledByForHandle: ariaLabelledByForHandle,\n      ariaRequired: ariaRequired,\n      ariaValueTextFormatterForHandle: ariaValueTextFormatterForHandle,\n      styles: styles || {},\n      classNames: classNames || {}\n    };\n  }, [mergedMin, mergedMax, direction, disabled, keyboard, mergedStep, included, includedStart, includedEnd, rangeEnabled, tabIndex, ariaLabelForHandle, ariaLabelledByForHandle, ariaRequired, ariaValueTextFormatterForHandle, styles, classNames]);\n\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(SliderContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: containerRef,\n    className: cls(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled), \"\".concat(prefixCls, \"-vertical\"), vertical), \"\".concat(prefixCls, \"-horizontal\"), !vertical), \"\".concat(prefixCls, \"-with-marks\"), markList.length)),\n    style: style,\n    onMouseDown: onSliderMouseDown,\n    id: id\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: cls(\"\".concat(prefixCls, \"-rail\"), classNames === null || classNames === void 0 ? void 0 : classNames.rail),\n    style: _objectSpread(_objectSpread({}, railStyle), styles === null || styles === void 0 ? void 0 : styles.rail)\n  }), track !== false && /*#__PURE__*/React.createElement(Tracks, {\n    prefixCls: prefixCls,\n    style: trackStyle,\n    values: rawValues,\n    startPoint: startPoint,\n    onStartMove: mergedDraggableTrack ? onStartMove : undefined\n  }), /*#__PURE__*/React.createElement(Steps, {\n    prefixCls: prefixCls,\n    marks: markList,\n    dots: dots,\n    style: dotStyle,\n    activeStyle: activeDotStyle\n  }), /*#__PURE__*/React.createElement(Handles, {\n    ref: handlesRef,\n    prefixCls: prefixCls,\n    style: handleStyle,\n    values: cacheValues,\n    draggingIndex: draggingIndex,\n    draggingDelete: draggingDelete,\n    onStartMove: onStartMove,\n    onOffsetChange: onHandleOffsetChange,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    handleRender: handleRender,\n    activeHandleRender: activeHandleRender,\n    onChangeComplete: finishChange,\n    onDelete: rangeEditable ? onDelete : undefined\n  }), /*#__PURE__*/React.createElement(Marks, {\n    prefixCls: prefixCls,\n    marks: markList,\n    onClick: changeToCloseValue\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Slider.displayName = 'Slider';\n}\nexport default Slider;"], "names": [], "mappings": ";;;AA4VU;AA5VV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AAEA;;;;;;;;;;;;CAYC,GAED,IAAI,SAAS,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IAC7D,IAAI,mBAAmB,MAAM,SAAS,EACpC,YAAY,qBAAqB,KAAK,IAAI,cAAc,kBACxD,YAAY,MAAM,SAAS,EAC3B,QAAQ,MAAM,KAAK,EACnB,aAAa,MAAM,UAAU,EAC7B,SAAS,MAAM,MAAM,EACrB,KAAK,MAAM,EAAE,EACb,kBAAkB,MAAM,QAAQ,EAChC,WAAW,oBAAoB,KAAK,IAAI,QAAQ,iBAChD,kBAAkB,MAAM,QAAQ,EAChC,WAAW,oBAAoB,KAAK,IAAI,OAAO,iBAC/C,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,SAAS,MAAM,MAAM,EACrB,aAAa,MAAM,GAAG,EACtB,MAAM,eAAe,KAAK,IAAI,IAAI,YAClC,aAAa,MAAM,GAAG,EACtB,MAAM,eAAe,KAAK,IAAI,MAAM,YACpC,cAAc,MAAM,IAAI,EACxB,OAAO,gBAAgB,KAAK,IAAI,IAAI,aACpC,QAAQ,MAAM,KAAK,EACnB,eAAe,MAAM,YAAY,EACjC,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,KAAK,EACnB,WAAW,MAAM,QAAQ,EACzB,iBAAiB,MAAM,cAAc,EACrC,gBAAgB,MAAM,aAAa,EACnC,mBAAmB,MAAM,gBAAgB,EACzC,oBAAoB,MAAM,UAAU,EACpC,aAAa,sBAAsB,KAAK,IAAI,OAAO,mBACnD,kBAAkB,MAAM,QAAQ,EAChC,WAAW,oBAAoB,KAAK,IAAI,QAAQ,iBAChD,UAAU,MAAM,OAAO,EACvB,WAAW,MAAM,QAAQ,EACzB,kBAAkB,MAAM,QAAQ,EAChC,WAAW,oBAAoB,KAAK,IAAI,OAAO,iBAC/C,aAAa,MAAM,UAAU,EAC7B,aAAa,MAAM,UAAU,EAC7B,cAAc,MAAM,WAAW,EAC/B,YAAY,MAAM,SAAS,EAC3B,WAAW,MAAM,QAAQ,EACzB,iBAAiB,MAAM,cAAc,EACrC,QAAQ,MAAM,KAAK,EACnB,OAAO,MAAM,IAAI,EACjB,eAAe,MAAM,YAAY,EACjC,qBAAqB,MAAM,kBAAkB,EAC7C,QAAQ,MAAM,KAAK,EACnB,kBAAkB,MAAM,QAAQ,EAChC,WAAW,oBAAoB,KAAK,IAAI,IAAI,iBAC5C,qBAAqB,MAAM,kBAAkB,EAC7C,0BAA0B,MAAM,uBAAuB,EACvD,eAAe,MAAM,YAAY,EACjC,kCAAkC,MAAM,+BAA+B;IACzE,IAAI,aAAa,6JAAA,CAAA,SAAY,CAAC;IAC9B,IAAI,eAAe,6JAAA,CAAA,SAAY,CAAC;IAChC,IAAI,YAAY,6JAAA,CAAA,UAAa;qCAAC;YAC5B,IAAI,UAAU;gBACZ,OAAO,UAAU,QAAQ;YAC3B;YACA,OAAO,UAAU,QAAQ;QAC3B;oCAAG;QAAC;QAAS;KAAS;IAEtB,mEAAmE;IACnE,IAAI,YAAY,CAAA,GAAA,0JAAA,CAAA,UAAQ,AAAD,EAAE,QACvB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACvC,eAAe,UAAU,CAAC,EAAE,EAC5B,gBAAgB,UAAU,CAAC,EAAE,EAC7B,sBAAsB,UAAU,CAAC,EAAE,EACnC,WAAW,UAAU,CAAC,EAAE,EACxB,WAAW,UAAU,CAAC,EAAE;IAC1B,IAAI,YAAY,6JAAA,CAAA,UAAa;qCAAC;YAC5B,OAAO,SAAS,OAAO,MAAM;QAC/B;oCAAG;QAAC;KAAI;IACR,IAAI,YAAY,6JAAA,CAAA,UAAa;qCAAC;YAC5B,OAAO,SAAS,OAAO,MAAM;QAC/B;oCAAG;QAAC;KAAI;IAER,mEAAmE;IACnE,IAAI,aAAa,6JAAA,CAAA,UAAa;sCAAC;YAC7B,OAAO,SAAS,QAAQ,QAAQ,IAAI,IAAI;QAC1C;qCAAG;QAAC;KAAK;IAET,mEAAmE;IACnE,IAAI,aAAa,6JAAA,CAAA,UAAa;sCAAC;YAC7B,IAAI,OAAO,aAAa,WAAW;gBACjC,OAAO,WAAW,aAAa;YACjC;YACA,OAAO,YAAY,IAAI,WAAW;QACpC;qCAAG;QAAC;QAAU;KAAW;IAEzB,mEAAmE;IACnE,IAAI,WAAW,6JAAA,CAAA,UAAa;oCAAC;YAC3B,OAAO,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG;4CAAC,SAAU,GAAG;oBAC/C,IAAI,OAAO,KAAK,CAAC,IAAI;oBACrB,IAAI,UAAU;wBACZ,OAAO,OAAO;oBAChB;oBACA,IAAI,QAAQ,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,YAAY,CAAE,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,SAAS,CAAC,WAAW,QAAQ,WAAW,IAAI,GAAG;wBAC3H,QAAQ,KAAK,GAAG,KAAK,KAAK;wBAC1B,QAAQ,KAAK,GAAG,KAAK,KAAK;oBAC5B,OAAO;wBACL,QAAQ,KAAK,GAAG;oBAClB;oBACA,OAAO;gBACT;2CAAG,MAAM;4CAAC,SAAU,IAAI;oBACtB,IAAI,QAAQ,KAAK,KAAK;oBACtB,OAAO,SAAS,OAAO,UAAU;gBACnC;2CAAG,IAAI;4CAAC,SAAU,CAAC,EAAE,CAAC;oBACpB,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC1B;;QACF;mCAAG;QAAC;KAAM;IAEV,mEAAmE;IACnE,IAAI,aAAa,CAAA,GAAA,2JAAA,CAAA,UAAS,AAAD,EAAE,WAAW,WAAW,YAAY,UAAU,YAAY,aACjF,cAAc,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACzC,cAAc,WAAW,CAAC,EAAE,EAC5B,eAAe,WAAW,CAAC,EAAE;IAE/B,mEAAmE;IACnE,IAAI,kBAAkB,CAAA,GAAA,8JAAA,CAAA,UAAc,AAAD,EAAE,cAAc;QAC/C,OAAO;IACT,IACA,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,cAAc,gBAAgB,CAAC,EAAE,EACjC,WAAW,gBAAgB,CAAC,EAAE;IAChC,IAAI,YAAY,6JAAA,CAAA,UAAa;qCAAC;YAC5B,IAAI,YAAY,gBAAgB,QAAQ,gBAAgB,YAAY,EAAE,GAAG,MAAM,OAAO,CAAC,eAAe,cAAc;gBAAC;aAAY;YACjI,IAAI,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACzC,cAAc,UAAU,CAAC,EAAE,EAC3B,OAAO,gBAAgB,KAAK,IAAI,YAAY;YAC9C,IAAI,eAAe,gBAAgB,OAAO,EAAE,GAAG;gBAAC;aAAK;YAErD,kBAAkB;YAClB,IAAI,cAAc;gBAChB,eAAe,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;gBAElC,8DAA8D;gBAC9D,IAAI,SAAS,gBAAgB,WAAW;oBACtC,IAAI,aAAa,SAAS,IAAI,QAAQ,IAAI;oBAC1C,eAAe,aAAa,KAAK,CAAC,GAAG;oBAErC,kBAAkB;oBAClB,MAAO,aAAa,MAAM,GAAG,WAAY;wBACvC,IAAI;wBACJ,aAAa,IAAI,CAAC,CAAC,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,MAAM,QAAQ,kBAAkB,KAAK,IAAI,gBAAgB;oBACnI;gBACF;gBACA,aAAa,IAAI;iDAAC,SAAU,CAAC,EAAE,CAAC;wBAC9B,OAAO,IAAI;oBACb;;YACF;YAEA,iBAAiB;YACjB,aAAa,OAAO;6CAAC,SAAU,GAAG,EAAE,KAAK;oBACvC,YAAY,CAAC,MAAM,GAAG,YAAY;gBACpC;;YACA,OAAO;QACT;oCAAG;QAAC;QAAa;QAAc;QAAW;QAAO;KAAY;IAE7D,mEAAmE;IACnE,IAAI,kBAAkB,SAAS,gBAAgB,aAAa;QAC1D,OAAO,eAAe,gBAAgB,aAAa,CAAC,EAAE;IACxD;IACA,IAAI,gBAAgB,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD;0CAAE,SAAU,UAAU;YAC/C,cAAc;YACd,IAAI,kBAAkB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,YAAY,IAAI;kEAAC,SAAU,CAAC,EAAE,CAAC;oBACtE,OAAO,IAAI;gBACb;;YAEA,0BAA0B;YAC1B,IAAI,YAAY,CAAC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,WAAW,OAAO;gBAC1D,SAAS,gBAAgB;YAC3B;YAEA,kEAAkE;YAClE,SAAS;QACX;;IACA,IAAI,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD;yCAAE,SAAU,cAAc;YAClD,2DAA2D;YAC3D,IAAI,gBAAgB;gBAClB,WAAW,OAAO,CAAC,QAAQ;YAC7B;YACA,IAAI,cAAc,gBAAgB;YAClC,kBAAkB,QAAQ,kBAAkB,KAAK,KAAK,cAAc;YACpE,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,eAAe;YACxB,qBAAqB,QAAQ,qBAAqB,KAAK,KAAK,iBAAiB;QAC/E;;IACA,IAAI,WAAW,SAAS,SAAS,KAAK;QACpC,IAAI,YAAY,CAAC,iBAAiB,UAAU,MAAM,IAAI,UAAU;YAC9D;QACF;QACA,IAAI,kBAAkB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;QACzC,gBAAgB,MAAM,CAAC,OAAO;QAC9B,mBAAmB,QAAQ,mBAAmB,KAAK,KAAK,eAAe,gBAAgB;QACvF,cAAc;QACd,IAAI,iBAAiB,KAAK,GAAG,CAAC,GAAG,QAAQ;QACzC,WAAW,OAAO,CAAC,QAAQ;QAC3B,WAAW,OAAO,CAAC,KAAK,CAAC;IAC3B;IACA,IAAI,WAAW,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,cAAc,WAAW,WAAW,WAAW,WAAW,aAAa,eAAe,cAAc,cAAc,eAAe,WACtJ,YAAY,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,UAAU,IACrC,gBAAgB,SAAS,CAAC,EAAE,EAC5B,gBAAgB,SAAS,CAAC,EAAE,EAC5B,iBAAiB,SAAS,CAAC,EAAE,EAC7B,cAAc,SAAS,CAAC,EAAE,EAC1B,cAAc,SAAS,CAAC,EAAE;IAE5B;;;GAGC,GACD,IAAI,qBAAqB,SAAS,mBAAmB,QAAQ,EAAE,CAAC;QAC9D,IAAI,CAAC,UAAU;YACb,oBAAoB;YACpB,IAAI,kBAAkB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;YACzC,IAAI,aAAa;YACjB,IAAI,mBAAmB,GAAG,0CAA0C;YACpE,IAAI,YAAY,YAAY;YAC5B,UAAU,OAAO,CAAC,SAAU,GAAG,EAAE,KAAK;gBACpC,IAAI,OAAO,KAAK,GAAG,CAAC,WAAW;gBAC/B,IAAI,QAAQ,WAAW;oBACrB,YAAY;oBACZ,aAAa;gBACf;gBACA,IAAI,MAAM,UAAU;oBAClB,mBAAmB;gBACrB;YACF;YACA,IAAI,aAAa;YACjB,IAAI,iBAAiB,cAAc,KAAK,CAAC,CAAC,YAAY,UAAU,MAAM,GAAG,QAAQ,GAAG;gBAClF,gBAAgB,MAAM,CAAC,mBAAmB,GAAG,GAAG;gBAChD,aAAa,mBAAmB;YAClC,OAAO;gBACL,eAAe,CAAC,WAAW,GAAG;YAChC;YAEA,iEAAiE;YACjE,IAAI,gBAAgB,CAAC,UAAU,MAAM,IAAI,UAAU,WAAW;gBAC5D,gBAAgB,IAAI,CAAC;YACvB;YACA,IAAI,YAAY,gBAAgB;YAChC,mBAAmB,QAAQ,mBAAmB,KAAK,KAAK,eAAe;YACvE,cAAc;YACd,IAAI,GAAG;gBACL,IAAI,uBAAuB;gBAC3B,CAAC,wBAAwB,SAAS,aAAa,MAAM,QAAQ,0BAA0B,KAAK,KAAK,CAAC,yBAAyB,sBAAsB,IAAI,MAAM,QAAQ,2BAA2B,KAAK,KAAK,uBAAuB,IAAI,CAAC;gBACpO,WAAW,OAAO,CAAC,KAAK,CAAC;gBACzB,YAAY,GAAG,YAAY;YAC7B,OAAO;gBACL,wDAAwD;gBACxD,kBAAkB,QAAQ,kBAAkB,KAAK,KAAK,cAAc;gBACpE,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,eAAe;gBACxB,qBAAqB,QAAQ,qBAAqB,KAAK,KAAK,iBAAiB;YAC/E;QACF;IACF;IAEA,mEAAmE;IACnE,IAAI,oBAAoB,SAAS,kBAAkB,CAAC;QAClD,EAAE,cAAc;QAChB,IAAI,wBAAwB,aAAa,OAAO,CAAC,qBAAqB,IACpE,QAAQ,sBAAsB,KAAK,EACnC,SAAS,sBAAsB,MAAM,EACrC,OAAO,sBAAsB,IAAI,EACjC,MAAM,sBAAsB,GAAG,EAC/B,SAAS,sBAAsB,MAAM,EACrC,QAAQ,sBAAsB,KAAK;QACrC,IAAI,UAAU,EAAE,OAAO,EACrB,UAAU,EAAE,OAAO;QACrB,IAAI;QACJ,OAAQ;YACN,KAAK;gBACH,UAAU,CAAC,SAAS,OAAO,IAAI;gBAC/B;YACF,KAAK;gBACH,UAAU,CAAC,UAAU,GAAG,IAAI;gBAC5B;YACF,KAAK;gBACH,UAAU,CAAC,QAAQ,OAAO,IAAI;gBAC9B;YACF;gBACE,UAAU,CAAC,UAAU,IAAI,IAAI;QACjC;QACA,IAAI,YAAY,YAAY,UAAU,CAAC,YAAY,SAAS;QAC5D,mBAAmB,YAAY,YAAY;IAC7C;IAEA,mEAAmE;IACnE,IAAI,kBAAkB,6JAAA,CAAA,WAAc,CAAC,OACnC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,gBAAgB,gBAAgB,CAAC,EAAE,EACnC,mBAAmB,gBAAgB,CAAC,EAAE;IACxC,IAAI,uBAAuB,SAAS,qBAAqB,MAAM,EAAE,UAAU;QACzE,IAAI,CAAC,UAAU;YACb,IAAI,OAAO,aAAa,WAAW,QAAQ;YAC3C,mBAAmB,QAAQ,mBAAmB,KAAK,KAAK,eAAe,gBAAgB;YACvF,cAAc,KAAK,MAAM;YACzB,iBAAiB,KAAK,KAAK;QAC7B;IACF;IACA,6JAAA,CAAA,YAAe;4BAAC;YACd,IAAI,kBAAkB,MAAM;gBAC1B,IAAI,aAAa,UAAU,OAAO,CAAC;gBACnC,IAAI,cAAc,GAAG;oBACnB,WAAW,OAAO,CAAC,KAAK,CAAC;gBAC3B;YACF;YACA,iBAAiB;QACnB;2BAAG;QAAC;KAAc;IAElB,mEAAmE;IACnE,IAAI,uBAAuB,6JAAA,CAAA,UAAa;gDAAC;YACvC,IAAI,uBAAuB,eAAe,MAAM;gBAC9C,wCAA2C;oBACzC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACjB;gBACA,OAAO;YACT;YACA,OAAO;QACT;+CAAG;QAAC;QAAqB;KAAW;IACpC,IAAI,cAAc,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD;wCAAE,SAAU,CAAC,EAAE,UAAU;YAChD,YAAY,GAAG;YACf,mBAAmB,QAAQ,mBAAmB,KAAK,KAAK,eAAe,gBAAgB;QACzF;;IAEA,gCAAgC;IAChC,IAAI,WAAW,kBAAkB,CAAC;IAClC,6JAAA,CAAA,YAAe;4BAAC;YACd,IAAI,CAAC,UAAU;gBACb,IAAI,aAAa,UAAU,WAAW,CAAC;gBACvC,WAAW,OAAO,CAAC,KAAK,CAAC;YAC3B;QACF;2BAAG;QAAC;KAAS;IAEb,mEAAmE;IACnE,IAAI,oBAAoB,6JAAA,CAAA,UAAa;6CAAC;YACpC,OAAO,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,aAAa,IAAI;qDAAC,SAAU,CAAC,EAAE,CAAC;oBACxD,OAAO,IAAI;gBACb;;QACF;4CAAG;QAAC;KAAY;IAEhB,kDAAkD;IAClD,6BAA6B;IAC7B,IAAI,iBAAiB,6JAAA,CAAA,UAAa;0CAAC;YAC/B,IAAI,CAAC,cAAc;gBACjB,OAAO;oBAAC;oBAAW,iBAAiB,CAAC,EAAE;iBAAC;YAC1C;YACA,OAAO;gBAAC,iBAAiB,CAAC,EAAE;gBAAE,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;aAAC;QAChF;yCAAG;QAAC;QAAmB;QAAc;KAAU,GAC/C,kBAAkB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,gBAAgB,IACjD,gBAAgB,eAAe,CAAC,EAAE,EAClC,cAAc,eAAe,CAAC,EAAE;IAElC,mEAAmE;IACnE,6JAAA,CAAA,sBAAyB,CAAC;sCAAK;YAC7B,OAAO;gBACL,OAAO,SAAS;oBACd,WAAW,OAAO,CAAC,KAAK,CAAC;gBAC3B;gBACA,MAAM,SAAS;oBACb,IAAI;oBACJ,IAAI,YAAY,UACd,gBAAgB,UAAU,aAAa;oBACzC,IAAI,CAAC,yBAAyB,aAAa,OAAO,MAAM,QAAQ,2BAA2B,KAAK,KAAK,uBAAuB,QAAQ,CAAC,gBAAgB;wBACnJ,kBAAkB,QAAQ,kBAAkB,KAAK,KAAK,cAAc,IAAI;oBAC1E;gBACF;YACF;QACF;;IAEA,mEAAmE;IACnE,6JAAA,CAAA,YAAe;4BAAC;YACd,IAAI,WAAW;gBACb,WAAW,OAAO,CAAC,KAAK,CAAC;YAC3B;QACF;2BAAG,EAAE;IAEL,mEAAmE;IACnE,IAAI,UAAU,6JAAA,CAAA,UAAa;mCAAC;YAC1B,OAAO;gBACL,KAAK;gBACL,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,oBAAoB;gBACpB,yBAAyB;gBACzB,cAAc;gBACd,iCAAiC;gBACjC,QAAQ,UAAU,CAAC;gBACnB,YAAY,cAAc,CAAC;YAC7B;QACF;kCAAG;QAAC;QAAW;QAAW;QAAW;QAAU;QAAU;QAAY;QAAU;QAAe;QAAa;QAAc;QAAU;QAAoB;QAAyB;QAAc;QAAiC;QAAQ;KAAW;IAElP,mEAAmE;IACnE,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,gJAAA,CAAA,UAAa,CAAC,QAAQ,EAAE;QAC9D,OAAO;IACT,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACzC,KAAK;QACL,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAG,AAAD,EAAE,WAAW,WAAW,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,cAAc,WAAW,GAAG,MAAM,CAAC,WAAW,cAAc,WAAW,GAAG,MAAM,CAAC,WAAW,gBAAgB,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,gBAAgB,SAAS,MAAM;QAC1S,OAAO;QACP,aAAa;QACb,IAAI;IACN,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACzC,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAG,AAAD,EAAE,GAAG,MAAM,CAAC,WAAW,UAAU,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,IAAI;QACrH,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,YAAY,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI;IAChH,IAAI,UAAU,SAAS,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,wJAAA,CAAA,UAAM,EAAE;QAC9D,WAAW;QACX,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,aAAa,uBAAuB,cAAc;IACpD,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,uJAAA,CAAA,UAAK,EAAE;QAC1C,WAAW;QACX,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,yJAAA,CAAA,UAAO,EAAE;QAC5C,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;QACR,eAAe;QACf,gBAAgB;QAChB,aAAa;QACb,gBAAgB;QAChB,SAAS;QACT,QAAQ;QACR,cAAc;QACd,oBAAoB;QACpB,kBAAkB;QAClB,UAAU,gBAAgB,WAAW;IACvC,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,uJAAA,CAAA,UAAK,EAAE;QAC1C,WAAW;QACX,OAAO;QACP,SAAS;IACX;AACF;AACA,wCAA2C;IACzC,OAAO,WAAW,GAAG;AACvB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1654, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/rc-slider/es/index.js"], "sourcesContent": ["import Slider from \"./Slider\";\nexport { UnstableContext } from \"./context\";\nexport default Slider;"], "names": [], "mappings": ";;;AAAA;AACA;;;uCACe,+IAAA,CAAA,UAAM", "ignoreList": [0], "debugId": null}}]}
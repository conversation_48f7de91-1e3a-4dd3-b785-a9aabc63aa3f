/* [project]/node_modules/react-pdf/dist/esm/Page/TextLayer.css [app-client] (css) */
:root {
  --react-pdf-text-layer: 1;
  --highlight-bg-color: #b400aa;
  --highlight-selected-bg-color: #006400;
}

@media screen and (forced-colors: active) {
  :root {
    --highlight-bg-color: Highlight;
    --highlight-selected-bg-color: ButtonText;
  }
}

[data-main-rotation="90"] {
  transform: rotate(90deg)translateY(-100%);
}

[data-main-rotation="180"] {
  transform: rotate(180deg)translate(-100%, -100%);
}

[data-main-rotation="270"] {
  transform: rotate(270deg)translateX(-100%);
}

.textLayer {
  text-align: initial;
  -moz-text-size-adjust: none;
  text-size-adjust: none;
  forced-color-adjust: none;
  transform-origin: 0 0;
  z-index: 2;
  line-height: 1;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.textLayer :-webkit-any(span, br) {
  color: rgba(0, 0, 0, 0);
  white-space: pre;
  cursor: text;
  transform-origin: 0 0;
  margin: 0;
  position: absolute;
}

.textLayer :-moz-any(span, br) {
  color: rgba(0, 0, 0, 0);
  white-space: pre;
  cursor: text;
  transform-origin: 0 0;
  margin: 0;
  position: absolute;
}

.textLayer :is(span, br) {
  color: rgba(0, 0, 0, 0);
  white-space: pre;
  cursor: text;
  transform-origin: 0 0;
  margin: 0;
  position: absolute;
}

.textLayer span.markedContent {
  height: 0;
  top: 0;
}

.textLayer .highlight {
  background-color: var(--highlight-bg-color);
  border-radius: 4px;
  margin: -1px;
  padding: 1px;
}

.textLayer .highlight.appended {
  position: initial;
}

.textLayer .highlight.begin {
  border-radius: 4px 0 0 4px;
}

.textLayer .highlight.end {
  border-radius: 0 4px 4px 0;
}

.textLayer .highlight.middle {
  border-radius: 0;
}

.textLayer .highlight.selected {
  background-color: var(--highlight-selected-bg-color);
}

.textLayer br::selection {
  background: none;
}

.textLayer .endOfContent {
  z-index: -1;
  cursor: default;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  display: block;
  position: absolute;
  top: 100%;
  bottom: 0;
  left: 0;
  right: 0;
}

.textLayer .endOfContent.active {
  top: 0;
}

.hiddenCanvasElement {
  width: 0;
  height: 0;
  display: none;
  position: absolute;
  top: 0;
  left: 0;
}

/*# sourceMappingURL=node_modules_react-pdf_dist_esm_Page_TextLayer_css_e59ae46c._.single.css.map*/
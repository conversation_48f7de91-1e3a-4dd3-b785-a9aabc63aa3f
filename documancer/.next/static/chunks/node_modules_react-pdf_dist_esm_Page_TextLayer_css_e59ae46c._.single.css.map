{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/react-pdf/dist/esm/Page/TextLayer.css"], "sourcesContent": ["/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n:root {\n  --react-pdf-text-layer: 1;\n  --highlight-bg-color: rgba(180, 0, 170, 1);\n  --highlight-selected-bg-color: rgba(0, 100, 0, 1);\n}\n\n@media screen and (forced-colors: active) {\n  :root {\n    --highlight-bg-color: Highlight;\n    --highlight-selected-bg-color: ButtonText;\n  }\n}\n\n[data-main-rotation='90'] {\n  transform: rotate(90deg) translateY(-100%);\n}\n[data-main-rotation='180'] {\n  transform: rotate(180deg) translate(-100%, -100%);\n}\n[data-main-rotation='270'] {\n  transform: rotate(270deg) translateX(-100%);\n}\n\n.textLayer {\n  position: absolute;\n  text-align: initial;\n  inset: 0;\n  overflow: hidden;\n  line-height: 1;\n  text-size-adjust: none;\n  forced-color-adjust: none;\n  transform-origin: 0 0;\n  z-index: 2;\n}\n\n.textLayer :is(span, br) {\n  color: transparent;\n  position: absolute;\n  white-space: pre;\n  cursor: text;\n  margin: 0;\n  transform-origin: 0 0;\n}\n\n/* Only necessary in Google Chrome, see issue 14205, and most unfortunately\n * the problem doesn't show up in \"text\" reference tests. */\n.textLayer span.markedContent {\n  top: 0;\n  height: 0;\n}\n\n.textLayer .highlight {\n  margin: -1px;\n  padding: 1px;\n  background-color: var(--highlight-bg-color);\n  border-radius: 4px;\n}\n\n.textLayer .highlight.appended {\n  position: initial;\n}\n\n.textLayer .highlight.begin {\n  border-radius: 4px 0 0 4px;\n}\n\n.textLayer .highlight.end {\n  border-radius: 0 4px 4px 0;\n}\n\n.textLayer .highlight.middle {\n  border-radius: 0;\n}\n\n.textLayer .highlight.selected {\n  background-color: var(--highlight-selected-bg-color);\n}\n\n/* Avoids https://github.com/mozilla/pdf.js/issues/13840 in Chrome */\n.textLayer br::selection {\n  background: transparent;\n}\n\n.textLayer .endOfContent {\n  display: block;\n  position: absolute;\n  inset: 100% 0 0;\n  z-index: -1;\n  cursor: default;\n  user-select: none;\n}\n\n.textLayer .endOfContent.active {\n  top: 0;\n}\n\n.hiddenCanvasElement {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 0;\n  height: 0;\n  display: none;\n}\n"], "names": [], "mappings": "AAeA;;;;;;AAMA;EACE;;;;;;AAMF;;;;AAGA;;;;AAGA;;;;AAIA;;;;;;;;;;;;;;;;AAYA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAWA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;AASA;;;;AAIA", "ignoreList": [0]}}]}
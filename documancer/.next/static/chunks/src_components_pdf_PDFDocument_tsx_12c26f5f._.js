(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/pdf/PDFDocument.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_pdfjs-dist_build_pdf_mjs_b3b2e714._.js",
  "static/chunks/node_modules_pdfjs-dist_build_pdf_mjs_39193cb1._.js",
  "static/chunks/node_modules_react-pdf_dist_esm_31efdb39._.js",
  "static/chunks/node_modules_8a6f8443._.js",
  "static/chunks/src_ae3b111f._.js",
  {
    "path": "static/chunks/node_modules_react-pdf_dist_esm_Page_cf9568f1._.css",
    "included": [
      "[project]/node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css [app-client] (css)",
      "[project]/node_modules/react-pdf/dist/esm/Page/TextLayer.css [app-client] (css)"
    ],
    "moduleChunks": [
      "static/chunks/node_modules_react-pdf_dist_esm_Page_AnnotationLayer_css_e59ae46c._.single.css",
      "static/chunks/node_modules_react-pdf_dist_esm_Page_TextLayer_css_e59ae46c._.single.css"
    ]
  },
  "static/chunks/src_components_pdf_PDFDocument_tsx_f9fe2fc7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/pdf/PDFDocument.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);
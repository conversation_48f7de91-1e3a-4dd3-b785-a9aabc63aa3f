{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40ant-design/icons/es/components/Icon.js"], "sourcesContent": ["function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n// Seems this is used for iconFont\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useComposeRef } from \"@rc-component/util/es/ref\";\nimport Context from \"./Context\";\nimport { svgBaseProps, warning, useInsertStyles } from \"../utils\";\nconst Icon = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    // affect outter <i>...</i>\n    className,\n    // affect inner <svg>...</svg>\n    component: Component,\n    viewBox,\n    spin,\n    rotate,\n    tabIndex,\n    onClick,\n    // children\n    children,\n    ...restProps\n  } = props;\n  const iconRef = React.useRef();\n  const mergedRef = useComposeRef(iconRef, ref);\n  warning(Boolean(Component || children), 'Should have `component` prop or `children`.');\n  useInsertStyles(iconRef);\n  const {\n    prefixCls = 'anticon',\n    rootClassName\n  } = React.useContext(Context);\n  const classString = classNames(rootClassName, prefixCls, {\n    [`${prefixCls}-spin`]: !!spin && !!Component\n  }, className);\n  const svgClassString = classNames({\n    [`${prefixCls}-spin`]: !!spin\n  });\n  const svgStyle = rotate ? {\n    msTransform: `rotate(${rotate}deg)`,\n    transform: `rotate(${rotate}deg)`\n  } : undefined;\n  const innerSvgProps = {\n    ...svgBaseProps,\n    className: svgClassString,\n    style: svgStyle,\n    viewBox\n  };\n  if (!viewBox) {\n    delete innerSvgProps.viewBox;\n  }\n\n  // component > children\n  const renderInnerNode = () => {\n    if (Component) {\n      return /*#__PURE__*/React.createElement(Component, innerSvgProps, children);\n    }\n    if (children) {\n      warning(Boolean(viewBox) || React.Children.count(children) === 1 && /*#__PURE__*/React.isValidElement(children) && React.Children.only(children).type === 'use', 'Make sure that you provide correct `viewBox`' + ' prop (default `0 0 1024 1024`) to the icon.');\n      return /*#__PURE__*/React.createElement(\"svg\", _extends({}, innerSvgProps, {\n        viewBox: viewBox\n      }), children);\n    }\n    return null;\n  };\n  let iconTabIndex = tabIndex;\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n  return /*#__PURE__*/React.createElement(\"span\", _extends({\n    role: \"img\"\n  }, restProps, {\n    ref: mergedRef,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), renderInnerNode());\n});\nIcon.displayName = 'AntdIcon';\nexport default Icon;"], "names": [], "mappings": ";;;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AANA,SAAS;IAAa,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK;IAAkO,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAAY;;;;;;AAOlV,MAAM,OAAO,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,CAAC,OAAO;IACjD,MAAM,EACJ,2BAA2B;IAC3B,SAAS,EACT,8BAA8B;IAC9B,WAAW,SAAS,EACpB,OAAO,EACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,OAAO,EACP,WAAW;IACX,QAAQ,EACR,GAAG,WACJ,GAAG;IACJ,MAAM,UAAU,6JAAA,CAAA,SAAY;IAC5B,MAAM,YAAY,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;IACzC,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,aAAa,WAAW;IACxC,CAAA,GAAA,0JAAA,CAAA,kBAAe,AAAD,EAAE;IAChB,MAAM,EACJ,YAAY,SAAS,EACrB,aAAa,EACd,GAAG,6JAAA,CAAA,aAAgB,CAAC,0KAAA,CAAA,UAAO;IAC5B,MAAM,cAAc,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,eAAe,WAAW;QACvD,CAAC,AAAC,GAAY,OAAV,WAAU,SAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;IACrC,GAAG;IACH,MAAM,iBAAiB,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE;QAChC,CAAC,AAAC,GAAY,OAAV,WAAU,SAAO,EAAE,CAAC,CAAC;IAC3B;IACA,MAAM,WAAW,SAAS;QACxB,aAAa,AAAC,UAAgB,OAAP,QAAO;QAC9B,WAAW,AAAC,UAAgB,OAAP,QAAO;IAC9B,IAAI;IACJ,MAAM,gBAAgB;QACpB,GAAG,0JAAA,CAAA,eAAY;QACf,WAAW;QACX,OAAO;QACP;IACF;IACA,IAAI,CAAC,SAAS;QACZ,OAAO,cAAc,OAAO;IAC9B;IAEA,uBAAuB;IACvB,MAAM,kBAAkB;QACtB,IAAI,WAAW;YACb,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,WAAW,eAAe;QACpE;QACA,IAAI,UAAU;YACZ,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,YAAY,6JAAA,CAAA,WAAc,CAAC,KAAK,CAAC,cAAc,KAAK,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,aAAa,6JAAA,CAAA,WAAc,CAAC,IAAI,CAAC,UAAU,IAAI,KAAK,OAAO,iDAAiD;YAClN,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,SAAS,CAAC,GAAG,eAAe;gBACzE,SAAS;YACX,IAAI;QACN;QACA,OAAO;IACT;IACA,IAAI,eAAe;IACnB,IAAI,iBAAiB,aAAa,SAAS;QACzC,eAAe,CAAC;IAClB;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ,SAAS;QACvD,MAAM;IACR,GAAG,WAAW;QACZ,KAAK;QACL,UAAU;QACV,SAAS;QACT,WAAW;IACb,IAAI;AACN;AACA,KAAK,WAAW,GAAG;uCACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40ant-design/icons/es/components/IconFont.js"], "sourcesContent": ["function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nimport * as React from 'react';\nimport Icon from \"./Icon\";\nconst customCache = new Set();\nfunction isValidCustomScriptUrl(scriptUrl) {\n  return Boolean(typeof scriptUrl === 'string' && scriptUrl.length && !customCache.has(scriptUrl));\n}\nfunction createScriptUrlElements(scriptUrls, index = 0) {\n  const currentScriptUrl = scriptUrls[index];\n  if (isValidCustomScriptUrl(currentScriptUrl)) {\n    const script = document.createElement('script');\n    script.setAttribute('src', currentScriptUrl);\n    script.setAttribute('data-namespace', currentScriptUrl);\n    if (scriptUrls.length > index + 1) {\n      script.onload = () => {\n        createScriptUrlElements(scriptUrls, index + 1);\n      };\n      script.onerror = () => {\n        createScriptUrlElements(scriptUrls, index + 1);\n      };\n    }\n    customCache.add(currentScriptUrl);\n    document.body.appendChild(script);\n  }\n}\nexport default function create(options = {}) {\n  const {\n    scriptUrl,\n    extraCommonProps = {}\n  } = options;\n\n  /**\n   * DOM API required.\n   * Make sure in browser environment.\n   * The Custom Icon will create a <script/>\n   * that loads SVG symbols and insert the SVG Element into the document body.\n   */\n  if (scriptUrl && typeof document !== 'undefined' && typeof window !== 'undefined' && typeof document.createElement === 'function') {\n    if (Array.isArray(scriptUrl)) {\n      // 因为iconfont资源会把svg插入before，所以前加载相同type会覆盖后加载，为了数组覆盖顺序，倒叙插入\n      createScriptUrlElements(scriptUrl.reverse());\n    } else {\n      createScriptUrlElements([scriptUrl]);\n    }\n  }\n  const Iconfont = /*#__PURE__*/React.forwardRef((props, ref) => {\n    const {\n      type,\n      children,\n      ...restProps\n    } = props;\n\n    // children > type\n    let content = null;\n    if (props.type) {\n      content = /*#__PURE__*/React.createElement(\"use\", {\n        xlinkHref: `#${type}`\n      });\n    }\n    if (children) {\n      content = children;\n    }\n    return /*#__PURE__*/React.createElement(Icon, _extends({}, extraCommonProps, restProps, {\n      ref: ref\n    }), content);\n  });\n  Iconfont.displayName = 'Iconfont';\n  return Iconfont;\n}"], "names": [], "mappings": ";;;AACA;AACA;AAFA,SAAS;IAAa,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK;IAAkO,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAAY;;;AAGlV,MAAM,cAAc,IAAI;AACxB,SAAS,uBAAuB,SAAS;IACvC,OAAO,QAAQ,OAAO,cAAc,YAAY,UAAU,MAAM,IAAI,CAAC,YAAY,GAAG,CAAC;AACvF;AACA,SAAS,wBAAwB,UAAU;QAAE,QAAA,iEAAQ;IACnD,MAAM,mBAAmB,UAAU,CAAC,MAAM;IAC1C,IAAI,uBAAuB,mBAAmB;QAC5C,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,YAAY,CAAC,OAAO;QAC3B,OAAO,YAAY,CAAC,kBAAkB;QACtC,IAAI,WAAW,MAAM,GAAG,QAAQ,GAAG;YACjC,OAAO,MAAM,GAAG;gBACd,wBAAwB,YAAY,QAAQ;YAC9C;YACA,OAAO,OAAO,GAAG;gBACf,wBAAwB,YAAY,QAAQ;YAC9C;QACF;QACA,YAAY,GAAG,CAAC;QAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF;AACe,SAAS;QAAO,UAAA,iEAAU,CAAC;IACxC,MAAM,EACJ,SAAS,EACT,mBAAmB,CAAC,CAAC,EACtB,GAAG;IAEJ;;;;;GAKC,GACD,IAAI,aAAa,OAAO,aAAa,eAAe,OAAO,WAAW,eAAe,OAAO,SAAS,aAAa,KAAK,YAAY;QACjI,IAAI,MAAM,OAAO,CAAC,YAAY;YAC5B,4DAA4D;YAC5D,wBAAwB,UAAU,OAAO;QAC3C,OAAO;YACL,wBAAwB;gBAAC;aAAU;QACrC;IACF;IACA,MAAM,WAAW,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,CAAC,OAAO;QACrD,MAAM,EACJ,IAAI,EACJ,QAAQ,EACR,GAAG,WACJ,GAAG;QAEJ,kBAAkB;QAClB,IAAI,UAAU;QACd,IAAI,MAAM,IAAI,EAAE;YACd,UAAU,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;gBAChD,WAAW,AAAC,IAAQ,OAAL;YACjB;QACF;QACA,IAAI,UAAU;YACZ,UAAU;QACZ;QACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,uKAAA,CAAA,UAAI,EAAE,SAAS,CAAC,GAAG,kBAAkB,WAAW;YACtF,KAAK;QACP,IAAI;IACN;IACA,SAAS,WAAW,GAAG;IACvB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40ant-design/icons/es/index.js"], "sourcesContent": ["import Context from \"./components/Context\";\nexport * from \"./icons\";\nexport * from \"./components/twoTonePrimaryColor\";\nexport { default as createFromIconfontCN } from \"./components/IconFont\";\nexport { default } from \"./components/Icon\";\nexport const IconProvider = Context.Provider;"], "names": [], "mappings": ";;;AAAA;;;;;;AAKO,MAAM,eAAe,0KAAA,CAAA,UAAO,CAAC,QAAQ", "ignoreList": [0], "debugId": null}}]}
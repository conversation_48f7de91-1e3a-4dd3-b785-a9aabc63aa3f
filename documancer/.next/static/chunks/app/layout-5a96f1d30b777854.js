(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{14961:(e,r,t)=>{Promise.resolve().then(t.bind(t,22306)),Promise.resolve().then(t.t.bind(t,4147,23)),Promise.resolve().then(t.t.bind(t,38489,23)),Promise.resolve().then(t.t.bind(t,30347,23)),Promise.resolve().then(t.bind(t,36090))},22306:(e,r,t)=>{"use strict";t.d(r,{ConfigProvider:()=>o.Ay});var o=t(68799)},30347:()=>{},36090:(e,r,t)=>{"use strict";t.d(r,{default:()=>d});var o=t(95155),s=t(12115),n=t(52059),i=t(30662),a=t(34140),h=t(98902);class l extends s.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("Error caught by boundary:",e,r),this.setState({error:e,errorInfo:r})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,o.jsx)(n.Ay,{status:"error",title:"Something went wrong",subTitle:"We're sorry, but something unexpected happened. Please try refreshing the page or go back to the home page.",extra:[(0,o.jsx)(i.Ay,{type:"primary",icon:(0,o.jsx)(a.A,{}),onClick:this.handleReload,children:"Reload Page"},"reload"),(0,o.jsx)(i.Ay,{icon:(0,o.jsx)(h.A,{}),onClick:this.handleGoHome,children:"Go Home"},"home")],children:!1})}):this.props.children}constructor(e){super(e),this.handleReload=()=>{window.location.reload()},this.handleGoHome=()=>{window.location.href="/"},this.state={hasError:!1}}}let d=l}},e=>{e.O(0,[896,685,743,441,964,358],()=>e(e.s=14961)),_N_E=e.O()}]);
"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[309],{54014:(t,e,i)=>{i.d(e,{D6:()=>e$,EA:()=>ep,Tm:()=>eW,YE:()=>ej,dU:()=>iC,ng:()=>l,rE:()=>eZ});var s=i(87358),r=i(44134).hp;let a="object"==typeof s&&s+""=="[object process]"&&!s.versions.nw&&!(s.versions.electron&&s.type&&"browser"!==s.type),n=[.001,0,0,.001,0,0],o={ANY:1,DISPLAY:2,PRINT:4,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,IS_EDITING:128,OPLIST:256},l={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENA<PERSON>E_STORAGE:3},h={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15,SIGNATURE:101},d={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},c={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},u={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},p={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},g={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},f={ERRORS:0,WARNINGS:1,INFOS:5},m={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93,rawFillPath:94},b={moveTo:0,lineTo:1,curveTo:2,closePath:3},A=f.WARNINGS;function v(t){A>=f.INFOS&&console.log(`Info: ${t}`)}function y(t){A>=f.WARNINGS&&console.log(`Warning: ${t}`)}function w(t){throw Error(t)}function _(t,e){t||w(e)}function x(t,e=null,i=null){if(!t)return null;if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){let e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{var s;s=t,t=decodeURIComponent(escape(s))}catch{}}let r=e?URL.parse(t,e):URL.parse(t);return!function(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(r)?null:r}function E(t,e,i=!1){let s=URL.parse(t);return s?(s.hash=e,s.href):i&&x(t,"http://example.com")?t.split("#",1)[0]+`${e?`#${e}`:""}`:""}function S(t,e,i,s=!1){return Object.defineProperty(t,e,{value:i,enumerable:!s,configurable:!0,writable:!1}),i}let C=function(){function t(t,e){this.message=t,this.name=e}return t.prototype=Error(),t.constructor=t,t}();class T extends C{constructor(t,e){super(t,"PasswordException"),this.code=e}}class M extends C{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class P extends C{constructor(t){super(t,"InvalidPDFException")}}class I extends C{constructor(t,e,i){super(t,"ResponseException"),this.status=e,this.missing=i}}class D extends C{constructor(t){super(t,"FormatError")}}class R extends C{constructor(t){super(t,"AbortException")}}function k(t){("object"!=typeof t||t?.length===void 0)&&w("Invalid argument for bytesToString");let e=t.length;if(e<8192)return String.fromCharCode.apply(null,t);let i=[];for(let s=0;s<e;s+=8192){let r=Math.min(s+8192,e),a=t.subarray(s,r);i.push(String.fromCharCode.apply(null,a))}return i.join("")}function L(t){"string"!=typeof t&&w("Invalid argument for stringToBytes");let e=t.length,i=new Uint8Array(e);for(let s=0;s<e;++s)i[s]=255&t.charCodeAt(s);return i}class F{static get isLittleEndian(){return S(this,"isLittleEndian",function(){let t=new Uint8Array(4);return t[0]=1,1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return S(this,"isEvalSupported",function(){try{return Function(""),!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return S(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get isImageDecoderSupported(){return S(this,"isImageDecoderSupported","undefined"!=typeof ImageDecoder)}static get platform(){let{platform:t,userAgent:e}=navigator;return S(this,"platform",{isAndroid:e.includes("Android"),isLinux:t.includes("Linux"),isMac:t.includes("Mac"),isWindows:t.includes("Win"),isFirefox:e.includes("Firefox")})}static get isCSSRoundSupported(){return S(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}let N=Array.from(Array(256).keys(),t=>t.toString(16).padStart(2,"0"));class O{static makeHexColor(t,e,i){return`#${N[t]}${N[e]}${N[i]}`}static scaleMinMax(t,e){let i;t[0]?(t[0]<0&&(i=e[0],e[0]=e[2],e[2]=i),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(i=e[1],e[1]=e[3],e[3]=i),e[1]*=t[3],e[3]*=t[3]):(i=e[0],e[0]=e[1],e[1]=i,i=e[2],e[2]=e[3],e[3]=i,t[1]<0&&(i=e[1],e[1]=e[3],e[3]=i),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(i=e[0],e[0]=e[2],e[2]=i),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e,i=0){let s=t[i],r=t[i+1];t[i]=s*e[0]+r*e[2]+e[4],t[i+1]=s*e[1]+r*e[3]+e[5]}static applyTransformToBezier(t,e,i=0){let s=e[0],r=e[1],a=e[2],n=e[3],o=e[4],l=e[5];for(let e=0;e<6;e+=2){let h=t[i+e],d=t[i+e+1];t[i+e]=h*s+d*a+o,t[i+e+1]=h*r+d*n+l}}static applyInverseTransform(t,e){let i=t[0],s=t[1],r=e[0]*e[3]-e[1]*e[2];t[0]=(i*e[3]-s*e[2]+e[2]*e[5]-e[4]*e[3])/r,t[1]=(-i*e[1]+s*e[0]+e[4]*e[1]-e[5]*e[0])/r}static axialAlignedBoundingBox(t,e,i){let s=e[0],r=e[1],a=e[2],n=e[3],o=e[4],l=e[5],h=t[0],d=t[1],c=t[2],u=t[3],p=s*h+o,g=p,f=s*c+o,m=f,b=n*d+l,A=b,v=n*u+l,y=v;if(0!==r||0!==a){let t=r*h,e=r*c,i=a*d,s=a*u;p+=i,m+=i,f+=s,g+=s,b+=t,y+=t,v+=e,A+=e}i[0]=Math.min(i[0],p,f,g,m),i[1]=Math.min(i[1],b,v,A,y),i[2]=Math.max(i[2],p,f,g,m),i[3]=Math.max(i[3],b,v,A,y)}static inverseTransform(t){let e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t,e){let i=t[0],s=t[1],r=t[2],a=t[3],n=i**2+s**2,o=r**2+a**2,l=(n+o)/2,h=Math.sqrt(l**2-(n*o-(i*r+s*a)**2));e[0]=Math.sqrt(l+h||1),e[1]=Math.sqrt(l-h||1)}static normalizeRect(t){let e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){let i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),s=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>s)return null;let r=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return r>a?null:[i,r,s,a]}static pointBoundingBox(t,e,i){i[0]=Math.min(i[0],t),i[1]=Math.min(i[1],e),i[2]=Math.max(i[2],t),i[3]=Math.max(i[3],e)}static rectBoundingBox(t,e,i,s,r){r[0]=Math.min(r[0],t,i),r[1]=Math.min(r[1],e,s),r[2]=Math.max(r[2],t,i),r[3]=Math.max(r[3],e,s)}static #t(t,e,i,s,r,a,n,o,l,h){if(l<=0||l>=1)return;let d=1-l,c=l*l,u=c*l,p=d*(d*(d*t+3*l*e)+3*c*i)+u*s,g=d*(d*(d*r+3*l*a)+3*c*n)+u*o;h[0]=Math.min(h[0],p),h[1]=Math.min(h[1],g),h[2]=Math.max(h[2],p),h[3]=Math.max(h[3],g)}static #e(t,e,i,s,r,a,n,o,l,h,d,c){if(1e-12>Math.abs(l)){Math.abs(h)>=1e-12&&this.#t(t,e,i,s,r,a,n,o,-d/h,c);return}let u=h**2-4*d*l;if(u<0)return;let p=Math.sqrt(u),g=2*l;this.#t(t,e,i,s,r,a,n,o,(-h+p)/g,c),this.#t(t,e,i,s,r,a,n,o,(-h-p)/g,c)}static bezierBoundingBox(t,e,i,s,r,a,n,o,l){l[0]=Math.min(l[0],t,n),l[1]=Math.min(l[1],e,o),l[2]=Math.max(l[2],t,n),l[3]=Math.max(l[3],e,o),this.#e(t,i,r,n,e,s,a,o,3*(-t+3*(i-r)+n),6*(t-2*i+r),3*(i-t),l),this.#e(t,i,r,n,e,s,a,o,3*(-e+3*(s-a)+o),6*(e-2*s+a),3*(s-e),l)}}let B=null,z=null;function H(){if("function"==typeof crypto.randomUUID)return crypto.randomUUID();let t=new Uint8Array(32);return crypto.getRandomValues(t),k(t)}let U="pdfjs_internal_id_";function $(t,e,i){return Math.min(Math.max(t,e),i)}function G(t){return Uint8Array.prototype.toBase64?t.toBase64():btoa(k(t))}"function"!=typeof Promise.try&&(Promise.try=function(t,...e){return new Promise(i=>{i(t(...e))})}),"function"!=typeof Math.sumPrecise&&(Math.sumPrecise=function(t){return t.reduce((t,e)=>t+e,0)});let j="http://www.w3.org/2000/svg";class V{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function W(t,e="text"){if(J(t,document.baseURI)){let i=await fetch(t);if(!i.ok)throw Error(i.statusText);switch(e){case"arraybuffer":return i.arrayBuffer();case"blob":return i.blob();case"json":return i.json()}return i.text()}return new Promise((i,s)=>{let r=new XMLHttpRequest;r.open("GET",t,!0),r.responseType=e,r.onreadystatechange=()=>{if(r.readyState===XMLHttpRequest.DONE){if(200===r.status||0===r.status){switch(e){case"arraybuffer":case"blob":case"json":i(r.response);return}i(r.responseText);return}s(Error(r.statusText))}},r.send(null)})}class q{constructor({viewBox:t,userUnit:e,scale:i,rotation:s,offsetX:r=0,offsetY:a=0,dontFlip:n=!1}){let o,l,h,d,c,u,p,g;this.viewBox=t,this.userUnit=e,this.scale=i,this.rotation=s,this.offsetX=r,this.offsetY=a,i*=e;let f=(t[2]+t[0])/2,m=(t[3]+t[1])/2;switch((s%=360)<0&&(s+=360),s){case 180:o=-1,l=0,h=0,d=1;break;case 90:o=0,l=1,h=1,d=0;break;case 270:o=0,l=-1,h=-1,d=0;break;case 0:o=1,l=0,h=0,d=-1;break;default:throw Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}n&&(h=-h,d=-d),0===o?(c=Math.abs(m-t[1])*i+r,u=Math.abs(f-t[0])*i+a,p=(t[3]-t[1])*i,g=(t[2]-t[0])*i):(c=Math.abs(f-t[0])*i+r,u=Math.abs(m-t[1])*i+a,p=(t[2]-t[0])*i,g=(t[3]-t[1])*i),this.transform=[o*i,l*i,h*i,d*i,c-o*i*f-h*i*m,u-l*i*f-d*i*m],this.width=p,this.height=g}get rawDims(){let t=this.viewBox;return S(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:s=this.offsetY,dontFlip:r=!1}={}){return new q({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:r})}convertToViewportPoint(t,e){let i=[t,e];return O.applyTransform(i,this.transform),i}convertToViewportRectangle(t){let e=[t[0],t[1]];O.applyTransform(e,this.transform);let i=[t[2],t[3]];return O.applyTransform(i,this.transform),[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){let i=[t,e];return O.applyInverseTransform(i,this.transform),i}}class K extends C{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function X(t){let e=t.length,i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}function Y(t){return"string"==typeof t&&/\.pdf$/i.test(t)}class Q{started=Object.create(null);times=[];time(t){t in this.started&&y(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||y(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){let t=[],e=0;for(let{name:t}of this.times)e=Math.max(t.length,e);for(let{name:i,start:s,end:r}of this.times)t.push(`${i.padEnd(e)} ${r-s}ms
`);return t.join("")}}function J(t,e){let i=e?URL.parse(t,e):URL.parse(t);return i?.protocol==="http:"||i?.protocol==="https:"}function Z(t){t.preventDefault()}function tt(t){t.preventDefault(),t.stopPropagation()}class te{static #i;static toDateObject(t){if(!t||"string"!=typeof t)return null;this.#i||=RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");let e=this.#i.exec(t);if(!e)return null;let i=parseInt(e[1],10),s=parseInt(e[2],10);s=s>=1&&s<=12?s-1:0;let r=parseInt(e[3],10);r=r>=1&&r<=31?r:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let n=parseInt(e[5],10);n=n>=0&&n<=59?n:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;let l=e[7]||"Z",h=parseInt(e[8],10);h=h>=0&&h<=23?h:0;let d=parseInt(e[9],10)||0;return d=d>=0&&d<=59?d:0,"-"===l?(a+=h,n+=d):"+"===l&&(a-=h,n-=d),new Date(Date.UTC(i,s,r,a,n,o))}}function ti(t){if(t.startsWith("#")){let e=parseInt(t.slice(1),16);return[(0xff0000&e)>>16,(65280&e)>>8,255&e]}return t.startsWith("rgb(")?t.slice(4,-1).split(",").map(t=>parseInt(t)):t.startsWith("rgba(")?t.slice(5,-1).split(",").map(t=>parseInt(t)).slice(0,3):(y(`Not a valid color format: "${t}"`),[0,0,0])}function ts(t){let{a:e,b:i,c:s,d:r,e:a,f:n}=t.getTransform();return[e,i,s,r,a,n]}function tr(t){let{a:e,b:i,c:s,d:r,e:a,f:n}=t.getTransform().invertSelf();return[e,i,s,r,a,n]}function ta(t,e,i=!1,s=!0){if(e instanceof q){let{pageWidth:s,pageHeight:r}=e.rawDims,{style:a}=t,n=F.isCSSRoundSupported,o=`var(--total-scale-factor) * ${s}px`,l=`var(--total-scale-factor) * ${r}px`,h=n?`round(down, ${o}, var(--scale-round-x))`:`calc(${o})`,d=n?`round(down, ${l}, var(--scale-round-y))`:`calc(${l})`;i&&e.rotation%180!=0?(a.width=d,a.height=h):(a.width=h,a.height=d)}s&&t.setAttribute("data-main-rotation",e.rotation)}class tn{constructor(){let{pixelRatio:t}=tn;this.sx=t,this.sy=t}get scaled(){return 1!==this.sx||1!==this.sy}get symmetric(){return this.sx===this.sy}limitCanvas(t,e,i,s,r=-1){let a=1/0,n=1/0,o=1/0;(i=tn.capPixels(i,r))>0&&(a=Math.sqrt(i/(t*e))),-1!==s&&(n=s/t,o=s/e);let l=Math.min(a,n,o);return(this.sx>l||this.sy>l)&&(this.sx=l,this.sy=l,!0)}static get pixelRatio(){return globalThis.devicePixelRatio||1}static capPixels(t,e){if(e>=0){let i=Math.ceil(window.screen.availWidth*window.screen.availHeight*this.pixelRatio**2*(1+e/100));return t>0?Math.min(t,i):i}return t}}let to=["image/apng","image/avif","image/bmp","image/gif","image/jpeg","image/png","image/svg+xml","image/webp","image/x-icon"];class tl{#s=null;#r=null;#a;#n=null;#o=null;#l=null;static #h=null;constructor(t){this.#a=t,tl.#h||=Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button",signature:"pdfjs-editor-remove-signature-button"})}render(){let t=this.#s=document.createElement("div");t.classList.add("editToolbar","hidden"),t.setAttribute("role","toolbar");let e=this.#a._uiManager._signal;t.addEventListener("contextmenu",Z,{signal:e}),t.addEventListener("pointerdown",tl.#d,{signal:e});let i=this.#n=document.createElement("div");i.className="buttons",t.append(i);let s=this.#a.toolbarPosition;if(s){let{style:e}=t,i="ltr"===this.#a._uiManager.direction?1-s[0]:s[0];e.insetInlineEnd=`${100*i}%`,e.top=`calc(${100*s[1]}% + var(--editor-toolbar-vert-offset))`}return this.#c(),t}get div(){return this.#s}static #d(t){t.stopPropagation()}#u(t){this.#a._focusEventsAllowed=!1,tt(t)}#p(t){this.#a._focusEventsAllowed=!0,tt(t)}#g(t){let e=this.#a._uiManager._signal;t.addEventListener("focusin",this.#u.bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",this.#p.bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",Z,{signal:e})}hide(){this.#s.classList.add("hidden"),this.#r?.hideDropdown()}show(){this.#s.classList.remove("hidden"),this.#o?.shown()}#c(){let{editorType:t,_uiManager:e}=this.#a,i=document.createElement("button");i.className="delete",i.tabIndex=0,i.setAttribute("data-l10n-id",tl.#h[t]),this.#g(i),i.addEventListener("click",t=>{e.delete()},{signal:e._signal}),this.#n.append(i)}get #f(){let t=document.createElement("div");return t.className="divider",t}async addAltText(t){let e=await t.render();this.#g(e),this.#n.prepend(e,this.#f),this.#o=t}addColorPicker(t){this.#r=t;let e=t.renderButton();this.#g(e),this.#n.prepend(e,this.#f)}async addEditSignatureButton(t){let e=this.#l=await t.renderEditButton(this.#a);this.#g(e),this.#n.prepend(e,this.#f)}updateEditSignatureButton(t){this.#l&&(this.#l.title=t)}remove(){this.#s.remove(),this.#r?.destroy(),this.#r=null}}class th{#n=null;#s=null;#m;constructor(t){this.#m=t}#b(){let t=this.#s=document.createElement("div");t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",Z,{signal:this.#m._signal});let e=this.#n=document.createElement("div");return e.className="buttons",t.append(e),this.#A(),t}#v(t,e){let i=0,s=0;for(let r of t){let t=r.y+r.height;if(t<i)continue;let a=r.x+(e?r.width:0);if(t>i){s=a,i=t;continue}e?a>s&&(s=a):a<s&&(s=a)}return[e?1-s:s,i]}show(t,e,i){let[s,r]=this.#v(e,i),{style:a}=this.#s||=this.#b();t.append(this.#s),a.insetInlineEnd=`${100*s}%`,a.top=`calc(${100*r}% + var(--editor-toolbar-vert-offset))`}hide(){this.#s.remove()}#A(){let t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");let e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");let i=this.#m._signal;t.addEventListener("contextmenu",Z,{signal:i}),t.addEventListener("click",()=>{this.#m.highlightSelection("floating_button")},{signal:i}),this.#n.append(t)}}function td(t,e,i){for(let s of i)e.addEventListener(s,t[s].bind(t))}class tc{#y=0;get id(){return`pdfjs_internal_editor_${this.#y++}`}}class tu{#w=H();#y=0;#_=null;static get _isSVGFittingCanvas(){let t=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),e=new Image;return e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',S(this,"_isSVGFittingCanvas",e.decode().then(()=>(t.drawImage(e,0,0,1,1,0,0,1,3),0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0])))}async #x(t,e){this.#_||=new Map;let i=this.#_.get(t);if(null===i)return null;if(i?.bitmap)return i.refCounter+=1,i;try{let t;if(i||={bitmap:null,id:`image_${this.#w}_${this.#y++}`,refCounter:0,isSvg:!1},"string"==typeof e?(i.url=e,t=await W(e,"blob")):e instanceof File?t=i.file=e:e instanceof Blob&&(t=e),"image/svg+xml"===t.type){let e=tu._isSVGFittingCanvas,s=new FileReader,r=new Image,a=new Promise((t,a)=>{r.onload=()=>{i.bitmap=r,i.isSvg=!0,t()},s.onload=async()=>{let t=i.svgUrl=s.result;r.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t},r.onerror=s.onerror=a});s.readAsDataURL(t),await a}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(t){y(t),i=null}return this.#_.set(t,i),i&&this.#_.set(i.id,i),i}async getFromFile(t){let{lastModified:e,name:i,size:s,type:r}=t;return this.#x(`${e}_${i}_${s}_${r}`,t)}async getFromUrl(t){return this.#x(t,t)}async getFromBlob(t,e){let i=await e;return this.#x(t,i)}async getFromId(t){this.#_||=new Map;let e=this.#_.get(t);if(!e)return null;if(e.bitmap)return e.refCounter+=1,e;if(e.file)return this.getFromFile(e.file);if(e.blobPromise){let{blobPromise:t}=e;return delete e.blobPromise,this.getFromBlob(e.id,t)}return this.getFromUrl(e.url)}getFromCanvas(t,e){this.#_||=new Map;let i=this.#_.get(t);if(i?.bitmap)return i.refCounter+=1,i;let s=new OffscreenCanvas(e.width,e.height);return s.getContext("2d").drawImage(e,0,0),i={bitmap:s.transferToImageBitmap(),id:`image_${this.#w}_${this.#y++}`,refCounter:1,isSvg:!1},this.#_.set(t,i),this.#_.set(i.id,i),i}getSvgUrl(t){let e=this.#_.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#_||=new Map;let e=this.#_.get(t);if(!e||(e.refCounter-=1,0!==e.refCounter))return;let{bitmap:i}=e;if(!e.url&&!e.file){let t=new OffscreenCanvas(i.width,i.height);t.getContext("bitmaprenderer").transferFromImageBitmap(i),e.blobPromise=t.convertToBlob()}i.close?.(),e.bitmap=null}isValidId(t){return t.startsWith(`image_${this.#w}_`)}}class tp{#E=[];#S=!1;#C;#T=-1;constructor(t=128){this.#C=t}add({cmd:t,undo:e,post:i,mustExec:s,type:r=NaN,overwriteIfSameType:a=!1,keepUndo:n=!1}){if(s&&t(),this.#S)return;let o={cmd:t,undo:e,post:i,type:r};if(-1===this.#T){this.#E.length>0&&(this.#E.length=0),this.#T=0,this.#E.push(o);return}if(a&&this.#E[this.#T].type===r){n&&(o.undo=this.#E[this.#T].undo),this.#E[this.#T]=o;return}let l=this.#T+1;l===this.#C?this.#E.splice(0,1):(this.#T=l,l<this.#E.length&&this.#E.splice(l)),this.#E.push(o)}undo(){if(-1===this.#T)return;this.#S=!0;let{undo:t,post:e}=this.#E[this.#T];t(),e?.(),this.#S=!1,this.#T-=1}redo(){if(this.#T<this.#E.length-1){this.#T+=1,this.#S=!0;let{cmd:t,post:e}=this.#E[this.#T];t(),e?.(),this.#S=!1}}hasSomethingToUndo(){return -1!==this.#T}hasSomethingToRedo(){return this.#T<this.#E.length-1}cleanType(t){if(-1!==this.#T){for(let e=this.#T;e>=0;e--)if(this.#E[e].type!==t){this.#E.splice(e+1,this.#T-e),this.#T=e;return}this.#E.length=0,this.#T=-1}}destroy(){this.#E=null}}class tg{constructor(t){this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;let{isMac:e}=F.platform;for(let[i,s,r={}]of t)for(let t of i){let i=t.startsWith("mac+");e&&i?(this.callbacks.set(t.slice(4),{callback:s,options:r}),this.allKeys.add(t.split("+").at(-1))):e||i||(this.callbacks.set(t,{callback:s,options:r}),this.allKeys.add(t.split("+").at(-1)))}}#M(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);let e=this.buffer.join("+");return this.buffer.length=0,e}exec(t,e){if(!this.allKeys.has(e.key))return;let i=this.callbacks.get(this.#M(e));if(!i)return;let{callback:s,options:{bubbles:r=!1,args:a=[],checker:n=null}}=i;(!n||n(t,e))&&(s.bind(t,...a,e)(),r||tt(e))}}class tf{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){let t=new Map([["CanvasText",null],["Canvas",null]]),e=document.createElement("span");for(let i of(e.style.visibility="hidden",e.style.colorScheme="only light",document.body.append(e),t.keys())){e.style.color=i;let s=window.getComputedStyle(e).color;t.set(i,ti(s))}return e.remove(),S(this,"_colors",t)}convert(t){let e=ti(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(let[t,i]of this._colors)if(i.every((t,i)=>t===e[i]))return tf._colorsMapping.get(t);return e}getHexCode(t){let e=this._colors.get(t);return e?O.makeHexColor(...e):t}}class tm{#P=new AbortController;#I=null;#D=new Map;#R=new Map;#k=null;#L=null;#F=null;#N=new tp;#O=null;#B=null;#z=0;#H=new Set;#U=null;#$=null;#G=new Set;_editorUndoBar=null;#j=!1;#V=!1;#W=!1;#q=null;#K=null;#X=null;#Y=null;#Q=!1;#J=null;#Z=new tc;#tt=!1;#te=!1;#ti=null;#ts=null;#tr=null;#ta=null;#tn=null;#to=h.NONE;#tl=new Set;#th=null;#td=null;#tc=null;#tu=null;#tp={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#tg=[0,0];#tf=null;#tm=null;#tb=null;#tA=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){let t=tm.prototype,e=t=>t.#tm.contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),i=(t,{target:e})=>{if(e instanceof HTMLInputElement){let{type:t}=e;return"text"!==t&&"number"!==t}return!0},s=this.TRANSLATE_SMALL,r=this.TRANSLATE_BIG;return S(this,"_keyboardManager",new tg([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:i}],[["ctrl+z","mac+meta+z"],t.undo,{checker:i}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:i}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:i}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#tm.contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#tm.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-s,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-r,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[s,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[r,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-s],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-r],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,s],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,r],checker:e}]]))}constructor(t,e,i,s,r,a,n,o,l,h,d,c,u,p){let g=this._signal=this.#P.signal;this.#tm=t,this.#tb=e,this.#k=i,this.#td=s,this._eventBus=r,r._on("editingaction",this.onEditingAction.bind(this),{signal:g}),r._on("pagechanging",this.onPageChanging.bind(this),{signal:g}),r._on("scalechanging",this.onScaleChanging.bind(this),{signal:g}),r._on("rotationchanging",this.onRotationChanging.bind(this),{signal:g}),r._on("setpreference",this.onSetPreference.bind(this),{signal:g}),r._on("switchannotationeditorparams",t=>this.updateParams(t.type,t.value),{signal:g}),this.#tv(),this.#ty(),this.#tw(),this.#L=a.annotationStorage,this.#q=a.filterFactory,this.#tc=n,this.#Y=o||null,this.#j=l,this.#V=h,this.#W=d,this.#tn=c||null,this.viewParameters={realScale:V.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1,this._editorUndoBar=u||null,this._supportsPinchToZoom=!1!==p}destroy(){for(let t of(this.#tA?.resolve(),this.#tA=null,this.#P?.abort(),this.#P=null,this._signal=null,this.#R.values()))t.destroy();this.#R.clear(),this.#D.clear(),this.#G.clear(),this.#ta?.clear(),this.#I=null,this.#tl.clear(),this.#N.destroy(),this.#k?.destroy(),this.#td?.destroy(),this.#J?.hide(),this.#J=null,this.#tr?.destroy(),this.#tr=null,this.#K&&(clearTimeout(this.#K),this.#K=null),this.#tf&&(clearTimeout(this.#tf),this.#tf=null),this._editorUndoBar?.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return this.#tn}get useNewAltTextFlow(){return this.#V}get useNewAltTextWhenAddingImage(){return this.#W}get hcmFilter(){return S(this,"hcmFilter",this.#tc?this.#q.addHCMFilter(this.#tc.foreground,this.#tc.background):"none")}get direction(){return S(this,"direction",getComputedStyle(this.#tm).direction)}get highlightColors(){return S(this,"highlightColors",this.#Y?new Map(this.#Y.split(",").map(t=>t.split("=").map(t=>t.trim()))):null)}get highlightColorNames(){return S(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,t=>t.reverse())):null)}setCurrentDrawingSession(t){t?(this.unselectAll(),this.disableUserSelect(!0)):this.disableUserSelect(!1),this.#B=t}setMainHighlightColorPicker(t){this.#tr=t}editAltText(t,e=!1){this.#k?.editAltText(this,t,e)}getSignature(t){this.#td?.getSignature({uiManager:this,editor:t})}get signatureManager(){return this.#td}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal}),this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){"enableNewAltTextWhenAddingImage"===t&&(this.#W=e)}onPageChanging({pageNumber:t}){this.#z=t-1}focusMainContainer(){this.#tm.focus()}findParent(t,e){for(let i of this.#R.values()){let{x:s,y:r,width:a,height:n}=i.div.getBoundingClientRect();if(t>=s&&t<=s+a&&e>=r&&e<=r+n)return i}return null}disableUserSelect(t=!1){this.#tb.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#G.add(t)}removeShouldRescale(t){this.#G.delete(t)}onScaleChanging({scale:t}){for(let e of(this.commitOrRemove(),this.viewParameters.realScale=t*V.PDF_TO_CSS_UNITS,this.#G))e.onScaleChanging();this.#B?.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}#t_({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}#tx(t){let{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(let e of this.#R.values())if(e.hasTextLayer(t))return e;return null}highlightSelection(t=""){let e=document.getSelection();if(!e||e.isCollapsed)return;let{anchorNode:i,anchorOffset:s,focusNode:r,focusOffset:a}=e,n=e.toString(),o=this.#t_(e).closest(".textLayer"),l=this.getSelectionBoxes(o);if(!l)return;e.empty();let d=this.#tx(o),c=this.#to===h.NONE,u=()=>{d?.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:i,anchorOffset:s,focusNode:r,focusOffset:a,text:n}),c&&this.showAllEditors("highlight",!0,!0)};if(c)return void this.switchToMode(h.HIGHLIGHT,u);u()}#tE(){let t=document.getSelection();if(!t||t.isCollapsed)return;let e=this.#t_(t).closest(".textLayer"),i=this.getSelectionBoxes(e);i&&(this.#J||=new th(this),this.#J.show(e,i,"ltr"===this.direction))}addToAnnotationStorage(t){t.isEmpty()||!this.#L||this.#L.has(t.id)||this.#L.setValue(t.id,t)}#tS(){let t=document.getSelection();if(!t||t.isCollapsed){this.#th&&(this.#J?.hide(),this.#th=null,this.#tC({hasSelectedText:!1}));return}let{anchorNode:e}=t;if(e===this.#th)return;let i=this.#t_(t).closest(".textLayer");if(!i){this.#th&&(this.#J?.hide(),this.#th=null,this.#tC({hasSelectedText:!1}));return}if(this.#J?.hide(),this.#th=e,this.#tC({hasSelectedText:!0}),(this.#to===h.HIGHLIGHT||this.#to===h.NONE)&&(this.#to===h.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),this.#Q=this.isShiftKeyDown,!this.isShiftKeyDown)){let t=this.#to===h.HIGHLIGHT?this.#tx(i):null;t?.toggleDrawing();let e=new AbortController,s=this.combinedSignal(e),r=i=>{("pointerup"!==i.type||0===i.button)&&(e.abort(),t?.toggleDrawing(!0),"pointerup"===i.type&&this.#tT("main_toolbar"))};window.addEventListener("pointerup",r,{signal:s}),window.addEventListener("blur",r,{signal:s})}}#tT(t=""){this.#to===h.HIGHLIGHT?this.highlightSelection(t):this.#j&&this.#tE()}#tv(){document.addEventListener("selectionchange",this.#tS.bind(this),{signal:this._signal})}#tM(){if(this.#X)return;this.#X=new AbortController;let t=this.combinedSignal(this.#X);window.addEventListener("focus",this.focus.bind(this),{signal:t}),window.addEventListener("blur",this.blur.bind(this),{signal:t})}#tP(){this.#X?.abort(),this.#X=null}blur(){if(this.isShiftKeyDown=!1,this.#Q&&(this.#Q=!1,this.#tT("main_toolbar")),!this.hasSelection)return;let{activeElement:t}=document;for(let e of this.#tl)if(e.div.contains(t)){this.#ts=[e,t],e._focusEventsAllowed=!1;break}}focus(){if(!this.#ts)return;let[t,e]=this.#ts;this.#ts=null,e.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this._signal}),e.focus()}#tw(){if(this.#ti)return;this.#ti=new AbortController;let t=this.combinedSignal(this.#ti);window.addEventListener("keydown",this.keydown.bind(this),{signal:t}),window.addEventListener("keyup",this.keyup.bind(this),{signal:t})}#tI(){this.#ti?.abort(),this.#ti=null}#tD(){if(this.#O)return;this.#O=new AbortController;let t=this.combinedSignal(this.#O);document.addEventListener("copy",this.copy.bind(this),{signal:t}),document.addEventListener("cut",this.cut.bind(this),{signal:t}),document.addEventListener("paste",this.paste.bind(this),{signal:t})}#tR(){this.#O?.abort(),this.#O=null}#ty(){let t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})}addEditListeners(){this.#tw(),this.#tD()}removeEditListeners(){this.#tI(),this.#tR()}dragOver(t){for(let{type:e}of t.dataTransfer.items)for(let i of this.#$)if(i.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy",t.preventDefault();return}}drop(t){for(let e of t.dataTransfer.items)for(let i of this.#$)if(i.isHandlingMimeForPasting(e.type)){i.paste(e,this.currentLayer),t.preventDefault();return}}copy(t){if(t.preventDefault(),this.#I?.commitOrRemove(),!this.hasSelection)return;let e=[];for(let t of this.#tl){let i=t.serialize(!0);i&&e.push(i)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}async paste(t){t.preventDefault();let{clipboardData:e}=t;for(let t of e.items)for(let e of this.#$)if(e.isHandlingMimeForPasting(t.type))return void e.paste(t,this.currentLayer);let i=e.getData("application/pdfjs");if(!i)return;try{i=JSON.parse(i)}catch(t){y(`paste: "${t.message}".`);return}if(!Array.isArray(i))return;this.unselectAll();let s=this.currentLayer;try{let t=[];for(let e of i){let i=await s.deserialize(e);if(!i)return;t.push(i)}this.addCommands({cmd:()=>{for(let e of t)this.#tk(e);this.#tL(t)},undo:()=>{for(let e of t)e.remove()},mustExec:!0})}catch(t){y(`paste: "${t.message}".`)}}keydown(t){this.isShiftKeyDown||"Shift"!==t.key||(this.isShiftKeyDown=!0),this.#to===h.NONE||this.isEditorHandlingKeyboard||tm._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&"Shift"===t.key&&(this.isShiftKeyDown=!1,this.#Q&&(this.#Q=!1,this.#tT("main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}#tC(t){Object.entries(t).some(([t,e])=>this.#tp[t]!==e)&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#tp,t)}),this.#to===h.HIGHLIGHT&&!1===t.hasSelectedEditor&&this.#tF([[d.HIGHLIGHT_FREE,!0]]))}#tF(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){t?(this.#tM(),this.#tD(),this.#tC({isEditing:this.#to!==h.NONE,isEmpty:this.#tN(),hasSomethingToUndo:this.#N.hasSomethingToUndo(),hasSomethingToRedo:this.#N.hasSomethingToRedo(),hasSelectedEditor:!1})):(this.#tP(),this.#tR(),this.#tC({isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!this.#$)for(let e of(this.#$=t,this.#$))this.#tF(e.defaultPropertiesToUpdate)}getId(){return this.#Z.id}get currentLayer(){return this.#R.get(this.#z)}getLayer(t){return this.#R.get(t)}get currentPageIndex(){return this.#z}addLayer(t){this.#R.set(t.pageIndex,t),this.#tt?t.enable():t.disable()}removeLayer(t){this.#R.delete(t.pageIndex)}async updateMode(t,e=null,i=!1){if(this.#to!==t&&(!this.#tA||(await this.#tA.promise,this.#tA))){if(this.#tA=Promise.withResolvers(),this.#B?.commitOrRemove(),this.#to=t,t===h.NONE){this.setEditingState(!1),this.#tO(),this._editorUndoBar?.hide(),this.#tA.resolve();return}for(let e of(t===h.SIGNATURE&&await this.#td?.loadSignatures(),this.setEditingState(!0),await this.#tB(),this.unselectAll(),this.#R.values()))e.updateMode(t);if(!e){i&&this.addNewEditorFromKeyboard(),this.#tA.resolve();return}for(let t of this.#D.values())t.annotationElementId===e||t.id===e?(this.setSelected(t),t.enterInEditMode()):t.unselect();this.#tA.resolve()}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t.mode!==this.#to&&this._eventBus.dispatch("switchannotationeditormode",{source:this,...t})}updateParams(t,e){if(this.#$){switch(t){case d.CREATE:this.currentLayer.addNewEditor(e);return;case d.HIGHLIGHT_DEFAULT_COLOR:this.#tr?.updateColor(e);break;case d.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(this.#tu||=new Map).set(t,e),this.showAllEditors("highlight",e)}for(let i of this.#tl)i.updateParams(t,e);for(let i of this.#$)i.updateDefaultParams(t,e)}}showAllEditors(t,e,i=!1){for(let i of this.#D.values())i.editorType===t&&i.show(e);(this.#tu?.get(d.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#tF([[d.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#te!==t)for(let e of(this.#te=t,this.#R.values()))t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}async #tB(){if(!this.#tt){this.#tt=!0;let t=[];for(let e of this.#R.values())t.push(e.enable());for(let e of(await Promise.all(t),this.#D.values()))e.enable()}}#tO(){if(this.unselectAll(),this.#tt){for(let t of(this.#tt=!1,this.#R.values()))t.disable();for(let t of this.#D.values())t.disable()}}getEditors(t){let e=[];for(let i of this.#D.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#D.get(t)}addEditor(t){this.#D.set(t.id,t)}removeEditor(t){t.div.contains(document.activeElement)&&(this.#K&&clearTimeout(this.#K),this.#K=setTimeout(()=>{this.focusMainContainer(),this.#K=null},0)),this.#D.delete(t.id),t.annotationElementId&&this.#ta?.delete(t.annotationElementId),this.unselect(t),t.annotationElementId&&this.#H.has(t.annotationElementId)||this.#L?.remove(t.id)}addDeletedAnnotationElement(t){this.#H.add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return this.#H.has(t)}removeDeletedAnnotationElement(t){this.#H.delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}#tk(t){let e=this.#R.get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))}setActiveEditor(t){this.#I!==t&&(this.#I=t,t&&this.#tF(t.propertiesToUpdate))}get #tz(){let t=null;for(t of this.#tl);return t}updateUI(t){this.#tz===t&&this.#tF(t.propertiesToUpdate)}updateUIForDefaultProperties(t){this.#tF(t.defaultPropertiesToUpdate)}toggleSelected(t){if(this.#tl.has(t)){this.#tl.delete(t),t.unselect(),this.#tC({hasSelectedEditor:this.hasSelection});return}this.#tl.add(t),t.select(),this.#tF(t.propertiesToUpdate),this.#tC({hasSelectedEditor:!0})}setSelected(t){for(let e of(this.#B?.commitOrRemove(),this.#tl))e!==t&&e.unselect();this.#tl.clear(),this.#tl.add(t),t.select(),this.#tF(t.propertiesToUpdate),this.#tC({hasSelectedEditor:!0})}isSelected(t){return this.#tl.has(t)}get firstSelectedEditor(){return this.#tl.values().next().value}unselect(t){t.unselect(),this.#tl.delete(t),this.#tC({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#tl.size}get isEnterHandled(){return 1===this.#tl.size&&this.firstSelectedEditor.isEnterHandled}undo(){this.#N.undo(),this.#tC({hasSomethingToUndo:this.#N.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#tN()}),this._editorUndoBar?.hide()}redo(){this.#N.redo(),this.#tC({hasSomethingToUndo:!0,hasSomethingToRedo:this.#N.hasSomethingToRedo(),isEmpty:this.#tN()})}addCommands(t){this.#N.add(t),this.#tC({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#tN()})}cleanUndoStack(t){this.#N.cleanType(t)}#tN(){if(0===this.#D.size)return!0;if(1===this.#D.size)for(let t of this.#D.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();let t=this.currentLayer?.endDrawingSession(!0);if(!this.hasSelection&&!t)return;let e=t?[t]:[...this.#tl],i=()=>{for(let t of e)this.#tk(t)};this.addCommands({cmd:()=>{for(let t of(this._editorUndoBar?.show(i,1===e.length?e[0].editorType:e.length),e))t.remove()},undo:i,mustExec:!0})}commitOrRemove(){this.#I?.commitOrRemove()}hasSomethingToControl(){return this.#I||this.hasSelection}#tL(t){for(let t of this.#tl)t.unselect();for(let e of(this.#tl.clear(),t))e.isEmpty()||(this.#tl.add(e),e.select());this.#tC({hasSelectedEditor:this.hasSelection})}selectAll(){for(let t of this.#tl)t.commit();this.#tL(this.#D.values())}unselectAll(){if(!this.#I||(this.#I.commitOrRemove(),this.#to===h.NONE)){if(!this.#B?.commitOrRemove()&&this.hasSelection){for(let t of this.#tl)t.unselect();this.#tl.clear(),this.#tC({hasSelectedEditor:!1})}}}translateSelectedEditors(t,e,i=!1){if(i||this.commitOrRemove(),!this.hasSelection)return;this.#tg[0]+=t,this.#tg[1]+=e;let[s,r]=this.#tg,a=[...this.#tl];for(let i of(this.#tf&&clearTimeout(this.#tf),this.#tf=setTimeout(()=>{this.#tf=null,this.#tg[0]=this.#tg[1]=0,this.addCommands({cmd:()=>{for(let t of a)this.#D.has(t.id)&&(t.translateInPage(s,r),t.translationDone())},undo:()=>{for(let t of a)this.#D.has(t.id)&&(t.translateInPage(-s,-r),t.translationDone())},mustExec:!1})},1e3),a))i.translateInPage(t,e),i.translationDone()}setUpDragSession(){if(this.hasSelection)for(let t of(this.disableUserSelect(!0),this.#U=new Map,this.#tl))this.#U.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}endDragSession(){if(!this.#U)return!1;this.disableUserSelect(!1);let t=this.#U;this.#U=null;let e=!1;for(let[{x:i,y:s,pageIndex:r},a]of t)a.newX=i,a.newY=s,a.newPageIndex=r,e||=i!==a.savedX||s!==a.savedY||r!==a.savedPageIndex;if(!e)return!1;let i=(t,e,i,s)=>{if(this.#D.has(t.id)){let r=this.#R.get(s);r?t._setParentAndPosition(r,e,i):(t.pageIndex=s,t.x=e,t.y=i)}};return this.addCommands({cmd:()=>{for(let[e,{newX:s,newY:r,newPageIndex:a}]of t)i(e,s,r,a)},undo:()=>{for(let[e,{savedX:s,savedY:r,savedPageIndex:a}]of t)i(e,s,r,a)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(this.#U)for(let i of this.#U.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){let e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||1===this.#tl.size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#I===t}getActive(){return this.#I}getMode(){return this.#to}get imageManager(){return S(this,"imageManager",new tu)}getSelectionBoxes(t){let e;if(!t)return null;let i=document.getSelection();for(let e=0,s=i.rangeCount;e<s;e++)if(!t.contains(i.getRangeAt(e).commonAncestorContainer))return null;let{x:s,y:r,width:a,height:n}=t.getBoundingClientRect();switch(t.getAttribute("data-main-rotation")){case"90":e=(t,e,i,o)=>({x:(e-r)/n,y:1-(t+i-s)/a,width:o/n,height:i/a});break;case"180":e=(t,e,i,o)=>({x:1-(t+i-s)/a,y:1-(e+o-r)/n,width:i/a,height:o/n});break;case"270":e=(t,e,i,o)=>({x:1-(e+o-r)/n,y:(t-s)/a,width:o/n,height:i/a});break;default:e=(t,e,i,o)=>({x:(t-s)/a,y:(e-r)/n,width:i/a,height:o/n})}let o=[];for(let t=0,s=i.rangeCount;t<s;t++){let s=i.getRangeAt(t);if(!s.collapsed)for(let{x:t,y:i,width:r,height:a}of s.getClientRects())0!==r&&0!==a&&o.push(e(t,i,r,a))}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#F||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#F?.delete(t)}renderAnnotationElement(t){let e=this.#F?.get(t.data.id);if(!e)return;let i=this.#L.getRawValue(e);i&&(this.#to!==h.NONE||i.hasBeenModified)&&i.renderAnnotationElement(t)}setMissingCanvas(t,e,i){let s=this.#ta?.get(t);s&&(s.setCanvas(e,i),this.#ta.delete(t))}addMissingCanvas(t,e){(this.#ta||=new Map).set(t,e)}}class tb{#o=null;#tH=!1;#tU=null;#t$=null;#tG=null;#tj=null;#tV=!1;#tW=null;#a=null;#tq=null;#tK=null;#tX=!1;static #tY=null;static _l10n=null;constructor(t){this.#a=t,this.#tX=t._uiManager.useNewAltTextFlow,tb.#tY||=Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"})}static initialize(t){tb._l10n??=t}async render(){let t=this.#tU=document.createElement("button");t.className="altText",t.tabIndex="0";let e=this.#t$=document.createElement("span");t.append(e),this.#tX?(t.classList.add("new"),t.setAttribute("data-l10n-id",tb.#tY.missing),e.setAttribute("data-l10n-id",tb.#tY["missing-label"])):(t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button"),e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label"));let i=this.#a._uiManager._signal;t.addEventListener("contextmenu",Z,{signal:i}),t.addEventListener("pointerdown",t=>t.stopPropagation(),{signal:i});let s=t=>{t.preventDefault(),this.#a._uiManager.editAltText(this.#a),this.#tX&&this.#a._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:this.#tQ}})};return t.addEventListener("click",s,{capture:!0,signal:i}),t.addEventListener("keydown",e=>{e.target===t&&"Enter"===e.key&&(this.#tV=!0,s(e))},{signal:i}),await this.#tJ(),t}get #tQ(){return this.#o&&"added"||null===this.#o&&this.guessedText&&"review"||"missing"}finish(){this.#tU&&(this.#tU.focus({focusVisible:this.#tV}),this.#tV=!1)}isEmpty(){return this.#tX?null===this.#o:!this.#o&&!this.#tH}hasData(){return this.#tX?null!==this.#o||!!this.#tq:this.isEmpty()}get guessedText(){return this.#tq}async setGuessedText(t){null===this.#o&&(this.#tq=t,this.#tK=await tb._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t}),this.#tJ())}toggleAltTextBadge(t=!1){if(!this.#tX||this.#o){this.#tW?.remove(),this.#tW=null;return}if(!this.#tW){let t=this.#tW=document.createElement("div");t.className="noAltTextBadge",this.#a.div.append(t)}this.#tW.classList.toggle("hidden",!t)}serialize(t){let e=this.#o;return t||this.#tq!==e||(e=this.#tK),{altText:e,decorative:this.#tH,guessedText:this.#tq,textWithDisclaimer:this.#tK}}get data(){return{altText:this.#o,decorative:this.#tH}}set data({altText:t,decorative:e,guessedText:i,textWithDisclaimer:s,cancel:r=!1}){i&&(this.#tq=i,this.#tK=s),(this.#o!==t||this.#tH!==e)&&(r||(this.#o=t,this.#tH=e),this.#tJ())}toggle(t=!1){this.#tU&&(!t&&this.#tj&&(clearTimeout(this.#tj),this.#tj=null),this.#tU.disabled=!t)}shown(){this.#a._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:this.#tQ}})}destroy(){this.#tU?.remove(),this.#tU=null,this.#t$=null,this.#tG=null,this.#tW?.remove(),this.#tW=null}async #tJ(){let t=this.#tU;if(!t)return;if(this.#tX){if(t.classList.toggle("done",!!this.#o),t.setAttribute("data-l10n-id",tb.#tY[this.#tQ]),this.#t$?.setAttribute("data-l10n-id",tb.#tY[`${this.#tQ}-label`]),!this.#o)return void this.#tG?.remove()}else{if(!this.#o&&!this.#tH){t.classList.remove("done"),this.#tG?.remove();return}t.classList.add("done"),t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=this.#tG;if(!e){this.#tG=e=document.createElement("span"),e.className="tooltip",e.setAttribute("role","tooltip"),e.id=`alt-text-tooltip-${this.#a.id}`;let i=this.#a._uiManager._signal;i.addEventListener("abort",()=>{clearTimeout(this.#tj),this.#tj=null},{once:!0}),t.addEventListener("mouseenter",()=>{this.#tj=setTimeout(()=>{this.#tj=null,this.#tG.classList.add("show"),this.#a._reportTelemetry({action:"alt_text_tooltip"})},100)},{signal:i}),t.addEventListener("mouseleave",()=>{this.#tj&&(clearTimeout(this.#tj),this.#tj=null),this.#tG?.classList.remove("show")},{signal:i})}this.#tH?e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip"):(e.removeAttribute("data-l10n-id"),e.textContent=this.#o),e.parentNode||t.append(e);let i=this.#a.getElementForAltText();i?.setAttribute("aria-describedby",e.id)}}class tA{#tm;#tZ=!1;#t0=null;#t1;#t2;#t3;#t5;#t6=null;#t4;#t8=null;#t7;#t9=null;constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:i=null,onPinchStart:s=null,onPinching:r=null,onPinchEnd:a=null,signal:n}){this.#tm=t,this.#t0=i,this.#t1=e,this.#t2=s,this.#t3=r,this.#t5=a,this.#t7=new AbortController,this.#t4=AbortSignal.any([n,this.#t7.signal]),t.addEventListener("touchstart",this.#et.bind(this),{passive:!1,signal:this.#t4})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return 35/tn.pixelRatio}#et(t){if(this.#t1?.())return;if(1===t.touches.length){if(this.#t6)return;let t=this.#t6=new AbortController,e=AbortSignal.any([this.#t4,t.signal]),i=this.#tm,s={capture:!0,signal:e,passive:!1},r=t=>{"touch"===t.pointerType&&(this.#t6?.abort(),this.#t6=null)};i.addEventListener("pointerdown",t=>{"touch"===t.pointerType&&(tt(t),r(t))},s),i.addEventListener("pointerup",r,s),i.addEventListener("pointercancel",r,s);return}if(!this.#t9){this.#t9=new AbortController;let t=AbortSignal.any([this.#t4,this.#t9.signal]),e=this.#tm,i={signal:t,capture:!1,passive:!1};e.addEventListener("touchmove",this.#ee.bind(this),i);let s=this.#ei.bind(this);e.addEventListener("touchend",s,i),e.addEventListener("touchcancel",s,i),i.capture=!0,e.addEventListener("pointerdown",tt,i),e.addEventListener("pointermove",tt,i),e.addEventListener("pointercancel",tt,i),e.addEventListener("pointerup",tt,i),this.#t2?.()}if(tt(t),2!==t.touches.length||this.#t0?.()){this.#t8=null;return}let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]),this.#t8={touch0X:e.screenX,touch0Y:e.screenY,touch1X:i.screenX,touch1Y:i.screenY}}#ee(t){if(!this.#t8||2!==t.touches.length)return;tt(t);let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]);let{screenX:s,screenY:r}=e,{screenX:a,screenY:n}=i,o=this.#t8,{touch0X:l,touch0Y:h,touch1X:d,touch1Y:c}=o,u=Math.hypot(a-s,n-r)||1,p=Math.hypot(d-l,c-h)||1;if(!this.#tZ&&Math.abs(p-u)<=tA.MIN_TOUCH_DISTANCE_TO_PINCH)return;if(o.touch0X=s,o.touch0Y=r,o.touch1X=a,o.touch1Y=n,!this.#tZ){this.#tZ=!0;return}let g=[(s+a)/2,(r+n)/2];this.#t3?.(g,p,u)}#ei(t){!(t.touches.length>=2)&&(this.#t9&&(this.#t9.abort(),this.#t9=null,this.#t5?.()),this.#t8&&(tt(t),this.#t8=null,this.#tZ=!1))}destroy(){this.#t7?.abort(),this.#t7=null,this.#t6?.abort(),this.#t6=null}}class tv{#es=null;#er=null;#o=null;#ea=!1;#en=null;#eo="";#el=!1;#eh=null;#ed=null;#ec=null;#eu=null;#ep="";#eg=!1;#ef=null;#em=!1;#eb=!1;#eA=!1;#ev=null;#ey=0;#ew=0;#e_=null;#ex=null;isSelected=!1;_isCopy=!1;_editToolbar=null;_initialOptions=Object.create(null);_initialData=null;_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;static _l10n=null;static _l10nResizer=null;#eE=!1;#eS=tv._zIndex++;static _borderLineWidth=-1;static _colorManager=new tf;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){let t=tv.prototype._resizeWithKeyboard,e=tm.TRANSLATE_SMALL,i=tm.TRANSLATE_BIG;return S(this,"_resizerKeyboardManager",new tg([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-i,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[i,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-i]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,i]}],[["Escape","mac+Escape"],tv.prototype._stopResizingWithKeyboard]]))}constructor(t){this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;let{rotation:e,rawDims:{pageWidth:i,pageHeight:s,pageX:r,pageY:a}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[i,s],this.pageTranslation=[r,a];let[n,o]=this.parentDimensions;this.x=t.x/n,this.y=t.y/o,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get isDrawer(){return!1}static get _defaultLineColor(){return S(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){let e=new ty({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){if(tv._l10n??=t,tv._l10nResizer||=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"}),-1!==tv._borderLineWidth)return;let i=getComputedStyle(document.documentElement);tv._borderLineWidth=parseFloat(i.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){w("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#eE}set _isDraggable(t){this.#eE=t,this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){let[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t),this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t),this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2,this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#eS}setParent(t){null!==t?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):this.#eC(),this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#eg?this.#eg=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed||!this.isAttachedToDOM)return;let e=t.relatedTarget;!e?.closest(`#${this.id}`)&&(t.preventDefault(),this.parent?.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,s){let[r,a]=this.parentDimensions;[i,s]=this.screenToPageTranslation(i,s),this.x=(t+i)/r,this.y=(e+s)/a,this.fixAndSetPosition()}_moveAfterPaste(t,e){let[i,s]=this.parentDimensions;this.setAt(t*i,e*s,this.width*i,this.height*s),this._onTranslated()}#eT([t,e],i,s){[i,s]=this.screenToPageTranslation(i,s),this.x+=i/t,this.y+=s/e,this._onTranslating(this.x,this.y),this.fixAndSetPosition()}translate(t,e){this.#eT(this.parentDimensions,t,e)}translateInPage(t,e){this.#ef||=[this.x,this.y,this.width,this.height],this.#eT(this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}translationDone(){this._onTranslated(this.x,this.y)}drag(t,e){this.#ef||=[this.x,this.y,this.width,this.height];let{div:i,parentDimensions:[s,r]}=this;if(this.x+=t/s,this.y+=e/r,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){let{x:t,y:e}=this.div.getBoundingClientRect();this.parent.findNewParent(this,t,e)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:a,y:n}=this,[o,l]=this.getBaseTranslation();a+=o,n+=l;let{style:h}=i;h.left=`${(100*a).toFixed(2)}%`,h.top=`${(100*n).toFixed(2)}%`,this._onTranslating(a,n),i.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!this.#ef&&(this.#ef[0]!==this.x||this.#ef[1]!==this.y)}get _hasBeenResized(){return!!this.#ef&&(this.#ef[2]!==this.width||this.#ef[3]!==this.height)}getBaseTranslation(){let[t,e]=this.parentDimensions,{_borderLineWidth:i}=tv,s=i/t,r=i/e;switch(this.rotation){case 90:return[-s,r];case 180:return[s,r];case 270:return[s,-r];default:return[-s,-r]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){let{div:{style:e},pageDimensions:[i,s]}=this,{x:r,y:a,width:n,height:o}=this;if(n*=i,o*=s,r*=i,a*=s,this._mustFixPosition)switch(t){case 0:r=$(r,0,i-n),a=$(a,0,s-o);break;case 90:r=$(r,0,i-o),a=$(a,n,s);break;case 180:r=$(r,n,i),a=$(a,o,s);break;case 270:r=$(r,o,i),a=$(a,0,s-n)}this.x=r/=i,this.y=a/=s;let[l,h]=this.getBaseTranslation();r+=l,a+=h,e.left=`${(100*r).toFixed(2)}%`,e.top=`${(100*a).toFixed(2)}%`,this.moveInDOM()}static #eM(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return tv.#eM(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return tv.#eM(t,e,360-this.parentRotation)}#eP(t){switch(t){case 90:{let[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{let[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){let{parentScale:t,pageDimensions:[e,i]}=this;return[e*t,i*t]}setDims(t,e){let[i,s]=this.parentDimensions,{style:r}=this.div;r.width=`${(100*t/i).toFixed(2)}%`,this.#el||(r.height=`${(100*e/s).toFixed(2)}%`)}fixDims(){let{style:t}=this.div,{height:e,width:i}=t,s=i.endsWith("%"),r=!this.#el&&e.endsWith("%");if(s&&r)return;let[a,n]=this.parentDimensions;s||(t.width=`${(100*parseFloat(i)/a).toFixed(2)}%`),this.#el||r||(t.height=`${(100*parseFloat(e)/n).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#eI(){if(this.#eh)return;this.#eh=document.createElement("div"),this.#eh.classList.add("resizers");let t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(let i of t){let t=document.createElement("div");this.#eh.append(t),t.classList.add("resizer",i),t.setAttribute("data-resizer-name",i),t.addEventListener("pointerdown",this.#eD.bind(this,i),{signal:e}),t.addEventListener("contextmenu",Z,{signal:e}),t.tabIndex=-1}this.div.prepend(this.#eh)}#eD(t,e){e.preventDefault();let{isMac:i}=F.platform;if(0!==e.button||e.ctrlKey&&i)return;this.#o?.toggle(!1);let s=this._isDraggable;this._isDraggable=!1,this.#ed=[e.screenX,e.screenY];let r=new AbortController,a=this._uiManager.combinedSignal(r);this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",this.#eR.bind(this,t),{passive:!0,capture:!0,signal:a}),window.addEventListener("touchmove",tt,{passive:!1,signal:a}),window.addEventListener("contextmenu",Z,{signal:a}),this.#ec={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};let n=this.parent.div.style.cursor,o=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;let l=()=>{r.abort(),this.parent.togglePointerEvents(!0),this.#o?.toggle(!0),this._isDraggable=s,this.parent.div.style.cursor=n,this.div.style.cursor=o,this.#ek()};window.addEventListener("pointerup",l,{signal:a}),window.addEventListener("blur",l,{signal:a})}#eL(t,e,i,s){this.width=i,this.height=s,this.x=t,this.y=e;let[r,a]=this.parentDimensions;this.setDims(r*i,a*s),this.fixAndSetPosition(),this._onResized()}_onResized(){}#ek(){if(!this.#ec)return;let{savedX:t,savedY:e,savedWidth:i,savedHeight:s}=this.#ec;this.#ec=null;let r=this.x,a=this.y,n=this.width,o=this.height;(r!==t||a!==e||n!==i||o!==s)&&this.addCommands({cmd:this.#eL.bind(this,r,a,n,o),undo:this.#eL.bind(this,t,e,i,s),mustExec:!0})}static _round(t){return Math.round(1e4*t)/1e4}#eR(t,e){let i,s,r,a,n,o,[l,h]=this.parentDimensions,d=this.x,c=this.y,u=this.width,p=this.height,g=tv.MIN_SIZE/l,f=tv.MIN_SIZE/h,m=this.#eP(this.rotation),b=(t,e)=>[m[0]*t+m[2]*e,m[1]*t+m[3]*e],A=this.#eP(360-this.rotation),v=!1,y=!1;switch(t){case"topLeft":v=!0,i=(t,e)=>[0,0],s=(t,e)=>[t,e];break;case"topMiddle":i=(t,e)=>[t/2,0],s=(t,e)=>[t/2,e];break;case"topRight":v=!0,i=(t,e)=>[t,0],s=(t,e)=>[0,e];break;case"middleRight":y=!0,i=(t,e)=>[t,e/2],s=(t,e)=>[0,e/2];break;case"bottomRight":v=!0,i=(t,e)=>[t,e],s=(t,e)=>[0,0];break;case"bottomMiddle":i=(t,e)=>[t/2,e],s=(t,e)=>[t/2,0];break;case"bottomLeft":v=!0,i=(t,e)=>[0,e],s=(t,e)=>[t,0];break;case"middleLeft":y=!0,i=(t,e)=>[0,e/2],s=(t,e)=>[t,e/2]}let w=i(u,p),_=s(u,p),x=b(..._),E=tv._round(d+x[0]),S=tv._round(c+x[1]),C=1,T=1;if(e.fromKeyboard)({deltaX:r,deltaY:a}=e);else{let{screenX:t,screenY:i}=e,[s,n]=this.#ed;[r,a]=this.screenToPageTranslation(t-s,i-n),this.#ed[0]=t,this.#ed[1]=i}if([r,a]=(n=r/l,o=a/h,[A[0]*n+A[2]*o,A[1]*n+A[3]*o]),v){let t=Math.hypot(u,p);C=T=Math.max(Math.min(Math.hypot(_[0]-w[0]-r,_[1]-w[1]-a)/t,1/u,1/p),g/u,f/p)}else y?C=$(Math.abs(_[0]-w[0]-r),g,1)/u:T=$(Math.abs(_[1]-w[1]-a),f,1)/p;let M=tv._round(u*C),P=tv._round(p*T),I=E-(x=b(...s(M,P)))[0],D=S-x[1];this.#ef||=[this.x,this.y,this.width,this.height],this.width=M,this.height=P,this.x=I,this.y=D,this.setDims(l*M,h*P),this.fixAndSetPosition(),this._onResizing()}_onResizing(){}altTextFinish(){this.#o?.finish()}async addEditToolbar(){return this._editToolbar||this.#eb||(this._editToolbar=new tl(this),this.div.append(this._editToolbar.render()),this.#o&&await this._editToolbar.addAltText(this.#o)),this._editToolbar}removeEditToolbar(){this._editToolbar&&(this._editToolbar.remove(),this._editToolbar=null,this.#o?.destroy())}addContainer(t){let e=this._editToolbar?.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){this.#o||(tb.initialize(tv._l10n),this.#o=new tb(this),this.#es&&(this.#o.data=this.#es,this.#es=null),await this.addEditToolbar())}get altTextData(){return this.#o?.data}set altTextData(t){this.#o&&(this.#o.data=t)}get guessedAltText(){return this.#o?.guessedText}async setGuessedAltText(t){await this.#o?.setGuessedText(t)}serializeAltText(t){return this.#o?.serialize(t)}hasAltText(){return!!this.#o&&!this.#o.isEmpty()}hasAltTextData(){return this.#o?.hasData()??!1}render(){let t=this.div=document.createElement("div");t.setAttribute("data-editor-rotation",(360-this.rotation)%360),t.className=this.name,t.setAttribute("id",this.id),t.tabIndex=this.#ea?-1:0,t.setAttribute("role","application"),this.defaultL10nId&&t.setAttribute("data-l10n-id",this.defaultL10nId),this._isVisible||t.classList.add("hidden"),this.setInForeground(),this.#eF();let[e,i]=this.parentDimensions;this.parentRotation%180!=0&&(t.style.maxWidth=`${(100*i/e).toFixed(2)}%`,t.style.maxHeight=`${(100*e/i).toFixed(2)}%`);let[s,r]=this.getInitialTranslation();return this.translate(s,r),td(this,t,["keydown","pointerdown","dblclick"]),this.isResizable&&this._uiManager._supportsPinchToZoom&&(this.#ex||=new tA({container:t,isPinchingDisabled:()=>!this.isSelected,onPinchStart:this.#eN.bind(this),onPinching:this.#eO.bind(this),onPinchEnd:this.#eB.bind(this),signal:this._uiManager._signal})),this._uiManager._editorUndoBar?.hide(),t}#eN(){this.#ec={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height},this.#o?.toggle(!1),this.parent.togglePointerEvents(!1)}#eO(t,e,i){let s=i/e*.7+1-.7;if(1===s)return;let r=this.#eP(this.rotation),a=(t,e)=>[r[0]*t+r[2]*e,r[1]*t+r[3]*e],[n,o]=this.parentDimensions,l=this.x,h=this.y,d=this.width,c=this.height;s=Math.max(Math.min(s,1/d,1/c),tv.MIN_SIZE/n/d,tv.MIN_SIZE/o/c);let u=tv._round(d*s),p=tv._round(c*s);if(u===d&&p===c)return;this.#ef||=[l,h,d,c];let g=a(d/2,c/2),f=tv._round(l+g[0]),m=tv._round(h+g[1]),b=a(u/2,p/2);this.x=f-b[0],this.y=m-b[1],this.width=u,this.height=p,this.setDims(n*u,o*p),this.fixAndSetPosition(),this._onResizing()}#eB(){this.#o?.toggle(!0),this.parent.togglePointerEvents(!0),this.#ek()}pointerdown(t){let{isMac:e}=F.platform;return 0!==t.button||t.ctrlKey&&e?void t.preventDefault():(this.#eg=!0,this._isDraggable)?void this.#ez(t):void this.#eH(t)}#eH(t){let{isMac:e}=F.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#ez(t){let{isSelected:e}=this;this._uiManager.setUpDragSession();let i=!1,s=new AbortController,r=this._uiManager.combinedSignal(s),a={capture:!0,passive:!1,signal:r},n=t=>{s.abort(),this.#en=null,this.#eg=!1,this._uiManager.endDragSession()||this.#eH(t),i&&this._onStopDragging()};e&&(this.#ey=t.clientX,this.#ew=t.clientY,this.#en=t.pointerId,this.#eo=t.pointerType,window.addEventListener("pointermove",t=>{i||(i=!0,this._onStartDragging());let{clientX:e,clientY:s,pointerId:r}=t;if(r!==this.#en)return void tt(t);let[a,n]=this.screenToPageTranslation(e-this.#ey,s-this.#ew);this.#ey=e,this.#ew=s,this._uiManager.dragSelectedEditors(a,n)},a),window.addEventListener("touchmove",tt,a),window.addEventListener("pointerdown",t=>{t.pointerType===this.#eo&&(this.#ex||t.isPrimary)&&n(t),tt(t)},a));let o=t=>{if(!this.#en||this.#en===t.pointerId)return void n(t);tt(t)};window.addEventListener("pointerup",o,{signal:r}),window.addEventListener("blur",o,{signal:r})}_onStartDragging(){}_onStopDragging(){}moveInDOM(){this.#ev&&clearTimeout(this.#ev),this.#ev=setTimeout(()=>{this.#ev=null,this.parent?.moveEditorInDOM(this)},0)}_setParentAndPosition(t,e,i){t.changeParent(this),this.x=e,this.y=i,this.fixAndSetPosition(),this._onTranslated()}getRect(t,e,i=this.rotation){let s=this.parentScale,[r,a]=this.pageDimensions,[n,o]=this.pageTranslation,l=t/s,h=e/s,d=this.x*r,c=this.y*a,u=this.width*r,p=this.height*a;switch(i){case 0:return[d+l+n,a-c-h-p+o,d+l+u+n,a-c-h+o];case 90:return[d+h+n,a-c+l+o,d+h+p+n,a-c+l+u+o];case 180:return[d-l-u+n,a-c+h+o,d-l+n,a-c+h+p+o];case 270:return[d-h-p+n,a-c-l-u+o,d-h+n,a-c-l+o];default:throw Error("Invalid rotation")}}getRectInCurrentCoords(t,e){let[i,s,r,a]=t,n=r-i,o=a-s;switch(this.rotation){case 0:return[i,e-a,n,o];case 90:return[i,e-s,o,n];case 180:return[r,e-s,n,o];case 270:return[r,e-a,o,n];default:throw Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){return!this.isInEditMode()&&(this.parent.setEditingState(!1),this.#eb=!0,!0)}disableEditMode(){return!!this.isInEditMode()&&(this.parent.setEditingState(!0),this.#eb=!1,!0)}isInEditMode(){return this.#eb}shouldGetKeyboardEvents(){return this.#eA}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){let{top:t,left:e,bottom:i,right:s}=this.getClientDimensions(),{innerHeight:r,innerWidth:a}=window;return e<a&&s>0&&t<r&&i>0}#eF(){if(this.#eu||!this.div)return;this.#eu=new AbortController;let t=this._uiManager.combinedSignal(this.#eu);this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t}),this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})}rebuild(){this.#eF()}rotate(t){}resize(){}serializeDeleted(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:this._initialData?.popupRef||""}}serialize(t=!1,e=null){w("An editor must be serializable")}static async deserialize(t,e,i){let s=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});s.rotation=t.rotation,s.#es=t.accessibilityData,s._isCopy=t.isCopy||!1;let[r,a]=s.pageDimensions,[n,o,l,h]=s.getRectInCurrentCoords(t.rect,a);return s.x=n/r,s.y=o/a,s.width=l/r,s.height=h/a,s}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){if(this.#eu?.abort(),this.#eu=null,this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),this.#ev&&(clearTimeout(this.#ev),this.#ev=null),this.#eC(),this.removeEditToolbar(),this.#e_){for(let t of this.#e_.values())clearTimeout(t);this.#e_=null}this.parent=null,this.#ex?.destroy(),this.#ex=null}get isResizable(){return!1}makeResizable(){this.isResizable&&(this.#eI(),this.#eh.classList.remove("hidden"))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||"Enter"!==t.key)return;this._uiManager.setSelected(this),this.#ec={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};let e=this.#eh.children;if(!this.#er){this.#er=Array.from(e);let t=this.#eU.bind(this),i=this.#e$.bind(this),s=this._uiManager._signal;for(let e of this.#er){let r=e.getAttribute("data-resizer-name");e.setAttribute("role","spinbutton"),e.addEventListener("keydown",t,{signal:s}),e.addEventListener("blur",i,{signal:s}),e.addEventListener("focus",this.#eG.bind(this,r),{signal:s}),e.setAttribute("data-l10n-id",tv._l10nResizer[r])}}let i=this.#er[0],s=0;for(let t of e){if(t===i)break;s++}let r=(360-this.rotation+this.parentRotation)%360/90*(this.#er.length/4);if(r!==s){if(r<s)for(let t=0;t<s-r;t++)this.#eh.append(this.#eh.firstChild);else if(r>s)for(let t=0;t<r-s;t++)this.#eh.firstChild.before(this.#eh.lastChild);let t=0;for(let i of e){let e=this.#er[t++].getAttribute("data-resizer-name");i.setAttribute("data-l10n-id",tv._l10nResizer[e])}}this.#ej(0),this.#eA=!0,this.#eh.firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}#eU(t){tv._resizerKeyboardManager.exec(this,t)}#e$(t){this.#eA&&t.relatedTarget?.parentNode!==this.#eh&&this.#eC()}#eG(t){this.#ep=this.#eA?t:""}#ej(t){if(this.#er)for(let e of this.#er)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#eA&&this.#eR(this.#ep,{deltaX:t,deltaY:e,fromKeyboard:!0})}#eC(){this.#eA=!1,this.#ej(-1),this.#ek()}_stopResizingWithKeyboard(){this.#eC(),this.div.focus()}select(){if(!this.isSelected||!this._editToolbar){if(this.isSelected=!0,this.makeResizable(),this.div?.classList.add("selectedEditor"),!this._editToolbar)return void this.addEditToolbar().then(()=>{this.div?.classList.contains("selectedEditor")&&this._editToolbar?.show()});this._editToolbar?.show(),this.#o?.toggleAltTextBadge(!1)}}unselect(){this.isSelected&&(this.isSelected=!1,this.#eh?.classList.add("hidden"),this.div?.classList.remove("selectedEditor"),this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),this._editToolbar?.hide(),this.#o?.toggleAltTextBadge(!0))}updateParams(t,e){}disableEditing(){}enableEditing(){}get canChangeContent(){return!1}enterInEditMode(){this.canChangeContent&&(this.enableEditMode(),this.div.focus())}dblclick(t){this.enterInEditMode(),this.parent.updateToolbar({mode:this.constructor._editorType,editId:this.id})}getElementForAltText(){return this.div}get contentDiv(){return this.div}get isEditing(){return this.#em}set isEditing(t){this.#em=t,this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){this.#el=!0;let{style:i}=this.div;i.aspectRatio=t/e,i.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#e_||=new Map;let{action:e}=t,i=this.#e_.get(e);i&&clearTimeout(i),i=setTimeout(()=>{this._reportTelemetry(t),this.#e_.delete(e),0===this.#e_.size&&(this.#e_=null)},tv._telemetryTimeout),this.#e_.set(e,i);return}t.type||=this.editorType,this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),this.#ea=!1}disable(){this.div&&(this.div.tabIndex=-1),this.#ea=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){let t=e;(e=document.createElement("div")).classList.add("annotationContent",this.editorType),t.before(e)}}else(e=document.createElement("div")).classList.add("annotationContent",this.editorType),t.container.prepend(e);return e}resetAnnotationElement(t){let{firstChild:e}=t.container;e?.nodeName==="DIV"&&e.classList.contains("annotationContent")&&e.remove()}}class ty extends tv{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return this.serializeDeleted()}}class tw{constructor(t){this.h1=t?0|t:0xc3d2e1f0,this.h2=t?0|t:0xc3d2e1f0}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length),i=0;for(let s=0,r=t.length;s<r;s++){let r=t.charCodeAt(s);r<=255?e[i++]=r:(e[i++]=r>>>8,e[i++]=255&r)}}else if(ArrayBuffer.isView(t))i=(e=t.slice()).byteLength;else throw Error("Invalid data format, must be a string or TypedArray.");let s=i>>2,r=i-4*s,a=new Uint32Array(e.buffer,0,s),n=0,o=0,l=this.h1,h=this.h2;for(let t=0;t<s;t++)1&t?(l^=n=0x1b873593*(n=(n=0xcc9e2d51*(n=a[t])&0xffff0000|11601*n&65535)<<15|n>>>17)&0xffff0000|13715*n&65535,l=5*(l=l<<13|l>>>19)+0xe6546b64):(h^=o=0x1b873593*(o=(o=0xcc9e2d51*(o=a[t])&0xffff0000|11601*o&65535)<<15|o>>>17)&0xffff0000|13715*o&65535,h=5*(h=h<<13|h>>>19)+0xe6546b64);switch(n=0,r){case 3:n^=e[4*s+2]<<16;case 2:n^=e[4*s+1]<<8;case 1:n^=e[4*s],n=0x1b873593*(n=(n=0xcc9e2d51*n&0xffff0000|11601*n&65535)<<15|n>>>17)&0xffff0000|13715*n&65535,1&s?l^=n:h^=n}this.h1=l,this.h2=h}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,e=0xff51afd7*e&0xffff0000|((e<<16|(t=0xed558ccd*t&0xffff0000|36045*t&65535)>>>16)*0xafd7ed55&0xffff0000)>>>16,t^=e>>>1,e=0xc4ceb9fe*e&0xffff0000|((e<<16|(t=0x1a85ec53*t&0xffff0000|60499*t&65535)>>>16)*0xb9fe1a85&0xffff0000)>>>16,((t^=e>>>1)>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}let t_=Object.freeze({map:null,hash:"",transfer:void 0});class tx{#eV=!1;#eW=null;#eq=new Map;constructor(){this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){let i=this.#eq.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#eq.get(t)}remove(t){if(this.#eq.delete(t),0===this.#eq.size&&this.resetModified(),"function"==typeof this.onAnnotationEditor){for(let t of this.#eq.values())if(t instanceof tv)return;this.onAnnotationEditor(null)}}setValue(t,e){let i=this.#eq.get(t),s=!1;if(void 0!==i)for(let[t,r]of Object.entries(e))i[t]!==r&&(s=!0,i[t]=r);else s=!0,this.#eq.set(t,e);s&&this.#eK(),e instanceof tv&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#eq.has(t)}get size(){return this.#eq.size}#eK(){this.#eV||(this.#eV=!0,"function"==typeof this.onSetModified&&this.onSetModified())}resetModified(){this.#eV&&(this.#eV=!1,"function"==typeof this.onResetModified&&this.onResetModified())}get print(){return new tE(this)}get serializable(){if(0===this.#eq.size)return t_;let t=new Map,e=new tw,i=[],s=Object.create(null),r=!1;for(let[i,a]of this.#eq){let n=a instanceof tv?a.serialize(!1,s):a;n&&(t.set(i,n),e.update(`${i}:${JSON.stringify(n)}`),r||=!!n.bitmap)}if(r)for(let e of t.values())e.bitmap&&i.push(e.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:i}:t_}get editorStats(){let t=null,e=new Map;for(let i of this.#eq.values()){if(!(i instanceof tv))continue;let s=i.telemetryFinalData;if(!s)continue;let{type:r}=s;e.has(r)||e.set(r,Object.getPrototypeOf(i).constructor),t||=Object.create(null);let a=t[r]||=new Map;for(let[t,e]of Object.entries(s)){if("type"===t)continue;let i=a.get(t);i||(i=new Map,a.set(t,i));let s=i.get(e)??0;i.set(e,s+1)}}for(let[i,s]of e)t[i]=s.computeTelemetryFinalData(t[i]);return t}resetModifiedIds(){this.#eW=null}get modifiedIds(){if(this.#eW)return this.#eW;let t=[];for(let e of this.#eq.values())e instanceof tv&&e.annotationElementId&&e.serialize()&&t.push(e.annotationElementId);return this.#eW={ids:new Set(t),hash:t.join(",")}}[Symbol.iterator](){return this.#eq.entries()}}class tE extends tx{#eX;constructor(t){super();let{map:e,hash:i,transfer:s}=t.serializable,r=structuredClone(e,s?{transfer:s}:null);this.#eX={map:r,hash:i,transfer:s}}get print(){w("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#eX}get modifiedIds(){return S(this,"modifiedIds",{ids:new Set,hash:""})}}class tS{#eY=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));let e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(let t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),this.#eY.clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,disableFontFace:e,_inspectFont:i}){if(!(!t||this.#eY.has(t.loadedName))){if(_(!e,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){let{loadedName:e,src:s,style:r}=t,a=new FontFace(e,s,r);this.addNativeFontFace(a);try{await a.load(),this.#eY.add(e),i?.(t)}catch{y(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(a)}return}w("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo)return void await this.loadSystemFont(t);if(this.isFontLoadingAPISupported){let e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){throw y(`Failed to load font '${e.family}': '${i}'.`),t.disableFontFace=!0,i}}return}let e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise(e=>{let i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)})}}get isFontLoadingAPISupported(){return S(this,"isFontLoadingAPISupported",!!this._document?.fonts)}get isSyncFontLoadingSupported(){return S(this,"isSyncFontLoadingSupported",a||F.platform.isFirefox)}_queueLoadingCallback(t){let{loadingRequests:e}=this,i={done:!1,complete:function(){for(_(!i.done,"completeRequest() cannot be called twice."),i.done=!0;e.length>0&&e[0].done;)setTimeout(e.shift().callback,0)},callback:t};return e.push(i),i}get _loadTestFont(){return S(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){var i;let s,r;function a(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function n(t,e,i,s){return t.substring(0,e)+s+t.substring(e+i)}let o=this._document.createElement("canvas");o.width=1,o.height=1;let l=o.getContext("2d"),h=0,d=`lt${Date.now()}${this.loadTestFontId++}`,c=this._loadTestFont,u=a(c=n(c,976,d.length,d),16);for(s=0,r=d.length-3;s<r;s+=4)u=u-0x58585858+a(d,s)|0;s<d.length&&(u=u-0x58585858+a(d+"XXX",s)|0),c=n(c,16,4,String.fromCharCode((i=u)>>24&255,i>>16&255,i>>8&255,255&i));let p=`url(data:font/opentype;base64,${btoa(c)});`,g=`@font-face {font-family:"${d}";src:${p}}`;this.insertRule(g);let f=this._document.createElement("div");for(let e of(f.style.visibility="hidden",f.style.width=f.style.height="10px",f.style.position="absolute",f.style.top=f.style.left="0px",[t.loadedName,d])){let t=this._document.createElement("span");t.textContent="Hi",t.style.fontFamily=e,f.append(t)}this._document.body.append(f),function t(e,i){if(++h>30){y("Load test font never loaded."),i();return}if(l.font="30px "+e,l.fillText(".",0,20),l.getImageData(0,0,1,1).data[3]>0)return void i();setTimeout(t.bind(null,e,i))}(d,()=>{f.remove(),e.complete()})}}class tC{constructor(t,e=null){for(let e in this.compiledGlyphs=Object.create(null),t)this[e]=t[e];this._inspectFont=e}createNativeFontFace(){let t;if(!this.data||this.disableFontFace)return null;if(this.cssFontInfo){let e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});return this._inspectFont?.(this),t}createFontFaceRule(){let t;if(!this.data||this.disableFontFace)return null;let e=`url(data:${this.mimetype};base64,${G(this.data)});`;if(this.cssFontInfo){let i=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(i+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),t=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${i}src:${e}}`}else t=`@font-face {font-family:"${this.loadedName}";src:${e}}`;return this._inspectFont?.(this,e),t}getPathGenerator(t,e){let i;if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];let s=this.loadedName+"_path_"+e;try{i=t.get(s)}catch(t){y(`getPathGenerator - ignoring character: "${t}".`)}let r=new Path2D(i||"");return this.fontExtraProperties||t.delete(s),this.compiledGlyphs[e]=r}}function tT(t){if("string"!=typeof t)return null;if(t.endsWith("/"))return t;throw Error(`Invalid factory url: "${t}" must include trailing slash.`)}let tM=t=>"object"==typeof t&&Number.isInteger(t?.num)&&t.num>=0&&Number.isInteger(t?.gen)&&t.gen>=0,tP=(function(t,e,i){if(!Array.isArray(i)||i.length<2)return!1;let[s,r,...a]=i;if(!t(s)&&!Number.isInteger(s)||!e(r))return!1;let n=a.length,o=!0;switch(r.name){case"XYZ":if(n<2||n>3)return!1;break;case"Fit":case"FitB":return 0===n;case"FitH":case"FitBH":case"FitV":case"FitBV":if(n>1)return!1;break;case"FitR":if(4!==n)return!1;o=!1;break;default:return!1}for(let t of a)if("number"!=typeof t&&(!o||null!==t))return!1;return!0}).bind(null,tM,t=>"object"==typeof t&&"string"==typeof t?.name);class tI{#eQ=new Map;#eJ=Promise.resolve();postMessage(t,e){let i={data:structuredClone(t,e?{transfer:e}:null)};this.#eJ.then(()=>{for(let[t]of this.#eQ)t.call(this,i)})}addEventListener(t,e,i=null){let s=null;if(i?.signal instanceof AbortSignal){let{signal:r}=i;if(r.aborted)return void y("LoopbackPort - cannot use an `aborted` signal.");let a=()=>this.removeEventListener(t,e);s=()=>r.removeEventListener("abort",a),r.addEventListener("abort",a)}this.#eQ.set(e,s)}removeEventListener(t,e){let i=this.#eQ.get(e);i?.(),this.#eQ.delete(e)}terminate(){for(let[,t]of this.#eQ)t?.();this.#eQ.clear()}}let tD={DATA:1,ERROR:2},tR={CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function tk(){}function tL(t){if(t instanceof R||t instanceof P||t instanceof T||t instanceof I||t instanceof M)return t;switch(!(t instanceof Error||"object"==typeof t&&null!==t)&&w('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),t.name){case"AbortException":return new R(t.message);case"InvalidPDFException":return new P(t.message);case"PasswordException":return new T(t.message,t.code);case"ResponseException":return new I(t.message,t.status,t.missing);case"UnknownErrorException":return new M(t.message,t.details)}return new M(t.message,t.toString())}class tF{#eZ=new AbortController;constructor(t,e,i){this.sourceName=t,this.targetName=e,this.comObj=i,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),i.addEventListener("message",this.#e0.bind(this),{signal:this.#eZ.signal})}#e0({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream)return void this.#e1(t);if(t.callback){let e=t.callbackId,i=this.callbackCapabilities[e];if(!i)throw Error(`Cannot resolve callback ${e}`);if(delete this.callbackCapabilities[e],t.callback===tD.DATA)i.resolve(t.data);else if(t.callback===tD.ERROR)i.reject(tL(t.reason));else throw Error("Unexpected callback case");return}let e=this.actionHandler[t.action];if(!e)throw Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){let i=this.sourceName,s=t.sourceName,r=this.comObj;Promise.try(e,t.data).then(function(e){r.postMessage({sourceName:i,targetName:s,callback:tD.DATA,callbackId:t.callbackId,data:e})},function(e){r.postMessage({sourceName:i,targetName:s,callback:tD.ERROR,callbackId:t.callbackId,reason:tL(e)})});return}if(t.streamId)return void this.#e2(t);e(t.data)}on(t,e){let i=this.actionHandler;if(i[t])throw Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){let s=this.callbackId++,r=Promise.withResolvers();this.callbackCapabilities[s]=r;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(t){r.reject(t)}return r.promise}sendWithStream(t,e,i,s){let r=this.streamId++,a=this.sourceName,n=this.targetName,o=this.comObj;return new ReadableStream({start:i=>{let l=Promise.withResolvers();return this.streamControllers[r]={controller:i,startCall:l,pullCall:null,cancelCall:null,isClosed:!1},o.postMessage({sourceName:a,targetName:n,action:t,streamId:r,data:e,desiredSize:i.desiredSize},s),l.promise},pull:t=>{let e=Promise.withResolvers();return this.streamControllers[r].pullCall=e,o.postMessage({sourceName:a,targetName:n,stream:tR.PULL,streamId:r,desiredSize:t.desiredSize}),e.promise},cancel:t=>{_(t instanceof Error,"cancel must have a valid reason");let e=Promise.withResolvers();return this.streamControllers[r].cancelCall=e,this.streamControllers[r].isClosed=!0,o.postMessage({sourceName:a,targetName:n,stream:tR.CANCEL,streamId:r,reason:tL(t)}),e.promise}},i)}#e2(t){let e=t.streamId,i=this.sourceName,s=t.sourceName,r=this.comObj,a=this,n=this.actionHandler[t.action],o={enqueue(t,a=1,n){if(this.isCancelled)return;let o=this.desiredSize;this.desiredSize-=a,o>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),r.postMessage({sourceName:i,targetName:s,stream:tR.ENQUEUE,streamId:e,chunk:t},n)},close(){this.isCancelled||(this.isCancelled=!0,r.postMessage({sourceName:i,targetName:s,stream:tR.CLOSE,streamId:e}),delete a.streamSinks[e])},error(t){_(t instanceof Error,"error must have a valid reason"),this.isCancelled||(this.isCancelled=!0,r.postMessage({sourceName:i,targetName:s,stream:tR.ERROR,streamId:e,reason:tL(t)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve(),o.ready=o.sinkCapability.promise,this.streamSinks[e]=o,Promise.try(n,t.data,o).then(function(){r.postMessage({sourceName:i,targetName:s,stream:tR.START_COMPLETE,streamId:e,success:!0})},function(t){r.postMessage({sourceName:i,targetName:s,stream:tR.START_COMPLETE,streamId:e,reason:tL(t)})})}#e1(t){let e=t.streamId,i=this.sourceName,s=t.sourceName,r=this.comObj,a=this.streamControllers[e],n=this.streamSinks[e];switch(t.stream){case tR.START_COMPLETE:t.success?a.startCall.resolve():a.startCall.reject(tL(t.reason));break;case tR.PULL_COMPLETE:t.success?a.pullCall.resolve():a.pullCall.reject(tL(t.reason));break;case tR.PULL:if(!n){r.postMessage({sourceName:i,targetName:s,stream:tR.PULL_COMPLETE,streamId:e,success:!0});break}n.desiredSize<=0&&t.desiredSize>0&&n.sinkCapability.resolve(),n.desiredSize=t.desiredSize,Promise.try(n.onPull||tk).then(function(){r.postMessage({sourceName:i,targetName:s,stream:tR.PULL_COMPLETE,streamId:e,success:!0})},function(t){r.postMessage({sourceName:i,targetName:s,stream:tR.PULL_COMPLETE,streamId:e,reason:tL(t)})});break;case tR.ENQUEUE:if(_(a,"enqueue should have stream controller"),a.isClosed)break;a.controller.enqueue(t.chunk);break;case tR.CLOSE:if(_(a,"close should have stream controller"),a.isClosed)break;a.isClosed=!0,a.controller.close(),this.#e3(a,e);break;case tR.ERROR:_(a,"error should have stream controller"),a.controller.error(tL(t.reason)),this.#e3(a,e);break;case tR.CANCEL_COMPLETE:t.success?a.cancelCall.resolve():a.cancelCall.reject(tL(t.reason)),this.#e3(a,e);break;case tR.CANCEL:if(!n)break;let o=tL(t.reason);Promise.try(n.onCancel||tk,o).then(function(){r.postMessage({sourceName:i,targetName:s,stream:tR.CANCEL_COMPLETE,streamId:e,success:!0})},function(t){r.postMessage({sourceName:i,targetName:s,stream:tR.CANCEL_COMPLETE,streamId:e,reason:tL(t)})}),n.sinkCapability.reject(o),n.isCancelled=!0,delete this.streamSinks[e];break;default:throw Error("Unexpected stream case")}}async #e3(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]),delete this.streamControllers[e]}destroy(){this.#eZ?.abort(),this.#eZ=null}}class tN{#e5=!1;constructor({enableHWA:t=!1}){this.#e5=t}create(t,e){if(t<=0||e<=0)throw Error("Invalid canvas size");let i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d",{willReadFrequently:!this.#e5})}}reset(t,e,i){if(!t.canvas)throw Error("Canvas is not specified");if(e<=0||i<=0)throw Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=i}destroy(t){if(!t.canvas)throw Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){w("Abstract method `_createCanvas` called.")}}class tO extends tN{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){let i=this._document.createElement("canvas");return i.width=t,i.height=e,i}}class tB{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw Error("CMap name must be specified.");let e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then(t=>({cMapData:t,isCompressed:this.isCompressed})).catch(t=>{throw Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)})}async _fetch(t){w("Abstract method `_fetch` called.")}}class tz extends tB{async _fetch(t){let e=await W(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):L(e)}}class tH{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,i,s,r){return"none"}destroy(t=!1){}}class tU extends tH{#e6;#e4;#e8;#e7;#e9;#it;#y=0;constructor({docId:t,ownerDocument:e=globalThis.document}){super(),this.#e7=t,this.#e9=e}get #_(){return this.#e4||=new Map}get #ie(){return this.#it||=new Map}get #ii(){if(!this.#e8){let t=this.#e9.createElement("div"),{style:e}=t;e.visibility="hidden",e.contain="strict",e.width=e.height=0,e.position="absolute",e.top=e.left=0,e.zIndex=-1;let i=this.#e9.createElementNS(j,"svg");i.setAttribute("width",0),i.setAttribute("height",0),this.#e8=this.#e9.createElementNS(j,"defs"),t.append(i),i.append(this.#e8),this.#e9.body.append(t)}return this.#e8}#is(t){if(1===t.length){let e=t[0],i=Array(256);for(let t=0;t<256;t++)i[t]=e[t]/255;let s=i.join(",");return[s,s,s]}let[e,i,s]=t,r=Array(256),a=Array(256),n=Array(256);for(let t=0;t<256;t++)r[t]=e[t]/255,a[t]=i[t]/255,n[t]=s[t]/255;return[r.join(","),a.join(","),n.join(",")]}#ir(t){if(void 0===this.#e6){this.#e6="";let t=this.#e9.URL;t!==this.#e9.baseURI&&(X(t)?y('#createUrl: ignore "data:"-URL for performance reasons.'):this.#e6=E(t,""))}return`url(${this.#e6}#${t})`}addFilter(t){if(!t)return"none";let e=this.#_.get(t);if(e)return e;let[i,s,r]=this.#is(t),a=1===t.length?i:`${i}${s}${r}`;if(e=this.#_.get(a))return this.#_.set(t,e),e;let n=`g_${this.#e7}_transfer_map_${this.#y++}`,o=this.#ir(n);this.#_.set(t,o),this.#_.set(a,o);let l=this.#ia(n);return this.#io(i,s,r,l),o}addHCMFilter(t,e){let i=`${t}-${e}`,s="base",r=this.#ie.get(s);if(r?.key===i||(r?(r.filter?.remove(),r.key=i,r.url="none",r.filter=null):(r={key:i,url:"none",filter:null},this.#ie.set(s,r)),!t||!e))return r.url;let a=this.#il(t);t=O.makeHexColor(...a);let n=this.#il(e);if(e=O.makeHexColor(...n),this.#ii.style.color="","#000000"===t&&"#ffffff"===e||t===e)return r.url;let o=Array(256);for(let t=0;t<=255;t++){let e=t/255;o[t]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}let l=o.join(","),h=`g_${this.#e7}_hcm_filter`,d=r.filter=this.#ia(h);this.#io(l,l,l,d),this.#ih(d);let c=(t,e)=>{let i=a[t]/255,s=n[t]/255,r=Array(e+1);for(let t=0;t<=e;t++)r[t]=i+t/e*(s-i);return r.join(",")};return this.#io(c(0,5),c(1,5),c(2,5),d),r.url=this.#ir(h),r.url}addAlphaFilter(t){let e=this.#_.get(t);if(e)return e;let[i]=this.#is([t]),s=`alpha_${i}`;if(e=this.#_.get(s))return this.#_.set(t,e),e;let r=`g_${this.#e7}_alpha_map_${this.#y++}`,a=this.#ir(r);this.#_.set(t,a),this.#_.set(s,a);let n=this.#ia(r);return this.#id(i,n),a}addLuminosityFilter(t){let e,i,s=this.#_.get(t||"luminosity");if(s)return s;if(t?([e]=this.#is([t]),i=`luminosity_${e}`):i="luminosity",s=this.#_.get(i))return this.#_.set(t,s),s;let r=`g_${this.#e7}_luminosity_map_${this.#y++}`,a=this.#ir(r);this.#_.set(t,a),this.#_.set(i,a);let n=this.#ia(r);return this.#ic(n),t&&this.#id(e,n),a}addHighlightHCMFilter(t,e,i,s,r){let a=`${e}-${i}-${s}-${r}`,n=this.#ie.get(t);if(n?.key===a||(n?(n.filter?.remove(),n.key=a,n.url="none",n.filter=null):(n={key:a,url:"none",filter:null},this.#ie.set(t,n)),!e||!i))return n.url;let[o,l]=[e,i].map(this.#il.bind(this)),h=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),d=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),[c,u]=[s,r].map(this.#il.bind(this));d<h&&([h,d,c,u]=[d,h,u,c]),this.#ii.style.color="";let p=(t,e,i)=>{let s=Array(256),r=(d-h)/i,a=t/255,n=(e-t)/(255*i),o=0;for(let t=0;t<=i;t++){let e=Math.round(h+t*r),i=a+t*n;for(let t=o;t<=e;t++)s[t]=i;o=e+1}for(let t=o;t<256;t++)s[t]=s[o-1];return s.join(",")},g=`g_${this.#e7}_hcm_${t}_filter`,f=n.filter=this.#ia(g);return this.#ih(f),this.#io(p(c[0],u[0],5),p(c[1],u[1],5),p(c[2],u[2],5),f),n.url=this.#ir(g),n.url}destroy(t=!1){t&&this.#it?.size||(this.#e8?.parentNode.parentNode.remove(),this.#e8=null,this.#e4?.clear(),this.#e4=null,this.#it?.clear(),this.#it=null,this.#y=0)}#ic(t){let e=this.#e9.createElementNS(j,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),t.append(e)}#ih(t){let e=this.#e9.createElementNS(j,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),t.append(e)}#ia(t){let e=this.#e9.createElementNS(j,"filter");return e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("id",t),this.#ii.append(e),e}#iu(t,e,i){let s=this.#e9.createElementNS(j,e);s.setAttribute("type","discrete"),s.setAttribute("tableValues",i),t.append(s)}#io(t,e,i,s){let r=this.#e9.createElementNS(j,"feComponentTransfer");s.append(r),this.#iu(r,"feFuncR",t),this.#iu(r,"feFuncG",e),this.#iu(r,"feFuncB",i)}#id(t,e){let i=this.#e9.createElementNS(j,"feComponentTransfer");e.append(i),this.#iu(i,"feFuncA",t)}#il(t){return this.#ii.style.color=t,ti(getComputedStyle(this.#ii).getPropertyValue("color"))}}class t${constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw Error("Font filename must be specified.");let e=`${this.baseUrl}${t}`;return this._fetch(e).catch(t=>{throw Error(`Unable to load font data at: ${e}`)})}async _fetch(t){w("Abstract method `_fetch` called.")}}class tG extends t${async _fetch(t){return new Uint8Array(await W(t,"arraybuffer"))}}class tj{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw Error("Ensure that the `wasmUrl` API parameter is provided.");if(!t)throw Error("Wasm filename must be specified.");let e=`${this.baseUrl}${t}`;return this._fetch(e).catch(t=>{throw Error(`Unable to load wasm data at: ${e}`)})}async _fetch(t){w("Abstract method `_fetch` called.")}}class tV extends tj{async _fetch(t){return new Uint8Array(await W(t,"arraybuffer"))}}async function tW(t){let e=s.getBuiltinModule("fs");return new Uint8Array(await e.promises.readFile(t))}a&&y("Please use the `legacy` build in Node.js environments.");class tq extends tH{}class tK extends tN{_createCanvas(t,e){return s.getBuiltinModule("module").createRequire("file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/node_modules/pdfjs-dist/build/pdf.mjs")("@napi-rs/canvas").createCanvas(t,e)}}class tX extends tB{async _fetch(t){return tW(t)}}class tY extends t${async _fetch(t){return tW(t)}}class tQ extends tj{async _fetch(t){return tW(t)}}let tJ={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function tZ(t,e){if(!e)return;let i=e[2]-e[0],s=e[3]-e[1],r=new Path2D;r.rect(e[0],e[1],i,s),t.clip(r)}class t0{isModifyingCurrentTransform(){return!1}getPattern(){w("Abstract method `getPattern` called.")}}class t1 extends t0{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;for(let i of("axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1)),this._colorStops))e.addColorStop(i[0],i[1]);return e}getPattern(t,e,i,s){let r;if(s===tJ.STROKE||s===tJ.FILL){let a=e.current.getClippedPathBoundingBox(s,ts(t))||[0,0,0,0],n=Math.ceil(a[2]-a[0])||1,o=Math.ceil(a[3]-a[1])||1,l=e.cachedCanvases.getCanvas("pattern",n,o),h=l.context;h.clearRect(0,0,h.canvas.width,h.canvas.height),h.beginPath(),h.rect(0,0,h.canvas.width,h.canvas.height),h.translate(-a[0],-a[1]),i=O.transform(i,[1,0,0,1,a[0],a[1]]),h.transform(...e.baseTransform),this.matrix&&h.transform(...this.matrix),tZ(h,this._bbox),h.fillStyle=this._createGradient(h),h.fill(),r=t.createPattern(l.canvas,"no-repeat");let d=new DOMMatrix(i);r.setTransform(d)}else tZ(t,this._bbox),r=this._createGradient(t);return r}}function t2(t,e,i,s,r,a,n,o){let l,h,d,c,u,p,g,f,m,b=e.coords,A=e.colors,v=t.data,y=4*t.width;b[i+1]>b[s+1]&&(l=i,i=s,s=l,l=a,a=n,n=l),b[s+1]>b[r+1]&&(l=s,s=r,r=l,l=n,n=o,o=l),b[i+1]>b[s+1]&&(l=i,i=s,s=l,l=a,a=n,n=l);let w=(b[i]+e.offsetX)*e.scaleX,_=(b[i+1]+e.offsetY)*e.scaleY,x=(b[s]+e.offsetX)*e.scaleX,E=(b[s+1]+e.offsetY)*e.scaleY,S=(b[r]+e.offsetX)*e.scaleX,C=(b[r+1]+e.offsetY)*e.scaleY;if(_>=C)return;let T=A[a],M=A[a+1],P=A[a+2],I=A[n],D=A[n+1],R=A[n+2],k=A[o],L=A[o+1],F=A[o+2],N=Math.round(_),O=Math.round(C);for(let t=N;t<=O;t++){let e;if(t<E){let e=t<_?0:(_-t)/(_-E);h=w-(w-x)*e,d=T-(T-I)*e,c=M-(M-D)*e,u=P-(P-R)*e}else{let e;h=x-(x-S)*(e=t>C?1:E===C?0:(E-t)/(E-C)),d=I-(I-k)*e,c=D-(D-L)*e,u=R-(R-F)*e}p=w-(w-S)*(e=t<_?0:t>C?1:(_-t)/(_-C)),g=T-(T-k)*e,f=M-(M-L)*e,m=P-(P-F)*e;let i=Math.round(Math.min(h,p)),s=Math.round(Math.max(h,p)),r=y*t+4*i;for(let t=i;t<=s;t++)(e=(h-t)/(h-p))<0?e=0:e>1&&(e=1),v[r++]=d-(d-g)*e|0,v[r++]=c-(c-f)*e|0,v[r++]=u-(u-m)*e|0,v[r++]=255}}class t3 extends t0{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[6],this._background=t[7],this.matrix=null}_createMeshCanvas(t,e,i){let s=Math.floor(this._bounds[0]),r=Math.floor(this._bounds[1]),a=Math.ceil(this._bounds[2])-s,n=Math.ceil(this._bounds[3])-r,o=Math.min(Math.ceil(Math.abs(a*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(n*t[1]*1.1)),3e3),h=a/o,d=n/l,c={coords:this._coords,colors:this._colors,offsetX:-s,offsetY:-r,scaleX:1/h,scaleY:1/d},u=o+4,p=l+4,g=i.getCanvas("mesh",u,p),f=g.context,m=f.createImageData(o,l);if(e){let t=m.data;for(let i=0,s=t.length;i<s;i+=4)t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=255}for(let t of this._figures)!function(t,e,i){let s,r,a=e.coords,n=e.colors;switch(e.type){case"lattice":let o=e.verticesPerRow,l=Math.floor(a.length/o)-1,h=o-1;for(s=0;s<l;s++){let e=s*o;for(let s=0;s<h;s++,e++)t2(t,i,a[e],a[e+1],a[e+o],n[e],n[e+1],n[e+o]),t2(t,i,a[e+o+1],a[e+1],a[e+o],n[e+o+1],n[e+1],n[e+o])}break;case"triangles":for(s=0,r=a.length;s<r;s+=3)t2(t,i,a[s],a[s+1],a[s+2],n[s],n[s+1],n[s+2]);break;default:throw Error("illegal figure")}}(m,t,c);return f.putImageData(m,2,2),{canvas:g.canvas,offsetX:s-2*h,offsetY:r-2*d,scaleX:h,scaleY:d}}isModifyingCurrentTransform(){return!0}getPattern(t,e,i,s){tZ(t,this._bbox);let r=new Float32Array(2);if(s===tJ.SHADING)O.singularValueDecompose2dScale(ts(t),r);else if(this.matrix){O.singularValueDecompose2dScale(this.matrix,r);let[t,i]=r;O.singularValueDecompose2dScale(e.baseTransform,r),r[0]*=t,r[1]*=i}else O.singularValueDecompose2dScale(e.baseTransform,r);let a=this._createMeshCanvas(r,s===tJ.SHADING?null:this._background,e.cachedCanvases);return s!==tJ.SHADING&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(a.offsetX,a.offsetY),t.scale(a.scaleX,a.scaleY),t.createPattern(a.canvas,"no-repeat")}}class t5 extends t0{getPattern(){return"hotpink"}}let t6={COLORED:1,UNCOLORED:2};class t4{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,s){this.color=t[1],this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.ctx=e,this.canvasGraphicsFactory=i,this.baseTransform=s}createPatternCanvas(t){let{bbox:e,operatorList:i,paintType:s,tilingType:r,color:a,canvasGraphicsFactory:n}=this,{xstep:o,ystep:l}=this;o=Math.abs(o),l=Math.abs(l),v("TilingType: "+r);let h=e[0],d=e[1],c=e[2],u=e[3],p=c-h,g=u-d,f=new Float32Array(2);O.singularValueDecompose2dScale(this.matrix,f);let[m,b]=f;O.singularValueDecompose2dScale(this.baseTransform,f);let A=m*f[0],y=b*f[1],w=p,_=g,x=!1,E=!1,S=Math.ceil(l*y),C=Math.ceil(g*y);Math.ceil(o*A)>=Math.ceil(p*A)?w=o:x=!0,S>=C?_=l:E=!0;let T=this.getSizeAndScale(w,this.ctx.canvas.width,A),M=this.getSizeAndScale(_,this.ctx.canvas.height,y),P=t.cachedCanvases.getCanvas("pattern",T.size,M.size),I=P.context,D=n.createCanvasGraphics(I);if(D.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(D,s,a),I.translate(-T.scale*h,-M.scale*d),D.transform(T.scale,0,0,M.scale,0,0),I.save(),this.clipBbox(D,h,d,c,u),D.baseTransform=ts(D.ctx),D.executeOperatorList(i),D.endDrawing(),I.restore(),x||E){let e=P.canvas;x&&(w=o),E&&(_=l);let i=this.getSizeAndScale(w,this.ctx.canvas.width,A),s=this.getSizeAndScale(_,this.ctx.canvas.height,y),r=i.size,a=s.size,n=t.cachedCanvases.getCanvas("pattern-workaround",r,a),c=n.context,u=x?Math.floor(p/o):0,f=E?Math.floor(g/l):0;for(let t=0;t<=u;t++)for(let i=0;i<=f;i++)c.drawImage(e,r*t,a*i,r,a,0,0,r,a);return{canvas:n.canvas,scaleX:i.scale,scaleY:s.scale,offsetX:h,offsetY:d}}return{canvas:P.canvas,scaleX:T.scale,scaleY:M.scale,offsetX:h,offsetY:d}}getSizeAndScale(t,e,i){let s=Math.max(t4.MAX_PATTERN_SIZE,e),r=Math.ceil(t*i);return r>=s?r=s:i=r/t,{scale:i,size:r}}clipBbox(t,e,i,s,r){let a=s-e,n=r-i;t.ctx.rect(e,i,a,n),O.axialAlignedBoundingBox([e,i,s,r],ts(t.ctx),t.current.minMax),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,i){let s=t.ctx,r=t.current;switch(e){case t6.COLORED:let{fillStyle:a,strokeStyle:n}=this.ctx;s.fillStyle=r.fillColor=a,s.strokeStyle=r.strokeColor=n;break;case t6.UNCOLORED:s.fillStyle=s.strokeStyle=i,r.fillColor=r.strokeColor=i;break;default:throw new D(`Unsupported paint type: ${e}`)}}isModifyingCurrentTransform(){return!1}getPattern(t,e,i,s){let r=i;s!==tJ.SHADING&&(r=O.transform(r,e.baseTransform),this.matrix&&(r=O.transform(r,this.matrix)));let a=this.createPatternCanvas(e),n=new DOMMatrix(r);n=(n=n.translate(a.offsetX,a.offsetY)).scale(1/a.scaleX,1/a.scaleY);let o=t.createPattern(a.canvas,"repeat");return o.setTransform(n),o}}let t8=new DOMMatrix,t7=new Float32Array(2),t9=new Float32Array([1/0,1/0,-1/0,-1/0]);class et{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,i){let s;return void 0!==this.cache[t]?(s=this.cache[t],this.canvasFactory.reset(s,e,i)):(s=this.canvasFactory.create(e,i),this.cache[t]=s),s}delete(t){delete this.cache[t]}clear(){for(let t in this.cache){let e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function ee(t,e,i,s,r,a,n,o,l,h){let[d,c,u,p,g,f]=ts(t);if(0===c&&0===u){let m=Math.round(n*d+g),b=Math.round(o*p+f),A=Math.abs(Math.round((n+l)*d+g)-m)||1,v=Math.abs(Math.round((o+h)*p+f)-b)||1;return t.setTransform(Math.sign(d),0,0,Math.sign(p),m,b),t.drawImage(e,i,s,r,a,0,0,A,v),t.setTransform(d,c,u,p,g,f),[A,v]}if(0===d&&0===p){let m=Math.round(o*u+g),b=Math.round(n*c+f),A=Math.abs(Math.round((o+h)*u+g)-m)||1,v=Math.abs(Math.round((n+l)*c+f)-b)||1;return t.setTransform(0,Math.sign(c),Math.sign(u),0,m,b),t.drawImage(e,i,s,r,a,0,0,v,A),t.setTransform(d,c,u,p,g,f),[v,A]}return t.drawImage(e,i,s,r,a,n,o,l,h),[Math.hypot(d,c)*l,Math.hypot(u,p)*h]}class ei{alphaIsShape=!1;fontSize=0;fontSizeScale=1;textMatrix=null;textMatrixScale=1;fontMatrix=n;leading=0;x=0;y=0;lineX=0;lineY=0;charSpacing=0;wordSpacing=0;textHScale=1;textRenderingMode=c.FILL;textRise=0;fillColor="#000000";strokeColor="#000000";patternFill=!1;patternStroke=!1;fillAlpha=1;strokeAlpha=1;lineWidth=1;activeSMask=null;transferMaps="none";constructor(t,e){this.clipBox=new Float32Array([0,0,t,e]),this.minMax=t9.slice()}clone(){let t=Object.create(this);return t.clipBox=this.clipBox.slice(),t.minMax=this.minMax.slice(),t}getPathBoundingBox(t=tJ.FILL,e=null){let i=this.minMax.slice();if(t===tJ.STROKE){e||w("Stroke bounding box must include transform."),O.singularValueDecompose2dScale(e,t7);let t=t7[0]*this.lineWidth/2,s=t7[1]*this.lineWidth/2;i[0]-=t,i[1]-=s,i[2]+=t,i[3]+=s}return i}updateClipFromPath(){let t=O.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minMax[0]===1/0}startNewPathAndClipBox(t){this.clipBox.set(t,0),this.minMax.set(t9,0)}getClippedPathBoundingBox(t=tJ.FILL,e=null){return O.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function es(t,e){let i,s,r,a;if(e instanceof ImageData)return void t.putImageData(e,0,0);let n=e.height,o=e.width,l=n%16,h=(n-l)/16,d=0===l?h:h+1,c=t.createImageData(o,16),p=0,g,f=e.data,m=c.data;if(e.kind===u.GRAYSCALE_1BPP){let e=f.byteLength,a=new Uint32Array(m.buffer,0,m.byteLength>>2),n=a.length,u=o+7>>3,b=F.isLittleEndian?0xff000000:255;for(i=0;i<d;i++){for(s=0,r=i<h?16:l,g=0;s<r;s++){let t=e-p,i=0,s=t>u?o:8*t-7,r=-8&s,n=0,l=0;for(;i<r;i+=8)l=f[p++],a[g++]=128&l?0xffffffff:b,a[g++]=64&l?0xffffffff:b,a[g++]=32&l?0xffffffff:b,a[g++]=16&l?0xffffffff:b,a[g++]=8&l?0xffffffff:b,a[g++]=4&l?0xffffffff:b,a[g++]=2&l?0xffffffff:b,a[g++]=1&l?0xffffffff:b;for(;i<s;i++)0===n&&(l=f[p++],n=128),a[g++]=l&n?0xffffffff:b,n>>=1}for(;g<n;)a[g++]=0;t.putImageData(c,0,16*i)}}else if(e.kind===u.RGBA_32BPP){for(i=0,s=0,a=16*o*4;i<h;i++)m.set(f.subarray(p,p+a)),p+=a,t.putImageData(c,0,s),s+=16;i<d&&(a=o*l*4,m.set(f.subarray(p,p+a)),t.putImageData(c,0,s))}else if(e.kind===u.RGB_24BPP)for(i=0,a=o*(r=16);i<d;i++){for(i>=h&&(a=o*(r=l)),g=0,s=a;s--;)m[g++]=f[p++],m[g++]=f[p++],m[g++]=f[p++],m[g++]=255;t.putImageData(c,0,16*i)}else throw Error(`bad image kind: ${e.kind}`)}function er(t,e){if(e.bitmap)return void t.drawImage(e.bitmap,0,0);let i=e.height,s=e.width,r=i%16,a=(i-r)/16,n=0===r?a:a+1,o=t.createImageData(s,16),l=0,h=e.data,d=o.data;for(let e=0;e<n;e++){let i=e<a?16:r;({srcPos:l}=function({src:t,srcPos:e=0,dest:i,width:s,height:r,nonBlackColor:a=0xffffffff,inverseDecode:n=!1}){let o=F.isLittleEndian?0xff000000:255,[l,h]=n?[a,o]:[o,a],d=s>>3,c=7&s,u=t.length;i=new Uint32Array(i.buffer);let p=0;for(let s=0;s<r;s++){for(let s=e+d;e<s;e++){let s=e<u?t[e]:255;i[p++]=128&s?h:l,i[p++]=64&s?h:l,i[p++]=32&s?h:l,i[p++]=16&s?h:l,i[p++]=8&s?h:l,i[p++]=4&s?h:l,i[p++]=2&s?h:l,i[p++]=1&s?h:l}if(0===c)continue;let s=e<u?t[e++]:255;for(let t=0;t<c;t++)i[p++]=s&1<<7-t?h:l}return{srcPos:e,destPos:p}}({src:h,srcPos:l,dest:d,width:s,height:i,nonBlackColor:0})),t.putImageData(o,0,16*e)}}function ea(t,e){for(let i of["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"])void 0!==t[i]&&(e[i]=t[i]);void 0!==t.setLineDash&&(e.setLineDash(t.getLineDash()),e.lineDashOffset=t.lineDashOffset)}function en(t){t.strokeStyle=t.fillStyle="#000000",t.fillRule="nonzero",t.globalAlpha=1,t.lineWidth=1,t.lineCap="butt",t.lineJoin="miter",t.miterLimit=10,t.globalCompositeOperation="source-over",t.font="10px sans-serif",void 0!==t.setLineDash&&(t.setLineDash([]),t.lineDashOffset=0);let{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}function eo(t,e){if(e)return!0;O.singularValueDecompose2dScale(t,t7);let i=Math.fround(tn.pixelRatio*V.PDF_TO_CSS_UNITS);return t7[0]<=i&&t7[1]<=i}let el=["butt","round","square"],eh=["miter","round","bevel"],ed={},ec={};class eu{constructor(t,e,i,s,r,{optionalContentConfig:a,markedContentStack:n=null},o,l){this.ctx=t,this.current=new ei(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=i,this.canvasFactory=s,this.filterFactory=r,this.groupStack=[],this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=n||[],this.optionalContentConfig=a,this.cachedCanvases=new et(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=o,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=l,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:i=!1,background:s=null}){let r=this.ctx.canvas.width,a=this.ctx.canvas.height,n=this.ctx.fillStyle;if(this.ctx.fillStyle=s||"#ffffff",this.ctx.fillRect(0,0,r,a),this.ctx.fillStyle=n,i){let t=this.cachedCanvases.getCanvas("transparent",r,a);this.compositeCtx=this.ctx,this.transparentCanvas=t.canvas,this.ctx=t.context,this.ctx.save(),this.ctx.transform(...ts(this.compositeCtx))}this.ctx.save(),en(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=ts(this.ctx)}executeOperatorList(t,e,i,s){let r,a=t.argsArray,n=t.fnArray,o=e||0,l=a.length;if(l===o)return o;let h=l-o>10&&"function"==typeof i,d=h?Date.now()+15:0,c=0,u=this.commonObjs,p=this.objs;for(;;){if(void 0!==s&&o===s.nextBreakPoint)return s.breakIt(o,i),o;if((r=n[o])!==m.dependency)this[r].apply(this,a[o]);else for(let t of a[o]){let e=t.startsWith("g_")?u:p;if(!e.has(t))return e.get(t,i),o}if(++o===l)return o;if(h&&++c>10){if(Date.now()>d)return i(),o;c=0}}}#ip(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null,this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)}endDrawing(){for(let t of(this.#ip(),this.cachedCanvases.clear(),this.cachedPatterns.clear(),this._cachedBitmapsMap.values())){for(let e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),this.#ig()}#ig(){if(this.pageColors){let t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){let e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}}_scaleImage(t,e){let i,s,r=t.width??t.displayWidth,a=t.height??t.displayHeight,n=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=r,h=a,d="prescale1";for(;n>2&&l>1||o>2&&h>1;){let e=l,r=h;n>2&&l>1&&(e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2),n/=l/e),o>2&&h>1&&(r=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h)/2,o/=h/r),(s=(i=this.cachedCanvases.getCanvas(d,e,r)).context).clearRect(0,0,e,r),s.drawImage(t,0,0,l,h,0,0,e,r),t=i.canvas,l=e,h=r,d="prescale1"===d?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:h}}_createMaskCanvas(t){let e,i,s,r,a=this.ctx,{width:n,height:o}=t,l=this.current.fillColor,h=this.current.patternFill,d=ts(a);if((t.bitmap||t.data)&&t.count>1){let r=t.bitmap||t.data.buffer;i=JSON.stringify(h?d:[d.slice(0,4),l]),(e=this._cachedBitmapsMap.get(r))||(e=new Map,this._cachedBitmapsMap.set(r,e));let a=e.get(i);if(a&&!h)return{canvas:a,offsetX:Math.round(Math.min(d[0],d[2])+d[4]),offsetY:Math.round(Math.min(d[1],d[3])+d[5])};s=a}s||er((r=this.cachedCanvases.getCanvas("maskCanvas",n,o)).context,t);let c=O.transform(d,[1/n,0,0,-1/o,0,0]);c=O.transform(c,[1,0,0,1,0,-o]);let u=t9.slice();O.axialAlignedBoundingBox([0,0,n,o],c,u);let[p,g,f,m]=u,b=Math.round(f-p)||1,A=Math.round(m-g)||1,v=this.cachedCanvases.getCanvas("fillCanvas",b,A),y=v.context;y.translate(-p,-g),y.transform(...c),!s&&(s=(s=this._scaleImage(r.canvas,tr(y))).img,e&&h&&e.set(i,s)),y.imageSmoothingEnabled=eo(ts(y),t.interpolate),ee(y,s,0,0,s.width,s.height,0,0,n,o),y.globalCompositeOperation="source-in";let w=O.transform(tr(y),[1,0,0,1,-p,-g]);return y.fillStyle=h?l.getPattern(a,this,w,tJ.FILL):l,y.fillRect(0,0,n,o),e&&!h&&(this.cachedCanvases.delete("fillCanvas"),e.set(i,v.canvas)),{canvas:v.canvas,offsetX:Math.round(p),offsetY:Math.round(g)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=el[t]}setLineJoin(t){this.ctx.lineJoin=eh[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){let i=this.ctx;void 0!==i.setLineDash&&(i.setLineDash(t),i.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(let[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.ctx.globalAlpha=this.current.fillAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){let t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw Error("beginSMaskMode called while already in smask mode");let t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,s=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx;let r=this.ctx=s.context;r.setTransform(this.suspendedCtx.getTransform()),ea(this.suspendedCtx,r);var a=this.suspendedCtx;if(r._removeMirroring)throw Error("Context is already forwarding operations.");r.__originalSave=r.save,r.__originalRestore=r.restore,r.__originalRotate=r.rotate,r.__originalScale=r.scale,r.__originalTranslate=r.translate,r.__originalTransform=r.transform,r.__originalSetTransform=r.setTransform,r.__originalResetTransform=r.resetTransform,r.__originalClip=r.clip,r.__originalMoveTo=r.moveTo,r.__originalLineTo=r.lineTo,r.__originalBezierCurveTo=r.bezierCurveTo,r.__originalRect=r.rect,r.__originalClosePath=r.closePath,r.__originalBeginPath=r.beginPath,r._removeMirroring=()=>{r.save=r.__originalSave,r.restore=r.__originalRestore,r.rotate=r.__originalRotate,r.scale=r.__originalScale,r.translate=r.__originalTranslate,r.transform=r.__originalTransform,r.setTransform=r.__originalSetTransform,r.resetTransform=r.__originalResetTransform,r.clip=r.__originalClip,r.moveTo=r.__originalMoveTo,r.lineTo=r.__originalLineTo,r.bezierCurveTo=r.__originalBezierCurveTo,r.rect=r.__originalRect,r.closePath=r.__originalClosePath,r.beginPath=r.__originalBeginPath,delete r._removeMirroring},r.save=function(){a.save(),this.__originalSave()},r.restore=function(){a.restore(),this.__originalRestore()},r.translate=function(t,e){a.translate(t,e),this.__originalTranslate(t,e)},r.scale=function(t,e){a.scale(t,e),this.__originalScale(t,e)},r.transform=function(t,e,i,s,r,n){a.transform(t,e,i,s,r,n),this.__originalTransform(t,e,i,s,r,n)},r.setTransform=function(t,e,i,s,r,n){a.setTransform(t,e,i,s,r,n),this.__originalSetTransform(t,e,i,s,r,n)},r.resetTransform=function(){a.resetTransform(),this.__originalResetTransform()},r.rotate=function(t){a.rotate(t),this.__originalRotate(t)},r.clip=function(t){a.clip(t),this.__originalClip(t)},r.moveTo=function(t,e){a.moveTo(t,e),this.__originalMoveTo(t,e)},r.lineTo=function(t,e){a.lineTo(t,e),this.__originalLineTo(t,e)},r.bezierCurveTo=function(t,e,i,s,r,n){a.bezierCurveTo(t,e,i,s,r,n),this.__originalBezierCurveTo(t,e,i,s,r,n)},r.rect=function(t,e,i,s){a.rect(t,e,i,s),this.__originalRect(t,e,i,s)},r.closePath=function(){a.closePath(),this.__originalClosePath()},r.beginPath=function(){a.beginPath(),this.__originalBeginPath()},this.setGState([["BM","source-over"]])}endSMaskMode(){if(!this.inSMaskMode)throw Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),ea(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];let e=this.current.activeSMask,i=this.suspendedCtx;this.composeSMask(i,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,i,s){let r=s[0],a=s[1],n=s[2]-r,o=s[3]-a;0!==n&&0!==o&&(this.genericComposeSMask(e.context,i,n,o,e.subtype,e.backdrop,e.transferMap,r,a,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(i.canvas,0,0),t.restore())}genericComposeSMask(t,e,i,s,r,a,n,o,l,h,d){let c=t.canvas,u=o-h,p=l-d;if(a)if(u<0||p<0||u+i>c.width||p+s>c.height){let t=this.cachedCanvases.getCanvas("maskExtension",i,s),e=t.context;e.drawImage(c,-u,-p),e.globalCompositeOperation="destination-atop",e.fillStyle=a,e.fillRect(0,0,i,s),e.globalCompositeOperation="source-over",c=t.canvas,u=p=0}else{t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);let e=new Path2D;e.rect(u,p,i,s),t.clip(e),t.globalCompositeOperation="destination-atop",t.fillStyle=a,t.fillRect(u,p,i,s),t.restore()}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),"Alpha"===r&&n?e.filter=this.filterFactory.addAlphaFilter(n):"Luminosity"===r&&(e.filter=this.filterFactory.addLuminosityFilter(n));let g=new Path2D;g.rect(o,l,i,s),e.clip(g),e.globalCompositeOperation="destination-in",e.drawImage(c,u,p,i,s,o,l,i,s),e.restore()}save(){this.inSMaskMode&&ea(this.ctx,this.suspendedCtx),this.ctx.save();let t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){if(0===this.stateStack.length){this.inSMaskMode&&this.endSMaskMode();return}this.current=this.stateStack.pop(),this.ctx.restore(),this.inSMaskMode&&ea(this.suspendedCtx,this.ctx),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}transform(t,e,i,s,r,a){this.ctx.transform(t,e,i,s,r,a),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){let[s]=e;if(!i){s||=e[0]=new Path2D,this[t](s);return}if(!(s instanceof Path2D)){let t=e[0]=new Path2D;for(let e=0,i=s.length;e<i;)switch(s[e++]){case b.moveTo:t.moveTo(s[e++],s[e++]);break;case b.lineTo:t.lineTo(s[e++],s[e++]);break;case b.curveTo:t.bezierCurveTo(s[e++],s[e++],s[e++],s[e++],s[e++],s[e++]);break;case b.closePath:t.closePath();break;default:y(`Unrecognized drawing path operator: ${s[e-1]}`)}s=t}O.axialAlignedBoundingBox(i,ts(this.ctx),this.current.minMax),this[t](s)}closePath(){this.ctx.closePath()}stroke(t,e=!0){let i=this.ctx,s=this.current.strokeColor;if(i.globalAlpha=this.current.strokeAlpha,this.contentVisible)if("object"==typeof s&&s?.getPattern){let e=s.isModifyingCurrentTransform()?i.getTransform():null;if(i.save(),i.strokeStyle=s.getPattern(i,this,tr(i),tJ.STROKE),e){let s=new Path2D;s.addPath(t,i.getTransform().invertSelf().multiplySelf(e)),t=s}this.rescaleAndStroke(t,!1),i.restore()}else this.rescaleAndStroke(t,!0);e&&this.consumePath(t,this.current.getClippedPathBoundingBox(tJ.STROKE,ts(this.ctx))),i.globalAlpha=this.current.fillAlpha}closeStroke(t){this.stroke(t)}fill(t,e=!0){let i=this.ctx,s=this.current.fillColor,r=this.current.patternFill,a=!1;if(r){let e=s.isModifyingCurrentTransform()?i.getTransform():null;if(i.save(),i.fillStyle=s.getPattern(i,this,tr(i),tJ.FILL),e){let s=new Path2D;s.addPath(t,i.getTransform().invertSelf().multiplySelf(e)),t=s}a=!0}let n=this.current.getClippedPathBoundingBox();this.contentVisible&&null!==n&&(this.pendingEOFill?(i.fill(t,"evenodd"),this.pendingEOFill=!1):i.fill(t)),a&&i.restore(),e&&this.consumePath(t,n)}eoFill(t){this.pendingEOFill=!0,this.fill(t)}fillStroke(t){this.fill(t,!1),this.stroke(t,!1),this.consumePath(t)}eoFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}closeFillStroke(t){this.fillStroke(t)}closeEOFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}endPath(t){this.consumePath(t)}rawFillPath(t){this.ctx.fill(t)}clip(){this.pendingClip=ed}eoClip(){this.pendingClip=ec}beginText(){this.current.textMatrix=null,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){let t=this.pendingTextPaths,e=this.ctx;if(void 0===t)return;let i=new Path2D,s=e.getTransform().invertSelf();for(let{transform:e,x:r,y:a,fontSize:n,path:o}of t)i.addPath(o,new DOMMatrix(e).preMultiplySelf(s).translate(r,a).scale(n,-n));e.clip(i),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){let i=this.commonObjs.get(t),s=this.current;if(!i)throw Error(`Can't find font for ${t}`);if(s.fontMatrix=i.fontMatrix||n,(0===s.fontMatrix[0]||0===s.fontMatrix[3])&&y("Invalid font matrix for font "+t),e<0?(e=-e,s.fontDirection=-1):s.fontDirection=1,this.current.font=i,this.current.fontSize=e,i.isType3Font)return;let r=i.loadedName||"sans-serif",a=i.systemFontInfo?.css||`"${r}", ${i.fallbackName}`,o="normal";i.black?o="900":i.bold&&(o="bold");let l=i.italic?"italic":"normal",h=e;e<16?h=16:e>100&&(h=100),this.current.fontSizeScale=e/h,this.ctx.font=`${l} ${o} ${h}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t){let{current:e}=this;e.textMatrix=t,e.textMatrixScale=Math.hypot(t[0],t[1]),e.x=e.lineX=0,e.y=e.lineY=0}nextLine(){this.moveText(0,this.current.leading)}#im(t,e,i){let s=new Path2D;return s.addPath(t,new DOMMatrix(i).invertSelf().multiplySelf(e)),s}paintChar(t,e,i,s,r){let a,n=this.ctx,o=this.current,l=o.font,h=o.textRenderingMode,d=o.fontSize/o.fontSizeScale,u=h&c.FILL_STROKE_MASK,p=!!(h&c.ADD_TO_PATH_FLAG),g=o.patternFill&&!l.missingFile,f=o.patternStroke&&!l.missingFile;if((l.disableFontFace||p||g||f)&&(a=l.getPathGenerator(this.commonObjs,t)),l.disableFontFace||g||f){let t;if(n.save(),n.translate(e,i),n.scale(d,-d),(u===c.FILL||u===c.FILL_STROKE)&&(s?(t=n.getTransform(),n.setTransform(...s),n.fill(this.#im(a,t,s))):n.fill(a)),u===c.STROKE||u===c.FILL_STROKE)if(r){t||=n.getTransform(),n.setTransform(...r);let{a:e,b:i,c:s,d:o}=t,l=O.inverseTransform(r),h=O.transform([e,i,s,o,0,0],l);O.singularValueDecompose2dScale(h,t7),n.lineWidth*=Math.max(t7[0],t7[1])/d,n.stroke(this.#im(a,t,r))}else n.lineWidth/=d,n.stroke(a);n.restore()}else(u===c.FILL||u===c.FILL_STROKE)&&n.fillText(t,e,i),(u===c.STROKE||u===c.FILL_STROKE)&&n.strokeText(t,e,i);p&&(this.pendingTextPaths||=[]).push({transform:ts(n),x:e,y:i,fontSize:d,path:a})}get isFontSubpixelAAEnabled(){let{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);let e=t.getImageData(0,0,10,10).data,i=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){i=!0;break}return S(this,"isFontSubpixelAAEnabled",i)}showText(t){let e,i,s=this.current,r=s.font;if(r.isType3Font)return this.showType3Text(t);let a=s.fontSize;if(0===a)return;let n=this.ctx,o=s.fontSizeScale,l=s.charSpacing,h=s.wordSpacing,d=s.fontDirection,u=s.textHScale*d,p=t.length,g=r.vertical,f=g?1:-1,m=r.defaultVMetrics,b=a*s.fontMatrix[0],A=s.textRenderingMode===c.FILL&&!r.disableFontFace&&!s.patternFill;if(n.save(),s.textMatrix&&n.transform(...s.textMatrix),n.translate(s.x,s.y+s.textRise),d>0?n.scale(u,-1):n.scale(u,1),s.patternFill){n.save();let t=s.fillColor.getPattern(n,this,tr(n),tJ.FILL);e=ts(n),n.restore(),n.fillStyle=t}if(s.patternStroke){n.save();let t=s.strokeColor.getPattern(n,this,tr(n),tJ.STROKE);i=ts(n),n.restore(),n.strokeStyle=t}let v=s.lineWidth,y=s.textMatrixScale;if(0===y||0===v){let t=s.textRenderingMode&c.FILL_STROKE_MASK;(t===c.STROKE||t===c.FILL_STROKE)&&(v=this.getSinglePixelWidth())}else v/=y;if(1!==o&&(n.scale(o,o),v/=o),n.lineWidth=v,r.isInvalidPDFjsFont){let e=[],i=0;for(let s of t)e.push(s.unicode),i+=s.width;n.fillText(e.join(""),0,0),s.x+=i*b*u,n.restore(),this.compose();return}let w=0,_;for(_=0;_<p;++_){let s,c,u=t[_];if("number"==typeof u){w+=f*u*a/1e3;continue}let p=!1,v=(u.isSpace?h:0)+l,y=u.fontChar,x=u.accent,E=u.width;if(g){let t=u.vmetric||m,e=-(u.vmetric?t[1]:.5*E)*b,i=t[2]*b;E=t?-t[0]:E,s=e/o,c=(w+i)/o}else s=w/o,c=0;if(r.remeasure&&E>0){let t=1e3*n.measureText(y).width/a*o;if(E<t&&this.isFontSubpixelAAEnabled){let e=E/t;p=!0,n.save(),n.scale(e,1),s/=e}else E!==t&&(s+=(E-t)/2e3*a/o)}if(this.contentVisible&&(u.isInFont||r.missingFile)){if(A&&!x)n.fillText(y,s,c);else if(this.paintChar(y,s,c,e,i),x){let t=s+a*x.offset.x/o,r=c-a*x.offset.y/o;this.paintChar(x.fontChar,t,r,e,i)}}w+=g?E*b-v*d:E*b+v*d,p&&n.restore()}g?s.y-=w:s.x+=w*u,n.restore(),this.compose()}showType3Text(t){let e,i,s,r,a=this.ctx,o=this.current,l=o.font,h=o.fontSize,d=o.fontDirection,u=l.vertical?1:-1,p=o.charSpacing,g=o.wordSpacing,f=o.textHScale*d,m=o.fontMatrix||n,b=t.length;if(o.textRenderingMode!==c.INVISIBLE&&0!==h){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,a.save(),o.textMatrix&&a.transform(...o.textMatrix),a.translate(o.x,o.y+o.textRise),a.scale(f,d),e=0;e<b;++e){if("number"==typeof(i=t[e])){r=u*i*h/1e3,this.ctx.translate(r,0),o.x+=r*f;continue}let n=(i.isSpace?g:0)+p,d=l.charProcOperatorList[i.operatorListId];d?this.contentVisible&&(this.save(),a.scale(h,h),a.transform(...m),this.executeOperatorList(d),this.restore()):y(`Type3 character "${i.operatorListId}" is not available.`);let c=[i.width,0];O.applyTransform(c,m),s=c[0]*h+n,a.translate(s,0),o.x+=s*f}a.restore()}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,s,r,a){let n=new Path2D;n.rect(i,s,r-i,a-s),this.ctx.clip(n),this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){let i=this.baseTransform||ts(this.ctx);e=new t4(t,this.ctx,{createCanvasGraphics:t=>new eu(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})},i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments),this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t){this.ctx.strokeStyle=this.current.strokeColor=t,this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent",this.current.patternStroke=!1}setFillRGBColor(t){this.ctx.fillStyle=this.current.fillColor=t,this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent",this.current.patternFill=!1}_getPattern(t,e=null){let i;return this.cachedPatterns.has(t)?i=this.cachedPatterns.get(t):(i=function(t){switch(t[0]){case"RadialAxial":return new t1(t);case"Mesh":return new t3(t);case"Dummy":return new t5}throw Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t)),this.cachedPatterns.set(t,i)),e&&(i.matrix=e),i}shadingFill(t){if(!this.contentVisible)return;let e=this.ctx;this.save(),e.fillStyle=this._getPattern(t).getPattern(e,this,tr(e),tJ.SHADING);let i=tr(e);if(i){let{width:t,height:s}=e.canvas,r=t9.slice();O.axialAlignedBoundingBox([0,0,t,s],i,r);let[a,n,o,l]=r;this.ctx.fillRect(a,n,o-a,l-n)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){w("Should not call beginInlineImage")}beginImageData(){w("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=ts(this.ctx),e)){O.axialAlignedBoundingBox(e,this.baseTransform,this.current.minMax);let[t,i,s,r]=e,a=new Path2D;a.rect(t,i,s-t,r-i),this.ctx.clip(a),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);let e=this.ctx;t.isolated||v("TODO: Support non-isolated groups."),t.knockout&&y("Knockout groups not supported.");let i=ts(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw Error("Bounding box is required.");let s=t9.slice();O.axialAlignedBoundingBox(t.bbox,ts(e),s);let r=[0,0,e.canvas.width,e.canvas.height],a=Math.floor((s=O.intersect(s,r)||[0,0,0,0])[0]),n=Math.floor(s[1]),o=Math.max(Math.ceil(s[2])-a,1),l=Math.max(Math.ceil(s[3])-n,1);this.current.startNewPathAndClipBox([0,0,o,l]);let h="groupAt"+this.groupLevel;t.smask&&(h+="_smask_"+this.smaskCounter++%2);let d=this.cachedCanvases.getCanvas(h,o,l),c=d.context;c.translate(-a,-n),c.transform(...i);let u=new Path2D,[p,g,f,m]=t.bbox;if(u.rect(p,g,f-p,m-g),t.matrix){let e=new Path2D;e.addPath(u,new DOMMatrix(t.matrix)),u=e}c.clip(u),t.smask?this.smaskStack.push({canvas:d.canvas,context:c,offsetX:a,offsetY:n,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(a,n),e.save()),ea(e,c),this.ctx=c,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;let e=this.ctx,i=this.groupStack.pop();if(this.ctx=i,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();let t=ts(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...t);let i=t9.slice();O.axialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t,i),this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(i)}}beginAnnotation(t,e,i,s,r){if(this.#ip(),en(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){let s=e[2]-e[0],a=e[3]-e[1];if(r&&this.annotationCanvasMap){i=i.slice(),i[4]-=e[0],i[5]-=e[1],(e=e.slice())[0]=e[1]=0,e[2]=s,e[3]=a,O.singularValueDecompose2dScale(ts(this.ctx),t7);let{viewportScale:r}=this,n=Math.ceil(s*this.outputScaleX*r),o=Math.ceil(a*this.outputScaleY*r);this.annotationCanvas=this.canvasFactory.create(n,o);let{canvas:l,context:h}=this.annotationCanvas;this.annotationCanvasMap.set(t,l),this.annotationCanvas.savedCtx=this.ctx,this.ctx=h,this.ctx.save(),this.ctx.setTransform(t7[0],0,0,-t7[1],0,a*t7[1]),en(this.ctx)}else{en(this.ctx),this.endPath();let t=new Path2D;t.rect(e[0],e[1],s,a),this.ctx.clip(t)}}this.current=new ei(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...i),this.transform(...s)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),this.#ig(),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;let e=t.count;(t=this.getObject(t.data,t)).count=e;let i=this.ctx,s=this._createMaskCanvas(t),r=s.canvas;i.save(),i.setTransform(1,0,0,1,0,0),i.drawImage(r,s.offsetX,s.offsetY),i.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,i=0,s=0,r,a){if(!this.contentVisible)return;t=this.getObject(t.data,t);let n=this.ctx;n.save();let o=ts(n);n.transform(e,i,s,r,0,0);let l=this._createMaskCanvas(t);n.setTransform(1,0,0,1,l.offsetX-o[4],l.offsetY-o[5]);for(let t=0,h=a.length;t<h;t+=2){let h=O.transform(o,[e,i,s,r,a[t],a[t+1]]);n.drawImage(l.canvas,h[4],h[5])}n.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;let e=this.ctx,i=this.current.fillColor,s=this.current.patternFill;for(let r of t){let{data:t,width:a,height:n,transform:o}=r,l=this.cachedCanvases.getCanvas("maskCanvas",a,n),h=l.context;h.save(),er(h,this.getObject(t,r)),h.globalCompositeOperation="source-in",h.fillStyle=s?i.getPattern(h,this,tr(e),tJ.FILL):i,h.fillRect(0,0,a,n),h.restore(),e.save(),e.transform(...o),e.scale(1,-1),ee(e,l.canvas,0,0,a,n,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;let e=this.getObject(t);if(!e)return void y("Dependent image isn't ready yet");this.paintInlineImageXObject(e)}paintImageXObjectRepeat(t,e,i,s){if(!this.contentVisible)return;let r=this.getObject(t);if(!r)return void y("Dependent image isn't ready yet");let a=r.width,n=r.height,o=[];for(let t=0,r=s.length;t<r;t+=2)o.push({transform:[e,0,0,i,s[t],s[t+1]],x:0,y:0,w:a,h:n});this.paintInlineImageXObjectGroup(r,o)}applyTransferMapsToCanvas(t){return"none"!==this.current.transferMaps&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;let{bitmap:e,width:i,height:s}=t,r=this.cachedCanvases.getCanvas("inlineImage",i,s),a=r.context;return a.filter=this.current.transferMaps,a.drawImage(e,0,0),a.filter="none",r.canvas}paintInlineImageXObject(t){let e;if(!this.contentVisible)return;let i=t.width,s=t.height,r=this.ctx;this.save();let{filter:a}=r;if("none"!==a&&""!==a&&(r.filter="none"),r.scale(1/i,-1/s),t.bitmap)e=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)e=t;else{let r=this.cachedCanvases.getCanvas("inlineImage",i,s).context;es(r,t),e=this.applyTransferMapsToCanvas(r)}let n=this._scaleImage(e,tr(r));r.imageSmoothingEnabled=eo(ts(r),t.interpolate),ee(r,n.img,0,0,n.paintWidth,n.paintHeight,0,-s,i,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){let i;if(!this.contentVisible)return;let s=this.ctx;if(t.bitmap)i=t.bitmap;else{let e=t.width,s=t.height,r=this.cachedCanvases.getCanvas("inlineImage",e,s).context;es(r,t),i=this.applyTransferMapsToCanvas(r)}for(let t of e)s.save(),s.transform(...t.transform),s.scale(1,-1),ee(s,i,t.x,t.y,t.w,t.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t,e){let i=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(e);let s=this.ctx;this.pendingClip&&(i||(this.pendingClip===ec?s.clip(t,"evenodd"):s.clip(t)),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox)}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){let t=ts(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{let e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),s=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,s)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){let t,e,{lineWidth:i}=this.current,{a:s,b:r,c:a,d:n}=this.ctx.getTransform();if(0===r&&0===a){let r=Math.abs(s),a=Math.abs(n);if(r===a)if(0===i)t=e=1/r;else{let s=r*i;t=e=s<1?1/s:1}else if(0===i)t=1/r,e=1/a;else{let s=r*i,n=a*i;t=s<1?1/s:1,e=n<1?1/n:1}}else{let o=Math.abs(s*n-r*a),l=Math.hypot(s,r),h=Math.hypot(a,n);if(0===i)t=h/o,e=l/o;else{let s=i*o;t=h>s?h/s:1,e=l>s?l/s:1}}this._cachedScaleForStroking[0]=t,this._cachedScaleForStroking[1]=e}return this._cachedScaleForStroking}rescaleAndStroke(t,e){let{ctx:i,current:{lineWidth:s}}=this,[r,a]=this.getScaleForStroking();if(r===a){i.lineWidth=(s||1)*r,i.stroke(t);return}let n=i.getLineDash();e&&i.save(),i.scale(r,a),t8.a=1/r,t8.d=1/a;let o=new Path2D;if(o.addPath(t,t8),n.length>0){let t=Math.max(r,a);i.setLineDash(n.map(e=>e/t)),i.lineDashOffset/=t}i.lineWidth=s||1,i.stroke(o),e&&i.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(let t in m)void 0!==eu.prototype[t]&&(eu.prototype[m[t]]=eu.prototype[t]);class ep{static #ib=null;static #iA="";static get workerPort(){return this.#ib}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw Error("Invalid `workerPort` type.");this.#ib=t}static get workerSrc(){return this.#iA}static set workerSrc(t){if("string"!=typeof t)throw Error("Invalid `workerSrc` type.");this.#iA=t}}class eg{#iv;#iy;constructor({parsedData:t,rawData:e}){this.#iv=t,this.#iy=e}getRaw(){return this.#iy}get(t){return this.#iv.get(t)??null}[Symbol.iterator](){return this.#iv.entries()}}let ef=Symbol("INTERNAL");class em{#iw=!1;#i_=!1;#ix=!1;#iE=!0;constructor(t,{name:e,intent:i,usage:s,rbGroups:r}){this.#iw=!!(t&o.DISPLAY),this.#i_=!!(t&o.PRINT),this.name=e,this.intent=i,this.usage=s,this.rbGroups=r}get visible(){if(this.#ix)return this.#iE;if(!this.#iE)return!1;let{print:t,view:e}=this.usage;return this.#iw?e?.viewState!=="OFF":!this.#i_||t?.printState!=="OFF"}_setVisible(t,e,i=!1){t!==ef&&w("Internal method `_setVisible` called."),this.#ix=i,this.#iE=e}}class eb{#iS=null;#iC=new Map;#iT=null;#iM=null;constructor(t,e=o.DISPLAY){if(this.renderingIntent=e,this.name=null,this.creator=null,null===t)return;for(let i of(this.name=t.name,this.creator=t.creator,this.#iM=t.order,t.groups))this.#iC.set(i.id,new em(e,i));if("OFF"===t.baseState)for(let t of this.#iC.values())t._setVisible(ef,!1);for(let e of t.on)this.#iC.get(e)._setVisible(ef,!0);for(let e of t.off)this.#iC.get(e)._setVisible(ef,!1);this.#iT=this.getHash()}#iP(t){let e=t.length;if(e<2)return!0;let i=t[0];for(let s=1;s<e;s++){let e,r=t[s];if(Array.isArray(r))e=this.#iP(r);else{if(!this.#iC.has(r))return y(`Optional content group not found: ${r}`),!0;e=this.#iC.get(r).visible}switch(i){case"And":if(!e)return!1;break;case"Or":if(e)return!0;break;case"Not":return!e;default:return!0}}return"And"===i}isVisible(t){if(0===this.#iC.size)return!0;if(!t)return v("Optional content group not defined."),!0;if("OCG"===t.type)return this.#iC.has(t.id)?this.#iC.get(t.id).visible:(y(`Optional content group not found: ${t.id}`),!0);if("OCMD"===t.type){if(t.expression)return this.#iP(t.expression);if(t.policy&&"AnyOn"!==t.policy){if("AllOn"===t.policy){for(let e of t.ids){if(!this.#iC.has(e)){y(`Optional content group not found: ${e}`);break}if(!this.#iC.get(e).visible)return!1}return!0}else if("AnyOff"===t.policy){for(let e of t.ids){if(!this.#iC.has(e))return y(`Optional content group not found: ${e}`),!0;if(!this.#iC.get(e).visible)return!0}return!1}else if("AllOff"===t.policy){for(let e of t.ids){if(!this.#iC.has(e)){y(`Optional content group not found: ${e}`);break}if(this.#iC.get(e).visible)return!1}return!0}}else{for(let e of t.ids){if(!this.#iC.has(e))return y(`Optional content group not found: ${e}`),!0;if(this.#iC.get(e).visible)return!0}return!1}return y(`Unknown optional content policy ${t.policy}.`),!0}return y(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0,i=!0){let s=this.#iC.get(t);if(!s)return void y(`Optional content group not found: ${t}`);if(i&&e&&s.rbGroups.length)for(let e of s.rbGroups)for(let i of e)i!==t&&this.#iC.get(i)?._setVisible(ef,!1,!0);s._setVisible(ef,!!e,!0),this.#iS=null}setOCGState({state:t,preserveRB:e}){let i;for(let s of t){switch(s){case"ON":case"OFF":case"Toggle":i=s;continue}let t=this.#iC.get(s);if(t)switch(i){case"ON":this.setVisibility(s,!0,e);break;case"OFF":this.setVisibility(s,!1,e);break;case"Toggle":this.setVisibility(s,!t.visible,e)}}this.#iS=null}get hasInitialVisibility(){return null===this.#iT||this.getHash()===this.#iT}getOrder(){return this.#iC.size?this.#iM?this.#iM.slice():[...this.#iC.keys()]:null}getGroup(t){return this.#iC.get(t)||null}getHash(){if(null!==this.#iS)return this.#iS;let t=new tw;for(let[e,i]of this.#iC)t.update(`${e}:${i.visible}`);return this.#iS=t.hexdigest()}[Symbol.iterator](){return this.#iC.entries()}}class eA{constructor(t,{disableRange:e=!1,disableStream:i=!1}){_(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');let{length:s,initialData:r,progressiveDone:a,contentDispositionFilename:n}=t;if(this._queuedChunks=[],this._progressiveDone=a,this._contentDispositionFilename=n,r?.length>0){let t=r instanceof Uint8Array&&r.byteLength===r.buffer.byteLength?r.buffer:new Uint8Array(r).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!i,this._isRangeSupported=!e,this._contentLength=s,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener((t,e)=>{this._onReceiveData({begin:t,chunk:e})}),t.addProgressListener((t,e)=>{this._onProgress({loaded:t,total:e})}),t.addProgressiveReadListener(t=>{this._onReceiveData({chunk:t})}),t.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),t.transportReady()}_onReceiveData({begin:t,chunk:e}){let i=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;void 0===t?this._fullRequestReader?this._fullRequestReader._enqueue(i):this._queuedChunks.push(i):_(this._rangeReaders.some(function(e){return e._begin===t&&(e._enqueue(i),!0)}),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){let e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){_(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");let t=this._queuedChunks;return this._queuedChunks=null,new ev(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;let i=new ey(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(i),i}cancelAllRequests(t){for(let e of(this._fullRequestReader?.cancel(t),this._rangeReaders.slice(0)))e.cancel(t);this._pdfDataRangeTransport.abort()}}class ev{constructor(t,e,i=!1,s=null){for(let r of(this._stream=t,this._done=i||!1,this._filename=Y(s)?s:null,this._queuedChunks=e||[],this._loaded=0,this._queuedChunks))this._loaded+=r.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){this._done||(this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunks.push(t),this._loaded+=t.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};let t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){for(let t of(this._done=!0,this._requests))t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class ey{constructor(t,e,i){this._stream=t,this._begin=e,this._end=i,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{for(let e of(this._requests.shift().resolve({value:t,done:!1}),this._requests))e.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){let t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};let t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){for(let t of(this._done=!0,this._requests))t.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function ew(t,e){let i=new Headers;if(!t||!e||"object"!=typeof e)return i;for(let t in e){let s=e[t];void 0!==s&&i.append(t,s)}return i}function e_(t){return URL.parse(t)?.origin??null}function ex({responseHeaders:t,isHttp:e,rangeChunkSize:i,disableRange:s}){let r={allowRangeRequests:!1,suggestedLength:void 0},a=parseInt(t.get("Content-Length"),10);return Number.isInteger(a)&&(r.suggestedLength=a,a<=2*i||s||!e||"bytes"!==t.get("Accept-Ranges")||"identity"!==(t.get("Content-Encoding")||"identity")||(r.allowRangeRequests=!0)),r}function eE(t){let e=t.get("Content-Disposition");if(e){let t=function(t){let e=!0,i=s("filename\\*","i").exec(t);if(i){let t=n(i=i[1]);return a(t=l(t=o(t=unescape(t))))}if(i=function(t){let e,i=[],r=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(e=r.exec(t));){let[,t,s,r]=e;if((t=parseInt(t,10))in i){if(0===t)break;continue}i[t]=[s,r]}let a=[];for(let t=0;t<i.length&&t in i;++t){let[e,s]=i[t];s=n(s),e&&(s=unescape(s),0===t&&(s=o(s))),a.push(s)}return a.join("")}(t))return a(l(i));if(i=s("filename","i").exec(t)){let t=n(i=i[1]);return a(t=l(t))}function s(t,e){return RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function r(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{let s=new TextDecoder(t,{fatal:!0}),r=L(i);i=s.decode(r),e=!1}catch{}}return i}function a(t){return e&&/[\x80-\xff]/.test(t)&&(t=r("utf-8",t),e&&(t=r("iso-8859-1",t))),t}function n(t){if(t.startsWith('"')){let e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){let i=e[t].indexOf('"');-1!==i&&(e[t]=e[t].slice(0,i),e.length=t+1),e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function o(t){let e=t.indexOf("'");return -1===e?t:r(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function l(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(t,e,i,s){if("q"===i||"Q"===i)return r(e,s=(s=s.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,function(t,e){return String.fromCharCode(parseInt(e,16))}));try{s=atob(s)}catch{}return r(e,s)})}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if(Y(t))return t}return null}function eS(t,e){return new I(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t,404===t||0===t&&e.startsWith("file:"))}function eC(t){return 200===t||206===t}function eT(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function eM(t){return t instanceof Uint8Array?t.buffer:t instanceof ArrayBuffer?t:(y(`getArrayBuffer - unexpected data format: ${t}`),new Uint8Array(t).buffer)}class eP{_responseOrigin=null;constructor(t){this.source=t,this.isHttp=/^https?:/i.test(t.url),this.headers=ew(this.isHttp,t.httpHeaders),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return _(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new eI(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;let i=new eD(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){for(let e of(this._fullRequestReader?.cancel(t),this._rangeRequestReaders.slice(0)))e.cancel(t)}}class eI{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;let e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange;let i=new Headers(t.headers),s=e.url;fetch(s,eT(i,this._withCredentials,this._abortController)).then(e=>{if(t._responseOrigin=e_(e.url),!eC(e.status))throw eS(e.status,s);this._reader=e.body.getReader(),this._headersCapability.resolve();let i=e.headers,{allowRangeRequests:r,suggestedLength:a}=ex({responseHeaders:i,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=r,this._contentLength=a||this._contentLength,this._filename=eE(i),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new R("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;let{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:eM(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class eD{constructor(t,e,i){this._stream=t,this._reader=null,this._loaded=0;let s=t.source;this._withCredentials=s.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!s.disableStream,this._abortController=new AbortController;let r=new Headers(t.headers);r.append("Range",`bytes=${e}-${i-1}`);let a=s.url;fetch(a,eT(r,this._withCredentials,this._abortController)).then(e=>{let i=e_(e.url);if(i!==t._responseOrigin)throw Error(`Expected range response-origin "${i}" to match "${t._responseOrigin}".`);if(!eC(e.status))throw eS(e.status,a);this._readCapability.resolve(),this._reader=e.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;let{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded}),{value:eM(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class eR{_responseOrigin=null;constructor({url:t,httpHeaders:e,withCredentials:i}){this.url=t,this.isHttp=/^https?:/i.test(t),this.headers=ew(this.isHttp,e),this.withCredentials=i||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}request(t){let e=new XMLHttpRequest,i=this.currXhrId++,s=this.pendingRequests[i]={xhr:e};for(let[t,i]of(e.open("GET",this.url),e.withCredentials=this.withCredentials,this.headers))e.setRequestHeader(t,i);return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),s.expectedStatus=206):s.expectedStatus=200,e.responseType="arraybuffer",_(t.onError,"Expected `onError` callback to be provided."),e.onerror=()=>{t.onError(e.status)},e.onreadystatechange=this.onStateChange.bind(this,i),e.onprogress=this.onProgress.bind(this,i),s.onHeadersReceived=t.onHeadersReceived,s.onDone=t.onDone,s.onError=t.onError,s.onProgress=t.onProgress,e.send(null),i}onProgress(t,e){let i=this.pendingRequests[t];i&&i.onProgress?.(e)}onStateChange(t,e){let i=this.pendingRequests[t];if(!i)return;let s=i.xhr;if(s.readyState>=2&&i.onHeadersReceived&&(i.onHeadersReceived(),delete i.onHeadersReceived),4!==s.readyState||!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],0===s.status&&this.isHttp)return void i.onError(s.status);let r=s.status||200;if((200!==r||206!==i.expectedStatus)&&r!==i.expectedStatus)return void i.onError(s.status);let a=function(t){let e=t.response;return"string"!=typeof e?e:L(e).buffer}(s);if(206===r){let t=s.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);e?i.onDone({begin:parseInt(e[1],10),chunk:a}):(y('Missing or invalid "Content-Range" header.'),i.onError(0))}else a?i.onDone({begin:0,chunk:a}):i.onError(s.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){let e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class ek{constructor(t){this._source=t,this._manager=new eR(t),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){let e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return _(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new eL(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){let i=new eF(this._manager,t,e);return i.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(i),i}cancelAllRequests(t){for(let e of(this._fullRequestReader?.cancel(t),this._rangeRequestReaders.slice(0)))e.cancel(t)}}class eL{constructor(t,e){this._manager=t,this._url=e.url,this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){let t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=e_(e.responseURL);let i=e.getAllResponseHeaders(),s=new Headers(i?i.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map(t=>{let[e,...i]=t.split(": ");return[e,i.join(": ")]}):[]),{allowRangeRequests:r,suggestedLength:a}=ex({responseHeaders:s,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});r&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=eE(s),this._isRangeSupported&&this._manager.abortRequest(t),this._headersCapability.resolve()}_onDone(t){if(t&&(this._requests.length>0?this._requests.shift().resolve({value:t.chunk,done:!1}):this._cachedChunks.push(t.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(let t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){for(let e of(this._storedError=eS(t,this._url),this._headersCapability.reject(this._storedError),this._requests))e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){if(await this._headersCapability.promise,this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};let t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){for(let e of(this._done=!0,this._headersCapability.reject(t),this._requests))e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class eF{constructor(t,e,i){this._manager=t,this._url=t.url,this._requestId=t.request({begin:e,end:i,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_onHeadersReceived(){let t=e_(this._manager.getRequestXhr(this._requestId)?.responseURL);t!==this._manager._responseOrigin&&(this._storedError=Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`),this._onError(0))}_close(){this.onClosed?.(this)}_onDone(t){let e=t.chunk;for(let t of(this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunk=e,this._done=!0,this._requests))t.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){for(let e of(this._storedError??=eS(t,this._url),this._requests))e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){let t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};let t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){for(let t of(this._done=!0,this._requests))t.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}let eN=/^[a-z][a-z0-9\-+.]+:/i;class eO{constructor(t){this.source=t,this.url=function(t){return new URL(eN.test(t)?t:s.getBuiltinModule("url").pathToFileURL(t))}(t.url),_("file:"===this.url.protocol,"PDFNodeStream only supports file:// URLs."),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return _(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=new eB(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;let i=new ez(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){for(let e of(this._fullRequestReader?.cancel(t),this._rangeRequestReaders.slice(0)))e.cancel(t)}}class eB{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;let e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers();let i=s.getBuiltinModule("fs");i.promises.lstat(this._url).then(t=>{this._contentLength=t.size,this._setReadableStream(i.createReadStream(this._url)),this._headersCapability.resolve()},t=>{"ENOENT"===t.code&&(t=eS(0,this._url.href)),this._storedError=t,this._headersCapability.reject(t)})}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;let t=this._readableStream.read();return null===t?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream)return void this._error(t);this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",t=>{this._error(t)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new R("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class ez{constructor(t,e,i){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();let r=t.source;this._isStreamingSupported=!r.disableStream;let a=s.getBuiltinModule("fs");this._setReadableStream(a.createReadStream(this._url,{start:e,end:i-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;let t=this._readableStream.read();return null===t?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream)return void this._error(t);this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",t=>{this._error(t)}),this._storedError&&this._readableStream.destroy(this._storedError)}}let eH=Symbol("INITIAL_DATA");class eU{#iI=Object.create(null);#iD(t){return this.#iI[t]||={...Promise.withResolvers(),data:eH}}get(t,e=null){if(e){let i=this.#iD(t);return i.promise.then(()=>e(i.data)),null}let i=this.#iI[t];if(!i||i.data===eH)throw Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){let e=this.#iI[t];return!!e&&e.data!==eH}delete(t){let e=this.#iI[t];return!!e&&e.data!==eH&&(delete this.#iI[t],!0)}resolve(t,e=null){let i=this.#iD(t);i.data=e,i.resolve()}clear(){for(let t in this.#iI){let{data:e}=this.#iI[t];e?.bitmap?.close()}this.#iI=Object.create(null)}*[Symbol.iterator](){for(let t in this.#iI){let{data:e}=this.#iI[t];e!==eH&&(yield[t,e])}}}class e${#iR=Promise.withResolvers();#tm=null;#ik=!1;#iL=!!globalThis.FontInspector?.enabled;#iF=null;#iN=null;#iO=0;#iB=0;#iz=null;#iH=null;#iU=0;#i$=0;#iG=Object.create(null);#ij=[];#iV=null;#iW=[];#iq=new WeakMap;#iK=null;static #iX=new Map;static #iY=new Map;static #iQ=new WeakMap;static #iJ=null;static #iZ=new Set;constructor({textContentSource:t,container:e,viewport:i}){if(t instanceof ReadableStream)this.#iV=t;else if("object"==typeof t)this.#iV=new ReadableStream({start(e){e.enqueue(t),e.close()}});else throw Error('No "textContentSource" parameter specified.');this.#tm=this.#iH=e,this.#i$=i.scale*tn.pixelRatio,this.#iU=i.rotation,this.#iN={div:null,properties:null,ctx:null};let{pageWidth:s,pageHeight:r,pageX:a,pageY:n}=i.rawDims;this.#iK=[1,0,0,-1,-a,n+r],this.#iB=s,this.#iO=r,e$.#i0(),ta(e,i),this.#iR.promise.finally(()=>{e$.#iZ.delete(this),this.#iN=null,this.#iG=null}).catch(()=>{})}static get fontFamilyMap(){let{isWindows:t,isFirefox:e}=F.platform;return S(this,"fontFamilyMap",new Map([["sans-serif",`${t&&e?"Calibri, ":""}sans-serif`],["monospace",`${t&&e?"Lucida Console, ":""}monospace`]]))}render(){let t=()=>{this.#iz.read().then(({value:e,done:i})=>{if(i)return void this.#iR.resolve();this.#iF??=e.lang,Object.assign(this.#iG,e.styles),this.#i1(e.items),t()},this.#iR.reject)};return this.#iz=this.#iV.getReader(),e$.#iZ.add(this),t(),this.#iR.promise}update({viewport:t,onBefore:e=null}){let i=t.scale*tn.pixelRatio,s=t.rotation;if(s!==this.#iU&&(e?.(),this.#iU=s,ta(this.#iH,{rotation:s})),i!==this.#i$){e?.(),this.#i$=i;let t={div:null,properties:null,ctx:e$.#i2(this.#iF)};for(let e of this.#iW)t.properties=this.#iq.get(e),t.div=e,this.#i3(t)}}cancel(){let t=new R("TextLayer task cancelled.");this.#iz?.cancel(t).catch(()=>{}),this.#iz=null,this.#iR.reject(t)}get textDivs(){return this.#iW}get textContentItemsStr(){return this.#ij}#i1(t){if(this.#ik)return;this.#iN.ctx??=e$.#i2(this.#iF);let e=this.#iW,i=this.#ij;for(let s of t){if(e.length>1e5){y("Ignoring additional textDivs for performance reasons."),this.#ik=!0;return}if(void 0===s.str){if("beginMarkedContentProps"===s.type||"beginMarkedContent"===s.type){let t=this.#tm;this.#tm=document.createElement("span"),this.#tm.classList.add("markedContent"),null!==s.id&&this.#tm.setAttribute("id",`${s.id}`),t.append(this.#tm)}else"endMarkedContent"===s.type&&(this.#tm=this.#tm.parentNode);continue}i.push(s.str),this.#i5(s)}}#i5(t){let e,i,s=document.createElement("span"),r={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};this.#iW.push(s);let a=O.transform(this.#iK,t.transform),n=Math.atan2(a[1],a[0]),o=this.#iG[t.fontName];o.vertical&&(n+=Math.PI/2);let l=this.#iL&&o.fontSubstitution||o.fontFamily;l=e$.fontFamilyMap.get(l)||l;let h=Math.hypot(a[2],a[3]),d=h*e$.#i6(l,o,this.#iF);0===n?(e=a[4],i=a[5]-d):(e=a[4]+d*Math.sin(n),i=a[5]-d*Math.cos(n));let c="calc(var(--total-scale-factor) *",u=s.style;this.#tm===this.#iH?(u.left=`${(100*e/this.#iB).toFixed(2)}%`,u.top=`${(100*i/this.#iO).toFixed(2)}%`):(u.left=`${c}${e.toFixed(2)}px)`,u.top=`${c}${i.toFixed(2)}px)`),u.fontSize=`${c}${(e$.#iJ*h).toFixed(2)}px)`,u.fontFamily=l,r.fontSize=h,s.setAttribute("role","presentation"),s.textContent=t.str,s.dir=t.dir,this.#iL&&(s.dataset.fontName=o.fontSubstitutionLoadedName||t.fontName),0!==n&&(r.angle=180/Math.PI*n);let p=!1;if(t.str.length>1)p=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){let e=Math.abs(t.transform[0]),i=Math.abs(t.transform[3]);e!==i&&Math.max(e,i)/Math.min(e,i)>1.5&&(p=!0)}if(p&&(r.canvasWidth=o.vertical?t.height:t.width),this.#iq.set(s,r),this.#iN.div=s,this.#iN.properties=r,this.#i3(this.#iN),r.hasText&&this.#tm.append(s),r.hasEOL){let t=document.createElement("br");t.setAttribute("role","presentation"),this.#tm.append(t)}}#i3(t){let{div:e,properties:i,ctx:s}=t,{style:r}=e,a="";if(e$.#iJ>1&&(a=`scale(${1/e$.#iJ})`),0!==i.canvasWidth&&i.hasText){let{fontFamily:t}=r,{canvasWidth:n,fontSize:o}=i;e$.#i4(s,o*this.#i$,t);let{width:l}=s.measureText(e.textContent);l>0&&(a=`scaleX(${n*this.#i$/l}) ${a}`)}0!==i.angle&&(a=`rotate(${i.angle}deg) ${a}`),a.length>0&&(r.transform=a)}static cleanup(){if(!(this.#iZ.size>0)){for(let{canvas:t}of(this.#iX.clear(),this.#iY.values()))t.remove();this.#iY.clear()}}static #i2(t=null){let e=this.#iY.get(t||="");if(!e){let i=document.createElement("canvas");i.className="hiddenCanvasElement",i.lang=t,document.body.append(i),e=i.getContext("2d",{alpha:!1,willReadFrequently:!0}),this.#iY.set(t,e),this.#iQ.set(e,{size:0,family:""})}return e}static #i4(t,e,i){let s=this.#iQ.get(t);(e!==s.size||i!==s.family)&&(t.font=`${e}px ${i}`,s.size=e,s.family=i)}static #i0(){if(null!==this.#iJ)return;let t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.style.position="absolute",t.textContent="X",document.body.append(t),this.#iJ=t.getBoundingClientRect().height,t.remove()}static #i6(t,e,i){let s=this.#iX.get(t);if(s)return s;let r=this.#i2(i);r.canvas.width=r.canvas.height=30,this.#i4(r,30,t);let a=r.measureText(""),n=a.fontBoundingBoxAscent,o=Math.abs(a.fontBoundingBoxDescent);r.canvas.width=r.canvas.height=0;let l=.8;return n?l=n/(n+o):(F.platform.isFirefox&&y("Enable the `dom.textMetrics.fontBoundingBox.enabled` preference in `about:config` to improve TextLayer rendering."),e.ascent?l=e.ascent:e.descent&&(l=1+e.descent)),this.#iX.set(t,l),l}}class eG{static textContent(t){let e=[],i={items:e,styles:Object.create(null)};return!function t(i){if(!i)return;let s=null,r=i.name;if("#text"===r)s=i.value;else{if(!eG.shouldBuildText(r))return;i?.attributes?.textContent?s=i.attributes.textContent:i.value&&(s=i.value)}if(null!==s&&e.push({str:s}),i.children)for(let e of i.children)t(e)}(t),i}static shouldBuildText(t){return"textarea"!==t&&"input"!==t&&"option"!==t&&"select"!==t}}function ej(t={}){"string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t});let e=new eV,{docId:i}=e,s=t.url?function(t){if(t instanceof URL)return t.href;if("string"==typeof t){if(a)return t;let e=URL.parse(t,window.location);if(e)return e.href}throw Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(t.url):null,n=t.data?function(t){if(a&&void 0!==r&&t instanceof r)throw Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return L(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(t?.length))return new Uint8Array(t);throw Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(t.data):null,o=t.httpHeaders||null,l=!0===t.withCredentials,h=t.password??null,d=t.range instanceof eW?t.range:null,c=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:65536,u=t.worker instanceof eX?t.worker:null,p=t.verbosity,g="string"!=typeof t.docBaseUrl||X(t.docBaseUrl)?null:t.docBaseUrl,f=tT(t.cMapUrl),m=!1!==t.cMapPacked,b=t.CMapReaderFactory||(a?tX:tz),v=tT(t.iccUrl),y=tT(t.standardFontDataUrl),w=t.StandardFontDataFactory||(a?tY:tG),_=tT(t.wasmUrl),x=t.WasmFactory||(a?tQ:tV),E=!0!==t.stopAtErrors,S=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,C=!1!==t.isEvalSupported,T="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!a,M="boolean"==typeof t.isImageDecoderSupported?t.isImageDecoderSupported:!a&&(F.platform.isFirefox||!globalThis.chrome),P=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,I="boolean"==typeof t.disableFontFace?t.disableFontFace:a,D=!0===t.fontExtraProperties,R=!0===t.enableXfa,k=t.ownerDocument||globalThis.document,N=!0===t.disableRange,O=!0===t.disableStream,B=!0===t.disableAutoFetch,z=!0===t.pdfBug,H=t.CanvasFactory||(a?tK:tO),U=t.FilterFactory||(a?tq:tU),$=!0===t.enableHWA,G=!1!==t.useWasm,j=d?d.length:t.length??NaN,V="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!a&&!I,W="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:!!(b===tz&&w===tG&&x===tV&&f&&y&&_&&J(f,document.baseURI)&&J(y,document.baseURI)&&J(_,document.baseURI));Number.isInteger(p)&&(A=p);let q={canvasFactory:new H({ownerDocument:k,enableHWA:$}),filterFactory:new U({docId:i,ownerDocument:k}),cMapReaderFactory:W?null:new b({baseUrl:f,isCompressed:m}),standardFontDataFactory:W?null:new w({baseUrl:y}),wasmFactory:W?null:new x({baseUrl:_})};u||(e._worker=u=eX.create({verbosity:p,port:ep.workerPort}));let K={docId:i,apiVersion:"5.3.31",data:n,password:h,disableAutoFetch:B,rangeChunkSize:c,length:j,docBaseUrl:g,enableXfa:R,evaluatorOptions:{maxImageSize:S,disableFontFace:I,ignoreErrors:E,isEvalSupported:C,isOffscreenCanvasSupported:T,isImageDecoderSupported:M,canvasMaxAreaInBytes:P,fontExtraProperties:D,useSystemFonts:V,useWasm:G,useWorkerFetch:W,cMapUrl:f,iccUrl:v,standardFontDataUrl:y,wasmUrl:_}},Y={ownerDocument:k,pdfBug:z,styleElement:null,loadingParams:{disableAutoFetch:B,enableXfa:R}};return u.promise.then(function(){let t;if(e.destroyed)throw Error("Loading aborted");if(u.destroyed)throw Error("Worker was destroyed");let r=u.messageHandler.sendWithPromise("GetDocRequest",K,n?[n.buffer]:null);if(d)t=new eA(d,{disableRange:N,disableStream:O});else if(!n){if(!s)throw Error("getDocument - no `url` parameter provided.");t=new(J(s)?eP:a?eO:ek)({url:s,length:j,httpHeaders:o,withCredentials:l,rangeChunkSize:c,disableRange:N,disableStream:O})}return r.then(s=>{if(e.destroyed)throw Error("Loading aborted");if(u.destroyed)throw Error("Worker was destroyed");let r=new tF(i,s,u.port),a=new eY(r,e,t,Y,q);e._transport=a,r.send("Ready",null)})}).catch(e._capability.reject),e}class eV{static #e7=0;_capability=Promise.withResolvers();_transport=null;_worker=null;docId=`d${eV.#e7++}`;destroyed=!1;onPassword=null;onProgress=null;get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0),await this._transport?.destroy()}catch(t){throw this._worker?.port&&delete this._worker._pendingDestroy,t}this._transport=null,this._worker?.destroy(),this._worker=null}async getData(){return this._transport.getData()}}class eW{#iR=Promise.withResolvers();#i8=[];#i7=[];#i9=[];#st=[];constructor(t,e,i=!1,s=null){this.length=t,this.initialData=e,this.progressiveDone=i,this.contentDispositionFilename=s}addRangeListener(t){this.#st.push(t)}addProgressListener(t){this.#i9.push(t)}addProgressiveReadListener(t){this.#i7.push(t)}addProgressiveDoneListener(t){this.#i8.push(t)}onDataRange(t,e){for(let i of this.#st)i(t,e)}onDataProgress(t,e){this.#iR.promise.then(()=>{for(let i of this.#i9)i(t,e)})}onDataProgressiveRead(t){this.#iR.promise.then(()=>{for(let e of this.#i7)e(t)})}onDataProgressiveDone(){this.#iR.promise.then(()=>{for(let t of this.#i8)t()})}transportReady(){this.#iR.resolve()}requestDataRange(t,e){w("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class eq{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return S(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){let{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class eK{#se=!1;constructor(t,e,i,s=!1){this._pageIndex=t,this._pageInfo=e,this._transport=i,this._stats=s?new Q:null,this._pdfBug=s,this.commonObjs=i.commonObjs,this.objs=new eU,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:s=0,dontFlip:r=!1}={}){return new q({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:r})}getAnnotations({intent:t="display"}={}){let{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return S(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:i="display",annotationMode:s=l.ENABLE,transform:r=null,background:a=null,optionalContentConfigPromise:n=null,annotationCanvasMap:h=null,pageColors:d=null,printAnnotationStorage:c=null,isEditing:u=!1}){this._stats?.time("Overall");let p=this._transport.getRenderingIntent(i,s,c,u),{renderingIntent:g,cacheKey:f}=p;this.#se=!1,n||=this._transport.getOptionalContentConfig(g);let m=this._intentStates.get(f);m||(m=Object.create(null),this._intentStates.set(f,m)),m.streamReaderCancelTimeout&&(clearTimeout(m.streamReaderCancelTimeout),m.streamReaderCancelTimeout=null);let b=!!(g&o.PRINT);m.displayReadyCapability||(m.displayReadyCapability=Promise.withResolvers(),m.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(p));let A=t=>{m.renderTasks.delete(v),b&&(this.#se=!0),this.#si(),t?(v.capability.reject(t),this._abortOperatorList({intentState:m,reason:t instanceof Error?t:Error(t)})):v.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},v=new eJ({callback:A,params:{canvasContext:t,viewport:e,transform:r,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:h,operatorList:m.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!b,pdfBug:this._pdfBug,pageColors:d});(m.renderTasks||=new Set).add(v);let y=v.task;return Promise.all([m.displayReadyCapability.promise,n]).then(([t,e])=>{if(this.destroyed)return void A();if(this._stats?.time("Rendering"),!(e.renderingIntent&g))throw Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");v.initializeGraphics({transparency:t,optionalContentConfig:e}),v.operatorListChanged()}).catch(A),y}getOperatorList({intent:t="display",annotationMode:e=l.ENABLE,printAnnotationStorage:i=null,isEditing:s=!1}={}){let r,a=this._transport.getRenderingIntent(t,e,i,s,!0),n=this._intentStates.get(a.cacheKey);return n||(n=Object.create(null),this._intentStates.set(a.cacheKey,n)),n.opListReadCapability||((r=Object.create(null)).operatorListChanged=function(){n.operatorList.lastChunk&&(n.opListReadCapability.resolve(n.operatorList),n.renderTasks.delete(r))},n.opListReadCapability=Promise.withResolvers(),(n.renderTasks||=new Set).add(r),n.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(a)),n.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then(t=>eG.textContent(t));let e=this.streamTextContent(t);return new Promise(function(t,i){let s=e.getReader(),r={items:[],styles:Object.create(null),lang:null};!function e(){s.read().then(function({value:i,done:s}){if(s)return void t(r);r.lang??=i.lang,Object.assign(r.styles,i.styles),r.items.push(...i.items),e()},i)}()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;let t=[];for(let e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(let i of e.renderTasks)t.push(i.completed),i.cancel();return this.objs.clear(),this.#se=!1,Promise.all(t)}cleanup(t=!1){this.#se=!0;let e=this.#si();return t&&e&&(this._stats&&=new Q),e}#si(){if(!this.#se||this.destroyed)return!1;for(let{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),this.#se=!1,!0}_startRenderPage(t,e){let i=this._intentStates.get(e);i&&(this._stats?.timeEnd("Page Request"),i.displayReadyCapability?.resolve(t))}_renderPageChunk(t,e){for(let i=0,s=t.length;i<s;i++)e.operatorList.fnArray.push(t.fnArray[i]),e.operatorList.argsArray.push(t.argsArray[i]);for(let i of(e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots,e.renderTasks))i.operatorListChanged();t.lastChunk&&this.#si()}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:i,modifiedIds:s}){let{map:r,transfer:a}=i,n=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:r,modifiedIds:s},a).getReader(),o=this._intentStates.get(e);o.streamReader=n;let l=()=>{n.read().then(({value:t,done:e})=>{if(e){o.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(t,o),l())},t=>{if(o.streamReader=null,!this._transport.destroyed){if(o.operatorList){for(let t of(o.operatorList.lastChunk=!0,o.renderTasks))t.operatorListChanged();this.#si()}if(o.displayReadyCapability)o.displayReadyCapability.reject(t);else if(o.opListReadCapability)o.opListReadCapability.reject(t);else throw t}})};l()}_abortOperatorList({intentState:t,reason:e,force:i=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!i){if(t.renderTasks.size>0)return;if(e instanceof K){let i=100;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),t.streamReaderCancelTimeout=setTimeout(()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})},i);return}}if(t.streamReader.cancel(new R(e.message)).catch(()=>{}),t.streamReader=null,!this._transport.destroyed){for(let[e,i]of this._intentStates)if(i===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}class eX{#iR=Promise.withResolvers();#ss=null;#ib=null;#sr=null;static #sa=0;static #sn=!1;static #so=new WeakMap;static{a&&(this.#sn=!0,ep.workerSrc||="./pdf.worker.mjs"),this._isSameOrigin=(t,e)=>{let i=URL.parse(t);if(!i?.origin||"null"===i.origin)return!1;let s=new URL(e,i);return i.origin===s.origin},this._createCDNWrapper=t=>{let e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))},this.fromPort=t=>{if(console.log("Deprecated API usage: `PDFWorker.fromPort` - please use `PDFWorker.create` instead."),!t?.port)throw Error("PDFWorker.fromPort - invalid method signature.");return this.create(t)}}constructor({name:t=null,port:e=null,verbosity:i=A}={}){if(this.name=t,this.destroyed=!1,this.verbosity=i,e){if(eX.#so.has(e))throw Error("Cannot use more than one PDFWorker per port.");eX.#so.set(e,this),this.#sl(e)}else this.#sh()}get promise(){return this.#iR.promise}#sd(){this.#iR.resolve(),this.#ss.send("configure",{verbosity:this.verbosity})}get port(){return this.#ib}get messageHandler(){return this.#ss}#sl(t){this.#ib=t,this.#ss=new tF("main","worker",t),this.#ss.on("ready",()=>{}),this.#sd()}#sh(){if(eX.#sn||eX.#sc)return void this.#su();let{workerSrc:t}=eX;try{eX._isSameOrigin(window.location,t)||(t=eX._createCDNWrapper(new URL(t,window.location).href));let e=new Worker(t,{type:"module"}),i=new tF("main","worker",e),s=()=>{r.abort(),i.destroy(),e.terminate(),this.destroyed?this.#iR.reject(Error("Worker was destroyed")):this.#su()},r=new AbortController;e.addEventListener("error",()=>{this.#sr||s()},{signal:r.signal}),i.on("test",t=>{if(r.abort(),this.destroyed||!t)return void s();this.#ss=i,this.#ib=e,this.#sr=e,this.#sd()}),i.on("ready",t=>{if(r.abort(),this.destroyed)return void s();try{a()}catch{this.#su()}});let a=()=>{let t=new Uint8Array;i.send("test",t,[t.buffer])};a();return}catch{v("The worker has been disabled.")}this.#su()}#su(){eX.#sn||(y("Setting up fake worker."),eX.#sn=!0),eX._setupFakeWorkerGlobal.then(t=>{if(this.destroyed)return void this.#iR.reject(Error("Worker was destroyed"));let e=new tI;this.#ib=e;let i=`fake${eX.#sa++}`,s=new tF(i+"_worker",i,e);t.setup(s,e),this.#ss=new tF(i,i+"_worker",e),this.#sd()}).catch(t=>{this.#iR.reject(Error(`Setting up fake worker failed: "${t.message}".`))})}destroy(){this.destroyed=!0,this.#sr?.terminate(),this.#sr=null,eX.#so.delete(this.#ib),this.#ib=null,this.#ss?.destroy(),this.#ss=null}static create(t){let e=this.#so.get(t?.port);if(e){if(e._pendingDestroy)throw Error("PDFWorker.create - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new eX(t)}static get workerSrc(){if(ep.workerSrc)return ep.workerSrc;throw Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get #sc(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){return S(this,"_setupFakeWorkerGlobal",(async()=>this.#sc?this.#sc:(await import(this.workerSrc)).WorkerMessageHandler)())}}class eY{#sp=new Map;#sg=new Map;#sf=new Map;#sm=new Map;#sb=null;constructor(t,e,i,s,r){this.messageHandler=t,this.loadingTask=e,this.commonObjs=new eU,this.fontLoader=new tS({ownerDocument:s.ownerDocument,styleElement:s.styleElement}),this.loadingParams=s.loadingParams,this._params=s,this.canvasFactory=r.canvasFactory,this.filterFactory=r.filterFactory,this.cMapReaderFactory=r.cMapReaderFactory,this.standardFontDataFactory=r.standardFontDataFactory,this.wasmFactory=r.wasmFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=i,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}#sA(t,e=null){let i=this.#sp.get(t);if(i)return i;let s=this.messageHandler.sendWithPromise(t,e);return this.#sp.set(t,s),s}get annotationStorage(){return S(this,"annotationStorage",new tx)}getRenderingIntent(t,e=l.ENABLE,i=null,s=!1,r=!1){let a=o.DISPLAY,n=t_;switch(t){case"any":a=o.ANY;break;case"display":break;case"print":a=o.PRINT;break;default:y(`getRenderingIntent - invalid intent: ${t}`)}let h=a&o.PRINT&&i instanceof tE?i:this.annotationStorage;switch(e){case l.DISABLE:a+=o.ANNOTATIONS_DISABLE;break;case l.ENABLE:break;case l.ENABLE_FORMS:a+=o.ANNOTATIONS_FORMS;break;case l.ENABLE_STORAGE:a+=o.ANNOTATIONS_STORAGE,n=h.serializable;break;default:y(`getRenderingIntent - invalid annotationMode: ${e}`)}s&&(a+=o.IS_EDITING),r&&(a+=o.OPLIST);let{ids:d,hash:c}=h.modifiedIds,u=[a,n.hash,c];return{renderingIntent:a,cacheKey:u.join("_"),annotationStorageSerializable:n,modifiedIds:d}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),this.#sb?.reject(Error("Worker was destroyed during onPassword callback"));let t=[];for(let e of this.#sg.values())t.push(e._destroy());this.#sg.clear(),this.#sf.clear(),this.#sm.clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();let e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then(()=>{this.commonObjs.clear(),this.fontLoader.clear(),this.#sp.clear(),this.filterFactory.destroy(),e$.cleanup(),this._networkStream?.cancelAllRequests(new R("Worker was terminated.")),this.messageHandler?.destroy(),this.messageHandler=null,this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){let{messageHandler:t,loadingTask:e}=this;t.on("GetReader",(t,e)=>{_(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}},e.onPull=()=>{this._fullReader.read().then(function({value:t,done:i}){if(i)return void e.close();_(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t])}).catch(t=>{e.error(t)})},e.onCancel=t=>{this._fullReader.cancel(t),e.ready.catch(t=>{if(!this.destroyed)throw t})}}),t.on("ReaderHeadersReady",async t=>{await this._fullReader.headersReady;let{isStreamingSupported:i,isRangeSupported:s,contentLength:r}=this._fullReader;return i&&s||(this._lastProgress&&e.onProgress?.(this._lastProgress),this._fullReader.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}),{isStreamingSupported:i,isRangeSupported:s,contentLength:r}}),t.on("GetRangeReader",(t,e)=>{_(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");let i=this._networkStream.getRangeReader(t.begin,t.end);if(!i)return void e.close();e.onPull=()=>{i.read().then(function({value:t,done:i}){if(i)return void e.close();_(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t])}).catch(t=>{e.error(t)})},e.onCancel=t=>{i.cancel(t),e.ready.catch(t=>{if(!this.destroyed)throw t})}}),t.on("GetDoc",({pdfInfo:t})=>{this._numPages=t.numPages,this._htmlForXfa=t.htmlForXfa,delete t.htmlForXfa,e._capability.resolve(new eq(t,this))}),t.on("DocException",t=>{e._capability.reject(tL(t))}),t.on("PasswordRequest",t=>{this.#sb=Promise.withResolvers();try{if(!e.onPassword)throw tL(t);e.onPassword(t=>{t instanceof Error?this.#sb.reject(t):this.#sb.resolve({password:t})},t.code)}catch(t){this.#sb.reject(t)}return this.#sb.promise}),t.on("DataLoaded",t=>{e.onProgress?.({loaded:t.length,total:t.length}),this.downloadInfoCapability.resolve(t)}),t.on("StartRenderPage",t=>{this.destroyed||this.#sg.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}),t.on("commonobj",([e,i,s])=>{if(this.destroyed||this.commonObjs.has(e))return null;switch(i){case"Font":if("error"in s){let t=s.error;y(`Error during font loading: ${t}`),this.commonObjs.resolve(e,t);break}let r=new tC(s,this._params.pdfBug&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null);this.fontLoader.bind(r).catch(()=>t.sendWithPromise("FontFallback",{id:e})).finally(()=>{!r.fontExtraProperties&&r.data&&(r.data=null),this.commonObjs.resolve(e,r)});break;case"CopyLocalImage":let{imageRef:a}=s;for(let t of(_(a,"The imageRef must be defined."),this.#sg.values()))for(let[,i]of t.objs)if(i?.ref===a){if(!i.dataLen)return null;return this.commonObjs.resolve(e,structuredClone(i)),i.dataLen}break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,s);break;default:throw Error(`Got unknown common object type ${i}`)}return null}),t.on("obj",([t,e,i,s])=>{if(this.destroyed)return;let r=this.#sg.get(e);if(!r.objs.has(t)){if(0===r._intentStates.size)return void s?.bitmap?.close();switch(i){case"Image":case"Pattern":r.objs.resolve(t,s);break;default:throw Error(`Got unknown object type ${i}`)}}}),t.on("DocProgress",t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})}),t.on("FetchBinaryData",async t=>{if(this.destroyed)throw Error("Worker was destroyed.");let e=this[t.type];if(!e)throw Error(`${t.type} not initialized, see the \`useWorkerFetch\` parameter.`);return e.fetch(t)})}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&y("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");let{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally(()=>{this.annotationStorage.resetModified()})}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(Error("Invalid page request."));let e=t-1,i=this.#sf.get(e);if(i)return i;let s=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then(i=>{if(this.destroyed)throw Error("Transport destroyed");i.refStr&&this.#sm.set(i.refStr,t);let s=new eK(e,i,this,this._params.pdfBug);return this.#sg.set(e,s),s});return this.#sf.set(e,s),s}getPageIndex(t){return tM(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#sA("GetFieldObjects")}hasJSActions(){return this.#sA("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#sA("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#sA("GetOptionalContentConfig").then(e=>new eb(e,t))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){let t="GetMetadata",e=this.#sp.get(t);if(e)return e;let i=this.messageHandler.sendWithPromise(t,null).then(t=>({info:t[0],metadata:t[1]?new eg(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null}));return this.#sp.set(t,i),i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){for(let t of(await this.messageHandler.sendWithPromise("Cleanup",null),this.#sg.values()))if(!t.cleanup())throw Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`);this.commonObjs.clear(),t||this.fontLoader.clear(),this.#sp.clear(),this.filterFactory.destroy(!0),e$.cleanup()}}cachedPageNumber(t){if(!tM(t))return null;let e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return this.#sm.get(e)??null}}class eQ{#sv=null;onContinue=null;onError=null;constructor(t){this.#sv=t}get promise(){return this.#sv.capability.promise}cancel(t=0){this.#sv.cancel(null,t)}get separateAnnots(){let{separateAnnots:t}=this.#sv.operatorList;if(!t)return!1;let{annotationCanvasMap:e}=this.#sv;return t.form||t.canvas&&e?.size>0}}class eJ{#sy=null;static #sw=new WeakSet;constructor({callback:t,params:e,objs:i,commonObjs:s,annotationCanvasMap:r,operatorList:a,pageIndex:n,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:d=!1,pageColors:c=null}){this.callback=t,this.params=e,this.objs=i,this.commonObjs=s,this.annotationCanvasMap=r,this.operatorListIdx=null,this.operatorList=a,this._pageIndex=n,this.canvasFactory=o,this.filterFactory=l,this._pdfBug=d,this.pageColors=c,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window,this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new eQ(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(eJ.#sw.has(this._canvas))throw Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");eJ.#sw.add(this._canvas)}this._pdfBug&&globalThis.StepperManager?.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());let{canvasContext:i,viewport:s,transform:r,background:a}=this.params;this.gfx=new eu(i,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:r,viewport:s,transparency:t,background:a}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1,this.cancelled=!0,this.gfx?.endDrawing(),this.#sy&&(window.cancelAnimationFrame(this.#sy),this.#sy=null),eJ.#sw.delete(this._canvas),t||=new K(`Rendering cancelled, page ${this._pageIndex+1}`,e),this.callback(t),this.task.onError?.(t)}operatorListChanged(){if(!this.graphicsReady){this.graphicsReadyCallback||=this._continueBound;return}this.stepper?.updateOperatorList(this.operatorList),this.running||this._continue()}_continue(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?this.#sy=window.requestAnimationFrame(()=>{this.#sy=null,this._nextBound().catch(this._cancelBound)}):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){!this.cancelled&&(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),eJ.#sw.delete(this._canvas),this.callback())))}}let eZ="5.3.31";function e0(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function e1(t){return Math.max(0,Math.min(255,255*t))}class e2{static CMYK_G([t,e,i,s]){return["G",1-Math.min(1,.3*t+.59*i+.11*e+s)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=e1(t),t,t]}static G_HTML([t]){let e=e0(t);return`#${e}${e}${e}`}static RGB_G([t,e,i]){return["G",.3*t+.59*e+.11*i]}static RGB_rgb(t){return t.map(e1)}static RGB_HTML(t){return`#${t.map(e0).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,i,s]){return["RGB",1-Math.min(1,t+s),1-Math.min(1,i+s),1-Math.min(1,e+s)]}static CMYK_rgb([t,e,i,s]){return[e1(1-Math.min(1,t+s)),e1(1-Math.min(1,i+s)),e1(1-Math.min(1,e+s))]}static CMYK_HTML(t){let e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,i]){let s=1-t,r=1-e,a=1-i,n=Math.min(s,r,a);return["CMYK",s,r,a,n]}}class e3{create(t,e,i=!1){if(t<=0||e<=0)throw Error("Invalid SVG dimensions");let s=this._createSVG("svg:svg");return s.setAttribute("version","1.1"),i||(s.setAttribute("width",`${t}px`),s.setAttribute("height",`${e}px`)),s.setAttribute("preserveAspectRatio","none"),s.setAttribute("viewBox",`0 0 ${t} ${e}`),s}createElement(t){if("string"!=typeof t)throw Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){w("Abstract method `_createSVG` called.")}}class e5 extends e3{_createSVG(t){return document.createElementNS(j,t)}}class e6{static setupStorage(t,e,i,s,r){let a=s.getValue(e,{value:null});switch(i.name){case"textarea":if(null!==a.value&&(t.textContent=a.value),"print"===r)break;t.addEventListener("input",t=>{s.setValue(e,{value:t.target.value})});break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){if(a.value===i.attributes.xfaOn?t.setAttribute("checked",!0):a.value===i.attributes.xfaOff&&t.removeAttribute("checked"),"print"===r)break;t.addEventListener("change",t=>{s.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})})}else{if(null!==a.value&&t.setAttribute("value",a.value),"print"===r)break;t.addEventListener("input",t=>{s.setValue(e,{value:t.target.value})})}break;case"select":if(null!==a.value)for(let e of(t.setAttribute("value",a.value),i.children))e.attributes.value===a.value?e.attributes.selected=!0:e.attributes.hasOwnProperty("selected")&&delete e.attributes.selected;t.addEventListener("input",t=>{let i=t.target.options,r=-1===i.selectedIndex?"":i[i.selectedIndex].value;s.setValue(e,{value:r})})}}static setAttributes({html:t,element:e,storage:i=null,intent:s,linkService:r}){let{attributes:a}=e,n=t instanceof HTMLAnchorElement;for(let[e,i]of("radio"===a.type&&(a.name=`${a.name}-${s}`),Object.entries(a)))if(null!=i)switch(e){case"class":i.length&&t.setAttribute(e,i.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",i);break;case"style":Object.assign(t.style,i);break;case"textContent":t.textContent=i;break;default:n&&("href"===e||"newWindow"===e)||t.setAttribute(e,i)}n&&r.addLinkAttributes(t,a.href,a.newWindow),i&&a.dataId&&this.setupStorage(t,a.dataId,e,i)}static render(t){let e=t.annotationStorage,i=t.linkService,s=t.xfaHtml,r=t.intent||"display",a=document.createElement(s.name);s.attributes&&this.setAttributes({html:a,element:s,intent:r,linkService:i});let n="richText"!==r,o=t.div;if(o.append(a),t.viewport){let e=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=e}n&&o.setAttribute("class","xfaLayer xfaFont");let l=[];if(0===s.children.length){if(s.value){let t=document.createTextNode(s.value);a.append(t),n&&eG.shouldBuildText(s.name)&&l.push(t)}return{textDivs:l}}let h=[[s,-1,a]];for(;h.length>0;){let[t,s,a]=h.at(-1);if(s+1===t.children.length){h.pop();continue}let o=t.children[++h.at(-1)[1]];if(null===o)continue;let{name:d}=o;if("#text"===d){let t=document.createTextNode(o.value);l.push(t),a.append(t);continue}let c=o?.attributes?.xmlns?document.createElementNS(o.attributes.xmlns,d):document.createElement(d);if(a.append(c),o.attributes&&this.setAttributes({html:c,element:o,storage:e,intent:r,linkService:i}),o.children?.length>0)h.push([o,-1,c]);else if(o.value){let t=document.createTextNode(o.value);n&&eG.shouldBuildText(d)&&l.push(t),c.append(t)}}for(let t of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:l}}static update(t){let e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}let e4=new WeakSet;class e8{static create(t){switch(t.data.annotationType){case p.LINK:return new e9(t);case p.TEXT:return new it(t);case p.WIDGET:switch(t.data.fieldType){case"Tx":return new ii(t);case"Btn":if(t.data.radioButton)return new ia(t);if(t.data.checkBox)return new ir(t);return new io(t);case"Ch":return new il(t);case"Sig":return new is(t)}return new ie(t);case p.POPUP:return new ih(t);case p.FREETEXT:return new ic(t);case p.LINE:return new iu(t);case p.SQUARE:return new ip(t);case p.CIRCLE:return new ig(t);case p.POLYLINE:return new im(t);case p.CARET:return new iA(t);case p.INK:return new iv(t);case p.POLYGON:return new ib(t);case p.HIGHLIGHT:return new iy(t);case p.UNDERLINE:return new iw(t);case p.SQUIGGLY:return new i_(t);case p.STRIKEOUT:return new ix(t);case p.STAMP:return new iE(t);case p.FILEATTACHMENT:return new iS(t);default:return new e7(t)}}}class e7{#s_=null;#sx=!1;#sE=null;constructor(t,{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:s=!1}={}){this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(i)),s&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:i}){return!!(t?.str||e?.str||i?.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return e7._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;this.#s_||={rect:this.data.rect.slice(0)};let{rect:e}=t;e&&this.#sS(e),this.#sE?.popup.updateEdited(t)}resetEdited(){this.#s_&&(this.#sS(this.#s_.rect),this.#sE?.popup.resetEdited(),this.#s_=null)}#sS(t){let{container:{style:e},data:{rect:i,rotation:s},parent:{viewport:{rawDims:{pageWidth:r,pageHeight:a,pageX:n,pageY:o}}}}=this;i?.splice(0,4,...t),e.left=`${100*(t[0]-n)/r}%`,e.top=`${100*(a-t[3]+o)/a}%`,0===s?(e.width=`${100*(t[2]-t[0])/r}%`,e.height=`${100*(t[3]-t[1])/a}%`):this.setRotation(s)}_createContainer(t){let{data:e,parent:{page:i,viewport:s}}=this,r=document.createElement("section");r.setAttribute("data-annotation-id",e.id),this instanceof ie||(r.tabIndex=1e3);let{style:a}=r;if(a.zIndex=this.parent.zIndex++,e.alternativeText&&(r.title=e.alternativeText),e.noRotate&&r.classList.add("norotate"),!e.rect||this instanceof ih){let{rotation:t}=e;return e.hasOwnCanvas||0===t||this.setRotation(t,r),r}let{width:n,height:o}=this;if(!t&&e.borderStyle.width>0){a.borderWidth=`${e.borderStyle.width}px`;let t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;switch(t>0||i>0?a.borderRadius=`calc(${t}px * var(--total-scale-factor)) / calc(${i}px * var(--total-scale-factor))`:this instanceof ia&&(a.borderRadius=`calc(${n}px * var(--total-scale-factor)) / calc(${o}px * var(--total-scale-factor))`),e.borderStyle.style){case g.SOLID:a.borderStyle="solid";break;case g.DASHED:a.borderStyle="dashed";break;case g.BEVELED:y("Unimplemented border style: beveled");break;case g.INSET:y("Unimplemented border style: inset");break;case g.UNDERLINE:a.borderBottomStyle="solid"}let s=e.borderColor||null;s?(this.#sx=!0,a.borderColor=O.makeHexColor(0|s[0],0|s[1],0|s[2])):a.borderWidth=0}let l=O.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]),{pageWidth:h,pageHeight:d,pageX:c,pageY:u}=s.rawDims;a.left=`${100*(l[0]-c)/h}%`,a.top=`${100*(l[1]-u)/d}%`;let{rotation:p}=e;return e.hasOwnCanvas||0===p?(a.width=`${100*n/h}%`,a.height=`${100*o/d}%`):this.setRotation(p,r),r}setRotation(t,e=this.container){if(!this.data.rect)return;let{pageWidth:i,pageHeight:s}=this.parent.viewport.rawDims,{width:r,height:a}=this;t%180!=0&&([r,a]=[a,r]),e.style.width=`${100*r/i}%`,e.style.height=`${100*a/s}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){let t=(t,e,i)=>{let s=i.detail[t],r=s[0],a=s.slice(1);i.target.style[e]=e2[`${r}_HTML`](a),this.annotationStorage.setValue(this.data.id,{[e]:e2[`${r}_rgb`](a)})};return S(this,"_commonActions",{display:t=>{let{display:e}=t.detail,i=e%2==1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{let{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout(()=>t.target.focus({preventScroll:!1}),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:t=>{let e=t.detail.rotation;this.setRotation(e),this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){let i=this._commonActions;for(let s of Object.keys(e.detail)){let r=t[s]||i[s];r?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;let e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;let i=this._commonActions;for(let[s,r]of Object.entries(e)){let a=i[s];a&&(a({detail:{[s]:r},target:t}),delete e[s])}}_createQuadrilaterals(){let t;if(!this.container)return;let{quadPoints:e}=this.data;if(!e)return;let[i,s,r,a]=this.data.rect.map(t=>Math.fround(t));if(8===e.length){let[t,n,o,l]=e.subarray(2,6);if(r===t&&a===n&&i===o&&s===l)return}let{style:n}=this.container;if(this.#sx){let{borderColor:e,borderWidth:i}=n;n.borderWidth=0,t=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${e}" stroke-width="${i}">`],this.container.classList.add("hasBorder")}let o=r-i,l=a-s,{svgFactory:h}=this,d=h.createElement("svg");d.classList.add("quadrilateralsContainer"),d.setAttribute("width",0),d.setAttribute("height",0);let c=h.createElement("defs");d.append(c);let u=h.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p),u.setAttribute("clipPathUnits","objectBoundingBox"),c.append(u);for(let s=2,r=e.length;s<r;s+=8){let r=e[s],n=e[s+1],d=e[s+2],c=e[s+3],p=h.createElement("rect"),g=(d-i)/o,f=(a-n)/l,m=(r-d)/o,b=(n-c)/l;p.setAttribute("x",g),p.setAttribute("y",f),p.setAttribute("width",m),p.setAttribute("height",b),u.append(p),t?.push(`<rect vector-effect="non-scaling-stroke" x="${g}" y="${f}" width="${m}" height="${b}"/>`)}this.#sx&&(t.push("</g></svg>')"),n.backgroundImage=t.join("")),this.container.append(d),this.container.style.clipPath=`url(#${p})`}_createPopup(){let{data:t}=this,e=this.#sE=new ih({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation},parent:this.parent,elements:[this]});this.parent.div.append(e.render())}render(){w("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){let i=[];if(this._fieldObjects){let s=this._fieldObjects[t];if(s)for(let{page:t,id:r,exportValues:a}of s){if(-1===t||r===e)continue;let s="string"==typeof a?a:null,n=document.querySelector(`[data-element-id="${r}"]`);if(n&&!e4.has(n)){y(`_getElementsByName - element not allowed: ${r}`);continue}i.push({id:r,exportValue:s,domElement:n})}return i}for(let s of document.getElementsByName(t)){let{exportValue:t}=s,r=s.getAttribute("data-element-id");r!==e&&e4.has(s)&&i.push({id:r,exportValue:t,domElement:s})}return i}show(){this.container&&(this.container.hidden=!1),this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0),this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){let t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(let e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;let{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})})}get width(){return this.data.rect[2]-this.data.rect[0]}get height(){return this.data.rect[3]-this.data.rect[1]}}class e9 extends e7{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0}),this.isTooltipOnly=t.data.isTooltipOnly}render(){let{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let s=!1;return t.url?(e.addLinkAttributes(i,t.url,t.newWindow),s=!0):t.action?(this._bindNamedAction(i,t.action),s=!0):t.attachment?(this.#sC(i,t.attachment,t.attachmentDest),s=!0):t.setOCGState?(this.#sT(i,t.setOCGState),s=!0):t.dest?(this._bindLink(i,t.dest),s=!0):(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,t),s=!0),t.resetForm?(this._bindResetFormAction(i,t.resetForm),s=!0):this.isTooltipOnly&&!s&&(this._bindLink(i,""),s=!0)),this.container.classList.add("linkAnnotation"),s&&this.container.append(i),this.container}#sM(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e),t.onclick=()=>(e&&this.linkService.goToDestination(e),!1),(e||""===e)&&this.#sM()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeNamedAction(e),!1),this.#sM()}#sC(t,e,i=null){t.href=this.linkService.getAnchorUrl(""),e.description&&(t.title=e.description),t.onclick=()=>(this.downloadManager?.openOrDownloadData(e.content,e.filename,i),!1),this.#sM()}#sT(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeSetOCGState(e),!1),this.#sM()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");let i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(let s of Object.keys(e.actions)){let r=i.get(s);r&&(t[r]=()=>(this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:s}}),!1))}t.onclick||(t.onclick=()=>!1),this.#sM()}_bindResetFormAction(t,e){let i=t.onclick;if(i||(t.href=this.linkService.getAnchorUrl("")),this.#sM(),!this._fieldObjects){y('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),i||(t.onclick=()=>!1);return}t.onclick=()=>{i?.();let{fields:t,refs:s,include:r}=e,a=[];if(0!==t.length||0!==s.length){let e=new Set(s);for(let i of t)for(let{id:t}of this._fieldObjects[i]||[])e.add(t);for(let t of Object.values(this._fieldObjects))for(let i of t)e.has(i.id)===r&&a.push(i)}else for(let t of Object.values(this._fieldObjects))a.push(...t);let n=this.annotationStorage,o=[];for(let t of a){let{id:e}=t;switch(o.push(e),t.type){case"text":{let i=t.defaultValue||"";n.setValue(e,{value:i});break}case"checkbox":case"radiobutton":{let i=t.defaultValue===t.exportValues;n.setValue(e,{value:i});break}case"combobox":case"listbox":{let i=t.defaultValue||"";n.setValue(e,{value:i});break}default:continue}let i=document.querySelector(`[data-element-id="${e}"]`);if(i){if(!e4.has(i)){y(`_bindResetFormAction - element not allowed: ${e}`);continue}i.dispatchEvent(new Event("resetform"))}}return this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:o,name:"ResetForm"}}),!1}}}class it extends e7{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");let t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class ie extends e7{render(){return this.container}showElementAndHideCanvas(t){this.data.hasOwnCanvas&&(t.previousSibling?.nodeName==="CANVAS"&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return F.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,i,s,r){i.includes("mouse")?t.addEventListener(i,t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:r(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})}):t.addEventListener(i,t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}r&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:r(t)}})})}_setEventListeners(t,e,i,s){for(let[r,a]of i)("Action"===a||this.data.actions?.[a])&&(("Focus"===a||"Blur"===a)&&(e||={focused:!1}),this._setEventListener(t,e,r,a,s),"Focus"!==a||this.data.actions?.Blur?"Blur"!==a||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null))}_setBackgroundColor(t){let e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":O.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){let e,{fontColor:i}=this.data.defaultAppearanceData,s=this.data.defaultAppearanceData.fontSize||9,r=t.style,a=t=>Math.round(10*t)/10;if(this.data.multiLine){let t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),i=Math.round(t/(1.35*s))||1;e=Math.min(s,a(t/i/1.35))}else e=Math.min(s,a(Math.abs(this.data.rect[3]-this.data.rect[1]-2)/1.35));r.fontSize=`calc(${e}px * var(--total-scale-factor))`,r.color=O.makeHexColor(i[0],i[1],i[2]),null!==this.data.textAlignment&&(r.textAlign=["left","center","right"][this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class ii extends ie{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,s){let r=this.annotationStorage;for(let a of this._getElementsByName(t.name,t.id))a.domElement&&(a.domElement[e]=i),r.setValue(a.id,{[s]:i})}render(){let t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){let s=t.getValue(e,{value:this.data.fieldValue}),r=s.value||"",a=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;a&&r.length>a&&(r=r.slice(0,a));let n=s.formattedValue||this.data.textContent?.join("\n")||null;n&&this.data.comb&&(n=n.replaceAll(/\s+/g,""));let o={userValue:r,formattedValue:n,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?((i=document.createElement("textarea")).textContent=n??r,this.data.doNotScroll&&(i.style.overflowY="hidden")):((i=document.createElement("input")).type=this.data.password?"password":"text",i.setAttribute("value",n??r),this.data.doNotScroll&&(i.style.overflowX="hidden")),this.data.hasOwnCanvas&&(i.hidden=!0),e4.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,i.name=this.data.fieldName,i.tabIndex=1e3,this._setRequired(i,this.data.required),a&&(i.maxLength=a),i.addEventListener("input",s=>{t.setValue(e,{value:s.target.value}),this.setPropertyOnSiblings(i,"value",s.target.value,"value"),o.formattedValue=null}),i.addEventListener("resetform",t=>{let e=this.data.defaultFieldValue??"";i.value=o.userValue=e,o.formattedValue=null});let l=t=>{let{formattedValue:e}=o;null!=e&&(t.target.value=e),t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",t=>{if(o.focused)return;let{target:e}=t;o.userValue&&(e.value=o.userValue),o.lastCommittedValue=e.value,o.commitKey=1,this.data.actions?.Focus||(o.focused=!0)}),i.addEventListener("updatefromsandbox",i=>{this.showElementAndHideCanvas(i.target),this._dispatchEventFromSandbox({value(i){o.userValue=i.detail.value??"",t.setValue(e,{value:o.userValue.toString()}),i.target.value=o.userValue},formattedValue(i){let{formattedValue:s}=i.detail;o.formattedValue=s,null!=s&&i.target!==document.activeElement&&(i.target.value=s),t.setValue(e,{formattedValue:s})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{let{charLimit:s}=i.detail,{target:r}=i;if(0===s)return void r.removeAttribute("maxLength");r.setAttribute("maxLength",s);let a=o.userValue;a&&!(a.length<=s)&&(r.value=o.userValue=a=a.slice(0,s),t.setValue(e,{value:a}),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,willCommit:!0,commitKey:1,selStart:r.selectionStart,selEnd:r.selectionEnd}}))}},i)}),i.addEventListener("keydown",t=>{o.commitKey=1;let i=-1;if("Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):i=2,-1===i)return;let{value:s}=t.target;o.lastCommittedValue!==s&&(o.lastCommittedValue=s,o.userValue=s,this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}))});let s=l;l=null,i.addEventListener("blur",t=>{if(!o.focused||!t.relatedTarget)return;this.data.actions?.Blur||(o.focused=!1);let{value:i}=t.target;o.userValue=i,o.lastCommittedValue!==i&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}),s(t)}),this.data.actions?.Keystroke&&i.addEventListener("beforeinput",t=>{o.lastCommittedValue=null;let{data:i,target:s}=t,{value:r,selectionStart:a,selectionEnd:n}=s,l=a,h=n;switch(t.inputType){case"deleteWordBackward":{let t=r.substring(0,a).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{let t=r.substring(a).match(/^[^\w]*\w*/);t&&(h+=t[0].length);break}case"deleteContentBackward":a===n&&(l-=1);break;case"deleteContentForward":a===n&&(h+=1)}t.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,change:i||"",willCommit:!1,selStart:l,selEnd:h}})}),this._setEventListeners(i,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],t=>t.target.value)}if(l&&i.addEventListener("blur",l),this.data.comb){let t=(this.data.rect[2]-this.data.rect[0])/a;i.classList.add("comb"),i.style.letterSpacing=`calc(${t}px * var(--total-scale-factor) - 1ch)`}}else(i=document.createElement("div")).textContent=this.data.fieldValue,i.style.verticalAlign="middle",i.style.display="table-cell",this.data.hasOwnCanvas&&(i.hidden=!0);return this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class is extends ie{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class ir extends ie{constructor(t){super(t,{isRenderable:t.renderForms})}render(){let t=this.annotationStorage,e=this.data,i=e.id,s=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;"string"==typeof s&&(s="Off"!==s,t.setValue(i,{value:s})),this.container.classList.add("buttonWidgetAnnotation","checkBox");let r=document.createElement("input");return e4.add(r),r.setAttribute("data-element-id",i),r.disabled=e.readOnly,this._setRequired(r,this.data.required),r.type="checkbox",r.name=e.fieldName,s&&r.setAttribute("checked",!0),r.setAttribute("exportValue",e.exportValue),r.tabIndex=1e3,r.addEventListener("change",s=>{let{name:r,checked:a}=s.target;for(let s of this._getElementsByName(r,i)){let i=a&&s.exportValue===e.exportValue;s.domElement&&(s.domElement.checked=i),t.setValue(s.id,{value:i})}t.setValue(i,{value:a})}),r.addEventListener("resetform",t=>{let i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue}),this.enableScripting&&this.hasJSActions&&(r.addEventListener("updatefromsandbox",e=>{this._dispatchEventFromSandbox({value(e){e.target.checked="Off"!==e.detail.value,t.setValue(i,{value:e.target.checked})}},e)}),this._setEventListeners(r,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],t=>t.target.checked)),this._setBackgroundColor(r),this._setDefaultPropertiesFromJS(r),this.container.append(r),this.container}}class ia extends ie{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");let t=this.annotationStorage,e=this.data,i=e.id,s=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof s&&(s=s!==e.buttonValue,t.setValue(i,{value:s})),s)for(let s of this._getElementsByName(e.fieldName,i))t.setValue(s.id,{value:!1});let r=document.createElement("input");if(e4.add(r),r.setAttribute("data-element-id",i),r.disabled=e.readOnly,this._setRequired(r,this.data.required),r.type="radio",r.name=e.fieldName,s&&r.setAttribute("checked",!0),r.tabIndex=1e3,r.addEventListener("change",e=>{let{name:s,checked:r}=e.target;for(let e of this._getElementsByName(s,i))t.setValue(e.id,{value:!1});t.setValue(i,{value:r})}),r.addEventListener("resetform",t=>{let i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue}),this.enableScripting&&this.hasJSActions){let s=e.buttonValue;r.addEventListener("updatefromsandbox",e=>{this._dispatchEventFromSandbox({value:e=>{let r=s===e.detail.value;for(let s of this._getElementsByName(e.target.name)){let e=r&&s.id===i;s.domElement&&(s.domElement.checked=e),t.setValue(s.id,{value:e})}}},e)}),this._setEventListeners(r,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],t=>t.target.checked)}return this._setBackgroundColor(r),this._setDefaultPropertiesFromJS(r),this.container.append(r),this.container}}class io extends e9{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){let t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");let e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",t=>{this._dispatchEventFromSandbox({},t)})),t}}class il extends ie{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");let t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),s=document.createElement("select");e4.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,this._setRequired(s,this.data.required),s.name=this.data.fieldName,s.tabIndex=1e3;let r=this.data.combo&&this.data.options.length>0;for(let t of(!this.data.combo&&(s.size=this.data.options.length,this.data.multiSelect&&(s.multiple=!0)),s.addEventListener("resetform",t=>{let e=this.data.defaultFieldValue;for(let t of s.options)t.selected=t.value===e}),this.data.options)){let e=document.createElement("option");e.textContent=t.displayValue,e.value=t.exportValue,i.value.includes(t.exportValue)&&(e.setAttribute("selected",!0),r=!1),s.append(e)}let a=null;if(r){let t=document.createElement("option");t.value=" ",t.setAttribute("hidden",!0),t.setAttribute("selected",!0),s.prepend(t),a=()=>{t.remove(),s.removeEventListener("input",a),a=null},s.addEventListener("input",a)}let n=t=>{let e=t?"value":"textContent",{options:i,multiple:r}=s;return r?Array.prototype.filter.call(i,t=>t.selected).map(t=>t[e]):-1===i.selectedIndex?null:i[i.selectedIndex][e]},o=n(!1),l=t=>{let e=t.target.options;return Array.prototype.map.call(e,t=>({displayValue:t.textContent,exportValue:t.value}))};return this.enableScripting&&this.hasJSActions?(s.addEventListener("updatefromsandbox",i=>{this._dispatchEventFromSandbox({value(i){a?.();let r=i.detail.value,l=new Set(Array.isArray(r)?r:[r]);for(let t of s.options)t.selected=l.has(t.value);t.setValue(e,{value:n(!0)}),o=n(!1)},multipleSelection(t){s.multiple=!0},remove(i){let r=s.options,a=i.detail.remove;r[a].selected=!1,s.remove(a),r.length>0&&-1===Array.prototype.findIndex.call(r,t=>t.selected)&&(r[0].selected=!0),t.setValue(e,{value:n(!0),items:l(i)}),o=n(!1)},clear(i){for(;0!==s.length;)s.remove(0);t.setValue(e,{value:null,items:[]}),o=n(!1)},insert(i){let{index:r,displayValue:a,exportValue:h}=i.detail.insert,d=s.children[r],c=document.createElement("option");c.textContent=a,c.value=h,d?d.before(c):s.append(c),t.setValue(e,{value:n(!0),items:l(i)}),o=n(!1)},items(i){let{items:r}=i.detail;for(;0!==s.length;)s.remove(0);for(let t of r){let{displayValue:e,exportValue:i}=t,r=document.createElement("option");r.textContent=e,r.value=i,s.append(r)}s.options.length>0&&(s.options[0].selected=!0),t.setValue(e,{value:n(!0),items:l(i)}),o=n(!1)},indices(i){let s=new Set(i.detail.indices);for(let t of i.target.options)t.selected=s.has(t.index);t.setValue(e,{value:n(!0)}),o=n(!1)},editable(t){t.target.disabled=!t.detail.editable}},i)}),s.addEventListener("input",i=>{let s=n(!0),r=n(!1);t.setValue(e,{value:s}),i.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:o,change:r,changeEx:s,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(s,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],t=>t.target.value)):s.addEventListener("input",function(i){t.setValue(e,{value:n(!0)})}),this.data.combo&&this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class ih extends e7{constructor(t){let{data:e,elements:i}=t;super(t,{isRenderable:e7._hasPopupData(e)}),this.elements=i,this.popup=null}render(){this.container.classList.add("popupAnnotation");let t=this.popup=new id({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(let i of this.elements)i.popup=t,i.container.ariaHasPopup="dialog",e.push(i.data.id),i.addHighlightArea();return this.container.setAttribute("aria-controls",e.map(t=>`${U}${t}`).join(",")),this.container}}class id{#sP=this.#sI.bind(this);#sD=this.#sR.bind(this);#sk=this.#sL.bind(this);#sF=this.#sN.bind(this);#sO=null;#tm=null;#sB=null;#sz=null;#sH=null;#sU=null;#s$=null;#sG=!1;#sj=null;#T=null;#sV=null;#sW=null;#sq=null;#s_=null;#sK=!1;constructor({container:t,color:e,elements:i,titleObj:s,modificationDate:r,contentsObj:a,richText:n,parent:o,rect:l,parentRect:h,open:d}){for(let d of(this.#tm=t,this.#sq=s,this.#sB=a,this.#sW=n,this.#sU=o,this.#sO=e,this.#sV=l,this.#s$=h,this.#sH=i,this.#sz=te.toDateObject(r),this.trigger=i.flatMap(t=>t.getElementsToTriggerPopup()),this.trigger))d.addEventListener("click",this.#sF),d.addEventListener("mouseenter",this.#sk),d.addEventListener("mouseleave",this.#sD),d.classList.add("popupTriggerArea");for(let t of i)t.container?.addEventListener("keydown",this.#sP);this.#tm.hidden=!0,d&&this.#sN()}render(){if(this.#sj)return;let t=this.#sj=document.createElement("div");if(t.className="popup",this.#sO){let e=t.style.outlineColor=O.makeHexColor(...this.#sO);t.style.backgroundColor=`color-mix(in srgb, ${e} 30%, white)`}let e=document.createElement("span");e.className="header";let i=document.createElement("h1");if(e.append(i),{dir:i.dir,str:i.textContent}=this.#sq,t.append(e),this.#sz){let t=document.createElement("span");t.classList.add("popupDate"),t.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string"),t.setAttribute("data-l10n-args",JSON.stringify({dateObj:this.#sz.valueOf()})),e.append(t)}let s=this.#sX;if(s)e6.render({xfaHtml:s,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{let e=this._formatContents(this.#sB);t.append(e)}this.#tm.append(t)}get #sX(){let t=this.#sW,e=this.#sB;return t?.str&&(!e?.str||e.str===t.str)&&this.#sW.html||null}get #sY(){return this.#sX?.attributes?.style?.fontSize||0}get #sQ(){return this.#sX?.attributes?.style?.color||null}#sJ(t){let e=[],i={style:{color:this.#sQ,fontSize:this.#sY?`calc(${this.#sY}px * var(--total-scale-factor))`:""}};for(let s of t.split("\n"))e.push({name:"span",value:s,attributes:i});return{str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}}}_formatContents({str:t,dir:e}){let i=document.createElement("p");i.classList.add("popupContent"),i.dir=e;let s=t.split(/(?:\r\n?|\n)/);for(let t=0,e=s.length;t<e;++t){let r=s[t];i.append(document.createTextNode(r)),t<e-1&&i.append(document.createElement("br"))}return i}#sI(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#sG)&&this.#sN()}updateEdited({rect:t,popupContent:e}){this.#s_||={contentsObj:this.#sB,richText:this.#sW},t&&(this.#T=null),e&&(this.#sW=this.#sJ(e),this.#sB=null),this.#sj?.remove(),this.#sj=null}resetEdited(){this.#s_&&({contentsObj:this.#sB,richText:this.#sW}=this.#s_,this.#s_=null,this.#sj?.remove(),this.#sj=null,this.#T=null)}#sZ(){if(null!==this.#T)return;let{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:i,pageX:s,pageY:r}}}=this.#sU,a=!!this.#s$,n=a?this.#s$:this.#sV;for(let t of this.#sH)if(!n||null!==O.intersect(t.data.rect,n)){n=t.data.rect,a=!0;break}let o=O.normalizeRect([n[0],t[3]-n[1]+t[1],n[2],t[3]-n[3]+t[1]]),l=a?n[2]-n[0]+5:0,h=o[0]+l,d=o[1];this.#T=[100*(h-s)/e,100*(d-r)/i];let{style:c}=this.#tm;c.left=`${this.#T[0]}%`,c.top=`${this.#T[1]}%`}#sN(){this.#sG=!this.#sG,this.#sG?(this.#sL(),this.#tm.addEventListener("click",this.#sF),this.#tm.addEventListener("keydown",this.#sP)):(this.#sR(),this.#tm.removeEventListener("click",this.#sF),this.#tm.removeEventListener("keydown",this.#sP))}#sL(){this.#sj||this.render(),this.isVisible?this.#sG&&this.#tm.classList.add("focused"):(this.#sZ(),this.#tm.hidden=!1,this.#tm.style.zIndex=parseInt(this.#tm.style.zIndex)+1e3)}#sR(){this.#tm.classList.remove("focused"),!this.#sG&&this.isVisible&&(this.#tm.hidden=!0,this.#tm.style.zIndex=parseInt(this.#tm.style.zIndex)-1e3)}forceHide(){this.#sK=this.isVisible,this.#sK&&(this.#tm.hidden=!0)}maybeShow(){this.#sK&&(this.#sj||this.#sL(),this.#sK=!1,this.#tm.hidden=!1)}get isVisible(){return!1===this.#tm.hidden}}class ic extends e7{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=h.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){let t=document.createElement("div");for(let e of(t.classList.add("annotationTextContent"),t.setAttribute("role","comment"),this.textContent)){let i=document.createElement("span");i.textContent=e,t.append(i)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class iu extends e7{#s0=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");let{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),r=this.#s0=this.svgFactory.createElement("svg:line");return r.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]),r.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]),r.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]),r.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]),r.setAttribute("stroke-width",t.borderStyle.width||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),s.append(r),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#s0}addHighlightArea(){this.container.classList.add("highlightArea")}}class ip extends e7{#s1=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");let{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),r=t.borderStyle.width,a=this.#s1=this.svgFactory.createElement("svg:rect");return a.setAttribute("x",r/2),a.setAttribute("y",r/2),a.setAttribute("width",e-r),a.setAttribute("height",i-r),a.setAttribute("stroke-width",r||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),s.append(a),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#s1}addHighlightArea(){this.container.classList.add("highlightArea")}}class ig extends e7{#s2=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");let{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),r=t.borderStyle.width,a=this.#s2=this.svgFactory.createElement("svg:ellipse");return a.setAttribute("cx",e/2),a.setAttribute("cy",i/2),a.setAttribute("rx",e/2-r/2),a.setAttribute("ry",i/2-r/2),a.setAttribute("stroke-width",r||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),s.append(a),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#s2}addHighlightArea(){this.container.classList.add("highlightArea")}}class im extends e7{#s3=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);let{data:{rect:t,vertices:e,borderStyle:i,popupRef:s},width:r,height:a}=this;if(!e)return this.container;let n=this.svgFactory.create(r,a,!0),o=[];for(let i=0,s=e.length;i<s;i+=2){let s=e[i]-t[0],r=t[3]-e[i+1];o.push(`${s},${r}`)}o=o.join(" ");let l=this.#s3=this.svgFactory.createElement(this.svgElementName);return l.setAttribute("points",o),l.setAttribute("stroke-width",i.width||1),l.setAttribute("stroke","transparent"),l.setAttribute("fill","transparent"),n.append(l),this.container.append(n),!s&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#s3}addHighlightArea(){this.container.classList.add("highlightArea")}}class ib extends im{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class iA extends e7{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class iv extends e7{#s5=null;#s6=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType="InkHighlight"===this.data.it?h.HIGHLIGHT:h.INK}#s4(t,e){switch(t){case 90:return{transform:`rotate(90) translate(${-e[0]},${e[1]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};case 180:return{transform:`rotate(180) translate(${-e[2]},${e[1]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]};case 270:return{transform:`rotate(270) translate(${-e[2]},${e[3]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};default:return{transform:`translate(${-e[0]},${e[3]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]}}}render(){this.container.classList.add(this.containerClassName);let{data:{rect:t,rotation:e,inkLists:i,borderStyle:s,popupRef:r}}=this,{transform:a,width:n,height:o}=this.#s4(e,t),l=this.svgFactory.create(n,o,!0),h=this.#s5=this.svgFactory.createElement("svg:g");l.append(h),h.setAttribute("stroke-width",s.width||1),h.setAttribute("stroke-linecap","round"),h.setAttribute("stroke-linejoin","round"),h.setAttribute("stroke-miterlimit",10),h.setAttribute("stroke","transparent"),h.setAttribute("fill","transparent"),h.setAttribute("transform",a);for(let t=0,e=i.length;t<e;t++){let e=this.svgFactory.createElement(this.svgElementName);this.#s6.push(e),e.setAttribute("points",i[t].join(",")),h.append(e)}return!r&&this.hasPopupData&&this._createPopup(),this.container.append(l),this._editOnDoubleClick(),this.container}updateEdited(t){super.updateEdited(t);let{thickness:e,points:i,rect:s}=t,r=this.#s5;if(e>=0&&r.setAttribute("stroke-width",e||1),i)for(let t=0,e=this.#s6.length;t<e;t++)this.#s6[t].setAttribute("points",i[t].join(","));if(s){let{transform:t,width:e,height:i}=this.#s4(this.data.rotation,s);r.parentElement.setAttribute("viewBox",`0 0 ${e} ${i}`),r.setAttribute("transform",t)}}getElementsToTriggerPopup(){return this.#s6}addHighlightArea(){this.container.classList.add("highlightArea")}}class iy extends e7{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}),this.annotationEditorType=h.HIGHLIGHT}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this._editOnDoubleClick(),this.container}}class iw extends e7{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class i_ extends e7{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class ix extends e7{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class iE extends e7{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.annotationEditorType=h.STAMP}render(){return this.container.classList.add("stampAnnotation"),this.container.setAttribute("role","img"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class iS extends e7{#s8=null;constructor(t){super(t,{isRenderable:!0});let{file:e}=this.data;this.filename=e.filename,this.content=e.content,this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){let t;this.container.classList.add("fileAttachmentAnnotation");let{container:e,data:i}=this;i.hasAppearance||0===i.fillAlpha?t=document.createElement("div"):((t=document.createElement("img")).src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(i.name)?"paperclip":"pushpin"}.svg`,i.fillAlpha&&i.fillAlpha<1&&(t.style=`filter: opacity(${Math.round(100*i.fillAlpha)}%);`)),t.addEventListener("dblclick",this.#s7.bind(this)),this.#s8=t;let{isMac:s}=F.platform;return e.addEventListener("keydown",t=>{"Enter"===t.key&&(s?t.metaKey:t.ctrlKey)&&this.#s7()}),!i.popupRef&&this.hasPopupData?this._createPopup():t.classList.add("popupTriggerArea"),e.append(t),e}getElementsToTriggerPopup(){return this.#s8}addHighlightArea(){this.container.classList.add("highlightArea")}#s7(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class iC{#s9=null;#rt=null;#re=new Map;#ri=null;constructor({div:t,accessibilityManager:e,annotationCanvasMap:i,annotationEditorUIManager:s,page:r,viewport:a,structTreeLayer:n}){this.div=t,this.#s9=e,this.#rt=i,this.#ri=n||null,this.page=r,this.viewport=a,this.zIndex=0,this._annotationEditorUIManager=s}hasEditableAnnotations(){return this.#re.size>0}async #rs(t,e){let i=t.firstChild||t,s=i.id=`${U}${e}`,r=await this.#ri?.getAriaAttributes(s);if(r)for(let[t,e]of r)i.setAttribute(t,e);this.div.append(t),this.#s9?.moveElementInDOM(this.div,t,i,!1)}async render(t){let{annotations:e}=t,i=this.div;ta(i,this.viewport);let s=new Map,r={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new e5,annotationStorage:t.annotationStorage||new tx,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(let t of e){if(t.noHTML)continue;let e=t.annotationType===p.POPUP;if(e){let e=s.get(t.id);if(!e)continue;r.elements=e}else if(t.rect[2]===t.rect[0]||t.rect[3]===t.rect[1])continue;r.data=t;let i=e8.create(r);if(!i.isRenderable)continue;if(!e&&t.popupRef){let e=s.get(t.popupRef);e?e.push(i):s.set(t.popupRef,[i])}let a=i.render();t.hidden&&(a.style.visibility="hidden"),await this.#rs(a,t.id),i._isEditable&&(this.#re.set(i.data.id,i),this._annotationEditorUIManager?.renderAnnotationElement(i))}this.#rr()}async addLinkAnnotations(t,e){let i={data:null,layer:this.div,linkService:e,svgFactory:new e5,parent:this};for(let e of t){e.borderStyle||=iC._defaultBorderStyle,i.data=e;let t=e8.create(i);if(!t.isRenderable)continue;let s=t.render();await this.#rs(s,e.id)}}update({viewport:t}){let e=this.div;this.viewport=t,ta(e,{rotation:t.rotation}),this.#rr(),e.hidden=!1}#rr(){if(!this.#rt)return;let t=this.div;for(let[e,i]of this.#rt){let s=t.querySelector(`[data-annotation-id="${e}"]`);if(!s)continue;i.className="annotationContent";let{firstChild:r}=s;r?"CANVAS"===r.nodeName?r.replaceWith(i):r.classList.contains("annotationContent")?r.after(i):r.before(i):s.append(i);let a=this.#re.get(e);a&&(a._hasNoCanvas?(this._annotationEditorUIManager?.setMissingCanvas(e,s.id,i),a._hasNoCanvas=!1):a.canvas=i)}this.#rt.clear()}getEditableAnnotations(){return Array.from(this.#re.values())}getEditableAnnotation(t){return this.#re.get(t)}static get _defaultBorderStyle(){return S(this,"_defaultBorderStyle",Object.freeze({width:1,rawWidth:1,style:g.SOLID,dashArray:[3],horizontalCornerRadius:0,verticalCornerRadius:0}))}}let iT=/\r\n?|\n/g;class iM extends tv{#sO;#ra="";#rn=`${this.id}-editor`;#ro=null;#sY;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){let t=iM.prototype,e=t=>t.isEmpty(),i=tm.TRANSLATE_SMALL,s=tm.TRANSLATE_BIG;return S(this,"_keyboardManager",new tg([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-s,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[s,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-s],checker:e}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,s],checker:e}]]))}static _type="freetext";static _editorType=h.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"}),this.#sO=t.color||iM._defaultColor||tv._defaultLineColor,this.#sY=t.fontSize||iM._defaultFontSize}static initialize(t,e){tv.initialize(t,e);let i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case d.FREETEXT_SIZE:iM._defaultFontSize=e;break;case d.FREETEXT_COLOR:iM._defaultColor=e}}updateParams(t,e){switch(t){case d.FREETEXT_SIZE:this.#rl(e);break;case d.FREETEXT_COLOR:this.#rh(e)}}static get defaultPropertiesToUpdate(){return[[d.FREETEXT_SIZE,iM._defaultFontSize],[d.FREETEXT_COLOR,iM._defaultColor||tv._defaultLineColor]]}get propertiesToUpdate(){return[[d.FREETEXT_SIZE,this.#sY],[d.FREETEXT_COLOR,this.#sO]]}#rl(t){let e=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--total-scale-factor))`,this.translate(0,-(t-this.#sY)*this.parentScale),this.#sY=t,this.#rd()},i=this.#sY;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:d.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#rh(t){let e=t=>{this.#sO=this.editorDiv.style.color=t},i=this.#sO;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:d.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){let t=this.parentScale;return[-iM._internalPadding*t,-(iM._internalPadding+this.#sY)*t]}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(!super.enableEditMode())return!1;this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.#ro=new AbortController;let t=this._uiManager.combinedSignal(this.#ro);return this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:t}),this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:t}),this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:t}),this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:t}),this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:t}),!0}disableEditMode(){return!!super.disableEditMode()&&(this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",this.#rn),this._isDraggable=!0,this.#ro?.abort(),this.#ro=null,this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"),!0)}focusin(t){this._focusEventsAllowed&&(super.focusin(t),t.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(t){this.width||(this.enableEditMode(),t&&this.editorDiv.focus(),this._initialOptions?.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}#rc(){let t=[];this.editorDiv.normalize();let e=null;for(let i of this.editorDiv.childNodes)(e?.nodeType!==Node.TEXT_NODE||"BR"!==i.nodeName)&&(t.push(iM.#ru(i)),e=i);return t.join("\n")}#rd(){let t,[e,i]=this.parentDimensions;if(this.isAttachedToDOM)t=this.div.getBoundingClientRect();else{let{currentLayer:e,div:i}=this,s=i.style.display,r=i.classList.contains("hidden");i.classList.remove("hidden"),i.style.display="hidden",e.div.append(this.div),t=i.getBoundingClientRect(),i.remove(),i.style.display=s,i.classList.toggle("hidden",r)}this.rotation%180==this.parentRotation%180?(this.width=t.width/e,this.height=t.height/i):(this.width=t.height/e,this.height=t.width/i),this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();let t=this.#ra,e=this.#ra=this.#rc().trimEnd();if(t===e)return;let i=t=>{if(this.#ra=t,!t)return void this.remove();this.#rp(),this._uiManager.rebuild(this),this.#rd()};this.addCommands({cmd:()=>{i(e)},undo:()=>{i(t)},mustExec:!1}),this.#rd()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}keydown(t){t.target===this.div&&"Enter"===t.key&&(this.enterInEditMode(),t.preventDefault())}editorDivKeydown(t){iM._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}get canChangeContent(){return!0}render(){let t,e;if(this.div)return this.div;(this._isCopy||this.annotationElementId)&&(t=this.x,e=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",this.#rn),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2"),this.editorDiv.setAttribute("data-l10n-attrs","default-content"),this.enableEditing(),this.editorDiv.contentEditable=!0;let{style:i}=this.editorDiv;if(i.fontSize=`calc(${this.#sY}px * var(--total-scale-factor))`,i.color=this.#sO,this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),this._isCopy||this.annotationElementId){let[i,s]=this.parentDimensions;if(this.annotationElementId){let r,a,{position:n}=this._initialData,[o,l]=this.getInitialTranslation();[o,l]=this.pageTranslationToScreen(o,l);let[h,d]=this.pageDimensions,[c,u]=this.pageTranslation;switch(this.rotation){case 0:r=t+(n[0]-c)/h,a=e+this.height-(n[1]-u)/d;break;case 90:r=t+(n[0]-c)/h,a=e-(n[1]-u)/d,[o,l]=[l,-o];break;case 180:r=t-this.width+(n[0]-c)/h,a=e-(n[1]-u)/d,[o,l]=[-o,-l];break;case 270:r=t+(n[0]-c-this.height*d)/h,a=e+(n[1]-u-this.width*h)/d,[o,l]=[-l,o]}this.setAt(r*i,a*s,o,l)}else this._moveAfterPaste(t,e);this.#rp(),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}static #ru(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(iT,"")}editorDivPaste(t){let e=t.clipboardData||window.clipboardData,{types:i}=e;if(1===i.length&&"text/plain"===i[0])return;t.preventDefault();let s=iM.#rg(e.getData("text")||"").replaceAll(iT,"\n");if(!s)return;let r=window.getSelection();if(!r.rangeCount)return;this.editorDiv.normalize(),r.deleteFromDocument();let a=r.getRangeAt(0);if(!s.includes("\n")){a.insertNode(document.createTextNode(s)),this.editorDiv.normalize(),r.collapseToStart();return}let{startContainer:n,startOffset:o}=a,l=[],h=[];if(n.nodeType===Node.TEXT_NODE){let t=n.parentElement;if(h.push(n.nodeValue.slice(o).replaceAll(iT,"")),t!==this.editorDiv){let e=l;for(let i of this.editorDiv.childNodes){if(i===t){e=h;continue}e.push(iM.#ru(i))}}l.push(n.nodeValue.slice(0,o).replaceAll(iT,""))}else if(n===this.editorDiv){let t=l,e=0;for(let i of this.editorDiv.childNodes)e++===o&&(t=h),t.push(iM.#ru(i))}this.#ra=`${l.join("\n")}${s}${h.join("\n")}`,this.#rp();let d=new Range,c=Math.sumPrecise(l.map(t=>t.length));for(let{firstChild:t}of this.editorDiv.childNodes)if(t.nodeType===Node.TEXT_NODE){let e=t.nodeValue.length;if(c<=e){d.setStart(t,c),d.setEnd(t,c);break}c-=e}r.removeAllRanges(),r.addRange(d)}#rp(){if(this.editorDiv.replaceChildren(),this.#ra)for(let t of this.#ra.split("\n")){let e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br")),this.editorDiv.append(e)}}#rf(){return this.#ra.replaceAll("\xa0"," ")}static #rg(t){return t.replaceAll(" ","\xa0")}get contentDiv(){return this.editorDiv}static async deserialize(t,e,i){let s=null;if(t instanceof ic){let{data:{defaultAppearanceData:{fontSize:e,fontColor:i},rect:r,rotation:a,id:n,popupRef:o},textContent:l,textPosition:d,parent:{page:{pageNumber:c}}}=t;if(!l||0===l.length)return null;s=t={annotationType:h.FREETEXT,color:Array.from(i),fontSize:e,value:l.join("\n"),position:d,pageIndex:c-1,rect:r.slice(0),rotation:a,id:n,deleted:!1,popupRef:o}}let r=await super.deserialize(t,e,i);return r.#sY=t.fontSize,r.#sO=O.makeHexColor(...t.color),r.#ra=iM.#rg(t.value),r.annotationElementId=t.id||null,r._initialData=s,r}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();let e=iM._internalPadding*this.parentScale,i=this.getRect(e,e),s=tv._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#sO),r={annotationType:h.FREETEXT,color:s,fontSize:this.#sY,value:this.#rf(),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(r.isCopy=!0,r):this.annotationElementId&&!this.#rm(r)?null:(r.id=this.annotationElementId,r)}#rm(t){let{value:e,fontSize:i,color:s,pageIndex:r}=this._initialData;return this._hasBeenMoved||t.value!==e||t.fontSize!==i||t.color.some((t,e)=>t!==s[e])||t.pageIndex!==r}renderAnnotationElement(t){let e=super.renderAnnotationElement(t);if(this.deleted)return e;let{style:i}=e;for(let t of(i.fontSize=`calc(${this.#sY}px * var(--total-scale-factor))`,i.color=this.#sO,e.replaceChildren(),this.#ra.split("\n"))){let i=document.createElement("div");i.append(t?document.createTextNode(t):document.createElement("br")),e.append(i)}let s=iM._internalPadding*this.parentScale;return t.updateEdited({rect:this.getRect(s,s),popupContent:this.#ra}),e}resetAnnotationElement(t){super.resetAnnotationElement(t),t.resetEdited()}}class iP{static PRECISION=1e-4;toSVGPath(){w("Abstract method `toSVGPath` must be implemented.")}get box(){w("Abstract getter `box` must be implemented.")}serialize(t,e){w("Abstract method `serialize` must be implemented.")}static _rescale(t,e,i,s,r,a){a||=new Float32Array(t.length);for(let n=0,o=t.length;n<o;n+=2)a[n]=e+t[n]*s,a[n+1]=i+t[n+1]*r;return a}static _rescaleAndSwap(t,e,i,s,r,a){a||=new Float32Array(t.length);for(let n=0,o=t.length;n<o;n+=2)a[n]=e+t[n+1]*s,a[n+1]=i+t[n]*r;return a}static _translate(t,e,i,s){s||=new Float32Array(t.length);for(let r=0,a=t.length;r<a;r+=2)s[r]=e+t[r],s[r+1]=i+t[r+1];return s}static svgRound(t){return Math.round(1e4*t)}static _normalizePoint(t,e,i,s,r){switch(r){case 90:return[1-e/i,t/s];case 180:return[1-t/i,1-e/s];case 270:return[e/i,1-t/s];default:return[t/i,e/s]}}static _normalizePagePoint(t,e,i){switch(i){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,i,s,r,a){return[(t+5*i)/6,(e+5*s)/6,(5*i+r)/6,(5*s+a)/6,(i+r)/2,(s+a)/2]}}class iI{#rb;#rA=[];#rv;#ry;#rw=[];#r_=new Float32Array(18);#rx;#rE;#rS;#rC;#rT;#rM;#rP=[];static #rI=8;static #rD=2;static #rR=iI.#rI+iI.#rD;constructor({x:t,y:e},i,s,r,a,n=0){this.#rb=i,this.#rM=r*s,this.#ry=a,this.#r_.set([NaN,NaN,NaN,NaN,t,e],6),this.#rv=n,this.#rC=iI.#rI*s,this.#rS=iI.#rR*s,this.#rT=s,this.#rP.push(t,e)}isEmpty(){return isNaN(this.#r_[8])}#rk(){let t=this.#r_.subarray(4,6),e=this.#r_.subarray(16,18),[i,s,r,a]=this.#rb;return[(this.#rx+(t[0]-e[0])/2-i)/r,(this.#rE+(t[1]-e[1])/2-s)/a,(this.#rx+(e[0]-t[0])/2-i)/r,(this.#rE+(e[1]-t[1])/2-s)/a]}add({x:t,y:e}){this.#rx=t,this.#rE=e;let[i,s,r,a]=this.#rb,[n,o,l,h]=this.#r_.subarray(8,12),d=t-l,c=e-h,u=Math.hypot(d,c);if(u<this.#rS)return!1;let p=u-this.#rC,g=p/u,f=g*d,m=g*c,b=n,A=o;n=l,o=h,l+=f,h+=m,this.#rP?.push(t,e);let v=-m/p*this.#rM,y=f/p*this.#rM;return(this.#r_.set(this.#r_.subarray(2,8),0),this.#r_.set([l+v,h+y],4),this.#r_.set(this.#r_.subarray(14,18),12),this.#r_.set([l-v,h-y],16),isNaN(this.#r_[6]))?(0===this.#rw.length&&(this.#r_.set([n+v,o+y],2),this.#rw.push(NaN,NaN,NaN,NaN,(n+v-i)/r,(o+y-s)/a),this.#r_.set([n-v,o-y],14),this.#rA.push(NaN,NaN,NaN,NaN,(n-v-i)/r,(o-y-s)/a)),this.#r_.set([b,A,n,o,l,h],6),!this.isEmpty()):((this.#r_.set([b,A,n,o,l,h],6),Math.abs(Math.atan2(A-o,b-n)-Math.atan2(m,f))<Math.PI/2)?([n,o,l,h]=this.#r_.subarray(2,6),this.#rw.push(NaN,NaN,NaN,NaN,((n+l)/2-i)/r,((o+h)/2-s)/a),[n,o,b,A]=this.#r_.subarray(14,18),this.#rA.push(NaN,NaN,NaN,NaN,((b+n)/2-i)/r,((A+o)/2-s)/a)):([b,A,n,o,l,h]=this.#r_.subarray(0,6),this.#rw.push(((b+5*n)/6-i)/r,((A+5*o)/6-s)/a,((5*n+l)/6-i)/r,((5*o+h)/6-s)/a,((n+l)/2-i)/r,((o+h)/2-s)/a),[l,h,n,o,b,A]=this.#r_.subarray(12,18),this.#rA.push(((b+5*n)/6-i)/r,((A+5*o)/6-s)/a,((5*n+l)/6-i)/r,((5*o+h)/6-s)/a,((n+l)/2-i)/r,((o+h)/2-s)/a)),!0)}toSVGPath(){if(this.isEmpty())return"";let t=this.#rw,e=this.#rA;if(isNaN(this.#r_[6])&&!this.isEmpty())return this.#rL();let i=[];i.push(`M${t[4]} ${t[5]}`);for(let e=6;e<t.length;e+=6)isNaN(t[e])?i.push(`L${t[e+4]} ${t[e+5]}`):i.push(`C${t[e]} ${t[e+1]} ${t[e+2]} ${t[e+3]} ${t[e+4]} ${t[e+5]}`);this.#rF(i);for(let t=e.length-6;t>=6;t-=6)isNaN(e[t])?i.push(`L${e[t+4]} ${e[t+5]}`):i.push(`C${e[t]} ${e[t+1]} ${e[t+2]} ${e[t+3]} ${e[t+4]} ${e[t+5]}`);return this.#rN(i),i.join(" ")}#rL(){let[t,e,i,s]=this.#rb,[r,a,n,o]=this.#rk();return`M${(this.#r_[2]-t)/i} ${(this.#r_[3]-e)/s} L${(this.#r_[4]-t)/i} ${(this.#r_[5]-e)/s} L${r} ${a} L${n} ${o} L${(this.#r_[16]-t)/i} ${(this.#r_[17]-e)/s} L${(this.#r_[14]-t)/i} ${(this.#r_[15]-e)/s} Z`}#rN(t){let e=this.#rA;t.push(`L${e[4]} ${e[5]} Z`)}#rF(t){let[e,i,s,r]=this.#rb,a=this.#r_.subarray(4,6),n=this.#r_.subarray(16,18),[o,l,h,d]=this.#rk();t.push(`L${(a[0]-e)/s} ${(a[1]-i)/r} L${o} ${l} L${h} ${d} L${(n[0]-e)/s} ${(n[1]-i)/r}`)}newFreeDrawOutline(t,e,i,s,r,a){return new iD(t,e,i,s,r,a)}getOutlines(){let t=this.#rw,e=this.#rA,i=this.#r_,[s,r,a,n]=this.#rb,o=new Float32Array((this.#rP?.length??0)+2);for(let t=0,e=o.length-2;t<e;t+=2)o[t]=(this.#rP[t]-s)/a,o[t+1]=(this.#rP[t+1]-r)/n;if(o[o.length-2]=(this.#rx-s)/a,o[o.length-1]=(this.#rE-r)/n,isNaN(i[6])&&!this.isEmpty())return this.#rO(o);let l=new Float32Array(this.#rw.length+24+this.#rA.length),h=t.length;for(let e=0;e<h;e+=2){if(isNaN(t[e])){l[e]=l[e+1]=NaN;continue}l[e]=t[e],l[e+1]=t[e+1]}h=this.#rB(l,h);for(let t=e.length-6;t>=6;t-=6)for(let i=0;i<6;i+=2){if(isNaN(e[t+i])){l[h]=l[h+1]=NaN,h+=2;continue}l[h]=e[t+i],l[h+1]=e[t+i+1],h+=2}return this.#rz(l,h),this.newFreeDrawOutline(l,o,this.#rb,this.#rT,this.#rv,this.#ry)}#rO(t){let e=this.#r_,[i,s,r,a]=this.#rb,[n,o,l,h]=this.#rk(),d=new Float32Array(36);return d.set([NaN,NaN,NaN,NaN,(e[2]-i)/r,(e[3]-s)/a,NaN,NaN,NaN,NaN,(e[4]-i)/r,(e[5]-s)/a,NaN,NaN,NaN,NaN,n,o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,(e[16]-i)/r,(e[17]-s)/a,NaN,NaN,NaN,NaN,(e[14]-i)/r,(e[15]-s)/a],0),this.newFreeDrawOutline(d,t,this.#rb,this.#rT,this.#rv,this.#ry)}#rz(t,e){let i=this.#rA;return t.set([NaN,NaN,NaN,NaN,i[4],i[5]],e),e+=6}#rB(t,e){let i=this.#r_.subarray(4,6),s=this.#r_.subarray(16,18),[r,a,n,o]=this.#rb,[l,h,d,c]=this.#rk();return t.set([NaN,NaN,NaN,NaN,(i[0]-r)/n,(i[1]-a)/o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,(s[0]-r)/n,(s[1]-a)/o],e),e+=24}}class iD extends iP{#rb;#rH=new Float32Array(4);#rv;#ry;#rP;#rT;#rU;constructor(t,e,i,s,r,a){super(),this.#rU=t,this.#rP=e,this.#rb=i,this.#rT=s,this.#rv=r,this.#ry=a,this.lastPoint=[NaN,NaN],this.#r$(a);let[n,o,l,h]=this.#rH;for(let e=0,i=t.length;e<i;e+=2)t[e]=(t[e]-n)/l,t[e+1]=(t[e+1]-o)/h;for(let t=0,i=e.length;t<i;t+=2)e[t]=(e[t]-n)/l,e[t+1]=(e[t+1]-o)/h}toSVGPath(){let t=[`M${this.#rU[4]} ${this.#rU[5]}`];for(let e=6,i=this.#rU.length;e<i;e+=6){if(isNaN(this.#rU[e])){t.push(`L${this.#rU[e+4]} ${this.#rU[e+5]}`);continue}t.push(`C${this.#rU[e]} ${this.#rU[e+1]} ${this.#rU[e+2]} ${this.#rU[e+3]} ${this.#rU[e+4]} ${this.#rU[e+5]}`)}return t.push("Z"),t.join(" ")}serialize([t,e,i,s],r){let a,n,o=i-t,l=s-e;switch(r){case 0:a=iP._rescale(this.#rU,t,s,o,-l),n=iP._rescale(this.#rP,t,s,o,-l);break;case 90:a=iP._rescaleAndSwap(this.#rU,t,e,o,l),n=iP._rescaleAndSwap(this.#rP,t,e,o,l);break;case 180:a=iP._rescale(this.#rU,i,e,-o,l),n=iP._rescale(this.#rP,i,e,-o,l);break;case 270:a=iP._rescaleAndSwap(this.#rU,i,s,-o,-l),n=iP._rescaleAndSwap(this.#rP,i,s,-o,-l)}return{outline:Array.from(a),points:[Array.from(n)]}}#r$(t){let e=this.#rU,i=e[4],s=e[5],r=[i,s,i,s],a=i,n=s,o=t?Math.max:Math.min;for(let t=6,l=e.length;t<l;t+=6){let l=e[t+4],h=e[t+5];if(isNaN(e[t]))O.pointBoundingBox(l,h,r),n<h?(a=l,n=h):n===h&&(a=o(a,l));else{let l=[1/0,1/0,-1/0,-1/0];O.bezierBoundingBox(i,s,...e.slice(t,t+6),l),O.rectBoundingBox(...l,r),n<l[3]?(a=l[2],n=l[3]):n===l[3]&&(a=o(a,l[2]))}i=l,s=h}let l=this.#rH;l[0]=r[0]-this.#rv,l[1]=r[1]-this.#rv,l[2]=r[2]-r[0]+2*this.#rv,l[3]=r[3]-r[1]+2*this.#rv,this.lastPoint=[a,n]}get box(){return this.#rH}newOutliner(t,e,i,s,r,a=0){return new iI(t,e,i,s,r,a)}getNewOutline(t,e){let[i,s,r,a]=this.#rH,[n,o,l,h]=this.#rb,d=r*l,c=a*h,u=i*l+n,p=s*h+o,g=this.newOutliner({x:this.#rP[0]*d+u,y:this.#rP[1]*c+p},this.#rb,this.#rT,t,this.#ry,e??this.#rv);for(let t=2;t<this.#rP.length;t+=2)g.add({x:this.#rP[t]*d+u,y:this.#rP[t+1]*c+p});return g.getOutlines()}}class iR{#rb;#rG;#rj=[];#rV=[];constructor(t,e=0,i=0,s=!0){let r=[1/0,1/0,-1/0,-1/0];for(let{x:i,y:s,width:a,height:n}of t){let t=1e-4*Math.floor((i-e)/1e-4),o=1e-4*Math.ceil((i+a+e)/1e-4),l=1e-4*Math.floor((s-e)/1e-4),h=1e-4*Math.ceil((s+n+e)/1e-4),d=[t,l,h,!0],c=[o,l,h,!1];this.#rj.push(d,c),O.rectBoundingBox(t,l,o,h,r)}let a=r[2]-r[0]+2*i,n=r[3]-r[1]+2*i,o=r[0]-i,l=r[1]-i,h=this.#rj.at(s?-1:-2),d=[h[0],h[2]];for(let t of this.#rj){let[e,i,s]=t;t[0]=(e-o)/a,t[1]=(i-l)/n,t[2]=(s-l)/n}this.#rb=new Float32Array([o,l,a,n]),this.#rG=d}getOutlines(){this.#rj.sort((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]);let t=[];for(let e of this.#rj)e[3]?(t.push(...this.#rW(e)),this.#rq(e)):(this.#rK(e),t.push(...this.#rW(e)));return this.#rX(t)}#rX(t){let e,i=[],s=new Set;for(let e of t){let[t,s,r]=e;i.push([t,s,e],[t,r,e])}i.sort((t,e)=>t[1]-e[1]||t[0]-e[0]);for(let t=0,e=i.length;t<e;t+=2){let e=i[t][2],r=i[t+1][2];e.push(r),r.push(e),s.add(e),s.add(r)}let r=[];for(;s.size>0;){let t=s.values().next().value,[i,a,n,o,l]=t;s.delete(t);let h=i,d=a;for(e=[i,n],r.push(e);;){let t;if(s.has(o))t=o;else if(s.has(l))t=l;else break;s.delete(t),[i,a,n,o,l]=t,h!==i&&(e.push(h,d,i,d===a?a:n),h=i),d=d===a?n:a}e.push(h,d)}return new ik(r,this.#rb,this.#rG)}#rY(t){let e=this.#rV,i=0,s=e.length-1;for(;i<=s;){let r=i+s>>1,a=e[r][0];if(a===t)return r;a<t?i=r+1:s=r-1}return s+1}#rq([,t,e]){let i=this.#rY(t);this.#rV.splice(i,0,[t,e])}#rK([,t,e]){let i=this.#rY(t);for(let s=i;s<this.#rV.length;s++){let[i,r]=this.#rV[s];if(i!==t)break;if(i===t&&r===e)return void this.#rV.splice(s,1)}for(let s=i-1;s>=0;s--){let[i,r]=this.#rV[s];if(i!==t)break;if(i===t&&r===e)return void this.#rV.splice(s,1)}}#rW(t){let[e,i,s]=t,r=[[e,i,s]],a=this.#rY(s);for(let t=0;t<a;t++){let[i,s]=this.#rV[t];for(let t=0,a=r.length;t<a;t++){let[,n,o]=r[t];if(!(s<=n)&&!(o<=i)){if(n>=i){if(o>s)r[t][1]=s;else{if(1===a)return[];r.splice(t,1),t--,a--}continue}r[t][2]=i,o>s&&r.push([e,s,o])}}}return r}}class ik extends iP{#rb;#rQ;constructor(t,e,i){super(),this.#rQ=t,this.#rb=e,this.lastPoint=i}toSVGPath(){let t=[];for(let e of this.#rQ){let[i,s]=e;t.push(`M${i} ${s}`);for(let r=2;r<e.length;r+=2){let a=e[r],n=e[r+1];a===i?(t.push(`V${n}`),s=n):n===s&&(t.push(`H${a}`),i=a)}t.push("Z")}return t.join(" ")}serialize([t,e,i,s],r){let a=[],n=i-t,o=s-e;for(let e of this.#rQ){let i=Array(e.length);for(let r=0;r<e.length;r+=2)i[r]=t+e[r]*n,i[r+1]=s-e[r+1]*o;a.push(i)}return a}get box(){return this.#rb}get classNamesForOutlining(){return["highlightOutline"]}}class iL extends iI{newFreeDrawOutline(t,e,i,s,r,a){return new iF(t,e,i,s,r,a)}}class iF extends iD{newOutliner(t,e,i,s,r,a=0){return new iL(t,e,i,s,r,a)}}class iN{#rJ=null;#rZ=null;#r0;#r1=null;#r2=!1;#r3=!1;#a=null;#r5;#r6=null;#m=null;#r4;static #r8=null;static get _keyboardManager(){return S(this,"_keyboardManager",new tg([[["Escape","mac+Escape"],iN.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],iN.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],iN.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],iN.prototype._moveToPrevious],[["Home","mac+Home"],iN.prototype._moveToBeginning],[["End","mac+End"],iN.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){t?(this.#r3=!1,this.#r4=d.HIGHLIGHT_COLOR,this.#a=t):(this.#r3=!0,this.#r4=d.HIGHLIGHT_DEFAULT_COLOR),this.#m=t?._uiManager||e,this.#r5=this.#m._eventBus,this.#r0=t?.color||this.#m?.highlightColors.values().next().value||"#FFFF98",iN.#r8||=Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"})}renderButton(){let t=this.#rJ=document.createElement("button");t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.setAttribute("aria-haspopup",!0);let e=this.#m._signal;t.addEventListener("click",this.#r7.bind(this),{signal:e}),t.addEventListener("keydown",this.#sI.bind(this),{signal:e});let i=this.#rZ=document.createElement("span");return i.className="swatch",i.setAttribute("aria-hidden",!0),i.style.backgroundColor=this.#r0,t.append(i),t}renderMainDropdown(){let t=this.#r1=this.#r9();return t.setAttribute("aria-orientation","horizontal"),t.setAttribute("aria-labelledby","highlightColorPickerLabel"),t}#r9(){let t=document.createElement("div"),e=this.#m._signal;for(let[i,s]of(t.addEventListener("contextmenu",Z,{signal:e}),t.className="dropdown",t.role="listbox",t.setAttribute("aria-multiselectable",!1),t.setAttribute("aria-orientation","vertical"),t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown"),this.#m.highlightColors)){let r=document.createElement("button");r.tabIndex="0",r.role="option",r.setAttribute("data-color",s),r.title=i,r.setAttribute("data-l10n-id",iN.#r8[i]);let a=document.createElement("span");r.append(a),a.className="swatch",a.style.backgroundColor=s,r.setAttribute("aria-selected",s===this.#r0),r.addEventListener("click",this.#at.bind(this,s),{signal:e}),t.append(r)}return t.addEventListener("keydown",this.#sI.bind(this),{signal:e}),t}#at(t,e){e.stopPropagation(),this.#r5.dispatch("switchannotationeditorparams",{source:this,type:this.#r4,value:t})}_colorSelectFromKeyboard(t){if(t.target===this.#rJ)return void this.#r7(t);let e=t.target.getAttribute("data-color");e&&this.#at(e,t)}_moveToNext(t){return this.#ae?t.target===this.#rJ?void this.#r1.firstChild?.focus():void t.target.nextSibling?.focus():void this.#r7(t)}_moveToPrevious(t){if(t.target===this.#r1?.firstChild||t.target===this.#rJ){this.#ae&&this._hideDropdownFromKeyboard();return}this.#ae||this.#r7(t),t.target.previousSibling?.focus()}_moveToBeginning(t){if(!this.#ae)return void this.#r7(t);this.#r1.firstChild?.focus()}_moveToEnd(t){if(!this.#ae)return void this.#r7(t);this.#r1.lastChild?.focus()}#sI(t){iN._keyboardManager.exec(this,t)}#r7(t){if(this.#ae)return void this.hideDropdown();if(this.#r2=0===t.detail,this.#r6||(this.#r6=new AbortController,window.addEventListener("pointerdown",this.#d.bind(this),{signal:this.#m.combinedSignal(this.#r6)})),this.#r1)return void this.#r1.classList.remove("hidden");let e=this.#r1=this.#r9();this.#rJ.append(e)}#d(t){this.#r1?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#r1?.classList.add("hidden"),this.#r6?.abort(),this.#r6=null}get #ae(){return this.#r1&&!this.#r1.classList.contains("hidden")}_hideDropdownFromKeyboard(){if(!this.#r3){if(!this.#ae)return void this.#a?.unselect();this.hideDropdown(),this.#rJ.focus({preventScroll:!0,focusVisible:this.#r2})}}updateColor(t){if(this.#rZ&&(this.#rZ.style.backgroundColor=t),!this.#r1)return;let e=this.#m.highlightColors.values();for(let i of this.#r1.children)i.setAttribute("aria-selected",e.next().value===t)}destroy(){this.#rJ?.remove(),this.#rJ=null,this.#rZ=null,this.#r1?.remove(),this.#r1=null}}class iO extends tv{#ai=null;#as=0;#ar;#aa=null;#r=null;#an=null;#ao=null;#al=0;#ah=null;#ad=null;#y=null;#ac=!1;#rG=null;#au;#ap=null;#ag="";#rM;#af="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _type="highlight";static _editorType=h.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){let t=iO.prototype;return S(this,"_keyboardManager",new tg([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"}),this.color=t.color||iO._defaultColor,this.#rM=t.thickness||iO._defaultThickness,this.#au=t.opacity||iO._defaultOpacity,this.#ar=t.boxes||null,this.#af=t.methodOfCreation||"",this.#ag=t.text||"",this._isDraggable=!1,this.defaultL10nId="pdfjs-editor-highlight-editor",t.highlightId>-1?(this.#ac=!0,this.#am(t),this.#ab()):this.#ar&&(this.#ai=t.anchorNode,this.#as=t.anchorOffset,this.#ao=t.focusNode,this.#al=t.focusOffset,this.#aA(),this.#ab(),this.rotate(this.rotation))}get telemetryInitialData(){return{action:"added",type:this.#ac?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:this.#rM,methodOfCreation:this.#af}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#aA(){let t=new iR(this.#ar,.001);this.#ad=t.getOutlines(),[this.x,this.y,this.width,this.height]=this.#ad.box;let e=new iR(this.#ar,.0025,.001,"ltr"===this._uiManager.direction);this.#an=e.getOutlines();let{lastPoint:i}=this.#an;this.#rG=[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height]}#am({highlightOutlines:t,highlightId:e,clipPathId:i}){if(this.#ad=t,this.#an=t.getNewOutline(this.#rM/2****,.0025),e>=0)this.#y=e,this.#aa=i,this.parent.drawLayer.finalizeDraw(e,{bbox:t.box,path:{d:t.toSVGPath()}}),this.#ap=this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:this.#an.box,path:{d:this.#an.toSVGPath()}},!0);else if(this.parent){let e=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(this.#y,{bbox:iO.#av(this.#ad.box,(e-this.rotation+360)%360),path:{d:t.toSVGPath()}}),this.parent.drawLayer.updateProperties(this.#ap,{bbox:iO.#av(this.#an.box,e),path:{d:this.#an.toSVGPath()}})}let[s,r,a,n]=t.box;switch(this.rotation){case 0:this.x=s,this.y=r,this.width=a,this.height=n;break;case 90:{let[t,e]=this.parentDimensions;this.x=r,this.y=1-s,this.width=a*e/t,this.height=n*t/e;break}case 180:this.x=1-s,this.y=1-r,this.width=a,this.height=n;break;case 270:{let[t,e]=this.parentDimensions;this.x=1-r,this.y=s,this.width=a*e/t,this.height=n*t/e}}let{lastPoint:o}=this.#an;this.#rG=[(o[0]-s)/a,(o[1]-r)/n]}static initialize(t,e){tv.initialize(t,e),iO._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case d.HIGHLIGHT_DEFAULT_COLOR:iO._defaultColor=e;break;case d.HIGHLIGHT_THICKNESS:iO._defaultThickness=e}}translateInPage(t,e){}get toolbarPosition(){return this.#rG}updateParams(t,e){switch(t){case d.HIGHLIGHT_COLOR:this.#rh(e);break;case d.HIGHLIGHT_THICKNESS:this.#ay(e)}}static get defaultPropertiesToUpdate(){return[[d.HIGHLIGHT_DEFAULT_COLOR,iO._defaultColor],[d.HIGHLIGHT_THICKNESS,iO._defaultThickness]]}get propertiesToUpdate(){return[[d.HIGHLIGHT_COLOR,this.color||iO._defaultColor],[d.HIGHLIGHT_THICKNESS,this.#rM||iO._defaultThickness],[d.HIGHLIGHT_FREE,this.#ac]]}#rh(t){let e=(t,e)=>{this.color=t,this.#au=e,this.parent?.drawLayer.updateProperties(this.#y,{root:{fill:t,"fill-opacity":e}}),this.#r?.updateColor(t)},i=this.color,s=this.#au;this.addCommands({cmd:e.bind(this,t,iO._defaultOpacity),undo:e.bind(this,i,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:d.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)}#ay(t){let e=this.#rM,i=t=>{this.#rM=t,this.#aw(t)};this.addCommands({cmd:i.bind(this,t),undo:i.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:d.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}async addEditToolbar(){let t=await super.addEditToolbar();return t?(this._uiManager.highlightColors&&(this.#r=new iN({editor:this}),t.addColorPicker(this.#r)),t):null}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#a_())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#a_())}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),t&&this.div.focus()}remove(){this.#ax(),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.#ab(),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?this.#ax():t&&(this.#ab(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),this.show(this._isVisible),e&&this.select()}#aw(t){if(!this.#ac)return;this.#am({highlightOutlines:this.#ad.getNewOutline(t/2)}),this.fixAndSetPosition();let[e,i]=this.parentDimensions;this.setDims(this.width*e,this.height*i)}#ax(){null!==this.#y&&this.parent&&(this.parent.drawLayer.remove(this.#y),this.#y=null,this.parent.drawLayer.remove(this.#ap),this.#ap=null)}#ab(t=this.parent){null===this.#y&&({id:this.#y,clipPathId:this.#aa}=t.drawLayer.draw({bbox:this.#ad.box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":this.#au},rootClass:{highlight:!0,free:this.#ac},path:{d:this.#ad.toSVGPath()}},!1,!0),this.#ap=t.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:this.#ac},bbox:this.#an.box,path:{d:this.#an.toSVGPath()}},this.#ac),this.#ah&&(this.#ah.style.clipPath=this.#aa))}static #av([t,e,i,s],r){switch(r){case 90:return[1-e-s,t,s,i];case 180:return[1-t-i,1-e-s,i,s];case 270:return[e,1-t-i,s,i]}return[t,e,i,s]}rotate(t){let e,{drawLayer:i}=this.parent;this.#ac?(t=(t-this.rotation+360)%360,e=iO.#av(this.#ad.box,t)):e=iO.#av([this.x,this.y,this.width,this.height],t),i.updateProperties(this.#y,{bbox:e,root:{"data-main-rotation":t}}),i.updateProperties(this.#ap,{bbox:iO.#av(this.#an.box,t),root:{"data-main-rotation":t}})}render(){if(this.div)return this.div;let t=super.render();this.#ag&&(t.setAttribute("aria-label",this.#ag),t.setAttribute("role","mark")),this.#ac?t.classList.add("free"):this.div.addEventListener("keydown",this.#aE.bind(this),{signal:this._uiManager._signal});let e=this.#ah=document.createElement("div");t.append(e),e.setAttribute("aria-hidden","true"),e.className="internal",e.style.clipPath=this.#aa;let[i,s]=this.parentDimensions;return this.setDims(this.width*i,this.height*s),td(this,this.#ah,["pointerover","pointerleave"]),this.enableEditing(),t}pointerover(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#ap,{rootClass:{hovered:!0}})}pointerleave(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#ap,{rootClass:{hovered:!1}})}#aE(t){iO._keyboardManager.exec(this,t)}_moveCaret(t){switch(this.parent.unselect(this),t){case 0:case 2:this.#aS(!0);break;case 1:case 3:this.#aS(!1)}}#aS(t){if(!this.#ai)return;let e=window.getSelection();t?e.setPosition(this.#ai,this.#as):e.setPosition(this.#ao,this.#al)}select(){super.select(),this.#ap&&this.parent?.drawLayer.updateProperties(this.#ap,{rootClass:{hovered:!1,selected:!0}})}unselect(){super.unselect(),this.#ap&&(this.parent?.drawLayer.updateProperties(this.#ap,{rootClass:{selected:!1}}),this.#ac||this.#aS(!1))}get _mustFixPosition(){return!this.#ac}show(t=this._isVisible){super.show(t),this.parent&&(this.parent.drawLayer.updateProperties(this.#y,{rootClass:{hidden:!t}}),this.parent.drawLayer.updateProperties(this.#ap,{rootClass:{hidden:!t}}))}#a_(){return this.#ac?this.rotation:0}#aC(){if(this.#ac)return null;let[t,e]=this.pageDimensions,[i,s]=this.pageTranslation,r=this.#ar,a=new Float32Array(8*r.length),n=0;for(let{x:o,y:l,width:h,height:d}of r){let r=o*t+i,c=(1-l)*e+s;a[n]=a[n+4]=r,a[n+1]=a[n+3]=c,a[n+2]=a[n+6]=r+h*t,a[n+5]=a[n+7]=c-d*e,n+=8}return a}#aT(t){return this.#ad.serialize(t,this.#a_())}static startHighlighting(t,e,{target:i,x:s,y:r}){let{x:a,y:n,width:o,height:l}=i.getBoundingClientRect(),h=new AbortController,d=t.combinedSignal(h),c=e=>{h.abort(),this.#aM(t,e)};window.addEventListener("blur",c,{signal:d}),window.addEventListener("pointerup",c,{signal:d}),window.addEventListener("pointerdown",tt,{capture:!0,passive:!1,signal:d}),window.addEventListener("contextmenu",Z,{signal:d}),i.addEventListener("pointermove",this.#aP.bind(this,t),{signal:d}),this._freeHighlight=new iL({x:s,y:r},[a,n,o,l],t.scale,this._defaultThickness/2,e,.001),{id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0)}static #aP(t,e){this._freeHighlight.add(e)&&t.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})}static #aM(t,e){this._freeHighlight.isEmpty()?t.drawLayer.remove(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""}static async deserialize(t,e,i){let s=null;if(t instanceof iy){let{data:{quadPoints:e,rect:i,rotation:r,id:a,color:n,opacity:o,popupRef:l},parent:{page:{pageNumber:d}}}=t;s=t={annotationType:h.HIGHLIGHT,color:Array.from(n),opacity:o,quadPoints:e,boxes:null,pageIndex:d-1,rect:i.slice(0),rotation:r,id:a,deleted:!1,popupRef:l}}else if(t instanceof iv){let{data:{inkLists:e,rect:i,rotation:r,id:a,color:n,borderStyle:{rawWidth:o},popupRef:l},parent:{page:{pageNumber:d}}}=t;s=t={annotationType:h.HIGHLIGHT,color:Array.from(n),thickness:o,inkLists:e,boxes:null,pageIndex:d-1,rect:i.slice(0),rotation:r,id:a,deleted:!1,popupRef:l}}let{color:r,quadPoints:a,inkLists:n,opacity:o}=t,l=await super.deserialize(t,e,i);l.color=O.makeHexColor(...r),l.#au=o||1,n&&(l.#rM=t.thickness),l.annotationElementId=t.id||null,l._initialData=s;let[d,c]=l.pageDimensions,[u,p]=l.pageTranslation;if(a){let t=l.#ar=[];for(let e=0;e<a.length;e+=8)t.push({x:(a[e]-u)/d,y:1-(a[e+1]-p)/c,width:(a[e+2]-a[e])/d,height:(a[e+1]-a[e+5])/c});l.#aA(),l.#ab(),l.rotate(l.rotation)}else if(n){l.#ac=!0;let t=n[0],i={x:t[0]-u,y:c-(t[1]-p)},s=new iL(i,[0,0,d,c],1,l.#rM/2,!0,.001);for(let e=0,r=t.length;e<r;e+=2)i.x=t[e]-u,i.y=c-(t[e+1]-p),s.add(i);let{id:r,clipPathId:a}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:l.color,"fill-opacity":l._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:s.toSVGPath()}},!0,!0);l.#am({highlightOutlines:s.getOutlines(),highlightId:r,clipPathId:a}),l.#ab(),l.rotate(l.parentRotation)}return l}serialize(t=!1){if(this.isEmpty()||t)return null;if(this.deleted)return this.serializeDeleted();let e=this.getRect(0,0),i=tv._colorManager.convert(this.color),s={annotationType:h.HIGHLIGHT,color:i,opacity:this.#au,thickness:this.#rM,quadPoints:this.#aC(),outlines:this.#aT(e),pageIndex:this.pageIndex,rect:e,rotation:this.#a_(),structTreeParentId:this._structTreeParentId};return this.annotationElementId&&!this.#rm(s)?null:(s.id=this.annotationElementId,s)}#rm(t){let{color:e}=this._initialData;return t.color.some((t,i)=>t!==e[i])}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}}class iB{#aI=Object.create(null);updateProperty(t,e){this[t]=e,this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(let[e,i]of Object.entries(t))e.startsWith("_")||this.updateProperty(e,i)}updateSVGProperty(t,e){this.#aI[t]=e}toSVGProperties(){let t=this.#aI;return this.#aI=Object.create(null),{root:t}}reset(){this.#aI=Object.create(null)}updateAll(t=this){this.updateProperties(t)}clone(){w("Not implemented")}}class iz extends tv{#aD=null;#aR;_drawId=null;static _currentDrawId=-1;static _currentParent=null;static #ak=null;static #aL=null;static #aF=null;static #aN=NaN;static #aO=null;static #aB=null;static #az=NaN;static _INNER_MARGIN=3;constructor(t){super(t),this.#aR=t.mustBeCommitted||!1,this._addOutlines(t)}_addOutlines(t){t.drawOutlines&&(this.#aH(t),this.#ab())}#aH({drawOutlines:t,drawId:e,drawingOptions:i}){this.#aD=t,this._drawingOptions||=i,e>=0?(this._drawId=e,this.parent.drawLayer.finalizeDraw(e,t.defaultProperties)):this._drawId=this.#aU(t,this.parent),this.#a$(t.box)}#aU(t,e){let{id:i}=e.drawLayer.draw(iz._mergeSVGProperties(this._drawingOptions.toSVGProperties(),t.defaultSVGProperties),!1,!1);return i}static _mergeSVGProperties(t,e){let i=new Set(Object.keys(t));for(let[s,r]of Object.entries(e))i.has(s)?Object.assign(t[s],r):t[s]=r;return t}static getDefaultDrawingOptions(t){w("Not implemented")}static get typesMap(){w("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(t,e){let i=this.typesMap.get(t);i&&this._defaultDrawingOptions.updateProperty(i,e),this._currentParent&&(iz.#ak.updateProperty(i,e),this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}updateParams(t,e){let i=this.constructor.typesMap.get(t);i&&this._updateProperty(t,i,e)}static get defaultPropertiesToUpdate(){let t=[],e=this._defaultDrawingOptions;for(let[i,s]of this.typesMap)t.push([i,e[s]]);return t}get propertiesToUpdate(){let t=[],{_drawingOptions:e}=this;for(let[i,s]of this.constructor.typesMap)t.push([i,e[s]]);return t}_updateProperty(t,e,i){let s=this._drawingOptions,r=s[e],a=t=>{s.updateProperty(e,t);let i=this.#aD.updateProperty(e,t);i&&this.#a$(i),this.parent?.drawLayer.updateProperties(this._drawId,s.toSVGProperties())};this.addCommands({cmd:a.bind(this,i),undo:a.bind(this,r),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:t,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){this.parent?.drawLayer.updateProperties(this._drawId,iz._mergeSVGProperties(this.#aD.getPathResizingSVGProperties(this.#aG()),{bbox:this.#aj()}))}_onResized(){this.parent?.drawLayer.updateProperties(this._drawId,iz._mergeSVGProperties(this.#aD.getPathResizedSVGProperties(this.#aG()),{bbox:this.#aj()}))}_onTranslating(t,e){this.parent?.drawLayer.updateProperties(this._drawId,{bbox:this.#aj()})}_onTranslated(){this.parent?.drawLayer.updateProperties(this._drawId,iz._mergeSVGProperties(this.#aD.getPathTranslatedSVGProperties(this.#aG(),this.parentDimensions),{bbox:this.#aj()}))}_onStartDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit(),this.disableEditMode(),this.disableEditing()}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),this._isDraggable=!0,this.#aR&&(this.#aR=!1,this.commit(),this.parent.setSelected(this),t&&this.isOnScreen&&this.div.focus())}remove(){this.#ax(),super.remove()}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.#ab(),this.#a$(this.#aD.box),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?(this._uiManager.removeShouldRescale(this),this.#ax()):t&&(this._uiManager.addShouldRescale(this),this.#ab(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),e&&this.select()}#ax(){null!==this._drawId&&this.parent&&(this.parent.drawLayer.remove(this._drawId),this._drawId=null,this._drawingOptions.reset())}#ab(t=this.parent){if(null===this._drawId||this.parent!==t){if(null!==this._drawId)return void this.parent.drawLayer.updateParent(this._drawId,t.drawLayer);this._drawingOptions.updateAll(),this._drawId=this.#aU(this.#aD,t)}}#aV([t,e,i,s]){let{parentDimensions:[r,a],rotation:n}=this;switch(n){case 90:return[e,1-t,a/r*i,r/a*s];case 180:return[1-t,1-e,i,s];case 270:return[1-e,t,a/r*i,r/a*s];default:return[t,e,i,s]}}#aG(){let{x:t,y:e,width:i,height:s,parentDimensions:[r,a],rotation:n}=this;switch(n){case 90:return[1-e,t,r/a*i,a/r*s];case 180:return[1-t,1-e,i,s];case 270:return[e,1-t,r/a*i,a/r*s];default:return[t,e,i,s]}}#a$(t){if([this.x,this.y,this.width,this.height]=this.#aV(t),this.div){this.fixAndSetPosition();let[t,e]=this.parentDimensions;this.setDims(this.width*t,this.height*e)}this._onResized()}#aj(){let{x:t,y:e,width:i,height:s,rotation:r,parentRotation:a,parentDimensions:[n,o]}=this;switch((4*r+a)/90){case 1:return[1-e-s,t,s,i];case 2:return[1-t-i,1-e-s,i,s];case 3:return[e,1-t-i,s,i];case 4:return[t,e-n/o*i,o/n*s,n/o*i];case 5:return[1-e,t,n/o*i,o/n*s];case 6:return[1-t-o/n*s,1-e,o/n*s,n/o*i];case 7:return[e-n/o*i,1-t-o/n*s,n/o*i,o/n*s];case 8:return[t-i,e-s,i,s];case 9:return[1-e,t-i,s,i];case 10:return[1-t,1-e,i,s];case 11:return[e-s,1-t,s,i];case 12:return[t-o/n*s,e,o/n*s,n/o*i];case 13:return[1-e-n/o*i,t-o/n*s,n/o*i,o/n*s];case 14:return[1-t,1-e-n/o*i,o/n*s,n/o*i];case 15:return[e,1-t,n/o*i,o/n*s];default:return[t,e,i,s]}}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,iz._mergeSVGProperties({bbox:this.#aj()},this.#aD.updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&this.#a$(this.#aD.updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){let t,e;if(this.div)return this.div;this._isCopy&&(t=this.x,e=this.y);let i=super.render();i.classList.add("draw");let s=document.createElement("div");i.append(s),s.setAttribute("aria-hidden","true"),s.className="internal";let[r,a]=this.parentDimensions;return this.setDims(this.width*r,this.height*a),this._uiManager.addShouldRescale(this),this.disableEditing(),this._isCopy&&this._moveAfterPaste(t,e),i}static createDrawerInstance(t,e,i,s,r){w("Not implemented")}static startDrawing(t,e,i,s){let{target:r,offsetX:a,offsetY:n,pointerId:o,pointerType:l}=s;if(iz.#aO&&iz.#aO!==l)return;let{viewport:{rotation:h}}=t,{width:d,height:c}=r.getBoundingClientRect(),u=iz.#aL=new AbortController,p=t.combinedSignal(u);if(iz.#aN||=o,iz.#aO??=l,window.addEventListener("pointerup",t=>{iz.#aN===t.pointerId?this._endDraw(t):iz.#aB?.delete(t.pointerId)},{signal:p}),window.addEventListener("pointercancel",t=>{iz.#aN===t.pointerId?this._currentParent.endDrawingSession():iz.#aB?.delete(t.pointerId)},{signal:p}),window.addEventListener("pointerdown",t=>{iz.#aO===t.pointerType&&((iz.#aB||=new Set).add(t.pointerId),iz.#ak.isCancellable()&&(iz.#ak.removeLastElement(),iz.#ak.isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)))},{capture:!0,passive:!1,signal:p}),window.addEventListener("contextmenu",Z,{signal:p}),r.addEventListener("pointermove",this._drawMove.bind(this),{signal:p}),r.addEventListener("touchmove",t=>{t.timeStamp===iz.#az&&tt(t)},{signal:p}),t.toggleDrawing(),e._editorUndoBar?.hide(),iz.#ak)return void t.drawLayer.updateProperties(this._currentDrawId,iz.#ak.startNew(a,n,d,c,h));e.updateUIForDefaultProperties(this),iz.#ak=this.createDrawerInstance(a,n,d,c,h),iz.#aF=this.getDefaultDrawingOptions(),this._currentParent=t,{id:this._currentDrawId}=t.drawLayer.draw(this._mergeSVGProperties(iz.#aF.toSVGProperties(),iz.#ak.defaultSVGProperties),!0,!1)}static _drawMove(t){if(iz.#az=-1,!iz.#ak)return;let{offsetX:e,offsetY:i,pointerId:s}=t;if(iz.#aN===s){if(iz.#aB?.size>=1)return void this._endDraw(t);this._currentParent.drawLayer.updateProperties(this._currentDrawId,iz.#ak.add(e,i)),iz.#az=t.timeStamp,tt(t)}}static _cleanup(t){t&&(this._currentDrawId=-1,this._currentParent=null,iz.#ak=null,iz.#aF=null,iz.#aO=null,iz.#az=NaN),iz.#aL&&(iz.#aL.abort(),iz.#aL=null,iz.#aN=NaN,iz.#aB=null)}static _endDraw(t){let e=this._currentParent;if(e){if(e.toggleDrawing(!0),this._cleanup(!1),t?.target===e.div&&e.drawLayer.updateProperties(this._currentDrawId,iz.#ak.end(t.offsetX,t.offsetY)),this.supportMultipleDrawings){let t=iz.#ak,i=this._currentDrawId,s=t.getLastElement();e.addCommands({cmd:()=>{e.drawLayer.updateProperties(i,t.setLastElement(s))},undo:()=>{e.drawLayer.updateProperties(i,t.removeLastElement())},mustExec:!1,type:d.DRAW_STEP});return}this.endDrawing(!1)}}static endDrawing(t){let e=this._currentParent;if(!e)return null;if(e.toggleDrawing(!0),e.cleanUndoStack(d.DRAW_STEP),!iz.#ak.isEmpty()){let{pageDimensions:[i,s],scale:r}=e,a=e.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:iz.#ak.getOutlines(i*r,s*r,r,this._INNER_MARGIN),drawingOptions:iz.#aF,mustBeCommitted:!t});return this._cleanup(!0),a}return e.drawLayer.remove(this._currentDrawId),this._cleanup(!0),null}createDrawingOptions(t){}static deserializeDraw(t,e,i,s,r,a){w("Not implemented")}static async deserialize(t,e,i){let{rawDims:{pageWidth:s,pageHeight:r,pageX:a,pageY:n}}=e.viewport,o=this.deserializeDraw(a,n,s,r,this._INNER_MARGIN,t),l=await super.deserialize(t,e,i);return l.createDrawingOptions(t),l.#aH({drawOutlines:o}),l.#ab(),l.onScaleChanging(),l.rotate(),l}serializeDraw(t){let[e,i]=this.pageTranslation,[s,r]=this.pageDimensions;return this.#aD.serialize([e,i,s,r],t)}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}}class iH{#r_=new Float64Array(6);#s0;#aW;#iU;#rM;#rP;#aq="";#aK=0;#rQ=new iU;#aX;#aY;constructor(t,e,i,s,r,a){this.#aX=i,this.#aY=s,this.#iU=r,this.#rM=a,[t,e]=this.#aQ(t,e);let n=this.#s0=[NaN,NaN,NaN,NaN,t,e];this.#rP=[t,e],this.#aW=[{line:n,points:this.#rP}],this.#r_.set(n,0)}updateProperty(t,e){"stroke-width"===t&&(this.#rM=e)}#aQ(t,e){return iP._normalizePoint(t,e,this.#aX,this.#aY,this.#iU)}isEmpty(){return!this.#aW||0===this.#aW.length}isCancellable(){return this.#rP.length<=10}add(t,e){[t,e]=this.#aQ(t,e);let[i,s,r,a]=this.#r_.subarray(2,6),n=t-r,o=e-a;return 2>=Math.hypot(this.#aX*n,this.#aY*o)?null:((this.#rP.push(t,e),isNaN(i))?(this.#r_.set([r,a,t,e],2),this.#s0.push(NaN,NaN,NaN,NaN,t,e)):(isNaN(this.#r_[0])&&this.#s0.splice(6,6),this.#r_.set([i,s,r,a,t,e],0),this.#s0.push(...iP.createBezierPoints(i,s,r,a,t,e))),{path:{d:this.toSVGPath()}})}end(t,e){let i=this.add(t,e);return i||(2===this.#rP.length?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,i,s,r){this.#aX=i,this.#aY=s,this.#iU=r,[t,e]=this.#aQ(t,e);let a=this.#s0=[NaN,NaN,NaN,NaN,t,e];this.#rP=[t,e];let n=this.#aW.at(-1);return n&&(n.line=new Float32Array(n.line),n.points=new Float32Array(n.points)),this.#aW.push({line:a,points:this.#rP}),this.#r_.set(a,0),this.#aK=0,this.toSVGPath(),null}getLastElement(){return this.#aW.at(-1)}setLastElement(t){return this.#aW?(this.#aW.push(t),this.#s0=t.line,this.#rP=t.points,this.#aK=0,{path:{d:this.toSVGPath()}}):this.#rQ.setLastElement(t)}removeLastElement(){if(!this.#aW)return this.#rQ.removeLastElement();this.#aW.pop(),this.#aq="";for(let t=0,e=this.#aW.length;t<e;t++){let{line:e,points:i}=this.#aW[t];this.#s0=e,this.#rP=i,this.#aK=0,this.toSVGPath()}return{path:{d:this.#aq}}}toSVGPath(){let t=iP.svgRound(this.#s0[4]),e=iP.svgRound(this.#s0[5]);if(2===this.#rP.length)return this.#aq=`${this.#aq} M ${t} ${e} Z`,this.#aq;if(this.#rP.length<=6){let i=this.#aq.lastIndexOf("M");this.#aq=`${this.#aq.slice(0,i)} M ${t} ${e}`,this.#aK=6}if(4===this.#rP.length){let t=iP.svgRound(this.#s0[10]),e=iP.svgRound(this.#s0[11]);return this.#aq=`${this.#aq} L ${t} ${e}`,this.#aK=12,this.#aq}let i=[];0===this.#aK&&(i.push(`M ${t} ${e}`),this.#aK=6);for(let t=this.#aK,e=this.#s0.length;t<e;t+=6){let[e,s,r,a,n,o]=this.#s0.slice(t,t+6).map(iP.svgRound);i.push(`C${e} ${s} ${r} ${a} ${n} ${o}`)}return this.#aq+=i.join(" "),this.#aK=this.#s0.length,this.#aq}getOutlines(t,e,i,s){let r=this.#aW.at(-1);return r.line=new Float32Array(r.line),r.points=new Float32Array(r.points),this.#rQ.build(this.#aW,t,e,i,this.#iU,this.#rM,s),this.#r_=null,this.#s0=null,this.#aW=null,this.#aq=null,this.#rQ}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}class iU extends iP{#rH;#aJ=0;#rv;#aW;#aX;#aY;#aZ;#iU;#rM;build(t,e,i,s,r,a,n){this.#aX=e,this.#aY=i,this.#aZ=s,this.#iU=r,this.#rM=a,this.#rv=n??0,this.#aW=t,this.#a0()}get thickness(){return this.#rM}setLastElement(t){return this.#aW.push(t),{path:{d:this.toSVGPath()}}}removeLastElement(){return this.#aW.pop(),{path:{d:this.toSVGPath()}}}toSVGPath(){let t=[];for(let{line:e}of this.#aW){if(t.push(`M${iP.svgRound(e[4])} ${iP.svgRound(e[5])}`),6===e.length){t.push("Z");continue}if(12===e.length&&isNaN(e[6])){t.push(`L${iP.svgRound(e[10])} ${iP.svgRound(e[11])}`);continue}for(let i=6,s=e.length;i<s;i+=6){let[s,r,a,n,o,l]=e.subarray(i,i+6).map(iP.svgRound);t.push(`C${s} ${r} ${a} ${n} ${o} ${l}`)}}return t.join("")}serialize([t,e,i,s],r){let a,n,o,l,h,d,c,u,p,g=[],f=[],[m,b,A,v]=this.#a1();switch(this.#iU){case 0:p=iP._rescale,a=t,n=e+s,o=i,l=-s,h=t+m*i,d=e+(1-b-v)*s,c=t+(m+A)*i,u=e+(1-b)*s;break;case 90:p=iP._rescaleAndSwap,a=t,n=e,o=i,l=s,h=t+b*i,d=e+m*s,c=t+(b+v)*i,u=e+(m+A)*s;break;case 180:p=iP._rescale,a=t+i,n=e,o=-i,l=s,h=t+(1-m-A)*i,d=e+b*s,c=t+(1-m)*i,u=e+(b+v)*s;break;case 270:p=iP._rescaleAndSwap,a=t+i,n=e+s,o=-i,l=-s,h=t+(1-b-v)*i,d=e+(1-m-A)*s,c=t+(1-b)*i,u=e+(1-m)*s}for(let{line:t,points:e}of this.#aW)g.push(p(t,a,n,o,l,r?Array(t.length):null)),f.push(p(e,a,n,o,l,r?Array(e.length):null));return{lines:g,points:f,rect:[h,d,c,u]}}static deserialize(t,e,i,s,r,{paths:{lines:a,points:n},rotation:o,thickness:l}){let h,d,c,u,p,g=[];switch(o){case 0:p=iP._rescale,h=-t/i,d=e/s+1,c=1/i,u=-1/s;break;case 90:p=iP._rescaleAndSwap,h=-e/s,d=-t/i,c=1/s,u=1/i;break;case 180:p=iP._rescale,h=t/i+1,d=-e/s,c=-1/i,u=1/s;break;case 270:p=iP._rescaleAndSwap,h=e/s+1,d=t/i+1,c=-1/s,u=-1/i}if(!a)for(let t of(a=[],n)){let e=t.length;if(2===e){a.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1]]));continue}if(4===e){a.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1],NaN,NaN,NaN,NaN,t[2],t[3]]));continue}let i=new Float32Array(3*(e-2));a.push(i);let[s,r,n,o]=t.subarray(0,4);i.set([NaN,NaN,NaN,NaN,s,r],0);for(let a=4;a<e;a+=2){let e=t[a],l=t[a+1];i.set(iP.createBezierPoints(s,r,n,o,e,l),(a-2)*3),[s,r,n,o]=[n,o,e,l]}}for(let t=0,e=a.length;t<e;t++)g.push({line:p(a[t].map(t=>t??NaN),h,d,c,u),points:p(n[t].map(t=>t??NaN),h,d,c,u)});let f=new this.prototype.constructor;return f.build(g,i,s,1,o,l,r),f}#a2(t=this.#rM){let e=this.#rv+t/2*this.#aZ;return this.#iU%180==0?[e/this.#aX,e/this.#aY]:[e/this.#aY,e/this.#aX]}#a1(){let[t,e,i,s]=this.#rH,[r,a]=this.#a2(0);return[t+r,e+a,i-2*r,s-2*a]}#a0(){let t=this.#rH=new Float32Array([1/0,1/0,-1/0,-1/0]);for(let{line:e}of this.#aW){if(e.length<=12){for(let i=4,s=e.length;i<s;i+=6)O.pointBoundingBox(e[i],e[i+1],t);continue}let i=e[4],s=e[5];for(let r=6,a=e.length;r<a;r+=6){let[a,n,o,l,h,d]=e.subarray(r,r+6);O.bezierBoundingBox(i,s,a,n,o,l,h,d,t),i=h,s=d}}let[e,i]=this.#a2();t[0]=$(t[0]-e,0,1),t[1]=$(t[1]-i,0,1),t[2]=$(t[2]+e,0,1),t[3]=$(t[3]+i,0,1),t[2]-=t[0],t[3]-=t[1]}get box(){return this.#rH}updateProperty(t,e){return"stroke-width"===t?this.#ay(e):null}#ay(t){let[e,i]=this.#a2();this.#rM=t;let[s,r]=this.#a2(),[a,n]=[s-e,r-i],o=this.#rH;return o[0]-=a,o[1]-=n,o[2]+=2*a,o[3]+=2*n,o}updateParentDimensions([t,e],i){let[s,r]=this.#a2();this.#aX=t,this.#aY=e,this.#aZ=i;let[a,n]=this.#a2(),o=a-s,l=n-r,h=this.#rH;return h[0]-=o,h[1]-=l,h[2]+=2*o,h[3]+=2*l,h}updateRotation(t){return this.#aJ=t,{path:{transform:this.rotationTransform}}}get viewBox(){return this.#rH.map(iP.svgRound).join(" ")}get defaultProperties(){let[t,e]=this.#rH;return{root:{viewBox:this.viewBox},path:{"transform-origin":`${iP.svgRound(t)} ${iP.svgRound(e)}`}}}get rotationTransform(){let[,,t,e]=this.#rH,i=0,s=0,r=0,a=0,n=0,o=0;switch(this.#aJ){case 90:s=e/t,r=-t/e,n=t;break;case 180:i=-1,a=-1,n=t,o=e;break;case 270:s=-e/t,r=t/e,o=e;break;default:return""}return`matrix(${i} ${s} ${r} ${a} ${iP.svgRound(n)} ${iP.svgRound(o)})`}getPathResizingSVGProperties([t,e,i,s]){let[r,a]=this.#a2(),[n,o,l,h]=this.#rH;if(Math.abs(l-r)<=iP.PRECISION||Math.abs(h-a)<=iP.PRECISION){let r=t+i/2-(n+l/2),a=e+s/2-(o+h/2);return{path:{"transform-origin":`${iP.svgRound(t)} ${iP.svgRound(e)}`,transform:`${this.rotationTransform} translate(${r} ${a})`}}}let d=(i-2*r)/(l-2*r),c=(s-2*a)/(h-2*a);return{path:{"transform-origin":`${iP.svgRound(n)} ${iP.svgRound(o)}`,transform:`${this.rotationTransform} scale(${l/i} ${h/s}) translate(${iP.svgRound(r)} ${iP.svgRound(a)}) scale(${d} ${c}) translate(${iP.svgRound(-r)} ${iP.svgRound(-a)})`}}}getPathResizedSVGProperties([t,e,i,s]){let[r,a]=this.#a2(),n=this.#rH,[o,l,h,d]=n;if(n[0]=t,n[1]=e,n[2]=i,n[3]=s,Math.abs(h-r)<=iP.PRECISION||Math.abs(d-a)<=iP.PRECISION){let r=t+i/2-(o+h/2),a=e+s/2-(l+d/2);for(let{line:t,points:e}of this.#aW)iP._translate(t,r,a,t),iP._translate(e,r,a,e);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${iP.svgRound(t)} ${iP.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}let c=(i-2*r)/(h-2*r),u=(s-2*a)/(d-2*a),p=-c*(o+r)+t+r,g=-u*(l+a)+e+a;if(1!==c||1!==u||0!==p||0!==g)for(let{line:t,points:e}of this.#aW)iP._rescale(t,p,g,c,u,t),iP._rescale(e,p,g,c,u,e);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${iP.svgRound(t)} ${iP.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([t,e],i){let[s,r]=i,a=this.#rH,n=t-a[0],o=e-a[1];if(this.#aX===s&&this.#aY===r)for(let{line:t,points:e}of this.#aW)iP._translate(t,n,o,t),iP._translate(e,n,o,e);else{let t=this.#aX/s,e=this.#aY/r;for(let{line:i,points:a}of(this.#aX=s,this.#aY=r,this.#aW))iP._rescale(i,n,o,t,e,i),iP._rescale(a,n,o,t,e,a);a[2]*=t,a[3]*=e}return a[0]=t,a[1]=e,{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${iP.svgRound(t)} ${iP.svgRound(e)}`}}}get defaultSVGProperties(){let t=this.#rH;return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${iP.svgRound(t[0])} ${iP.svgRound(t[1])}`,transform:this.rotationTransform||null},bbox:t}}}class i$ extends iB{constructor(t){super(),this._viewParameters=t,super.updateProperties({fill:"none",stroke:tv._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){"stroke-width"===t&&(e??=this["stroke-width"],e*=this._viewParameters.realScale),super.updateSVGProperty(t,e)}clone(){let t=new i$(this._viewParameters);return t.updateAll(this),t}}class iG extends iz{static _type="ink";static _editorType=h.INK;static _defaultDrawingOptions=null;constructor(t){super({...t,name:"inkEditor"}),this._willKeepAspectRatio=!0,this.defaultL10nId="pdfjs-editor-ink-editor"}static initialize(t,e){tv.initialize(t,e),this._defaultDrawingOptions=new i$(e.viewParameters)}static getDefaultDrawingOptions(t){let e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!0}static get typesMap(){return S(this,"typesMap",new Map([[d.INK_THICKNESS,"stroke-width"],[d.INK_COLOR,"stroke"],[d.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(t,e,i,s,r){return new iH(t,e,i,s,r,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(t,e,i,s,r,a){return iU.deserialize(t,e,i,s,r,a)}static async deserialize(t,e,i){let s=null;if(t instanceof iv){let{data:{inkLists:e,rect:i,rotation:r,id:a,color:n,opacity:o,borderStyle:{rawWidth:l},popupRef:d},parent:{page:{pageNumber:c}}}=t;s=t={annotationType:h.INK,color:Array.from(n),thickness:l,opacity:o,paths:{points:e},boxes:null,pageIndex:c-1,rect:i.slice(0),rotation:r,id:a,deleted:!1,popupRef:d}}let r=await super.deserialize(t,e,i);return r.annotationElementId=t.id||null,r._initialData=s,r}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();let{_drawId:t,_drawingOptions:e,parent:i}=this;e.updateSVGProperty("stroke-width"),i.drawLayer.updateProperties(t,e.toSVGProperties())}static onScaleChangingWhenDrawing(){let t=this._currentParent;t&&(super.onScaleChangingWhenDrawing(),this._defaultDrawingOptions.updateSVGProperty("stroke-width"),t.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}createDrawingOptions({color:t,thickness:e,opacity:i}){this._drawingOptions=iG.getDefaultDrawingOptions({stroke:O.makeHexColor(...t),"stroke-width":e,"stroke-opacity":i})}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();let{lines:e,points:i,rect:s}=this.serializeDraw(t),{_drawingOptions:{stroke:r,"stroke-opacity":a,"stroke-width":n}}=this,o={annotationType:h.INK,color:tv._colorManager.convert(r),opacity:a,thickness:n,paths:{lines:e,points:i},pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(o.isCopy=!0,o):this.annotationElementId&&!this.#rm(o)?null:(o.id=this.annotationElementId,o)}#rm(t){let{color:e,thickness:i,opacity:s,pageIndex:r}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||t.color.some((t,i)=>t!==e[i])||t.thickness!==i||t.opacity!==s||t.pageIndex!==r}renderAnnotationElement(t){let{points:e,rect:i}=this.serializeDraw(!1);return t.updateEdited({rect:i,thickness:this._drawingOptions["stroke-width"],points:e}),null}}class ij extends iU{toSVGPath(){let t=super.toSVGPath();return t.endsWith("Z")||(t+="Z"),t}}class iV{static #a3={maxDim:512,sigmaSFactor:.02,sigmaR:25,kernelSize:16};static #a5(t,e,i,s){return(i-=t,s-=e,0===i)?s>0?0:4:1===i?s+6:2-s}static #a6=new Int32Array([0,1,-1,1,-1,0,-1,-1,0,-1,1,-1,1,0,1,1]);static #a4(t,e,i,s,r,a,n){let o=this.#a5(i,s,r,a);for(let r=0;r<8;r++){let a=(-r+o-n+16)%8;if(0!==t[(i+this.#a6[2*a])*e+(s+this.#a6[2*a+1])])return a}return -1}static #a8(t,e,i,s,r,a,n){let o=this.#a5(i,s,r,a);for(let r=0;r<8;r++){let a=(r+o+n+16)%8;if(0!==t[(i+this.#a6[2*a])*e+(s+this.#a6[2*a+1])])return a}return -1}static #a7(t,e,i,s){let r,a=t.length,n=new Int32Array(a);for(let e=0;e<a;e++)n[e]=+(t[e]<=s);for(let t=1;t<i-1;t++)n[t*e]=n[t*e+e-1]=0;for(let t=0;t<e;t++)n[t]=n[e*i-1-t]=0;let o=1,l=[];for(let t=1;t<i-1;t++){r=1;for(let i=1;i<e-1;i++){let s,a=t*e+i,h=n[a];if(0===h)continue;let d=t,c=i;if(1===h&&0===n[a-1])o+=1,c-=1;else if(h>=1&&0===n[a+1])o+=1,c+=1,h>1&&(r=h);else{1!==h&&(r=Math.abs(h));continue}let u=[i,t],p=c===i+1,g={isHole:p,points:u,id:o,parent:0};for(let t of(l.push(g),l))if(t.id===r){s=t;break}s?s.isHole?g.parent=p?s.parent:r:g.parent=p?r:s.parent:g.parent=p?r:0;let f=this.#a4(n,e,t,i,d,c,0);if(-1===f){n[a]=-o,1!==n[a]&&(r=Math.abs(n[a]));continue}let m=this.#a6[2*f],b=this.#a6[2*f+1],A=t+m,v=i+b;d=A,c=v;let y=t,w=i;for(;;){let s=this.#a8(n,e,y,w,d,c,1);m=this.#a6[2*s],b=this.#a6[2*s+1];let l=y+m,h=w+b;u.push(h,l);let p=y*e+w;if(0===n[p+1]?n[p]=-o:1===n[p]&&(n[p]=o),l===t&&h===i&&y===A&&w===v){1!==n[a]&&(r=Math.abs(n[a]));break}d=y,c=w,y=l,w=h}}}return l}static #a9(t,e,i,s){if(i-e<=4){for(let r=e;r<i-2;r+=2)s.push(t[r],t[r+1]);return}let r=t[e],a=t[e+1],n=t[i-4]-r,o=t[i-3]-a,l=Math.hypot(n,o),h=n/l,d=o/l,c=h*a-d*r,u=1/l,p=Math.atan(o/n),g=Math.cos(p),f=Math.sin(p),m=u*(Math.abs(g)+Math.abs(f)),b=u*(1-m+m**2),A=Math.max(Math.atan(Math.abs(f+g)*b),Math.atan(Math.abs(f-g)*b)),v=0,y=e;for(let s=e+2;s<i-2;s+=2){let e=Math.abs(c-h*t[s+1]+d*t[s]);e>v&&(y=s,v=e)}v>(l*A)**2?(this.#a9(t,e,y+2,s),this.#a9(t,y,i,s)):s.push(r,a)}static #nt(t){let e=[],i=t.length;return this.#a9(t,0,i,e),e.push(t[i-2],t[i-1]),e.length<=4?null:e}static #ne(t,e,i,s,r,a){let n=new Float32Array(a**2),o=-2*s**2,l=a>>1;for(let t=0;t<a;t++){let e=(t-l)**2;for(let i=0;i<a;i++)n[t*a+i]=Math.exp((e+(i-l)**2)/o)}let h=new Float32Array(256),d=-2*r**2;for(let t=0;t<256;t++)h[t]=Math.exp(t**2/d);let c=new Uint8Array(t.length),u=new Uint32Array(256);for(let s=0;s<i;s++)for(let r=0;r<e;r++){let o=s*e+r,d=t[o],p=0,g=0;for(let o=0;o<a;o++){let c=s+o-l;if(!(c<0)&&!(c>=i))for(let i=0;i<a;i++){let s=r+i-l;if(s<0||s>=e)continue;let u=t[c*e+s],f=n[o*a+i]*h[Math.abs(u-d)];p+=u*f,g+=f}}let f=c[o]=Math.round(p/g);u[f]++}return[c,u]}static #ni(t){let e=new Uint32Array(256);for(let i of t)e[i]++;return e}static #ns(t){let e=t.length,i=new Uint8ClampedArray(e>>2),s=-1/0,r=1/0;for(let e=0,a=i.length;e<a;e++){if(0===t[(e<<2)+3]){s=i[e]=255;continue}let a=i[e]=t[e<<2];a>s&&(s=a),a<r&&(r=a)}let a=255/(s-r);for(let t=0;t<e;t++)i[t]=(i[t]-r)*a;return i}static #nr(t){let e,i=-1/0,s=-1/0,r=t.findIndex(t=>0!==t),a=r,n=r;for(e=r;e<256;e++){let r=t[e];r>i&&(e-a>s&&(s=e-a,n=e-1),i=r,a=e)}for(e=n-1;e>=0&&!(t[e]>t[e+1]);e--);return e}static #na(t){let e=t,{width:i,height:s}=t,{maxDim:r}=this.#a3,a=i,n=s;if(i>r||s>r){let o=i,l=s,h=Math.log2(Math.max(i,s)/r),d=Math.floor(h);h=h===d?d-1:d;for(let i=0;i<h;i++){let i=new OffscreenCanvas(a=Math.ceil(o/2),n=Math.ceil(l/2));i.getContext("2d").drawImage(t,0,0,o,l,0,0,a,n),o=a,l=n,t!==e&&t.close(),t=i.transferToImageBitmap()}let c=Math.min(r/a,r/n);a=Math.round(a*c),n=Math.round(n*c)}let o=new OffscreenCanvas(a,n).getContext("2d",{willReadFrequently:!0});o.filter="grayscale(1)",o.drawImage(t,0,0,t.width,t.height,0,0,a,n);let l=o.getImageData(0,0,a,n).data;return[this.#ns(l),a,n]}static extractContoursFromText(t,{fontFamily:e,fontStyle:i,fontWeight:s},r,a,n,o){let l=new OffscreenCanvas(1,1),h=l.getContext("2d",{alpha:!1}),d=h.font=`${i} ${s} 200px ${e}`,{actualBoundingBoxLeft:c,actualBoundingBoxRight:u,actualBoundingBoxAscent:p,actualBoundingBoxDescent:g,fontBoundingBoxAscent:f,fontBoundingBoxDescent:m,width:b}=h.measureText(t),A=Math.ceil(1.5*Math.max(Math.abs(c)+Math.abs(u)||0,b)),v=Math.ceil(1.5*Math.max(Math.abs(p)+Math.abs(g)||200,Math.abs(f)+Math.abs(m)||200));(h=(l=new OffscreenCanvas(A,v)).getContext("2d",{alpha:!0,willReadFrequently:!0})).font=d,h.filter="grayscale(1)",h.fillStyle="white",h.fillRect(0,0,A,v),h.fillStyle="black",h.fillText(t,.5*A/2,1.5*v/2);let y=this.#ns(h.getImageData(0,0,A,v).data),w=this.#ni(y),_=this.#nr(w),x=this.#a7(y,A,v,_);return this.processDrawnLines({lines:{curves:x,width:A,height:v},pageWidth:r,pageHeight:a,rotation:n,innerMargin:o,mustSmooth:!0,areContours:!0})}static process(t,e,i,s,r){let[a,n,o]=this.#na(t),[l,h]=this.#ne(a,n,o,Math.hypot(n,o)*this.#a3.sigmaSFactor,this.#a3.sigmaR,this.#a3.kernelSize),d=this.#nr(h),c=this.#a7(l,n,o,d);return this.processDrawnLines({lines:{curves:c,width:n,height:o},pageWidth:e,pageHeight:i,rotation:s,innerMargin:r,mustSmooth:!0,areContours:!0})}static processDrawnLines({lines:t,pageWidth:e,pageHeight:i,rotation:s,innerMargin:r,mustSmooth:a,areContours:n}){s%180!=0&&([e,i]=[i,e]);let{curves:o,width:l,height:h}=t,d=t.thickness??0,c=[],u=Math.min(e/l,i/h),p=u/e,g=u/i,f=[];for(let{points:t}of o){let e=a?this.#nt(t):t;if(!e)continue;f.push(e);let i=e.length,s=new Float32Array(i),r=new Float32Array(3*(2===i?2:i-2));if(c.push({line:r,points:s}),2===i){s[0]=e[0]*p,s[1]=e[1]*g,r.set([NaN,NaN,NaN,NaN,s[0],s[1]],0);continue}let[n,o,l,h]=e;n*=p,o*=g,l*=p,h*=g,s.set([n,o,l,h],0),r.set([NaN,NaN,NaN,NaN,n,o],0);for(let t=4;t<i;t+=2){let i=s[t]=e[t]*p,a=s[t+1]=e[t+1]*g;r.set(iP.createBezierPoints(n,o,l,h,i,a),(t-2)*3),[n,o,l,h]=[l,h,i,a]}}if(0===c.length)return null;let m=n?new ij:new iU;return m.build(c,e,i,1,s,n?0:d,r),{outline:m,newCurves:f,areContours:n,thickness:d,width:l,height:h}}static async compressSignature({outlines:t,areContours:e,thickness:i,width:s,height:r}){let a,n=1/0,o=-1/0,l=0;for(let e of t){l+=e.length;for(let t=2,i=e.length;t<i;t++){let i=e[t]-e[t-2];n=Math.min(n,i),o=Math.max(o,i)}}a=n>=-128&&o<=127?Int8Array:n>=-32768&&o<=32767?Int16Array:Int32Array;let h=t.length,d=8+3*h,c=new Uint32Array(d),u=0;for(let n of(c[u++]=d*Uint32Array.BYTES_PER_ELEMENT+(l-2*h)*a.BYTES_PER_ELEMENT,c[u++]=0,c[u++]=s,c[u++]=r,c[u++]=+!e,c[u++]=Math.max(0,Math.floor(i??0)),c[u++]=h,c[u++]=a.BYTES_PER_ELEMENT,t))c[u++]=n.length-2,c[u++]=n[0],c[u++]=n[1];let p=new CompressionStream("deflate-raw"),g=p.writable.getWriter();await g.ready,g.write(c);let f=a.prototype.constructor;for(let e of t){let t=new f(e.length-2);for(let i=2,s=e.length;i<s;i++)t[i-2]=e[i]-e[i-2];g.write(t)}return g.close(),G(new Uint8Array(await new Response(p.readable).arrayBuffer()))}static async decompressSignature(t){try{let e,i=Uint8Array.fromBase64?Uint8Array.fromBase64(t):L(atob(t)),{readable:s,writable:r}=new DecompressionStream("deflate-raw"),a=r.getWriter();await a.ready,a.write(i).then(async()=>{await a.ready,await a.close()}).catch(()=>{});let n=null,o=0;for await(let t of s)(n||=new Uint8Array(new Uint32Array(t.buffer,0,4)[0])).set(t,o),o+=t.length;let l=new Uint32Array(n.buffer,0,n.length>>2),h=l[1];if(0!==h)throw Error(`Invalid version: ${h}`);let d=l[2],c=l[3],u=0===l[4],p=l[5],g=l[6],f=l[7],m=[],b=(8+3*g)*Uint32Array.BYTES_PER_ELEMENT;switch(f){case Int8Array.BYTES_PER_ELEMENT:e=new Int8Array(n.buffer,b);break;case Int16Array.BYTES_PER_ELEMENT:e=new Int16Array(n.buffer,b);break;case Int32Array.BYTES_PER_ELEMENT:e=new Int32Array(n.buffer,b)}o=0;for(let t=0;t<g;t++){let i=l[3*t+8],s=new Float32Array(i+2);m.push(s);for(let e=0;e<2;e++)s[e]=l[3*t+8+e+1];for(let t=0;t<i;t++)s[t+2]=s[t]+e[o++]}return{areContours:u,thickness:p,outlines:m,width:d,height:c}}catch(t){return y(`decompressSignature: ${t}`),null}}}class iW extends iB{constructor(){super(),super.updateProperties({fill:tv._defaultLineColor,"stroke-width":0})}clone(){let t=new iW;return t.updateAll(this),t}}class iq extends i${constructor(t){super(t),super.updateProperties({stroke:tv._defaultLineColor,"stroke-width":1})}clone(){let t=new iq(this._viewParameters);return t.updateAll(this),t}}class iK extends iz{#nn=!1;#no=null;#nl=null;#nh=null;static _type="signature";static _editorType=h.SIGNATURE;static _defaultDrawingOptions=null;constructor(t){super({...t,mustBeCommitted:!0,name:"signatureEditor"}),this._willKeepAspectRatio=!0,this.#nl=t.signatureData||null,this.#no=null,this.defaultL10nId="pdfjs-editor-signature-editor1"}static initialize(t,e){tv.initialize(t,e),this._defaultDrawingOptions=new iW,this._defaultDrawnSignatureOptions=new iq(e.viewParameters)}static getDefaultDrawingOptions(t){let e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!1}static get typesMap(){return S(this,"typesMap",new Map)}static get isDrawer(){return!1}get telemetryFinalData(){return{type:"signature",hasDescription:!!this.#no}}static computeTelemetryFinalData(t){let e=t.get("hasDescription");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}get isResizable(){return!0}onScaleChanging(){null!==this._drawId&&super.onScaleChanging()}render(){let t,e;if(this.div)return this.div;let{_isCopy:i}=this;if(i&&(this._isCopy=!1,t=this.x,e=this.y),super.render(),null===this._drawId)if(this.#nl){let{lines:t,mustSmooth:e,areContours:i,description:s,uuid:r,heightInPage:a}=this.#nl,{rawDims:{pageWidth:n,pageHeight:o},rotation:l}=this.parent.viewport,h=iV.processDrawnLines({lines:t,pageWidth:n,pageHeight:o,rotation:l,innerMargin:iK._INNER_MARGIN,mustSmooth:e,areContours:i});this.addSignature(h,a,s,r)}else this.div.setAttribute("data-l10n-args",JSON.stringify({description:""})),this.div.hidden=!0,this._uiManager.getSignature(this);return i&&(this._isCopy=!0,this._moveAfterPaste(t,e)),this.div}setUuid(t){this.#nh=t,this.addEditToolbar()}getUuid(){return this.#nh}get description(){return this.#no}set description(t){this.#no=t,super.addEditToolbar().then(e=>{e?.updateEditSignatureButton(t)})}getSignaturePreview(){let{newCurves:t,areContours:e,thickness:i,width:s,height:r}=this.#nl,a=Math.max(s,r),n=iV.processDrawnLines({lines:{curves:t.map(t=>({points:t})),thickness:i,width:s,height:r},pageWidth:a,pageHeight:a,rotation:0,innerMargin:0,mustSmooth:!1,areContours:e});return{areContours:e,outline:n.outline}}async addEditToolbar(){let t=await super.addEditToolbar();return t?(this._uiManager.signatureManager&&null!==this.#no&&(await t.addEditSignatureButton(this._uiManager.signatureManager,this.#nh,this.#no),t.show()),t):null}addSignature(t,e,i,s){let r,{x:a,y:n}=this,{outline:o}=this.#nl=t;this.#nn=o instanceof ij,this.#no=i,this.div.setAttribute("data-l10n-args",JSON.stringify({description:i})),this.#nn?r=iK.getDefaultDrawingOptions():(r=iK._defaultDrawnSignatureOptions.clone()).updateProperties({"stroke-width":o.thickness}),this._addOutlines({drawOutlines:o,drawingOptions:r});let[l,h]=this.parentDimensions,[,d]=this.pageDimensions,c=e/d;c=c>=1?.5:c,this.width*=c/this.height,this.width>=1&&(c*=.9/this.width,this.width=.9),this.height=c,this.setDims(l*this.width,h*this.height),this.x=a,this.y=n,this.center(),this._onResized(),this.onScaleChanging(),this.rotate(),this._uiManager.addToAnnotationStorage(this),this.setUuid(s),this._reportTelemetry({action:"pdfjs.signature.inserted",data:{hasBeenSaved:!!s,hasDescription:!!i}}),this.div.hidden=!1}getFromImage(t){let{rawDims:{pageWidth:e,pageHeight:i},rotation:s}=this.parent.viewport;return iV.process(t,e,i,s,iK._INNER_MARGIN)}getFromText(t,e){let{rawDims:{pageWidth:i,pageHeight:s},rotation:r}=this.parent.viewport;return iV.extractContoursFromText(t,e,i,s,r,iK._INNER_MARGIN)}getDrawnSignature(t){let{rawDims:{pageWidth:e,pageHeight:i},rotation:s}=this.parent.viewport;return iV.processDrawnLines({lines:t,pageWidth:e,pageHeight:i,rotation:s,innerMargin:iK._INNER_MARGIN,mustSmooth:!1,areContours:!1})}createDrawingOptions({areContours:t,thickness:e}){t?this._drawingOptions=iK.getDefaultDrawingOptions():(this._drawingOptions=iK._defaultDrawnSignatureOptions.clone(),this._drawingOptions.updateProperties({"stroke-width":e}))}serialize(t=!1){if(this.isEmpty())return null;let{lines:e,points:i,rect:s}=this.serializeDraw(t),{_drawingOptions:{"stroke-width":r}}=this,a={annotationType:h.SIGNATURE,isSignature:!0,areContours:this.#nn,color:[0,0,0],thickness:this.#nn?0:r,pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(a.paths={lines:e,points:i},a.uuid=this.#nh,a.isCopy=!0):a.lines=e,this.#no&&(a.accessibilityData={type:"Figure",alt:this.#no}),a}static deserializeDraw(t,e,i,s,r,a){return a.areContours?ij.deserialize(t,e,i,s,r,a):iU.deserialize(t,e,i,s,r,a)}static async deserialize(t,e,i){let s=await super.deserialize(t,e,i);return s.#nn=t.areContours,s.#no=t.accessibilityData?.alt||"",s.#nh=t.uuid,s}}class iX extends tv{#nd=null;#nc=null;#nu=null;#np=null;#ng=null;#nf="";#nm=null;#nb=!1;#nA=null;#nv=!1;#ny=!1;static _type="stamp";static _editorType=h.STAMP;constructor(t){super({...t,name:"stampEditor"}),this.#np=t.bitmapUrl,this.#ng=t.bitmapFile,this.defaultL10nId="pdfjs-editor-stamp-editor"}static initialize(t,e){tv.initialize(t,e)}static isHandlingMimeForPasting(t){return to.includes(t)}static paste(t,e){e.pasteEditor({mode:h.STAMP},{bitmapFile:t.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1),super.altTextFinish()}get telemetryFinalData(){return{type:"stamp",hasAltText:!!this.altTextData?.altText}}static computeTelemetryFinalData(t){let e=t.get("hasAltText");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}#nw(t,e=!1){if(!t)return void this.remove();this.#nd=t.bitmap,e||(this.#nc=t.id,this.#nv=t.isSvg),t.file&&(this.#nf=t.file.name),this.#n_()}#nx(){if(this.#nu=null,this._uiManager.enableWaiting(!1),this.#nm){if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#nd){this._editToolbar.hide(),this._uiManager.editAltText(this,!0);return}if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#nd){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}}async mlGuessAltText(t=null,e=!0){if(this.hasAltTextData())return null;let{mlManager:i}=this._uiManager;if(!i)throw Error("No ML.");if(!await i.isEnabledFor("altText"))throw Error("ML isn't enabled for alt text.");let{data:s,width:r,height:a}=t||this.copyCanvas(null,null,!0).imageData,n=await i.guess({name:"altText",request:{data:s,width:r,height:a,channels:s.length/(r*a)}});if(!n)throw Error("No response from the AI service.");if(n.error)throw Error("Error from the AI service.");if(n.cancel)return null;if(!n.output)throw Error("No valid response from the AI service.");let o=n.output;return await this.setGuessedAltText(o),e&&!this.hasAltTextData()&&(this.altTextData={alt:o,decorative:!1}),o}#nE(){if(this.#nc){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(this.#nc).then(t=>this.#nw(t,!0)).finally(()=>this.#nx());return}if(this.#np){let t=this.#np;this.#np=null,this._uiManager.enableWaiting(!0),this.#nu=this._uiManager.imageManager.getFromUrl(t).then(t=>this.#nw(t)).finally(()=>this.#nx());return}if(this.#ng){let t=this.#ng;this.#ng=null,this._uiManager.enableWaiting(!0),this.#nu=this._uiManager.imageManager.getFromFile(t).then(t=>this.#nw(t)).finally(()=>this.#nx());return}let t=document.createElement("input");t.type="file",t.accept=to.join(",");let e=this._uiManager._signal;this.#nu=new Promise(i=>{t.addEventListener("change",async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);let e=await this._uiManager.imageManager.getFromFile(t.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}}),this.#nw(e)}else this.remove();i()},{signal:e}),t.addEventListener("cancel",()=>{this.remove(),i()},{signal:e})}).finally(()=>this.#nx()),t.click()}remove(){this.#nc&&(this.#nd=null,this._uiManager.imageManager.deleteId(this.#nc),this.#nm?.remove(),this.#nm=null,this.#nA&&(clearTimeout(this.#nA),this.#nA=null)),super.remove()}rebuild(){if(!this.parent){this.#nc&&this.#nE();return}super.rebuild(),null!==this.div&&(this.#nc&&null===this.#nm&&this.#nE(),this.isAttachedToDOM||this.parent.add(this))}onceAdded(t){this._isDraggable=!0,t&&this.div.focus()}isEmpty(){return!(this.#nu||this.#nd||this.#np||this.#ng||this.#nc||this.#nb)}get isResizable(){return!0}render(){let t,e;return this.div||(this._isCopy&&(t=this.x,e=this.y),super.render(),this.div.hidden=!0,this.addAltTextButton(),this.#nb||(this.#nd?this.#n_():this.#nE()),this._isCopy&&this._moveAfterPaste(t,e),this._uiManager.addShouldRescale(this)),this.div}setCanvas(t,e){let{id:i,bitmap:s}=this._uiManager.imageManager.getFromCanvas(t,e);e.remove(),i&&this._uiManager.imageManager.isValidId(i)&&(this.#nc=i,s&&(this.#nd=s),this.#nb=!1,this.#n_())}_onResized(){this.onScaleChanging()}onScaleChanging(){this.parent&&(null!==this.#nA&&clearTimeout(this.#nA),this.#nA=setTimeout(()=>{this.#nA=null,this.#nS()},200))}#n_(){let{div:t}=this,{width:e,height:i}=this.#nd,[s,r]=this.pageDimensions;if(this.width)e=this.width*s,i=this.height*r;else if(e>.75*s||i>.75*r){let t=Math.min(.75*s/e,.75*r/i);e*=t,i*=t}let[a,n]=this.parentDimensions;this.setDims(e*a/s,i*n/r),this._uiManager.enableWaiting(!1);let o=this.#nm=document.createElement("canvas");o.setAttribute("role","img"),this.addContainer(o),this.width=e/s,this.height=i/r,this._initialOptions?.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&!this.annotationElementId||(t.hidden=!1),this.#nS(),this.#ny||(this.parent.addUndoableEditor(this),this.#ny=!0),this._reportTelemetry({action:"inserted_image"}),this.#nf&&this.div.setAttribute("aria-description",this.#nf)}copyCanvas(t,e,i=!1){t||(t=224);let{width:s,height:r}=this.#nd,a=new tn,n=this.#nd,o=s,l=r,h=null;if(e){if(s>e||r>e){let t=Math.min(e/s,e/r);o=Math.floor(s*t),l=Math.floor(r*t)}let t=(h=document.createElement("canvas")).width=Math.ceil(o*a.sx),i=h.height=Math.ceil(l*a.sy);this.#nv||(n=this.#nC(t,i));let d=h.getContext("2d");d.filter=this._uiManager.hcmFilter;let c="white",u="#cfcfd8";"none"!==this._uiManager.hcmFilter?u="black":window.matchMedia?.("(prefers-color-scheme: dark)").matches&&(c="#8f8f9d",u="#42414d");let p=15*a.sx,g=15*a.sy,f=new OffscreenCanvas(2*p,2*g),m=f.getContext("2d");m.fillStyle=c,m.fillRect(0,0,2*p,2*g),m.fillStyle=u,m.fillRect(0,0,p,g),m.fillRect(p,g,p,g),d.fillStyle=d.createPattern(f,"repeat"),d.fillRect(0,0,t,i),d.drawImage(n,0,0,n.width,n.height,0,0,t,i)}let d=null;if(i){let e,i;if(a.symmetric&&n.width<t&&n.height<t)e=n.width,i=n.height;else if(n=this.#nd,s>t||r>t){let a=Math.min(t/s,t/r);e=Math.floor(s*a),i=Math.floor(r*a),this.#nv||(n=this.#nC(e,i))}let o=new OffscreenCanvas(e,i).getContext("2d",{willReadFrequently:!0});o.drawImage(n,0,0,n.width,n.height,0,0,e,i),d={width:e,height:i,data:o.getImageData(0,0,e,i).data}}return{canvas:h,width:o,height:l,imageData:d}}#nC(t,e){let{width:i,height:s}=this.#nd,r=i,a=s,n=this.#nd;for(;r>2*t||a>2*e;){let i=r,s=a;r>2*t&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2)),a>2*e&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2));let o=new OffscreenCanvas(r,a);o.getContext("2d").drawImage(n,0,0,i,s,0,0,r,a),n=o.transferToImageBitmap()}return n}#nS(){let[t,e]=this.parentDimensions,{width:i,height:s}=this,r=new tn,a=Math.ceil(i*t*r.sx),n=Math.ceil(s*e*r.sy),o=this.#nm;if(!o||o.width===a&&o.height===n)return;o.width=a,o.height=n;let l=this.#nv?this.#nd:this.#nC(a,n),h=o.getContext("2d");h.filter=this._uiManager.hcmFilter,h.drawImage(l,0,0,l.width,l.height,0,0,a,n)}#nT(t){if(t){if(this.#nv){let t=this._uiManager.imageManager.getSvgUrl(this.#nc);if(t)return t}let t=document.createElement("canvas");return{width:t.width,height:t.height}=this.#nd,t.getContext("2d").drawImage(this.#nd,0,0),t.toDataURL()}if(this.#nv){let[t,e]=this.pageDimensions,i=Math.round(this.width*t*V.PDF_TO_CSS_UNITS),s=Math.round(this.height*e*V.PDF_TO_CSS_UNITS),r=new OffscreenCanvas(i,s);return r.getContext("2d").drawImage(this.#nd,0,0,this.#nd.width,this.#nd.height,0,0,i,s),r.transferToImageBitmap()}return structuredClone(this.#nd)}static async deserialize(t,e,i){let s=null,r=!1;if(t instanceof iE){let a,n,{data:{rect:o,rotation:l,id:d,structParent:c,popupRef:u},container:p,parent:{page:{pageNumber:g}},canvas:f}=t;f?(delete t.canvas,{id:a,bitmap:n}=i.imageManager.getFromCanvas(p.id,f),f.remove()):(r=!0,t._hasNoCanvas=!0);let m=(await e._structTree.getAriaAttributes(`${U}${d}`))?.get("aria-label")||"";s=t={annotationType:h.STAMP,bitmapId:a,bitmap:n,pageIndex:g-1,rect:o.slice(0),rotation:l,id:d,deleted:!1,accessibilityData:{decorative:!1,altText:m},isSvg:!1,structParent:c,popupRef:u}}let a=await super.deserialize(t,e,i),{rect:n,bitmap:o,bitmapUrl:l,bitmapId:d,isSvg:c,accessibilityData:u}=t;r?(i.addMissingCanvas(t.id,a),a.#nb=!0):d&&i.imageManager.isValidId(d)?(a.#nc=d,o&&(a.#nd=o)):a.#np=l,a.#nv=c;let[p,g]=a.pageDimensions;return a.width=(n[2]-n[0])/p,a.height=(n[3]-n[1])/g,a.annotationElementId=t.id||null,u&&(a.altTextData=u),a._initialData=s,a.#ny=!!s,a}serialize(t=!1,e=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();let i={annotationType:h.STAMP,bitmapId:this.#nc,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#nv,structTreeParentId:this._structTreeParentId};if(t)return i.bitmapUrl=this.#nT(!0),i.accessibilityData=this.serializeAltText(!0),i.isCopy=!0,i;let{decorative:s,altText:r}=this.serializeAltText(!1);if(!s&&r&&(i.accessibilityData={type:"Figure",alt:r}),this.annotationElementId){let t=this.#rm(i);if(t.isSame)return null;t.isSameAltText?delete i.accessibilityData:i.accessibilityData.structParent=this._initialData.structParent??-1}if(i.id=this.annotationElementId,null===e)return i;e.stamps||=new Map;let a=this.#nv?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(e.stamps.has(this.#nc)){if(this.#nv){let t=e.stamps.get(this.#nc);a>t.area&&(t.area=a,t.serialized.bitmap.close(),t.serialized.bitmap=this.#nT(!1))}}else e.stamps.set(this.#nc,{area:a,serialized:i}),i.bitmap=this.#nT(!1);return i}#rm(t){let{pageIndex:e,accessibilityData:{altText:i}}=this._initialData,s=t.pageIndex===e,r=(t.accessibilityData?.alt||"")===i;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&s&&r,isSameAltText:r}}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}}class iY{#s9;#nM=!1;#nP=null;#nI=null;#nD=null;#nR=new Map;#nk=!1;#nL=!1;#nF=!1;#nN=null;#nO=null;#nB=null;#nz=null;#m;static _initialized=!1;static #$=new Map([iM,iG,iX,iO,iK].map(t=>[t._editorType,t]));constructor({uiManager:t,pageIndex:e,div:i,structTreeLayer:s,accessibilityManager:r,annotationLayer:a,drawLayer:n,textLayer:o,viewport:l,l10n:h}){let d=[...iY.#$.values()];if(!iY._initialized)for(let e of(iY._initialized=!0,d))e.initialize(h,t);t.registerEditorTypes(d),this.#m=t,this.pageIndex=e,this.div=i,this.#s9=r,this.#nP=a,this.viewport=l,this.#nB=o,this.drawLayer=n,this._structTree=s,this.#m.addLayer(this)}get isEmpty(){return 0===this.#nR.size}get isInvisible(){return this.isEmpty&&this.#m.getMode()===h.NONE}updateToolbar(t){this.#m.updateToolbar(t)}updateMode(t=this.#m.getMode()){switch(this.#nH(),t){case h.NONE:this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),this.disableClick();return;case h.INK:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick();break;case h.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);let{classList:e}=this.div;for(let i of iY.#$.values())e.toggle(`${i._type}Editing`,t===i._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#nB?.div}setEditingState(t){this.#m.setEditingState(t)}addCommands(t){this.#m.addCommands(t)}cleanUndoStack(t){this.#m.cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#nP?.div.classList.toggle("disabled",!t)}async enable(){this.#nF=!0,this.div.tabIndex=0,this.togglePointerEvents(!0);let t=new Set;for(let e of this.#nR.values())e.enableEditing(),e.show(!0),e.annotationElementId&&(this.#m.removeChangedExistingAnnotation(e),t.add(e.annotationElementId));if(!this.#nP){this.#nF=!1;return}for(let e of this.#nP.getEditableAnnotations()){if(e.hide(),this.#m.isDeletedAnnotationElement(e.data.id)||t.has(e.data.id))continue;let i=await this.deserialize(e);i&&(this.addOrRebuild(i),i.enableEditing())}this.#nF=!1}disable(){this.#nL=!0,this.div.tabIndex=-1,this.togglePointerEvents(!1);let t=new Map,e=new Map;for(let i of this.#nR.values())if(i.disableEditing(),i.annotationElementId){if(null!==i.serialize()){t.set(i.annotationElementId,i);continue}e.set(i.annotationElementId,i),this.getEditableAnnotation(i.annotationElementId)?.show(),i.remove()}if(this.#nP)for(let i of this.#nP.getEditableAnnotations()){let{id:s}=i.data;if(this.#m.isDeletedAnnotationElement(s))continue;let r=e.get(s);if(r){r.resetAnnotationElement(i),r.show(!1),i.show();continue}(r=t.get(s))&&(this.#m.addChangedExistingAnnotation(r),r.renderAnnotationElement(i)&&r.show(!1)),i.show()}this.#nH(),this.isEmpty&&(this.div.hidden=!0);let{classList:i}=this.div;for(let t of iY.#$.values())i.remove(`${t._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),this.#nL=!1}getEditableAnnotation(t){return this.#nP?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#m.getActive()!==t&&this.#m.setActiveEditor(t)}enableTextSelection(){if(this.div.tabIndex=-1,this.#nB?.div&&!this.#nz){this.#nz=new AbortController;let t=this.#m.combinedSignal(this.#nz);this.#nB.div.addEventListener("pointerdown",this.#nU.bind(this),{signal:t}),this.#nB.div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0,this.#nB?.div&&this.#nz&&(this.#nz.abort(),this.#nz=null,this.#nB.div.classList.remove("highlighting"))}#nU(t){this.#m.unselectAll();let{target:e}=t;if(e===this.#nB.div||("img"===e.getAttribute("role")||e.classList.contains("endOfContent"))&&this.#nB.div.contains(e)){let{isMac:e}=F.platform;if(0!==t.button||t.ctrlKey&&e)return;this.#m.showAllEditors("highlight",!0,!0),this.#nB.div.classList.add("free"),this.toggleDrawing(),iO.startHighlighting(this,"ltr"===this.#m.direction,{target:this.#nB.div,x:t.x,y:t.y}),this.#nB.div.addEventListener("pointerup",()=>{this.#nB.div.classList.remove("free"),this.toggleDrawing(!0)},{once:!0,signal:this.#m._signal}),t.preventDefault()}}enableClick(){if(this.#nI)return;this.#nI=new AbortController;let t=this.#m.combinedSignal(this.#nI);this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});let e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t}),this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){this.#nI?.abort(),this.#nI=null}attach(t){this.#nR.set(t.id,t);let{annotationElementId:e}=t;e&&this.#m.isDeletedAnnotationElement(e)&&this.#m.removeDeletedAnnotationElement(t)}detach(t){this.#nR.delete(t.id),this.#s9?.removePointerInTextLayer(t.contentDiv),!this.#nL&&t.annotationElementId&&this.#m.addDeletedAnnotationElement(t)}remove(t){this.detach(t),this.#m.removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1}changeParent(t){t.parent!==this&&(t.parent&&t.annotationElementId&&(this.#m.addDeletedAnnotationElement(t.annotationElementId),tv.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),t.parent?.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(t.parent!==this||!t.isAttachedToDOM){if(this.changeParent(t),this.#m.addEditor(t),this.attach(t),!t.isAttachedToDOM){let e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(!this.#nF),this.#m.addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;let{activeElement:e}=document;t.div.contains(e)&&!this.#nD&&(t._focusEventsAllowed=!1,this.#nD=setTimeout(()=>{this.#nD=null,t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this.#m._signal}),e.focus())},0)),t._structTreeParentId=this.#s9?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||=this,t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#m.getId()}get #n$(){return iY.#$.get(this.#m.getMode())}combinedSignal(t){return this.#m.combinedSignal(t)}#nG(t){let e=this.#n$;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#n$?.canCreateNewEmptyEditor()}async pasteEditor(t,e){this.updateToolbar(t),await this.#m.updateMode(t.mode);let{offsetX:i,offsetY:s}=this.#nj(),r=this.getNextId(),a=this.#nG({parent:this,id:r,x:i,y:s,uiManager:this.#m,isCentered:!0,...e});a&&this.add(a)}async deserialize(t){return await iY.#$.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#m)||null}createAndAddNewEditor(t,e,i={}){let s=this.getNextId(),r=this.#nG({parent:this,id:s,x:t.offsetX,y:t.offsetY,uiManager:this.#m,isCentered:e,...i});return r&&this.add(r),r}#nj(){let{x:t,y:e,width:i,height:s}=this.div.getBoundingClientRect(),r=Math.max(0,t),a=Math.max(0,e),n=Math.min(window.innerWidth,t+i),o=Math.min(window.innerHeight,e+s),l=(r+n)/2-t,h=(a+o)/2-e,[d,c]=this.viewport.rotation%180==0?[l,h]:[h,l];return{offsetX:d,offsetY:c}}addNewEditor(t={}){this.createAndAddNewEditor(this.#nj(),!0,t)}setSelected(t){this.#m.setSelected(t)}toggleSelected(t){this.#m.toggleSelected(t)}unselect(t){this.#m.unselect(t)}pointerup(t){let{isMac:e}=F.platform;if(0!==t.button||t.ctrlKey&&e||t.target!==this.div||!this.#nk||(this.#nk=!1,this.#n$?.isDrawer&&this.#n$.supportMultipleDrawings))return;if(!this.#nM){this.#nM=!0;return}let i=this.#m.getMode();if(i===h.STAMP||i===h.SIGNATURE)return void this.#m.unselectAll();this.createAndAddNewEditor(t,!1)}pointerdown(t){if(this.#m.getMode()===h.HIGHLIGHT&&this.enableTextSelection(),this.#nk){this.#nk=!1;return}let{isMac:e}=F.platform;if(0!==t.button||t.ctrlKey&&e||t.target!==this.div)return;if(this.#nk=!0,this.#n$?.isDrawer)return void this.startDrawingSession(t);let i=this.#m.getActive();this.#nM=!i||i.isEmpty()}startDrawingSession(t){if(this.div.focus({preventScroll:!0}),this.#nN)return void this.#n$.startDrawing(this,this.#m,!1,t);this.#m.setCurrentDrawingSession(this),this.#nN=new AbortController;let e=this.#m.combinedSignal(this.#nN);this.div.addEventListener("blur",({relatedTarget:t})=>{t&&!this.div.contains(t)&&(this.#nO=null,this.commitOrRemove())},{signal:e}),this.#n$.startDrawing(this,this.#m,!1,t)}pause(t){if(t){let{activeElement:t}=document;this.div.contains(t)&&(this.#nO=t);return}this.#nO&&setTimeout(()=>{this.#nO?.focus(),this.#nO=null},0)}endDrawingSession(t=!1){return this.#nN?(this.#m.setCurrentDrawingSession(null),this.#nN.abort(),this.#nN=null,this.#nO=null,this.#n$.endDrawing(t)):null}findNewParent(t,e,i){let s=this.#m.findParent(e,i);return null!==s&&s!==this&&(s.changeParent(t),!0)}commitOrRemove(){return!!this.#nN&&(this.endDrawingSession(),!0)}onScaleChanging(){this.#nN&&this.#n$.onScaleChangingWhenDrawing(this)}destroy(){for(let t of(this.commitOrRemove(),this.#m.getActive()?.parent===this&&(this.#m.commitOrRemove(),this.#m.setActiveEditor(null)),this.#nD&&(clearTimeout(this.#nD),this.#nD=null),this.#nR.values()))this.#s9?.removePointerInTextLayer(t.contentDiv),t.setParent(null),t.isAttachedToDOM=!1,t.div.remove();this.div=null,this.#nR.clear(),this.#m.removeLayer(this)}#nH(){for(let t of this.#nR.values())t.isEmpty()&&t.remove()}render({viewport:t}){for(let e of(this.viewport=t,ta(this.div,t),this.#m.getEditors(this.pageIndex)))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){this.#m.commitOrRemove(),this.#nH();let e=this.viewport.rotation,i=t.rotation;if(this.viewport=t,ta(this.div,{rotation:i}),e!==i)for(let t of this.#nR.values())t.rotate(i)}get pageDimensions(){let{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#m.viewParameters.realScale}}class iQ{#sU=null;#nV=new Map;#nW=new Map;static #y=0;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(!this.#sU){this.#sU=t;return}if(this.#sU!==t){if(this.#nV.size>0)for(let e of this.#nV.values())e.remove(),t.append(e);this.#sU=t}}static get _svgFactory(){return S(this,"_svgFactory",new e5)}static #nq(t,[e,i,s,r]){let{style:a}=t;a.top=`${100*i}%`,a.left=`${100*e}%`,a.width=`${100*s}%`,a.height=`${100*r}%`}#nK(){let t=iQ._svgFactory.create(1,1,!0);return this.#sU.append(t),t.setAttribute("aria-hidden",!0),t}#nX(t,e){let i=iQ._svgFactory.createElement("clipPath");t.append(i);let s=`clip_${e}`;i.setAttribute("id",s),i.setAttribute("clipPathUnits","objectBoundingBox");let r=iQ._svgFactory.createElement("use");return i.append(r),r.setAttribute("href",`#${e}`),r.classList.add("clip"),s}#nY(t,e){for(let[i,s]of Object.entries(e))null===s?t.removeAttribute(i):t.setAttribute(i,s)}draw(t,e=!1,i=!1){let s=iQ.#y++,r=this.#nK(),a=iQ._svgFactory.createElement("defs");r.append(a);let n=iQ._svgFactory.createElement("path");a.append(n);let o=`path_p${this.pageIndex}_${s}`;n.setAttribute("id",o),n.setAttribute("vector-effect","non-scaling-stroke"),e&&this.#nW.set(s,n);let l=i?this.#nX(a,o):null,h=iQ._svgFactory.createElement("use");return r.append(h),h.setAttribute("href",`#${o}`),this.updateProperties(r,t),this.#nV.set(s,r),{id:s,clipPathId:`url(#${l})`}}drawOutline(t,e){let i,s=iQ.#y++,r=this.#nK(),a=iQ._svgFactory.createElement("defs");r.append(a);let n=iQ._svgFactory.createElement("path");a.append(n);let o=`path_p${this.pageIndex}_${s}`;if(n.setAttribute("id",o),n.setAttribute("vector-effect","non-scaling-stroke"),e){let t=iQ._svgFactory.createElement("mask");a.append(t),i=`mask_p${this.pageIndex}_${s}`,t.setAttribute("id",i),t.setAttribute("maskUnits","objectBoundingBox");let e=iQ._svgFactory.createElement("rect");t.append(e),e.setAttribute("width","1"),e.setAttribute("height","1"),e.setAttribute("fill","white");let r=iQ._svgFactory.createElement("use");t.append(r),r.setAttribute("href",`#${o}`),r.setAttribute("stroke","none"),r.setAttribute("fill","black"),r.setAttribute("fill-rule","nonzero"),r.classList.add("mask")}let l=iQ._svgFactory.createElement("use");r.append(l),l.setAttribute("href",`#${o}`),i&&l.setAttribute("mask",`url(#${i})`);let h=l.cloneNode();return r.append(h),l.classList.add("mainOutline"),h.classList.add("secondaryOutline"),this.updateProperties(r,t),this.#nV.set(s,r),s}finalizeDraw(t,e){this.#nW.delete(t),this.updateProperties(t,e)}updateProperties(t,e){if(!e)return;let{root:i,bbox:s,rootClass:r,path:a}=e,n="number"==typeof t?this.#nV.get(t):t;if(n){if(i&&this.#nY(n,i),s&&iQ.#nq(n,s),r){let{classList:t}=n;for(let[e,i]of Object.entries(r))t.toggle(e,i)}if(a){let t=n.firstChild.firstChild;this.#nY(t,a)}}}updateParent(t,e){if(e===this)return;let i=this.#nV.get(t);i&&(e.#sU.append(i),this.#nV.delete(t),e.#nV.set(t,i))}remove(t){this.#nW.delete(t),null!==this.#sU&&(this.#nV.get(t).remove(),this.#nV.delete(t))}destroy(){for(let t of(this.#sU=null,this.#nV.values()))t.remove();this.#nV.clear(),this.#nW.clear()}}globalThis._pdfjsTestingUtils={HighlightOutliner:iR},globalThis.pdfjsLib={AbortException:R,AnnotationEditorLayer:iY,AnnotationEditorParamsType:d,AnnotationEditorType:h,AnnotationEditorUIManager:tm,AnnotationLayer:iC,AnnotationMode:l,AnnotationType:p,build:"47ad820d9",ColorPicker:iN,createValidAbsoluteUrl:x,DOMSVGFactory:e5,DrawLayer:iQ,FeatureTest:F,fetchData:W,getDocument:ej,getFilenameFromUrl:function(t){return[t]=t.split(/[#?]/,1),t.substring(t.lastIndexOf("/")+1)},getPdfFilenameFromUrl:function(t,e="document.pdf"){if("string"!=typeof t)return e;if(X(t))return y('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),e;let i=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,s=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t),r=i.exec(s[1])||i.exec(s[2])||i.exec(s[3]);if(r&&(r=r[0]).includes("%"))try{r=i.exec(decodeURIComponent(r))[0]}catch{}return r||e},getUuid:H,getXfaPageViewport:function(t,{scale:e=1,rotation:i=0}){let{width:s,height:r}=t.attributes.style;return new q({viewBox:[0,0,parseInt(s),parseInt(r)],userUnit:1,scale:e,rotation:i})},GlobalWorkerOptions:ep,ImageKind:u,InvalidPDFException:P,isDataScheme:X,isPdfFile:Y,isValidExplicitDest:tP,MathClamp:$,noContextMenu:Z,normalizeUnicode:function(t){return B||(B=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,z=new Map([["ﬅ","ſt"]])),t.replaceAll(B,(t,e,i)=>e?e.normalize("NFKC"):z.get(i))},OPS:m,OutputScale:tn,PasswordResponses:{NEED_PASSWORD:1,INCORRECT_PASSWORD:2},PDFDataRangeTransport:eW,PDFDateString:te,PDFWorker:eX,PermissionFlag:{PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},PixelsPerInch:V,RenderingCancelledException:K,ResponseException:I,setLayerDimensions:ta,shadow:S,SignatureExtractor:iV,stopEvent:tt,SupportedImageMimeTypes:to,TextLayer:e$,TouchManager:tA,updateUrlHash:E,Util:O,VerbosityLevel:f,version:eZ,XfaLayer:e6}}}]);
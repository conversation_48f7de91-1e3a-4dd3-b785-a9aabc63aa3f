{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/DocumentContext.js"], "sourcesContent": ["'use client';\nimport { createContext } from 'react';\nconst documentContext = createContext(null);\nexport default documentContext;\n"], "names": [], "mappings": ";;;AACA;AADA;;AAEA,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;uCACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/Message.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function Message({ children, type }) {\n    return _jsx(\"div\", { className: `react-pdf__message react-pdf__message--${type}`, children: children });\n}\n"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,QAAQ,KAAkB;QAAlB,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAlB;IAC5B,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAAE,WAAW,AAAC,0CAA8C,OAAL;QAAQ,UAAU;IAAS;AACzG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/LinkService.js"], "sourcesContent": ["/* Copyright 2015 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport invariant from 'tiny-invariant';\nconst DEFAULT_LINK_REL = 'noopener noreferrer nofollow';\nexport default class LinkService {\n    constructor() {\n        this.externalLinkEnabled = true;\n        this.externalLinkRel = undefined;\n        this.externalLinkTarget = undefined;\n        this.isInPresentationMode = false;\n        this.pdfDocument = undefined;\n        this.pdfViewer = undefined;\n    }\n    setDocument(pdfDocument) {\n        this.pdfDocument = pdfDocument;\n    }\n    setViewer(pdfViewer) {\n        this.pdfViewer = pdfViewer;\n    }\n    setExternalLinkRel(externalLinkRel) {\n        this.externalLinkRel = externalLinkRel;\n    }\n    setExternalLinkTarget(externalLinkTarget) {\n        this.externalLinkTarget = externalLinkTarget;\n    }\n    setHistory() {\n        // Intentionally empty\n    }\n    get pagesCount() {\n        return this.pdfDocument ? this.pdfDocument.numPages : 0;\n    }\n    get page() {\n        invariant(this.pdfViewer, 'PDF viewer is not initialized.');\n        return this.pdfViewer.currentPageNumber || 0;\n    }\n    set page(value) {\n        invariant(this.pdfViewer, 'PDF viewer is not initialized.');\n        this.pdfViewer.currentPageNumber = value;\n    }\n    get rotation() {\n        return 0;\n    }\n    set rotation(_value) {\n        // Intentionally empty\n    }\n    goToDestination(dest) {\n        return new Promise((resolve) => {\n            invariant(this.pdfDocument, 'PDF document not loaded.');\n            invariant(dest, 'Destination is not specified.');\n            if (typeof dest === 'string') {\n                this.pdfDocument.getDestination(dest).then(resolve);\n            }\n            else if (Array.isArray(dest)) {\n                resolve(dest);\n            }\n            else {\n                dest.then(resolve);\n            }\n        }).then((explicitDest) => {\n            invariant(Array.isArray(explicitDest), `\"${explicitDest}\" is not a valid destination array.`);\n            const destRef = explicitDest[0];\n            new Promise((resolve) => {\n                invariant(this.pdfDocument, 'PDF document not loaded.');\n                if (destRef instanceof Object) {\n                    this.pdfDocument\n                        .getPageIndex(destRef)\n                        .then((pageIndex) => {\n                        resolve(pageIndex);\n                    })\n                        .catch(() => {\n                        invariant(false, `\"${destRef}\" is not a valid page reference.`);\n                    });\n                }\n                else if (typeof destRef === 'number') {\n                    resolve(destRef);\n                }\n                else {\n                    invariant(false, `\"${destRef}\" is not a valid destination reference.`);\n                }\n            }).then((pageIndex) => {\n                const pageNumber = pageIndex + 1;\n                invariant(this.pdfViewer, 'PDF viewer is not initialized.');\n                invariant(pageNumber >= 1 && pageNumber <= this.pagesCount, `\"${pageNumber}\" is not a valid page number.`);\n                this.pdfViewer.scrollPageIntoView({\n                    dest: explicitDest,\n                    pageIndex,\n                    pageNumber,\n                });\n            });\n        });\n    }\n    navigateTo(dest) {\n        this.goToDestination(dest);\n    }\n    goToPage(pageNumber) {\n        const pageIndex = pageNumber - 1;\n        invariant(this.pdfViewer, 'PDF viewer is not initialized.');\n        invariant(pageNumber >= 1 && pageNumber <= this.pagesCount, `\"${pageNumber}\" is not a valid page number.`);\n        this.pdfViewer.scrollPageIntoView({\n            pageIndex,\n            pageNumber,\n        });\n    }\n    addLinkAttributes(link, url, newWindow) {\n        link.href = url;\n        link.rel = this.externalLinkRel || DEFAULT_LINK_REL;\n        link.target = newWindow ? '_blank' : this.externalLinkTarget || '';\n    }\n    getDestinationHash() {\n        return '#';\n    }\n    getAnchorUrl() {\n        return '#';\n    }\n    setHash() {\n        // Intentionally empty\n    }\n    executeNamedAction() {\n        // Intentionally empty\n    }\n    cachePageRef() {\n        // Intentionally empty\n    }\n    isPageVisible() {\n        return true;\n    }\n    isPageCached() {\n        return true;\n    }\n    executeSetOCGState() {\n        // Intentionally empty\n    }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;CAaC;;;AACD;;AACA,MAAM,mBAAmB;AACV,MAAM;IASjB,YAAY,WAAW,EAAE;QACrB,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,UAAU,SAAS,EAAE;QACjB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,CAAC,eAAe,GAAG;IAC3B;IACA,sBAAsB,kBAAkB,EAAE;QACtC,IAAI,CAAC,kBAAkB,GAAG;IAC9B;IACA,aAAa;IACT,sBAAsB;IAC1B;IACA,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG;IAC1D;IACA,IAAI,OAAO;QACP,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,IAAI;IAC/C;IACA,IAAI,KAAK,KAAK,EAAE;QACZ,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE;QAC1B,IAAI,CAAC,SAAS,CAAC,iBAAiB,GAAG;IACvC;IACA,IAAI,WAAW;QACX,OAAO;IACX;IACA,IAAI,SAAS,MAAM,EAAE;IACjB,sBAAsB;IAC1B;IACA,gBAAgB,IAAI,EAAE;QAClB,OAAO,IAAI,QAAQ,CAAC;YAChB,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,MAAM;YAChB,IAAI,OAAO,SAAS,UAAU;gBAC1B,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC;YAC/C,OACK,IAAI,MAAM,OAAO,CAAC,OAAO;gBAC1B,QAAQ;YACZ,OACK;gBACD,KAAK,IAAI,CAAC;YACd;QACJ,GAAG,IAAI,CAAC,CAAC;YACL,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,MAAM,OAAO,CAAC,eAAe,AAAC,IAAgB,OAAb,cAAa;YACxD,MAAM,UAAU,YAAY,CAAC,EAAE;YAC/B,IAAI,QAAQ,CAAC;gBACT,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE;gBAC5B,IAAI,mBAAmB,QAAQ;oBAC3B,IAAI,CAAC,WAAW,CACX,YAAY,CAAC,SACb,IAAI,CAAC,CAAC;wBACP,QAAQ;oBACZ,GACK,KAAK,CAAC;wBACP,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,OAAO,AAAC,IAAW,OAAR,SAAQ;oBACjC;gBACJ,OACK,IAAI,OAAO,YAAY,UAAU;oBAClC,QAAQ;gBACZ,OACK;oBACD,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,OAAO,AAAC,IAAW,OAAR,SAAQ;gBACjC;YACJ,GAAG,IAAI,CAAC,CAAC;gBACL,MAAM,aAAa,YAAY;gBAC/B,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE;gBAC1B,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,cAAc,KAAK,cAAc,IAAI,CAAC,UAAU,EAAE,AAAC,IAAc,OAAX,YAAW;gBAC3E,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;oBAC9B,MAAM;oBACN;oBACA;gBACJ;YACJ;QACJ;IACJ;IACA,WAAW,IAAI,EAAE;QACb,IAAI,CAAC,eAAe,CAAC;IACzB;IACA,SAAS,UAAU,EAAE;QACjB,MAAM,YAAY,aAAa;QAC/B,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE;QAC1B,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,cAAc,KAAK,cAAc,IAAI,CAAC,UAAU,EAAE,AAAC,IAAc,OAAX,YAAW;QAC3E,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;YAC9B;YACA;QACJ;IACJ;IACA,kBAAkB,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,GAAG,GAAG,IAAI,CAAC,eAAe,IAAI;QACnC,KAAK,MAAM,GAAG,YAAY,WAAW,IAAI,CAAC,kBAAkB,IAAI;IACpE;IACA,qBAAqB;QACjB,OAAO;IACX;IACA,eAAe;QACX,OAAO;IACX;IACA,UAAU;IACN,sBAAsB;IAC1B;IACA,qBAAqB;IACjB,sBAAsB;IAC1B;IACA,eAAe;IACX,sBAAsB;IAC1B;IACA,gBAAgB;QACZ,OAAO;IACX;IACA,eAAe;QACX,OAAO;IACX;IACA,qBAAqB;IACjB,sBAAsB;IAC1B;IA9HA,aAAc;QACV,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,SAAS,GAAG;IACrB;AAwHJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/PasswordResponses.js"], "sourcesContent": ["// As defined in https://github.com/mozilla/pdf.js/blob/d9fac3459609a807be6506fb3441b5da4b154d14/src/shared/util.js#L371-L374\nconst PasswordResponses = {\n    NEED_PASSWORD: 1,\n    INCORRECT_PASSWORD: 2,\n};\nexport default PasswordResponses;\n"], "names": [], "mappings": "AAAA,6HAA6H;;;;AAC7H,MAAM,oBAAoB;IACtB,eAAe;IACf,oBAAoB;AACxB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/shared/utils.js"], "sourcesContent": ["import invariant from 'tiny-invariant';\nimport warning from 'warning';\n/**\n * Checks if we're running in a browser environment.\n */\nexport const isBrowser = typeof window !== 'undefined';\n/**\n * Checks whether we're running from a local file system.\n */\nexport const isLocalFileSystem = isBrowser && window.location.protocol === 'file:';\n/**\n * Checks whether a variable is defined.\n *\n * @param {*} variable Variable to check\n */\nexport function isDefined(variable) {\n    return typeof variable !== 'undefined';\n}\n/**\n * Checks whether a variable is defined and not null.\n *\n * @param {*} variable Variable to check\n */\nexport function isProvided(variable) {\n    return isDefined(variable) && variable !== null;\n}\n/**\n * Checks whether a variable provided is a string.\n *\n * @param {*} variable Variable to check\n */\nexport function isString(variable) {\n    return typeof variable === 'string';\n}\n/**\n * Checks whether a variable provided is an ArrayBuffer.\n *\n * @param {*} variable Variable to check\n */\nexport function isArrayBuffer(variable) {\n    return variable instanceof ArrayBuffer;\n}\n/**\n * Checks whether a variable provided is a Blob.\n *\n * @param {*} variable Variable to check\n */\nexport function isBlob(variable) {\n    invariant(isBrowser, 'isBlob can only be used in a browser environment');\n    return variable instanceof Blob;\n}\n/**\n * Checks whether a variable provided is a data URI.\n *\n * @param {*} variable String to check\n */\nexport function isDataURI(variable) {\n    return isString(variable) && /^data:/.test(variable);\n}\nexport function dataURItoByteString(dataURI) {\n    invariant(isDataURI(dataURI), 'Invalid data URI.');\n    const [headersString = '', dataString = ''] = dataURI.split(',');\n    const headers = headersString.split(';');\n    if (headers.indexOf('base64') !== -1) {\n        return atob(dataString);\n    }\n    return unescape(dataString);\n}\nexport function getDevicePixelRatio() {\n    return (isBrowser && window.devicePixelRatio) || 1;\n}\nconst allowFileAccessFromFilesTip = 'On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.';\nexport function displayCORSWarning() {\n    warning(!isLocalFileSystem, `Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`);\n}\nexport function displayWorkerWarning() {\n    warning(!isLocalFileSystem, `Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ${allowFileAccessFromFilesTip}`);\n}\nexport function cancelRunningTask(runningTask) {\n    if (runningTask === null || runningTask === void 0 ? void 0 : runningTask.cancel)\n        runningTask.cancel();\n}\nexport function makePageCallback(page, scale) {\n    Object.defineProperty(page, 'width', {\n        get() {\n            return this.view[2] * scale;\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'height', {\n        get() {\n            return this.view[3] * scale;\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'originalWidth', {\n        get() {\n            return this.view[2];\n        },\n        configurable: true,\n    });\n    Object.defineProperty(page, 'originalHeight', {\n        get() {\n            return this.view[3];\n        },\n        configurable: true,\n    });\n    return page;\n}\nexport function isCancelException(error) {\n    return error.name === 'RenderingCancelledException';\n}\nexport function loadFromFile(file) {\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = () => {\n            if (!reader.result) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            resolve(reader.result);\n        };\n        reader.onerror = (event) => {\n            if (!event.target) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            const { error } = event.target;\n            if (!error) {\n                return reject(new Error('Error while reading a file.'));\n            }\n            switch (error.code) {\n                case error.NOT_FOUND_ERR:\n                    return reject(new Error('Error while reading a file: File not found.'));\n                case error.SECURITY_ERR:\n                    return reject(new Error('Error while reading a file: Security error.'));\n                case error.ABORT_ERR:\n                    return reject(new Error('Error while reading a file: Aborted.'));\n                default:\n                    return reject(new Error('Error while reading a file.'));\n            }\n        };\n        reader.readAsArrayBuffer(file);\n    });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAIO,MAAM,YAAY,OAAO,WAAW;AAIpC,MAAM,oBAAoB,aAAa,OAAO,QAAQ,CAAC,QAAQ,KAAK;AAMpE,SAAS,UAAU,QAAQ;IAC9B,OAAO,OAAO,aAAa;AAC/B;AAMO,SAAS,WAAW,QAAQ;IAC/B,OAAO,UAAU,aAAa,aAAa;AAC/C;AAMO,SAAS,SAAS,QAAQ;IAC7B,OAAO,OAAO,aAAa;AAC/B;AAMO,SAAS,cAAc,QAAQ;IAClC,OAAO,oBAAoB;AAC/B;AAMO,SAAS,OAAO,QAAQ;IAC3B,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,WAAW;IACrB,OAAO,oBAAoB;AAC/B;AAMO,SAAS,UAAU,QAAQ;IAC9B,OAAO,SAAS,aAAa,SAAS,IAAI,CAAC;AAC/C;AACO,SAAS,oBAAoB,OAAO;IACvC,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,UAAU,UAAU;IAC9B,MAAM,CAAC,gBAAgB,EAAE,EAAE,aAAa,EAAE,CAAC,GAAG,QAAQ,KAAK,CAAC;IAC5D,MAAM,UAAU,cAAc,KAAK,CAAC;IACpC,IAAI,QAAQ,OAAO,CAAC,cAAc,CAAC,GAAG;QAClC,OAAO,KAAK;IAChB;IACA,OAAO,SAAS;AACpB;AACO,SAAS;IACZ,OAAO,AAAC,aAAa,OAAO,gBAAgB,IAAK;AACrD;AACA,MAAM,8BAA8B;AAC7B,SAAS;IACZ,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,CAAC,mBAAmB,AAAC,uFAAkH,OAA5B;AACvH;AACO,SAAS;IACZ,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,CAAC,mBAAmB,AAAC,0EAAqG,OAA5B;AAC1G;AACO,SAAS,kBAAkB,WAAW;IACzC,IAAI,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,MAAM,EAC5E,YAAY,MAAM;AAC1B;AACO,SAAS,iBAAiB,IAAI,EAAE,KAAK;IACxC,OAAO,cAAc,CAAC,MAAM,SAAS;QACjC;YACI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;QAC1B;QACA,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,MAAM,UAAU;QAClC;YACI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;QAC1B;QACA,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,MAAM,iBAAiB;QACzC;YACI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;QACvB;QACA,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,MAAM,kBAAkB;QAC1C;YACI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;QACvB;QACA,cAAc;IAClB;IACA,OAAO;AACX;AACO,SAAS,kBAAkB,KAAK;IACnC,OAAO,MAAM,IAAI,KAAK;AAC1B;AACO,SAAS,aAAa,IAAI;IAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;QACzB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG;YACZ,IAAI,CAAC,OAAO,MAAM,EAAE;gBAChB,OAAO,OAAO,IAAI,MAAM;YAC5B;YACA,QAAQ,OAAO,MAAM;QACzB;QACA,OAAO,OAAO,GAAG,CAAC;YACd,IAAI,CAAC,MAAM,MAAM,EAAE;gBACf,OAAO,OAAO,IAAI,MAAM;YAC5B;YACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM;YAC9B,IAAI,CAAC,OAAO;gBACR,OAAO,OAAO,IAAI,MAAM;YAC5B;YACA,OAAQ,MAAM,IAAI;gBACd,KAAK,MAAM,aAAa;oBACpB,OAAO,OAAO,IAAI,MAAM;gBAC5B,KAAK,MAAM,YAAY;oBACnB,OAAO,OAAO,IAAI,MAAM;gBAC5B,KAAK,MAAM,SAAS;oBAChB,OAAO,OAAO,IAAI,MAAM;gBAC5B;oBACI,OAAO,OAAO,IAAI,MAAM;YAChC;QACJ;QACA,OAAO,iBAAiB,CAAC;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js"], "sourcesContent": ["import { useReducer } from 'react';\nfunction reducer(state, action) {\n    switch (action.type) {\n        case 'RESOLVE':\n            return { value: action.value, error: undefined };\n        case 'REJECT':\n            return { value: false, error: action.error };\n        case 'RESET':\n            return { value: undefined, error: undefined };\n        default:\n            return state;\n    }\n}\nexport default function useResolver() {\n    return useReducer((reducer), { value: undefined, error: undefined });\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,KAAK,EAAE,MAAM;IAC1B,OAAQ,OAAO,IAAI;QACf,KAAK;YACD,OAAO;gBAAE,OAAO,OAAO,KAAK;gBAAE,OAAO;YAAU;QACnD,KAAK;YACD,OAAO;gBAAE,OAAO;gBAAO,OAAO,OAAO,KAAK;YAAC;QAC/C,KAAK;YACD,OAAO;gBAAE,OAAO;gBAAW,OAAO;YAAU;QAChD;YACI,OAAO;IACf;AACJ;AACe,SAAS;IACpB,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAG,SAAU;QAAE,OAAO;QAAW,OAAO;IAAU;AACtE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/Document.js"], "sourcesContent": ["'use client';\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef } from 'react';\nimport makeEventProps from 'make-event-props';\nimport makeCancellable from 'make-cancellable-promise';\nimport clsx from 'clsx';\nimport invariant from 'tiny-invariant';\nimport warning from 'warning';\nimport { dequal } from 'dequal';\nimport * as pdfjs from 'pdfjs-dist';\nimport DocumentContext from './DocumentContext.js';\nimport Message from './Message.js';\nimport LinkService from './LinkService.js';\nimport PasswordResponses from './PasswordResponses.js';\nimport { cancelRunningTask, dataURItoByteString, displayCORSWarning, isArrayBuffer, isBlob, isBrowser, isDataURI, loadFromFile, } from './shared/utils.js';\nimport useResolver from './shared/hooks/useResolver.js';\nconst { PDFDataRangeTransport } = pdfjs;\nconst defaultOnPassword = (callback, reason) => {\n    switch (reason) {\n        case PasswordResponses.NEED_PASSWORD: {\n            const password = prompt('Enter the password to open this PDF file.');\n            callback(password);\n            break;\n        }\n        case PasswordResponses.INCORRECT_PASSWORD: {\n            const password = prompt('Invalid password. Please try again.');\n            callback(password);\n            break;\n        }\n        default:\n    }\n};\nfunction isParameterObject(file) {\n    return (typeof file === 'object' &&\n        file !== null &&\n        ('data' in file || 'range' in file || 'url' in file));\n}\n/**\n * Loads a document passed using `file` prop.\n */\nconst Document = forwardRef(function Document(_a, ref) {\n    var { children, className, error = 'Failed to load PDF file.', externalLinkRel, externalLinkTarget, file, inputRef, imageResourcesPath, loading = 'Loading PDF…', noData = 'No PDF file specified.', onItemClick, onLoadError: onLoadErrorProps, onLoadProgress, onLoadSuccess: onLoadSuccessProps, onPassword = defaultOnPassword, onSourceError: onSourceErrorProps, onSourceSuccess: onSourceSuccessProps, options, renderMode, rotate } = _a, otherProps = __rest(_a, [\"children\", \"className\", \"error\", \"externalLinkRel\", \"externalLinkTarget\", \"file\", \"inputRef\", \"imageResourcesPath\", \"loading\", \"noData\", \"onItemClick\", \"onLoadError\", \"onLoadProgress\", \"onLoadSuccess\", \"onPassword\", \"onSourceError\", \"onSourceSuccess\", \"options\", \"renderMode\", \"rotate\"]);\n    const [sourceState, sourceDispatch] = useResolver();\n    const { value: source, error: sourceError } = sourceState;\n    const [pdfState, pdfDispatch] = useResolver();\n    const { value: pdf, error: pdfError } = pdfState;\n    const linkService = useRef(new LinkService());\n    const pages = useRef([]);\n    const prevFile = useRef(undefined);\n    const prevOptions = useRef(undefined);\n    if (file && file !== prevFile.current && isParameterObject(file)) {\n        warning(!dequal(file, prevFile.current), `File prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to \"file\" prop.`);\n        prevFile.current = file;\n    }\n    // Detect non-memoized changes in options prop\n    if (options && options !== prevOptions.current) {\n        warning(!dequal(options, prevOptions.current), `Options prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to \"options\" prop.`);\n        prevOptions.current = options;\n    }\n    const viewer = useRef({\n        // Handling jumping to internal links target\n        scrollPageIntoView: (args) => {\n            const { dest, pageNumber, pageIndex = pageNumber - 1 } = args;\n            // First, check if custom handling of onItemClick was provided\n            if (onItemClick) {\n                onItemClick({ dest, pageIndex, pageNumber });\n                return;\n            }\n            // If not, try to look for target page within the <Document>.\n            const page = pages.current[pageIndex];\n            if (page) {\n                // Scroll to the page automatically\n                page.scrollIntoView();\n                return;\n            }\n            warning(false, `An internal link leading to page ${pageNumber} was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>.`);\n        },\n    });\n    useImperativeHandle(ref, () => ({\n        linkService,\n        pages,\n        viewer,\n    }), []);\n    /**\n     * Called when a document source is resolved correctly\n     */\n    function onSourceSuccess() {\n        if (onSourceSuccessProps) {\n            onSourceSuccessProps();\n        }\n    }\n    /**\n     * Called when a document source failed to be resolved correctly\n     */\n    function onSourceError() {\n        if (!sourceError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning(false, sourceError.toString());\n        if (onSourceErrorProps) {\n            onSourceErrorProps(sourceError);\n        }\n    }\n    function resetSource() {\n        sourceDispatch({ type: 'RESET' });\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: See https://github.com/biomejs/biome/issues/3080\n    useEffect(resetSource, [file, sourceDispatch]);\n    const findDocumentSource = useCallback(() => __awaiter(this, void 0, void 0, function* () {\n        if (!file) {\n            return null;\n        }\n        // File is a string\n        if (typeof file === 'string') {\n            if (isDataURI(file)) {\n                const fileByteString = dataURItoByteString(file);\n                return { data: fileByteString };\n            }\n            displayCORSWarning();\n            return { url: file };\n        }\n        // File is PDFDataRangeTransport\n        if (file instanceof PDFDataRangeTransport) {\n            return { range: file };\n        }\n        // File is an ArrayBuffer\n        if (isArrayBuffer(file)) {\n            return { data: file };\n        }\n        /**\n         * The cases below are browser-only.\n         * If you're running on a non-browser environment, these cases will be of no use.\n         */\n        if (isBrowser) {\n            // File is a Blob\n            if (isBlob(file)) {\n                const data = yield loadFromFile(file);\n                return { data };\n            }\n        }\n        // At this point, file must be an object\n        invariant(typeof file === 'object', 'Invalid parameter in file, need either Uint8Array, string or a parameter object');\n        invariant(isParameterObject(file), 'Invalid parameter object: need either .data, .range or .url');\n        // File .url is a string\n        if ('url' in file && typeof file.url === 'string') {\n            if (isDataURI(file.url)) {\n                const { url } = file, otherParams = __rest(file, [\"url\"]);\n                const fileByteString = dataURItoByteString(url);\n                return Object.assign({ data: fileByteString }, otherParams);\n            }\n            displayCORSWarning();\n        }\n        return file;\n    }), [file]);\n    useEffect(() => {\n        const cancellable = makeCancellable(findDocumentSource());\n        cancellable.promise\n            .then((nextSource) => {\n            sourceDispatch({ type: 'RESOLVE', value: nextSource });\n        })\n            .catch((error) => {\n            sourceDispatch({ type: 'REJECT', error });\n        });\n        return () => {\n            cancelRunningTask(cancellable);\n        };\n    }, [findDocumentSource, sourceDispatch]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    useEffect(() => {\n        if (typeof source === 'undefined') {\n            return;\n        }\n        if (source === false) {\n            onSourceError();\n            return;\n        }\n        onSourceSuccess();\n    }, [source]);\n    /**\n     * Called when a document is read successfully\n     */\n    function onLoadSuccess() {\n        if (!pdf) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onLoadSuccessProps) {\n            onLoadSuccessProps(pdf);\n        }\n        pages.current = new Array(pdf.numPages);\n        linkService.current.setDocument(pdf);\n    }\n    /**\n     * Called when a document failed to read successfully\n     */\n    function onLoadError() {\n        if (!pdfError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning(false, pdfError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(pdfError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on source change\n    useEffect(function resetDocument() {\n        pdfDispatch({ type: 'RESET' });\n    }, [pdfDispatch, source]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    useEffect(function loadDocument() {\n        if (!source) {\n            return;\n        }\n        const documentInitParams = options\n            ? Object.assign(Object.assign({}, source), options) : source;\n        const destroyable = pdfjs.getDocument(documentInitParams);\n        if (onLoadProgress) {\n            destroyable.onProgress = onLoadProgress;\n        }\n        if (onPassword) {\n            destroyable.onPassword = onPassword;\n        }\n        const loadingTask = destroyable;\n        const loadingPromise = loadingTask.promise\n            .then((nextPdf) => {\n            pdfDispatch({ type: 'RESOLVE', value: nextPdf });\n        })\n            .catch((error) => {\n            if (loadingTask.destroyed) {\n                return;\n            }\n            pdfDispatch({ type: 'REJECT', error });\n        });\n        return () => {\n            loadingPromise.finally(() => loadingTask.destroy());\n        };\n    }, [options, pdfDispatch, source]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    useEffect(() => {\n        if (typeof pdf === 'undefined') {\n            return;\n        }\n        if (pdf === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [pdf]);\n    useEffect(function setupLinkService() {\n        linkService.current.setViewer(viewer.current);\n        linkService.current.setExternalLinkRel(externalLinkRel);\n        linkService.current.setExternalLinkTarget(externalLinkTarget);\n    }, [externalLinkRel, externalLinkTarget]);\n    const registerPage = useCallback((pageIndex, ref) => {\n        pages.current[pageIndex] = ref;\n    }, []);\n    const unregisterPage = useCallback((pageIndex) => {\n        delete pages.current[pageIndex];\n    }, []);\n    const childContext = useMemo(() => ({\n        imageResourcesPath,\n        linkService: linkService.current,\n        onItemClick,\n        pdf,\n        registerPage,\n        renderMode,\n        rotate,\n        unregisterPage,\n    }), [imageResourcesPath, onItemClick, pdf, registerPage, renderMode, rotate, unregisterPage]);\n    const eventProps = useMemo(() => makeEventProps(otherProps, () => pdf), \n    // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME\n    [otherProps, pdf]);\n    function renderChildren() {\n        return _jsx(DocumentContext.Provider, { value: childContext, children: children });\n    }\n    function renderContent() {\n        if (!file) {\n            return _jsx(Message, { type: \"no-data\", children: typeof noData === 'function' ? noData() : noData });\n        }\n        if (pdf === undefined || pdf === null) {\n            return (_jsx(Message, { type: \"loading\", children: typeof loading === 'function' ? loading() : loading }));\n        }\n        if (pdf === false) {\n            return _jsx(Message, { type: \"error\", children: typeof error === 'function' ? error() : error });\n        }\n        return renderChildren();\n    }\n    return (_jsx(\"div\", Object.assign({ className: clsx('react-pdf__Document', className), \n        // Assertion is needed for React 18 compatibility\n        ref: inputRef, style: {\n            ['--scale-factor']: '1',\n        } }, eventProps, { children: renderContent() })));\n});\nexport default Document;\n"], "names": [], "mappings": ";;;AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnCA;AACA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACnF,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AACA,IAAI,SAAS,4CAAS,yCAAK,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC;IAChD,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAC9E,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QACpE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACJ,OAAO;AACX;;;;;;;;;;;;;;;;AAgBA,MAAM,EAAE,qBAAqB,EAAE,GAAG;AAClC,MAAM,oBAAoB,CAAC,UAAU;IACjC,OAAQ;QACJ,KAAK,mKAAA,CAAA,UAAiB,CAAC,aAAa;YAAE;gBAClC,MAAM,WAAW,OAAO;gBACxB,SAAS;gBACT;YACJ;QACA,KAAK,mKAAA,CAAA,UAAiB,CAAC,kBAAkB;YAAE;gBACvC,MAAM,WAAW,OAAO;gBACxB,SAAS;gBACT;YACJ;QACA;IACJ;AACJ;AACA,SAAS,kBAAkB,IAAI;IAC3B,OAAQ,OAAO,SAAS,YACpB,SAAS,QACT,CAAC,UAAU,QAAQ,WAAW,QAAQ,SAAS,IAAI;AAC3D;AACA;;CAEC,GACD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS,EAAE,EAAE,GAAG;IACjD,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,0BAA0B,EAAE,eAAe,EAAE,kBAAkB,EAAE,IAAI,EAAE,QAAQ,EAAE,kBAAkB,EAAE,UAAU,cAAc,EAAE,SAAS,wBAAwB,EAAE,WAAW,EAAE,aAAa,gBAAgB,EAAE,cAAc,EAAE,eAAe,kBAAkB,EAAE,aAAa,iBAAiB,EAAE,eAAe,kBAAkB,EAAE,iBAAiB,oBAAoB,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,IAAI,aAAa,OAAO,IAAI;QAAC;QAAY;QAAa;QAAS;QAAmB;QAAsB;QAAQ;QAAY;QAAsB;QAAW;QAAU;QAAe;QAAe;QAAkB;QAAiB;QAAc;QAAiB;QAAmB;QAAW;QAAc;KAAS;IAC1uB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,gLAAA,CAAA,UAAW,AAAD;IAChD,MAAM,EAAE,OAAO,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,gLAAA,CAAA,UAAW,AAAD;IAC1C,MAAM,EAAE,OAAO,GAAG,EAAE,OAAO,QAAQ,EAAE,GAAG;IACxC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,IAAI,6JAAA,CAAA,UAAW;IAC1C,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,IAAI,QAAQ,SAAS,SAAS,OAAO,IAAI,kBAAkB,OAAO;QAC9D,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,CAAC,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAE,MAAM,SAAS,OAAO,GAAI;QAC1C,SAAS,OAAO,GAAG;IACvB;IACA,8CAA8C;IAC9C,IAAI,WAAW,YAAY,YAAY,OAAO,EAAE;QAC5C,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,CAAC,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAE,SAAS,YAAY,OAAO,GAAI;QAChD,YAAY,OAAO,GAAG;IAC1B;IACA,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAClB,4CAA4C;QAC5C,kBAAkB;gDAAE,CAAC;gBACjB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,aAAa,CAAC,EAAE,GAAG;gBACzD,8DAA8D;gBAC9D,IAAI,aAAa;oBACb,YAAY;wBAAE;wBAAM;wBAAW;oBAAW;oBAC1C;gBACJ;gBACA,6DAA6D;gBAC7D,MAAM,OAAO,MAAM,OAAO,CAAC,UAAU;gBACrC,IAAI,MAAM;oBACN,mCAAmC;oBACnC,KAAK,cAAc;oBACnB;gBACJ;gBACA,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,AAAC,oCAA8C,OAAX,YAAW;YAClE;;IACJ;IACA,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;iDAAK,IAAM,CAAC;gBAC5B;gBACA;gBACA;YACJ,CAAC;gDAAG,EAAE;IACN;;KAEC,GACD,SAAS;QACL,IAAI,sBAAsB;YACtB;QACJ;IACJ;IACA;;KAEC,GACD,SAAS;QACL,IAAI,CAAC,aAAa;YACd,+CAA+C;YAC/C;QACJ;QACA,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,YAAY,QAAQ;QACnC,IAAI,oBAAoB;YACpB,mBAAmB;QACvB;IACJ;IACA,SAAS;QACL,eAAe;YAAE,MAAM;QAAQ;IACnC;IACA,4GAA4G;IAC5G,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QAAC;QAAM;KAAe;IAC7C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK;qEAAG;oBACzE,IAAI,CAAC,MAAM;wBACP,OAAO;oBACX;oBACA,mBAAmB;oBACnB,IAAI,OAAO,SAAS,UAAU;wBAC1B,IAAI,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,OAAO;4BACjB,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,sBAAmB,AAAD,EAAE;4BAC3C,OAAO;gCAAE,MAAM;4BAAe;wBAClC;wBACA,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD;wBACjB,OAAO;4BAAE,KAAK;wBAAK;oBACvB;oBACA,gCAAgC;oBAChC,IAAI,gBAAgB,uBAAuB;wBACvC,OAAO;4BAAE,OAAO;wBAAK;oBACzB;oBACA,yBAAyB;oBACzB,IAAI,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;wBACrB,OAAO;4BAAE,MAAM;wBAAK;oBACxB;oBACA;;;SAGC,GACD,IAAI,iKAAA,CAAA,YAAS,EAAE;wBACX,iBAAiB;wBACjB,IAAI,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,OAAO;4BACd,MAAM,OAAO,MAAM,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;4BAChC,OAAO;gCAAE;4BAAK;wBAClB;oBACJ;oBACA,wCAAwC;oBACxC,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,OAAO,SAAS,UAAU;oBACpC,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,kBAAkB,OAAO;oBACnC,wBAAwB;oBACxB,IAAI,SAAS,QAAQ,OAAO,KAAK,GAAG,KAAK,UAAU;wBAC/C,IAAI,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,GAAG,GAAG;4BACrB,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,cAAc,OAAO,MAAM;gCAAC;6BAAM;4BACxD,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,sBAAmB,AAAD,EAAE;4BAC3C,OAAO,OAAO,MAAM,CAAC;gCAAE,MAAM;4BAAe,GAAG;wBACnD;wBACA,CAAA,GAAA,iKAAA,CAAA,qBAAkB,AAAD;oBACrB;oBACA,OAAO;gBACX;;4DAAI;QAAC;KAAK;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACN,MAAM,cAAc,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE;YACpC,YAAY,OAAO,CACd,IAAI;+CAAC,CAAC;oBACP,eAAe;wBAAE,MAAM;wBAAW,OAAO;oBAAW;gBACxD;8CACK,KAAK;+CAAC,CAAC;oBACR,eAAe;wBAAE,MAAM;wBAAU;oBAAM;gBAC3C;;YACA;+CAAO;oBACH,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE;gBACtB;;QACJ;sCAAG;QAAC;QAAoB;KAAe;IACvC,4HAA4H;IAC5H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACN,IAAI,OAAO,WAAW,aAAa;gBAC/B;YACJ;YACA,IAAI,WAAW,OAAO;gBAClB;gBACA;YACJ;YACA;QACJ;sCAAG;QAAC;KAAO;IACX;;KAEC,GACD,SAAS;QACL,IAAI,CAAC,KAAK;YACN,+CAA+C;YAC/C;QACJ;QACA,IAAI,oBAAoB;YACpB,mBAAmB;QACvB;QACA,MAAM,OAAO,GAAG,IAAI,MAAM,IAAI,QAAQ;QACtC,YAAY,OAAO,CAAC,WAAW,CAAC;IACpC;IACA;;KAEC,GACD,SAAS;QACL,IAAI,CAAC,UAAU;YACX,+CAA+C;YAC/C;QACJ;QACA,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,SAAS,QAAQ;QAChC,IAAI,kBAAkB;YAClB,iBAAiB;QACrB;IACJ;IACA,8GAA8G;IAC9G,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,YAAY;YAAE,MAAM;QAAQ;IAChC,GAAG;QAAC;QAAa;KAAO;IACxB,4HAA4H;IAC5H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,MAAM,qBAAqB,UACrB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,WAAW;QAC1D,MAAM,cAAc,kJAAM,WAAW,CAAC;QACtC,IAAI,gBAAgB;YAChB,YAAY,UAAU,GAAG;QAC7B;QACA,IAAI,YAAY;YACZ,YAAY,UAAU,GAAG;QAC7B;QACA,MAAM,cAAc;QACpB,MAAM,iBAAiB,YAAY,OAAO,CACrC,IAAI;uEAAC,CAAC;gBACP,YAAY;oBAAE,MAAM;oBAAW,OAAO;gBAAQ;YAClD;sEACK,KAAK;uEAAC,CAAC;gBACR,IAAI,YAAY,SAAS,EAAE;oBACvB;gBACJ;gBACA,YAAY;oBAAE,MAAM;oBAAU;gBAAM;YACxC;;QACA;wDAAO;gBACH,eAAe,OAAO;gEAAC,IAAM,YAAY,OAAO;;YACpD;;IACJ,GAAG;QAAC;QAAS;QAAa;KAAO;IACjC,4HAA4H;IAC5H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACN,IAAI,OAAO,QAAQ,aAAa;gBAC5B;YACJ;YACA,IAAI,QAAQ,OAAO;gBACf;gBACA;YACJ;YACA;QACJ;sCAAG;QAAC;KAAI;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,YAAY,OAAO,CAAC,SAAS,CAAC,OAAO,OAAO;QAC5C,YAAY,OAAO,CAAC,kBAAkB,CAAC;QACvC,YAAY,OAAO,CAAC,qBAAqB,CAAC;IAC9C,GAAG;QAAC;QAAiB;KAAmB;IACxC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,WAAW;YACzC,MAAM,OAAO,CAAC,UAAU,GAAG;QAC/B;sDAAG,EAAE;IACL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YAChC,OAAO,MAAM,OAAO,CAAC,UAAU;QACnC;wDAAG,EAAE;IACL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM,CAAC;gBAChC;gBACA,aAAa,YAAY,OAAO;gBAChC;gBACA;gBACA;gBACA;gBACA;gBACA;YACJ,CAAC;kDAAG;QAAC;QAAoB;QAAa;QAAK;QAAc;QAAY;QAAQ;KAAe;IAC5F,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE,IAAM,CAAA,GAAA,iKAAA,CAAA,UAAc,AAAD,EAAE;yDAAY,IAAM;;gDAClE,iEAAiE;IACjE;QAAC;QAAY;KAAI;IACjB,SAAS;QACL,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,iKAAA,CAAA,UAAe,CAAC,QAAQ,EAAE;YAAE,OAAO;YAAc,UAAU;QAAS;IACpF;IACA,SAAS;QACL,IAAI,CAAC,MAAM;YACP,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,yJAAA,CAAA,UAAO,EAAE;gBAAE,MAAM;gBAAW,UAAU,OAAO,WAAW,aAAa,WAAW;YAAO;QACvG;QACA,IAAI,QAAQ,aAAa,QAAQ,MAAM;YACnC,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,yJAAA,CAAA,UAAO,EAAE;gBAAE,MAAM;gBAAW,UAAU,OAAO,YAAY,aAAa,YAAY;YAAQ;QAC3G;QACA,IAAI,QAAQ,OAAO;YACf,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,yJAAA,CAAA,UAAO,EAAE;gBAAE,MAAM;gBAAS,UAAU,OAAO,UAAU,aAAa,UAAU;YAAM;QAClG;QACA,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAAE,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,uBAAuB;QACvE,iDAAiD;QACjD,KAAK;QAAU,OAAO;YAClB,CAAC,iBAAiB,EAAE;QACxB;IAAE,GAAG,YAAY;QAAE,UAAU;IAAgB;AACrD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/PageContext.js"], "sourcesContent": ["'use client';\nimport { createContext } from 'react';\nconst pageContext = createContext(null);\nexport default pageContext;\n"], "names": [], "mappings": ";;;AACA;AADA;;AAEA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;uCACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/shared/constants.js"], "sourcesContent": ["// From pdfjs-dist/lib/web/struct_tree_layer_builder.js\nexport const PDF_ROLE_TO_HTML_ROLE = {\n    // Document level structure types\n    Document: null, // There's a \"document\" role, but it doesn't make sense here.\n    DocumentFragment: null,\n    // Grouping level structure types\n    Part: 'group',\n    Sect: 'group', // XXX: There's a \"section\" role, but it's abstract.\n    Div: 'group',\n    Aside: 'note',\n    NonStruct: 'none',\n    // Block level structure types\n    P: null,\n    // H<n>,\n    H: 'heading',\n    Title: null,\n    FENote: 'note',\n    // Sub-block level structure type\n    Sub: 'group',\n    // General inline level structure types\n    Lbl: null,\n    Span: null,\n    Em: null,\n    Strong: null,\n    Link: 'link',\n    Annot: 'note',\n    Form: 'form',\n    // Ruby and Warichu structure types\n    Ruby: null,\n    RB: null,\n    RT: null,\n    RP: null,\n    Warichu: null,\n    WT: null,\n    WP: null,\n    // List standard structure types\n    L: 'list',\n    LI: 'listitem',\n    LBody: null,\n    // Table standard structure types\n    Table: 'table',\n    TR: 'row',\n    TH: 'columnheader',\n    TD: 'cell',\n    THead: 'columnheader',\n    TBody: null,\n    TFoot: null,\n    // Standard structure type Caption\n    Caption: null,\n    // Standard structure type Figure\n    Figure: 'figure',\n    // Standard structure type Formula\n    Formula: null,\n    // standard structure type Artifact\n    Artifact: null,\n};\nexport const HEADING_PATTERN = /^H(\\d+)$/;\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;AAChD,MAAM,wBAAwB;IACjC,iCAAiC;IACjC,UAAU;IACV,kBAAkB;IAClB,iCAAiC;IACjC,MAAM;IACN,MAAM;IACN,KAAK;IACL,OAAO;IACP,WAAW;IACX,8BAA8B;IAC9B,GAAG;IACH,QAAQ;IACR,GAAG;IACH,OAAO;IACP,QAAQ;IACR,iCAAiC;IACjC,KAAK;IACL,uCAAuC;IACvC,KAAK;IACL,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;IACN,mCAAmC;IACnC,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,gCAAgC;IAChC,GAAG;IACH,IAAI;IACJ,OAAO;IACP,iCAAiC;IACjC,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO;IACP,OAAO;IACP,kCAAkC;IAClC,SAAS;IACT,iCAAiC;IACjC,QAAQ;IACR,kCAAkC;IAClC,SAAS;IACT,mCAAmC;IACnC,UAAU;AACd;AACO,MAAM,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/shared/structTreeUtils.js"], "sourcesContent": ["import { HEADING_PATTERN, PDF_ROLE_TO_HTML_ROLE } from './constants.js';\nexport function isPdfRole(role) {\n    return role in PDF_ROLE_TO_HTML_ROLE;\n}\nexport function isStructTreeNode(node) {\n    return 'children' in node;\n}\nexport function isStructTreeNodeWithOnlyContentChild(node) {\n    if (!isStructTreeNode(node)) {\n        return false;\n    }\n    return node.children.length === 1 && 0 in node.children && 'id' in node.children[0];\n}\nexport function getRoleAttributes(node) {\n    const attributes = {};\n    if (isStructTreeNode(node)) {\n        const { role } = node;\n        const matches = role.match(HEADING_PATTERN);\n        if (matches) {\n            attributes.role = 'heading';\n            attributes['aria-level'] = Number(matches[1]);\n        }\n        else if (isPdfRole(role)) {\n            const htmlRole = PDF_ROLE_TO_HTML_ROLE[role];\n            if (htmlRole) {\n                attributes.role = htmlRole;\n            }\n        }\n    }\n    return attributes;\n}\nexport function getBaseAttributes(node) {\n    const attributes = {};\n    if (isStructTreeNode(node)) {\n        if (node.alt !== undefined) {\n            attributes['aria-label'] = node.alt;\n        }\n        if (node.lang !== undefined) {\n            attributes.lang = node.lang;\n        }\n        if (isStructTreeNodeWithOnlyContentChild(node)) {\n            const [child] = node.children;\n            if (child) {\n                const childAttributes = getBaseAttributes(child);\n                return Object.assign(Object.assign({}, attributes), childAttributes);\n            }\n        }\n    }\n    else {\n        if ('id' in node) {\n            attributes['aria-owns'] = node.id;\n        }\n    }\n    return attributes;\n}\nexport function getAttributes(node) {\n    if (!node) {\n        return null;\n    }\n    return Object.assign(Object.assign({}, getRoleAttributes(node)), getBaseAttributes(node));\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,SAAS,UAAU,IAAI;IAC1B,OAAO,QAAQ,qKAAA,CAAA,wBAAqB;AACxC;AACO,SAAS,iBAAiB,IAAI;IACjC,OAAO,cAAc;AACzB;AACO,SAAS,qCAAqC,IAAI;IACrD,IAAI,CAAC,iBAAiB,OAAO;QACzB,OAAO;IACX;IACA,OAAO,KAAK,QAAQ,CAAC,MAAM,KAAK,KAAK,KAAK,KAAK,QAAQ,IAAI,QAAQ,KAAK,QAAQ,CAAC,EAAE;AACvF;AACO,SAAS,kBAAkB,IAAI;IAClC,MAAM,aAAa,CAAC;IACpB,IAAI,iBAAiB,OAAO;QACxB,MAAM,EAAE,IAAI,EAAE,GAAG;QACjB,MAAM,UAAU,KAAK,KAAK,CAAC,qKAAA,CAAA,kBAAe;QAC1C,IAAI,SAAS;YACT,WAAW,IAAI,GAAG;YAClB,UAAU,CAAC,aAAa,GAAG,OAAO,OAAO,CAAC,EAAE;QAChD,OACK,IAAI,UAAU,OAAO;YACtB,MAAM,WAAW,qKAAA,CAAA,wBAAqB,CAAC,KAAK;YAC5C,IAAI,UAAU;gBACV,WAAW,IAAI,GAAG;YACtB;QACJ;IACJ;IACA,OAAO;AACX;AACO,SAAS,kBAAkB,IAAI;IAClC,MAAM,aAAa,CAAC;IACpB,IAAI,iBAAiB,OAAO;QACxB,IAAI,KAAK,GAAG,KAAK,WAAW;YACxB,UAAU,CAAC,aAAa,GAAG,KAAK,GAAG;QACvC;QACA,IAAI,KAAK,IAAI,KAAK,WAAW;YACzB,WAAW,IAAI,GAAG,KAAK,IAAI;QAC/B;QACA,IAAI,qCAAqC,OAAO;YAC5C,MAAM,CAAC,MAAM,GAAG,KAAK,QAAQ;YAC7B,IAAI,OAAO;gBACP,MAAM,kBAAkB,kBAAkB;gBAC1C,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,aAAa;YACxD;QACJ;IACJ,OACK;QACD,IAAI,QAAQ,MAAM;YACd,UAAU,CAAC,YAAY,GAAG,KAAK,EAAE;QACrC;IACJ;IACA,OAAO;AACX;AACO,SAAS,cAAc,IAAI;IAC9B,IAAI,CAAC,MAAM;QACP,OAAO;IACX;IACA,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB,QAAQ,kBAAkB;AACvF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 999, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/StructTreeItem.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useMemo } from 'react';\nimport { getAttributes, isStructTreeNode, isStructTreeNodeWithOnlyContentChild, } from './shared/structTreeUtils.js';\nexport default function StructTreeItem({ className, node, }) {\n    const attributes = useMemo(() => getAttributes(node), [node]);\n    const children = useMemo(() => {\n        if (!isStructTreeNode(node)) {\n            return null;\n        }\n        if (isStructTreeNodeWithOnlyContentChild(node)) {\n            return null;\n        }\n        return node.children.map((child, index) => {\n            return (\n            // biome-ignore lint/suspicious/noArrayIndexKey: index is stable here\n            _jsx(StructTreeItem, { node: child }, index));\n        });\n    }, [node]);\n    return (_jsx(\"span\", Object.assign({ className: className }, attributes, { children: children })));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACe,SAAS,eAAe,KAAoB;QAApB,EAAE,SAAS,EAAE,IAAI,EAAG,GAApB;IACnC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE,IAAM,CAAA,GAAA,2KAAA,CAAA,gBAAa,AAAD,EAAE;6CAAO;QAAC;KAAK;IAC5D,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACrB,IAAI,CAAC,CAAA,GAAA,2KAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;gBACzB,OAAO;YACX;YACA,IAAI,CAAA,GAAA,2KAAA,CAAA,uCAAoC,AAAD,EAAE,OAAO;gBAC5C,OAAO;YACX;YACA,OAAO,KAAK,QAAQ,CAAC,GAAG;oDAAC,CAAC,OAAO;oBAC7B,OACA,qEAAqE;oBACrE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,gBAAgB;wBAAE,MAAM;oBAAM,GAAG;gBAC1C;;QACJ;2CAAG;QAAC;KAAK;IACT,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ,OAAO,MAAM,CAAC;QAAE,WAAW;IAAU,GAAG,YAAY;QAAE,UAAU;IAAS;AAClG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js"], "sourcesContent": ["import { useContext } from 'react';\nimport PageContext from '../../PageContext.js';\nexport default function usePageContext() {\n    return useContext(PageContext);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACe,SAAS;IACpB,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,6JAAA,CAAA,UAAW;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1059, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/StructTree.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useEffect } from 'react';\nimport makeCancellable from 'make-cancellable-promise';\nimport invariant from 'tiny-invariant';\nimport warning from 'warning';\nimport StructTreeItem from './StructTreeItem.js';\nimport usePageContext from './shared/hooks/usePageContext.js';\nimport useResolver from './shared/hooks/useResolver.js';\nimport { cancelRunningTask } from './shared/utils.js';\nexport default function StructTree() {\n    const pageContext = usePageContext();\n    invariant(pageContext, 'Unable to find Page context.');\n    const { onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, } = pageContext;\n    const [structTreeState, structTreeDispatch] = useResolver();\n    const { value: structTree, error: structTreeError } = structTreeState;\n    const { customTextRenderer, page } = pageContext;\n    function onLoadSuccess() {\n        if (!structTree) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetStructTreeSuccessProps) {\n            onGetStructTreeSuccessProps(structTree);\n        }\n    }\n    function onLoadError() {\n        if (!structTreeError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning(false, structTreeError.toString());\n        if (onGetStructTreeErrorProps) {\n            onGetStructTreeErrorProps(structTreeError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    useEffect(function resetStructTree() {\n        structTreeDispatch({ type: 'RESET' });\n    }, [structTreeDispatch, page]);\n    useEffect(function loadStructTree() {\n        if (customTextRenderer) {\n            // TODO: Document why this is necessary\n            return;\n        }\n        if (!page) {\n            return;\n        }\n        const cancellable = makeCancellable(page.getStructTree());\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextStructTree) => {\n            structTreeDispatch({ type: 'RESOLVE', value: nextStructTree });\n        })\n            .catch((error) => {\n            structTreeDispatch({ type: 'REJECT', error });\n        });\n        return () => cancelRunningTask(runningTask);\n    }, [customTextRenderer, page, structTreeDispatch]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    useEffect(() => {\n        if (structTree === undefined) {\n            return;\n        }\n        if (structTree === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [structTree]);\n    if (!structTree) {\n        return null;\n    }\n    return _jsx(StructTreeItem, { className: \"react-pdf__Page__structTree structTree\", node: structTree });\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACe,SAAS;IACpB,MAAM,cAAc,CAAA,GAAA,mLAAA,CAAA,UAAc,AAAD;IACjC,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,aAAa;IACvB,MAAM,EAAE,sBAAsB,yBAAyB,EAAE,wBAAwB,2BAA2B,EAAG,GAAG;IAClH,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,gLAAA,CAAA,UAAW,AAAD;IACxD,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG;IACtD,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE,GAAG;IACrC,SAAS;QACL,IAAI,CAAC,YAAY;YACb,+CAA+C;YAC/C;QACJ;QACA,IAAI,6BAA6B;YAC7B,4BAA4B;QAChC;IACJ;IACA,SAAS;QACL,IAAI,CAAC,iBAAiB;YAClB,+CAA+C;YAC/C;QACJ;QACA,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,gBAAgB,QAAQ;QACvC,IAAI,2BAA2B;YAC3B,0BAA0B;QAC9B;IACJ;IACA,4GAA4G;IAC5G,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,mBAAmB;YAAE,MAAM;QAAQ;IACvC,GAAG;QAAC;QAAoB;KAAK;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,oBAAoB;YACpB,uCAAuC;YACvC;QACJ;QACA,IAAI,CAAC,MAAM;YACP;QACJ;QACA,MAAM,cAAc,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,KAAK,aAAa;QACtD,MAAM,cAAc;QACpB,YAAY,OAAO,CACd,IAAI;mDAAC,CAAC;gBACP,mBAAmB;oBAAE,MAAM;oBAAW,OAAO;gBAAe;YAChE;kDACK,KAAK;mDAAC,CAAC;gBACR,mBAAmB;oBAAE,MAAM;oBAAU;gBAAM;YAC/C;;QACA;mDAAO,IAAM,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE;;IACnC,GAAG;QAAC;QAAoB;QAAM;KAAmB;IACjD,4HAA4H;IAC5H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACN,IAAI,eAAe,WAAW;gBAC1B;YACJ;YACA,IAAI,eAAe,OAAO;gBACtB;gBACA;YACJ;YACA;QACJ;+BAAG;QAAC;KAAW;IACf,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,gKAAA,CAAA,UAAc,EAAE;QAAE,WAAW;QAA0C,MAAM;IAAW;AACxG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/Page/Canvas.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useCallback, useEffect, useMemo, useRef } from 'react';\nimport mergeRefs from 'merge-refs';\nimport invariant from 'tiny-invariant';\nimport warning from 'warning';\nimport * as pdfjs from 'pdfjs-dist';\nimport StructTree from '../StructTree.js';\nimport usePageContext from '../shared/hooks/usePageContext.js';\nimport { cancelRunningTask, getDevicePixelRatio, isCancelException, makePageCallback, } from '../shared/utils.js';\nconst ANNOTATION_MODE = pdfjs.AnnotationMode;\nexport default function Canvas(props) {\n    const pageContext = usePageContext();\n    invariant(pageContext, 'Unable to find Page context.');\n    const mergedProps = Object.assign(Object.assign({}, pageContext), props);\n    const { _className, canvasBackground, devicePixelRatio = getDevicePixelRatio(), onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, page, renderForms, renderTextLayer, rotate, scale, } = mergedProps;\n    const { canvasRef } = props;\n    invariant(page, 'Attempted to render page canvas, but no page was specified.');\n    const canvasElement = useRef(null);\n    /**\n     * Called when a page is rendered successfully.\n     */\n    function onRenderSuccess() {\n        if (!page) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onRenderSuccessProps) {\n            onRenderSuccessProps(makePageCallback(page, scale));\n        }\n    }\n    /**\n     * Called when a page fails to render.\n     */\n    function onRenderError(error) {\n        if (isCancelException(error)) {\n            return;\n        }\n        warning(false, error.toString());\n        if (onRenderErrorProps) {\n            onRenderErrorProps(error);\n        }\n    }\n    const renderViewport = useMemo(() => page.getViewport({ scale: scale * devicePixelRatio, rotation: rotate }), [devicePixelRatio, page, rotate, scale]);\n    const viewport = useMemo(() => page.getViewport({ scale, rotation: rotate }), [page, rotate, scale]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    useEffect(function drawPageOnCanvas() {\n        if (!page) {\n            return;\n        }\n        // Ensures the canvas will be re-rendered from scratch. Otherwise all form data will stay.\n        page.cleanup();\n        const { current: canvas } = canvasElement;\n        if (!canvas) {\n            return;\n        }\n        canvas.width = renderViewport.width;\n        canvas.height = renderViewport.height;\n        canvas.style.width = `${Math.floor(viewport.width)}px`;\n        canvas.style.height = `${Math.floor(viewport.height)}px`;\n        canvas.style.visibility = 'hidden';\n        const renderContext = {\n            annotationMode: renderForms ? ANNOTATION_MODE.ENABLE_FORMS : ANNOTATION_MODE.ENABLE,\n            canvasContext: canvas.getContext('2d', { alpha: false }),\n            viewport: renderViewport,\n        };\n        if (canvasBackground) {\n            renderContext.background = canvasBackground;\n        }\n        const cancellable = page.render(renderContext);\n        const runningTask = cancellable;\n        cancellable.promise\n            .then(() => {\n            canvas.style.visibility = '';\n            onRenderSuccess();\n        })\n            .catch(onRenderError);\n        return () => cancelRunningTask(runningTask);\n    }, [canvasBackground, page, renderForms, renderViewport, viewport]);\n    const cleanup = useCallback(() => {\n        const { current: canvas } = canvasElement;\n        /**\n         * Zeroing the width and height cause most browsers to release graphics\n         * resources immediately, which can greatly reduce memory consumption.\n         */\n        if (canvas) {\n            canvas.width = 0;\n            canvas.height = 0;\n        }\n    }, []);\n    useEffect(() => cleanup, [cleanup]);\n    return (_jsx(\"canvas\", { className: `${_className}__canvas`, dir: \"ltr\", ref: mergeRefs(canvasRef, canvasElement), style: {\n            display: 'block',\n            userSelect: 'none',\n        }, children: renderTextLayer ? _jsx(StructTree, {}) : null }));\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAUA,MAAM,kBAAkB,iJAAA,CAAA,iBAAoB;AAC7B,SAAS,OAAO,KAAK;IAChC,MAAM,cAAc,CAAA,GAAA,mLAAA,CAAA,UAAc,AAAD;IACjC,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,aAAa;IACvB,MAAM,cAAc,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc;IAClE,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,sBAAmB,AAAD,GAAG,EAAE,eAAe,kBAAkB,EAAE,iBAAiB,oBAAoB,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,EAAE,KAAK,EAAG,GAAG;IACjN,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,MAAM;IAChB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC7B;;KAEC,GACD,SAAS;QACL,IAAI,CAAC,MAAM;YACP,+CAA+C;YAC/C;QACJ;QACA,IAAI,sBAAsB;YACtB,qBAAqB,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;QAChD;IACJ;IACA;;KAEC,GACD,SAAS,cAAc,KAAK;QACxB,IAAI,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;YAC1B;QACJ;QACA,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,MAAM,QAAQ;QAC7B,IAAI,oBAAoB;YACpB,mBAAmB;QACvB;IACJ;IACA,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE,IAAM,KAAK,WAAW,CAAC;gBAAE,OAAO,QAAQ;gBAAkB,UAAU;YAAO;yCAAI;QAAC;QAAkB;QAAM;QAAQ;KAAM;IACrJ,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oCAAE,IAAM,KAAK,WAAW,CAAC;gBAAE;gBAAO,UAAU;YAAO;mCAAI;QAAC;QAAM;QAAQ;KAAM;IACnG,4HAA4H;IAC5H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,CAAC,MAAM;YACP;QACJ;QACA,0FAA0F;QAC1F,KAAK,OAAO;QACZ,MAAM,EAAE,SAAS,MAAM,EAAE,GAAG;QAC5B,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,OAAO,KAAK,GAAG,eAAe,KAAK;QACnC,OAAO,MAAM,GAAG,eAAe,MAAM;QACrC,OAAO,KAAK,CAAC,KAAK,GAAG,AAAC,GAA6B,OAA3B,KAAK,KAAK,CAAC,SAAS,KAAK,GAAE;QACnD,OAAO,KAAK,CAAC,MAAM,GAAG,AAAC,GAA8B,OAA5B,KAAK,KAAK,CAAC,SAAS,MAAM,GAAE;QACrD,OAAO,KAAK,CAAC,UAAU,GAAG;QAC1B,MAAM,gBAAgB;YAClB,gBAAgB,cAAc,gBAAgB,YAAY,GAAG,gBAAgB,MAAM;YACnF,eAAe,OAAO,UAAU,CAAC,MAAM;gBAAE,OAAO;YAAM;YACtD,UAAU;QACd;QACA,IAAI,kBAAkB;YAClB,cAAc,UAAU,GAAG;QAC/B;QACA,MAAM,cAAc,KAAK,MAAM,CAAC;QAChC,MAAM,cAAc;QACpB,YAAY,OAAO,CACd,IAAI;iDAAC;gBACN,OAAO,KAAK,CAAC,UAAU,GAAG;gBAC1B;YACJ;gDACK,KAAK,CAAC;QACX;iDAAO,IAAM,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE;;IACnC,GAAG;QAAC;QAAkB;QAAM;QAAa;QAAgB;KAAS;IAClE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE;YACxB,MAAM,EAAE,SAAS,MAAM,EAAE,GAAG;YAC5B;;;SAGC,GACD,IAAI,QAAQ;gBACR,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;YACpB;QACJ;sCAAG,EAAE;IACL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE,IAAM;2BAAS;QAAC;KAAQ;IAClC,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,UAAU;QAAE,WAAW,AAAC,GAAa,OAAX,YAAW;QAAW,KAAK;QAAO,KAAK,CAAA,GAAA,wJAAA,CAAA,UAAS,AAAD,EAAE,WAAW;QAAgB,OAAO;YAClH,SAAS;YACT,YAAY;QAChB;QAAG,UAAU,kBAAkB,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,4JAAA,CAAA,UAAU,EAAE,CAAC,KAAK;IAAK;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/Page/TextLayer.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useCallback, useEffect, useLayoutEffect, useMemo, useRef } from 'react';\nimport makeCancellable from 'make-cancellable-promise';\nimport clsx from 'clsx';\nimport invariant from 'tiny-invariant';\nimport warning from 'warning';\nimport * as pdfjs from 'pdfjs-dist';\nimport usePageContext from '../shared/hooks/usePageContext.js';\nimport useResolver from '../shared/hooks/useResolver.js';\nimport { cancelRunningTask } from '../shared/utils.js';\nfunction isTextItem(item) {\n    return 'str' in item;\n}\nexport default function TextLayer() {\n    const pageContext = usePageContext();\n    invariant(pageContext, 'Unable to find Page context.');\n    const { customTextRenderer, onGetTextError, onGetTextSuccess, onRenderTextLayerError, onRenderTextLayerSuccess, page, pageIndex, pageNumber, rotate, scale, } = pageContext;\n    invariant(page, 'Attempted to load page text content, but no page was specified.');\n    const [textContentState, textContentDispatch] = useResolver();\n    const { value: textContent, error: textContentError } = textContentState;\n    const layerElement = useRef(null);\n    const endElement = useRef(undefined);\n    warning(Number.parseInt(window.getComputedStyle(document.body).getPropertyValue('--react-pdf-text-layer'), 10) === 1, 'TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer');\n    /**\n     * Called when a page text content is read successfully\n     */\n    function onLoadSuccess() {\n        if (!textContent) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetTextSuccess) {\n            onGetTextSuccess(textContent);\n        }\n    }\n    /**\n     * Called when a page text content failed to read successfully\n     */\n    function onLoadError() {\n        if (!textContentError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning(false, textContentError.toString());\n        if (onGetTextError) {\n            onGetTextError(textContentError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    useEffect(function resetTextContent() {\n        textContentDispatch({ type: 'RESET' });\n    }, [page, textContentDispatch]);\n    useEffect(function loadTextContent() {\n        if (!page) {\n            return;\n        }\n        const cancellable = makeCancellable(page.getTextContent());\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextTextContent) => {\n            textContentDispatch({ type: 'RESOLVE', value: nextTextContent });\n        })\n            .catch((error) => {\n            textContentDispatch({ type: 'REJECT', error });\n        });\n        return () => cancelRunningTask(runningTask);\n    }, [page, textContentDispatch]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    useEffect(() => {\n        if (textContent === undefined) {\n            return;\n        }\n        if (textContent === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [textContent]);\n    /**\n     * Called when a text layer is rendered successfully\n     */\n    const onRenderSuccess = useCallback(() => {\n        if (onRenderTextLayerSuccess) {\n            onRenderTextLayerSuccess();\n        }\n    }, [onRenderTextLayerSuccess]);\n    /**\n     * Called when a text layer failed to render successfully\n     */\n    const onRenderError = useCallback((error) => {\n        warning(false, error.toString());\n        if (onRenderTextLayerError) {\n            onRenderTextLayerError(error);\n        }\n    }, [onRenderTextLayerError]);\n    function onMouseDown() {\n        const end = endElement.current;\n        if (!end) {\n            return;\n        }\n        end.classList.add('active');\n    }\n    function onMouseUp() {\n        const end = endElement.current;\n        if (!end) {\n            return;\n        }\n        end.classList.remove('active');\n    }\n    const viewport = useMemo(() => page.getViewport({ scale, rotation: rotate }), [page, rotate, scale]);\n    useLayoutEffect(function renderTextLayer() {\n        if (!page || !textContent) {\n            return;\n        }\n        const { current: layer } = layerElement;\n        if (!layer) {\n            return;\n        }\n        layer.innerHTML = '';\n        const textContentSource = page.streamTextContent({ includeMarkedContent: true });\n        const parameters = {\n            container: layer,\n            textContentSource,\n            viewport,\n        };\n        const cancellable = new pdfjs.TextLayer(parameters);\n        const runningTask = cancellable;\n        cancellable\n            .render()\n            .then(() => {\n            const end = document.createElement('div');\n            end.className = 'endOfContent';\n            layer.append(end);\n            endElement.current = end;\n            const layerChildren = layer.querySelectorAll('[role=\"presentation\"]');\n            if (customTextRenderer) {\n                let index = 0;\n                textContent.items.forEach((item, itemIndex) => {\n                    if (!isTextItem(item)) {\n                        return;\n                    }\n                    const child = layerChildren[index];\n                    if (!child) {\n                        return;\n                    }\n                    const content = customTextRenderer(Object.assign({ pageIndex,\n                        pageNumber,\n                        itemIndex }, item));\n                    child.innerHTML = content;\n                    index += item.str && item.hasEOL ? 2 : 1;\n                });\n            }\n            // Intentional immediate callback\n            onRenderSuccess();\n        })\n            .catch(onRenderError);\n        return () => cancelRunningTask(runningTask);\n    }, [\n        customTextRenderer,\n        onRenderError,\n        onRenderSuccess,\n        page,\n        pageIndex,\n        pageNumber,\n        textContent,\n        viewport,\n    ]);\n    return (_jsx(\"div\", { className: clsx('react-pdf__Page__textContent', 'textLayer'), onMouseUp: onMouseUp, onMouseDown: onMouseDown, ref: layerElement }));\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAWA,SAAS,WAAW,IAAI;IACpB,OAAO,SAAS;AACpB;AACe,SAAS;IACpB,MAAM,cAAc,CAAA,GAAA,mLAAA,CAAA,UAAc,AAAD;IACjC,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,aAAa;IACvB,MAAM,EAAE,kBAAkB,EAAE,cAAc,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,wBAAwB,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAG,GAAG;IAChK,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,MAAM;IAChB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,gLAAA,CAAA,UAAW,AAAD;IAC1D,MAAM,EAAE,OAAO,WAAW,EAAE,OAAO,gBAAgB,EAAE,GAAG;IACxD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,QAAQ,CAAC,OAAO,gBAAgB,CAAC,SAAS,IAAI,EAAE,gBAAgB,CAAC,2BAA2B,QAAQ,GAAG;IACtH;;KAEC,GACD,SAAS;QACL,IAAI,CAAC,aAAa;YACd,+CAA+C;YAC/C;QACJ;QACA,IAAI,kBAAkB;YAClB,iBAAiB;QACrB;IACJ;IACA;;KAEC,GACD,SAAS;QACL,IAAI,CAAC,kBAAkB;YACnB,+CAA+C;YAC/C;QACJ;QACA,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,iBAAiB,QAAQ;QACxC,IAAI,gBAAgB;YAChB,eAAe;QACnB;IACJ;IACA,4GAA4G;IAC5G,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,oBAAoB;YAAE,MAAM;QAAQ;IACxC,GAAG;QAAC;QAAM;KAAoB;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,CAAC,MAAM;YACP;QACJ;QACA,MAAM,cAAc,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,KAAK,cAAc;QACvD,MAAM,cAAc;QACpB,YAAY,OAAO,CACd,IAAI;mDAAC,CAAC;gBACP,oBAAoB;oBAAE,MAAM;oBAAW,OAAO;gBAAgB;YAClE;kDACK,KAAK;mDAAC,CAAC;gBACR,oBAAoB;oBAAE,MAAM;oBAAU;gBAAM;YAChD;;QACA;mDAAO,IAAM,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE;;IACnC,GAAG;QAAC;QAAM;KAAoB;IAC9B,4HAA4H;IAC5H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,IAAI,gBAAgB,WAAW;gBAC3B;YACJ;YACA,IAAI,gBAAgB,OAAO;gBACvB;gBACA;YACJ;YACA;QACJ;8BAAG;QAAC;KAAY;IAChB;;KAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAChC,IAAI,0BAA0B;gBAC1B;YACJ;QACJ;iDAAG;QAAC;KAAyB;IAC7B;;KAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAC/B,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,MAAM,QAAQ;YAC7B,IAAI,wBAAwB;gBACxB,uBAAuB;YAC3B;QACJ;+CAAG;QAAC;KAAuB;IAC3B,SAAS;QACL,MAAM,MAAM,WAAW,OAAO;QAC9B,IAAI,CAAC,KAAK;YACN;QACJ;QACA,IAAI,SAAS,CAAC,GAAG,CAAC;IACtB;IACA,SAAS;QACL,MAAM,MAAM,WAAW,OAAO;QAC9B,IAAI,CAAC,KAAK;YACN;QACJ;QACA,IAAI,SAAS,CAAC,MAAM,CAAC;IACzB;IACA,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uCAAE,IAAM,KAAK,WAAW,CAAC;gBAAE;gBAAO,UAAU;YAAO;sCAAI;QAAC;QAAM;QAAQ;KAAM;IACnG,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD,EAAE,SAAS;QACrB,IAAI,CAAC,QAAQ,CAAC,aAAa;YACvB;QACJ;QACA,MAAM,EAAE,SAAS,KAAK,EAAE,GAAG;QAC3B,IAAI,CAAC,OAAO;YACR;QACJ;QACA,MAAM,SAAS,GAAG;QAClB,MAAM,oBAAoB,KAAK,iBAAiB,CAAC;YAAE,sBAAsB;QAAK;QAC9E,MAAM,aAAa;YACf,WAAW;YACX;YACA;QACJ;QACA,MAAM,cAAc,IAAI,iJAAA,CAAA,YAAe,CAAC;QACxC,MAAM,cAAc;QACpB,YACK,MAAM,GACN,IAAI;yDAAC;gBACN,MAAM,MAAM,SAAS,aAAa,CAAC;gBACnC,IAAI,SAAS,GAAG;gBAChB,MAAM,MAAM,CAAC;gBACb,WAAW,OAAO,GAAG;gBACrB,MAAM,gBAAgB,MAAM,gBAAgB,CAAC;gBAC7C,IAAI,oBAAoB;oBACpB,IAAI,QAAQ;oBACZ,YAAY,KAAK,CAAC,OAAO;qEAAC,CAAC,MAAM;4BAC7B,IAAI,CAAC,WAAW,OAAO;gCACnB;4BACJ;4BACA,MAAM,QAAQ,aAAa,CAAC,MAAM;4BAClC,IAAI,CAAC,OAAO;gCACR;4BACJ;4BACA,MAAM,UAAU,mBAAmB,OAAO,MAAM,CAAC;gCAAE;gCAC/C;gCACA;4BAAU,GAAG;4BACjB,MAAM,SAAS,GAAG;4BAClB,SAAS,KAAK,GAAG,IAAI,KAAK,MAAM,GAAG,IAAI;wBAC3C;;gBACJ;gBACA,iCAAiC;gBACjC;YACJ;wDACK,KAAK,CAAC;QACX;yDAAO,IAAM,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE;;IACnC,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAAE,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,gCAAgC;QAAc,WAAW;QAAW,aAAa;QAAa,KAAK;IAAa;AAC1J", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1559, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js"], "sourcesContent": ["import { useContext } from 'react';\nimport DocumentContext from '../../DocumentContext.js';\nexport default function useDocumentContext() {\n    return useContext(DocumentContext);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACe,SAAS;IACpB,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iKAAA,CAAA,UAAe;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1573, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { useEffect, useMemo, useRef } from 'react';\nimport makeCancellable from 'make-cancellable-promise';\nimport clsx from 'clsx';\nimport invariant from 'tiny-invariant';\nimport warning from 'warning';\nimport * as pdfjs from 'pdfjs-dist';\nimport useDocumentContext from '../shared/hooks/useDocumentContext.js';\nimport usePageContext from '../shared/hooks/usePageContext.js';\nimport useResolver from '../shared/hooks/useResolver.js';\nimport { cancelRunningTask } from '../shared/utils.js';\nexport default function AnnotationLayer() {\n    const documentContext = useDocumentContext();\n    const pageContext = usePageContext();\n    invariant(pageContext, 'Unable to find Page context.');\n    const mergedProps = Object.assign(Object.assign({}, documentContext), pageContext);\n    const { imageResourcesPath, linkService, onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, page, pdf, renderForms, rotate, scale = 1, } = mergedProps;\n    invariant(pdf, 'Attempted to load page annotations, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.');\n    invariant(page, 'Attempted to load page annotations, but no page was specified.');\n    invariant(linkService, 'Attempted to load page annotations, but no linkService was specified.');\n    const [annotationsState, annotationsDispatch] = useResolver();\n    const { value: annotations, error: annotationsError } = annotationsState;\n    const layerElement = useRef(null);\n    warning(Number.parseInt(window.getComputedStyle(document.body).getPropertyValue('--react-pdf-annotation-layer'), 10) === 1, 'AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations');\n    function onLoadSuccess() {\n        if (!annotations) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        if (onGetAnnotationsSuccessProps) {\n            onGetAnnotationsSuccessProps(annotations);\n        }\n    }\n    function onLoadError() {\n        if (!annotationsError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning(false, annotationsError.toString());\n        if (onGetAnnotationsErrorProps) {\n            onGetAnnotationsErrorProps(annotationsError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change\n    useEffect(function resetAnnotations() {\n        annotationsDispatch({ type: 'RESET' });\n    }, [annotationsDispatch, page]);\n    useEffect(function loadAnnotations() {\n        if (!page) {\n            return;\n        }\n        const cancellable = makeCancellable(page.getAnnotations());\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextAnnotations) => {\n            annotationsDispatch({ type: 'RESOLVE', value: nextAnnotations });\n        })\n            .catch((error) => {\n            annotationsDispatch({ type: 'REJECT', error });\n        });\n        return () => {\n            cancelRunningTask(runningTask);\n        };\n    }, [annotationsDispatch, page]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    useEffect(() => {\n        if (annotations === undefined) {\n            return;\n        }\n        if (annotations === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [annotations]);\n    function onRenderSuccess() {\n        if (onRenderAnnotationLayerSuccessProps) {\n            onRenderAnnotationLayerSuccessProps();\n        }\n    }\n    function onRenderError(error) {\n        warning(false, `${error}`);\n        if (onRenderAnnotationLayerErrorProps) {\n            onRenderAnnotationLayerErrorProps(error);\n        }\n    }\n    const viewport = useMemo(() => page.getViewport({ scale, rotation: rotate }), [page, rotate, scale]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    useEffect(function renderAnnotationLayer() {\n        if (!pdf || !page || !linkService || !annotations) {\n            return;\n        }\n        const { current: layer } = layerElement;\n        if (!layer) {\n            return;\n        }\n        const clonedViewport = viewport.clone({ dontFlip: true });\n        const annotationLayerParameters = {\n            accessibilityManager: null, // TODO: Implement this\n            annotationCanvasMap: null, // TODO: Implement this\n            annotationEditorUIManager: null, // TODO: Implement this\n            div: layer,\n            l10n: null, // TODO: Implement this\n            page,\n            viewport: clonedViewport,\n        };\n        const renderParameters = {\n            annotations,\n            annotationStorage: pdf.annotationStorage,\n            div: layer,\n            imageResourcesPath,\n            linkService,\n            page,\n            renderForms,\n            viewport: clonedViewport,\n        };\n        layer.innerHTML = '';\n        try {\n            new pdfjs.AnnotationLayer(annotationLayerParameters).render(renderParameters);\n            // Intentional immediate callback\n            onRenderSuccess();\n        }\n        catch (error) {\n            onRenderError(error);\n        }\n        return () => {\n            // TODO: Cancel running task?\n        };\n    }, [annotations, imageResourcesPath, linkService, page, pdf, renderForms, viewport]);\n    return (_jsx(\"div\", { className: clsx('react-pdf__Page__annotations', 'annotationLayer'), ref: layerElement }));\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAYe,SAAS;IACpB,MAAM,kBAAkB,CAAA,GAAA,uLAAA,CAAA,UAAkB,AAAD;IACzC,MAAM,cAAc,CAAA,GAAA,mLAAA,CAAA,UAAc,AAAD;IACjC,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,aAAa;IACvB,MAAM,cAAc,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB;IACtE,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,uBAAuB,0BAA0B,EAAE,yBAAyB,4BAA4B,EAAE,8BAA8B,iCAAiC,EAAE,gCAAgC,mCAAmC,EAAE,IAAI,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAG,GAAG;IACxU,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,KAAK;IACf,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,MAAM;IAChB,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,aAAa;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,gLAAA,CAAA,UAAW,AAAD;IAC1D,MAAM,EAAE,OAAO,WAAW,EAAE,OAAO,gBAAgB,EAAE,GAAG;IACxD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,QAAQ,CAAC,OAAO,gBAAgB,CAAC,SAAS,IAAI,EAAE,gBAAgB,CAAC,iCAAiC,QAAQ,GAAG;IAC5H,SAAS;QACL,IAAI,CAAC,aAAa;YACd,+CAA+C;YAC/C;QACJ;QACA,IAAI,8BAA8B;YAC9B,6BAA6B;QACjC;IACJ;IACA,SAAS;QACL,IAAI,CAAC,kBAAkB;YACnB,+CAA+C;YAC/C;QACJ;QACA,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,iBAAiB,QAAQ;QACxC,IAAI,4BAA4B;YAC5B,2BAA2B;QAC/B;IACJ;IACA,4GAA4G;IAC5G,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,oBAAoB;YAAE,MAAM;QAAQ;IACxC,GAAG;QAAC;QAAqB;KAAK;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,CAAC,MAAM;YACP;QACJ;QACA,MAAM,cAAc,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,KAAK,cAAc;QACvD,MAAM,cAAc;QACpB,YAAY,OAAO,CACd,IAAI;yDAAC,CAAC;gBACP,oBAAoB;oBAAE,MAAM;oBAAW,OAAO;gBAAgB;YAClE;wDACK,KAAK;yDAAC,CAAC;gBACR,oBAAoB;oBAAE,MAAM;oBAAU;gBAAM;YAChD;;QACA;yDAAO;gBACH,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE;YACtB;;IACJ,GAAG;QAAC;QAAqB;KAAK;IAC9B,4HAA4H;IAC5H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACN,IAAI,gBAAgB,WAAW;gBAC3B;YACJ;YACA,IAAI,gBAAgB,OAAO;gBACvB;gBACA;YACJ;YACA;QACJ;oCAAG;QAAC;KAAY;IAChB,SAAS;QACL,IAAI,qCAAqC;YACrC;QACJ;IACJ;IACA,SAAS,cAAc,KAAK;QACxB,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,AAAC,GAAQ,OAAN;QAClB,IAAI,mCAAmC;YACnC,kCAAkC;QACtC;IACJ;IACA,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE,IAAM,KAAK,WAAW,CAAC;gBAAE;gBAAO,UAAU;YAAO;4CAAI;QAAC;QAAM;QAAQ;KAAM;IACnG,4HAA4H;IAC5H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,aAAa;YAC/C;QACJ;QACA,MAAM,EAAE,SAAS,KAAK,EAAE,GAAG;QAC3B,IAAI,CAAC,OAAO;YACR;QACJ;QACA,MAAM,iBAAiB,SAAS,KAAK,CAAC;YAAE,UAAU;QAAK;QACvD,MAAM,4BAA4B;YAC9B,sBAAsB;YACtB,qBAAqB;YACrB,2BAA2B;YAC3B,KAAK;YACL,MAAM;YACN;YACA,UAAU;QACd;QACA,MAAM,mBAAmB;YACrB;YACA,mBAAmB,IAAI,iBAAiB;YACxC,KAAK;YACL;YACA;YACA;YACA;YACA,UAAU;QACd;QACA,MAAM,SAAS,GAAG;QAClB,IAAI;YACA,IAAI,iJAAA,CAAA,kBAAqB,CAAC,2BAA2B,MAAM,CAAC;YAC5D,iCAAiC;YACjC;QACJ,EACA,OAAO,OAAO;YACV,cAAc;QAClB;QACA;+DAAO;YACH,6BAA6B;YACjC;;IACJ,GAAG;QAAC;QAAa;QAAoB;QAAa;QAAM;QAAK;QAAa;KAAS;IACnF,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAAE,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,gCAAgC;QAAoB,KAAK;IAAa;AAChH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/Page.js"], "sourcesContent": ["'use client';\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useEffect, useMemo, useRef } from 'react';\nimport makeCancellable from 'make-cancellable-promise';\nimport makeEventProps from 'make-event-props';\nimport clsx from 'clsx';\nimport mergeRefs from 'merge-refs';\nimport invariant from 'tiny-invariant';\nimport warning from 'warning';\nimport PageContext from './PageContext.js';\nimport Message from './Message.js';\nimport Canvas from './Page/Canvas.js';\nimport TextLayer from './Page/TextLayer.js';\nimport AnnotationLayer from './Page/AnnotationLayer.js';\nimport { cancelRunningTask, isProvided, makePageCallback } from './shared/utils.js';\nimport useDocumentContext from './shared/hooks/useDocumentContext.js';\nimport useResolver from './shared/hooks/useResolver.js';\nconst defaultScale = 1;\n/**\n * Displays a page.\n *\n * Should be placed inside `<Document />`. Alternatively, it can have `pdf` prop passed, which can be obtained from `<Document />`'s `onLoadSuccess` callback function, however some advanced functions like linking between pages inside a document may not be working correctly.\n */\nexport default function Page(props) {\n    const documentContext = useDocumentContext();\n    const mergedProps = Object.assign(Object.assign({}, documentContext), props);\n    const { _className = 'react-pdf__Page', _enableRegisterUnregisterPage = true, canvasBackground, canvasRef, children, className, customRenderer: CustomRenderer, customTextRenderer, devicePixelRatio, error = 'Failed to load the page.', height, inputRef, loading = 'Loading page…', noData = 'No page specified.', onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, onGetTextError: onGetTextErrorProps, onGetTextSuccess: onGetTextSuccessProps, onLoadError: onLoadErrorProps, onLoadSuccess: onLoadSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, onRenderTextLayerError: onRenderTextLayerErrorProps, onRenderTextLayerSuccess: onRenderTextLayerSuccessProps, pageIndex: pageIndexProps, pageNumber: pageNumberProps, pdf, registerPage, renderAnnotationLayer: renderAnnotationLayerProps = true, renderForms = false, renderMode = 'canvas', renderTextLayer: renderTextLayerProps = true, rotate: rotateProps, scale: scaleProps = defaultScale, unregisterPage, width } = mergedProps, otherProps = __rest(mergedProps, [\"_className\", \"_enableRegisterUnregisterPage\", \"canvasBackground\", \"canvasRef\", \"children\", \"className\", \"customRenderer\", \"customTextRenderer\", \"devicePixelRatio\", \"error\", \"height\", \"inputRef\", \"loading\", \"noData\", \"onGetAnnotationsError\", \"onGetAnnotationsSuccess\", \"onGetStructTreeError\", \"onGetStructTreeSuccess\", \"onGetTextError\", \"onGetTextSuccess\", \"onLoadError\", \"onLoadSuccess\", \"onRenderAnnotationLayerError\", \"onRenderAnnotationLayerSuccess\", \"onRenderError\", \"onRenderSuccess\", \"onRenderTextLayerError\", \"onRenderTextLayerSuccess\", \"pageIndex\", \"pageNumber\", \"pdf\", \"registerPage\", \"renderAnnotationLayer\", \"renderForms\", \"renderMode\", \"renderTextLayer\", \"rotate\", \"scale\", \"unregisterPage\", \"width\"]);\n    const [pageState, pageDispatch] = useResolver();\n    const { value: page, error: pageError } = pageState;\n    const pageElement = useRef(null);\n    invariant(pdf, 'Attempted to load a page, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.');\n    const pageIndex = isProvided(pageNumberProps) ? pageNumberProps - 1 : (pageIndexProps !== null && pageIndexProps !== void 0 ? pageIndexProps : null);\n    const pageNumber = pageNumberProps !== null && pageNumberProps !== void 0 ? pageNumberProps : (isProvided(pageIndexProps) ? pageIndexProps + 1 : null);\n    const rotate = rotateProps !== null && rotateProps !== void 0 ? rotateProps : (page ? page.rotate : null);\n    const scale = useMemo(() => {\n        if (!page) {\n            return null;\n        }\n        // Be default, we'll render page at 100% * scale width.\n        let pageScale = 1;\n        // Passing scale explicitly null would cause the page not to render\n        const scaleWithDefault = scaleProps !== null && scaleProps !== void 0 ? scaleProps : defaultScale;\n        // If width/height is defined, calculate the scale of the page so it could be of desired width.\n        if (width || height) {\n            const viewport = page.getViewport({ scale: 1, rotation: rotate });\n            if (width) {\n                pageScale = width / viewport.width;\n            }\n            else if (height) {\n                pageScale = height / viewport.height;\n            }\n        }\n        return scaleWithDefault * pageScale;\n    }, [height, page, rotate, scaleProps, width]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf change\n    useEffect(function hook() {\n        return () => {\n            if (!isProvided(pageIndex)) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            if (_enableRegisterUnregisterPage && unregisterPage) {\n                unregisterPage(pageIndex);\n            }\n        };\n    }, [_enableRegisterUnregisterPage, pdf, pageIndex, unregisterPage]);\n    /**\n     * Called when a page is loaded successfully\n     */\n    function onLoadSuccess() {\n        if (onLoadSuccessProps) {\n            if (!page || !scale) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            onLoadSuccessProps(makePageCallback(page, scale));\n        }\n        if (_enableRegisterUnregisterPage && registerPage) {\n            if (!isProvided(pageIndex) || !pageElement.current) {\n                // Impossible, but TypeScript doesn't know that\n                return;\n            }\n            registerPage(pageIndex, pageElement.current);\n        }\n    }\n    /**\n     * Called when a page failed to load\n     */\n    function onLoadError() {\n        if (!pageError) {\n            // Impossible, but TypeScript doesn't know that\n            return;\n        }\n        warning(false, pageError.toString());\n        if (onLoadErrorProps) {\n            onLoadErrorProps(pageError);\n        }\n    }\n    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf and pageIndex change\n    useEffect(function resetPage() {\n        pageDispatch({ type: 'RESET' });\n    }, [pageDispatch, pdf, pageIndex]);\n    useEffect(function loadPage() {\n        if (!pdf || !pageNumber) {\n            return;\n        }\n        const cancellable = makeCancellable(pdf.getPage(pageNumber));\n        const runningTask = cancellable;\n        cancellable.promise\n            .then((nextPage) => {\n            pageDispatch({ type: 'RESOLVE', value: nextPage });\n        })\n            .catch((error) => {\n            pageDispatch({ type: 'REJECT', error });\n        });\n        return () => cancelRunningTask(runningTask);\n    }, [pageDispatch, pdf, pageNumber]);\n    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change\n    useEffect(() => {\n        if (page === undefined) {\n            return;\n        }\n        if (page === false) {\n            onLoadError();\n            return;\n        }\n        onLoadSuccess();\n    }, [page, scale]);\n    const childContext = useMemo(() => \n    // Technically there cannot be page without pageIndex, pageNumber, rotate and scale, but TypeScript doesn't know that\n    page && isProvided(pageIndex) && pageNumber && isProvided(rotate) && isProvided(scale)\n        ? {\n            _className,\n            canvasBackground,\n            customTextRenderer,\n            devicePixelRatio,\n            onGetAnnotationsError: onGetAnnotationsErrorProps,\n            onGetAnnotationsSuccess: onGetAnnotationsSuccessProps,\n            onGetStructTreeError: onGetStructTreeErrorProps,\n            onGetStructTreeSuccess: onGetStructTreeSuccessProps,\n            onGetTextError: onGetTextErrorProps,\n            onGetTextSuccess: onGetTextSuccessProps,\n            onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps,\n            onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps,\n            onRenderError: onRenderErrorProps,\n            onRenderSuccess: onRenderSuccessProps,\n            onRenderTextLayerError: onRenderTextLayerErrorProps,\n            onRenderTextLayerSuccess: onRenderTextLayerSuccessProps,\n            page,\n            pageIndex,\n            pageNumber,\n            renderForms,\n            renderTextLayer: renderTextLayerProps,\n            rotate,\n            scale,\n        }\n        : null, [\n        _className,\n        canvasBackground,\n        customTextRenderer,\n        devicePixelRatio,\n        onGetAnnotationsErrorProps,\n        onGetAnnotationsSuccessProps,\n        onGetStructTreeErrorProps,\n        onGetStructTreeSuccessProps,\n        onGetTextErrorProps,\n        onGetTextSuccessProps,\n        onRenderAnnotationLayerErrorProps,\n        onRenderAnnotationLayerSuccessProps,\n        onRenderErrorProps,\n        onRenderSuccessProps,\n        onRenderTextLayerErrorProps,\n        onRenderTextLayerSuccessProps,\n        page,\n        pageIndex,\n        pageNumber,\n        renderForms,\n        renderTextLayerProps,\n        rotate,\n        scale,\n    ]);\n    const eventProps = useMemo(() => makeEventProps(otherProps, () => page ? (scale ? makePageCallback(page, scale) : undefined) : page), \n    // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME\n    [otherProps, page, scale]);\n    const pageKey = `${pageIndex}@${scale}/${rotate}`;\n    function renderMainLayer() {\n        switch (renderMode) {\n            case 'custom': {\n                invariant(CustomRenderer, `renderMode was set to \"custom\", but no customRenderer was passed.`);\n                return _jsx(CustomRenderer, {}, `${pageKey}_custom`);\n            }\n            case 'none':\n                return null;\n            case 'canvas':\n            default:\n                return _jsx(Canvas, { canvasRef: canvasRef }, `${pageKey}_canvas`);\n        }\n    }\n    function renderTextLayer() {\n        if (!renderTextLayerProps) {\n            return null;\n        }\n        return _jsx(TextLayer, {}, `${pageKey}_text`);\n    }\n    function renderAnnotationLayer() {\n        if (!renderAnnotationLayerProps) {\n            return null;\n        }\n        return _jsx(AnnotationLayer, {}, `${pageKey}_annotations`);\n    }\n    function renderChildren() {\n        return (_jsxs(PageContext.Provider, { value: childContext, children: [renderMainLayer(), renderTextLayer(), renderAnnotationLayer(), children] }));\n    }\n    function renderContent() {\n        if (!pageNumber) {\n            return _jsx(Message, { type: \"no-data\", children: typeof noData === 'function' ? noData() : noData });\n        }\n        if (pdf === null || page === undefined || page === null) {\n            return (_jsx(Message, { type: \"loading\", children: typeof loading === 'function' ? loading() : loading }));\n        }\n        if (pdf === false || page === false) {\n            return _jsx(Message, { type: \"error\", children: typeof error === 'function' ? error() : error });\n        }\n        return renderChildren();\n    }\n    return (_jsx(\"div\", Object.assign({ className: clsx(_className, className), \"data-page-number\": pageNumber, \n        // Assertion is needed for React 18 compatibility\n        ref: mergeRefs(inputRef, pageElement), style: {\n            ['--scale-factor']: `${scale}`,\n            backgroundColor: canvasBackground || 'white',\n            position: 'relative',\n            minWidth: 'min-content',\n            minHeight: 'min-content',\n        } }, eventProps, { children: renderContent() })));\n}\n"], "names": [], "mappings": ";;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA3BA;AACA,IAAI,SAAS,4CAAS,yCAAK,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC;IAChD,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAC9E,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QACpE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACJ,OAAO;AACX;;;;;;;;;;;;;;;;;AAiBA,MAAM,eAAe;AAMN,SAAS,KAAK,KAAK;IAC9B,MAAM,kBAAkB,CAAA,GAAA,uLAAA,CAAA,UAAkB,AAAD;IACzC,MAAM,cAAc,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB;IACtE,MAAM,EAAE,aAAa,iBAAiB,EAAE,gCAAgC,IAAI,EAAE,gBAAgB,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,gBAAgB,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,QAAQ,0BAA0B,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,eAAe,EAAE,SAAS,oBAAoB,EAAE,uBAAuB,0BAA0B,EAAE,yBAAyB,4BAA4B,EAAE,sBAAsB,yBAAyB,EAAE,wBAAwB,2BAA2B,EAAE,gBAAgB,mBAAmB,EAAE,kBAAkB,qBAAqB,EAAE,aAAa,gBAAgB,EAAE,eAAe,kBAAkB,EAAE,8BAA8B,iCAAiC,EAAE,gCAAgC,mCAAmC,EAAE,eAAe,kBAAkB,EAAE,iBAAiB,oBAAoB,EAAE,wBAAwB,2BAA2B,EAAE,0BAA0B,6BAA6B,EAAE,WAAW,cAAc,EAAE,YAAY,eAAe,EAAE,GAAG,EAAE,YAAY,EAAE,uBAAuB,6BAA6B,IAAI,EAAE,cAAc,KAAK,EAAE,aAAa,QAAQ,EAAE,iBAAiB,uBAAuB,IAAI,EAAE,QAAQ,WAAW,EAAE,OAAO,aAAa,YAAY,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,aAAa,aAAa,OAAO,aAAa;QAAC;QAAc;QAAiC;QAAoB;QAAa;QAAY;QAAa;QAAkB;QAAsB;QAAoB;QAAS;QAAU;QAAY;QAAW;QAAU;QAAyB;QAA2B;QAAwB;QAA0B;QAAkB;QAAoB;QAAe;QAAiB;QAAgC;QAAkC;QAAiB;QAAmB;QAA0B;QAA4B;QAAa;QAAc;QAAO;QAAgB;QAAyB;QAAe;QAAc;QAAmB;QAAU;QAAS;QAAkB;KAAQ;IAC5/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,gLAAA,CAAA,UAAW,AAAD;IAC5C,MAAM,EAAE,OAAO,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG;IAC1C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,KAAK;IACf,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,mBAAmB,kBAAkB,IAAK,mBAAmB,QAAQ,mBAAmB,KAAK,IAAI,iBAAiB;IAC/I,MAAM,aAAa,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,kBAAmB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB,iBAAiB,IAAI;IACjJ,MAAM,SAAS,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,cAAe,OAAO,KAAK,MAAM,GAAG;IACpG,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+BAAE;YAClB,IAAI,CAAC,MAAM;gBACP,OAAO;YACX;YACA,uDAAuD;YACvD,IAAI,YAAY;YAChB,mEAAmE;YACnE,MAAM,mBAAmB,eAAe,QAAQ,eAAe,KAAK,IAAI,aAAa;YACrF,+FAA+F;YAC/F,IAAI,SAAS,QAAQ;gBACjB,MAAM,WAAW,KAAK,WAAW,CAAC;oBAAE,OAAO;oBAAG,UAAU;gBAAO;gBAC/D,IAAI,OAAO;oBACP,YAAY,QAAQ,SAAS,KAAK;gBACtC,OACK,IAAI,QAAQ;oBACb,YAAY,SAAS,SAAS,MAAM;gBACxC;YACJ;YACA,OAAO,mBAAmB;QAC9B;8BAAG;QAAC;QAAQ;QAAM;QAAQ;QAAY;KAAM;IAC5C,2GAA2G;IAC3G,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf;mCAAO;gBACH,IAAI,CAAC,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,YAAY;oBACxB,+CAA+C;oBAC/C;gBACJ;gBACA,IAAI,iCAAiC,gBAAgB;oBACjD,eAAe;gBACnB;YACJ;;IACJ,GAAG;QAAC;QAA+B;QAAK;QAAW;KAAe;IAClE;;KAEC,GACD,SAAS;QACL,IAAI,oBAAoB;YACpB,IAAI,CAAC,QAAQ,CAAC,OAAO;gBACjB,+CAA+C;gBAC/C;YACJ;YACA,mBAAmB,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;QAC9C;QACA,IAAI,iCAAiC,cAAc;YAC/C,IAAI,CAAC,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,cAAc,CAAC,YAAY,OAAO,EAAE;gBAChD,+CAA+C;gBAC/C;YACJ;YACA,aAAa,WAAW,YAAY,OAAO;QAC/C;IACJ;IACA;;KAEC,GACD,SAAS;QACL,IAAI,CAAC,WAAW;YACZ,+CAA+C;YAC/C;QACJ;QACA,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,UAAU,QAAQ;QACjC,IAAI,kBAAkB;YAClB,iBAAiB;QACrB;IACJ;IACA,yHAAyH;IACzH,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,aAAa;YAAE,MAAM;QAAQ;IACjC,GAAG;QAAC;QAAc;QAAK;KAAU;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACf,IAAI,CAAC,OAAO,CAAC,YAAY;YACrB;QACJ;QACA,MAAM,cAAc,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,IAAI,OAAO,CAAC;QAChD,MAAM,cAAc;QACpB,YAAY,OAAO,CACd,IAAI;uCAAC,CAAC;gBACP,aAAa;oBAAE,MAAM;oBAAW,OAAO;gBAAS;YACpD;sCACK,KAAK;uCAAC,CAAC;gBACR,aAAa;oBAAE,MAAM;oBAAU;gBAAM;YACzC;;QACA;uCAAO,IAAM,CAAA,GAAA,iKAAA,CAAA,oBAAiB,AAAD,EAAE;;IACnC,GAAG;QAAC;QAAc;QAAK;KAAW;IAClC,4HAA4H;IAC5H,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACN,IAAI,SAAS,WAAW;gBACpB;YACJ;YACA,IAAI,SAAS,OAAO;gBAChB;gBACA;YACJ;YACA;QACJ;yBAAG;QAAC;QAAM;KAAM;IAChB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCAAE,IAC7B,qHAAqH;YACrH,QAAQ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,cAAc,cAAc,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,WAAW,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,SAC1E;gBACE;gBACA;gBACA;gBACA;gBACA,uBAAuB;gBACvB,yBAAyB;gBACzB,sBAAsB;gBACtB,wBAAwB;gBACxB,gBAAgB;gBAChB,kBAAkB;gBAClB,8BAA8B;gBAC9B,gCAAgC;gBAChC,eAAe;gBACf,iBAAiB;gBACjB,wBAAwB;gBACxB,0BAA0B;gBAC1B;gBACA;gBACA;gBACA;gBACA,iBAAiB;gBACjB;gBACA;YACJ,IACE;qCAAM;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oCAAE,IAAM,CAAA,GAAA,iKAAA,CAAA,UAAc,AAAD,EAAE;4CAAY,IAAM,OAAQ,QAAQ,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,SAAS,YAAa;;mCAC/H,iEAAiE;IACjE;QAAC;QAAY;QAAM;KAAM;IACzB,MAAM,UAAU,AAAC,GAAe,OAAb,WAAU,KAAY,OAAT,OAAM,KAAU,OAAP;IACzC,SAAS;QACL,OAAQ;YACJ,KAAK;gBAAU;oBACX,CAAA,GAAA,wKAAA,CAAA,UAAS,AAAD,EAAE,gBAAiB;oBAC3B,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,gBAAgB,CAAC,GAAG,AAAC,GAAU,OAAR,SAAQ;gBAC/C;YACA,KAAK;gBACD,OAAO;YACX,KAAK;YACL;gBACI,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,gKAAA,CAAA,UAAM,EAAE;oBAAE,WAAW;gBAAU,GAAG,AAAC,GAAU,OAAR,SAAQ;QACjE;IACJ;IACA,SAAS;QACL,IAAI,CAAC,sBAAsB;YACvB,OAAO;QACX;QACA,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,mKAAA,CAAA,UAAS,EAAE,CAAC,GAAG,AAAC,GAAU,OAAR,SAAQ;IAC1C;IACA,SAAS;QACL,IAAI,CAAC,4BAA4B;YAC7B,OAAO;QACX;QACA,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,yKAAA,CAAA,UAAe,EAAE,CAAC,GAAG,AAAC,GAAU,OAAR,SAAQ;IAChD;IACA,SAAS;QACL,OAAQ,CAAA,GAAA,sKAAA,CAAA,OAAK,AAAD,EAAE,6JAAA,CAAA,UAAW,CAAC,QAAQ,EAAE;YAAE,OAAO;YAAc,UAAU;gBAAC;gBAAmB;gBAAmB;gBAAyB;aAAS;QAAC;IACnJ;IACA,SAAS;QACL,IAAI,CAAC,YAAY;YACb,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,yJAAA,CAAA,UAAO,EAAE;gBAAE,MAAM;gBAAW,UAAU,OAAO,WAAW,aAAa,WAAW;YAAO;QACvG;QACA,IAAI,QAAQ,QAAQ,SAAS,aAAa,SAAS,MAAM;YACrD,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,yJAAA,CAAA,UAAO,EAAE;gBAAE,MAAM;gBAAW,UAAU,OAAO,YAAY,aAAa,YAAY;YAAQ;QAC3G;QACA,IAAI,QAAQ,SAAS,SAAS,OAAO;YACjC,OAAO,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,yJAAA,CAAA,UAAO,EAAE;gBAAE,MAAM;gBAAS,UAAU,OAAO,UAAU,aAAa,UAAU;YAAM;QAClG;QACA,OAAO;IACX;IACA,OAAQ,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAAE,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,YAAY;QAAY,oBAAoB;QAC5F,iDAAiD;QACjD,KAAK,CAAA,GAAA,wJAAA,CAAA,UAAS,AAAD,EAAE,UAAU;QAAc,OAAO;YAC1C,CAAC,iBAAiB,EAAE,AAAC,GAAQ,OAAN;YACvB,iBAAiB,oBAAoB;YACrC,UAAU;YACV,UAAU;YACV,WAAW;QACf;IAAE,GAAG,YAAY;QAAE,UAAU;IAAgB;AACrD", "ignoreList": [0], "debugId": null}}]}
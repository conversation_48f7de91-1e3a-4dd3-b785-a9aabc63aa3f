"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[768],{3351:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(12115).createContext)(null)},13086:(e,t,n)=>{n.d(t,{A:()=>j});var r=n(95155),o=n(12115),i=n(50430),a=n(55653),u=n(52596);function s(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.filter(Boolean);return n.length<=1?n[0]||null:function(e){for(var t=0;t<n.length;t++){var r=n[t];"function"==typeof r?r(e):r&&(r.current=e)}}}var l=n(93179),c=n(94274);let d=(0,o.createContext)(null);var f=n(89448),p=n(54014);let g={Document:null,DocumentFragment:null,Part:"group",Sect:"group",Div:"group",Aside:"note",NonStruct:"none",P:null,H:"heading",Title:null,FENote:"note",Sub:"group",Lbl:null,Span:null,Em:null,Strong:null,Link:"link",Annot:"note",Form:"form",Ruby:null,RB:null,RT:null,RP:null,Warichu:null,WT:null,WP:null,L:"list",LI:"listitem",LBody:null,Table:"table",TR:"row",TH:"columnheader",TD:"cell",THead:"columnheader",TBody:null,TFoot:null,Caption:null,Figure:"figure",Formula:null,Artifact:null},h=/^H(\d+)$/;function y(e){return"children"in e}function m(e){return!!y(e)&&1===e.children.length&&0 in e.children&&"id"in e.children[0]}function E({className:e,node:t}){let n=(0,o.useMemo)(()=>t?Object.assign(Object.assign({},function(e){let t={};if(y(e)){let{role:n}=e,r=n.match(h);if(r)t.role="heading",t["aria-level"]=Number(r[1]);else if(n in g){let e=g[n];e&&(t.role=e)}}return t}(t)),function e(t){let n={};if(y(t)){if(void 0!==t.alt&&(n["aria-label"]=t.alt),void 0!==t.lang&&(n.lang=t.lang),m(t)){let[r]=t.children;if(r){let t=e(r);return Object.assign(Object.assign({},n),t)}}}else"id"in t&&(n["aria-owns"]=t.id);return n}(t)):null,[t]),i=(0,o.useMemo)(()=>!y(t)||m(t)?null:t.children.map((e,t)=>(0,r.jsx)(E,{node:e},t)),[t]);return(0,r.jsx)("span",Object.assign({className:e},n,{children:i}))}function v(){return(0,o.useContext)(d)}var b=n(50189),A=n(34266);function w(){let e=v();(0,l.A)(e,"Unable to find Page context.");let{onGetStructTreeError:t,onGetStructTreeSuccess:n}=e,[a,u]=(0,b.A)(),{value:s,error:d}=a,{customTextRenderer:f,page:p}=e;return((0,o.useEffect)(function(){u({type:"RESET"})},[u,p]),(0,o.useEffect)(function(){if(f||!p)return;let e=(0,i.A)(p.getStructTree());return e.promise.then(e=>{u({type:"RESOLVE",value:e})}).catch(e=>{u({type:"REJECT",error:e})}),()=>(0,A.xL)(e)},[f,p,u]),(0,o.useEffect)(()=>{if(void 0!==s){if(!1===s)return void(d&&(c(!1,d.toString()),t&&t(d)));s&&n&&n(s)}},[s]),s)?(0,r.jsx)(E,{className:"react-pdf__Page__structTree structTree",node:s}):null}let S=p.ng;function R(e){let t=v();(0,l.A)(t,"Unable to find Page context.");let{_className:n,canvasBackground:i,devicePixelRatio:a=(0,A.mZ)(),onRenderError:u,onRenderSuccess:d,page:f,renderForms:p,renderTextLayer:g,rotate:h,scale:y}=Object.assign(Object.assign({},t),e),{canvasRef:m}=e;(0,l.A)(f,"Attempted to render page canvas, but no page was specified.");let E=(0,o.useRef)(null);function b(e){!(0,A.UT)(e)&&(c(!1,e.toString()),u&&u(e))}let R=(0,o.useMemo)(()=>f.getViewport({scale:y*a,rotation:h}),[a,f,h,y]),x=(0,o.useMemo)(()=>f.getViewport({scale:y,rotation:h}),[f,h,y]);(0,o.useEffect)(function(){if(!f)return;f.cleanup();let{current:e}=E;if(!e)return;e.width=R.width,e.height=R.height,e.style.width="".concat(Math.floor(x.width),"px"),e.style.height="".concat(Math.floor(x.height),"px"),e.style.visibility="hidden";let t={annotationMode:p?S.ENABLE_FORMS:S.ENABLE,canvasContext:e.getContext("2d",{alpha:!1}),viewport:R};i&&(t.background=i);let n=f.render(t);return n.promise.then(()=>{e.style.visibility="",f&&d&&d((0,A.vS)(f,y))}).catch(b),()=>(0,A.xL)(n)},[i,f,p,R,x]);let L=(0,o.useCallback)(()=>{let{current:e}=E;e&&(e.width=0,e.height=0)},[]);return(0,o.useEffect)(()=>L,[L]),(0,r.jsx)("canvas",{className:"".concat(n,"__canvas"),dir:"ltr",ref:s(m,E),style:{display:"block",userSelect:"none"},children:g?(0,r.jsx)(w,{}):null})}function x(){let e=v();(0,l.A)(e,"Unable to find Page context.");let{customTextRenderer:t,onGetTextError:n,onGetTextSuccess:a,onRenderTextLayerError:s,onRenderTextLayerSuccess:d,page:f,pageIndex:g,pageNumber:h,rotate:y,scale:m}=e;(0,l.A)(f,"Attempted to load page text content, but no page was specified.");let[E,w]=(0,b.A)(),{value:S,error:R}=E,x=(0,o.useRef)(null);c(1===Number.parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-text-layer"),10),"TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer"),(0,o.useEffect)(function(){w({type:"RESET"})},[f,w]),(0,o.useEffect)(function(){if(!f)return;let e=(0,i.A)(f.getTextContent());return e.promise.then(e=>{w({type:"RESOLVE",value:e})}).catch(e=>{w({type:"REJECT",error:e})}),()=>(0,A.xL)(e)},[f,w]),(0,o.useEffect)(()=>{if(void 0!==S){if(!1===S)return void(R&&(c(!1,R.toString()),n&&n(R)));S&&a&&a(S)}},[S]);let L=(0,o.useCallback)(()=>{d&&d()},[d]),P=(0,o.useCallback)(e=>{c(!1,e.toString()),s&&s(e)},[s]),T=(0,o.useMemo)(()=>f.getViewport({scale:m,rotation:y}),[f,y,m]);return(0,o.useLayoutEffect)(function(){if(!f||!S)return;let{current:e}=x;if(!e)return;e.innerHTML="";let n=f.streamTextContent({includeMarkedContent:!0}),r=new p.D6({container:e,textContentSource:n,viewport:T});return r.render().then(()=>{let n=document.createElement("div");n.className="endOfContent",e.append(n);let r=e.querySelectorAll('[role="presentation"]');if(t){let e=0;S.items.forEach((n,o)=>{if(!("str"in n))return;let i=r[e];i&&(i.innerHTML=t(Object.assign({pageIndex:g,pageNumber:h,itemIndex:o},n)),e+=n.str&&n.hasEOL?2:1)})}L()}).catch(P),()=>(0,A.xL)(r)},[t,P,L,f,g,h,S,T]),(0,r.jsx)("div",{className:(0,u.A)("react-pdf__Page__textContent","textLayer"),onMouseUp:function(){let e=x.current;e&&e.classList.remove("selecting")},onMouseDown:function(){let e=x.current;e&&e.classList.add("selecting")},ref:x})}var L=n(3351);function P(){return(0,o.useContext)(L.A)}function T(){let e=P(),t=v();(0,l.A)(t,"Unable to find Page context.");let{imageResourcesPath:n,linkService:a,onGetAnnotationsError:s,onGetAnnotationsSuccess:d,onRenderAnnotationLayerError:f,onRenderAnnotationLayerSuccess:g,page:h,pdf:y,renderForms:m,rotate:E,scale:w=1}=Object.assign(Object.assign({},e),t);(0,l.A)(y,"Attempted to load page annotations, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop."),(0,l.A)(h,"Attempted to load page annotations, but no page was specified."),(0,l.A)(a,"Attempted to load page annotations, but no linkService was specified.");let[S,R]=(0,b.A)(),{value:x,error:L}=S,T=(0,o.useRef)(null);c(1===Number.parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-annotation-layer"),10),"AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations"),(0,o.useEffect)(function(){R({type:"RESET"})},[R,h]),(0,o.useEffect)(function(){if(!h)return;let e=(0,i.A)(h.getAnnotations());return e.promise.then(e=>{R({type:"RESOLVE",value:e})}).catch(e=>{R({type:"REJECT",error:e})}),()=>{(0,A.xL)(e)}},[R,h]),(0,o.useEffect)(()=>{if(void 0!==x){if(!1===x)return void(L&&(c(!1,L.toString()),s&&s(L)));x&&d&&d(x)}},[x]);let O=(0,o.useMemo)(()=>h.getViewport({scale:w,rotation:E}),[h,E,w]);return(0,o.useEffect)(function(){if(!y||!h||!a||!x)return;let{current:e}=T;if(!e)return;let t=O.clone({dontFlip:!0}),r={annotations:x,annotationStorage:y.annotationStorage,div:e,imageResourcesPath:n,linkService:a,page:h,renderForms:m,viewport:t};e.innerHTML="";try{new p.dU({accessibilityManager:null,annotationCanvasMap:null,annotationEditorUIManager:null,div:e,l10n:null,page:h,structTreeLayer:null,viewport:t}).render(r),g&&g()}catch(e){c(!1,"".concat(e)),f&&f(e)}return()=>{}},[x,n,a,h,y,m,O]),(0,r.jsx)("div",{className:(0,u.A)("react-pdf__Page__annotations","annotationLayer"),ref:T})}var O=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function j(e){let t=Object.assign(Object.assign({},P()),e),{_className:n="react-pdf__Page",_enableRegisterUnregisterPage:p=!0,canvasBackground:g,canvasRef:h,children:y,className:m,customRenderer:E,customTextRenderer:v,devicePixelRatio:w,error:S="Failed to load the page.",height:L,inputRef:j,loading:C="Loading page…",noData:D="No page specified.",onGetAnnotationsError:k,onGetAnnotationsSuccess:_,onGetStructTreeError:M,onGetStructTreeSuccess:N,onGetTextError:V,onGetTextSuccess:I,onLoadError:F,onLoadSuccess:G,onRenderAnnotationLayerError:U,onRenderAnnotationLayerSuccess:B,onRenderError:H,onRenderSuccess:z,onRenderTextLayerError:W,onRenderTextLayerSuccess:q,pageIndex:J,pageNumber:$,pdf:K,registerPage:Y,renderAnnotationLayer:Z=!0,renderForms:Q=!1,renderMode:X="canvas",renderTextLayer:ee=!0,rotate:et,scale:en=1,unregisterPage:er,width:eo}=t,ei=O(t,["_className","_enableRegisterUnregisterPage","canvasBackground","canvasRef","children","className","customRenderer","customTextRenderer","devicePixelRatio","error","height","inputRef","loading","noData","onGetAnnotationsError","onGetAnnotationsSuccess","onGetStructTreeError","onGetStructTreeSuccess","onGetTextError","onGetTextSuccess","onLoadError","onLoadSuccess","onRenderAnnotationLayerError","onRenderAnnotationLayerSuccess","onRenderError","onRenderSuccess","onRenderTextLayerError","onRenderTextLayerSuccess","pageIndex","pageNumber","pdf","registerPage","renderAnnotationLayer","renderForms","renderMode","renderTextLayer","rotate","scale","unregisterPage","width"]),[ea,eu]=(0,b.A)(),{value:es,error:el}=ea,ec=(0,o.useRef)(null);(0,l.A)(K,"Attempted to load a page, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.");let ed=(0,A.ci)($)?$-1:null!=J?J:null,ef=null!=$?$:(0,A.ci)(J)?J+1:null,ep=null!=et?et:es?es.rotate:null,eg=(0,o.useMemo)(()=>{if(!es)return null;let e=1,t=null!=en?en:1;if(eo||L){let t=es.getViewport({scale:1,rotation:ep});eo?e=eo/t.width:L&&(e=L/t.height)}return t*e},[L,es,ep,en,eo]);(0,o.useEffect)(function(){return()=>{(0,A.ci)(ed)&&p&&er&&er(ed)}},[p,K,ed,er]),(0,o.useEffect)(function(){eu({type:"RESET"})},[eu,K,ed]),(0,o.useEffect)(function(){if(!K||!ef)return;let e=(0,i.A)(K.getPage(ef));return e.promise.then(e=>{eu({type:"RESOLVE",value:e})}).catch(e=>{eu({type:"REJECT",error:e})}),()=>(0,A.xL)(e)},[eu,K,ef]),(0,o.useEffect)(()=>{if(void 0!==es){if(!1===es)return void(el&&(c(!1,el.toString()),F&&F(el)));!function(){if(G){if(!es||!eg)return;G((0,A.vS)(es,eg))}if(p&&Y){if(!(0,A.ci)(ed)||!ec.current)return;Y(ed,ec.current)}}()}},[es,eg]);let eh=(0,o.useMemo)(()=>es&&(0,A.ci)(ed)&&ef&&(0,A.ci)(ep)&&(0,A.ci)(eg)?{_className:n,canvasBackground:g,customTextRenderer:v,devicePixelRatio:w,onGetAnnotationsError:k,onGetAnnotationsSuccess:_,onGetStructTreeError:M,onGetStructTreeSuccess:N,onGetTextError:V,onGetTextSuccess:I,onRenderAnnotationLayerError:U,onRenderAnnotationLayerSuccess:B,onRenderError:H,onRenderSuccess:z,onRenderTextLayerError:W,onRenderTextLayerSuccess:q,page:es,pageIndex:ed,pageNumber:ef,renderForms:Q,renderTextLayer:ee,rotate:ep,scale:eg}:null,[n,g,v,w,k,_,M,N,V,I,U,B,H,z,W,q,es,ed,ef,Q,ee,ep,eg]),ey=(0,o.useMemo)(()=>(0,a.Ay)(ei,()=>es?eg?(0,A.vS)(es,eg):void 0:es),[ei,es,eg]),em="".concat(ed,"@").concat(eg,"/").concat(ep);return(0,r.jsx)("div",Object.assign({className:(0,u.A)(n,m),"data-page-number":ef,ref:s(j,ec),style:{"--scale-round-x":"1px","--scale-round-y":"1px","--scale-factor":"1","--user-unit":"".concat(eg),"--total-scale-factor":"calc(var(--scale-factor) * var(--user-unit))",backgroundColor:g||"white",position:"relative",minWidth:"min-content",minHeight:"min-content"}},ey,{children:ef?null===K||null==es?(0,r.jsx)(f.A,{type:"loading",children:"function"==typeof C?C():C}):!1===K||!1===es?(0,r.jsx)(f.A,{type:"error",children:"function"==typeof S?S():S}):(0,r.jsxs)(d.Provider,{value:eh,children:[function(){switch(X){case"custom":return(0,l.A)(E,'renderMode was set to "custom", but no customRenderer was passed.'),(0,r.jsx)(E,{},"".concat(em,"_custom"));case"none":return null;default:return(0,r.jsx)(R,{canvasRef:h},"".concat(em,"_canvas"))}}(),ee?(0,r.jsx)(x,{},"".concat(em,"_text")):null,Z?(0,r.jsx)(T,{},"".concat(em,"_annotations")):null,y]}):(0,r.jsx)(f.A,{type:"no-data",children:"function"==typeof D?D():D})}))}},34266:(e,t,n)=>{n.d(t,{Bd:()=>i,UT:()=>y,ci:()=>u,h1:()=>m,jA:()=>d,mZ:()=>f,mw:()=>s,qC:()=>p,qf:()=>l,vS:()=>h,xL:()=>g,zL:()=>c});var r=n(93179),o=n(94274);let i="undefined"!=typeof window,a=i&&"file:"===window.location.protocol;function u(e){return null!=e}function s(e){return e instanceof ArrayBuffer}function l(e){return(0,r.A)(i,"isBlob can only be used in a browser environment"),e instanceof Blob}function c(e){return"string"==typeof e&&/^data:/.test(e)}function d(e){(0,r.A)(c(e),"Invalid data URI.");let[t="",n=""]=e.split(",");return -1!==t.split(";").indexOf("base64")?atob(n):unescape(n)}function f(){return i&&window.devicePixelRatio||1}function p(){o(!a,"Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.")}function g(e){(null==e?void 0:e.cancel)&&e.cancel()}function h(e,t){return Object.defineProperty(e,"width",{get(){return this.view[2]*t},configurable:!0}),Object.defineProperty(e,"height",{get(){return this.view[3]*t},configurable:!0}),Object.defineProperty(e,"originalWidth",{get(){return this.view[2]},configurable:!0}),Object.defineProperty(e,"originalHeight",{get(){return this.view[3]},configurable:!0}),e}function y(e){return"RenderingCancelledException"===e.name}function m(e){return new Promise((t,n)=>{let r=new FileReader;r.onload=()=>{if(!r.result)return n(Error("Error while reading a file."));t(r.result)},r.onerror=e=>{if(!e.target)return n(Error("Error while reading a file."));let{error:t}=e.target;if(!t)return n(Error("Error while reading a file."));switch(t.code){case t.NOT_FOUND_ERR:return n(Error("Error while reading a file: File not found."));case t.SECURITY_ERR:return n(Error("Error while reading a file: Security error."));case t.ABORT_ERR:return n(Error("Error while reading a file: Aborted."));default:return n(Error("Error while reading a file."))}},r.readAsArrayBuffer(e)})}},50189:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(12115);function o(e,t){switch(t.type){case"RESOLVE":return{value:t.value,error:void 0};case"REJECT":return{value:!1,error:t.error};case"RESET":return{value:void 0,error:void 0};default:return e}}function i(){return(0,r.useReducer)(o,{value:void 0,error:void 0})}},50430:(e,t,n)=>{n.d(t,{A:()=>r});function r(e){let t=!1;return{promise:new Promise((n,r)=>{e.then(e=>!t&&n(e)).catch(e=>!t&&r(e))}),cancel(){t=!0}}}},52596:(e,t,n)=>{n.d(t,{A:()=>r});let r=function(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(n=0;n<i;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}(e))&&(r&&(r+=" "),r+=t);return r}},55653:(e,t,n)=>{n.d(t,{Ay:()=>o});let r=["onCopy","onCut","onPaste","onCompositionEnd","onCompositionStart","onCompositionUpdate","onFocus","onBlur","onInput","onInvalid","onReset","onSubmit","onLoad","onError","onKeyDown","onKeyPress","onKeyUp","onAbort","onCanPlay","onCanPlayThrough","onDurationChange","onEmptied","onEncrypted","onEnded","onError","onLoadedData","onLoadedMetadata","onLoadStart","onPause","onPlay","onPlaying","onProgress","onRateChange","onSeeked","onSeeking","onStalled","onSuspend","onTimeUpdate","onVolumeChange","onWaiting","onClick","onContextMenu","onDoubleClick","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onPointerDown","onPointerMove","onPointerUp","onPointerCancel","onGotPointerCapture","onLostPointerCapture","onPointerEnter","onPointerLeave","onPointerOver","onPointerOut","onScroll","onWheel","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd","onChange","onToggle"];function o(e,t){let n={};for(let o of r){let r=e[o];r&&(t?n[o]=e=>r(e,t(o)):n[o]=r)}return n}},72167:(e,t,n)=>{n.d(t,{A:()=>R});var r=n(95155),o=n(12115),i=n(55653),a=n(50430),u=n(52596),s=n(93179),l=n(94274),c=Object.prototype.hasOwnProperty;function d(e,t,n){for(n of e.keys())if(f(n,t))return n}function f(e,t){var n,r,o;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&f(e[r],t[r]););return -1===r}if(n===Set){if(e.size!==t.size)return!1;for(r of e)if((o=r)&&"object"==typeof o&&!(o=d(t,o))||!t.has(o))return!1;return!0}if(n===Map){if(e.size!==t.size)return!1;for(r of e)if((o=r[0])&&"object"==typeof o&&!(o=d(t,o))||!f(r[1],t.get(o)))return!1;return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((r=e.byteLength)===t.byteLength)for(;r--&&e.getInt8(r)===t.getInt8(r););return -1===r}if(ArrayBuffer.isView(e)){if((r=e.byteLength)===t.byteLength)for(;r--&&e[r]===t[r];);return -1===r}if(!n||"object"==typeof e){for(n in r=0,e)if(c.call(e,n)&&++r&&!c.call(t,n)||!(n in t)||!f(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!=e&&t!=t}var p=n(54014),g=n(3351),h=n(89448);class y{constructor(){this.externalLinkEnabled=!0,this.externalLinkRel=void 0,this.externalLinkTarget=void 0,this.isInPresentationMode=!1,this.pdfDocument=void 0,this.pdfViewer=void 0}setDocument(e){this.pdfDocument=e}setViewer(e){this.pdfViewer=e}setExternalLinkRel(e){this.externalLinkRel=e}setExternalLinkTarget(e){this.externalLinkTarget=e}setHistory(){}get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0}get page(){return(0,s.A)(this.pdfViewer,"PDF viewer is not initialized."),this.pdfViewer.currentPageNumber||0}set page(e){(0,s.A)(this.pdfViewer,"PDF viewer is not initialized."),this.pdfViewer.currentPageNumber=e}get rotation(){return 0}set rotation(e){}goToDestination(e){return new Promise(t=>{(0,s.A)(this.pdfDocument,"PDF document not loaded."),(0,s.A)(e,"Destination is not specified."),"string"==typeof e?this.pdfDocument.getDestination(e).then(t):Array.isArray(e)?t(e):e.then(t)}).then(e=>{(0,s.A)(Array.isArray(e),`"${e}" is not a valid destination array.`);let t=e[0];new Promise(e=>{(0,s.A)(this.pdfDocument,"PDF document not loaded."),t instanceof Object?this.pdfDocument.getPageIndex(t).then(t=>{e(t)}).catch(()=>{(0,s.A)(!1,`"${t}" is not a valid page reference.`)}):"number"==typeof t?e(t):(0,s.A)(!1,`"${t}" is not a valid destination reference.`)}).then(t=>{let n=t+1;(0,s.A)(this.pdfViewer,"PDF viewer is not initialized."),(0,s.A)(n>=1&&n<=this.pagesCount,`"${n}" is not a valid page number.`),this.pdfViewer.scrollPageIntoView({dest:e,pageIndex:t,pageNumber:n})})})}navigateTo(e){this.goToDestination(e)}goToPage(e){(0,s.A)(this.pdfViewer,"PDF viewer is not initialized."),(0,s.A)(e>=1&&e<=this.pagesCount,`"${e}" is not a valid page number.`),this.pdfViewer.scrollPageIntoView({pageIndex:e-1,pageNumber:e})}addLinkAttributes(e,t,n){e.href=t,e.rel=this.externalLinkRel||"noopener noreferrer nofollow",e.target=n?"_blank":this.externalLinkTarget||""}getDestinationHash(){return"#"}getAnchorUrl(){return"#"}setHash(){}executeNamedAction(){}cachePageRef(){}isPageVisible(){return!0}isPageCached(){return!0}executeSetOCGState(){}}let m={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};var E=n(34266),v=n(50189),b=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let{Tm:A}=p,w=(e,t)=>{switch(t){case m.NEED_PASSWORD:e(prompt("Enter the password to open this PDF file."));break;case m.INCORRECT_PASSWORD:e(prompt("Invalid password. Please try again."))}};function S(e){return"object"==typeof e&&null!==e&&("data"in e||"range"in e||"url"in e)}let R=(0,o.forwardRef)(function(e,t){var{children:n,className:c,error:d="Failed to load PDF file.",externalLinkRel:m,externalLinkTarget:R,file:x,inputRef:L,imageResourcesPath:P,loading:T="Loading PDF…",noData:O="No PDF file specified.",onItemClick:j,onLoadError:C,onLoadProgress:D,onLoadSuccess:k,onPassword:_=w,onSourceError:M,onSourceSuccess:N,options:V,renderMode:I,rotate:F,scale:G}=e,U=b(e,["children","className","error","externalLinkRel","externalLinkTarget","file","inputRef","imageResourcesPath","loading","noData","onItemClick","onLoadError","onLoadProgress","onLoadSuccess","onPassword","onSourceError","onSourceSuccess","options","renderMode","rotate","scale"]);let[B,H]=(0,v.A)(),{value:z,error:W}=B,[q,J]=(0,v.A)(),{value:$,error:K}=q,Y=(0,o.useRef)(new y),Z=(0,o.useRef)([]),Q=(0,o.useRef)(void 0),X=(0,o.useRef)(void 0);x&&x!==Q.current&&S(x)&&(l(!f(x,Q.current),'File prop passed to <Document /> changed, but it\'s equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "file" prop.'),Q.current=x),V&&V!==X.current&&(l(!f(V,X.current),'Options prop passed to <Document /> changed, but it\'s equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "options" prop.'),X.current=V);let ee=(0,o.useRef)({scrollPageIntoView:e=>{let{dest:t,pageNumber:n,pageIndex:r=n-1}=e;if(j)return void j({dest:t,pageIndex:r,pageNumber:n});let o=Z.current[r];if(o)return void o.scrollIntoView();l(!1,"An internal link leading to page ".concat(n," was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>."))}});(0,o.useImperativeHandle)(t,()=>({linkService:Y,pages:Z,viewer:ee}),[]),(0,o.useEffect)(function(){H({type:"RESET"})},[x,H]);let et=(0,o.useCallback)(async()=>{if(!x)return null;if("string"==typeof x)return(0,E.zL)(x)?{data:(0,E.jA)(x)}:((0,E.qC)(),{url:x});if(x instanceof A)return{range:x};if((0,E.mw)(x))return{data:x};if(E.Bd&&(0,E.qf)(x))return{data:await (0,E.h1)(x)};if((0,s.A)("object"==typeof x,"Invalid parameter in file, need either Uint8Array, string or a parameter object"),(0,s.A)(S(x),"Invalid parameter object: need either .data, .range or .url"),"url"in x&&"string"==typeof x.url){if((0,E.zL)(x.url)){let{url:e}=x,t=b(x,["url"]);return Object.assign({data:(0,E.jA)(e)},t)}(0,E.qC)()}return x},[x]);(0,o.useEffect)(()=>{let e=(0,a.A)(et());return e.promise.then(e=>{H({type:"RESOLVE",value:e})}).catch(e=>{H({type:"REJECT",error:e})}),()=>{(0,E.xL)(e)}},[et,H]),(0,o.useEffect)(()=>{if(void 0!==z){if(!1===z)return void(W&&(l(!1,W.toString()),M&&M(W)));N&&N()}},[z]),(0,o.useEffect)(function(){J({type:"RESET"})},[J,z]),(0,o.useEffect)(function(){if(!z)return;let e=V?Object.assign(Object.assign({},z),V):z,t=p.YE(e);D&&(t.onProgress=D),_&&(t.onPassword=_);let n=t.promise.then(e=>{J({type:"RESOLVE",value:e})}).catch(e=>{t.destroyed||J({type:"REJECT",error:e})});return()=>{n.finally(()=>t.destroy())}},[V,J,z]),(0,o.useEffect)(()=>{if(void 0!==$){if(!1===$)return void(K&&(l(!1,K.toString()),C&&C(K)));$&&(k&&k($),Z.current=Array($.numPages),Y.current.setDocument($))}},[$]),(0,o.useEffect)(function(){Y.current.setViewer(ee.current),Y.current.setExternalLinkRel(m),Y.current.setExternalLinkTarget(R)},[m,R]);let en=(0,o.useCallback)((e,t)=>{Z.current[e]=t},[]),er=(0,o.useCallback)(e=>{delete Z.current[e]},[]),eo=(0,o.useMemo)(()=>({imageResourcesPath:P,linkService:Y.current,onItemClick:j,pdf:$,registerPage:en,renderMode:I,rotate:F,scale:G,unregisterPage:er}),[P,j,$,en,I,F,G,er]),ei=(0,o.useMemo)(()=>(0,i.Ay)(U,()=>$),[U,$]);return(0,r.jsx)("div",Object.assign({className:(0,u.A)("react-pdf__Document",c),ref:L},ei,{children:x?null==$?(0,r.jsx)(h.A,{type:"loading",children:"function"==typeof T?T():T}):!1===$?(0,r.jsx)(h.A,{type:"error",children:"function"==typeof d?d():d}):(0,r.jsx)(g.A.Provider,{value:eo,children:n}):(0,r.jsx)(h.A,{type:"no-data",children:"function"==typeof O?O():O})}))})},89448:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(95155);function o({children:e,type:t}){return(0,r.jsx)("div",{className:`react-pdf__message react-pdf__message--${t}`,children:e})}},93179:(e,t,n)=>{n.d(t,{A:()=>r});function r(e,t){if(!e)throw Error("Invariant failed")}},94274:e=>{e.exports=function(){}}}]);
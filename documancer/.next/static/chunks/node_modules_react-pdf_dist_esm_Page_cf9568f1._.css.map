{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css"], "sourcesContent": ["/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n:root {\n  --react-pdf-annotation-layer: 1;\n  --annotation-unfocused-field-background: url(\"data:image/svg+xml;charset=UTF-8,<svg width='1px' height='1px' xmlns='http://www.w3.org/2000/svg'><rect width='100%' height='100%' style='fill:rgba(0, 54, 255, 0.13);'/></svg>\");\n  --input-focus-border-color: Highlight;\n  --input-focus-outline: 1px solid Canvas;\n  --input-unfocused-border-color: transparent;\n  --input-disabled-border-color: transparent;\n  --input-hover-border-color: black;\n  --link-outline: none;\n}\n\n@media screen and (forced-colors: active) {\n  :root {\n    --input-focus-border-color: CanvasText;\n    --input-unfocused-border-color: ActiveText;\n    --input-disabled-border-color: GrayText;\n    --input-hover-border-color: Highlight;\n    --link-outline: 1.5px solid LinkText;\n  }\n  .annotationLayer .textWidgetAnnotation :is(input, textarea):required,\n  .annotationLayer .choiceWidgetAnnotation select:required,\n  .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:required {\n    outline: 1.5px solid selectedItem;\n  }\n\n  .annotationLayer .linkAnnotation:hover {\n    backdrop-filter: invert(100%);\n  }\n}\n\n.annotationLayer {\n  position: absolute;\n  top: 0;\n  left: 0;\n  pointer-events: none;\n  transform-origin: 0 0;\n  z-index: 3;\n}\n\n.annotationLayer[data-main-rotation='90'] .norotate {\n  transform: rotate(270deg) translateX(-100%);\n}\n.annotationLayer[data-main-rotation='180'] .norotate {\n  transform: rotate(180deg) translate(-100%, -100%);\n}\n.annotationLayer[data-main-rotation='270'] .norotate {\n  transform: rotate(90deg) translateY(-100%);\n}\n\n.annotationLayer canvas {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n}\n\n.annotationLayer section {\n  position: absolute;\n  text-align: initial;\n  pointer-events: auto;\n  box-sizing: border-box;\n  margin: 0;\n  transform-origin: 0 0;\n}\n\n.annotationLayer .linkAnnotation {\n  outline: var(--link-outline);\n}\n\n.annotationLayer :is(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a {\n  position: absolute;\n  font-size: 1em;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.annotationLayer :is(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a:hover {\n  opacity: 0.2;\n  background: rgba(255, 255, 0, 1);\n  box-shadow: 0 2px 10px rgba(255, 255, 0, 1);\n}\n\n.annotationLayer .textAnnotation img {\n  position: absolute;\n  cursor: pointer;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n}\n\n.annotationLayer .textWidgetAnnotation :is(input, textarea),\n.annotationLayer .choiceWidgetAnnotation select,\n.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input {\n  background-image: var(--annotation-unfocused-field-background);\n  border: 2px solid var(--input-unfocused-border-color);\n  box-sizing: border-box;\n  font: calc(9px * var(--scale-factor)) sans-serif;\n  height: 100%;\n  margin: 0;\n  vertical-align: top;\n  width: 100%;\n}\n\n.annotationLayer .textWidgetAnnotation :is(input, textarea):required,\n.annotationLayer .choiceWidgetAnnotation select:required,\n.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:required {\n  outline: 1.5px solid red;\n}\n\n.annotationLayer .choiceWidgetAnnotation select option {\n  padding: 0;\n}\n\n.annotationLayer .buttonWidgetAnnotation.radioButton input {\n  border-radius: 50%;\n}\n\n.annotationLayer .textWidgetAnnotation textarea {\n  resize: none;\n}\n\n.annotationLayer .textWidgetAnnotation :is(input, textarea)[disabled],\n.annotationLayer .choiceWidgetAnnotation select[disabled],\n.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input[disabled] {\n  background: none;\n  border: 2px solid var(--input-disabled-border-color);\n  cursor: not-allowed;\n}\n\n.annotationLayer .textWidgetAnnotation :is(input, textarea):hover,\n.annotationLayer .choiceWidgetAnnotation select:hover,\n.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:hover {\n  border: 2px solid var(--input-hover-border-color);\n}\n.annotationLayer .textWidgetAnnotation :is(input, textarea):hover,\n.annotationLayer .choiceWidgetAnnotation select:hover,\n.annotationLayer .buttonWidgetAnnotation.checkBox input:hover {\n  border-radius: 2px;\n}\n\n.annotationLayer .textWidgetAnnotation :is(input, textarea):focus,\n.annotationLayer .choiceWidgetAnnotation select:focus {\n  background: none;\n  border: 2px solid var(--input-focus-border-color);\n  border-radius: 2px;\n  outline: var(--input-focus-outline);\n}\n\n.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) :focus {\n  background-image: none;\n  background-color: transparent;\n}\n\n.annotationLayer .buttonWidgetAnnotation.checkBox :focus {\n  border: 2px solid var(--input-focus-border-color);\n  border-radius: 2px;\n  outline: var(--input-focus-outline);\n}\n\n.annotationLayer .buttonWidgetAnnotation.radioButton :focus {\n  border: 2px solid var(--input-focus-border-color);\n  outline: var(--input-focus-outline);\n}\n\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before,\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after,\n.annotationLayer .buttonWidgetAnnotation.radioButton input:checked::before {\n  background-color: CanvasText;\n  content: '';\n  display: block;\n  position: absolute;\n}\n\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before,\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after {\n  height: 80%;\n  left: 45%;\n  width: 1px;\n}\n\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before {\n  transform: rotate(45deg);\n}\n\n.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after {\n  transform: rotate(-45deg);\n}\n\n.annotationLayer .buttonWidgetAnnotation.radioButton input:checked::before {\n  border-radius: 50%;\n  height: 50%;\n  left: 30%;\n  top: 20%;\n  width: 50%;\n}\n\n.annotationLayer .textWidgetAnnotation input.comb {\n  font-family: monospace;\n  padding-left: 2px;\n  padding-right: 0;\n}\n\n.annotationLayer .textWidgetAnnotation input.comb:focus {\n  /*\n   * Letter spacing is placed on the right side of each character. Hence, the\n   * letter spacing of the last character may be placed outside the visible\n   * area, causing horizontal scrolling. We avoid this by extending the width\n   * when the element has focus and revert this when it loses focus.\n   */\n  width: 103%;\n}\n\n.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input {\n  appearance: none;\n}\n\n.annotationLayer .popupTriggerArea {\n  height: 100%;\n  width: 100%;\n}\n\n.annotationLayer .fileAttachmentAnnotation .popupTriggerArea {\n  position: absolute;\n}\n\n.annotationLayer .popupWrapper {\n  position: absolute;\n  font-size: calc(9px * var(--scale-factor));\n  width: 100%;\n  min-width: calc(180px * var(--scale-factor));\n  pointer-events: none;\n}\n\n.annotationLayer .popup {\n  position: absolute;\n  max-width: calc(180px * var(--scale-factor));\n  background-color: rgba(255, 255, 153, 1);\n  box-shadow: 0 calc(2px * var(--scale-factor)) calc(5px * var(--scale-factor))\n    rgba(136, 136, 136, 1);\n  border-radius: calc(2px * var(--scale-factor));\n  padding: calc(6px * var(--scale-factor));\n  margin-left: calc(5px * var(--scale-factor));\n  cursor: pointer;\n  font: message-box;\n  white-space: normal;\n  word-wrap: break-word;\n  pointer-events: auto;\n}\n\n.annotationLayer .popup > * {\n  font-size: calc(9px * var(--scale-factor));\n}\n\n.annotationLayer .popup h1 {\n  display: inline-block;\n}\n\n.annotationLayer .popupDate {\n  display: inline-block;\n  margin-left: calc(5px * var(--scale-factor));\n}\n\n.annotationLayer .popupContent {\n  border-top: 1px solid rgba(51, 51, 51, 1);\n  margin-top: calc(2px * var(--scale-factor));\n  padding-top: calc(2px * var(--scale-factor));\n}\n\n.annotationLayer .richText > * {\n  white-space: pre-wrap;\n  font-size: calc(9px * var(--scale-factor));\n}\n\n.annotationLayer .highlightAnnotation,\n.annotationLayer .underlineAnnotation,\n.annotationLayer .squigglyAnnotation,\n.annotationLayer .strikeoutAnnotation,\n.annotationLayer .freeTextAnnotation,\n.annotationLayer .lineAnnotation svg line,\n.annotationLayer .squareAnnotation svg rect,\n.annotationLayer .circleAnnotation svg ellipse,\n.annotationLayer .polylineAnnotation svg polyline,\n.annotationLayer .polygonAnnotation svg polygon,\n.annotationLayer .caretAnnotation,\n.annotationLayer .inkAnnotation svg polyline,\n.annotationLayer .stampAnnotation,\n.annotationLayer .fileAttachmentAnnotation {\n  cursor: pointer;\n}\n\n.annotationLayer section svg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n}\n\n.annotationLayer .annotationTextContent {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  opacity: 0;\n  color: transparent;\n  user-select: none;\n  pointer-events: none;\n}\n\n.annotationLayer .annotationTextContent span {\n  width: 100%;\n  display: inline-block;\n}\n"], "names": [], "mappings": "AAeA;;;;;;;;;;;AAWA;EACE;;;;;;;;EAOA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAMA;;;;;;AAKF;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAIA;;;;;;AAMA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AASA;;;;;;AAAA;;;;;;AAAA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;;;;AAAA;;;;;;;;;;;AAAA;;;;;;;;;;;AAAA;;;;;;;;;;;AAAA;;;;;;;;;;;AAAA;;;;;;;;;;;AAAA;;;;;;;;;;;AAaA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAQA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAKA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAMA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAQA;;;;;AAAA;;;;;AAAA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;;AASA;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;;AAMA;;;;AAUA;;;;;;AAAA;;;;;;AAAA;;;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAgBA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;AAiBA;;;;;;;;AAQA;;;;;;;;;;;;AAUA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/react-pdf/dist/esm/Page/TextLayer.css"], "sourcesContent": ["/* Copyright 2014 Mozilla Foundation\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n:root {\n  --react-pdf-text-layer: 1;\n  --highlight-bg-color: rgba(180, 0, 170, 1);\n  --highlight-selected-bg-color: rgba(0, 100, 0, 1);\n}\n\n@media screen and (forced-colors: active) {\n  :root {\n    --highlight-bg-color: Highlight;\n    --highlight-selected-bg-color: ButtonText;\n  }\n}\n\n[data-main-rotation='90'] {\n  transform: rotate(90deg) translateY(-100%);\n}\n[data-main-rotation='180'] {\n  transform: rotate(180deg) translate(-100%, -100%);\n}\n[data-main-rotation='270'] {\n  transform: rotate(270deg) translateX(-100%);\n}\n\n.textLayer {\n  position: absolute;\n  text-align: initial;\n  inset: 0;\n  overflow: hidden;\n  line-height: 1;\n  text-size-adjust: none;\n  forced-color-adjust: none;\n  transform-origin: 0 0;\n  z-index: 2;\n}\n\n.textLayer :is(span, br) {\n  color: transparent;\n  position: absolute;\n  white-space: pre;\n  cursor: text;\n  margin: 0;\n  transform-origin: 0 0;\n}\n\n/* Only necessary in Google Chrome, see issue 14205, and most unfortunately\n * the problem doesn't show up in \"text\" reference tests. */\n.textLayer span.markedContent {\n  top: 0;\n  height: 0;\n}\n\n.textLayer .highlight {\n  margin: -1px;\n  padding: 1px;\n  background-color: var(--highlight-bg-color);\n  border-radius: 4px;\n}\n\n.textLayer .highlight.appended {\n  position: initial;\n}\n\n.textLayer .highlight.begin {\n  border-radius: 4px 0 0 4px;\n}\n\n.textLayer .highlight.end {\n  border-radius: 0 4px 4px 0;\n}\n\n.textLayer .highlight.middle {\n  border-radius: 0;\n}\n\n.textLayer .highlight.selected {\n  background-color: var(--highlight-selected-bg-color);\n}\n\n/* Avoids https://github.com/mozilla/pdf.js/issues/13840 in Chrome */\n.textLayer br::selection {\n  background: transparent;\n}\n\n.textLayer .endOfContent {\n  display: block;\n  position: absolute;\n  inset: 100% 0 0;\n  z-index: -1;\n  cursor: default;\n  user-select: none;\n}\n\n.textLayer .endOfContent.active {\n  top: 0;\n}\n\n.hiddenCanvasElement {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 0;\n  height: 0;\n  display: none;\n}\n"], "names": [], "mappings": "AAeA;;;;;;AAMA;EACE;;;;;;AAMF;;;;AAGA;;;;AAGA;;;;AAIA;;;;;;;;;;;;;;;;AAYA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAWA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;AASA;;;;AAIA", "ignoreList": [0], "debugId": null}}]}
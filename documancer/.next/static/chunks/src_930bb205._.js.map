{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/constants.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_CONFIG = {\n  name: 'DocuMancer',\n  version: '1.0.0',\n  description: 'AI-Powered Academic Paper Reading Assistant',\n  maxFileSize: 50 * 1024 * 1024, // 50MB\n  allowedFileTypes: ['.pdf'],\n  supportedFormats: ['PDF'],\n} as const;\n\nexport const COLORS = {\n  primary: '#1890ff',\n  secondary: '#722ed1',\n  success: '#52c41a',\n  warning: '#faad14',\n  error: '#ff4d4f',\n  text: {\n    primary: '#262626',\n    secondary: '#595959',\n    disabled: '#bfbfbf',\n  },\n  background: {\n    primary: '#ffffff',\n    secondary: '#fafafa',\n    tertiary: '#f5f5f5',\n  },\n  border: '#d9d9d9',\n} as const;\n\nexport const BREAKPOINTS = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600,\n} as const;\n\nexport const ROUTES = {\n  home: '/',\n  library: '/library',\n  reader: '/reader',\n  comparison: '/comparison',\n  analysis: '/analysis',\n  settings: '/settings',\n} as const;\n\nexport const API_ENDPOINTS = {\n  papers: '/api/papers',\n  upload: '/api/upload',\n  chat: '/api/chat',\n  analysis: '/api/analysis',\n  search: '/api/search',\n  comparison: '/api/comparison',\n} as const;\n\nexport const PAPER_FORMATS = {\n  ARXIV: 'arXiv',\n  IEEE: 'IEEE',\n  ACM: 'ACM',\n  SPRINGER: 'Springer',\n  ELSEVIER: 'Elsevier',\n  GENERIC: 'Generic',\n} as const;\n\nexport const ANALYSIS_TYPES = {\n  SUMMARY: 'summary',\n  KEY_FINDINGS: 'key_findings',\n  METHODOLOGY: 'methodology',\n  CONCEPTS: 'concepts',\n  CITATIONS: 'citations',\n  COMPARISON: 'comparison',\n} as const;\n\nexport const MESSAGE_TYPES = {\n  USER: 'user',\n  ASSISTANT: 'assistant',\n  SYSTEM: 'system',\n} as const;\n\nexport const ANNOTATION_TYPES = {\n  HIGHLIGHT: 'highlight',\n  NOTE: 'note',\n  BOOKMARK: 'bookmark',\n} as const;\n\nexport const VIEW_MODES = {\n  READER: 'reader',\n  LIBRARY: 'library',\n  COMPARISON: 'comparison',\n  ANALYSIS: 'analysis',\n} as const;\n\nexport const LOADING_MESSAGES = [\n  'Processing your document...',\n  'Extracting text content...',\n  'Analyzing paper structure...',\n  'Generating insights...',\n  'Almost ready...',\n] as const;\n\nexport const ERROR_MESSAGES = {\n  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',\n  INVALID_FILE_TYPE: 'Only PDF files are supported',\n  UPLOAD_FAILED: 'Failed to upload file. Please try again.',\n  PROCESSING_FAILED: 'Failed to process the document',\n  API_ERROR: 'An error occurred while communicating with the server',\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  GENERIC_ERROR: 'An unexpected error occurred',\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;;;;AAEjB,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,aAAa;IACb,aAAa,KAAK,OAAO;IACzB,kBAAkB;QAAC;KAAO;IAC1B,kBAAkB;QAAC;KAAM;AAC3B;AAEO,MAAM,SAAS;IACpB,SAAS;IACT,WAAW;IACX,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;QACJ,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,YAAY;QACV,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,QAAQ;AACV;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;AACP;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,SAAS;IACT,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,UAAU;AACZ;AAEO,MAAM,gBAAgB;IAC3B,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,UAAU;IACV,QAAQ;IACR,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,OAAO;IACP,MAAM;IACN,KAAK;IACL,UAAU;IACV,UAAU;IACV,SAAS;AACX;AAEO,MAAM,iBAAiB;IAC5B,SAAS;IACT,cAAc;IACd,aAAa;IACb,UAAU;IACV,WAAW;IACX,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,MAAM;IACN,WAAW;IACX,QAAQ;AACV;AAEO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,UAAU;AACZ;AAEO,MAAM,aAAa;IACxB,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,UAAU;AACZ;AAEO,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,mBAAmB;IACnB,eAAe;IACf,mBAAmB;IACnB,WAAW;IACX,eAAe;IACf,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/store/useAppStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { Paper, ChatMessage, UIState, ReadingSession, Annotation } from '@/lib/types';\nimport { VIEW_MODES } from '@/lib/constants';\n\ninterface AppState extends UIState {\n  // Papers\n  papers: Paper[];\n  currentPaperId: string | null;\n  \n  // Chat\n  chatMessages: ChatMessage[];\n  \n  // Reading sessions\n  readingSessions: ReadingSession[];\n  currentSession: ReadingSession | null;\n  \n  // Annotations\n  annotations: Annotation[];\n  \n  // Actions\n  setPapers: (papers: Paper[]) => void;\n  addPaper: (paper: Paper) => void;\n  removePaper: (paperId: string) => void;\n  setCurrentPaper: (paperId: string | null) => void;\n  \n  addChatMessage: (message: ChatMessage) => void;\n  clearChatMessages: () => void;\n  \n  setCurrentView: (view: UIState['currentView']) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSidebarCollapsed: (collapsed: boolean) => void;\n  \n  addAnnotation: (annotation: Annotation) => void;\n  removeAnnotation: (annotationId: string) => void;\n  updateAnnotation: (annotationId: string, updates: Partial<Annotation>) => void;\n  \n  startReadingSession: (paperId: string) => void;\n  endReadingSession: () => void;\n  updateReadingProgress: (progress: number) => void;\n  \n  selectPaper: (paperId: string) => void;\n  deselectPaper: (paperId: string) => void;\n  clearSelectedPapers: () => void;\n}\n\nexport const useAppStore = create<AppState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        // Initial state\n        papers: [],\n        currentPaperId: null,\n        currentPaper: null,\n        selectedPapers: [],\n        chatMessages: [],\n        readingSessions: [],\n        currentSession: null,\n        annotations: [],\n        isLoading: false,\n        error: null,\n        sidebarCollapsed: false,\n        currentView: VIEW_MODES.LIBRARY,\n        \n        // Paper actions\n        setPapers: (papers) => set({ papers }),\n        \n        addPaper: (paper) => set((state) => ({\n          papers: [...state.papers, paper]\n        })),\n        \n        removePaper: (paperId) => set((state) => ({\n          papers: state.papers.filter(p => p.id !== paperId),\n          selectedPapers: state.selectedPapers.filter(id => id !== paperId),\n          currentPaperId: state.currentPaperId === paperId ? null : state.currentPaperId,\n          currentPaper: state.currentPaperId === paperId ? null : state.currentPaper,\n        })),\n        \n        setCurrentPaper: (paperId) => {\n          const paper = paperId ? get().papers.find(p => p.id === paperId) : null;\n          set({ \n            currentPaperId: paperId, \n            currentPaper: paper || null \n          });\n        },\n        \n        // Chat actions\n        addChatMessage: (message) => set((state) => ({\n          chatMessages: [...state.chatMessages, message]\n        })),\n        \n        clearChatMessages: () => set({ chatMessages: [] }),\n        \n        // UI actions\n        setCurrentView: (view) => set({ currentView: view }),\n        setLoading: (isLoading) => set({ isLoading }),\n        setError: (error) => set({ error }),\n        setSidebarCollapsed: (sidebarCollapsed) => set({ sidebarCollapsed }),\n        \n        // Annotation actions\n        addAnnotation: (annotation) => set((state) => ({\n          annotations: [...state.annotations, annotation]\n        })),\n        \n        removeAnnotation: (annotationId) => set((state) => ({\n          annotations: state.annotations.filter(a => a.id !== annotationId)\n        })),\n        \n        updateAnnotation: (annotationId, updates) => set((state) => ({\n          annotations: state.annotations.map(a => \n            a.id === annotationId ? { ...a, ...updates } : a\n          )\n        })),\n        \n        // Reading session actions\n        startReadingSession: (paperId) => {\n          const session: ReadingSession = {\n            id: `session_${Date.now()}`,\n            paperId,\n            startTime: new Date(),\n            progress: 0,\n            notes: [],\n            bookmarks: [],\n          };\n          set((state) => ({\n            currentSession: session,\n            readingSessions: [...state.readingSessions, session]\n          }));\n        },\n        \n        endReadingSession: () => set((state) => {\n          if (state.currentSession) {\n            const updatedSession = {\n              ...state.currentSession,\n              endTime: new Date()\n            };\n            return {\n              currentSession: null,\n              readingSessions: state.readingSessions.map(s => \n                s.id === updatedSession.id ? updatedSession : s\n              )\n            };\n          }\n          return state;\n        }),\n        \n        updateReadingProgress: (progress) => set((state) => {\n          if (state.currentSession) {\n            const updatedSession = { ...state.currentSession, progress };\n            return {\n              currentSession: updatedSession,\n              readingSessions: state.readingSessions.map(s => \n                s.id === updatedSession.id ? updatedSession : s\n              )\n            };\n          }\n          return state;\n        }),\n        \n        // Selection actions\n        selectPaper: (paperId) => set((state) => ({\n          selectedPapers: state.selectedPapers.includes(paperId) \n            ? state.selectedPapers \n            : [...state.selectedPapers, paperId]\n        })),\n        \n        deselectPaper: (paperId) => set((state) => ({\n          selectedPapers: state.selectedPapers.filter(id => id !== paperId)\n        })),\n        \n        clearSelectedPapers: () => set({ selectedPapers: [] }),\n      }),\n      {\n        name: 'documancer-store',\n        partialize: (state) => ({\n          papers: state.papers,\n          readingSessions: state.readingSessions,\n          annotations: state.annotations,\n          sidebarCollapsed: state.sidebarCollapsed,\n        }),\n      }\n    ),\n    { name: 'documancer-store' }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AA4CO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,QAAQ,EAAE;QACV,gBAAgB;QAChB,cAAc;QACd,gBAAgB,EAAE;QAClB,cAAc,EAAE;QAChB,iBAAiB,EAAE;QACnB,gBAAgB;QAChB,aAAa,EAAE;QACf,WAAW;QACX,OAAO;QACP,kBAAkB;QAClB,aAAa,0HAAA,CAAA,aAAU,CAAC,OAAO;QAE/B,gBAAgB;QAChB,WAAW,CAAC,SAAW,IAAI;gBAAE;YAAO;QAEpC,UAAU,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBACnC,QAAQ;2BAAI,MAAM,MAAM;wBAAE;qBAAM;gBAClC,CAAC;QAED,aAAa,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBACxC,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC1C,gBAAgB,MAAM,cAAc,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;oBACzD,gBAAgB,MAAM,cAAc,KAAK,UAAU,OAAO,MAAM,cAAc;oBAC9E,cAAc,MAAM,cAAc,KAAK,UAAU,OAAO,MAAM,YAAY;gBAC5E,CAAC;QAED,iBAAiB,CAAC;YAChB,MAAM,QAAQ,UAAU,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW;YACnE,IAAI;gBACF,gBAAgB;gBAChB,cAAc,SAAS;YACzB;QACF;QAEA,eAAe;QACf,gBAAgB,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3C,cAAc;2BAAI,MAAM,YAAY;wBAAE;qBAAQ;gBAChD,CAAC;QAED,mBAAmB,IAAM,IAAI;gBAAE,cAAc,EAAE;YAAC;QAEhD,aAAa;QACb,gBAAgB,CAAC,OAAS,IAAI;gBAAE,aAAa;YAAK;QAClD,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,qBAAqB,CAAC,mBAAqB,IAAI;gBAAE;YAAiB;QAElE,qBAAqB;QACrB,eAAe,CAAC,aAAe,IAAI,CAAC,QAAU,CAAC;oBAC7C,aAAa;2BAAI,MAAM,WAAW;wBAAE;qBAAW;gBACjD,CAAC;QAED,kBAAkB,CAAC,eAAiB,IAAI,CAAC,QAAU,CAAC;oBAClD,aAAa,MAAM,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACtD,CAAC;QAED,kBAAkB,CAAC,cAAc,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,aAAa,MAAM,WAAW,CAAC,GAAG,CAAC,CAAA,IACjC,EAAE,EAAE,KAAK,eAAe;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEnD,CAAC;QAED,0BAA0B;QAC1B,qBAAqB,CAAC;YACpB,MAAM,UAA0B;gBAC9B,IAAI,AAAC,WAAqB,OAAX,KAAK,GAAG;gBACvB;gBACA,WAAW,IAAI;gBACf,UAAU;gBACV,OAAO,EAAE;gBACT,WAAW,EAAE;YACf;YACA,IAAI,CAAC,QAAU,CAAC;oBACd,gBAAgB;oBAChB,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;qBAAQ;gBACtD,CAAC;QACH;QAEA,mBAAmB,IAAM,IAAI,CAAC;gBAC5B,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,iBAAiB;wBACrB,GAAG,MAAM,cAAc;wBACvB,SAAS,IAAI;oBACf;oBACA,OAAO;wBACL,gBAAgB;wBAChB,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,eAAe,EAAE,GAAG,iBAAiB;oBAElD;gBACF;gBACA,OAAO;YACT;QAEA,uBAAuB,CAAC,WAAa,IAAI,CAAC;gBACxC,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,iBAAiB;wBAAE,GAAG,MAAM,cAAc;wBAAE;oBAAS;oBAC3D,OAAO;wBACL,gBAAgB;wBAChB,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,eAAe,EAAE,GAAG,iBAAiB;oBAElD;gBACF;gBACA,OAAO;YACT;QAEA,oBAAoB;QACpB,aAAa,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBACxC,gBAAgB,MAAM,cAAc,CAAC,QAAQ,CAAC,WAC1C,MAAM,cAAc,GACpB;2BAAI,MAAM,cAAc;wBAAE;qBAAQ;gBACxC,CAAC;QAED,eAAe,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC1C,gBAAgB,MAAM,cAAc,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;gBAC3D,CAAC;QAED,qBAAqB,IAAM,IAAI;gBAAE,gBAAgB,EAAE;YAAC;IACtD,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,QAAQ,MAAM,MAAM;YACpB,iBAAiB,MAAM,eAAe;YACtC,aAAa,MAAM,WAAW;YAC9B,kBAAkB,MAAM,gBAAgB;QAC1C,CAAC;AACH,IAEF;IAAE,MAAM;AAAmB", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Layout, Menu, Button, Avatar, Dropdown, Space, Typography, Badge, Drawer } from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  BookOutlined,\n  FileTextOutlined,\n  Bar<PERSON>hartOutlined,\n  SettingOutlined,\n  UserOutlined,\n  BellOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store/useAppStore';\nimport { VIEW_MODES } from '@/lib/constants';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const { \n    sidebarCollapsed, \n    setSidebarCollapsed, \n    currentView, \n    setCurrentView,\n    papers\n  } = useAppStore();\n\n  const menuItems = [\n    {\n      key: VIEW_MODES.LIBRARY,\n      icon: <BookOutlined />,\n      label: 'Library',\n    },\n    {\n      key: VIEW_MODES.READER,\n      icon: <FileTextOutlined />,\n      label: 'Reader',\n    },\n    {\n      key: VIEW_MODES.COMPARISON,\n      icon: <span>⚖️</span>,\n      label: 'Compare',\n    },\n    {\n      key: VIEW_MODES.ANALYSIS,\n      icon: <BarChartOutlined />,\n      label: 'Analysis',\n    },\n  ];\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      label: 'Profile',\n      icon: <UserOutlined />,\n    },\n    {\n      key: 'settings',\n      label: 'Settings',\n      icon: <SettingOutlined />,\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      label: 'Logout',\n      danger: true,\n    },\n  ];\n\n  const handleMenuClick = (key: string) => {\n    setCurrentView(key as any);\n  };\n\n  const handleUserMenuClick = ({ key }: { key: string }) => {\n    switch (key) {\n      case 'profile':\n        // Handle profile\n        break;\n      case 'settings':\n        // Handle settings\n        break;\n      case 'logout':\n        // Handle logout\n        break;\n    }\n  };\n\n  return (\n    <Layout className=\"min-h-screen\">\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={sidebarCollapsed}\n        width={240}\n        className=\"bg-white border-r border-gray-200\"\n        style={{\n          boxShadow: '2px 0 8px rgba(0,0,0,0.06)',\n        }}\n      >\n        <div className=\"flex items-center justify-center h-16 border-b border-gray-200\">\n          {!sidebarCollapsed ? (\n            <Title level={3} className=\"text-gradient m-0\">\n              DocuMancer\n            </Title>\n          ) : (\n            <div className=\"text-2xl font-bold text-gradient\">D</div>\n          )}\n        </div>\n        \n        <Menu\n          mode=\"inline\"\n          selectedKeys={[currentView]}\n          items={menuItems}\n          onClick={({ key }) => handleMenuClick(key)}\n          className=\"border-none\"\n          style={{ height: 'calc(100vh - 64px)' }}\n        />\n      </Sider>\n\n      <Layout>\n        <Header className=\"bg-white border-b border-gray-200 px-4 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              type=\"text\"\n              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}\n              className=\"text-lg\"\n            />\n            \n            <div className=\"flex items-center space-x-2 text-gray-600\">\n              <BookOutlined />\n              <span>{papers.length} Papers</span>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              type=\"text\"\n              icon={<SearchOutlined />}\n              className=\"text-lg\"\n            />\n            \n            <Badge count={5} size=\"small\">\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                className=\"text-lg\"\n              />\n            </Badge>\n\n            <Dropdown\n              menu={{\n                items: userMenuItems,\n                onClick: handleUserMenuClick,\n              }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space className=\"cursor-pointer hover:bg-gray-50 px-2 py-1 rounded\">\n                <Avatar size=\"small\" icon={<UserOutlined />} />\n                <span className=\"text-sm font-medium\">User</span>\n              </Space>\n            </Dropdown>\n          </div>\n        </Header>\n\n        <Content className=\"bg-gray-50 overflow-hidden\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AAhBA;;;;;AAkBA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,KAAK,EAAE,GAAG,6LAAA,CAAA,aAAU;AAM5B,MAAM,aAAwC;QAAC,EAAE,QAAQ,EAAE;;IACzD,MAAM,EACJ,gBAAgB,EAChB,mBAAmB,EACnB,WAAW,EACX,cAAc,EACd,MAAM,EACP,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,YAAY;QAChB;YACE,KAAK,0HAAA,CAAA,aAAU,CAAC,OAAO;YACvB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;QACA;YACE,KAAK,0HAAA,CAAA,aAAU,CAAC,MAAM;YACtB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;QACT;QACA;YACE,KAAK,0HAAA,CAAA,aAAU,CAAC,UAAU;YAC1B,oBAAM,6LAAC;0BAAK;;;;;;YACZ,OAAO;QACT;QACA;YACE,KAAK,0HAAA,CAAA,aAAU,CAAC,QAAQ;YACxB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;QACT;KACD;IAED,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;QACrB;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;QACxB;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ;QACV;KACD;IAED,MAAM,kBAAkB,CAAC;QACvB,eAAe;IACjB;IAEA,MAAM,sBAAsB;YAAC,EAAE,GAAG,EAAmB;QACnD,OAAQ;YACN,KAAK;gBAEH;YACF,KAAK;gBAEH;YACF,KAAK;gBAEH;QACJ;IACF;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAU;;0BAChB,6LAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,WAAU;gBACV,OAAO;oBACL,WAAW;gBACb;;kCAEA,6LAAC;wBAAI,WAAU;kCACZ,CAAC,iCACA,6LAAC;4BAAM,OAAO;4BAAG,WAAU;sCAAoB;;;;;qFAI/C,6LAAC;4BAAI,WAAU;sCAAmC;;;;;;;;;;;kCAItD,6LAAC,iLAAA,CAAA,OAAI;wBACH,MAAK;wBACL,cAAc;4BAAC;yBAAY;wBAC3B,OAAO;wBACP,SAAS;gCAAC,EAAE,GAAG,EAAE;mCAAK,gBAAgB;;wBACtC,WAAU;wBACV,OAAO;4BAAE,QAAQ;wBAAqB;;;;;;;;;;;;0BAI1C,6LAAC,qLAAA,CAAA,SAAM;;kCACL,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,iCAAmB,6LAAC,iOAAA,CAAA,qBAAkB;;;;mEAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wCACnE,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;;;;;;kDAGZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,eAAY;;;;;0DACb,6LAAC;;oDAAM,OAAO,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;0CAIzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACrB,WAAU;;;;;;kDAGZ,6LAAC,mLAAA,CAAA,QAAK;wCAAC,OAAO;wCAAG,MAAK;kDACpB,cAAA,6LAAC,qMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4CACnB,WAAU;;;;;;;;;;;kDAId,6LAAC,yLAAA,CAAA,WAAQ;wCACP,MAAM;4CACJ,OAAO;4CACP,SAAS;wCACX;wCACA,WAAU;wCACV,KAAK;kDAEL,cAAA,6LAAC,mMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,6LAAC,qLAAA,CAAA,SAAM;oDAAC,MAAK;oDAAQ,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;8DACxC,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,6LAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;GA5JM;;QAOA,8HAAA,CAAA,cAAW;;;KAPX;uCA8JS", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/file-utils.ts"], "sourcesContent": ["import { APP_CONFIG, ERROR_MESSAGES } from './constants';\n\nexport interface FileValidationResult {\n  isValid: boolean;\n  error?: string;\n  fileInfo?: {\n    name: string;\n    size: number;\n    type: string;\n    lastModified: number;\n  };\n}\n\nexport class FileUtils {\n  static validateFile(file: File): FileValidationResult {\n    // Check if file exists\n    if (!file) {\n      return {\n        isValid: false,\n        error: 'No file provided',\n      };\n    }\n\n    // Check file type\n    const fileName = file.name.toLowerCase();\n    const isValidType = APP_CONFIG.allowedFileTypes.some(type => \n      fileName.endsWith(type.toLowerCase())\n    );\n\n    if (!isValidType) {\n      return {\n        isValid: false,\n        error: ERROR_MESSAGES.INVALID_FILE_TYPE,\n      };\n    }\n\n    // Check file size\n    if (file.size > APP_CONFIG.maxFileSize) {\n      return {\n        isValid: false,\n        error: ERROR_MESSAGES.FILE_TOO_LARGE,\n      };\n    }\n\n    // Check if file is empty\n    if (file.size === 0) {\n      return {\n        isValid: false,\n        error: 'File is empty',\n      };\n    }\n\n    return {\n      isValid: true,\n      fileInfo: {\n        name: file.name,\n        size: file.size,\n        type: file.type,\n        lastModified: file.lastModified,\n      },\n    };\n  }\n\n  static formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  static getFileExtension(filename: string): string {\n    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);\n  }\n\n  static generateUniqueFileName(originalName: string): string {\n    const timestamp = Date.now();\n    const randomString = Math.random().toString(36).substring(2, 15);\n    const extension = this.getFileExtension(originalName);\n    const nameWithoutExt = originalName.replace(`.${extension}`, '');\n    \n    return `${timestamp}_${randomString}_${nameWithoutExt}.${extension}`;\n  }\n\n  static async fileToBuffer(file: File): Promise<Buffer> {\n    const arrayBuffer = await file.arrayBuffer();\n    return Buffer.from(arrayBuffer);\n  }\n\n  static async fileToBase64(file: File): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        const result = reader.result as string;\n        // Remove the data URL prefix (e.g., \"data:application/pdf;base64,\")\n        const base64 = result.split(',')[1];\n        resolve(base64);\n      };\n      reader.onerror = error => reject(error);\n    });\n  }\n\n  static createFileFromBuffer(buffer: Buffer, filename: string, mimeType: string): File {\n    const blob = new Blob([buffer], { type: mimeType });\n    return new File([blob], filename, { type: mimeType });\n  }\n\n  static downloadFile(content: string | Blob, filename: string, mimeType: string = 'text/plain') {\n    const blob = content instanceof Blob ? content : new Blob([content], { type: mimeType });\n    const url = URL.createObjectURL(blob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    \n    // Cleanup\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  }\n\n  static async compressFile(file: File, quality: number = 0.8): Promise<File> {\n    // For PDF files, we don't compress as it might affect readability\n    // This is a placeholder for future image compression if needed\n    if (file.type === 'application/pdf') {\n      return file;\n    }\n\n    // For other file types, return as-is for now\n    return file;\n  }\n\n  static getFileIcon(filename: string): string {\n    const extension = this.getFileExtension(filename).toLowerCase();\n    \n    switch (extension) {\n      case 'pdf':\n        return '📄';\n      case 'doc':\n      case 'docx':\n        return '📝';\n      case 'txt':\n        return '📃';\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n      case 'gif':\n        return '🖼️';\n      default:\n        return '📁';\n    }\n  }\n\n  static truncateFileName(filename: string, maxLength: number = 30): string {\n    if (filename.length <= maxLength) {\n      return filename;\n    }\n\n    const extension = this.getFileExtension(filename);\n    const nameWithoutExt = filename.replace(`.${extension}`, '');\n    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4);\n    \n    return `${truncatedName}...${extension}`;\n  }\n\n  static isValidPDFHeader(buffer: Buffer): boolean {\n    // Check if the buffer starts with PDF header\n    const header = buffer.slice(0, 5).toString();\n    return header === '%PDF-';\n  }\n\n  static extractPDFVersion(buffer: Buffer): string | null {\n    // Extract PDF version from header (e.g., %PDF-1.4)\n    const header = buffer.slice(0, 8).toString();\n    const match = header.match(/%PDF-(\\d+\\.\\d+)/);\n    return match ? match[1] : null;\n  }\n}\n"], "names": [], "mappings": ";;;AAwFW;AAxFX;;AAaO,MAAM;IACX,OAAO,aAAa,IAAU,EAAwB;QACpD,uBAAuB;QACvB,IAAI,CAAC,MAAM;YACT,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;QACtC,MAAM,cAAc,0HAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,OACnD,SAAS,QAAQ,CAAC,KAAK,WAAW;QAGpC,IAAI,CAAC,aAAa;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,0HAAA,CAAA,iBAAc,CAAC,iBAAiB;YACzC;QACF;QAEA,kBAAkB;QAClB,IAAI,KAAK,IAAI,GAAG,0HAAA,CAAA,aAAU,CAAC,WAAW,EAAE;YACtC,OAAO;gBACL,SAAS;gBACT,OAAO,0HAAA,CAAA,iBAAc,CAAC,cAAc;YACtC;QACF;QAEA,yBAAyB;QACzB,IAAI,KAAK,IAAI,KAAK,GAAG;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,UAAU;gBACR,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,cAAc,KAAK,YAAY;YACjC;QACF;IACF;IAEA,OAAO,eAAe,KAAa,EAAU;QAC3C,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,OAAO,iBAAiB,QAAgB,EAAU;QAChD,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;IAChE;IAEA,OAAO,uBAAuB,YAAoB,EAAU;QAC1D,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,eAAe,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;QAC7D,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,MAAM,iBAAiB,aAAa,OAAO,CAAC,AAAC,IAAa,OAAV,YAAa;QAE7D,OAAO,AAAC,GAAe,OAAb,WAAU,KAAmB,OAAhB,cAAa,KAAqB,OAAlB,gBAAe,KAAa,OAAV;IAC3D;IAEA,aAAa,aAAa,IAAU,EAAmB;QACrD,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,OAAO,8JAAA,CAAA,SAAM,CAAC,IAAI,CAAC;IACrB;IAEA,aAAa,aAAa,IAAU,EAAmB;QACrD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,aAAa,CAAC;YACrB,OAAO,MAAM,GAAG;gBACd,MAAM,SAAS,OAAO,MAAM;gBAC5B,oEAAoE;gBACpE,MAAM,SAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;gBACnC,QAAQ;YACV;YACA,OAAO,OAAO,GAAG,CAAA,QAAS,OAAO;QACnC;IACF;IAEA,OAAO,qBAAqB,MAAc,EAAE,QAAgB,EAAE,QAAgB,EAAQ;QACpF,MAAM,OAAO,IAAI,KAAK;YAAC;SAAO,EAAE;YAAE,MAAM;QAAS;QACjD,OAAO,IAAI,KAAK;YAAC;SAAK,EAAE,UAAU;YAAE,MAAM;QAAS;IACrD;IAEA,OAAO,aAAa,OAAsB,EAAE,QAAgB,EAAmC;YAAjC,WAAA,iEAAmB;QAC/E,MAAM,OAAO,mBAAmB,OAAO,UAAU,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAS;QACtF,MAAM,MAAM,IAAI,eAAe,CAAC;QAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QAEV,UAAU;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,aAAa,aAAa,IAAU,EAAwC;YAAtC,UAAA,iEAAkB;QACtD,kEAAkE;QAClE,+DAA+D;QAC/D,IAAI,KAAK,IAAI,KAAK,mBAAmB;YACnC,OAAO;QACT;QAEA,6CAA6C;QAC7C,OAAO;IACT;IAEA,OAAO,YAAY,QAAgB,EAAU;QAC3C,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,UAAU,WAAW;QAE7D,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,OAAO,iBAAiB,QAAgB,EAAkC;YAAhC,YAAA,iEAAoB;QAC5D,IAAI,SAAS,MAAM,IAAI,WAAW;YAChC,OAAO;QACT;QAEA,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,MAAM,iBAAiB,SAAS,OAAO,CAAC,AAAC,IAAa,OAAV,YAAa;QACzD,MAAM,gBAAgB,eAAe,SAAS,CAAC,GAAG,YAAY,UAAU,MAAM,GAAG;QAEjF,OAAO,AAAC,GAAqB,OAAnB,eAAc,OAAe,OAAV;IAC/B;IAEA,OAAO,iBAAiB,MAAc,EAAW;QAC/C,6CAA6C;QAC7C,MAAM,SAAS,OAAO,KAAK,CAAC,GAAG,GAAG,QAAQ;QAC1C,OAAO,WAAW;IACpB;IAEA,OAAO,kBAAkB,MAAc,EAAiB;QACtD,mDAAmD;QACnD,MAAM,SAAS,OAAO,KAAK,CAAC,GAAG,GAAG,QAAQ;QAC1C,MAAM,QAAQ,OAAO,KAAK,CAAC;QAC3B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/hooks/useFileUpload.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { FileUtils, FileValidationResult } from '@/lib/file-utils';\nimport { Paper, FileUploadProgress } from '@/lib/types';\nimport { API_ENDPOINTS, ERROR_MESSAGES } from '@/lib/constants';\nimport { useAppStore } from '@/store/useAppStore';\n\ninterface UseFileUploadReturn {\n  uploadFile: (file: File) => Promise<Paper | null>;\n  uploadProgress: FileUploadProgress | null;\n  isUploading: boolean;\n  error: string | null;\n  clearError: () => void;\n}\n\nexport const useFileUpload = (): UseFileUploadReturn => {\n  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress | null>(null);\n  const [isUploading, setIsUploading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  \n  const { addPaper, setError: setGlobalError } = useAppStore();\n\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  const uploadFile = useCallback(async (file: File): Promise<Paper | null> => {\n    try {\n      setIsUploading(true);\n      setError(null);\n      \n      // Validate file\n      const validation: FileValidationResult = FileUtils.validateFile(file);\n      if (!validation.isValid) {\n        throw new Error(validation.error);\n      }\n\n      // Initialize progress\n      setUploadProgress({\n        fileName: file.name,\n        progress: 0,\n        status: 'uploading',\n      });\n\n      // Create form data\n      const formData = new FormData();\n      formData.append('file', file);\n\n      // Upload file with progress tracking\n      const response = await fetch(API_ENDPOINTS.upload, {\n        method: 'POST',\n        body: formData,\n      });\n\n      // Update progress\n      setUploadProgress(prev => prev ? {\n        ...prev,\n        progress: 50,\n        status: 'processing',\n      } : null);\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || ERROR_MESSAGES.UPLOAD_FAILED);\n      }\n\n      const result = await response.json();\n      \n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.UPLOAD_FAILED);\n      }\n\n      // Update progress\n      setUploadProgress(prev => prev ? {\n        ...prev,\n        progress: 90,\n      } : null);\n\n      const paper: Paper = result.data.paper;\n      \n      // Add paper to store\n      addPaper(paper);\n\n      // Save paper to backend\n      await fetch(API_ENDPOINTS.papers, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(paper),\n      });\n\n      // Complete progress\n      setUploadProgress(prev => prev ? {\n        ...prev,\n        progress: 100,\n        status: 'completed',\n      } : null);\n\n      // Clear progress after a delay\n      setTimeout(() => {\n        setUploadProgress(null);\n      }, 2000);\n\n      return paper;\n\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.UPLOAD_FAILED;\n      \n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      \n      setUploadProgress(prev => prev ? {\n        ...prev,\n        status: 'error',\n        error: errorMessage,\n      } : null);\n\n      return null;\n    } finally {\n      setIsUploading(false);\n    }\n  }, [addPaper, setGlobalError]);\n\n  return {\n    uploadFile,\n    uploadProgress,\n    isUploading,\n    error,\n    clearError,\n  };\n};\n\n// Hook for drag and drop functionality\nexport const useDragAndDrop = (onFileDrop: (files: File[]) => void) => {\n  const [isDragOver, setIsDragOver] = useState(false);\n\n  const handleDragEnter = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragOver(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragOver(false);\n  }, []);\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragOver(false);\n\n    const files = Array.from(e.dataTransfer.files);\n    if (files.length > 0) {\n      onFileDrop(files);\n    }\n  }, [onFileDrop]);\n\n  return {\n    isDragOver,\n    dragHandlers: {\n      onDragEnter: handleDragEnter,\n      onDragLeave: handleDragLeave,\n      onDragOver: handleDragOver,\n      onDrop: handleDrop,\n    },\n  };\n};\n\n// Hook for batch file upload\nexport const useBatchUpload = () => {\n  const [uploads, setUploads] = useState<Map<string, FileUploadProgress>>(new Map());\n  const { uploadFile } = useFileUpload();\n\n  const uploadFiles = useCallback(async (files: File[]) => {\n    const uploadPromises = files.map(async (file) => {\n      const fileId = `${file.name}_${Date.now()}`;\n      \n      // Initialize progress for this file\n      setUploads(prev => new Map(prev.set(fileId, {\n        fileName: file.name,\n        progress: 0,\n        status: 'uploading',\n      })));\n\n      try {\n        const result = await uploadFile(file);\n        \n        // Update success status\n        setUploads(prev => new Map(prev.set(fileId, {\n          fileName: file.name,\n          progress: 100,\n          status: 'completed',\n        })));\n\n        return result;\n      } catch (error) {\n        // Update error status\n        setUploads(prev => new Map(prev.set(fileId, {\n          fileName: file.name,\n          progress: 0,\n          status: 'error',\n          error: error instanceof Error ? error.message : 'Upload failed',\n        })));\n\n        return null;\n      }\n    });\n\n    const results = await Promise.all(uploadPromises);\n    \n    // Clear uploads after a delay\n    setTimeout(() => {\n      setUploads(new Map());\n    }, 5000);\n\n    return results.filter(Boolean) as Paper[];\n  }, [uploadFile]);\n\n  const clearUploads = useCallback(() => {\n    setUploads(new Map());\n  }, []);\n\n  return {\n    uploadFiles,\n    uploads: Array.from(uploads.values()),\n    clearUploads,\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AACA;;;;;;AAUO,MAAM,gBAAgB;;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAChF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,QAAQ,EAAE,UAAU,cAAc,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAEzD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B,SAAS;QACX;gDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,OAAO;YACpC,IAAI;gBACF,eAAe;gBACf,SAAS;gBAET,gBAAgB;gBAChB,MAAM,aAAmC,8HAAA,CAAA,YAAS,CAAC,YAAY,CAAC;gBAChE,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,MAAM,IAAI,MAAM,WAAW,KAAK;gBAClC;gBAEA,sBAAsB;gBACtB,kBAAkB;oBAChB,UAAU,KAAK,IAAI;oBACnB,UAAU;oBACV,QAAQ;gBACV;gBAEA,mBAAmB;gBACnB,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,qCAAqC;gBACrC,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,gBAAa,CAAC,MAAM,EAAE;oBACjD,QAAQ;oBACR,MAAM;gBACR;gBAEA,kBAAkB;gBAClB;6DAAkB,CAAA,OAAQ,OAAO;4BAC/B,GAAG,IAAI;4BACP,UAAU;4BACV,QAAQ;wBACV,IAAI;;gBAEJ,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,aAAa;gBACjE;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,aAAa;gBAC9D;gBAEA,kBAAkB;gBAClB;6DAAkB,CAAA,OAAQ,OAAO;4BAC/B,GAAG,IAAI;4BACP,UAAU;wBACZ,IAAI;;gBAEJ,MAAM,QAAe,OAAO,IAAI,CAAC,KAAK;gBAEtC,qBAAqB;gBACrB,SAAS;gBAET,wBAAwB;gBACxB,MAAM,MAAM,0HAAA,CAAA,gBAAa,CAAC,MAAM,EAAE;oBAChC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,oBAAoB;gBACpB;6DAAkB,CAAA,OAAQ,OAAO;4BAC/B,GAAG,IAAI;4BACP,UAAU;4BACV,QAAQ;wBACV,IAAI;;gBAEJ,+BAA+B;gBAC/B;6DAAW;wBACT,kBAAkB;oBACpB;4DAAG;gBAEH,OAAO;YAET,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,0HAAA,CAAA,iBAAc,CAAC,aAAa;gBAEtF,SAAS;gBACT,eAAe;gBAEf;6DAAkB,CAAA,OAAQ,OAAO;4BAC/B,GAAG,IAAI;4BACP,QAAQ;4BACR,OAAO;wBACT,IAAI;;gBAEJ,OAAO;YACT,SAAU;gBACR,eAAe;YACjB;QACF;gDAAG;QAAC;QAAU;KAAe;IAE7B,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GApHa;;QAKoC,8HAAA,CAAA,cAAW;;;AAkHrD,MAAM,iBAAiB,CAAC;;IAC7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACnC,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,cAAc;QAChB;sDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACnC,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,cAAc;QAChB;sDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,EAAE,cAAc;YAChB,EAAE,eAAe;QACnB;qDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,cAAc;YAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;YAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,WAAW;YACb;QACF;iDAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA,cAAc;YACZ,aAAa;YACb,aAAa;YACb,YAAY;YACZ,QAAQ;QACV;IACF;AACF;IAxCa;AA2CN,MAAM,iBAAiB;;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC,IAAI;IAC5E,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YACrC,MAAM,iBAAiB,MAAM,GAAG;0EAAC,OAAO;oBACtC,MAAM,SAAS,AAAC,GAAe,OAAb,KAAK,IAAI,EAAC,KAAc,OAAX,KAAK,GAAG;oBAEvC,oCAAoC;oBACpC;kFAAW,CAAA,OAAQ,IAAI,IAAI,KAAK,GAAG,CAAC,QAAQ;gCAC1C,UAAU,KAAK,IAAI;gCACnB,UAAU;gCACV,QAAQ;4BACV;;oBAEA,IAAI;wBACF,MAAM,SAAS,MAAM,WAAW;wBAEhC,wBAAwB;wBACxB;sFAAW,CAAA,OAAQ,IAAI,IAAI,KAAK,GAAG,CAAC,QAAQ;oCAC1C,UAAU,KAAK,IAAI;oCACnB,UAAU;oCACV,QAAQ;gCACV;;wBAEA,OAAO;oBACT,EAAE,OAAO,OAAO;wBACd,sBAAsB;wBACtB;sFAAW,CAAA,OAAQ,IAAI,IAAI,KAAK,GAAG,CAAC,QAAQ;oCAC1C,UAAU,KAAK,IAAI;oCACnB,UAAU;oCACV,QAAQ;oCACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gCAClD;;wBAEA,OAAO;oBACT;gBACF;;YAEA,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAElC,8BAA8B;YAC9B;2DAAW;oBACT,WAAW,IAAI;gBACjB;0DAAG;YAEH,OAAO,QAAQ,MAAM,CAAC;QACxB;kDAAG;QAAC;KAAW;IAEf,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC/B,WAAW,IAAI;QACjB;mDAAG,EAAE;IAEL,OAAO;QACL;QACA,SAAS,MAAM,IAAI,CAAC,QAAQ,MAAM;QAClC;IACF;AACF;IA1Da;;QAEY", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/upload/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { Upload, Button, Progress, message, Card, Typography, Space, Alert, List, Tooltip } from 'antd';\nimport { InboxOutlined, UploadOutlined, FileTextOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined, InfoCircleOutlined } from '@ant-design/icons';\nimport { useFileUpload, useDragAndDrop } from '@/hooks/useFileUpload';\nimport { FileUtils } from '@/lib/file-utils';\nimport { APP_CONFIG } from '@/lib/constants';\n\nconst { Dragger } = Upload;\nconst { Title, Text } = Typography;\n\ninterface FileUploadProps {\n  onUploadSuccess?: (paperId: string) => void;\n  className?: string;\n  showProgress?: boolean;\n  maxFileSize?: number;\n  allowedTypes?: string[];\n  showFileInfo?: boolean;\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({\n  onUploadSuccess,\n  className = '',\n  showProgress = true,\n  maxFileSize = APP_CONFIG.maxFileSize,\n  allowedTypes = ['.pdf'],\n  showFileInfo = true,\n}) => {\n  const { uploadFile, uploadProgress, isUploading, error, clearError } = useFileUpload();\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');\n\n  const handleFileSelect = useCallback(async (file: File) => {\n    // Validate file\n    const validation = FileUtils.validateFile(file);\n    if (!validation.isValid) {\n      message.error(validation.error);\n      setUploadStatus('error');\n      return false;\n    }\n\n    setSelectedFile(file);\n    setUploadStatus('uploading');\n    clearError();\n\n    try {\n      // Upload file\n      const paper = await uploadFile(file);\n      if (paper) {\n        message.success('File uploaded successfully!');\n        setUploadStatus('success');\n        onUploadSuccess?.(paper.id);\n\n        // Reset after a delay\n        setTimeout(() => {\n          setSelectedFile(null);\n          setUploadStatus('idle');\n        }, 2000);\n      } else {\n        setUploadStatus('error');\n      }\n    } catch (error) {\n      console.error('Upload error:', error);\n      setUploadStatus('error');\n    }\n\n    return false; // Prevent default upload behavior\n  }, [uploadFile, onUploadSuccess, clearError]);\n\n  const { isDragOver, dragHandlers } = useDragAndDrop((files) => {\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  });\n\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: APP_CONFIG.allowedFileTypes.join(','),\n    beforeUpload: handleFileSelect,\n    showUploadList: false,\n    ...dragHandlers,\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      <Card className={`${isDragOver ? 'border-blue-400 bg-blue-50' : ''} transition-all duration-200`}>\n        <Dragger {...uploadProps} className=\"border-dashed\">\n          <div className=\"py-8\">\n            <p className=\"ant-upload-drag-icon\">\n              <InboxOutlined className=\"text-4xl text-blue-500\" />\n            </p>\n            <Title level={4} className=\"ant-upload-text\">\n              Click or drag PDF files to upload\n            </Title>\n            <Text className=\"ant-upload-hint text-gray-500\">\n              Support for academic papers in PDF format. Maximum file size: {FileUtils.formatFileSize(APP_CONFIG.maxFileSize)}\n            </Text>\n          </div>\n        </Dragger>\n      </Card>\n\n      {/* Upload Progress and Status */}\n      {showProgress && (uploadProgress || selectedFile) && (\n        <Card className=\"fade-in\">\n          <Space direction=\"vertical\" className=\"w-full\">\n            <div className=\"flex items-center space-x-3\">\n              {uploadStatus === 'uploading' && <LoadingOutlined className=\"text-blue-500\" />}\n              {uploadStatus === 'success' && <CheckCircleOutlined className=\"text-green-500\" />}\n              {uploadStatus === 'error' && <CloseCircleOutlined className=\"text-red-500\" />}\n              {uploadStatus === 'idle' && <FileTextOutlined className=\"text-blue-500\" />}\n\n              <div className=\"flex-1\">\n                <Text strong>{selectedFile?.name || uploadProgress?.fileName}</Text>\n                <div className=\"text-sm text-gray-500 capitalize\">\n                  {uploadStatus === 'uploading' && 'Uploading...'}\n                  {uploadStatus === 'success' && 'Upload completed successfully'}\n                  {uploadStatus === 'error' && 'Upload failed'}\n                  {uploadStatus === 'idle' && uploadProgress?.status.replace('_', ' ')}\n                </div>\n              </div>\n\n              {selectedFile && showFileInfo && (\n                <Tooltip title=\"File Information\">\n                  <div className=\"text-right text-sm text-gray-500\">\n                    <div>{FileUtils.formatFileSize(selectedFile.size)}</div>\n                    <div>{selectedFile.type || 'PDF'}</div>\n                  </div>\n                </Tooltip>\n              )}\n            </div>\n\n            {uploadProgress && (\n              <Progress\n                percent={uploadProgress.progress}\n                status={\n                  uploadProgress.status === 'error'\n                    ? 'exception'\n                    : uploadProgress.status === 'completed'\n                      ? 'success'\n                      : 'active'\n                }\n                strokeColor={{\n                  '0%': '#108ee9',\n                  '100%': '#87d068',\n                }}\n              />\n            )}\n\n            {uploadProgress?.error && (\n              <Text type=\"danger\" className=\"text-sm\">\n                {uploadProgress.error}\n              </Text>\n            )}\n          </Space>\n        </Card>\n      )}\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"border-red-200 bg-red-50\">\n          <Text type=\"danger\">{error}</Text>\n        </Card>\n      )}\n\n      {/* Alternative Upload Button */}\n      <div className=\"text-center\">\n        <Upload {...uploadProps}>\n          <Button \n            icon={<UploadOutlined />} \n            loading={isUploading}\n            size=\"large\"\n            type=\"primary\"\n          >\n            Choose File\n          </Button>\n        </Upload>\n      </div>\n\n      {/* File Info */}\n      <div className=\"text-center text-sm text-gray-500\">\n        <div>Supported formats: {APP_CONFIG.supportedFormats.join(', ')}</div>\n        <div>Maximum size: {FileUtils.formatFileSize(APP_CONFIG.maxFileSize)}</div>\n      </div>\n    </div>\n  );\n};\n\nexport default FileUpload;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AAC1B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAWlC,MAAM,aAAwC;QAAC,EAC7C,eAAe,EACf,YAAY,EAAE,EACd,eAAe,IAAI,EACnB,cAAc,0HAAA,CAAA,aAAU,CAAC,WAAW,EACpC,eAAe;QAAC;KAAO,EACvB,eAAe,IAAI,EACpB;;IACC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8C;IAE7F,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YAC1C,gBAAgB;YAChB,MAAM,aAAa,8HAAA,CAAA,YAAS,CAAC,YAAY,CAAC;YAC1C,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,WAAW,KAAK;gBAC9B,gBAAgB;gBAChB,OAAO;YACT;YAEA,gBAAgB;YAChB,gBAAgB;YAChB;YAEA,IAAI;gBACF,cAAc;gBACd,MAAM,QAAQ,MAAM,WAAW;gBAC/B,IAAI,OAAO;oBACT,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;oBAChB,gBAAgB;oBAChB,4BAAA,sCAAA,gBAAkB,MAAM,EAAE;oBAE1B,sBAAsB;oBACtB;oEAAW;4BACT,gBAAgB;4BAChB,gBAAgB;wBAClB;mEAAG;gBACL,OAAO;oBACL,gBAAgB;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,gBAAgB;YAClB;YAEA,OAAO,OAAO,kCAAkC;QAClD;mDAAG;QAAC;QAAY;QAAiB;KAAW;IAE5C,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;qCAAE,CAAC;YACnD,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,iBAAiB,KAAK,CAAC,EAAE;YAC3B;QACF;;IAEA,MAAM,cAAc;QAClB,MAAM;QACN,UAAU;QACV,QAAQ,0HAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACzC,cAAc;QACd,gBAAgB;QAChB,GAAG,YAAY;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAC3B,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAW,AAAC,GAAiD,OAA/C,aAAa,+BAA+B,IAAG;0BACjE,cAAA,6LAAC;oBAAS,GAAG,WAAW;oBAAE,WAAU;8BAClC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACX,cAAA,6LAAC,uNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAE3B,6LAAC;gCAAM,OAAO;gCAAG,WAAU;0CAAkB;;;;;;0CAG7C,6LAAC;gCAAK,WAAU;;oCAAgC;oCACiB,8HAAA,CAAA,YAAS,CAAC,cAAc,CAAC,0HAAA,CAAA,aAAU,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;YAOrH,gBAAgB,CAAC,kBAAkB,YAAY,mBAC9C,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAW,WAAU;;sCACpC,6LAAC;4BAAI,WAAU;;gCACZ,iBAAiB,6BAAe,6LAAC,2NAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;gCAC3D,iBAAiB,2BAAa,6LAAC,mOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;gCAC7D,iBAAiB,yBAAW,6LAAC,mOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;gCAC3D,iBAAiB,wBAAU,6LAAC,6NAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;8CAExD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,MAAM;sDAAE,CAAA,yBAAA,mCAAA,aAAc,IAAI,MAAI,2BAAA,qCAAA,eAAgB,QAAQ;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;;gDACZ,iBAAiB,eAAe;gDAChC,iBAAiB,aAAa;gDAC9B,iBAAiB,WAAW;gDAC5B,iBAAiB,WAAU,2BAAA,qCAAA,eAAgB,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;gCAInE,gBAAgB,8BACf,6LAAC,uLAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK,8HAAA,CAAA,YAAS,CAAC,cAAc,CAAC,aAAa,IAAI;;;;;;0DAChD,6LAAC;0DAAK,aAAa,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;;wBAMlC,gCACC,6LAAC,yLAAA,CAAA,WAAQ;4BACP,SAAS,eAAe,QAAQ;4BAChC,QACE,eAAe,MAAM,KAAK,UACtB,cACA,eAAe,MAAM,KAAK,cACxB,YACA;4BAER,aAAa;gCACX,MAAM;gCACN,QAAQ;4BACV;;;;;;wBAIH,CAAA,2BAAA,qCAAA,eAAgB,KAAK,mBACpB,6LAAC;4BAAK,MAAK;4BAAS,WAAU;sCAC3B,eAAe,KAAK;;;;;;;;;;;;;;;;;YAQ9B,uBACC,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC;oBAAK,MAAK;8BAAU;;;;;;;;;;;0BAKzB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qLAAA,CAAA,SAAM;oBAAE,GAAG,WAAW;8BACrB,cAAA,6LAAC,qMAAA,CAAA,SAAM;wBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wBACrB,SAAS;wBACT,MAAK;wBACL,MAAK;kCACN;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAI;4BAAoB,0HAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC;;;;;;;kCAC1D,6LAAC;;4BAAI;4BAAe,8HAAA,CAAA,YAAS,CAAC,cAAc,CAAC,0HAAA,CAAA,aAAU,CAAC,WAAW;;;;;;;;;;;;;;;;;;;AAI3E;GAtKM;;QAQmE,gIAAA,CAAA,gBAAa;QAyC/C,gIAAA,CAAA,iBAAc;;;KAjD/C;uCAwKS", "debugId": null}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/common/LoadingStates.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Spin, Progress, Card, Typography, Space, Skeleton } from 'antd';\nimport {\n  LoadingOutlined,\n  FileTextOutlined,\n  CloudUploadOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\n\nconst { Text, Title } = Typography;\n\ninterface LoadingSpinnerProps {\n  size?: 'small' | 'default' | 'large';\n  message?: string;\n  className?: string;\n}\n\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n  size = 'default',\n  message,\n  className = '',\n}) => {\n  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 32 : size === 'small' ? 16 : 24 }} spin />;\n\n  return (\n    <div className={`flex flex-col items-center justify-center p-8 ${className}`}>\n      <Spin indicator={antIcon} size={size} />\n      {message && (\n        <Text className=\"mt-4 text-gray-600 text-center\">\n          {message}\n        </Text>\n      )}\n    </div>\n  );\n};\n\ninterface ProgressLoadingProps {\n  progress: number;\n  message?: string;\n  subMessage?: string;\n  className?: string;\n}\n\nexport const ProgressLoading: React.FC<ProgressLoadingProps> = ({\n  progress,\n  message,\n  subMessage,\n  className = '',\n}) => {\n  return (\n    <div className={`flex flex-col items-center justify-center p-8 ${className}`}>\n      <div className=\"w-full max-w-md space-y-4\">\n        <div className=\"text-center\">\n          {message && (\n            <Text strong className=\"text-lg\">\n              {message}\n            </Text>\n          )}\n          {subMessage && (\n            <div className=\"text-sm text-gray-600 mt-1\">\n              {subMessage}\n            </div>\n          )}\n        </div>\n        \n        <Progress\n          percent={progress}\n          strokeColor={{\n            '0%': '#108ee9',\n            '100%': '#87d068',\n          }}\n          trailColor=\"#f0f0f0\"\n          strokeWidth={8}\n          className=\"progress-loading\"\n        />\n        \n        <div className=\"text-center text-sm text-gray-500\">\n          {progress}% complete\n        </div>\n      </div>\n    </div>\n  );\n};\n\ninterface StepLoadingProps {\n  steps: Array<{\n    title: string;\n    description?: string;\n    icon?: React.ReactNode;\n  }>;\n  currentStep: number;\n  className?: string;\n}\n\nexport const StepLoading: React.FC<StepLoadingProps> = ({\n  steps,\n  currentStep,\n  className = '',\n}) => {\n  return (\n    <div className={`p-8 ${className}`}>\n      <div className=\"max-w-md mx-auto space-y-4\">\n        {steps.map((step, index) => {\n          const isActive = index === currentStep;\n          const isCompleted = index < currentStep;\n          \n          return (\n            <div\n              key={index}\n              className={`flex items-center space-x-3 p-3 rounded-lg transition-all ${\n                isActive\n                  ? 'bg-blue-50 border border-blue-200'\n                  : isCompleted\n                  ? 'bg-green-50 border border-green-200'\n                  : 'bg-gray-50 border border-gray-200'\n              }`}\n            >\n              <div\n                className={`flex items-center justify-center w-8 h-8 rounded-full ${\n                  isActive\n                    ? 'bg-blue-500 text-white'\n                    : isCompleted\n                    ? 'bg-green-500 text-white'\n                    : 'bg-gray-300 text-gray-600'\n                }`}\n              >\n                {isActive ? (\n                  <Spin size=\"small\" />\n                ) : isCompleted ? (\n                  '✓'\n                ) : (\n                  step.icon || index + 1\n                )}\n              </div>\n              \n              <div className=\"flex-1\">\n                <div\n                  className={`font-medium ${\n                    isActive\n                      ? 'text-blue-700'\n                      : isCompleted\n                      ? 'text-green-700'\n                      : 'text-gray-600'\n                  }`}\n                >\n                  {step.title}\n                </div>\n                {step.description && (\n                  <div className=\"text-sm text-gray-500\">\n                    {step.description}\n                  </div>\n                )}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\n// Specific loading components for different features\nexport const PDFProcessingLoader: React.FC<{ fileName?: string }> = ({ fileName }) => {\n  const steps = [\n    {\n      title: 'Uploading file',\n      description: 'Transferring your PDF to the server',\n      icon: <CloudUploadOutlined />,\n    },\n    {\n      title: 'Processing PDF',\n      description: 'Extracting text and metadata',\n      icon: <FileTextOutlined />,\n    },\n    {\n      title: 'Analyzing content',\n      description: 'Preparing for AI analysis',\n      icon: <BrainOutlined />,\n    },\n  ];\n\n  return (\n    <Card className=\"max-w-lg mx-auto\">\n      <div className=\"text-center mb-6\">\n        <Title level={4}>Processing PDF</Title>\n        {fileName && (\n          <Text type=\"secondary\">\n            {fileName}\n          </Text>\n        )}\n      </div>\n      <StepLoading steps={steps} currentStep={1} />\n    </Card>\n  );\n};\n\nexport const AIAnalysisLoader: React.FC<{ analysisType?: string }> = ({ analysisType }) => {\n  return (\n    <div className=\"text-center p-8\">\n      <div className=\"mb-4\">\n        <div className=\"text-4xl text-blue-500 animate-pulse\">🧠</div>\n      </div>\n      <Title level={4}>AI Analysis in Progress</Title>\n      <Text type=\"secondary\">\n        {analysisType ? `Performing ${analysisType.toLowerCase()}...` : 'Analyzing your document...'}\n      </Text>\n      <div className=\"mt-4\">\n        <LoadingSpinner size=\"large\" />\n      </div>\n    </div>\n  );\n};\n\nexport const SearchLoader: React.FC<{ query?: string }> = ({ query }) => {\n  return (\n    <div className=\"text-center p-8\">\n      <div className=\"mb-4\">\n        <SearchOutlined className=\"text-4xl text-green-500 animate-pulse\" />\n      </div>\n      <Title level={4}>Searching...</Title>\n      {query && (\n        <Text type=\"secondary\">\n          Looking for \"{query}\"\n        </Text>\n      )}\n      <div className=\"mt-4\">\n        <LoadingSpinner />\n      </div>\n    </div>\n  );\n};\n\n// Skeleton loaders for different content types\nexport const PaperCardSkeleton: React.FC = () => {\n  return (\n    <Card className=\"h-full\">\n      <Skeleton\n        active\n        avatar={{ size: 'large', shape: 'square' }}\n        paragraph={{ rows: 4 }}\n        title={{ width: '80%' }}\n      />\n      <div className=\"mt-4 flex space-x-2\">\n        <Skeleton.Button size=\"small\" />\n        <Skeleton.Button size=\"small\" />\n        <Skeleton.Button size=\"small\" />\n      </div>\n    </Card>\n  );\n};\n\nexport const ChatMessageSkeleton: React.FC = () => {\n  return (\n    <div className=\"flex space-x-3 mb-4\">\n      <Skeleton.Avatar size=\"default\" />\n      <div className=\"flex-1\">\n        <Skeleton\n          active\n          paragraph={{ rows: 2, width: ['60%', '40%'] }}\n          title={false}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport const LibraryViewSkeleton: React.FC = () => {\n  return (\n    <div className=\"p-6\">\n      {/* Header skeleton */}\n      <div className=\"mb-6\">\n        <Skeleton.Input style={{ width: 200, height: 32 }} active />\n        <div className=\"mt-4\">\n          <Skeleton.Input style={{ width: '100%', height: 40 }} active />\n        </div>\n      </div>\n\n      {/* Cards grid skeleton */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n        {Array.from({ length: 8 }).map((_, index) => (\n          <PaperCardSkeleton key={index} />\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport const ReaderViewSkeleton: React.FC = () => {\n  return (\n    <div className=\"h-full flex\">\n      {/* PDF viewer skeleton */}\n      <div className=\"flex-1 p-4\">\n        <div className=\"mb-4\">\n          <Skeleton.Input style={{ width: 150, height: 32 }} active />\n        </div>\n        <Card className=\"h-full\">\n          <Skeleton\n            active\n            paragraph={{ rows: 20 }}\n            title={{ width: '60%' }}\n          />\n        </Card>\n      </div>\n\n      {/* Sidebar skeleton */}\n      <div className=\"w-96 border-l p-4\">\n        <div className=\"space-y-4\">\n          <Skeleton.Input style={{ width: '100%', height: 40 }} active />\n          {Array.from({ length: 5 }).map((_, index) => (\n            <ChatMessageSkeleton key={index} />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Loading overlay component\nexport const LoadingOverlay: React.FC<{\n  loading: boolean;\n  children: React.ReactNode;\n  message?: string;\n}> = ({ loading, children, message }) => {\n  return (\n    <Spin spinning={loading} tip={message}>\n      {children}\n    </Spin>\n  );\n};\n\nexport default LoadingSpinner;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAJA;;;;AAWA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,6LAAA,CAAA,aAAU;AAQ3B,MAAM,iBAAgD;QAAC,EAC5D,OAAO,SAAS,EAChB,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,wBAAU,6LAAC,2NAAA,CAAA,kBAAe;QAAC,OAAO;YAAE,UAAU,SAAS,UAAU,KAAK,SAAS,UAAU,KAAK;QAAG;QAAG,IAAI;;;;;;IAE9G,qBACE,6LAAC;QAAI,WAAW,AAAC,iDAA0D,OAAV;;0BAC/D,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAW;gBAAS,MAAM;;;;;;YAC/B,yBACC,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAKX;KAjBa;AA0BN,MAAM,kBAAkD;QAAC,EAC9D,QAAQ,EACR,OAAO,EACP,UAAU,EACV,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QAAI,WAAW,AAAC,iDAA0D,OAAV;kBAC/D,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;wBACZ,yBACC,6LAAC;4BAAK,MAAM;4BAAC,WAAU;sCACpB;;;;;;wBAGJ,4BACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;8BAKP,6LAAC,yLAAA,CAAA,WAAQ;oBACP,SAAS;oBACT,aAAa;wBACX,MAAM;wBACN,QAAQ;oBACV;oBACA,YAAW;oBACX,aAAa;oBACb,WAAU;;;;;;8BAGZ,6LAAC;oBAAI,WAAU;;wBACZ;wBAAS;;;;;;;;;;;;;;;;;;AAKpB;MAvCa;AAmDN,MAAM,cAA0C;QAAC,EACtD,KAAK,EACL,WAAW,EACX,YAAY,EAAE,EACf;IACC,qBACE,6LAAC;QAAI,WAAW,AAAC,OAAgB,OAAV;kBACrB,cAAA,6LAAC;YAAI,WAAU;sBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gBAChB,MAAM,WAAW,UAAU;gBAC3B,MAAM,cAAc,QAAQ;gBAE5B,qBACE,6LAAC;oBAEC,WAAW,AAAC,6DAMX,OALC,WACI,sCACA,cACA,wCACA;;sCAGN,6LAAC;4BACC,WAAW,AAAC,yDAMX,OALC,WACI,2BACA,cACA,4BACA;sCAGL,yBACC,6LAAC,iLAAA,CAAA,OAAI;gCAAC,MAAK;;;;;2EACT,cACF,MAEA,KAAK,IAAI,IAAI,QAAQ;;;;;;sCAIzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAW,AAAC,eAMX,OALC,WACI,kBACA,cACA,mBACA;8CAGL,KAAK,KAAK;;;;;;gCAEZ,KAAK,WAAW,kBACf,6LAAC;oCAAI,WAAU;8CACZ,KAAK,WAAW;;;;;;;;;;;;;mBAzClB;;;;;YA+CX;;;;;;;;;;;AAIR;MAjEa;AAoEN,MAAM,sBAAuD;QAAC,EAAE,QAAQ,EAAE;IAC/E,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;QAC5B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;QACzB;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC;;;;;QACT;KACD;IAED,qBACE,6LAAC,iLAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,OAAO;kCAAG;;;;;;oBAChB,0BACC,6LAAC;wBAAK,MAAK;kCACR;;;;;;;;;;;;0BAIP,6LAAC;gBAAY,OAAO;gBAAO,aAAa;;;;;;;;;;;;AAG9C;MAhCa;AAkCN,MAAM,mBAAwD;QAAC,EAAE,YAAY,EAAE;IACpF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAuC;;;;;;;;;;;0BAExD,6LAAC;gBAAM,OAAO;0BAAG;;;;;;0BACjB,6LAAC;gBAAK,MAAK;0BACR,eAAe,AAAC,cAAwC,OAA3B,aAAa,WAAW,IAAG,SAAO;;;;;;0BAElE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAe,MAAK;;;;;;;;;;;;;;;;;AAI7B;MAfa;AAiBN,MAAM,eAA6C;QAAC,EAAE,KAAK,EAAE;IAClE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yNAAA,CAAA,iBAAc;oBAAC,WAAU;;;;;;;;;;;0BAE5B,6LAAC;gBAAM,OAAO;0BAAG;;;;;;YAChB,uBACC,6LAAC;gBAAK,MAAK;;oBAAY;oBACP;oBAAM;;;;;;;0BAGxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjBa;AAoBN,MAAM,oBAA8B;IACzC,qBACE,6LAAC,iLAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,yLAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,QAAQ;oBAAE,MAAM;oBAAS,OAAO;gBAAS;gBACzC,WAAW;oBAAE,MAAM;gBAAE;gBACrB,OAAO;oBAAE,OAAO;gBAAM;;;;;;0BAExB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yLAAA,CAAA,WAAQ,CAAC,MAAM;wBAAC,MAAK;;;;;;kCACtB,6LAAC,yLAAA,CAAA,WAAQ,CAAC,MAAM;wBAAC,MAAK;;;;;;kCACtB,6LAAC,yLAAA,CAAA,WAAQ,CAAC,MAAM;wBAAC,MAAK;;;;;;;;;;;;;;;;;;AAI9B;MAhBa;AAkBN,MAAM,sBAAgC;IAC3C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yLAAA,CAAA,WAAQ,CAAC,MAAM;gBAAC,MAAK;;;;;;0BACtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yLAAA,CAAA,WAAQ;oBACP,MAAM;oBACN,WAAW;wBAAE,MAAM;wBAAG,OAAO;4BAAC;4BAAO;yBAAM;oBAAC;oBAC5C,OAAO;;;;;;;;;;;;;;;;;AAKjB;MAba;AAeN,MAAM,sBAAgC;IAC3C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yLAAA,CAAA,WAAQ,CAAC,KAAK;wBAAC,OAAO;4BAAE,OAAO;4BAAK,QAAQ;wBAAG;wBAAG,MAAM;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yLAAA,CAAA,WAAQ,CAAC,KAAK;4BAAC,OAAO;gCAAE,OAAO;gCAAQ,QAAQ;4BAAG;4BAAG,MAAM;;;;;;;;;;;;;;;;;0BAKhE,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,uBAAuB;;;;;;;;;;;;;;;;AAKlC;MAnBa;AAqBN,MAAM,qBAA+B;IAC1C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yLAAA,CAAA,WAAQ,CAAC,KAAK;4BAAC,OAAO;gCAAE,OAAO;gCAAK,QAAQ;4BAAG;4BAAG,MAAM;;;;;;;;;;;kCAE3D,6LAAC,iLAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,yLAAA,CAAA,WAAQ;4BACP,MAAM;4BACN,WAAW;gCAAE,MAAM;4BAAG;4BACtB,OAAO;gCAAE,OAAO;4BAAM;;;;;;;;;;;;;;;;;0BAM5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yLAAA,CAAA,WAAQ,CAAC,KAAK;4BAAC,OAAO;gCAAE,OAAO;gCAAQ,QAAQ;4BAAG;4BAAG,MAAM;;;;;;wBAC3D,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,yBAAyB;;;;;;;;;;;;;;;;;;;;;;AAMtC;MA5Ba;AA+BN,MAAM,iBAIR;QAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE;IAClC,qBACE,6LAAC,iLAAA,CAAA,OAAI;QAAC,UAAU;QAAS,KAAK;kBAC3B;;;;;;AAGP;OAVa;uCAYE", "debugId": null}}, {"offset": {"line": 2176, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/library/PaperCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Card, Typography, Tag, Button, Space, Dropdown, Progress, Tooltip, Avatar } from 'antd';\nimport {\n  FileTextOutlined,\n  UserOutlined,\n  CalendarOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  MoreOutlined,\n  StarOutlined,\n  StarFilled,\n  DownloadOutlined,\n  ShareAltOutlined,\n} from '@ant-design/icons';\nimport { Paper } from '@/lib/types';\nimport { useAppStore } from '@/store/useAppStore';\n\nconst { Title, Text, Paragraph } = Typography;\n\ninterface PaperCardProps {\n  paper: Paper;\n  onView: (paper: Paper) => void;\n  onEdit?: (paper: Paper) => void;\n  onDelete?: (paper: Paper) => void;\n  className?: string;\n}\n\nconst PaperCard: React.FC<PaperCardProps> = ({\n  paper,\n  onView,\n  onEdit,\n  onDelete,\n  className = '',\n}) => {\n  const [isFavorite, setIsFavorite] = useState(false);\n  const { readingSessions } = useAppStore();\n\n  // Get reading progress for this paper\n  const paperSessions = readingSessions.filter(s => s.paperId === paper.id);\n  const latestSession = paperSessions[paperSessions.length - 1];\n  const readingProgress = latestSession?.progress || 0;\n\n  const handleFavoriteToggle = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setIsFavorite(!isFavorite);\n  };\n\n  const handleDownload = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    // In a real implementation, you would download the PDF\n    const link = document.createElement('a');\n    link.href = paper.filePath;\n    link.download = `${paper.title}.pdf`;\n    link.click();\n  };\n\n  const handleShare = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    // In a real implementation, you would implement sharing functionality\n    navigator.clipboard.writeText(`Check out this paper: ${paper.title}`);\n  };\n\n  const menuItems = [\n    {\n      key: 'view',\n      label: 'View Paper',\n      icon: <EyeOutlined />,\n      onClick: () => onView(paper),\n    },\n    {\n      key: 'edit',\n      label: 'Edit Details',\n      icon: <EditOutlined />,\n      onClick: () => onEdit?.(paper),\n    },\n    {\n      key: 'download',\n      label: 'Download PDF',\n      icon: <DownloadOutlined />,\n      onClick: handleDownload,\n    },\n    {\n      key: 'share',\n      label: 'Share',\n      icon: <ShareAltOutlined />,\n      onClick: handleShare,\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'delete',\n      label: 'Delete',\n      icon: <DeleteOutlined />,\n      danger: true,\n      onClick: () => onDelete?.(paper),\n    },\n  ];\n\n  const formatDate = (date: Date) => {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <Card\n      hoverable\n      className={`paper-card hover-lift ${className}`}\n      onClick={() => onView(paper)}\n      actions={[\n        <Button \n          type=\"text\" \n          icon={<EyeOutlined />}\n          onClick={(e) => {\n            e.stopPropagation();\n            onView(paper);\n          }}\n          key=\"view\"\n        >\n          View\n        </Button>,\n        <Tooltip title={isFavorite ? 'Remove from favorites' : 'Add to favorites'} key=\"favorite\">\n          <Button\n            type=\"text\"\n            icon={isFavorite ? <StarFilled className=\"text-yellow-500\" /> : <StarOutlined />}\n            onClick={handleFavoriteToggle}\n          />\n        </Tooltip>,\n        <Dropdown \n          menu={{ items: menuItems }}\n          trigger={['click']}\n          key=\"more\"\n        >\n          <Button \n            type=\"text\" \n            icon={<MoreOutlined />}\n            onClick={(e) => e.stopPropagation()}\n          />\n        </Dropdown>,\n      ]}\n    >\n      <div className=\"flex flex-col h-full\">\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <FileTextOutlined className=\"text-blue-500 text-xl\" />\n            {readingProgress > 0 && (\n              <Progress\n                type=\"circle\"\n                size={20}\n                percent={readingProgress}\n                showInfo={false}\n                strokeColor=\"#52c41a\"\n              />\n            )}\n          </div>\n          <Text type=\"secondary\" className=\"text-xs\">\n            {formatDate(paper.uploadedAt)}\n          </Text>\n        </div>\n        \n        {/* Title */}\n        <Title level={5} className=\"mb-2 line-clamp-2 min-h-[2.5rem]\">\n          {paper.title}\n        </Title>\n        \n        {/* Authors */}\n        <div className=\"flex items-center mb-3\">\n          <div className=\"flex -space-x-1 mr-2\">\n            {paper.authors.slice(0, 3).map((author, index) => (\n              <Tooltip title={author} key={index}>\n                <Avatar \n                  size=\"small\" \n                  className=\"border-2 border-white bg-blue-500\"\n                >\n                  {getInitials(author)}\n                </Avatar>\n              </Tooltip>\n            ))}\n            {paper.authors.length > 3 && (\n              <Avatar size=\"small\" className=\"border-2 border-white bg-gray-400\">\n                +{paper.authors.length - 3}\n              </Avatar>\n            )}\n          </div>\n          <Text className=\"text-sm text-gray-600 truncate flex-1\">\n            {paper.authors.slice(0, 2).join(', ')}\n            {paper.authors.length > 2 && ` +${paper.authors.length - 2} more`}\n          </Text>\n        </div>\n        \n        {/* Abstract */}\n        <Paragraph \n          className=\"text-sm text-gray-600 flex-1 mb-3\"\n          ellipsis={{ rows: 3, tooltip: paper.abstract }}\n        >\n          {paper.abstract}\n        </Paragraph>\n        \n        {/* Reading Progress */}\n        {readingProgress > 0 && (\n          <div className=\"mb-3\">\n            <div className=\"flex items-center justify-between mb-1\">\n              <Text className=\"text-xs text-gray-500\">Reading Progress</Text>\n              <Text className=\"text-xs text-gray-500\">{readingProgress}%</Text>\n            </div>\n            <Progress \n              percent={readingProgress} \n              size=\"small\" \n              showInfo={false}\n              strokeColor=\"#1890ff\"\n            />\n          </div>\n        )}\n        \n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-1\">\n          {paper.tags.slice(0, 3).map((tag) => (\n            <Tag \n              key={tag} \n              size=\"small\" \n              color=\"blue\"\n              className=\"cursor-pointer hover:bg-blue-100\"\n              onClick={(e) => {\n                e.stopPropagation();\n                // In a real implementation, you would filter by this tag\n              }}\n            >\n              {tag}\n            </Tag>\n          ))}\n          {paper.tags.length > 3 && (\n            <Tag size=\"small\" className=\"cursor-pointer\">\n              +{paper.tags.length - 3}\n            </Tag>\n          )}\n        </div>\n\n        {/* Footer Info */}\n        <div className=\"flex items-center justify-between mt-3 pt-2 border-t border-gray-100\">\n          <div className=\"flex items-center space-x-3 text-xs text-gray-500\">\n            <span className=\"flex items-center\">\n              <CalendarOutlined className=\"mr-1\" />\n              {formatDate(paper.lastAccessedAt)}\n            </span>\n          </div>\n          \n          {paperSessions.length > 0 && (\n            <Text className=\"text-xs text-gray-500\">\n              {paperSessions.length} session{paperSessions.length > 1 ? 's' : ''}\n            </Text>\n          )}\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default PaperCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAlBA;;;;;AAoBA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAU7C,MAAM,YAAsC;QAAC,EAC3C,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAEtC,sCAAsC;IACtC,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,MAAM,EAAE;IACxE,MAAM,gBAAgB,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;IAC7D,MAAM,kBAAkB,CAAA,0BAAA,oCAAA,cAAe,QAAQ,KAAI;IAEnD,MAAM,uBAAuB,CAAC;QAC5B,EAAE,eAAe;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,eAAe;QACjB,uDAAuD;QACvD,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,MAAM,QAAQ;QAC1B,KAAK,QAAQ,GAAG,AAAC,GAAc,OAAZ,MAAM,KAAK,EAAC;QAC/B,KAAK,KAAK;IACZ;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,eAAe;QACjB,sEAAsE;QACtE,UAAU,SAAS,CAAC,SAAS,CAAC,AAAC,yBAAoC,OAAZ,MAAM,KAAK;IACpE;IAEA,MAAM,YAAY;QAChB;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;YAClB,SAAS,IAAM,OAAO;QACxB;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,SAAS,IAAM,mBAAA,6BAAA,OAAS;QAC1B;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,SAAS;QACX;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,SAAS;QACX;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,QAAQ;YACR,SAAS,IAAM,qBAAA,+BAAA,SAAW;QAC5B;KACD;IAED,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAChD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;QACH,SAAS;QACT,WAAW,AAAC,yBAAkC,OAAV;QACpC,SAAS,IAAM,OAAO;QACtB,SAAS;0BACP,6LAAC,qMAAA,CAAA,SAAM;gBACL,MAAK;gBACL,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;gBAClB,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,OAAO;gBACT;0BAED;eADK;;;;;0BAIN,6LAAC,uLAAA,CAAA,UAAO;gBAAC,OAAO,aAAa,0BAA0B;0BACrD,cAAA,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,MAAM,2BAAa,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;+CAAuB,6LAAC,qNAAA,CAAA,eAAY;;;;;oBAC7E,SAAS;;;;;;eAJkE;;;;;0BAO/E,6LAAC,yLAAA,CAAA,WAAQ;gBACP,MAAM;oBAAE,OAAO;gBAAU;gBACzB,SAAS;oBAAC;iBAAQ;0BAGlB,cAAA,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;eAL/B;;;;;SAQP;kBAED,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6NAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;gCAC3B,kBAAkB,mBACjB,6LAAC,yLAAA,CAAA,WAAQ;oCACP,MAAK;oCACL,MAAM;oCACN,SAAS;oCACT,UAAU;oCACV,aAAY;;;;;;;;;;;;sCAIlB,6LAAC;4BAAK,MAAK;4BAAY,WAAU;sCAC9B,WAAW,MAAM,UAAU;;;;;;;;;;;;8BAKhC,6LAAC;oBAAM,OAAO;oBAAG,WAAU;8BACxB,MAAM,KAAK;;;;;;8BAId,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACtC,6LAAC,uLAAA,CAAA,UAAO;wCAAC,OAAO;kDACd,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;sDAET,YAAY;;;;;;uCALY;;;;;gCAS9B,MAAM,OAAO,CAAC,MAAM,GAAG,mBACtB,6LAAC,qLAAA,CAAA,SAAM;oCAAC,MAAK;oCAAQ,WAAU;;wCAAoC;wCAC/D,MAAM,OAAO,CAAC,MAAM,GAAG;;;;;;;;;;;;;sCAI/B,6LAAC;4BAAK,WAAU;;gCACb,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;gCAC/B,MAAM,OAAO,CAAC,MAAM,GAAG,KAAK,AAAC,KAA6B,OAAzB,MAAM,OAAO,CAAC,MAAM,GAAG,GAAE;;;;;;;;;;;;;8BAK/D,6LAAC;oBACC,WAAU;oBACV,UAAU;wBAAE,MAAM;wBAAG,SAAS,MAAM,QAAQ;oBAAC;8BAE5C,MAAM,QAAQ;;;;;;gBAIhB,kBAAkB,mBACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,6LAAC;oCAAK,WAAU;;wCAAyB;wCAAgB;;;;;;;;;;;;;sCAE3D,6LAAC,yLAAA,CAAA,WAAQ;4BACP,SAAS;4BACT,MAAK;4BACL,UAAU;4BACV,aAAY;;;;;;;;;;;;8BAMlB,6LAAC;oBAAI,WAAU;;wBACZ,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC3B,6LAAC,+KAAA,CAAA,MAAG;gCAEF,MAAK;gCACL,OAAM;gCACN,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;gCACjB,yDAAyD;gCAC3D;0CAEC;+BATI;;;;;wBAYR,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,MAAK;4BAAQ,WAAU;;gCAAiB;gCACzC,MAAM,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8BAM5B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;;kDACd,6LAAC,6NAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;oCAC3B,WAAW,MAAM,cAAc;;;;;;;;;;;;wBAInC,cAAc,MAAM,GAAG,mBACtB,6LAAC;4BAAK,WAAU;;gCACb,cAAc,MAAM;gCAAC;gCAAS,cAAc,MAAM,GAAG,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAO9E;GAjPM;;QAQwB,8HAAA,CAAA,cAAW;;;KARnC;uCAmPS", "debugId": null}}, {"offset": {"line": 2657, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/hooks/usePapers.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { Paper, ApiResponse } from '@/lib/types';\nimport { API_ENDPOINTS, ERROR_MESSAGES } from '@/lib/constants';\nimport { useAppStore } from '@/store/useAppStore';\n\ninterface UsePapersReturn {\n  papers: Paper[];\n  loading: boolean;\n  error: string | null;\n  fetchPapers: () => Promise<void>;\n  fetchPaper: (id: string) => Promise<Paper | null>;\n  updatePaper: (id: string, updates: Partial<Paper>) => Promise<boolean>;\n  deletePaper: (id: string) => Promise<boolean>;\n  searchPapers: (query: string, filters?: any[]) => Promise<Paper[]>;\n  refreshPapers: () => Promise<void>;\n}\n\nexport const usePapers = (): UsePapersReturn => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  \n  const { \n    papers, \n    setPapers, \n    addPaper, \n    removePaper,\n    setError: setGlobalError \n  } = useAppStore();\n\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  const fetchPapers = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch(API_ENDPOINTS.papers);\n      const result: ApiResponse<{ papers: Paper[] }> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      if (result.data) {\n        setPapers(result.data.papers);\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [setPapers, setGlobalError]);\n\n  const fetchPaper = useCallback(async (id: string): Promise<Paper | null> => {\n    try {\n      setError(null);\n\n      const response = await fetch(`${API_ENDPOINTS.papers}?id=${id}`);\n      const result: ApiResponse<Paper> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      return result.data || null;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      return null;\n    }\n  }, []);\n\n  const updatePaper = useCallback(async (id: string, updates: Partial<Paper>): Promise<boolean> => {\n    try {\n      setError(null);\n\n      const existingPaper = papers.find(p => p.id === id);\n      if (!existingPaper) {\n        throw new Error('Paper not found');\n      }\n\n      const updatedPaper = { ...existingPaper, ...updates };\n\n      const response = await fetch(API_ENDPOINTS.papers, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(updatedPaper),\n      });\n\n      const result: ApiResponse<Paper> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      // Update local state\n      setPapers(papers.map(p => p.id === id ? updatedPaper : p));\n      \n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      return false;\n    }\n  }, [papers, setPapers, setGlobalError]);\n\n  const deletePaper = useCallback(async (id: string): Promise<boolean> => {\n    try {\n      setError(null);\n\n      const response = await fetch(`${API_ENDPOINTS.papers}?id=${id}`, {\n        method: 'DELETE',\n      });\n\n      const result: ApiResponse<Paper> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      // Update local state\n      removePaper(id);\n      \n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      return false;\n    }\n  }, [removePaper, setGlobalError]);\n\n  const searchPapers = useCallback(async (query: string, filters: any[] = []): Promise<Paper[]> => {\n    try {\n      setError(null);\n\n      const searchParams = new URLSearchParams({\n        q: query,\n        filters: JSON.stringify(filters),\n      });\n\n      const response = await fetch(`${API_ENDPOINTS.search}?${searchParams}`);\n      const result = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      // Convert search results back to papers\n      const searchResults = result.data.results;\n      const foundPapers = searchResults\n        .map((searchResult: any) => papers.find(p => p.id === searchResult.paperId))\n        .filter(Boolean);\n\n      return foundPapers;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      return [];\n    }\n  }, [papers]);\n\n  const refreshPapers = useCallback(async () => {\n    await fetchPapers();\n  }, [fetchPapers]);\n\n  // Load papers on mount\n  useEffect(() => {\n    if (papers.length === 0) {\n      fetchPapers();\n    }\n  }, [fetchPapers, papers.length]);\n\n  return {\n    papers,\n    loading,\n    error,\n    fetchPapers,\n    fetchPaper,\n    updatePaper,\n    deletePaper,\n    searchPapers,\n    refreshPapers,\n  };\n};\n\n// Hook for managing paper state\nexport const usePaperState = (paperId: string | null) => {\n  const { papers, currentPaper, setCurrentPaper } = useAppStore();\n  const [paper, setPaper] = useState<Paper | null>(null);\n\n  useEffect(() => {\n    if (paperId) {\n      const foundPaper = papers.find(p => p.id === paperId);\n      setPaper(foundPaper || null);\n      \n      if (foundPaper && currentPaper?.id !== paperId) {\n        setCurrentPaper(paperId);\n      }\n    } else {\n      setPaper(null);\n    }\n  }, [paperId, papers, currentPaper, setCurrentPaper]);\n\n  return paper;\n};\n\n// Hook for paper statistics\nexport const usePaperStats = () => {\n  const { papers, readingSessions, annotations } = useAppStore();\n\n  const stats = {\n    totalPapers: papers.length,\n    totalSessions: readingSessions.length,\n    totalAnnotations: annotations.length,\n    averageReadingProgress: readingSessions.length > 0 \n      ? readingSessions.reduce((sum, session) => sum + session.progress, 0) / readingSessions.length\n      : 0,\n    papersWithAnnotations: new Set(annotations.map(a => a.paperId)).size,\n    mostActiveDay: getMostActiveDay(readingSessions),\n    topAuthors: <AUTHORS>\n    topTags: getTopTags(papers),\n  };\n\n  return stats;\n};\n\nfunction getMostActiveDay(sessions: any[]): string {\n  const dayCount: { [key: string]: number } = {};\n  \n  sessions.forEach(session => {\n    const day = new Date(session.startTime).toLocaleDateString('en-US', { weekday: 'long' });\n    dayCount[day] = (dayCount[day] || 0) + 1;\n  });\n\n  return Object.entries(dayCount).reduce((a, b) => dayCount[a[0]] > dayCount[b[0]] ? a : b)?.[0] || 'No data';\n}\n\nfunction getTopAuthors(papers: Paper[]): Array<{ name: string; count: number }> {\n  const authorCount: { [key: string]: number } = {};\n  \n  papers.forEach(paper => {\n    paper.authors.forEach(author => {\n      authorCount[author] = (authorCount[author] || 0) + 1;\n    });\n  });\n\n  return Object.entries(authorCount)\n    .map(([name, count]) => ({ name, count }))\n    .sort((a, b) => b.count - a.count)\n    .slice(0, 5);\n}\n\nfunction getTopTags(papers: Paper[]): Array<{ tag: string; count: number }> {\n  const tagCount: { [key: string]: number } = {};\n  \n  papers.forEach(paper => {\n    paper.tags.forEach(tag => {\n      tagCount[tag] = (tagCount[tag] || 0) + 1;\n    });\n  });\n\n  return Object.entries(tagCount)\n    .map(([tag, count]) => ({ tag, count }))\n    .sort((a, b) => b.count - a.count)\n    .slice(0, 10);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;;;;;AAcO,MAAM,YAAY;;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EACJ,MAAM,EACN,SAAS,EACT,QAAQ,EACR,WAAW,EACX,UAAU,cAAc,EACzB,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAC7B,SAAS;QACX;4CAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAC9B,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,gBAAa,CAAC,MAAM;gBACjD,MAAM,SAA2C,MAAM,SAAS,IAAI;gBAEpE,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC1D;gBAEA,IAAI,OAAO,IAAI,EAAE;oBACf,UAAU,OAAO,IAAI,CAAC,MAAM;gBAC9B;YACF,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAClF,SAAS;gBACT,eAAe;YACjB,SAAU;gBACR,WAAW;YACb;QACF;6CAAG;QAAC;QAAW;KAAe;IAE9B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,OAAO;YACpC,IAAI;gBACF,SAAS;gBAET,MAAM,WAAW,MAAM,MAAM,AAAC,GAA6B,OAA3B,0HAAA,CAAA,gBAAa,CAAC,MAAM,EAAC,QAAS,OAAH;gBAC3D,MAAM,SAA6B,MAAM,SAAS,IAAI;gBAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC1D;gBAEA,OAAO,OAAO,IAAI,IAAI;YACxB,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAClF,SAAS;gBACT,OAAO;YACT;QACF;4CAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,OAAO,IAAY;YACjD,IAAI;gBACF,SAAS;gBAET,MAAM,gBAAgB,OAAO,IAAI;wEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;gBAChD,IAAI,CAAC,eAAe;oBAClB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,eAAe;oBAAE,GAAG,aAAa;oBAAE,GAAG,OAAO;gBAAC;gBAEpD,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,gBAAa,CAAC,MAAM,EAAE;oBACjD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;gBAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC1D;gBAEA,qBAAqB;gBACrB,UAAU,OAAO,GAAG;0DAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,eAAe;;gBAEvD,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAClF,SAAS;gBACT,eAAe;gBACf,OAAO;YACT;QACF;6CAAG;QAAC;QAAQ;QAAW;KAAe;IAEtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,OAAO;YACrC,IAAI;gBACF,SAAS;gBAET,MAAM,WAAW,MAAM,MAAM,AAAC,GAA6B,OAA3B,0HAAA,CAAA,gBAAa,CAAC,MAAM,EAAC,QAAS,OAAH,KAAM;oBAC/D,QAAQ;gBACV;gBAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;gBAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC1D;gBAEA,qBAAqB;gBACrB,YAAY;gBAEZ,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAClF,SAAS;gBACT,eAAe;gBACf,OAAO;YACT;QACF;6CAAG;QAAC;QAAa;KAAe;IAEhC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,eAAO;gBAAe,2EAAiB,EAAE;YACxE,IAAI;gBACF,SAAS;gBAET,MAAM,eAAe,IAAI,gBAAgB;oBACvC,GAAG;oBACH,SAAS,KAAK,SAAS,CAAC;gBAC1B;gBAEA,MAAM,WAAW,MAAM,MAAM,AAAC,GAA0B,OAAxB,0HAAA,CAAA,gBAAa,CAAC,MAAM,EAAC,KAAgB,OAAb;gBACxD,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC1D;gBAEA,wCAAwC;gBACxC,MAAM,gBAAgB,OAAO,IAAI,CAAC,OAAO;gBACzC,MAAM,cAAc,cACjB,GAAG;uEAAC,CAAC,eAAsB,OAAO,IAAI;+EAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,OAAO;;sEACzE,MAAM,CAAC;gBAEV,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAClF,SAAS;gBACT,OAAO,EAAE;YACX;QACF;8CAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAChC,MAAM;QACR;+CAAG;QAAC;KAAY;IAEhB,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,OAAO,MAAM,KAAK,GAAG;gBACvB;YACF;QACF;8BAAG;QAAC;QAAa,OAAO,MAAM;KAAC;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA9Ka;;QAUP,8HAAA,CAAA,cAAW;;;AAuKV,MAAM,gBAAgB,CAAC;;IAC5B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC5D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,SAAS;gBACX,MAAM,aAAa,OAAO,IAAI;0DAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;gBAC7C,SAAS,cAAc;gBAEvB,IAAI,cAAc,CAAA,yBAAA,mCAAA,aAAc,EAAE,MAAK,SAAS;oBAC9C,gBAAgB;gBAClB;YACF,OAAO;gBACL,SAAS;YACX;QACF;kCAAG;QAAC;QAAS;QAAQ;QAAc;KAAgB;IAEnD,OAAO;AACT;IAlBa;;QACuC,8HAAA,CAAA,cAAW;;;AAoBxD,MAAM,gBAAgB;;IAC3B,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAE3D,MAAM,QAAQ;QACZ,aAAa,OAAO,MAAM;QAC1B,eAAe,gBAAgB,MAAM;QACrC,kBAAkB,YAAY,MAAM;QACpC,wBAAwB,gBAAgB,MAAM,GAAG,IAC7C,gBAAgB,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,QAAQ,EAAE,KAAK,gBAAgB,MAAM,GAC5F;QACJ,uBAAuB,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,GAAG,IAAI;QACpE,eAAe,iBAAiB;QAChC,YAAY,cAAc;QAC1B,SAAS,WAAW;IACtB;IAEA,OAAO;AACT;IAjBa;;QACsC,8HAAA,CAAA,cAAW;;;AAkB9D,SAAS,iBAAiB,QAAe;QAQhC;IAPP,MAAM,WAAsC,CAAC;IAE7C,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,MAAM,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC,SAAS;YAAE,SAAS;QAAO;QACtF,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI;IACzC;IAEA,OAAO,EAAA,yBAAA,OAAO,OAAO,CAAC,UAAU,MAAM,CAAC,CAAC,GAAG,IAAM,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,gBAAhF,6CAAA,sBAAoF,CAAC,EAAE,KAAI;AACpG;AAEA,SAAS,cAAc,MAAe;IACpC,MAAM,cAAyC,CAAC;IAEhD,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,OAAO,CAAC,OAAO,CAAC,CAAA;YACpB,WAAW,CAAC,OAAO,GAAG,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,IAAI;QACrD;IACF;IAEA,OAAO,OAAO,OAAO,CAAC,aACnB,GAAG,CAAC;YAAC,CAAC,MAAM,MAAM;eAAM;YAAE;YAAM;QAAM;OACtC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG;AACd;AAEA,SAAS,WAAW,MAAe;IACjC,MAAM,WAAsC,CAAC;IAE7C,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,IAAI,CAAC,OAAO,CAAC,CAAA;YACjB,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI;QACzC;IACF;IAEA,OAAO,OAAO,OAAO,CAAC,UACnB,GAAG,CAAC;YAAC,CAAC,KAAK,MAAM;eAAM;YAAE;YAAK;QAAM;OACpC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 2955, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/views/LibraryView.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Input, Button, Empty, Typography, Dropdown, Pagination } from 'antd';\nimport {\n  SearchOutlined,\n  PlusOutlined,\n  FilterOutlined,\n  SortAscendingOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store/useAppStore';\nimport { Paper } from '@/lib/types';\nimport FileUpload from '@/components/upload/FileUpload';\nimport { LibraryViewSkeleton } from '@/components/common/LoadingStates';\nimport PaperCard from '@/components/library/PaperCard';\nimport { VIEW_MODES } from '@/lib/constants';\nimport { usePapers } from '@/hooks/usePapers';\n\nconst { Search } = Input;\nconst { Title } = Typography;\n\nconst LibraryView: React.FC = () => {\n  const {\n    setCurrentPaper,\n    setCurrentView,\n    isLoading,\n  } = useAppStore();\n\n  const { papers, deletePaper } = usePapers();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredPapers, setFilteredPapers] = useState<Paper[]>(papers);\n  const [showUpload, setShowUpload] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize] = useState(12);\n\n  useEffect(() => {\n    // Filter papers based on search term\n    if (searchTerm) {\n      const filtered = papers.filter(paper =>\n        paper.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        paper.authors.some(author => author.toLowerCase().includes(searchTerm.toLowerCase())) ||\n        paper.abstract.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        paper.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n      setFilteredPapers(filtered);\n    } else {\n      setFilteredPapers(papers);\n    }\n    setCurrentPage(1);\n  }, [searchTerm, papers]);\n\n  const handlePaperClick = (paper: Paper) => {\n    setCurrentPaper(paper.id);\n    setCurrentView(VIEW_MODES.READER);\n  };\n\n  const handleUploadSuccess = (paperId: string) => {\n    setShowUpload(false);\n    // Optionally navigate to the uploaded paper\n    setCurrentPaper(paperId);\n    setCurrentView(VIEW_MODES.READER);\n  };\n\n  const handleDeletePaper = async (paper: Paper) => {\n    await deletePaper(paper.id);\n  };\n\n\n\n  const sortMenuItems = [\n    {\n      key: 'title',\n      label: 'Sort by Title',\n    },\n    {\n      key: 'date',\n      label: 'Sort by Date',\n    },\n    {\n      key: 'author',\n      label: 'Sort by Author',\n    },\n  ];\n\n  const filterMenuItems = [\n    {\n      key: 'all',\n      label: 'All Papers',\n    },\n    {\n      key: 'recent',\n      label: 'Recently Added',\n    },\n    {\n      key: 'favorites',\n      label: 'Favorites',\n    },\n  ];\n\n  // Pagination\n  const startIndex = (currentPage - 1) * pageSize;\n  const endIndex = startIndex + pageSize;\n  const paginatedPapers = filteredPapers.slice(startIndex, endIndex);\n\n  if (isLoading) {\n    return <LibraryViewSkeleton />;\n  }\n\n  return (\n    <div className=\"p-6 h-full overflow-auto\">\n      {/* Header */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <Title level={2} className=\"m-0\">\n            Paper Library\n          </Title>\n          <Button \n            type=\"primary\" \n            icon={<PlusOutlined />}\n            onClick={() => setShowUpload(!showUpload)}\n            size=\"large\"\n          >\n            Add Paper\n          </Button>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"flex items-center space-x-4 mb-4\">\n          <Search\n            placeholder=\"Search papers, authors, or keywords...\"\n            allowClear\n            size=\"large\"\n            className=\"flex-1\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            prefix={<SearchOutlined />}\n          />\n          \n          <Dropdown menu={{ items: sortMenuItems }} placement=\"bottomRight\">\n            <Button icon={<SortAscendingOutlined />} size=\"large\">\n              Sort\n            </Button>\n          </Dropdown>\n          \n          <Dropdown menu={{ items: filterMenuItems }} placement=\"bottomRight\">\n            <Button icon={<FilterOutlined />} size=\"large\">\n              Filter\n            </Button>\n          </Dropdown>\n        </div>\n\n        {/* Stats */}\n        <div className=\"flex items-center space-x-6 text-gray-600\">\n          <span>{filteredPapers.length} papers</span>\n          {searchTerm && (\n            <span>Filtered from {papers.length} total</span>\n          )}\n        </div>\n      </div>\n\n      {/* Upload Section */}\n      {showUpload && (\n        <Card className=\"mb-6\">\n          <FileUpload onUploadSuccess={handleUploadSuccess} />\n        </Card>\n      )}\n\n      {/* Papers Grid */}\n      {paginatedPapers.length === 0 ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description={\n            searchTerm ? 'No papers found matching your search' : 'No papers in your library yet'\n          }\n        >\n          {!searchTerm && (\n            <Button \n              type=\"primary\" \n              icon={<PlusOutlined />}\n              onClick={() => setShowUpload(true)}\n            >\n              Add Your First Paper\n            </Button>\n          )}\n        </Empty>\n      ) : (\n        <>\n          <Row gutter={[16, 16]}>\n            {paginatedPapers.map((paper) => (\n              <Col xs={24} sm={12} lg={8} xl={6} key={paper.id}>\n                <PaperCard\n                  paper={paper}\n                  onView={handlePaperClick}\n                  onDelete={handleDeletePaper}\n                  className=\"h-full\"\n                />\n              </Col>\n            ))}\n          </Row>\n\n          {/* Pagination */}\n          {filteredPapers.length > pageSize && (\n            <div className=\"flex justify-center mt-8\">\n              <Pagination\n                current={currentPage}\n                total={filteredPapers.length}\n                pageSize={pageSize}\n                onChange={setCurrentPage}\n                showSizeChanger={false}\n                showQuickJumper\n                showTotal={(total, range) => \n                  `${range[0]}-${range[1]} of ${total} papers`\n                }\n              />\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default LibraryView;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAMA;AAEA;AACA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;;AAkBA,MAAM,EAAE,MAAM,EAAE,GAAG,mLAAA,CAAA,QAAK;AACxB,MAAM,EAAE,KAAK,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE5B,MAAM,cAAwB;;IAC5B,MAAM,EACJ,eAAe,EACf,cAAc,EACd,SAAS,EACV,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD;IACxC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,qCAAqC;YACrC,IAAI,YAAY;gBACd,MAAM,WAAW,OAAO,MAAM;sDAAC,CAAA,QAC7B,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,OAAO,CAAC,IAAI;8DAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gEACjF,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,MAAM,IAAI,CAAC,IAAI;8DAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;;gBAE1E,kBAAkB;YACpB,OAAO;gBACL,kBAAkB;YACpB;YACA,eAAe;QACjB;gCAAG;QAAC;QAAY;KAAO;IAEvB,MAAM,mBAAmB,CAAC;QACxB,gBAAgB,MAAM,EAAE;QACxB,eAAe,0HAAA,CAAA,aAAU,CAAC,MAAM;IAClC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,4CAA4C;QAC5C,gBAAgB;QAChB,eAAe,0HAAA,CAAA,aAAU,CAAC,MAAM;IAClC;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,YAAY,MAAM,EAAE;IAC5B;IAIA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;KACD;IAED,aAAa;IACb,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAC9B,MAAM,kBAAkB,eAAe,KAAK,CAAC,YAAY;IAEzD,IAAI,WAAW;QACb,qBAAO,6LAAC,gJAAA,CAAA,sBAAmB;;;;;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,OAAO;gCAAG,WAAU;0CAAM;;;;;;0CAGjC,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gCACnB,SAAS,IAAM,cAAc,CAAC;gCAC9B,MAAK;0CACN;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,aAAY;gCACZ,UAAU;gCACV,MAAK;gCACL,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;0CAGzB,6LAAC,yLAAA,CAAA,WAAQ;gCAAC,MAAM;oCAAE,OAAO;gCAAc;gCAAG,WAAU;0CAClD,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,uOAAA,CAAA,wBAAqB;;;;;oCAAK,MAAK;8CAAQ;;;;;;;;;;;0CAKxD,6LAAC,yLAAA,CAAA,WAAQ;gCAAC,MAAM;oCAAE,OAAO;gCAAgB;gCAAG,WAAU;0CACpD,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oCAAK,MAAK;8CAAQ;;;;;;;;;;;;;;;;;kCAOnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAM,eAAe,MAAM;oCAAC;;;;;;;4BAC5B,4BACC,6LAAC;;oCAAK;oCAAe,OAAO,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;YAMxC,4BACC,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,6IAAA,CAAA,UAAU;oBAAC,iBAAiB;;;;;;;;;;;YAKhC,gBAAgB,MAAM,KAAK,kBAC1B,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,mLAAA,CAAA,QAAK,CAAC,sBAAsB;gBACnC,aACE,aAAa,yCAAyC;0BAGvD,CAAC,4BACA,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,SAAS,IAAM,cAAc;8BAC9B;;;;;;;;;;yEAML;;kCACE,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;kCAClB,gBAAgB,GAAG,CAAC,CAAC,sBACpB,6LAAC,+KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CAC9B,cAAA,6LAAC,6IAAA,CAAA,UAAS;oCACR,OAAO;oCACP,QAAQ;oCACR,UAAU;oCACV,WAAU;;;;;;+BAL0B,MAAM,EAAE;;;;;;;;;;oBAYnD,eAAe,MAAM,GAAG,0BACvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,aAAU;4BACT,SAAS;4BACT,OAAO,eAAe,MAAM;4BAC5B,UAAU;4BACV,UAAU;4BACV,iBAAiB;4BACjB,eAAe;4BACf,WAAW,CAAC,OAAO,QACjB,AAAC,GAAc,OAAZ,KAAK,CAAC,EAAE,EAAC,KAAkB,OAAf,KAAK,CAAC,EAAE,EAAC,QAAY,OAAN,OAAM;;;;;;;;;;;;;;;;;;;AAStD;GAvMM;;QAKA,8HAAA,CAAA,cAAW;QAEiB,4HAAA,CAAA,YAAS;;;KAPrC;uCAyMS", "debugId": null}}, {"offset": {"line": 3337, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/annotation-storage.ts"], "sourcesContent": ["import { Annotation, AnnotationGroup, AnnotationFilter, AnnotationStats } from './annotation-types';\n\nclass AnnotationStorage {\n  private readonly STORAGE_KEY = 'documancer_annotations';\n  private readonly API_BASE = '/api/annotations';\n\n  // Local storage methods\n  private getLocalAnnotations(): Record<string, AnnotationGroup> {\n    if (typeof window === 'undefined') return {};\n    \n    try {\n      const stored = localStorage.getItem(this.STORAGE_KEY);\n      return stored ? JSON.parse(stored) : {};\n    } catch (error) {\n      console.error('Failed to load annotations from localStorage:', error);\n      return {};\n    }\n  }\n\n  private saveLocalAnnotations(annotations: Record<string, AnnotationGroup>): void {\n    if (typeof window === 'undefined') return;\n    \n    try {\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(annotations));\n    } catch (error) {\n      console.error('Failed to save annotations to localStorage:', error);\n    }\n  }\n\n  // API methods for server-side persistence\n  private async apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {\n    try {\n      const response = await fetch(`${this.API_BASE}${endpoint}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`API request failed: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error('API request failed:', error);\n      throw error;\n    }\n  }\n\n  // Public methods\n  async getAnnotations(paperId: string): Promise<Annotation[]> {\n    try {\n      // Try to get from server first\n      const response = await this.apiRequest(`/${paperId}`);\n      return response.data || [];\n    } catch (error) {\n      // Fallback to local storage\n      const localAnnotations = this.getLocalAnnotations();\n      return localAnnotations[paperId]?.annotations || [];\n    }\n  }\n\n  async saveAnnotation(annotation: Annotation): Promise<Annotation> {\n    try {\n      // Save to server\n      const response = await this.apiRequest('', {\n        method: 'POST',\n        body: JSON.stringify(annotation),\n      });\n      \n      return response.data;\n    } catch (error) {\n      // Fallback to local storage\n      const localAnnotations = this.getLocalAnnotations();\n      \n      if (!localAnnotations[annotation.paperId]) {\n        localAnnotations[annotation.paperId] = {\n          paperId: annotation.paperId,\n          annotations: [],\n          lastModified: new Date(),\n        };\n      }\n\n      const existingIndex = localAnnotations[annotation.paperId].annotations.findIndex(\n        a => a.id === annotation.id\n      );\n\n      if (existingIndex >= 0) {\n        localAnnotations[annotation.paperId].annotations[existingIndex] = annotation;\n      } else {\n        localAnnotations[annotation.paperId].annotations.push(annotation);\n      }\n\n      localAnnotations[annotation.paperId].lastModified = new Date();\n      this.saveLocalAnnotations(localAnnotations);\n      \n      return annotation;\n    }\n  }\n\n  async updateAnnotation(annotation: Annotation): Promise<Annotation> {\n    annotation.updatedAt = new Date();\n    return this.saveAnnotation(annotation);\n  }\n\n  async deleteAnnotation(paperId: string, annotationId: string): Promise<void> {\n    try {\n      // Delete from server\n      await this.apiRequest(`/${annotationId}`, {\n        method: 'DELETE',\n      });\n    } catch (error) {\n      // Fallback to local storage\n      const localAnnotations = this.getLocalAnnotations();\n      \n      if (localAnnotations[paperId]) {\n        localAnnotations[paperId].annotations = localAnnotations[paperId].annotations.filter(\n          a => a.id !== annotationId\n        );\n        localAnnotations[paperId].lastModified = new Date();\n        this.saveLocalAnnotations(localAnnotations);\n      }\n    }\n  }\n\n  async filterAnnotations(paperId: string, filter: AnnotationFilter): Promise<Annotation[]> {\n    const annotations = await this.getAnnotations(paperId);\n    \n    return annotations.filter(annotation => {\n      if (filter.type && annotation.type !== filter.type) return false;\n      if (filter.pageNumber && annotation.pageNumber !== filter.pageNumber) return false;\n      if (filter.searchText) {\n        const searchLower = filter.searchText.toLowerCase();\n        const contentMatch = annotation.content.toLowerCase().includes(searchLower);\n        const selectionMatch = annotation.selection?.selectedText.toLowerCase().includes(searchLower);\n        if (!contentMatch && !selectionMatch) return false;\n      }\n      if (filter.dateRange) {\n        const createdAt = new Date(annotation.createdAt);\n        if (createdAt < filter.dateRange.start || createdAt > filter.dateRange.end) return false;\n      }\n      return true;\n    });\n  }\n\n  async getAnnotationStats(paperId: string): Promise<AnnotationStats> {\n    const annotations = await this.getAnnotations(paperId);\n    \n    const stats: AnnotationStats = {\n      totalAnnotations: annotations.length,\n      highlightCount: annotations.filter(a => a.type === 'highlight').length,\n      noteCount: annotations.filter(a => a.type === 'note').length,\n      bookmarkCount: annotations.filter(a => a.type === 'bookmark').length,\n      pagesWithAnnotations: [...new Set(annotations.map(a => a.pageNumber))].sort((a, b) => a - b),\n    };\n\n    return stats;\n  }\n\n  // Bulk operations\n  async exportAnnotations(paperId: string): Promise<string> {\n    const annotations = await this.getAnnotations(paperId);\n    return JSON.stringify(annotations, null, 2);\n  }\n\n  async importAnnotations(paperId: string, annotationsJson: string): Promise<void> {\n    try {\n      const annotations: Annotation[] = JSON.parse(annotationsJson);\n      \n      for (const annotation of annotations) {\n        annotation.paperId = paperId; // Ensure correct paper ID\n        await this.saveAnnotation(annotation);\n      }\n    } catch (error) {\n      throw new Error('Invalid annotations format');\n    }\n  }\n\n  // Sync methods\n  async syncWithServer(paperId: string): Promise<void> {\n    try {\n      const localAnnotations = this.getLocalAnnotations();\n      const localGroup = localAnnotations[paperId];\n      \n      if (!localGroup) return;\n\n      // Upload local annotations to server\n      await this.apiRequest('/sync', {\n        method: 'POST',\n        body: JSON.stringify(localGroup),\n      });\n\n      console.log('Annotations synced with server');\n    } catch (error) {\n      console.error('Failed to sync annotations:', error);\n    }\n  }\n}\n\nexport const annotationStorage = new AnnotationStorage();\n"], "names": [], "mappings": ";;;;;AAEA,MAAM;IAIJ,wBAAwB;IAChB,sBAAuD;QAC7D;;QAEA,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW;YACpD,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,CAAC;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,OAAO,CAAC;QACV;IACF;IAEQ,qBAAqB,WAA4C,EAAQ;QAC/E;;QAEA,IAAI;YACF,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;QAC/D;IACF;IAEA,0CAA0C;IAC1C,MAAc,WAAW,QAAgB,EAA2C;YAAzC,UAAA,iEAAuB,CAAC;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAAkB,OAAhB,IAAI,CAAC,QAAQ,EAAY,OAAT,WAAY;gBAC1D,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,uBAA0C,OAApB,SAAS,UAAU;YAC5D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,MAAM,eAAe,OAAe,EAAyB;QAC3D,IAAI;YACF,+BAA+B;YAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,UAAU,CAAC,AAAC,IAAW,OAAR;YAC3C,OAAO,SAAS,IAAI,IAAI,EAAE;QAC5B,EAAE,OAAO,OAAO;gBAGP;YAFP,4BAA4B;YAC5B,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;YACjD,OAAO,EAAA,4BAAA,gBAAgB,CAAC,QAAQ,cAAzB,gDAAA,0BAA2B,WAAW,KAAI,EAAE;QACrD;IACF;IAEA,MAAM,eAAe,UAAsB,EAAuB;QAChE,IAAI;YACF,iBAAiB;YACjB,MAAM,WAAW,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI;gBACzC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,4BAA4B;YAC5B,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;YAEjD,IAAI,CAAC,gBAAgB,CAAC,WAAW,OAAO,CAAC,EAAE;gBACzC,gBAAgB,CAAC,WAAW,OAAO,CAAC,GAAG;oBACrC,SAAS,WAAW,OAAO;oBAC3B,aAAa,EAAE;oBACf,cAAc,IAAI;gBACpB;YACF;YAEA,MAAM,gBAAgB,gBAAgB,CAAC,WAAW,OAAO,CAAC,CAAC,WAAW,CAAC,SAAS,CAC9E,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;YAG7B,IAAI,iBAAiB,GAAG;gBACtB,gBAAgB,CAAC,WAAW,OAAO,CAAC,CAAC,WAAW,CAAC,cAAc,GAAG;YACpE,OAAO;gBACL,gBAAgB,CAAC,WAAW,OAAO,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC;YACxD;YAEA,gBAAgB,CAAC,WAAW,OAAO,CAAC,CAAC,YAAY,GAAG,IAAI;YACxD,IAAI,CAAC,oBAAoB,CAAC;YAE1B,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,UAAsB,EAAuB;QAClE,WAAW,SAAS,GAAG,IAAI;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,MAAM,iBAAiB,OAAe,EAAE,YAAoB,EAAiB;QAC3E,IAAI;YACF,qBAAqB;YACrB,MAAM,IAAI,CAAC,UAAU,CAAC,AAAC,IAAgB,OAAb,eAAgB;gBACxC,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,4BAA4B;YAC5B,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;YAEjD,IAAI,gBAAgB,CAAC,QAAQ,EAAE;gBAC7B,gBAAgB,CAAC,QAAQ,CAAC,WAAW,GAAG,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAClF,CAAA,IAAK,EAAE,EAAE,KAAK;gBAEhB,gBAAgB,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI;gBAC7C,IAAI,CAAC,oBAAoB,CAAC;YAC5B;QACF;IACF;IAEA,MAAM,kBAAkB,OAAe,EAAE,MAAwB,EAAyB;QACxF,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,OAAO,YAAY,MAAM,CAAC,CAAA;YACxB,IAAI,OAAO,IAAI,IAAI,WAAW,IAAI,KAAK,OAAO,IAAI,EAAE,OAAO;YAC3D,IAAI,OAAO,UAAU,IAAI,WAAW,UAAU,KAAK,OAAO,UAAU,EAAE,OAAO;YAC7E,IAAI,OAAO,UAAU,EAAE;oBAGE;gBAFvB,MAAM,cAAc,OAAO,UAAU,CAAC,WAAW;gBACjD,MAAM,eAAe,WAAW,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;gBAC/D,MAAM,kBAAiB,wBAAA,WAAW,SAAS,cAApB,4CAAA,sBAAsB,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC;gBACjF,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,OAAO;YAC/C;YACA,IAAI,OAAO,SAAS,EAAE;gBACpB,MAAM,YAAY,IAAI,KAAK,WAAW,SAAS;gBAC/C,IAAI,YAAY,OAAO,SAAS,CAAC,KAAK,IAAI,YAAY,OAAO,SAAS,CAAC,GAAG,EAAE,OAAO;YACrF;YACA,OAAO;QACT;IACF;IAEA,MAAM,mBAAmB,OAAe,EAA4B;QAClE,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,MAAM,QAAyB;YAC7B,kBAAkB,YAAY,MAAM;YACpC,gBAAgB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;YACtE,WAAW,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;YAC5D,eAAe,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,MAAM;YACpE,sBAAsB;mBAAI,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;aAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAC5F;QAEA,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,OAAe,EAAmB;QACxD,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAC9C,OAAO,KAAK,SAAS,CAAC,aAAa,MAAM;IAC3C;IAEA,MAAM,kBAAkB,OAAe,EAAE,eAAuB,EAAiB;QAC/E,IAAI;YACF,MAAM,cAA4B,KAAK,KAAK,CAAC;YAE7C,KAAK,MAAM,cAAc,YAAa;gBACpC,WAAW,OAAO,GAAG,SAAS,0BAA0B;gBACxD,MAAM,IAAI,CAAC,cAAc,CAAC;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,eAAe;IACf,MAAM,eAAe,OAAe,EAAiB;QACnD,IAAI;YACF,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;YACjD,MAAM,aAAa,gBAAgB,CAAC,QAAQ;YAE5C,IAAI,CAAC,YAAY;YAEjB,qCAAqC;YACrC,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS;gBAC7B,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;;QAnMA,+KAAiB,eAAc;QAC/B,+KAAiB,YAAW;;AAmM9B;AAEO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 3525, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/hooks/useAnnotations.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { Annotation, AnnotationFilter, AnnotationStats, TextSelection, AnnotationColor } from '@/lib/annotation-types';\nimport { annotationStorage } from '@/lib/annotation-storage';\nimport { message } from 'antd';\n\nexport function useAnnotations(paperId: string) {\n  const [annotations, setAnnotations] = useState<Annotation[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedAnnotation, setSelectedAnnotation] = useState<Annotation | null>(null);\n  const [filter, setFilter] = useState<AnnotationFilter>({});\n\n  // Load annotations\n  const loadAnnotations = useCallback(async () => {\n    if (!paperId) return;\n    \n    try {\n      setLoading(true);\n      setError(null);\n      \n      const loadedAnnotations = await annotationStorage.getAnnotations(paperId);\n      setAnnotations(loadedAnnotations);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to load annotations';\n      setError(errorMessage);\n      message.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [paperId]);\n\n  // Create annotation\n  const createAnnotation = useCallback(async (\n    type: Annotation['type'],\n    content: string,\n    selection?: TextSelection,\n    color?: AnnotationColor\n  ): Promise<Annotation | null> => {\n    try {\n      const annotation: Annotation = {\n        id: `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        paperId,\n        type,\n        pageNumber: selection?.pageNumber || 1,\n        selection,\n        content,\n        color,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      const savedAnnotation = await annotationStorage.saveAnnotation(annotation);\n      setAnnotations(prev => [...prev, savedAnnotation]);\n      \n      message.success(`${type.charAt(0).toUpperCase() + type.slice(1)} created successfully`);\n      return savedAnnotation;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to create annotation';\n      message.error(errorMessage);\n      return null;\n    }\n  }, [paperId]);\n\n  // Update annotation\n  const updateAnnotation = useCallback(async (\n    annotationId: string,\n    updates: Partial<Annotation>\n  ): Promise<boolean> => {\n    try {\n      const existingAnnotation = annotations.find(a => a.id === annotationId);\n      if (!existingAnnotation) {\n        throw new Error('Annotation not found');\n      }\n\n      const updatedAnnotation = {\n        ...existingAnnotation,\n        ...updates,\n        updatedAt: new Date(),\n      };\n\n      await annotationStorage.updateAnnotation(updatedAnnotation);\n      \n      setAnnotations(prev => \n        prev.map(a => a.id === annotationId ? updatedAnnotation : a)\n      );\n      \n      message.success('Annotation updated successfully');\n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to update annotation';\n      message.error(errorMessage);\n      return false;\n    }\n  }, [annotations]);\n\n  // Delete annotation\n  const deleteAnnotation = useCallback(async (annotationId: string): Promise<boolean> => {\n    try {\n      await annotationStorage.deleteAnnotation(paperId, annotationId);\n      \n      setAnnotations(prev => prev.filter(a => a.id !== annotationId));\n      \n      if (selectedAnnotation?.id === annotationId) {\n        setSelectedAnnotation(null);\n      }\n      \n      message.success('Annotation deleted successfully');\n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to delete annotation';\n      message.error(errorMessage);\n      return false;\n    }\n  }, [paperId, selectedAnnotation]);\n\n  // Filter annotations\n  const filteredAnnotations = useCallback(async (newFilter: AnnotationFilter) => {\n    try {\n      setFilter(newFilter);\n      const filtered = await annotationStorage.filterAnnotations(paperId, newFilter);\n      return filtered;\n    } catch (err) {\n      console.error('Failed to filter annotations:', err);\n      return annotations;\n    }\n  }, [paperId, annotations]);\n\n  // Get annotation stats\n  const getStats = useCallback(async (): Promise<AnnotationStats | null> => {\n    try {\n      return await annotationStorage.getAnnotationStats(paperId);\n    } catch (err) {\n      console.error('Failed to get annotation stats:', err);\n      return null;\n    }\n  }, [paperId]);\n\n  // Export annotations\n  const exportAnnotations = useCallback(async (): Promise<string | null> => {\n    try {\n      const exported = await annotationStorage.exportAnnotations(paperId);\n      message.success('Annotations exported successfully');\n      return exported;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to export annotations';\n      message.error(errorMessage);\n      return null;\n    }\n  }, [paperId]);\n\n  // Import annotations\n  const importAnnotations = useCallback(async (annotationsJson: string): Promise<boolean> => {\n    try {\n      await annotationStorage.importAnnotations(paperId, annotationsJson);\n      await loadAnnotations(); // Reload annotations\n      message.success('Annotations imported successfully');\n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to import annotations';\n      message.error(errorMessage);\n      return false;\n    }\n  }, [paperId, loadAnnotations]);\n\n  // Sync with server\n  const syncAnnotations = useCallback(async (): Promise<boolean> => {\n    try {\n      await annotationStorage.syncWithServer(paperId);\n      await loadAnnotations(); // Reload annotations\n      message.success('Annotations synced successfully');\n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to sync annotations';\n      message.error(errorMessage);\n      return false;\n    }\n  }, [paperId, loadAnnotations]);\n\n  // Create highlight annotation\n  const createHighlight = useCallback((\n    selection: TextSelection,\n    color: AnnotationColor = 'yellow'\n  ) => {\n    return createAnnotation('highlight', selection.selectedText, selection, color);\n  }, [createAnnotation]);\n\n  // Create note annotation\n  const createNote = useCallback((\n    content: string,\n    selection?: TextSelection,\n    pageNumber: number = 1\n  ) => {\n    return createAnnotation('note', content, selection || { \n      startOffset: 0, \n      endOffset: 0, \n      selectedText: '', \n      pageNumber \n    });\n  }, [createAnnotation]);\n\n  // Create bookmark annotation\n  const createBookmark = useCallback((\n    pageNumber: number,\n    title: string = `Bookmark - Page ${pageNumber}`\n  ) => {\n    return createAnnotation('bookmark', title, {\n      startOffset: 0,\n      endOffset: 0,\n      selectedText: '',\n      pageNumber,\n    });\n  }, [createAnnotation]);\n\n  // Load annotations on mount\n  useEffect(() => {\n    loadAnnotations();\n  }, [loadAnnotations]);\n\n  return {\n    // State\n    annotations,\n    loading,\n    error,\n    selectedAnnotation,\n    filter,\n    \n    // Actions\n    loadAnnotations,\n    createAnnotation,\n    updateAnnotation,\n    deleteAnnotation,\n    setSelectedAnnotation,\n    \n    // Filtering\n    filteredAnnotations,\n    setFilter,\n    \n    // Stats and utilities\n    getStats,\n    exportAnnotations,\n    importAnnotations,\n    syncAnnotations,\n    \n    // Convenience methods\n    createHighlight,\n    createNote,\n    createBookmark,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;;AAEO,SAAS,eAAe,OAAe;;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,CAAC;IAExD,mBAAmB;IACnB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAClC,IAAI,CAAC,SAAS;YAEd,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,oBAAoB,MAAM,sIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;gBACjE,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YAChB,SAAU;gBACR,WAAW;YACb;QACF;sDAAG;QAAC;KAAQ;IAEZ,oBAAoB;IACpB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OACnC,MACA,SACA,WACA;YAEA,IAAI;gBACF,MAAM,aAAyB;oBAC7B,IAAI,AAAC,cAA2B,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;oBACrE;oBACA;oBACA,YAAY,CAAA,sBAAA,gCAAA,UAAW,UAAU,KAAI;oBACrC;oBACA;oBACA;oBACA,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBAEA,MAAM,kBAAkB,MAAM,sIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;gBAC/D;oEAAe,CAAA,OAAQ;+BAAI;4BAAM;yBAAgB;;gBAEjD,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,AAAC,GAA+C,OAA7C,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IAAG;gBAChE,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBACd,OAAO;YACT;QACF;uDAAG;QAAC;KAAQ;IAEZ,oBAAoB;IACpB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OACnC,cACA;YAEA,IAAI;gBACF,MAAM,qBAAqB,YAAY,IAAI;uFAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;gBAC1D,IAAI,CAAC,oBAAoB;oBACvB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,oBAAoB;oBACxB,GAAG,kBAAkB;oBACrB,GAAG,OAAO;oBACV,WAAW,IAAI;gBACjB;gBAEA,MAAM,sIAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC;gBAEzC;oEAAe,CAAA,OACb,KAAK,GAAG;4EAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,oBAAoB;;;gBAG5D,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBACd,OAAO;YACT;QACF;uDAAG;QAAC;KAAY;IAEhB,oBAAoB;IACpB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO;YAC1C,IAAI;gBACF,MAAM,sIAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC,SAAS;gBAElD;oEAAe,CAAA,OAAQ,KAAK,MAAM;4EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;;gBAEjD,IAAI,CAAA,+BAAA,yCAAA,mBAAoB,EAAE,MAAK,cAAc;oBAC3C,sBAAsB;gBACxB;gBAEA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBACd,OAAO;YACT;QACF;uDAAG;QAAC;QAAS;KAAmB;IAEhC,qBAAqB;IACrB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO;YAC7C,IAAI;gBACF,UAAU;gBACV,MAAM,WAAW,MAAM,sIAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC,SAAS;gBACpE,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,OAAO;YACT;QACF;0DAAG;QAAC;QAAS;KAAY;IAEzB,uBAAuB;IACvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC3B,IAAI;gBACF,OAAO,MAAM,sIAAA,CAAA,oBAAiB,CAAC,kBAAkB,CAAC;YACpD,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,OAAO;YACT;QACF;+CAAG;QAAC;KAAQ;IAEZ,qBAAqB;IACrB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACpC,IAAI;gBACF,MAAM,WAAW,MAAM,sIAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC;gBAC3D,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBACd,OAAO;YACT;QACF;wDAAG;QAAC;KAAQ;IAEZ,qBAAqB;IACrB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO;YAC3C,IAAI;gBACF,MAAM,sIAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC,SAAS;gBACnD,MAAM,mBAAmB,qBAAqB;gBAC9C,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBACd,OAAO;YACT;QACF;wDAAG;QAAC;QAAS;KAAgB;IAE7B,mBAAmB;IACnB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAClC,IAAI;gBACF,MAAM,sIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;gBACvC,MAAM,mBAAmB,qBAAqB;gBAC9C,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBACd,OAAO;YACT;QACF;sDAAG;QAAC;QAAS;KAAgB;IAE7B,8BAA8B;IAC9B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,SAClC;gBACA,yEAAyB;YAEzB,OAAO,iBAAiB,aAAa,UAAU,YAAY,EAAE,WAAW;QAC1E;sDAAG;QAAC;KAAiB;IAErB,yBAAyB;IACzB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,SAC7B,SACA;gBACA,8EAAqB;YAErB,OAAO,iBAAiB,QAAQ,SAAS,aAAa;gBACpD,aAAa;gBACb,WAAW;gBACX,cAAc;gBACd;YACF;QACF;iDAAG;QAAC;KAAiB;IAErB,6BAA6B;IAC7B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,SACjC;gBACA,yEAAgB,AAAC,mBAA6B,OAAX;YAEnC,OAAO,iBAAiB,YAAY,OAAO;gBACzC,aAAa;gBACb,WAAW;gBACX,cAAc;gBACd;YACF;QACF;qDAAG;QAAC;KAAiB;IAErB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QAEA,YAAY;QACZ;QACA;QAEA,sBAAsB;QACtB;QACA;QACA;QACA;QAEA,sBAAsB;QACtB;QACA;QACA;IACF;AACF;GAnPgB", "debugId": null}}, {"offset": {"line": 3815, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/hooks/useTextSelection.ts"], "sourcesContent": ["import { useState, useCallback, useEffect } from 'react';\nimport { TextSelection } from '@/lib/annotation-types';\n\nexport function useTextSelection(currentPage: number = 1) {\n  const [selection, setSelection] = useState<TextSelection | null>(null);\n  const [isSelecting, setIsSelecting] = useState(false);\n\n  // Get text selection from the DOM\n  const getTextSelection = useCallback((): TextSelection | null => {\n    const domSelection = window.getSelection();\n    \n    if (!domSelection || domSelection.rangeCount === 0) {\n      return null;\n    }\n\n    const range = domSelection.getRangeAt(0);\n    const selectedText = domSelection.toString().trim();\n    \n    if (!selectedText) {\n      return null;\n    }\n\n    // Find the PDF page element\n    const pdfPageElement = range.commonAncestorContainer.nodeType === Node.TEXT_NODE\n      ? range.commonAncestorContainer.parentElement?.closest('.react-pdf__Page')\n      : (range.commonAncestorContainer as Element).closest('.react-pdf__Page');\n\n    if (!pdfPageElement) {\n      return null;\n    }\n\n    // Get page number from the PDF page element\n    const pageNumber = currentPage; // Use current page as fallback\n\n    // Calculate relative positions within the page\n    const pageRect = pdfPageElement.getBoundingClientRect();\n    const rangeRect = range.getBoundingClientRect();\n\n    const textSelection: TextSelection = {\n      startOffset: range.startOffset,\n      endOffset: range.endOffset,\n      selectedText,\n      pageNumber,\n      boundingRect: rangeRect,\n    };\n\n    return textSelection;\n  }, [currentPage]);\n\n  // Handle text selection\n  const handleTextSelection = useCallback((event?: MouseEvent | React.MouseEvent) => {\n    // Small delay to ensure selection is complete\n    setTimeout(() => {\n      const textSelection = getTextSelection();\n      setSelection(textSelection);\n      setIsSelecting(false);\n    }, 10);\n  }, [getTextSelection]);\n\n  // Handle selection start\n  const handleSelectionStart = useCallback(() => {\n    setIsSelecting(true);\n    setSelection(null);\n  }, []);\n\n  // Clear selection\n  const clearSelection = useCallback(() => {\n    setSelection(null);\n    setIsSelecting(false);\n    \n    // Clear DOM selection\n    const domSelection = window.getSelection();\n    if (domSelection) {\n      domSelection.removeAllRanges();\n    }\n  }, []);\n\n  // Check if there's an active selection\n  const hasSelection = selection !== null && selection.selectedText.length > 0;\n\n  // Get selection position for positioning UI elements\n  const getSelectionPosition = useCallback(() => {\n    if (!selection?.boundingRect) return null;\n\n    const rect = selection.boundingRect;\n    return {\n      x: rect.left + rect.width / 2,\n      y: rect.top,\n      width: rect.width,\n      height: rect.height,\n    };\n  }, [selection]);\n\n  // Restore selection (useful for highlighting)\n  const restoreSelection = useCallback((textSelection: TextSelection) => {\n    try {\n      // This is a simplified version - in a real implementation,\n      // you'd need to find the exact text nodes and recreate the range\n      const textNodes = document.querySelectorAll('.react-pdf__Page__textContent span');\n      \n      for (const node of textNodes) {\n        if (node.textContent?.includes(textSelection.selectedText)) {\n          const range = document.createRange();\n          range.selectNodeContents(node);\n          \n          const domSelection = window.getSelection();\n          if (domSelection) {\n            domSelection.removeAllRanges();\n            domSelection.addRange(range);\n          }\n          break;\n        }\n      }\n    } catch (error) {\n      console.error('Failed to restore selection:', error);\n    }\n  }, []);\n\n  // Listen for selection changes\n  useEffect(() => {\n    const handleSelectionChange = () => {\n      const domSelection = window.getSelection();\n      if (domSelection && domSelection.toString().trim()) {\n        setIsSelecting(false);\n      }\n    };\n\n    document.addEventListener('selectionchange', handleSelectionChange);\n    \n    return () => {\n      document.removeEventListener('selectionchange', handleSelectionChange);\n    };\n  }, []);\n\n  // Listen for mouse events to detect selection\n  useEffect(() => {\n    const handleMouseDown = () => {\n      handleSelectionStart();\n    };\n\n    const handleMouseUp = (event: MouseEvent) => {\n      handleTextSelection(event);\n    };\n\n    // Only listen on PDF content areas\n    const pdfElements = document.querySelectorAll('.react-pdf__Page');\n    \n    pdfElements.forEach(element => {\n      element.addEventListener('mousedown', handleMouseDown);\n      element.addEventListener('mouseup', handleMouseUp);\n    });\n\n    return () => {\n      pdfElements.forEach(element => {\n        element.removeEventListener('mousedown', handleMouseDown);\n        element.removeEventListener('mouseup', handleMouseUp);\n      });\n    };\n  }, [handleTextSelection, handleSelectionStart]);\n\n  return {\n    // State\n    selection,\n    isSelecting,\n    hasSelection,\n    \n    // Actions\n    handleTextSelection,\n    handleSelectionStart,\n    clearSelection,\n    restoreSelection,\n    \n    // Utilities\n    getSelectionPosition,\n    getTextSelection,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAGO,SAAS;QAAiB,cAAA,iEAAsB;;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kCAAkC;IAClC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;gBAgB/B;YAfJ,MAAM,eAAe,OAAO,YAAY;YAExC,IAAI,CAAC,gBAAgB,aAAa,UAAU,KAAK,GAAG;gBAClD,OAAO;YACT;YAEA,MAAM,QAAQ,aAAa,UAAU,CAAC;YACtC,MAAM,eAAe,aAAa,QAAQ,GAAG,IAAI;YAEjD,IAAI,CAAC,cAAc;gBACjB,OAAO;YACT;YAEA,4BAA4B;YAC5B,MAAM,iBAAiB,MAAM,uBAAuB,CAAC,QAAQ,KAAK,KAAK,SAAS,IAC5E,+CAAA,MAAM,uBAAuB,CAAC,aAAa,cAA3C,mEAAA,6CAA6C,OAAO,CAAC,sBACrD,AAAC,MAAM,uBAAuB,CAAa,OAAO,CAAC;YAEvD,IAAI,CAAC,gBAAgB;gBACnB,OAAO;YACT;YAEA,4CAA4C;YAC5C,MAAM,aAAa,aAAa,+BAA+B;YAE/D,+CAA+C;YAC/C,MAAM,WAAW,eAAe,qBAAqB;YACrD,MAAM,YAAY,MAAM,qBAAqB;YAE7C,MAAM,gBAA+B;gBACnC,aAAa,MAAM,WAAW;gBAC9B,WAAW,MAAM,SAAS;gBAC1B;gBACA;gBACA,cAAc;YAChB;YAEA,OAAO;QACT;yDAAG;QAAC;KAAY;IAEhB,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACvC,8CAA8C;YAC9C;qEAAW;oBACT,MAAM,gBAAgB;oBACtB,aAAa;oBACb,eAAe;gBACjB;oEAAG;QACL;4DAAG;QAAC;KAAiB;IAErB,yBAAyB;IACzB,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACvC,eAAe;YACf,aAAa;QACf;6DAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YACjC,aAAa;YACb,eAAe;YAEf,sBAAsB;YACtB,MAAM,eAAe,OAAO,YAAY;YACxC,IAAI,cAAc;gBAChB,aAAa,eAAe;YAC9B;QACF;uDAAG,EAAE;IAEL,uCAAuC;IACvC,MAAM,eAAe,cAAc,QAAQ,UAAU,YAAY,CAAC,MAAM,GAAG;IAE3E,qDAAqD;IACrD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACvC,IAAI,EAAC,sBAAA,gCAAA,UAAW,YAAY,GAAE,OAAO;YAErC,MAAM,OAAO,UAAU,YAAY;YACnC,OAAO;gBACL,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;gBAC5B,GAAG,KAAK,GAAG;gBACX,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,MAAM;YACrB;QACF;6DAAG;QAAC;KAAU;IAEd,8CAA8C;IAC9C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACpC,IAAI;gBACF,2DAA2D;gBAC3D,iEAAiE;gBACjE,MAAM,YAAY,SAAS,gBAAgB,CAAC;gBAE5C,KAAK,MAAM,QAAQ,UAAW;wBACxB;oBAAJ,KAAI,oBAAA,KAAK,WAAW,cAAhB,wCAAA,kBAAkB,QAAQ,CAAC,cAAc,YAAY,GAAG;wBAC1D,MAAM,QAAQ,SAAS,WAAW;wBAClC,MAAM,kBAAkB,CAAC;wBAEzB,MAAM,eAAe,OAAO,YAAY;wBACxC,IAAI,cAAc;4BAChB,aAAa,eAAe;4BAC5B,aAAa,QAAQ,CAAC;wBACxB;wBACA;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;yDAAG,EAAE;IAEL,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;oEAAwB;oBAC5B,MAAM,eAAe,OAAO,YAAY;oBACxC,IAAI,gBAAgB,aAAa,QAAQ,GAAG,IAAI,IAAI;wBAClD,eAAe;oBACjB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,mBAAmB;YAE7C;8CAAO;oBACL,SAAS,mBAAmB,CAAC,mBAAmB;gBAClD;;QACF;qCAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;8DAAkB;oBACtB;gBACF;;YAEA,MAAM;4DAAgB,CAAC;oBACrB,oBAAoB;gBACtB;;YAEA,mCAAmC;YACnC,MAAM,cAAc,SAAS,gBAAgB,CAAC;YAE9C,YAAY,OAAO;8CAAC,CAAA;oBAClB,QAAQ,gBAAgB,CAAC,aAAa;oBACtC,QAAQ,gBAAgB,CAAC,WAAW;gBACtC;;YAEA;8CAAO;oBACL,YAAY,OAAO;sDAAC,CAAA;4BAClB,QAAQ,mBAAmB,CAAC,aAAa;4BACzC,QAAQ,mBAAmB,CAAC,WAAW;wBACzC;;gBACF;;QACF;qCAAG;QAAC;QAAqB;KAAqB;IAE9C,OAAO;QACL,QAAQ;QACR;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QAEA,YAAY;QACZ;QACA;IACF;AACF;GA7KgB", "debugId": null}}, {"offset": {"line": 4017, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/annotation-types.ts"], "sourcesContent": ["export interface TextSelection {\n  startOffset: number;\n  endOffset: number;\n  selectedText: string;\n  pageNumber: number;\n  boundingRect?: DOMRect;\n}\n\nexport interface Annotation {\n  id: string;\n  paperId: string;\n  type: 'highlight' | 'note' | 'bookmark';\n  pageNumber: number;\n  selection?: TextSelection;\n  content: string;\n  color?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  position?: {\n    x: number;\n    y: number;\n    width: number;\n    height: number;\n  };\n}\n\nexport interface AnnotationGroup {\n  paperId: string;\n  annotations: Annotation[];\n  lastModified: Date;\n}\n\nexport interface AnnotationFilter {\n  type?: Annotation['type'];\n  pageNumber?: number;\n  searchText?: string;\n  dateRange?: {\n    start: Date;\n    end: Date;\n  };\n}\n\nexport interface AnnotationStats {\n  totalAnnotations: number;\n  highlightCount: number;\n  noteCount: number;\n  bookmarkCount: number;\n  pagesWithAnnotations: number[];\n}\n\nexport const ANNOTATION_COLORS = {\n  yellow: '#FFEB3B',\n  green: '#4CAF50',\n  blue: '#2196F3',\n  red: '#F44336',\n  purple: '#9C27B0',\n  orange: '#FF9800',\n} as const;\n\nexport type AnnotationColor = keyof typeof ANNOTATION_COLORS;\n"], "names": [], "mappings": ";;;AAkDO,MAAM,oBAAoB;IAC/B,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 4037, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/annotations/AnnotationToolbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Button, Tooltip, Dropdown, ColorPicker, Input, Space, Popover } from 'antd';\nimport { \n  HighlightOutlined, \n  EditOutlined, \n  BookOutlined, \n  BgColorsOutlined,\n  MessageOutlined,\n  PlusOutlined \n} from '@ant-design/icons';\nimport { TextSelection, AnnotationColor, ANNOTATION_COLORS } from '@/lib/annotation-types';\n\ninterface AnnotationToolbarProps {\n  selection: TextSelection | null;\n  position?: { x: number; y: number; width: number; height: number } | null;\n  onCreateHighlight: (selection: TextSelection, color: AnnotationColor) => void;\n  onCreateNote: (selection: TextSelection, content: string) => void;\n  onCreateBookmark: (pageNumber: number, title: string) => void;\n  visible?: boolean;\n  currentPage: number;\n}\n\nconst AnnotationToolbar: React.FC<AnnotationToolbarProps> = ({\n  selection,\n  position,\n  onCreateHighlight,\n  onCreateNote,\n  onCreateBookmark,\n  visible = false,\n  currentPage,\n}) => {\n  const [selectedColor, setSelectedColor] = useState<AnnotationColor>('yellow');\n  const [noteContent, setNoteContent] = useState('');\n  const [bookmarkTitle, setBookmarkTitle] = useState('');\n  const [showNoteInput, setShowNoteInput] = useState(false);\n  const [showBookmarkInput, setShowBookmarkInput] = useState(false);\n\n  if (!visible || !position) {\n    return null;\n  }\n\n  const handleCreateHighlight = () => {\n    if (selection) {\n      onCreateHighlight(selection, selectedColor);\n    }\n  };\n\n  const handleCreateNote = () => {\n    if (selection && noteContent.trim()) {\n      onCreateNote(selection, noteContent.trim());\n      setNoteContent('');\n      setShowNoteInput(false);\n    }\n  };\n\n  const handleCreateBookmark = () => {\n    const title = bookmarkTitle.trim() || `Bookmark - Page ${currentPage}`;\n    onCreateBookmark(currentPage, title);\n    setBookmarkTitle('');\n    setShowBookmarkInput(false);\n  };\n\n  const colorOptions = Object.entries(ANNOTATION_COLORS).map(([key, value]) => ({\n    key,\n    label: (\n      <div className=\"flex items-center space-x-2\">\n        <div \n          className=\"w-4 h-4 rounded border\"\n          style={{ backgroundColor: value }}\n        />\n        <span className=\"capitalize\">{key}</span>\n      </div>\n    ),\n    onClick: () => setSelectedColor(key as AnnotationColor),\n  }));\n\n  const notePopoverContent = (\n    <div className=\"w-64 space-y-3\">\n      <Input.TextArea\n        placeholder=\"Add your note...\"\n        value={noteContent}\n        onChange={(e) => setNoteContent(e.target.value)}\n        rows={3}\n        maxLength={500}\n      />\n      <div className=\"flex justify-end space-x-2\">\n        <Button size=\"small\" onClick={() => setShowNoteInput(false)}>\n          Cancel\n        </Button>\n        <Button \n          type=\"primary\" \n          size=\"small\" \n          onClick={handleCreateNote}\n          disabled={!noteContent.trim()}\n        >\n          Add Note\n        </Button>\n      </div>\n    </div>\n  );\n\n  const bookmarkPopoverContent = (\n    <div className=\"w-64 space-y-3\">\n      <Input\n        placeholder={`Bookmark - Page ${currentPage}`}\n        value={bookmarkTitle}\n        onChange={(e) => setBookmarkTitle(e.target.value)}\n        maxLength={100}\n      />\n      <div className=\"flex justify-end space-x-2\">\n        <Button size=\"small\" onClick={() => setShowBookmarkInput(false)}>\n          Cancel\n        </Button>\n        <Button \n          type=\"primary\" \n          size=\"small\" \n          onClick={handleCreateBookmark}\n        >\n          Add Bookmark\n        </Button>\n      </div>\n    </div>\n  );\n\n  const toolbarStyle: React.CSSProperties = {\n    position: 'fixed',\n    left: position.x - 100, // Center the toolbar\n    top: position.y - 50, // Position above the selection\n    zIndex: 1000,\n    background: 'white',\n    borderRadius: '8px',\n    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n    border: '1px solid #d9d9d9',\n    padding: '8px',\n  };\n\n  return (\n    <div style={toolbarStyle} className=\"annotation-toolbar\">\n      <Space size=\"small\">\n        {/* Highlight Button with Color Picker */}\n        <Dropdown\n          menu={{ items: colorOptions }}\n          trigger={['click']}\n          placement=\"bottomLeft\"\n        >\n          <Tooltip title={`Highlight (${selectedColor})`}>\n            <Button\n              type=\"text\"\n              icon={<HighlightOutlined />}\n              onClick={handleCreateHighlight}\n              style={{ \n                color: ANNOTATION_COLORS[selectedColor],\n                borderColor: ANNOTATION_COLORS[selectedColor] \n              }}\n              className=\"hover:bg-gray-50\"\n            />\n          </Tooltip>\n        </Dropdown>\n\n        {/* Color Picker */}\n        <Dropdown\n          menu={{ items: colorOptions }}\n          trigger={['click']}\n          placement=\"bottomLeft\"\n        >\n          <Tooltip title=\"Choose highlight color\">\n            <Button\n              type=\"text\"\n              icon={<BgColorsOutlined />}\n              size=\"small\"\n              className=\"hover:bg-gray-50\"\n            />\n          </Tooltip>\n        </Dropdown>\n\n        {/* Note Button */}\n        <Popover\n          content={notePopoverContent}\n          title=\"Add Note\"\n          trigger=\"click\"\n          open={showNoteInput}\n          onOpenChange={setShowNoteInput}\n          placement=\"bottom\"\n        >\n          <Tooltip title=\"Add note\">\n            <Button\n              type=\"text\"\n              icon={<MessageOutlined />}\n              className=\"hover:bg-gray-50\"\n            />\n          </Tooltip>\n        </Popover>\n\n        {/* Bookmark Button */}\n        <Popover\n          content={bookmarkPopoverContent}\n          title=\"Add Bookmark\"\n          trigger=\"click\"\n          open={showBookmarkInput}\n          onOpenChange={setShowBookmarkInput}\n          placement=\"bottom\"\n        >\n          <Tooltip title=\"Add bookmark\">\n            <Button\n              type=\"text\"\n              icon={<BookOutlined />}\n              className=\"hover:bg-gray-50\"\n            />\n          </Tooltip>\n        </Popover>\n      </Space>\n    </div>\n  );\n};\n\nexport default AnnotationToolbar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAQA;;;AAZA;;;;;AAwBA,MAAM,oBAAsD;QAAC,EAC3D,SAAS,EACT,QAAQ,EACR,iBAAiB,EACjB,YAAY,EACZ,gBAAgB,EAChB,UAAU,KAAK,EACf,WAAW,EACZ;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,IAAI,CAAC,WAAW,CAAC,UAAU;QACzB,OAAO;IACT;IAEA,MAAM,wBAAwB;QAC5B,IAAI,WAAW;YACb,kBAAkB,WAAW;QAC/B;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa,YAAY,IAAI,IAAI;YACnC,aAAa,WAAW,YAAY,IAAI;YACxC,eAAe;YACf,iBAAiB;QACnB;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,QAAQ,cAAc,IAAI,MAAM,AAAC,mBAA8B,OAAZ;QACzD,iBAAiB,aAAa;QAC9B,iBAAiB;QACjB,qBAAqB;IACvB;IAEA,MAAM,eAAe,OAAO,OAAO,CAAC,oIAAA,CAAA,oBAAiB,EAAE,GAAG,CAAC;YAAC,CAAC,KAAK,MAAM;eAAM;YAC5E;YACA,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAM;;;;;;kCAElC,6LAAC;wBAAK,WAAU;kCAAc;;;;;;;;;;;;YAGlC,SAAS,IAAM,iBAAiB;QAClC;;IAEA,MAAM,mCACJ,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;gBACb,aAAY;gBACZ,OAAO;gBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gBAC9C,MAAM;gBACN,WAAW;;;;;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qMAAA,CAAA,SAAM;wBAAC,MAAK;wBAAQ,SAAS,IAAM,iBAAiB;kCAAQ;;;;;;kCAG7D,6LAAC,qMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,MAAK;wBACL,SAAS;wBACT,UAAU,CAAC,YAAY,IAAI;kCAC5B;;;;;;;;;;;;;;;;;;IAOP,MAAM,uCACJ,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mLAAA,CAAA,QAAK;gBACJ,aAAa,AAAC,mBAA8B,OAAZ;gBAChC,OAAO;gBACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gBAChD,WAAW;;;;;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qMAAA,CAAA,SAAM;wBAAC,MAAK;wBAAQ,SAAS,IAAM,qBAAqB;kCAAQ;;;;;;kCAGjE,6LAAC,qMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,MAAK;wBACL,SAAS;kCACV;;;;;;;;;;;;;;;;;;IAOP,MAAM,eAAoC;QACxC,UAAU;QACV,MAAM,SAAS,CAAC,GAAG;QACnB,KAAK,SAAS,CAAC,GAAG;QAClB,QAAQ;QACR,YAAY;QACZ,cAAc;QACd,WAAW;QACX,QAAQ;QACR,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,OAAO;QAAc,WAAU;kBAClC,cAAA,6LAAC,mMAAA,CAAA,QAAK;YAAC,MAAK;;8BAEV,6LAAC,yLAAA,CAAA,WAAQ;oBACP,MAAM;wBAAE,OAAO;oBAAa;oBAC5B,SAAS;wBAAC;qBAAQ;oBAClB,WAAU;8BAEV,cAAA,6LAAC,uLAAA,CAAA,UAAO;wBAAC,OAAO,AAAC,cAA2B,OAAd,eAAc;kCAC1C,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;4BACxB,SAAS;4BACT,OAAO;gCACL,OAAO,oIAAA,CAAA,oBAAiB,CAAC,cAAc;gCACvC,aAAa,oIAAA,CAAA,oBAAiB,CAAC,cAAc;4BAC/C;4BACA,WAAU;;;;;;;;;;;;;;;;8BAMhB,6LAAC,yLAAA,CAAA,WAAQ;oBACP,MAAM;wBAAE,OAAO;oBAAa;oBAC5B,SAAS;wBAAC;qBAAQ;oBAClB,WAAU;8BAEV,cAAA,6LAAC,uLAAA,CAAA,UAAO;wBAAC,OAAM;kCACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;4BACvB,MAAK;4BACL,WAAU;;;;;;;;;;;;;;;;8BAMhB,6LAAC,uLAAA,CAAA,UAAO;oBACN,SAAS;oBACT,OAAM;oBACN,SAAQ;oBACR,MAAM;oBACN,cAAc;oBACd,WAAU;8BAEV,cAAA,6LAAC,uLAAA,CAAA,UAAO;wBAAC,OAAM;kCACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;4BACtB,WAAU;;;;;;;;;;;;;;;;8BAMhB,6LAAC,uLAAA,CAAA,UAAO;oBACN,SAAS;oBACT,OAAM;oBACN,SAAQ;oBACR,MAAM;oBACN,cAAc;oBACd,WAAU;8BAEV,cAAA,6LAAC,uLAAA,CAAA,UAAO;wBAAC,OAAM;kCACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;GA/LM;KAAA;uCAiMS", "debugId": null}}, {"offset": {"line": 4398, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/annotations/AnnotationDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Card, List, Button, Input, Select, Space, Typography, Tag, Tooltip, Popconfirm, Empty } from 'antd';\nimport { \n  HighlightOutlined, \n  MessageOutlined, \n  BookOutlined, \n  EditOutlined, \n  DeleteOutlined,\n  SearchOutlined,\n  FilterOutlined,\n  ExportOutlined,\n  ImportOutlined,\n  SyncOutlined\n} from '@ant-design/icons';\nimport { Annotation, AnnotationFilter, ANNOTATION_COLORS } from '@/lib/annotation-types';\nimport { formatDistanceToNow } from 'date-fns';\n\nconst { Text, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\ninterface AnnotationDisplayProps {\n  annotations: Annotation[];\n  loading?: boolean;\n  onUpdateAnnotation: (annotationId: string, updates: Partial<Annotation>) => void;\n  onDeleteAnnotation: (annotationId: string) => void;\n  onFilterChange: (filter: AnnotationFilter) => void;\n  onExport: () => void;\n  onImport: (data: string) => void;\n  onSync: () => void;\n  currentPage?: number;\n  onPageJump?: (pageNumber: number) => void;\n}\n\nconst AnnotationDisplay: React.FC<AnnotationDisplayProps> = ({\n  annotations,\n  loading = false,\n  onUpdateAnnotation,\n  onDeleteAnnotation,\n  onFilterChange,\n  onExport,\n  onImport,\n  onSync,\n  currentPage,\n  onPageJump,\n}) => {\n  const [filter, setFilter] = useState<AnnotationFilter>({});\n  const [editingId, setEditingId] = useState<string | null>(null);\n  const [editContent, setEditContent] = useState('');\n  const [searchText, setSearchText] = useState('');\n\n  const handleFilterChange = (newFilter: Partial<AnnotationFilter>) => {\n    const updatedFilter = { ...filter, ...newFilter };\n    setFilter(updatedFilter);\n    onFilterChange(updatedFilter);\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchText(value);\n    handleFilterChange({ searchText: value || undefined });\n  };\n\n  const handleEdit = (annotation: Annotation) => {\n    setEditingId(annotation.id);\n    setEditContent(annotation.content);\n  };\n\n  const handleSaveEdit = (annotationId: string) => {\n    onUpdateAnnotation(annotationId, { content: editContent });\n    setEditingId(null);\n    setEditContent('');\n  };\n\n  const handleCancelEdit = () => {\n    setEditingId(null);\n    setEditContent('');\n  };\n\n  const getAnnotationIcon = (type: Annotation['type']) => {\n    switch (type) {\n      case 'highlight':\n        return <HighlightOutlined className=\"text-yellow-500\" />;\n      case 'note':\n        return <MessageOutlined className=\"text-blue-500\" />;\n      case 'bookmark':\n        return <BookOutlined className=\"text-red-500\" />;\n      default:\n        return <EditOutlined />;\n    }\n  };\n\n  const getAnnotationColor = (annotation: Annotation) => {\n    if (annotation.type === 'highlight' && annotation.color) {\n      return ANNOTATION_COLORS[annotation.color];\n    }\n    return undefined;\n  };\n\n  const filteredAnnotations = annotations.filter(annotation => {\n    if (filter.type && annotation.type !== filter.type) return false;\n    if (filter.pageNumber && annotation.pageNumber !== filter.pageNumber) return false;\n    if (searchText) {\n      const searchLower = searchText.toLowerCase();\n      const contentMatch = annotation.content.toLowerCase().includes(searchLower);\n      const selectionMatch = annotation.selection?.selectedText.toLowerCase().includes(searchLower);\n      if (!contentMatch && !selectionMatch) return false;\n    }\n    return true;\n  });\n\n  const groupedAnnotations = filteredAnnotations.reduce((groups, annotation) => {\n    const page = annotation.pageNumber;\n    if (!groups[page]) {\n      groups[page] = [];\n    }\n    groups[page].push(annotation);\n    return groups;\n  }, {} as Record<number, Annotation[]>);\n\n  return (\n    <div className=\"annotation-display h-full flex flex-col\">\n      {/* Header with controls */}\n      <div className=\"p-4 border-b bg-gray-50\">\n        <div className=\"flex justify-between items-center mb-3\">\n          <Text strong className=\"text-lg\">Annotations ({annotations.length})</Text>\n          <Space>\n            <Tooltip title=\"Export annotations\">\n              <Button icon={<ExportOutlined />} size=\"small\" onClick={onExport} />\n            </Tooltip>\n            <Tooltip title=\"Sync annotations\">\n              <Button icon={<SyncOutlined />} size=\"small\" onClick={onSync} />\n            </Tooltip>\n          </Space>\n        </div>\n\n        {/* Search and filters */}\n        <Space direction=\"vertical\" className=\"w-full\">\n          <Input\n            placeholder=\"Search annotations...\"\n            prefix={<SearchOutlined />}\n            value={searchText}\n            onChange={(e) => handleSearch(e.target.value)}\n            allowClear\n          />\n          \n          <Space wrap>\n            <Select\n              placeholder=\"Filter by type\"\n              style={{ width: 120 }}\n              allowClear\n              onChange={(value) => handleFilterChange({ type: value })}\n            >\n              <Option value=\"highlight\">Highlights</Option>\n              <Option value=\"note\">Notes</Option>\n              <Option value=\"bookmark\">Bookmarks</Option>\n            </Select>\n            \n            <Select\n              placeholder=\"Filter by page\"\n              style={{ width: 120 }}\n              allowClear\n              onChange={(value) => handleFilterChange({ pageNumber: value })}\n            >\n              {Array.from(new Set(annotations.map(a => a.pageNumber)))\n                .sort((a, b) => a - b)\n                .map(page => (\n                  <Option key={page} value={page}>Page {page}</Option>\n                ))}\n            </Select>\n          </Space>\n        </Space>\n      </div>\n\n      {/* Annotations list */}\n      <div className=\"flex-1 overflow-auto\">\n        {filteredAnnotations.length === 0 ? (\n          <Empty\n            description=\"No annotations found\"\n            className=\"mt-8\"\n          />\n        ) : (\n          Object.entries(groupedAnnotations)\n            .sort(([a], [b]) => Number(a) - Number(b))\n            .map(([pageNumber, pageAnnotations]) => (\n              <div key={pageNumber} className=\"mb-4\">\n                <div className=\"px-4 py-2 bg-gray-100 border-b\">\n                  <Button\n                    type=\"link\"\n                    className=\"p-0 h-auto font-semibold\"\n                    onClick={() => onPageJump?.(Number(pageNumber))}\n                  >\n                    Page {pageNumber} ({pageAnnotations.length})\n                  </Button>\n                </div>\n                \n                <List\n                  dataSource={pageAnnotations}\n                  renderItem={(annotation) => (\n                    <List.Item className=\"px-4 py-3 hover:bg-gray-50\">\n                      <div className=\"w-full\">\n                        <div className=\"flex items-start justify-between mb-2\">\n                          <div className=\"flex items-center space-x-2\">\n                            {getAnnotationIcon(annotation.type)}\n                            <Tag \n                              color={annotation.type === 'highlight' ? 'gold' : \n                                     annotation.type === 'note' ? 'blue' : 'red'}\n                            >\n                              {annotation.type}\n                            </Tag>\n                            {annotation.type === 'highlight' && annotation.color && (\n                              <div\n                                className=\"w-3 h-3 rounded border\"\n                                style={{ backgroundColor: getAnnotationColor(annotation) }}\n                              />\n                            )}\n                          </div>\n                          \n                          <Space size=\"small\">\n                            <Tooltip title=\"Edit\">\n                              <Button\n                                type=\"text\"\n                                size=\"small\"\n                                icon={<EditOutlined />}\n                                onClick={() => handleEdit(annotation)}\n                              />\n                            </Tooltip>\n                            <Popconfirm\n                              title=\"Delete annotation?\"\n                              description=\"This action cannot be undone.\"\n                              onConfirm={() => onDeleteAnnotation(annotation.id)}\n                              okText=\"Delete\"\n                              cancelText=\"Cancel\"\n                            >\n                              <Tooltip title=\"Delete\">\n                                <Button\n                                  type=\"text\"\n                                  size=\"small\"\n                                  icon={<DeleteOutlined />}\n                                  danger\n                                />\n                              </Tooltip>\n                            </Popconfirm>\n                          </Space>\n                        </div>\n\n                        {/* Selected text (for highlights and notes) */}\n                        {annotation.selection?.selectedText && (\n                          <div className=\"mb-2 p-2 bg-gray-100 rounded text-sm\">\n                            <Text italic>\"{annotation.selection.selectedText}\"</Text>\n                          </div>\n                        )}\n\n                        {/* Annotation content */}\n                        {editingId === annotation.id ? (\n                          <div className=\"space-y-2\">\n                            <TextArea\n                              value={editContent}\n                              onChange={(e) => setEditContent(e.target.value)}\n                              rows={3}\n                              maxLength={500}\n                            />\n                            <Space>\n                              <Button\n                                type=\"primary\"\n                                size=\"small\"\n                                onClick={() => handleSaveEdit(annotation.id)}\n                              >\n                                Save\n                              </Button>\n                              <Button size=\"small\" onClick={handleCancelEdit}>\n                                Cancel\n                              </Button>\n                            </Space>\n                          </div>\n                        ) : (\n                          <Paragraph className=\"mb-2\">\n                            {annotation.content}\n                          </Paragraph>\n                        )}\n\n                        {/* Timestamp */}\n                        <Text type=\"secondary\" className=\"text-xs\">\n                          {formatDistanceToNow(new Date(annotation.createdAt), { addSuffix: true })}\n                          {annotation.updatedAt !== annotation.createdAt && (\n                            <span> (edited {formatDistanceToNow(new Date(annotation.updatedAt), { addSuffix: true })})</span>\n                          )}\n                        </Text>\n                      </div>\n                    </List.Item>\n                  )}\n                />\n              </div>\n            ))\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AnnotationDisplay;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;;;;;;AAhBA;;;;;;AAmBA,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AACtC,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAezB,MAAM,oBAAsD;QAAC,EAC3D,WAAW,EACX,UAAU,KAAK,EACf,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,WAAW,EACX,UAAU,EACX;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,CAAC;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,gBAAgB;YAAE,GAAG,MAAM;YAAE,GAAG,SAAS;QAAC;QAChD,UAAU;QACV,eAAe;IACjB;IAEA,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,mBAAmB;YAAE,YAAY,SAAS;QAAU;IACtD;IAEA,MAAM,aAAa,CAAC;QAClB,aAAa,WAAW,EAAE;QAC1B,eAAe,WAAW,OAAO;IACnC;IAEA,MAAM,iBAAiB,CAAC;QACtB,mBAAmB,cAAc;YAAE,SAAS;QAAY;QACxD,aAAa;QACb,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,+NAAA,CAAA,oBAAiB;oBAAC,WAAU;;;;;;YACtC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,6LAAC,qNAAA,CAAA,eAAY;;;;;QACxB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,WAAW,IAAI,KAAK,eAAe,WAAW,KAAK,EAAE;YACvD,OAAO,oIAAA,CAAA,oBAAiB,CAAC,WAAW,KAAK,CAAC;QAC5C;QACA,OAAO;IACT;IAEA,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA;QAC7C,IAAI,OAAO,IAAI,IAAI,WAAW,IAAI,KAAK,OAAO,IAAI,EAAE,OAAO;QAC3D,IAAI,OAAO,UAAU,IAAI,WAAW,UAAU,KAAK,OAAO,UAAU,EAAE,OAAO;QAC7E,IAAI,YAAY;gBAGS;YAFvB,MAAM,cAAc,WAAW,WAAW;YAC1C,MAAM,eAAe,WAAW,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;YAC/D,MAAM,kBAAiB,wBAAA,WAAW,SAAS,cAApB,4CAAA,sBAAsB,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC;YACjF,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,OAAO;QAC/C;QACA,OAAO;IACT;IAEA,MAAM,qBAAqB,oBAAoB,MAAM,CAAC,CAAC,QAAQ;QAC7D,MAAM,OAAO,WAAW,UAAU;QAClC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACjB,MAAM,CAAC,KAAK,GAAG,EAAE;QACnB;QACA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB,OAAO;IACT,GAAG,CAAC;IAEJ,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,MAAM;gCAAC,WAAU;;oCAAU;oCAAc,YAAY,MAAM;oCAAC;;;;;;;0CAClE,6LAAC,mMAAA,CAAA,QAAK;;kDACJ,6LAAC,uLAAA,CAAA,UAAO;wCAAC,OAAM;kDACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4CAAC,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4CAAK,MAAK;4CAAQ,SAAS;;;;;;;;;;;kDAE1D,6LAAC,uLAAA,CAAA,UAAO;wCAAC,OAAM;kDACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4CAAC,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4CAAK,MAAK;4CAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;kCAM5D,6LAAC,mMAAA,CAAA,QAAK;wBAAC,WAAU;wBAAW,WAAU;;0CACpC,6LAAC,mLAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;gCACvB,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,UAAU;;;;;;0CAGZ,6LAAC,mMAAA,CAAA,QAAK;gCAAC,IAAI;;kDACT,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAI;wCACpB,UAAU;wCACV,UAAU,CAAC,QAAU,mBAAmB;gDAAE,MAAM;4CAAM;;0DAEtD,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;kDAG3B,6LAAC,qLAAA,CAAA,SAAM;wCACL,aAAY;wCACZ,OAAO;4CAAE,OAAO;wCAAI;wCACpB,UAAU;wCACV,UAAU,CAAC,QAAU,mBAAmB;gDAAE,YAAY;4CAAM;kDAE3D,MAAM,IAAI,CAAC,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,IAClD,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,GACnB,GAAG,CAAC,CAAA,qBACH,6LAAC;gDAAkB,OAAO;;oDAAM;oDAAM;;+CAAzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,6LAAC;gBAAI,WAAU;0BACZ,oBAAoB,MAAM,KAAK,kBAC9B,6LAAC,mLAAA,CAAA,QAAK;oBACJ,aAAY;oBACZ,WAAU;;;;;+DAGZ,OAAO,OAAO,CAAC,oBACZ,IAAI,CAAC;wBAAC,CAAC,EAAE,UAAE,CAAC,EAAE;2BAAK,OAAO,KAAK,OAAO;mBACtC,GAAG,CAAC;wBAAC,CAAC,YAAY,gBAAgB;yCACjC,6LAAC;wBAAqB,WAAU;;0CAC9B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,uBAAA,iCAAA,WAAa,OAAO;;wCACpC;wCACO;wCAAW;wCAAG,gBAAgB,MAAM;wCAAC;;;;;;;;;;;;0CAI/C,6LAAC,iLAAA,CAAA,OAAI;gCACH,YAAY;gCACZ,YAAY,CAAC;wCAiDN;yDAhDL,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCAAC,WAAU;kDACnB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,kBAAkB,WAAW,IAAI;8EAClC,6LAAC,+KAAA,CAAA,MAAG;oEACF,OAAO,WAAW,IAAI,KAAK,cAAc,SAClC,WAAW,IAAI,KAAK,SAAS,SAAS;8EAE5C,WAAW,IAAI;;;;;;gEAEjB,WAAW,IAAI,KAAK,eAAe,WAAW,KAAK,kBAClD,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,mBAAmB;oEAAY;;;;;;;;;;;;sEAK/D,6LAAC,mMAAA,CAAA,QAAK;4DAAC,MAAK;;8EACV,6LAAC,uLAAA,CAAA,UAAO;oEAAC,OAAM;8EACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;wEACL,MAAK;wEACL,MAAK;wEACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wEACnB,SAAS,IAAM,WAAW;;;;;;;;;;;8EAG9B,6LAAC,6LAAA,CAAA,aAAU;oEACT,OAAM;oEACN,aAAY;oEACZ,WAAW,IAAM,mBAAmB,WAAW,EAAE;oEACjD,QAAO;oEACP,YAAW;8EAEX,cAAA,6LAAC,uLAAA,CAAA,UAAO;wEAAC,OAAM;kFACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4EACL,MAAK;4EACL,MAAK;4EACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4EACrB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;gDAQf,EAAA,wBAAA,WAAW,SAAS,cAApB,4CAAA,sBAAsB,YAAY,mBACjC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,MAAM;;4DAAC;4DAAE,WAAW,SAAS,CAAC,YAAY;4DAAC;;;;;;;;;;;;gDAKpD,cAAc,WAAW,EAAE,iBAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4DAC9C,MAAM;4DACN,WAAW;;;;;;sEAEb,6LAAC,mMAAA,CAAA,QAAK;;8EACJ,6LAAC,qMAAA,CAAA,SAAM;oEACL,MAAK;oEACL,MAAK;oEACL,SAAS,IAAM,eAAe,WAAW,EAAE;8EAC5C;;;;;;8EAGD,6LAAC,qMAAA,CAAA,SAAM;oEAAC,MAAK;oEAAQ,SAAS;8EAAkB;;;;;;;;;;;;;;;;;2EAMpD,6LAAC;oDAAU,WAAU;8DAClB,WAAW,OAAO;;;;;;8DAKvB,6LAAC;oDAAK,MAAK;oDAAY,WAAU;;wDAC9B,oBAAoB,IAAI,KAAK,WAAW,SAAS,GAAG;4DAAE,WAAW;wDAAK;wDACtE,WAAW,SAAS,KAAK,WAAW,SAAS,kBAC5C,6LAAC;;gEAAK;gEAAU,oBAAoB,IAAI,KAAK,WAAW,SAAS,GAAG;oEAAE,WAAW;gEAAK;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBApG7F;;;;;;;;;;;;;;;;;AAiHxB;GAvQM;KAAA;uCAyQS", "debugId": null}}, {"offset": {"line": 5011, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/pdf/PDFViewer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef } from 'react';\nimport dynamic from 'next/dynamic';\nimport { Card, Button, Input, Slider, Space, Tooltip, Typography, Spin, message } from 'antd';\nimport { useAnnotations } from '@/hooks/useAnnotations';\nimport { useTextSelection } from '@/hooks/useTextSelection';\nimport AnnotationToolbar from '@/components/annotations/AnnotationToolbar';\nimport AnnotationDisplay from '@/components/annotations/AnnotationDisplay';\nimport {\n  ZoomInOutlined,\n  ZoomOutOutlined,\n  LeftOutlined,\n  RightOutlined,\n  FullscreenOutlined,\n  DownloadOutlined,\n  SearchOutlined,\n  HighlightOutlined,\n  EditOutlined,\n  BookOutlined,\n} from '@ant-design/icons';\nimport { Paper } from '@/lib/types';\nimport { Annotation } from '@/lib/annotation-types';\n\n// Dynamic import for PDF component to avoid SSR issues\nconst PDFDocument = dynamic(() => import('./PDFDocument'), {\n  ssr: false,\n  loading: () => <Spin size=\"large\" />,\n});\n\nconst { Text } = Typography;\n\ninterface PDFViewerProps {\n  paper: Paper;\n  className?: string;\n  onAnnotationCreate?: (annotation: Annotation) => void;\n}\n\nconst PDFViewer: React.FC<PDFViewerProps> = ({\n  paper,\n  className = '',\n  onAnnotationCreate,\n}) => {\n  const [numPages, setNumPages] = useState<number>(0);\n  const [currentPage, setCurrentPage] = useState<number>(1);\n  const [scale, setScale] = useState<number>(1.0);\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [searchText, setSearchText] = useState<string>('');\n  const [showAnnotations, setShowAnnotations] = useState<boolean>(false);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // Annotation hooks\n  const {\n    annotations,\n    loading: annotationsLoading,\n    createHighlight,\n    createNote,\n    createBookmark,\n    updateAnnotation,\n    deleteAnnotation,\n    exportAnnotations,\n    importAnnotations,\n    syncAnnotations,\n  } = useAnnotations(paper.id);\n\n  const {\n    selection,\n    hasSelection,\n    getSelectionPosition,\n    clearSelection,\n    handleTextSelection,\n  } = useTextSelection(currentPage);\n\n  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {\n    setNumPages(numPages);\n    setIsLoading(false);\n    message.success('PDF loaded successfully');\n  };\n\n  const onDocumentLoadError = (error: Error) => {\n    console.error('Error loading PDF:', error);\n    setIsLoading(false);\n    message.error('Failed to load PDF: ' + error.message);\n  };\n\n  const onPageLoadSuccess = () => {\n    // Page loaded successfully\n  };\n\n  const onPageLoadError = (error: Error) => {\n    console.error('Error loading page:', error);\n    message.warning('Failed to load page: ' + error.message);\n  };\n\n  const handlePageChange = (page: number) => {\n    if (page >= 1 && page <= numPages) {\n      setCurrentPage(page);\n    }\n  };\n\n  const handleZoomIn = () => {\n    setScale(prev => Math.min(prev + 0.2, 3.0));\n  };\n\n  const handleZoomOut = () => {\n    setScale(prev => Math.max(prev - 0.2, 0.5));\n  };\n\n  const handleScaleChange = (value: number) => {\n    setScale(value);\n  };\n\n  const handleFullscreen = () => {\n    if (containerRef.current) {\n      if (document.fullscreenElement) {\n        document.exitFullscreen();\n      } else {\n        containerRef.current.requestFullscreen();\n      }\n    }\n  };\n\n  const handleDownload = () => {\n    // In a real implementation, you would download the PDF file\n    const link = document.createElement('a');\n    link.href = paper.filePath;\n    link.download = `${paper.title}.pdf`;\n    link.click();\n  };\n\n  // Annotation handlers\n  const handleCreateHighlight = async (selection: any, color: any) => {\n    const annotation = await createHighlight(selection, color);\n    if (annotation) {\n      onAnnotationCreate?.(annotation);\n      clearSelection();\n    }\n  };\n\n  const handleCreateNote = async (selection: any, content: string) => {\n    const annotation = await createNote(content, selection, currentPage);\n    if (annotation) {\n      onAnnotationCreate?.(annotation);\n      clearSelection();\n    }\n  };\n\n  const handleCreateBookmark = async (pageNumber: number, title: string) => {\n    const annotation = await createBookmark(pageNumber, title);\n    if (annotation) {\n      onAnnotationCreate?.(annotation);\n    }\n  };\n\n  const handleExportAnnotations = async () => {\n    const exported = await exportAnnotations();\n    if (exported) {\n      const blob = new Blob([exported], { type: 'application/json' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${paper.title}_annotations.json`;\n      link.click();\n      URL.revokeObjectURL(url);\n    }\n  };\n\n  const currentPageAnnotations = annotations.filter(a => a.pageNumber === currentPage);\n\n  return (\n    <div className={`pdf-viewer ${className}`} ref={containerRef}>\n      {/* Toolbar */}\n      <Card className=\"mb-4\" styles={{ body: { padding: '12px 16px' } }}>\n        <div className=\"flex items-center justify-between\">\n          <Space>\n            {/* Navigation */}\n            <Button\n              icon={<LeftOutlined />}\n              onClick={() => handlePageChange(currentPage - 1)}\n              disabled={currentPage <= 1}\n            />\n            <Input\n              value={currentPage}\n              onChange={(e) => handlePageChange(parseInt(e.target.value) || 1)}\n              className=\"w-16 text-center\"\n              size=\"small\"\n            />\n            <Text>of {numPages}</Text>\n            <Button\n              icon={<RightOutlined />}\n              onClick={() => handlePageChange(currentPage + 1)}\n              disabled={currentPage >= numPages}\n            />\n          </Space>\n\n          <Space>\n            {/* Zoom Controls */}\n            <Button icon={<ZoomOutOutlined />} onClick={handleZoomOut} />\n            <Slider\n              min={0.5}\n              max={3.0}\n              step={0.1}\n              value={scale}\n              onChange={handleScaleChange}\n              className=\"w-24\"\n            />\n            <Button icon={<ZoomInOutlined />} onClick={handleZoomIn} />\n            <Text className=\"w-12 text-center\">{Math.round(scale * 100)}%</Text>\n          </Space>\n\n          <Space>\n            {/* Annotation Tools */}\n            <Tooltip title=\"Toggle Annotations Panel\">\n              <Button\n                icon={<EditOutlined />}\n                type={showAnnotations ? 'primary' : 'default'}\n                onClick={() => setShowAnnotations(!showAnnotations)}\n              />\n            </Tooltip>\n            <Tooltip title=\"Export Annotations\">\n              <Button\n                icon={<DownloadOutlined />}\n                onClick={handleExportAnnotations}\n              />\n            </Tooltip>\n            <Tooltip title=\"Sync Annotations\">\n              <Button\n                icon={<BookOutlined />}\n                onClick={syncAnnotations}\n                loading={annotationsLoading}\n              />\n            </Tooltip>\n          </Space>\n\n          <Space>\n            {/* Search */}\n            <Input\n              placeholder=\"Search in document...\"\n              prefix={<SearchOutlined />}\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              className=\"w-48\"\n              size=\"small\"\n            />\n            \n            {/* Actions */}\n            <Button icon={<FullscreenOutlined />} onClick={handleFullscreen} />\n            <Button icon={<DownloadOutlined />} onClick={handleDownload} />\n          </Space>\n        </div>\n      </Card>\n\n      {/* Main Content Area */}\n      <div className=\"flex flex-1 gap-4\">\n        {/* PDF Content */}\n        <Card className=\"flex-1 overflow-auto\" styles={{ body: { padding: 0 } }}>\n          {isLoading && (\n            <div className=\"flex items-center justify-center h-64\">\n              <Spin size=\"large\" />\n            </div>\n          )}\n\n          <div className=\"relative\">\n            <PDFDocument\n              file={paper.filePath}\n              pageNumber={currentPage}\n              scale={scale}\n              onLoadSuccess={onDocumentLoadSuccess}\n              onLoadError={onDocumentLoadError}\n              onPageLoadSuccess={onPageLoadSuccess}\n              onPageLoadError={onPageLoadError}\n            />\n\n            {/* Annotation Toolbar */}\n            <AnnotationToolbar\n              selection={selection}\n              position={getSelectionPosition()}\n              onCreateHighlight={handleCreateHighlight}\n              onCreateNote={handleCreateNote}\n              onCreateBookmark={handleCreateBookmark}\n              visible={hasSelection}\n              currentPage={currentPage}\n            />\n          </div>\n        </Card>\n\n        {/* Annotations Panel */}\n        {showAnnotations && (\n          <Card className=\"w-80 h-full\" styles={{ body: { padding: 0 } }}>\n            <AnnotationDisplay\n              annotations={annotations}\n              loading={annotationsLoading}\n              onUpdateAnnotation={updateAnnotation}\n              onDeleteAnnotation={deleteAnnotation}\n              onFilterChange={() => {}}\n              onExport={handleExportAnnotations}\n              onImport={importAnnotations}\n              onSync={syncAnnotations}\n              currentPage={currentPage}\n              onPageJump={setCurrentPage}\n            />\n          </Card>\n        )}\n      </div>\n\n      {/* Status Bar */}\n      <div className=\"flex items-center justify-between mt-2 text-sm text-gray-600\">\n        <div>\n          {annotations.length} annotations\n        </div>\n        <div>\n          Page {currentPage} of {numPages}\n        </div>\n        <div>\n          {selection?.selectedText && `Selected: \"${selection.selectedText.substring(0, 50)}${selection.selectedText.length > 50 ? '...' : ''}\"`}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PDFViewer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AATA;;;;;;;;;AAwBA,uDAAuD;AACvD,MAAM,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAC1B,KAAK;IACL,SAAS,kBAAM,6LAAC,iLAAA,CAAA,OAAI;YAAC,MAAK;;;;;;;KAFtB;AAKN,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAQ3B,MAAM,YAAsC;QAAC,EAC3C,KAAK,EACL,YAAY,EAAE,EACd,kBAAkB,EACnB;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,mBAAmB;IACnB,MAAM,EACJ,WAAW,EACX,SAAS,kBAAkB,EAC3B,eAAe,EACf,UAAU,EACV,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,EAChB,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,EAAE;IAE3B,MAAM,EACJ,SAAS,EACT,YAAY,EACZ,oBAAoB,EACpB,cAAc,EACd,mBAAmB,EACpB,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;IAErB,MAAM,wBAAwB;YAAC,EAAE,QAAQ,EAAwB;QAC/D,YAAY;QACZ,aAAa;QACb,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,KAAK,CAAC,sBAAsB;QACpC,aAAa;QACb,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,yBAAyB,MAAM,OAAO;IACtD;IAEA,MAAM,oBAAoB;IACxB,2BAA2B;IAC7B;IAEA,MAAM,kBAAkB,CAAC;QACvB,QAAQ,KAAK,CAAC,uBAAuB;QACrC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,0BAA0B,MAAM,OAAO;IACzD;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,KAAK,QAAQ,UAAU;YACjC,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,KAAK;IACxC;IAEA,MAAM,gBAAgB;QACpB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,KAAK;IACxC;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS;IACX;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa,OAAO,EAAE;YACxB,IAAI,SAAS,iBAAiB,EAAE;gBAC9B,SAAS,cAAc;YACzB,OAAO;gBACL,aAAa,OAAO,CAAC,iBAAiB;YACxC;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,4DAA4D;QAC5D,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,MAAM,QAAQ;QAC1B,KAAK,QAAQ,GAAG,AAAC,GAAc,OAAZ,MAAM,KAAK,EAAC;QAC/B,KAAK,KAAK;IACZ;IAEA,sBAAsB;IACtB,MAAM,wBAAwB,OAAO,WAAgB;QACnD,MAAM,aAAa,MAAM,gBAAgB,WAAW;QACpD,IAAI,YAAY;YACd,+BAAA,yCAAA,mBAAqB;YACrB;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO,WAAgB;QAC9C,MAAM,aAAa,MAAM,WAAW,SAAS,WAAW;QACxD,IAAI,YAAY;YACd,+BAAA,yCAAA,mBAAqB;YACrB;QACF;IACF;IAEA,MAAM,uBAAuB,OAAO,YAAoB;QACtD,MAAM,aAAa,MAAM,eAAe,YAAY;QACpD,IAAI,YAAY;YACd,+BAAA,yCAAA,mBAAqB;QACvB;IACF;IAEA,MAAM,0BAA0B;QAC9B,MAAM,WAAW,MAAM;QACvB,IAAI,UAAU;YACZ,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAS,EAAE;gBAAE,MAAM;YAAmB;YAC7D,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,AAAC,GAAc,OAAZ,MAAM,KAAK,EAAC;YAC/B,KAAK,KAAK;YACV,IAAI,eAAe,CAAC;QACtB;IACF;IAEA,MAAM,yBAAyB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;IAExE,qBACE,6LAAC;QAAI,WAAW,AAAC,cAAuB,OAAV;QAAa,KAAK;;0BAE9C,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAU;gBAAO,QAAQ;oBAAE,MAAM;wBAAE,SAAS;oBAAY;gBAAE;0BAC9D,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mMAAA,CAAA,QAAK;;8CAEJ,6LAAC,qMAAA,CAAA,SAAM;oCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,SAAS,IAAM,iBAAiB,cAAc;oCAC9C,UAAU,eAAe;;;;;;8CAE3B,6LAAC,mLAAA,CAAA,QAAK;oCACJ,OAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oCAC9D,WAAU;oCACV,MAAK;;;;;;8CAEP,6LAAC;;wCAAK;wCAAI;;;;;;;8CACV,6LAAC,qMAAA,CAAA,SAAM;oCACL,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oCACpB,SAAS,IAAM,iBAAiB,cAAc;oCAC9C,UAAU,eAAe;;;;;;;;;;;;sCAI7B,6LAAC,mMAAA,CAAA,QAAK;;8CAEJ,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;oCAAK,SAAS;;;;;;8CAC5C,6LAAC,qLAAA,CAAA,SAAM;oCACL,KAAK;oCACL,KAAK;oCACL,MAAM;oCACN,OAAO;oCACP,UAAU;oCACV,WAAU;;;;;;8CAEZ,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oCAAK,SAAS;;;;;;8CAC3C,6LAAC;oCAAK,WAAU;;wCAAoB,KAAK,KAAK,CAAC,QAAQ;wCAAK;;;;;;;;;;;;;sCAG9D,6LAAC,mMAAA,CAAA,QAAK;;8CAEJ,6LAAC,uLAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,MAAM,kBAAkB,YAAY;wCACpC,SAAS,IAAM,mBAAmB,CAAC;;;;;;;;;;;8CAGvC,6LAAC,uLAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wCACvB,SAAS;;;;;;;;;;;8CAGb,6LAAC,uLAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,SAAS;wCACT,SAAS;;;;;;;;;;;;;;;;;sCAKf,6LAAC,mMAAA,CAAA,QAAK;;8CAEJ,6LAAC,mLAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,sBAAQ,6LAAC,yNAAA,CAAA,iBAAc;;;;;oCACvB,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;oCACV,MAAK;;;;;;8CAIP,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;oCAAK,SAAS;;;;;;8CAC/C,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oCAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,iLAAA,CAAA,OAAI;wBAAC,WAAU;wBAAuB,QAAQ;4BAAE,MAAM;gCAAE,SAAS;4BAAE;wBAAE;;4BACnE,2BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;oCAAC,MAAK;;;;;;;;;;;0CAIf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAM,MAAM,QAAQ;wCACpB,YAAY;wCACZ,OAAO;wCACP,eAAe;wCACf,aAAa;wCACb,mBAAmB;wCACnB,iBAAiB;;;;;;kDAInB,6LAAC,yJAAA,CAAA,UAAiB;wCAChB,WAAW;wCACX,UAAU;wCACV,mBAAmB;wCACnB,cAAc;wCACd,kBAAkB;wCAClB,SAAS;wCACT,aAAa;;;;;;;;;;;;;;;;;;oBAMlB,iCACC,6LAAC,iLAAA,CAAA,OAAI;wBAAC,WAAU;wBAAc,QAAQ;4BAAE,MAAM;gCAAE,SAAS;4BAAE;wBAAE;kCAC3D,cAAA,6LAAC,yJAAA,CAAA,UAAiB;4BAChB,aAAa;4BACb,SAAS;4BACT,oBAAoB;4BACpB,oBAAoB;4BACpB,gBAAgB,KAAO;4BACvB,UAAU;4BACV,UAAU;4BACV,QAAQ;4BACR,aAAa;4BACb,YAAY;;;;;;;;;;;;;;;;;0BAOpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BACE,YAAY,MAAM;4BAAC;;;;;;;kCAEtB,6LAAC;;4BAAI;4BACG;4BAAY;4BAAK;;;;;;;kCAEzB,6LAAC;kCACE,CAAA,sBAAA,gCAAA,UAAW,YAAY,KAAI,AAAC,cAAuD,OAA1C,UAAU,YAAY,CAAC,SAAS,CAAC,GAAG,KAAsD,OAAhD,UAAU,YAAY,CAAC,MAAM,GAAG,KAAK,QAAQ,IAAG;;;;;;;;;;;;;;;;;;AAK9I;GAzRM;;QAyBA,iIAAA,CAAA,iBAAc;QAQd,mIAAA,CAAA,mBAAgB;;;MAjChB;uCA2RS", "debugId": null}}, {"offset": {"line": 5582, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/hooks/useAIAnalysis.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { Paper, PaperAnalysis, ChatMessage } from '@/lib/types';\nimport { API_ENDPOINTS, ANALYSIS_TYPES, ERROR_MESSAGES } from '@/lib/constants';\nimport { useAppStore } from '@/store/useAppStore';\n\ninterface UseAIAnalysisReturn {\n  analyzeDocument: (paper: Paper, analysisType: string) => Promise<string | null>;\n  askQuestion: (paper: Paper, question: string) => Promise<ChatMessage | null>;\n  comparePapers: (papers: Paper[]) => Promise<string | null>;\n  isAnalyzing: boolean;\n  error: string | null;\n  clearError: () => void;\n}\n\nexport const useAIAnalysis = (): UseAIAnalysisReturn => {\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  \n  const { addChatMessage, setError: setGlobalError } = useAppStore();\n\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  const analyzeDocument = useCallback(async (\n    paper: Paper, \n    analysisType: string\n  ): Promise<string | null> => {\n    try {\n      setIsAnalyzing(true);\n      setError(null);\n\n      const response = await fetch(API_ENDPOINTS.analysis, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          paperId: paper.id,\n          content: paper.content,\n          analysisType,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      const result = await response.json();\n      \n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      return result.data.result;\n\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      return null;\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [setGlobalError]);\n\n  const askQuestion = useCallback(async (\n    paper: Paper, \n    question: string\n  ): Promise<ChatMessage | null> => {\n    try {\n      setIsAnalyzing(true);\n      setError(null);\n\n      // Create user message\n      const userMessage: ChatMessage = {\n        id: `msg_${Date.now()}_user`,\n        role: 'user',\n        content: question,\n        timestamp: new Date(),\n        paperId: paper.id,\n      };\n\n      addChatMessage(userMessage);\n\n      const response = await fetch(API_ENDPOINTS.chat, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: question,\n          paperId: paper.id,\n          paperContent: paper.content,\n          context: `Paper: ${paper.title}`,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      const result = await response.json();\n      \n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      const assistantMessage: ChatMessage = result.data.message;\n      addChatMessage(assistantMessage);\n\n      return assistantMessage;\n\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      \n      // Add error message to chat\n      const errorChatMessage: ChatMessage = {\n        id: `msg_${Date.now()}_error`,\n        role: 'assistant',\n        content: `I apologize, but I encountered an error: ${errorMessage}`,\n        timestamp: new Date(),\n        paperId: paper.id,\n      };\n      \n      addChatMessage(errorChatMessage);\n      return null;\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [addChatMessage, setGlobalError]);\n\n  const comparePapers = useCallback(async (papers: Paper[]): Promise<string | null> => {\n    try {\n      setIsAnalyzing(true);\n      setError(null);\n\n      if (papers.length < 2) {\n        throw new Error('At least 2 papers are required for comparison');\n      }\n\n      // For now, we'll compare the first two papers\n      // In a full implementation, you might want to handle multiple papers differently\n      const [paper1, paper2] = papers;\n\n      const response = await fetch('/api/comparison', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          paper1: {\n            id: paper1.id,\n            title: paper1.title,\n            content: paper1.content,\n          },\n          paper2: {\n            id: paper2.id,\n            title: paper2.title,\n            content: paper2.content,\n          },\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      const result = await response.json();\n      \n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      return result.data.comparison;\n\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      return null;\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [setGlobalError]);\n\n  return {\n    analyzeDocument,\n    askQuestion,\n    comparePapers,\n    isAnalyzing,\n    error,\n    clearError,\n  };\n};\n\n// Hook for streaming chat responses\nexport const useStreamingChat = () => {\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [streamingMessage, setStreamingMessage] = useState('');\n  \n  const { addChatMessage } = useAppStore();\n\n  const streamQuestion = useCallback(async (\n    paper: Paper,\n    question: string,\n    onChunk?: (chunk: string) => void\n  ) => {\n    try {\n      setIsStreaming(true);\n      setStreamingMessage('');\n\n      // Add user message\n      const userMessage: ChatMessage = {\n        id: `msg_${Date.now()}_user`,\n        role: 'user',\n        content: question,\n        timestamp: new Date(),\n        paperId: paper.id,\n      };\n      addChatMessage(userMessage);\n\n      const response = await fetch(\n        `${API_ENDPOINTS.chat}?message=${encodeURIComponent(question)}&content=${encodeURIComponent(paper.content)}`,\n        {\n          method: 'GET',\n          headers: {\n            'Accept': 'text/event-stream',\n          },\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error('Failed to start streaming');\n      }\n\n      const reader = response.body?.getReader();\n      if (!reader) {\n        throw new Error('No response body');\n      }\n\n      const decoder = new TextDecoder();\n      let fullMessage = '';\n\n      while (true) {\n        const { done, value } = await reader.read();\n        \n        if (done) break;\n\n        const chunk = decoder.decode(value);\n        const lines = chunk.split('\\n');\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            \n            if (data === '[DONE]') {\n              // Streaming complete, add final message\n              const assistantMessage: ChatMessage = {\n                id: `msg_${Date.now()}_assistant`,\n                role: 'assistant',\n                content: fullMessage,\n                timestamp: new Date(),\n                paperId: paper.id,\n              };\n              addChatMessage(assistantMessage);\n              return;\n            }\n\n            try {\n              const parsed = JSON.parse(data);\n              if (parsed.chunk) {\n                fullMessage += parsed.chunk;\n                setStreamingMessage(fullMessage);\n                onChunk?.(parsed.chunk);\n              }\n            } catch {\n              // Ignore parsing errors\n            }\n          }\n        }\n      }\n\n    } catch (error) {\n      console.error('Streaming error:', error);\n      // Add error message\n      const errorMessage: ChatMessage = {\n        id: `msg_${Date.now()}_error`,\n        role: 'assistant',\n        content: 'Sorry, I encountered an error while processing your question.',\n        timestamp: new Date(),\n        paperId: paper.id,\n      };\n      addChatMessage(errorMessage);\n    } finally {\n      setIsStreaming(false);\n      setStreamingMessage('');\n    }\n  }, [addChatMessage]);\n\n  return {\n    streamQuestion,\n    isStreaming,\n    streamingMessage,\n  };\n};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;;AAWO,MAAM,gBAAgB;;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,cAAc,EAAE,UAAU,cAAc,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAE/D,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B,SAAS;QACX;gDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAClC,OACA;YAEA,IAAI;gBACF,eAAe;gBACf,SAAS;gBAET,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,gBAAa,CAAC,QAAQ,EAAE;oBACnD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,SAAS,MAAM,EAAE;wBACjB,SAAS,MAAM,OAAO;wBACtB;oBACF;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC7D;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC1D;gBAEA,OAAO,OAAO,IAAI,CAAC,MAAM;YAE3B,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAClF,SAAS;gBACT,eAAe;gBACf,OAAO;YACT,SAAU;gBACR,eAAe;YACjB;QACF;qDAAG;QAAC;KAAe;IAEnB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,OAC9B,OACA;YAEA,IAAI;gBACF,eAAe;gBACf,SAAS;gBAET,sBAAsB;gBACtB,MAAM,cAA2B;oBAC/B,IAAI,AAAC,OAAiB,OAAX,KAAK,GAAG,IAAG;oBACtB,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;oBACf,SAAS,MAAM,EAAE;gBACnB;gBAEA,eAAe;gBAEf,MAAM,WAAW,MAAM,MAAM,0HAAA,CAAA,gBAAa,CAAC,IAAI,EAAE;oBAC/C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,SAAS;wBACT,SAAS,MAAM,EAAE;wBACjB,cAAc,MAAM,OAAO;wBAC3B,SAAS,AAAC,UAAqB,OAAZ,MAAM,KAAK;oBAChC;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC7D;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC1D;gBAEA,MAAM,mBAAgC,OAAO,IAAI,CAAC,OAAO;gBACzD,eAAe;gBAEf,OAAO;YAET,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAClF,SAAS;gBACT,eAAe;gBAEf,4BAA4B;gBAC5B,MAAM,mBAAgC;oBACpC,IAAI,AAAC,OAAiB,OAAX,KAAK,GAAG,IAAG;oBACtB,MAAM;oBACN,SAAS,AAAC,4CAAwD,OAAb;oBACrD,WAAW,IAAI;oBACf,SAAS,MAAM,EAAE;gBACnB;gBAEA,eAAe;gBACf,OAAO;YACT,SAAU;gBACR,eAAe;YACjB;QACF;iDAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YACvC,IAAI;gBACF,eAAe;gBACf,SAAS;gBAET,IAAI,OAAO,MAAM,GAAG,GAAG;oBACrB,MAAM,IAAI,MAAM;gBAClB;gBAEA,8CAA8C;gBAC9C,iFAAiF;gBACjF,MAAM,CAAC,QAAQ,OAAO,GAAG;gBAEzB,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,QAAQ;4BACN,IAAI,OAAO,EAAE;4BACb,OAAO,OAAO,KAAK;4BACnB,SAAS,OAAO,OAAO;wBACzB;wBACA,QAAQ;4BACN,IAAI,OAAO,EAAE;4BACb,OAAO,OAAO,KAAK;4BACnB,SAAS,OAAO,OAAO;wBACzB;oBACF;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC7D;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAC1D;gBAEA,OAAO,OAAO,IAAI,CAAC,UAAU;YAE/B,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,0HAAA,CAAA,iBAAc,CAAC,SAAS;gBAClF,SAAS;gBACT,eAAe;gBACf,OAAO;YACT,SAAU;gBACR,eAAe;YACjB;QACF;mDAAG;QAAC;KAAe;IAEnB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAzLa;;QAI0C,8HAAA,CAAA,cAAW;;;AAwL3D,MAAM,mBAAmB;;IAC9B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAErC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OACjC,OACA,UACA;YAEA,IAAI;oBA4Ba;gBA3Bf,eAAe;gBACf,oBAAoB;gBAEpB,mBAAmB;gBACnB,MAAM,cAA2B;oBAC/B,IAAI,AAAC,OAAiB,OAAX,KAAK,GAAG,IAAG;oBACtB,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;oBACf,SAAS,MAAM,EAAE;gBACnB;gBACA,eAAe;gBAEf,MAAM,WAAW,MAAM,MACrB,AAAC,GAAgC,OAA9B,0HAAA,CAAA,gBAAa,CAAC,IAAI,EAAC,aAAmD,OAAxC,mBAAmB,WAAU,aAA6C,OAAlC,mBAAmB,MAAM,OAAO,IACzG;oBACE,QAAQ;oBACR,SAAS;wBACP,UAAU;oBACZ;gBACF;gBAGF,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,UAAS,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,SAAS;gBACvC,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,UAAU,IAAI;gBACpB,IAAI,cAAc;gBAElB,MAAO,KAAM;oBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;oBAEzC,IAAI,MAAM;oBAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC;oBAC7B,MAAM,QAAQ,MAAM,KAAK,CAAC;oBAE1B,KAAK,MAAM,QAAQ,MAAO;wBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;4BAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;4BAExB,IAAI,SAAS,UAAU;gCACrB,wCAAwC;gCACxC,MAAM,mBAAgC;oCACpC,IAAI,AAAC,OAAiB,OAAX,KAAK,GAAG,IAAG;oCACtB,MAAM;oCACN,SAAS;oCACT,WAAW,IAAI;oCACf,SAAS,MAAM,EAAE;gCACnB;gCACA,eAAe;gCACf;4BACF;4BAEA,IAAI;gCACF,MAAM,SAAS,KAAK,KAAK,CAAC;gCAC1B,IAAI,OAAO,KAAK,EAAE;oCAChB,eAAe,OAAO,KAAK;oCAC3B,oBAAoB;oCACpB,oBAAA,8BAAA,QAAU,OAAO,KAAK;gCACxB;4BACF,EAAE,UAAM;4BACN,wBAAwB;4BAC1B;wBACF;oBACF;gBACF;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,oBAAoB;gBACpB,MAAM,eAA4B;oBAChC,IAAI,AAAC,OAAiB,OAAX,KAAK,GAAG,IAAG;oBACtB,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;oBACf,SAAS,MAAM,EAAE;gBACnB;gBACA,eAAe;YACjB,SAAU;gBACR,eAAe;gBACf,oBAAoB;YACtB;QACF;uDAAG;QAAC;KAAe;IAEnB,OAAO;QACL;QACA;QACA;IACF;AACF;IA5Ga;;QAIgB,8HAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 5870, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/chat/ChatInterface.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Card, Input, Button, Avatar, Typography, Space, Divider, Tooltip, Dropdown } from 'antd';\nimport {\n  SendOutlined,\n  UserOutlined,\n  RobotOutlined,\n  ClearOutlined,\n  DownloadOutlined,\n  CopyOutlined,\n  MoreOutlined,\n  BulbOutlined,\n} from '@ant-design/icons';\nimport { ChatMessage, Paper } from '@/lib/types';\nimport { useAppStore } from '@/store/useAppStore';\nimport { useAIAnalysis } from '@/hooks/useAIAnalysis';\nimport ReactMarkdown from 'react-markdown';\nimport { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';\nimport { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';\n\nconst { TextArea } = Input;\nconst { Text, Paragraph } = Typography;\n\ninterface ChatInterfaceProps {\n  paper: Paper;\n  className?: string;\n}\n\nconst ChatInterface: React.FC<ChatInterfaceProps> = ({\n  paper,\n  className = '',\n}) => {\n  const [inputValue, setInputValue] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<any>(null);\n\n  const { chatMessages, clearChatMessages } = useAppStore();\n  const { askQuestion, isAnalyzing } = useAIAnalysis();\n\n  const paperMessages = chatMessages.filter(msg => msg.paperId === paper.id);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [paperMessages]);\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isAnalyzing) return;\n\n    const userMessage = inputValue.trim();\n    setInputValue('');\n    setIsTyping(true);\n\n    try {\n      await askQuestion(paper, userMessage);\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const handleClearChat = () => {\n    clearChatMessages();\n  };\n\n  const handleCopyMessage = (content: string) => {\n    navigator.clipboard.writeText(content);\n  };\n\n  const handleExportChat = () => {\n    const chatContent = paperMessages\n      .map(msg => `${msg.role.toUpperCase()}: ${msg.content}`)\n      .join('\\n\\n');\n    \n    const blob = new Blob([chatContent], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `chat-${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n\n  const suggestedQuestions = [\n    \"What are the main findings of this paper?\",\n    \"Can you summarize the methodology?\",\n    \"What are the key contributions?\",\n    \"What are the limitations mentioned?\",\n    \"How does this relate to previous work?\",\n  ];\n\n  const handleSuggestedQuestion = (question: string) => {\n    setInputValue(question);\n    inputRef.current?.focus();\n  };\n\n  const chatMenuItems = [\n    {\n      key: 'clear',\n      label: 'Clear Chat',\n      icon: <ClearOutlined />,\n      onClick: handleClearChat,\n    },\n    {\n      key: 'export',\n      label: 'Export Chat',\n      icon: <DownloadOutlined />,\n      onClick: handleExportChat,\n    },\n  ];\n\n  return (\n    <Card \n      className={`chat-container ${className}`}\n      title={\n        <div className=\"flex items-center justify-between\">\n          <Space>\n            <RobotOutlined className=\"text-blue-500\" />\n            <span>AI Assistant</span>\n          </Space>\n          <Dropdown menu={{ items: chatMenuItems }} trigger={['click']}>\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        </div>\n      }\n      styles={{\n        body: {\n          padding: 0,\n          height: 'calc(100vh - 120px)',\n          display: 'flex',\n          flexDirection: 'column'\n        }\n      }}\n    >\n      {/* Messages Area */}\n      <div className=\"chat-messages flex-1 overflow-y-auto p-4 space-y-4\">\n        {paperMessages.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <RobotOutlined className=\"text-4xl text-gray-400 mb-4\" />\n            <Text type=\"secondary\" className=\"block mb-4\">\n              Ask me anything about this paper!\n            </Text>\n            \n            {/* Suggested Questions */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-center mb-3\">\n                <BulbOutlined className=\"text-yellow-500 mr-2\" />\n                <Text strong>Suggested questions:</Text>\n              </div>\n              {suggestedQuestions.map((question, index) => (\n                <Button\n                  key={index}\n                  type=\"dashed\"\n                  size=\"small\"\n                  className=\"block mx-auto mb-2\"\n                  onClick={() => handleSuggestedQuestion(question)}\n                >\n                  {question}\n                </Button>\n              ))}\n            </div>\n          </div>\n        ) : (\n          paperMessages.map((message) => (\n            <div\n              key={message.id}\n              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n            >\n              <div className={`flex items-start space-x-3 max-w-[80%]`}>\n                {message.role === 'assistant' && (\n                  <Avatar \n                    icon={<RobotOutlined />} \n                    className=\"bg-blue-500 flex-shrink-0\"\n                  />\n                )}\n                \n                <div\n                  className={`message-bubble ${\n                    message.role === 'user' \n                      ? 'message-user bg-blue-500 text-white' \n                      : 'message-assistant bg-gray-100'\n                  }`}\n                >\n                  {message.role === 'assistant' ? (\n                    <div className=\"prose prose-sm max-w-none\">\n                      <ReactMarkdown\n                        components={{\n                          code({ className, children, ...props }: any) {\n                            const match = /language-(\\w+)/.exec(className || '');\n                            return match ? (\n                              <SyntaxHighlighter\n                                style={tomorrow as any}\n                                language={match[1]}\n                                PreTag=\"div\"\n                              >\n                                {String(children).replace(/\\n$/, '')}\n                              </SyntaxHighlighter>\n                            ) : (\n                              <code className={`${className} bg-gray-100 px-2 py-1 rounded text-sm`} {...props}>\n                                {children}\n                              </code>\n                            );\n                          },\n                        }}\n                      >\n                        {message.content}\n                      </ReactMarkdown>\n                    </div>\n                  ) : (\n                    <Paragraph className=\"mb-0 text-inherit\">\n                      {message.content}\n                    </Paragraph>\n                  )}\n                  \n                  <div className=\"flex items-center justify-between mt-2 pt-2 border-t border-gray-200 border-opacity-20\">\n                    <Text \n                      className={`text-xs ${\n                        message.role === 'user' ? 'text-blue-100' : 'text-gray-500'\n                      }`}\n                    >\n                      {new Date(message.timestamp).toLocaleTimeString()}\n                    </Text>\n                    \n                    <Tooltip title=\"Copy message\">\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<CopyOutlined />}\n                        onClick={() => handleCopyMessage(message.content)}\n                        className={message.role === 'user' ? 'text-blue-100 hover:text-white' : ''}\n                      />\n                    </Tooltip>\n                  </div>\n                </div>\n                \n                {message.role === 'user' && (\n                  <Avatar \n                    icon={<UserOutlined />} \n                    className=\"bg-gray-500 flex-shrink-0\"\n                  />\n                )}\n              </div>\n            </div>\n          ))\n        )}\n        \n        {/* Typing Indicator */}\n        {(isAnalyzing || isTyping) && (\n          <div className=\"flex justify-start\">\n            <div className=\"flex items-start space-x-3\">\n              <Avatar \n                icon={<RobotOutlined />} \n                className=\"bg-blue-500\"\n              />\n              <div className=\"message-bubble message-assistant bg-gray-100\">\n                <div className=\"loading-dots\">Thinking...</div>\n              </div>\n            </div>\n          </div>\n        )}\n        \n        <div ref={messagesEndRef} />\n      </div>\n\n      <Divider className=\"m-0\" />\n\n      {/* Input Area */}\n      <div className=\"chat-input p-4\">\n        <div className=\"flex space-x-2\">\n          <TextArea\n            ref={inputRef}\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            onKeyDown={handleKeyPress}\n            placeholder=\"Ask a question about this paper...\"\n            autoSize={{ minRows: 1, maxRows: 4 }}\n            className=\"flex-1\"\n            disabled={isAnalyzing}\n          />\n          <Button\n            type=\"primary\"\n            icon={<SendOutlined />}\n            onClick={handleSendMessage}\n            disabled={!inputValue.trim() || isAnalyzing}\n            loading={isAnalyzing}\n          >\n            Send\n          </Button>\n        </div>\n        \n        <div className=\"flex justify-between items-center mt-2 text-xs text-gray-500\">\n          <span>Press Enter to send, Shift+Enter for new line</span>\n          <span>{paperMessages.length} messages</span>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default ChatInterface;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;AAqBA,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAC1B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAOtC,MAAM,gBAA8C;QAAC,EACnD,KAAK,EACL,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IAE7B,MAAM,EAAE,YAAY,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IACtD,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAEjD,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,OAAO,KAAK,MAAM,EAAE;IAEzE,MAAM,iBAAiB;YACrB;SAAA,0BAAA,eAAe,OAAO,cAAtB,8CAAA,wBAAwB,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAc;IAElB,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,MAAM,aAAa;QAEvC,MAAM,cAAc,WAAW,IAAI;QACnC,cAAc;QACd,YAAY;QAEZ,IAAI;YACF,MAAM,YAAY,OAAO;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,UAAU,SAAS,CAAC,SAAS,CAAC;IAChC;IAEA,MAAM,mBAAmB;QACvB,MAAM,cAAc,cACjB,GAAG,CAAC,CAAA,MAAO,AAAC,GAA6B,OAA3B,IAAI,IAAI,CAAC,WAAW,IAAG,MAAgB,OAAZ,IAAI,OAAO,GACpD,IAAI,CAAC;QAER,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAa;QAC1D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,AAAC,QAA6D,OAAtD,MAAM,KAAK,CAAC,OAAO,CAAC,eAAe,KAAK,WAAW,IAAG;QAC9E,KAAK,KAAK;QACV,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,0BAA0B,CAAC;YAE/B;QADA,cAAc;SACd,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,KAAK;IACzB;IAEA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;YACpB,SAAS;QACX;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,SAAS;QACX;KACD;IAED,qBACE,6LAAC,iLAAA,CAAA,OAAI;QACH,WAAW,AAAC,kBAA2B,OAAV;QAC7B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mMAAA,CAAA,QAAK;;sCACJ,6LAAC,uNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;sCAAK;;;;;;;;;;;;8BAER,6LAAC,yLAAA,CAAA,WAAQ;oBAAC,MAAM;wBAAE,OAAO;oBAAc;oBAAG,SAAS;wBAAC;qBAAQ;8BAC1D,cAAA,6LAAC,qMAAA,CAAA,SAAM;wBAAC,MAAK;wBAAO,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;QAI7C,QAAQ;YACN,MAAM;gBACJ,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,eAAe;YACjB;QACF;;0BAGA,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC;gCAAK,MAAK;gCAAY,WAAU;0CAAa;;;;;;0CAK9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;gDAAK,MAAM;0DAAC;;;;;;;;;;;;oCAEd,mBAAmB,GAAG,CAAC,CAAC,UAAU,sBACjC,6LAAC,qMAAA,CAAA,SAAM;4CAEL,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,wBAAwB;sDAEtC;2CANI;;;;;;;;;;;;;;;;mEAYb,cAAc,GAAG,CAAC,CAAC,wBACjB,6LAAC;4BAEC,WAAW,AAAC,QAAiE,OAA1D,QAAQ,IAAI,KAAK,SAAS,gBAAgB;sCAE7D,cAAA,6LAAC;gCAAI,WAAY;;oCACd,QAAQ,IAAI,KAAK,6BAChB,6LAAC,qLAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;wCACpB,WAAU;;;;;;kDAId,6LAAC;wCACC,WAAW,AAAC,kBAIX,OAHC,QAAQ,IAAI,KAAK,SACb,wCACA;;4CAGL,QAAQ,IAAI,KAAK,4BAChB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2LAAA,CAAA,UAAa;oDACZ,YAAY;wDACV,MAAK,KAAsC;gEAAtC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY,GAAtC;4DACH,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;4DACjD,OAAO,sBACL,6LAAC,6MAAA,CAAA,QAAiB;gEAChB,OAAO,sOAAA,CAAA,WAAQ;gEACf,UAAU,KAAK,CAAC,EAAE;gEAClB,QAAO;0EAEN,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;uFAGnC,6LAAC;gEAAK,WAAW,AAAC,GAAY,OAAV,WAAU;gEAA0C,GAAG,KAAK;0EAC7E;;;;;;wDAGP;oDACF;8DAEC,QAAQ,OAAO;;;;;;;;;;yGAIpB,6LAAC;gDAAU,WAAU;0DAClB,QAAQ,OAAO;;;;;;0DAIpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAW,AAAC,WAEX,OADC,QAAQ,IAAI,KAAK,SAAS,kBAAkB;kEAG7C,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;kEAGjD,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4DACnB,SAAS,IAAM,kBAAkB,QAAQ,OAAO;4DAChD,WAAW,QAAQ,IAAI,KAAK,SAAS,mCAAmC;;;;;;;;;;;;;;;;;;;;;;;oCAM/E,QAAQ,IAAI,KAAK,wBAChB,6LAAC,qLAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,WAAU;;;;;;;;;;;;2BAzEX,QAAQ,EAAE;;;;;oBAkFpB,CAAC,eAAe,QAAQ,mBACvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qLAAA,CAAA,SAAM;oCACL,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oCACpB,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;kCAMtC,6LAAC;wBAAI,KAAK;;;;;;;;;;;;0BAGZ,6LAAC,uLAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BAGnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAW;gCACX,aAAY;gCACZ,UAAU;oCAAE,SAAS;oCAAG,SAAS;gCAAE;gCACnC,WAAU;gCACV,UAAU;;;;;;0CAEZ,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gCACnB,SAAS;gCACT,UAAU,CAAC,WAAW,IAAI,MAAM;gCAChC,SAAS;0CACV;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAM,cAAc,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAKtC;GAzRM;;QASwC,8HAAA,CAAA,cAAW;QAClB,gIAAA,CAAA,gBAAa;;;KAV9C;uCA2RS", "debugId": null}}, {"offset": {"line": 6442, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/chat/QuickActions.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Card, Button, Space, Typography, Divider, Modal, Spin } from 'antd';\nimport {\n  FileTextOutlined,\n  BulbOutlined,\n  ExperimentOutlined,\n  BookOutlined,\n  LinkOutlined,\n  BarChartOutlined,\n} from '@ant-design/icons';\nimport { Paper } from '@/lib/types';\nimport { useAIAnalysis } from '@/hooks/useAIAnalysis';\nimport { ANALYSIS_TYPES } from '@/lib/constants';\nimport ReactMarkdown from 'react-markdown';\n\nconst { Title, Text } = Typography;\n\ninterface QuickActionsProps {\n  paper: Paper;\n  className?: string;\n}\n\nconst QuickActions: React.FC<QuickActionsProps> = ({\n  paper,\n  className = '',\n}) => {\n  const [analysisResult, setAnalysisResult] = useState<string>('');\n  const [analysisType, setAnalysisType] = useState<string>('');\n  const [modalVisible, setModalVisible] = useState(false);\n  \n  const { analyzeDocument, isAnalyzing } = useAIAnalysis();\n\n  const handleQuickAnalysis = async (type: string, title: string) => {\n    setAnalysisType(title);\n    setModalVisible(true);\n    setAnalysisResult('');\n\n    const result = await analyzeDocument(paper, type);\n    if (result) {\n      setAnalysisResult(result);\n    }\n  };\n\n  const quickActions = [\n    {\n      key: ANALYSIS_TYPES.SUMMARY,\n      title: 'Summarize',\n      description: 'Get a comprehensive summary',\n      icon: <FileTextOutlined />,\n      color: '#1890ff',\n    },\n    {\n      key: ANALYSIS_TYPES.KEY_FINDINGS,\n      title: 'Key Findings',\n      description: 'Extract main discoveries',\n      icon: <BulbOutlined />,\n      color: '#52c41a',\n    },\n    {\n      key: ANALYSIS_TYPES.METHODOLOGY,\n      title: 'Methodology',\n      description: 'Explain research methods',\n      icon: <ExperimentOutlined />,\n      color: '#722ed1',\n    },\n    {\n      key: ANALYSIS_TYPES.CONCEPTS,\n      title: 'Key Concepts',\n      description: 'Define important terms',\n      icon: <BookOutlined />,\n      color: '#fa8c16',\n    },\n    {\n      key: ANALYSIS_TYPES.CITATIONS,\n      title: 'Citations',\n      description: 'Analyze references',\n      icon: <LinkOutlined />,\n      color: '#eb2f96',\n    },\n  ];\n\n  return (\n    <>\n      <Card \n        title=\"Quick Analysis\" \n        className={className}\n        extra={\n          <Text type=\"secondary\" className=\"text-sm\">\n            AI-powered insights\n          </Text>\n        }\n      >\n        <div className=\"space-y-3\">\n          {quickActions.map((action) => (\n            <Button\n              key={action.key}\n              block\n              size=\"large\"\n              className=\"text-left h-auto py-3\"\n              onClick={() => handleQuickAnalysis(action.key, action.title)}\n              loading={isAnalyzing && analysisType === action.title}\n              disabled={isAnalyzing}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div \n                  className=\"flex items-center justify-center w-8 h-8 rounded\"\n                  style={{ backgroundColor: `${action.color}20`, color: action.color }}\n                >\n                  {action.icon}\n                </div>\n                <div className=\"flex-1 text-left\">\n                  <div className=\"font-medium\">{action.title}</div>\n                  <div className=\"text-sm text-gray-500\">{action.description}</div>\n                </div>\n              </div>\n            </Button>\n          ))}\n        </div>\n\n        <Divider />\n\n        <div className=\"text-center\">\n          <Text type=\"secondary\" className=\"text-sm\">\n            Or ask specific questions in the chat above\n          </Text>\n        </div>\n      </Card>\n\n      {/* Analysis Result Modal */}\n      <Modal\n        title={\n          <Space>\n            <BarChartOutlined />\n            {analysisType} - {paper.title}\n          </Space>\n        }\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setModalVisible(false)}>\n            Close\n          </Button>,\n        ]}\n        width={800}\n        className=\"analysis-modal\"\n      >\n        {isAnalyzing ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <Spin size=\"large\" />\n            <Text className=\"ml-3\">Analyzing paper...</Text>\n          </div>\n        ) : analysisResult ? (\n          <div className=\"prose prose-sm max-w-none\">\n            <ReactMarkdown>{analysisResult}</ReactMarkdown>\n          </div>\n        ) : (\n          <div className=\"text-center py-8\">\n            <Text type=\"secondary\">No analysis result available</Text>\n          </div>\n        )}\n      </Modal>\n    </>\n  );\n};\n\nexport default QuickActions;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;;;AAfA;;;;;;;AAiBA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAOlC,MAAM,eAA4C;QAAC,EACjD,KAAK,EACL,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAErD,MAAM,sBAAsB,OAAO,MAAc;QAC/C,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAElB,MAAM,SAAS,MAAM,gBAAgB,OAAO;QAC5C,IAAI,QAAQ;YACV,kBAAkB;QACpB;IACF;IAEA,MAAM,eAAe;QACnB;YACE,KAAK,0HAAA,CAAA,iBAAc,CAAC,OAAO;YAC3B,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;QACT;QACA;YACE,KAAK,0HAAA,CAAA,iBAAc,CAAC,YAAY;YAChC,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;QACA;YACE,KAAK,0HAAA,CAAA,iBAAc,CAAC,WAAW;YAC/B,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;YACzB,OAAO;QACT;QACA;YACE,KAAK,0HAAA,CAAA,iBAAc,CAAC,QAAQ;YAC5B,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;QACA;YACE,KAAK,0HAAA,CAAA,iBAAc,CAAC,SAAS;YAC7B,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;KACD;IAED,qBACE;;0BACE,6LAAC,iLAAA,CAAA,OAAI;gBACH,OAAM;gBACN,WAAW;gBACX,qBACE,6LAAC;oBAAK,MAAK;oBAAY,WAAU;8BAAU;;;;;;;kCAK7C,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,qMAAA,CAAA,SAAM;gCAEL,KAAK;gCACL,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,oBAAoB,OAAO,GAAG,EAAE,OAAO,KAAK;gCAC3D,SAAS,eAAe,iBAAiB,OAAO,KAAK;gCACrD,UAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,AAAC,GAAe,OAAb,OAAO,KAAK,EAAC;gDAAK,OAAO,OAAO,KAAK;4CAAC;sDAElE,OAAO,IAAI;;;;;;sDAEd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAe,OAAO,KAAK;;;;;;8DAC1C,6LAAC;oDAAI,WAAU;8DAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;+BAjBzD,OAAO,GAAG;;;;;;;;;;kCAwBrB,6LAAC,uLAAA,CAAA,UAAO;;;;;kCAER,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,MAAK;4BAAY,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAO/C,6LAAC,mLAAA,CAAA,QAAK;gBACJ,qBACE,6LAAC,mMAAA,CAAA,QAAK;;sCACJ,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAChB;wBAAa;wBAAI,MAAM,KAAK;;;;;;;gBAGjC,MAAM;gBACN,UAAU,IAAM,gBAAgB;gBAChC,QAAQ;kCACN,6LAAC,qMAAA,CAAA,SAAM;wBAAa,SAAS,IAAM,gBAAgB;kCAAQ;uBAA/C;;;;;iBAGb;gBACD,OAAO;gBACP,WAAU;0BAET,4BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iLAAA,CAAA,OAAI;4BAAC,MAAK;;;;;;sCACX,6LAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;+DAEvB,+BACF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2LAAA,CAAA,UAAa;kCAAE;;;;;;;;;;6EAGlB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;;;;;;;AAMnC;GA7IM;;QAQqC,gIAAA,CAAA,gBAAa;;;KARlD;uCA+IS", "debugId": null}}, {"offset": {"line": 6761, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/pdf/AnnotationPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Card, List, Button, Input, Tag, Typography, Space, Popconfirm, Tooltip } from 'antd';\nimport {\n  HighlightOutlined,\n  EditOutlined,\n  BookOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  MessageOutlined,\n} from '@ant-design/icons';\nimport { Annotation } from '@/lib/types';\nimport { useAppStore } from '@/store/useAppStore';\n\nconst { Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface AnnotationPanelProps {\n  paperId: string;\n  currentPage?: number;\n  onAnnotationClick?: (annotation: Annotation) => void;\n  className?: string;\n}\n\nconst AnnotationPanel: React.FC<AnnotationPanelProps> = ({\n  paperId,\n  currentPage,\n  onAnnotationClick,\n  className = '',\n}) => {\n  const { annotations, removeAnnotation, updateAnnotation } = useAppStore();\n  const [editingId, setEditingId] = useState<string | null>(null);\n  const [editContent, setEditContent] = useState<string>('');\n  const [filter, setFilter] = useState<'all' | 'highlight' | 'note' | 'bookmark'>('all');\n\n  const paperAnnotations = annotations.filter(a => a.paperId === paperId);\n  const filteredAnnotations = paperAnnotations.filter(a => \n    filter === 'all' || a.type === filter\n  );\n\n  const handleEdit = (annotation: Annotation) => {\n    setEditingId(annotation.id);\n    setEditContent(annotation.content);\n  };\n\n  const handleSaveEdit = (annotationId: string) => {\n    updateAnnotation(annotationId, { content: editContent });\n    setEditingId(null);\n    setEditContent('');\n  };\n\n  const handleCancelEdit = () => {\n    setEditingId(null);\n    setEditContent('');\n  };\n\n  const handleDelete = (annotationId: string) => {\n    removeAnnotation(annotationId);\n  };\n\n  const getAnnotationIcon = (type: string) => {\n    switch (type) {\n      case 'highlight':\n        return <HighlightOutlined className=\"text-yellow-500\" />;\n      case 'note':\n        return <MessageOutlined className=\"text-blue-500\" />;\n      case 'bookmark':\n        return <BookOutlined className=\"text-red-500\" />;\n      default:\n        return <EditOutlined />;\n    }\n  };\n\n  const getAnnotationColor = (type: string) => {\n    switch (type) {\n      case 'highlight':\n        return 'gold';\n      case 'note':\n        return 'blue';\n      case 'bookmark':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n\n  const filterButtons = [\n    { key: 'all', label: 'All', count: paperAnnotations.length },\n    { key: 'highlight', label: 'Highlights', count: paperAnnotations.filter(a => a.type === 'highlight').length },\n    { key: 'note', label: 'Notes', count: paperAnnotations.filter(a => a.type === 'note').length },\n    { key: 'bookmark', label: 'Bookmarks', count: paperAnnotations.filter(a => a.type === 'bookmark').length },\n  ];\n\n  return (\n    <Card \n      title=\"Annotations\" \n      className={className}\n      extra={\n        <Text type=\"secondary\" className=\"text-sm\">\n          {filteredAnnotations.length} items\n        </Text>\n      }\n    >\n      {/* Filter Buttons */}\n      <div className=\"mb-4 flex flex-wrap gap-2\">\n        {filterButtons.map(({ key, label, count }) => (\n          <Button\n            key={key}\n            size=\"small\"\n            type={filter === key ? 'primary' : 'default'}\n            onClick={() => setFilter(key as any)}\n          >\n            {label} ({count})\n          </Button>\n        ))}\n      </div>\n\n      {/* Annotations List */}\n      <List\n        dataSource={filteredAnnotations}\n        locale={{ emptyText: 'No annotations yet' }}\n        renderItem={(annotation) => (\n          <List.Item\n            className={`${\n              currentPage === annotation.pageNumber ? 'bg-blue-50 border-blue-200' : ''\n            } rounded p-3 mb-2 border transition-colors hover:bg-gray-50`}\n          >\n            <div className=\"w-full\">\n              <div className=\"flex items-start justify-between mb-2\">\n                <div className=\"flex items-center space-x-2\">\n                  {getAnnotationIcon(annotation.type)}\n                  <Tag color={getAnnotationColor(annotation.type)}>\n                    {annotation.type}\n                  </Tag>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Page {annotation.pageNumber}\n                  </Text>\n                </div>\n                \n                <Space size=\"small\">\n                  <Tooltip title=\"Go to annotation\">\n                    <Button\n                      type=\"text\"\n                      size=\"small\"\n                      icon={<EyeOutlined />}\n                      onClick={() => onAnnotationClick?.(annotation)}\n                    />\n                  </Tooltip>\n                  <Tooltip title=\"Edit\">\n                    <Button\n                      type=\"text\"\n                      size=\"small\"\n                      icon={<EditOutlined />}\n                      onClick={() => handleEdit(annotation)}\n                    />\n                  </Tooltip>\n                  <Popconfirm\n                    title=\"Delete annotation?\"\n                    description=\"This action cannot be undone.\"\n                    onConfirm={() => handleDelete(annotation.id)}\n                    okText=\"Delete\"\n                    cancelText=\"Cancel\"\n                    okButtonProps={{ danger: true }}\n                  >\n                    <Tooltip title=\"Delete\">\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<DeleteOutlined />}\n                        danger\n                      />\n                    </Tooltip>\n                  </Popconfirm>\n                </Space>\n              </div>\n\n              {editingId === annotation.id ? (\n                <div className=\"space-y-2\">\n                  <TextArea\n                    value={editContent}\n                    onChange={(e) => setEditContent(e.target.value)}\n                    rows={3}\n                    placeholder=\"Edit annotation content...\"\n                  />\n                  <div className=\"flex justify-end space-x-2\">\n                    <Button size=\"small\" onClick={handleCancelEdit}>\n                      Cancel\n                    </Button>\n                    <Button \n                      type=\"primary\" \n                      size=\"small\" \n                      onClick={() => handleSaveEdit(annotation.id)}\n                    >\n                      Save\n                    </Button>\n                  </div>\n                </div>\n              ) : (\n                <Paragraph \n                  className=\"mb-0 text-sm\"\n                  ellipsis={{ rows: 3, expandable: true, symbol: 'more' }}\n                >\n                  {annotation.content}\n                </Paragraph>\n              )}\n\n              <Text type=\"secondary\" className=\"text-xs\">\n                {new Date(annotation.createdAt).toLocaleString()}\n              </Text>\n            </div>\n          </List.Item>\n        )}\n      />\n    </Card>\n  );\n};\n\nexport default AnnotationPanel;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAbA;;;;;AAeA,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AACtC,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAS1B,MAAM,kBAAkD;QAAC,EACvD,OAAO,EACP,WAAW,EACX,iBAAiB,EACjB,YAAY,EAAE,EACf;;IACC,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C;IAEhF,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;IAC/D,MAAM,sBAAsB,iBAAiB,MAAM,CAAC,CAAA,IAClD,WAAW,SAAS,EAAE,IAAI,KAAK;IAGjC,MAAM,aAAa,CAAC;QAClB,aAAa,WAAW,EAAE;QAC1B,eAAe,WAAW,OAAO;IACnC;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,cAAc;YAAE,SAAS;QAAY;QACtD,aAAa;QACb,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,eAAe;IACjB;IAEA,MAAM,eAAe,CAAC;QACpB,iBAAiB;IACnB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,+NAAA,CAAA,oBAAiB;oBAAC,WAAU;;;;;;YACtC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,6LAAC,qNAAA,CAAA,eAAY;;;;;QACxB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB;YAAE,KAAK;YAAO,OAAO;YAAO,OAAO,iBAAiB,MAAM;QAAC;QAC3D;YAAE,KAAK;YAAa,OAAO;YAAc,OAAO,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;QAAC;QAC5G;YAAE,KAAK;YAAQ,OAAO;YAAS,OAAO,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;QAAC;QAC7F;YAAE,KAAK;YAAY,OAAO;YAAa,OAAO,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,MAAM;QAAC;KAC1G;IAED,qBACE,6LAAC,iLAAA,CAAA,OAAI;QACH,OAAM;QACN,WAAW;QACX,qBACE,6LAAC;YAAK,MAAK;YAAY,WAAU;;gBAC9B,oBAAoB,MAAM;gBAAC;;;;;;;;0BAKhC,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC;wBAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE;yCACvC,6LAAC,qMAAA,CAAA,SAAM;wBAEL,MAAK;wBACL,MAAM,WAAW,MAAM,YAAY;wBACnC,SAAS,IAAM,UAAU;;4BAExB;4BAAM;4BAAG;4BAAM;;uBALX;;;;;;;;;;;0BAWX,6LAAC,iLAAA,CAAA,OAAI;gBACH,YAAY;gBACZ,QAAQ;oBAAE,WAAW;gBAAqB;gBAC1C,YAAY,CAAC,2BACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wBACR,WAAW,AAAC,GAEX,OADC,gBAAgB,WAAW,UAAU,GAAG,+BAA+B,IACxE;kCAED,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,kBAAkB,WAAW,IAAI;8DAClC,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAO,mBAAmB,WAAW,IAAI;8DAC3C,WAAW,IAAI;;;;;;8DAElB,6LAAC;oDAAK,MAAK;oDAAY,WAAU;;wDAAU;wDACnC,WAAW,UAAU;;;;;;;;;;;;;sDAI/B,6LAAC,mMAAA,CAAA,QAAK;4CAAC,MAAK;;8DACV,6LAAC,uLAAA,CAAA,UAAO;oDAAC,OAAM;8DACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;wDACL,MAAK;wDACL,MAAK;wDACL,oBAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;wDAClB,SAAS,IAAM,8BAAA,wCAAA,kBAAoB;;;;;;;;;;;8DAGvC,6LAAC,uLAAA,CAAA,UAAO;oDAAC,OAAM;8DACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;wDACL,MAAK;wDACL,MAAK;wDACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wDACnB,SAAS,IAAM,WAAW;;;;;;;;;;;8DAG9B,6LAAC,6LAAA,CAAA,aAAU;oDACT,OAAM;oDACN,aAAY;oDACZ,WAAW,IAAM,aAAa,WAAW,EAAE;oDAC3C,QAAO;oDACP,YAAW;oDACX,eAAe;wDAAE,QAAQ;oDAAK;8DAE9B,cAAA,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,qMAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;4DACrB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAOf,cAAc,WAAW,EAAE,iBAC1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,MAAM;4CACN,aAAY;;;;;;sDAEd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,SAAM;oDAAC,MAAK;oDAAQ,SAAS;8DAAkB;;;;;;8DAGhD,6LAAC,qMAAA,CAAA,SAAM;oDACL,MAAK;oDACL,MAAK;oDACL,SAAS,IAAM,eAAe,WAAW,EAAE;8DAC5C;;;;;;;;;;;;;;;;;2DAML,6LAAC;oCACC,WAAU;oCACV,UAAU;wCAAE,MAAM;wCAAG,YAAY;wCAAM,QAAQ;oCAAO;8CAErD,WAAW,OAAO;;;;;;8CAIvB,6LAAC;oCAAK,MAAK;oCAAY,WAAU;8CAC9B,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D;GA/LM;;QAMwD,8HAAA,CAAA,cAAW;;;KANnE;uCAiMS", "debugId": null}}, {"offset": {"line": 7171, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/views/ReaderView.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Button, Typography, Empty, Tabs } from 'antd';\nimport { BookOutlined, ArrowLeftOutlined, MessageOutlined, HighlightOutlined } from '@ant-design/icons';\nimport { useAppStore } from '@/store/useAppStore';\nimport { VIEW_MODES } from '@/lib/constants';\nimport PDFViewer from '@/components/pdf/PDFViewer';\nimport ChatInterface from '@/components/chat/ChatInterface';\nimport QuickActions from '@/components/chat/QuickActions';\nimport AnnotationPanel from '@/components/pdf/AnnotationPanel';\nimport { Annotation } from '@/lib/types';\n\nconst { Title, Text } = Typography;\n\nconst ReaderView: React.FC = () => {\n  const { currentPaper, setCurrentView } = useAppStore();\n  const [activeTab, setActiveTab] = useState('chat');\n\n  const handleBackToLibrary = () => {\n    setCurrentView(VIEW_MODES.LIBRARY);\n  };\n\n  const handleAnnotationClick = (annotation: Annotation) => {\n    // In a real implementation, you would scroll to the annotation\n    console.log('Navigate to annotation:', annotation);\n  };\n\n  if (!currentPaper) {\n    return (\n      <div className=\"p-6 h-full flex items-center justify-center\">\n        <Empty\n          image={<BookOutlined className=\"text-6xl text-gray-400\" />}\n          description=\"No paper selected\"\n        >\n          <Button type=\"primary\" onClick={handleBackToLibrary}>\n            Go to Library\n          </Button>\n        </Empty>\n      </div>\n    );\n  }\n\n  const sidebarTabs = [\n    {\n      key: 'chat',\n      label: (\n        <span>\n          <MessageOutlined />\n          Chat\n        </span>\n      ),\n      children: (\n        <div className=\"h-full flex flex-col\">\n          <div className=\"flex-1\">\n            <ChatInterface paper={currentPaper} />\n          </div>\n          <div className=\"mt-4\">\n            <QuickActions paper={currentPaper} />\n          </div>\n        </div>\n      ),\n    },\n    {\n      key: 'annotations',\n      label: (\n        <span>\n          <HighlightOutlined />\n          Notes\n        </span>\n      ),\n      children: (\n        <AnnotationPanel\n          paperId={currentPaper.id}\n          onAnnotationClick={handleAnnotationClick}\n          className=\"h-full\"\n        />\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"h-full flex\">\n      {/* PDF Viewer Side */}\n      <div className=\"flex-1 bg-gray-50 p-4\">\n        <div className=\"mb-4\">\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={handleBackToLibrary}\n            className=\"mb-2\"\n          >\n            Back to Library\n          </Button>\n          <Title level={4} className=\"m-0 mb-1\">\n            {currentPaper.title}\n          </Title>\n          <Text type=\"secondary\">\n            by {currentPaper.authors.join(', ')}\n          </Text>\n        </div>\n\n        <PDFViewer\n          paper={currentPaper}\n          className=\"h-full\"\n          onAnnotationCreate={(annotation) => {\n            console.log('New annotation created:', annotation);\n          }}\n        />\n      </div>\n\n      {/* Sidebar */}\n      <div className=\"w-96 border-l bg-white\">\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={sidebarTabs}\n          className=\"h-full\"\n          tabBarStyle={{\n            margin: 0,\n            padding: '0 16px',\n            borderBottom: '1px solid #f0f0f0'\n          }}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default ReaderView;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAaA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAElC,MAAM,aAAuB;;IAC3B,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,sBAAsB;QAC1B,eAAe,0HAAA,CAAA,aAAU,CAAC,OAAO;IACnC;IAEA,MAAM,wBAAwB,CAAC;QAC7B,+DAA+D;QAC/D,QAAQ,GAAG,CAAC,2BAA2B;IACzC;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;gBACJ,qBAAO,6LAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;gBAC/B,aAAY;0BAEZ,cAAA,6LAAC,qMAAA,CAAA,SAAM;oBAAC,MAAK;oBAAU,SAAS;8BAAqB;;;;;;;;;;;;;;;;IAM7D;IAEA,MAAM,cAAc;QAClB;YACE,KAAK;YACL,qBACE,6LAAC;;kCACC,6LAAC,2NAAA,CAAA,kBAAe;;;;;oBAAG;;;;;;;YAIvB,wBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8IAAA,CAAA,UAAa;4BAAC,OAAO;;;;;;;;;;;kCAExB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6IAAA,CAAA,UAAY;4BAAC,OAAO;;;;;;;;;;;;;;;;;QAI7B;QACA;YACE,KAAK;YACL,qBACE,6LAAC;;kCACC,6LAAC,+NAAA,CAAA,oBAAiB;;;;;oBAAG;;;;;;;YAIzB,wBACE,6LAAC,+IAAA,CAAA,UAAe;gBACd,SAAS,aAAa,EAAE;gBACxB,mBAAmB;gBACnB,WAAU;;;;;;QAGhB;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,SAAM;gCACL,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;gCACxB,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAM,OAAO;gCAAG,WAAU;0CACxB,aAAa,KAAK;;;;;;0CAErB,6LAAC;gCAAK,MAAK;;oCAAY;oCACjB,aAAa,OAAO,CAAC,IAAI,CAAC;;;;;;;;;;;;;kCAIlC,6LAAC,yIAAA,CAAA,UAAS;wBACR,OAAO;wBACP,WAAU;wBACV,oBAAoB,CAAC;4BACnB,QAAQ,GAAG,CAAC,2BAA2B;wBACzC;;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBACH,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,WAAU;oBACV,aAAa;wBACX,QAAQ;wBACR,SAAS;wBACT,cAAc;oBAChB;;;;;;;;;;;;;;;;;AAKV;GA/GM;;QACqC,8HAAA,CAAA,cAAW;;;KADhD;uCAiHS", "debugId": null}}, {"offset": {"line": 7441, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/views/ComparisonView.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { <PERSON>, Typography, Button, Empty } from 'antd';\nimport { SwapOutlined } from '@ant-design/icons';\nimport { useAppStore } from '@/store/useAppStore';\nimport { VIEW_MODES } from '@/lib/constants';\n\nconst { Title } = Typography;\n\nconst ComparisonView: React.FC = () => {\n  const { setCurrentView } = useAppStore();\n\n  const handleGoToLibrary = () => {\n    setCurrentView(VIEW_MODES.LIBRARY);\n  };\n\n  return (\n    <div className=\"p-6 h-full\">\n      <Title level={2} className=\"mb-6\">\n        Paper Comparison\n      </Title>\n      \n      <Card className=\"h-full\">\n        <div className=\"flex items-center justify-center h-full\">\n          <Empty\n            image={<SwapOutlined className=\"text-6xl text-gray-400\" />}\n            description=\"Paper comparison feature coming soon\"\n          >\n            <Button type=\"primary\" onClick={handleGoToLibrary}>\n              Go to Library\n            </Button>\n          </Empty>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default ComparisonView;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;AAQA,MAAM,EAAE,KAAK,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE5B,MAAM,iBAA2B;;IAC/B,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAErC,MAAM,oBAAoB;QACxB,eAAe,0HAAA,CAAA,aAAU,CAAC,OAAO;IACnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAM,OAAO;gBAAG,WAAU;0BAAO;;;;;;0BAIlC,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;wBACJ,qBAAO,6LAAC,qNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B,aAAY;kCAEZ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BAAC,MAAK;4BAAU,SAAS;sCAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D;GA3BM;;QACuB,8HAAA,CAAA,cAAW;;;KADlC;uCA6BS", "debugId": null}}, {"offset": {"line": 7540, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/views/AnalysisView.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { <PERSON>, Typo<PERSON>, Button, Empty } from 'antd';\nimport { BarChartOutlined } from '@ant-design/icons';\nimport { useAppStore } from '@/store/useAppStore';\nimport { VIEW_MODES } from '@/lib/constants';\n\nconst { Title } = Typography;\n\nconst AnalysisView: React.FC = () => {\n  const { setCurrentView } = useAppStore();\n\n  const handleGoToLibrary = () => {\n    setCurrentView(VIEW_MODES.LIBRARY);\n  };\n\n  return (\n    <div className=\"p-6 h-full\">\n      <Title level={2} className=\"mb-6\">\n        Paper Analysis\n      </Title>\n      \n      <Card className=\"h-full\">\n        <div className=\"flex items-center justify-center h-full\">\n          <Empty\n            image={<BarChartOutlined className=\"text-6xl text-gray-400\" />}\n            description=\"Advanced analysis features coming soon\"\n          >\n            <Button type=\"primary\" onClick={handleGoToLibrary}>\n              Go to Library\n            </Button>\n          </Empty>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default AnalysisView;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;AAQA,MAAM,EAAE,KAAK,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE5B,MAAM,eAAyB;;IAC7B,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAErC,MAAM,oBAAoB;QACxB,eAAe,0HAAA,CAAA,aAAU,CAAC,OAAO;IACnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAM,OAAO;gBAAG,WAAU;0BAAO;;;;;;0BAIlC,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;wBACJ,qBAAO,6LAAC,6NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;wBACnC,aAAY;kCAEZ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BAAC,MAAK;4BAAU,SAAS;sCAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D;GA3BM;;QACuB,8HAAA,CAAA,cAAW;;;KADlC;uCA6BS", "debugId": null}}, {"offset": {"line": 7639, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport LibraryView from '@/components/views/LibraryView';\nimport ReaderView from '@/components/views/ReaderView';\nimport ComparisonView from '@/components/views/ComparisonView';\nimport AnalysisView from '@/components/views/AnalysisView';\nimport { useAppStore } from '@/store/useAppStore';\nimport { VIEW_MODES } from '@/lib/constants';\n\nexport default function Home() {\n  const { currentView } = useAppStore();\n\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case VIEW_MODES.LIBRARY:\n        return <LibraryView />;\n      case VIEW_MODES.READER:\n        return <ReaderView />;\n      case VIEW_MODES.COMPARISON:\n        return <ComparisonView />;\n      case VIEW_MODES.ANALYSIS:\n        return <AnalysisView />;\n      default:\n        return <LibraryView />;\n    }\n  };\n\n  return (\n    <MainLayout>\n      {renderCurrentView()}\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAElC,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK,0HAAA,CAAA,aAAU,CAAC,OAAO;gBACrB,qBAAO,6LAAC,6IAAA,CAAA,UAAW;;;;;YACrB,KAAK,0HAAA,CAAA,aAAU,CAAC,MAAM;gBACpB,qBAAO,6LAAC,4IAAA,CAAA,UAAU;;;;;YACpB,KAAK,0HAAA,CAAA,aAAU,CAAC,UAAU;gBACxB,qBAAO,6LAAC,gJAAA,CAAA,UAAc;;;;;YACxB,KAAK,0HAAA,CAAA,aAAU,CAAC,QAAQ;gBACtB,qBAAO,6LAAC,8IAAA,CAAA,UAAY;;;;;YACtB;gBACE,qBAAO,6LAAC,6IAAA,CAAA,UAAW;;;;;QACvB;IACF;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAU;kBACR;;;;;;AAGP;GAvBwB;;QACE,8HAAA,CAAA,cAAW;;;KADb", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/test-utils.ts"], "sourcesContent": ["import { Paper } from './types';\nimport { Annotation } from './annotation-types';\n\nexport interface TestResult {\n  success: boolean;\n  message: string;\n  details?: any;\n  duration?: number;\n}\n\nexport interface WorkflowTestResult {\n  testName: string;\n  steps: Array<{\n    name: string;\n    result: TestResult;\n  }>;\n  overallSuccess: boolean;\n  totalDuration: number;\n}\n\nexport class EndToEndTester {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = 'http://localhost:3000') {\n    this.baseUrl = baseUrl;\n  }\n\n  async testCompleteWorkflow(): Promise<WorkflowTestResult> {\n    const startTime = Date.now();\n    const steps: Array<{ name: string; result: TestResult }> = [];\n\n    // Step 1: Test PDF Upload\n    const uploadResult = await this.testPDFUpload();\n    steps.push({ name: 'PDF Upload', result: uploadResult });\n\n    let paperId: string | null = null;\n    if (uploadResult.success && uploadResult.details?.paperId) {\n      paperId = uploadResult.details.paperId;\n    }\n\n    // Step 2: Test PDF Display\n    if (paperId) {\n      const displayResult = await this.testPDFDisplay(paperId);\n      steps.push({ name: 'PDF Display', result: displayResult });\n    }\n\n    // Step 3: Test AI Analysis\n    if (paperId) {\n      const analysisResult = await this.testAIAnalysis(paperId);\n      steps.push({ name: 'AI Analysis', result: analysisResult });\n    }\n\n    // Step 4: Test Annotation System\n    if (paperId) {\n      const annotationResult = await this.testAnnotationSystem(paperId);\n      steps.push({ name: 'Annotation System', result: annotationResult });\n    }\n\n    // Step 5: Test Q&A System\n    if (paperId) {\n      const qaResult = await this.testQASystem(paperId);\n      steps.push({ name: 'Q&A System', result: qaResult });\n    }\n\n    // Step 6: Test Data Persistence\n    if (paperId) {\n      const persistenceResult = await this.testDataPersistence(paperId);\n      steps.push({ name: 'Data Persistence', result: persistenceResult });\n    }\n\n    const totalDuration = Date.now() - startTime;\n    const overallSuccess = steps.every(step => step.result.success);\n\n    return {\n      testName: 'Complete Workflow Test',\n      steps,\n      overallSuccess,\n      totalDuration,\n    };\n  }\n\n  async testPDFUpload(): Promise<TestResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Create a test PDF file (mock)\n      const testPDFContent = this.createTestPDFBuffer();\n      \n      const formData = new FormData();\n      const blob = new Blob([testPDFContent], { type: 'application/pdf' });\n      formData.append('file', blob, 'test-paper.pdf');\n\n      const response = await fetch(`${this.baseUrl}/api/upload`, {\n        method: 'POST',\n        body: formData,\n      });\n\n      const result = await response.json();\n      const duration = Date.now() - startTime;\n\n      if (response.ok && result.success) {\n        return {\n          success: true,\n          message: 'PDF upload successful',\n          details: { paperId: result.data?.id },\n          duration,\n        };\n      } else {\n        return {\n          success: false,\n          message: `Upload failed: ${result.error || 'Unknown error'}`,\n          duration,\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: `Upload error: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: Date.now() - startTime,\n      };\n    }\n  }\n\n  async testPDFDisplay(paperId: string): Promise<TestResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Test if PDF file exists and is accessible\n      const response = await fetch(`${this.baseUrl}/uploads/${paperId}.pdf`);\n      const duration = Date.now() - startTime;\n\n      if (response.ok) {\n        return {\n          success: true,\n          message: 'PDF display test successful',\n          duration,\n        };\n      } else {\n        return {\n          success: false,\n          message: `PDF not accessible: ${response.status}`,\n          duration,\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: `PDF display error: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: Date.now() - startTime,\n      };\n    }\n  }\n\n  async testAIAnalysis(paperId: string): Promise<TestResult> {\n    const startTime = Date.now();\n    \n    try {\n      const response = await fetch(`${this.baseUrl}/api/analysis`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          paperId,\n          analysisType: 'comprehensive',\n        }),\n      });\n\n      const result = await response.json();\n      const duration = Date.now() - startTime;\n\n      if (response.ok && result.success && result.data) {\n        const analysis = result.data;\n        const hasRequiredFields = analysis.summary && \n                                 analysis.keyFindings && \n                                 analysis.mainContributions;\n\n        if (hasRequiredFields) {\n          return {\n            success: true,\n            message: 'AI analysis successful',\n            details: { analysisLength: analysis.summary.length },\n            duration,\n          };\n        } else {\n          return {\n            success: false,\n            message: 'AI analysis incomplete - missing required fields',\n            duration,\n          };\n        }\n      } else {\n        return {\n          success: false,\n          message: `AI analysis failed: ${result.error || 'Unknown error'}`,\n          duration,\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: `AI analysis error: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: Date.now() - startTime,\n      };\n    }\n  }\n\n  async testAnnotationSystem(paperId: string): Promise<TestResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Test creating an annotation\n      const testAnnotation: Partial<Annotation> = {\n        paperId,\n        type: 'highlight',\n        content: 'Test highlight annotation',\n        pageNumber: 1,\n        selection: {\n          startOffset: 0,\n          endOffset: 10,\n          selectedText: 'Test text',\n          pageNumber: 1,\n        },\n      };\n\n      const createResponse = await fetch(`${this.baseUrl}/api/annotations`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(testAnnotation),\n      });\n\n      const createResult = await createResponse.json();\n\n      if (!createResponse.ok || !createResult.success) {\n        return {\n          success: false,\n          message: `Annotation creation failed: ${createResult.error || 'Unknown error'}`,\n          duration: Date.now() - startTime,\n        };\n      }\n\n      // Test retrieving annotations\n      const getResponse = await fetch(`${this.baseUrl}/api/annotations?paperId=${paperId}`);\n      const getResult = await getResponse.json();\n\n      const duration = Date.now() - startTime;\n\n      if (getResponse.ok && getResult.success && Array.isArray(getResult.data)) {\n        return {\n          success: true,\n          message: 'Annotation system test successful',\n          details: { annotationCount: getResult.data.length },\n          duration,\n        };\n      } else {\n        return {\n          success: false,\n          message: `Annotation retrieval failed: ${getResult.error || 'Unknown error'}`,\n          duration,\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: `Annotation system error: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: Date.now() - startTime,\n      };\n    }\n  }\n\n  async testQASystem(paperId: string): Promise<TestResult> {\n    const startTime = Date.now();\n    \n    try {\n      const testQuestion = 'What is the main contribution of this paper?';\n      \n      const response = await fetch(`${this.baseUrl}/api/chat`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          paperId,\n          message: testQuestion,\n        }),\n      });\n\n      const result = await response.json();\n      const duration = Date.now() - startTime;\n\n      if (response.ok && result.success && result.data) {\n        return {\n          success: true,\n          message: 'Q&A system test successful',\n          details: { answerLength: result.data.length },\n          duration,\n        };\n      } else {\n        return {\n          success: false,\n          message: `Q&A system failed: ${result.error || 'Unknown error'}`,\n          duration,\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: `Q&A system error: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: Date.now() - startTime,\n      };\n    }\n  }\n\n  async testDataPersistence(paperId: string): Promise<TestResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Test that annotations persist across requests\n      const response1 = await fetch(`${this.baseUrl}/api/annotations?paperId=${paperId}`);\n      const result1 = await response1.json();\n\n      if (!response1.ok || !result1.success) {\n        return {\n          success: false,\n          message: 'Data persistence test failed - cannot retrieve annotations',\n          duration: Date.now() - startTime,\n        };\n      }\n\n      const initialCount = result1.data.length;\n\n      // Wait a moment and check again\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      const response2 = await fetch(`${this.baseUrl}/api/annotations?paperId=${paperId}`);\n      const result2 = await response2.json();\n\n      const duration = Date.now() - startTime;\n\n      if (response2.ok && result2.success && result2.data.length === initialCount) {\n        return {\n          success: true,\n          message: 'Data persistence test successful',\n          details: { persistedAnnotations: initialCount },\n          duration,\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Data persistence test failed - annotation count mismatch',\n          duration,\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        message: `Data persistence error: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: Date.now() - startTime,\n      };\n    }\n  }\n\n  private createTestPDFBuffer(): ArrayBuffer {\n    // Create a minimal PDF structure for testing\n    const pdfContent = `%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n\n4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n72 720 Td\n(Test PDF Document) Tj\nET\nendstream\nendobj\n\nxref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000189 00000 n \ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n284\n%%EOF`;\n\n    return new TextEncoder().encode(pdfContent).buffer;\n  }\n}\n\nexport const endToEndTester = new EndToEndTester();\n"], "names": [], "mappings": ";;;;;;AAoBO,MAAM;IAOX,MAAM,uBAAoD;YAS5B;QAR5B,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,QAAqD,EAAE;QAE7D,0BAA0B;QAC1B,MAAM,eAAe,MAAM,IAAI,CAAC,aAAa;QAC7C,MAAM,IAAI,CAAC;YAAE,MAAM;YAAc,QAAQ;QAAa;QAEtD,IAAI,UAAyB;QAC7B,IAAI,aAAa,OAAO,MAAI,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,OAAO,GAAE;YACzD,UAAU,aAAa,OAAO,CAAC,OAAO;QACxC;QAEA,2BAA2B;QAC3B,IAAI,SAAS;YACX,MAAM,gBAAgB,MAAM,IAAI,CAAC,cAAc,CAAC;YAChD,MAAM,IAAI,CAAC;gBAAE,MAAM;gBAAe,QAAQ;YAAc;QAC1D;QAEA,2BAA2B;QAC3B,IAAI,SAAS;YACX,MAAM,iBAAiB,MAAM,IAAI,CAAC,cAAc,CAAC;YACjD,MAAM,IAAI,CAAC;gBAAE,MAAM;gBAAe,QAAQ;YAAe;QAC3D;QAEA,iCAAiC;QACjC,IAAI,SAAS;YACX,MAAM,mBAAmB,MAAM,IAAI,CAAC,oBAAoB,CAAC;YACzD,MAAM,IAAI,CAAC;gBAAE,MAAM;gBAAqB,QAAQ;YAAiB;QACnE;QAEA,0BAA0B;QAC1B,IAAI,SAAS;YACX,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC;YACzC,MAAM,IAAI,CAAC;gBAAE,MAAM;gBAAc,QAAQ;YAAS;QACpD;QAEA,gCAAgC;QAChC,IAAI,SAAS;YACX,MAAM,oBAAoB,MAAM,IAAI,CAAC,mBAAmB,CAAC;YACzD,MAAM,IAAI,CAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAkB;QACnE;QAEA,MAAM,gBAAgB,KAAK,GAAG,KAAK;QACnC,MAAM,iBAAiB,MAAM,KAAK,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,OAAO;QAE9D,OAAO;YACL,UAAU;YACV;YACA;YACA;QACF;IACF;IAEA,MAAM,gBAAqC;QACzC,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,gCAAgC;YAChC,MAAM,iBAAiB,IAAI,CAAC,mBAAmB;YAE/C,MAAM,WAAW,IAAI;YACrB,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAe,EAAE;gBAAE,MAAM;YAAkB;YAClE,SAAS,MAAM,CAAC,QAAQ,MAAM;YAE9B,MAAM,WAAW,MAAM,MAAM,AAAC,GAAe,OAAb,IAAI,CAAC,OAAO,EAAC,gBAAc;gBACzD,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;oBAIX;gBAHtB,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,SAAS;wBAAE,OAAO,GAAE,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,EAAE;oBAAC;oBACpC;gBACF;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS,AAAC,kBAAiD,OAAhC,OAAO,KAAK,IAAI;oBAC3C;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS,AAAC,iBAAyE,OAAzD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACnE,UAAU,KAAK,GAAG,KAAK;YACzB;QACF;IACF;IAEA,MAAM,eAAe,OAAe,EAAuB;QACzD,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,4CAA4C;YAC5C,MAAM,WAAW,MAAM,MAAM,AAAC,GAA0B,OAAxB,IAAI,CAAC,OAAO,EAAC,aAAmB,OAAR,SAAQ;YAChE,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT;gBACF;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS,AAAC,uBAAsC,OAAhB,SAAS,MAAM;oBAC/C;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS,AAAC,sBAA8E,OAAzD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxE,UAAU,KAAK,GAAG,KAAK;YACzB;QACF;IACF;IAEA,MAAM,eAAe,OAAe,EAAuB;QACzD,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAAe,OAAb,IAAI,CAAC,OAAO,EAAC,kBAAgB;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,cAAc;gBAChB;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBAChD,MAAM,WAAW,OAAO,IAAI;gBAC5B,MAAM,oBAAoB,SAAS,OAAO,IACjB,SAAS,WAAW,IACpB,SAAS,iBAAiB;gBAEnD,IAAI,mBAAmB;oBACrB,OAAO;wBACL,SAAS;wBACT,SAAS;wBACT,SAAS;4BAAE,gBAAgB,SAAS,OAAO,CAAC,MAAM;wBAAC;wBACnD;oBACF;gBACF,OAAO;oBACL,OAAO;wBACL,SAAS;wBACT,SAAS;wBACT;oBACF;gBACF;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS,AAAC,uBAAsD,OAAhC,OAAO,KAAK,IAAI;oBAChD;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS,AAAC,sBAA8E,OAAzD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxE,UAAU,KAAK,GAAG,KAAK;YACzB;QACF;IACF;IAEA,MAAM,qBAAqB,OAAe,EAAuB;QAC/D,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,8BAA8B;YAC9B,MAAM,iBAAsC;gBAC1C;gBACA,MAAM;gBACN,SAAS;gBACT,YAAY;gBACZ,WAAW;oBACT,aAAa;oBACb,WAAW;oBACX,cAAc;oBACd,YAAY;gBACd;YACF;YAEA,MAAM,iBAAiB,MAAM,MAAM,AAAC,GAAe,OAAb,IAAI,CAAC,OAAO,EAAC,qBAAmB;gBACpE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,eAAe,MAAM,eAAe,IAAI;YAE9C,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,OAAO,EAAE;gBAC/C,OAAO;oBACL,SAAS;oBACT,SAAS,AAAC,+BAAoE,OAAtC,aAAa,KAAK,IAAI;oBAC9D,UAAU,KAAK,GAAG,KAAK;gBACzB;YACF;YAEA,8BAA8B;YAC9B,MAAM,cAAc,MAAM,MAAM,AAAC,GAA0C,OAAxC,IAAI,CAAC,OAAO,EAAC,6BAAmC,OAAR;YAC3E,MAAM,YAAY,MAAM,YAAY,IAAI;YAExC,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,IAAI,YAAY,EAAE,IAAI,UAAU,OAAO,IAAI,MAAM,OAAO,CAAC,UAAU,IAAI,GAAG;gBACxE,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,SAAS;wBAAE,iBAAiB,UAAU,IAAI,CAAC,MAAM;oBAAC;oBAClD;gBACF;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS,AAAC,gCAAkE,OAAnC,UAAU,KAAK,IAAI;oBAC5D;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS,AAAC,4BAAoF,OAAzD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9E,UAAU,KAAK,GAAG,KAAK;YACzB;QACF;IACF;IAEA,MAAM,aAAa,OAAe,EAAuB;QACvD,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,MAAM,eAAe;YAErB,MAAM,WAAW,MAAM,MAAM,AAAC,GAAe,OAAb,IAAI,CAAC,OAAO,EAAC,cAAY;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,SAAS;gBACX;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBAChD,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,SAAS;wBAAE,cAAc,OAAO,IAAI,CAAC,MAAM;oBAAC;oBAC5C;gBACF;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS,AAAC,sBAAqD,OAAhC,OAAO,KAAK,IAAI;oBAC/C;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS,AAAC,qBAA6E,OAAzD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACvE,UAAU,KAAK,GAAG,KAAK;YACzB;QACF;IACF;IAEA,MAAM,oBAAoB,OAAe,EAAuB;QAC9D,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,gDAAgD;YAChD,MAAM,YAAY,MAAM,MAAM,AAAC,GAA0C,OAAxC,IAAI,CAAC,OAAO,EAAC,6BAAmC,OAAR;YACzE,MAAM,UAAU,MAAM,UAAU,IAAI;YAEpC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,OAAO,EAAE;gBACrC,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,UAAU,KAAK,GAAG,KAAK;gBACzB;YACF;YAEA,MAAM,eAAe,QAAQ,IAAI,CAAC,MAAM;YAExC,gCAAgC;YAChC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,MAAM,MAAM,AAAC,GAA0C,OAAxC,IAAI,CAAC,OAAO,EAAC,6BAAmC,OAAR;YACzE,MAAM,UAAU,MAAM,UAAU,IAAI;YAEpC,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,IAAI,UAAU,EAAE,IAAI,QAAQ,OAAO,IAAI,QAAQ,IAAI,CAAC,MAAM,KAAK,cAAc;gBAC3E,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,SAAS;wBAAE,sBAAsB;oBAAa;oBAC9C;gBACF;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS,AAAC,2BAAmF,OAAzD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC7E,UAAU,KAAK,GAAG,KAAK;YACzB;QACF;IACF;IAEQ,sBAAmC;QACzC,6CAA6C;QAC7C,MAAM,aAAc;QAsDpB,OAAO,IAAI,cAAc,MAAM,CAAC,YAAY,MAAM;IACpD;IA9YA,YAAY,UAAkB,uBAAuB,CAAE;QAFvD,+KAAQ,WAAR,KAAA;QAGE,IAAI,CAAC,OAAO,GAAG;IACjB;AA6YF;AAEO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/app/test/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Card, Button, Typography, List, Tag, Progress, Space, Alert, Divider } from 'antd';\nimport { PlayCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons';\nimport { endToEndTester, WorkflowTestResult, TestResult } from '@/lib/test-utils';\n\nconst { Title, Text, Paragraph } = Typography;\n\nexport default function TestPage() {\n  const [isRunning, setIsRunning] = useState(false);\n  const [testResults, setTestResults] = useState<WorkflowTestResult | null>(null);\n  const [currentStep, setCurrentStep] = useState<string>('');\n\n  const runTests = async () => {\n    setIsRunning(true);\n    setTestResults(null);\n    setCurrentStep('Starting tests...');\n\n    try {\n      const results = await endToEndTester.testCompleteWorkflow();\n      setTestResults(results);\n    } catch (error) {\n      console.error('Test execution failed:', error);\n    } finally {\n      setIsRunning(false);\n      setCurrentStep('');\n    }\n  };\n\n  const getStatusIcon = (result: TestResult) => {\n    if (result.success) {\n      return <CheckCircleOutlined className=\"text-green-500\" />;\n    } else {\n      return <CloseCircleOutlined className=\"text-red-500\" />;\n    }\n  };\n\n  const getStatusColor = (success: boolean) => {\n    return success ? 'success' : 'error';\n  };\n\n  const formatDuration = (duration?: number) => {\n    if (!duration) return 'N/A';\n    return `${duration}ms`;\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6\">\n      <Card>\n        <div className=\"text-center mb-6\">\n          <Title level={2}>DocuMancer End-to-End Testing</Title>\n          <Paragraph>\n            This page tests the complete workflow: PDF upload → display → AI analysis → annotations → Q&A\n          </Paragraph>\n        </div>\n\n        <div className=\"mb-6\">\n          <Button\n            type=\"primary\"\n            size=\"large\"\n            icon={isRunning ? <LoadingOutlined /> : <PlayCircleOutlined />}\n            onClick={runTests}\n            disabled={isRunning}\n            block\n          >\n            {isRunning ? 'Running Tests...' : 'Run Complete Workflow Test'}\n          </Button>\n          \n          {currentStep && (\n            <div className=\"mt-3 text-center\">\n              <Text type=\"secondary\">{currentStep}</Text>\n            </div>\n          )}\n        </div>\n\n        {testResults && (\n          <div className=\"space-y-6\">\n            <Alert\n              message={`Test ${testResults.overallSuccess ? 'Passed' : 'Failed'}`}\n              description={`Total duration: ${formatDuration(testResults.totalDuration)}`}\n              type={testResults.overallSuccess ? 'success' : 'error'}\n              showIcon\n            />\n\n            <Card title=\"Test Results\" size=\"small\">\n              <List\n                dataSource={testResults.steps}\n                renderItem={(step) => (\n                  <List.Item>\n                    <div className=\"w-full\">\n                      <div className=\"flex justify-between items-center mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          {getStatusIcon(step.result)}\n                          <Text strong>{step.name}</Text>\n                          <Tag color={getStatusColor(step.result.success)}>\n                            {step.result.success ? 'PASS' : 'FAIL'}\n                          </Tag>\n                        </div>\n                        <Text type=\"secondary\" className=\"text-sm\">\n                          {formatDuration(step.result.duration)}\n                        </Text>\n                      </div>\n                      \n                      <div className=\"ml-6\">\n                        <Text className={step.result.success ? 'text-green-600' : 'text-red-600'}>\n                          {step.result.message}\n                        </Text>\n                        \n                        {step.result.details && (\n                          <div className=\"mt-1\">\n                            <Text type=\"secondary\" className=\"text-xs\">\n                              Details: {JSON.stringify(step.result.details)}\n                            </Text>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </List.Item>\n                )}\n              />\n            </Card>\n\n            <Card title=\"Test Coverage\" size=\"small\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Title level={5}>Core Features Tested</Title>\n                  <List size=\"small\">\n                    <List.Item>✅ PDF File Upload</List.Item>\n                    <List.Item>✅ PDF Display & Rendering</List.Item>\n                    <List.Item>✅ AI-Powered Analysis</List.Item>\n                    <List.Item>✅ Annotation System</List.Item>\n                    <List.Item>✅ Interactive Q&A</List.Item>\n                    <List.Item>✅ Data Persistence</List.Item>\n                  </List>\n                </div>\n                \n                <div>\n                  <Title level={5}>Technical Validations</Title>\n                  <List size=\"small\">\n                    <List.Item>✅ API Endpoints</List.Item>\n                    <List.Item>✅ File Processing</List.Item>\n                    <List.Item>✅ Database Operations</List.Item>\n                    <List.Item>✅ Error Handling</List.Item>\n                    <List.Item>✅ Response Times</List.Item>\n                    <List.Item>✅ Data Integrity</List.Item>\n                  </List>\n                </div>\n              </div>\n            </Card>\n\n            <Card title=\"Performance Metrics\" size=\"small\">\n              <div className=\"space-y-3\">\n                {testResults.steps.map((step, index) => (\n                  <div key={index}>\n                    <div className=\"flex justify-between items-center mb-1\">\n                      <Text>{step.name}</Text>\n                      <Text type=\"secondary\">{formatDuration(step.result.duration)}</Text>\n                    </div>\n                    <Progress\n                      percent={Math.min((step.result.duration || 0) / 100, 100)}\n                      size=\"small\"\n                      status={step.result.success ? 'success' : 'exception'}\n                      showInfo={false}\n                    />\n                  </div>\n                ))}\n              </div>\n            </Card>\n          </div>\n        )}\n\n        <Divider />\n\n        <Card title=\"Manual Testing Checklist\" size=\"small\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <Title level={5}>UI/UX Testing</Title>\n              <List size=\"small\">\n                <List.Item>□ Responsive design on mobile</List.Item>\n                <List.Item>□ Drag & drop file upload</List.Item>\n                <List.Item>□ PDF navigation controls</List.Item>\n                <List.Item>□ Text selection & highlighting</List.Item>\n                <List.Item>□ Annotation toolbar positioning</List.Item>\n                <List.Item>□ Mobile drawer functionality</List.Item>\n              </List>\n            </div>\n            \n            <div>\n              <Title level={5}>Feature Testing</Title>\n              <List size=\"small\">\n                <List.Item>□ Multiple PDF formats</List.Item>\n                <List.Item>□ Large file handling</List.Item>\n                <List.Item>□ Complex document analysis</List.Item>\n                <List.Item>□ Annotation persistence</List.Item>\n                <List.Item>□ Export/import functionality</List.Item>\n                <List.Item>□ Error recovery</List.Item>\n              </List>\n            </div>\n          </div>\n        </Card>\n\n        <Card title=\"Browser Compatibility\" size=\"small\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n            <div>\n              <Text strong>Chrome</Text>\n              <div className=\"text-green-500\">✅ Supported</div>\n            </div>\n            <div>\n              <Text strong>Firefox</Text>\n              <div className=\"text-green-500\">✅ Supported</div>\n            </div>\n            <div>\n              <Text strong>Safari</Text>\n              <div className=\"text-yellow-500\">⚠️ Limited</div>\n            </div>\n            <div>\n              <Text strong>Edge</Text>\n              <div className=\"text-green-500\">✅ Supported</div>\n            </div>\n          </div>\n        </Card>\n\n        <Alert\n          message=\"Testing Notes\"\n          description={\n            <div>\n              <p>• Automated tests verify core functionality and API endpoints</p>\n              <p>• Manual testing is recommended for UI/UX validation</p>\n              <p>• Performance may vary based on PDF size and complexity</p>\n              <p>• Some features require a valid DeepSeek API key</p>\n            </div>\n          }\n          type=\"info\"\n          showIcon\n        />\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAE9B,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,MAAM,WAAW;QACf,aAAa;QACb,eAAe;QACf,eAAe;QAEf,IAAI;YACF,MAAM,UAAU,MAAM,8HAAA,CAAA,iBAAc,CAAC,oBAAoB;YACzD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;YACb,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,OAAO,OAAO,EAAE;YAClB,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;gBAAC,WAAU;;;;;;QACxC,OAAO;YACL,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;gBAAC,WAAU;;;;;;QACxC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,UAAU,YAAY;IAC/B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,UAAU,OAAO;QACtB,OAAO,AAAC,GAAW,OAAT,UAAS;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;;8BACH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,OAAO;sCAAG;;;;;;sCACjB,6LAAC;sCAAU;;;;;;;;;;;;8BAKb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,MAAM,0BAAY,6LAAC,2NAAA,CAAA,kBAAe;;;;uDAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;4BAC3D,SAAS;4BACT,UAAU;4BACV,KAAK;sCAEJ,YAAY,qBAAqB;;;;;;wBAGnC,6BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,MAAK;0CAAa;;;;;;;;;;;;;;;;;gBAK7B,6BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mLAAA,CAAA,QAAK;4BACJ,SAAS,AAAC,QAAwD,OAAjD,YAAY,cAAc,GAAG,WAAW;4BACzD,aAAa,AAAC,mBAA4D,OAA1C,eAAe,YAAY,aAAa;4BACxE,MAAM,YAAY,cAAc,GAAG,YAAY;4BAC/C,QAAQ;;;;;;sCAGV,6LAAC,iLAAA,CAAA,OAAI;4BAAC,OAAM;4BAAe,MAAK;sCAC9B,cAAA,6LAAC,iLAAA,CAAA,OAAI;gCACH,YAAY,YAAY,KAAK;gCAC7B,YAAY,CAAC,qBACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kDACR,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,KAAK,MAAM;8EAC1B,6LAAC;oEAAK,MAAM;8EAAE,KAAK,IAAI;;;;;;8EACvB,6LAAC,+KAAA,CAAA,MAAG;oEAAC,OAAO,eAAe,KAAK,MAAM,CAAC,OAAO;8EAC3C,KAAK,MAAM,CAAC,OAAO,GAAG,SAAS;;;;;;;;;;;;sEAGpC,6LAAC;4DAAK,MAAK;4DAAY,WAAU;sEAC9B,eAAe,KAAK,MAAM,CAAC,QAAQ;;;;;;;;;;;;8DAIxC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,KAAK,MAAM,CAAC,OAAO,GAAG,mBAAmB;sEACvD,KAAK,MAAM,CAAC,OAAO;;;;;;wDAGrB,KAAK,MAAM,CAAC,OAAO,kBAClB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,MAAK;gEAAY,WAAU;;oEAAU;oEAC/B,KAAK,SAAS,CAAC,KAAK,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAW9D,6LAAC,iLAAA,CAAA,OAAI;4BAAC,OAAM;4BAAgB,MAAK;sCAC/B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,OAAO;0DAAG;;;;;;0DACjB,6LAAC,iLAAA,CAAA,OAAI;gDAAC,MAAK;;kEACT,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;kEACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;kEACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;kEACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;kEACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;kEACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;;;;;;;;;;;;;kDAIf,6LAAC;;0DACC,6LAAC;gDAAM,OAAO;0DAAG;;;;;;0DACjB,6LAAC,iLAAA,CAAA,OAAI;gDAAC,MAAK;;kEACT,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;kEACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;kEACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;kEACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;kEACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;kEACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMnB,6LAAC,iLAAA,CAAA,OAAI;4BAAC,OAAM;4BAAsB,MAAK;sCACrC,cAAA,6LAAC;gCAAI,WAAU;0CACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,KAAK,IAAI;;;;;;kEAChB,6LAAC;wDAAK,MAAK;kEAAa,eAAe,KAAK,MAAM,CAAC,QAAQ;;;;;;;;;;;;0DAE7D,6LAAC,yLAAA,CAAA,WAAQ;gDACP,SAAS,KAAK,GAAG,CAAC,CAAC,KAAK,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,KAAK;gDACrD,MAAK;gDACL,QAAQ,KAAK,MAAM,CAAC,OAAO,GAAG,YAAY;gDAC1C,UAAU;;;;;;;uCATJ;;;;;;;;;;;;;;;;;;;;;8BAkBpB,6LAAC,uLAAA,CAAA,UAAO;;;;;8BAER,6LAAC,iLAAA,CAAA,OAAI;oBAAC,OAAM;oBAA2B,MAAK;8BAC1C,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,OAAO;kDAAG;;;;;;kDACjB,6LAAC,iLAAA,CAAA,OAAI;wCAAC,MAAK;;0DACT,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;0DACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;0DACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;0DACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;0DACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;0DACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;;;;;;;;;;;;;0CAIf,6LAAC;;kDACC,6LAAC;wCAAM,OAAO;kDAAG;;;;;;kDACjB,6LAAC,iLAAA,CAAA,OAAI;wCAAC,MAAK;;0DACT,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;0DACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;0DACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;0DACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;0DACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;0DACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMnB,6LAAC,iLAAA,CAAA,OAAI;oBAAC,OAAM;oBAAwB,MAAK;8BACvC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,6LAAC;wCAAI,WAAU;kDAAiB;;;;;;;;;;;;0CAElC,6LAAC;;kDACC,6LAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,6LAAC;wCAAI,WAAU;kDAAiB;;;;;;;;;;;;0CAElC,6LAAC;;kDACC,6LAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,6LAAC;wCAAI,WAAU;kDAAkB;;;;;;;;;;;;0CAEnC,6LAAC;;kDACC,6LAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,6LAAC;wCAAI,WAAU;kDAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAKtC,6LAAC,mLAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,2BACE,6LAAC;;0CACC,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;;;;;;;oBAGP,MAAK;oBACL,QAAQ;;;;;;;;;;;;;;;;;AAKlB;GAtOwB;KAAA", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40ant-design/icons-svg/es/asn/PlayCircleOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar PlayCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z\" } }] }, \"name\": \"play-circle\", \"theme\": \"outlined\" };\nexport default PlayCircleOutlined;\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C,IAAI,qBAAqB;IAAE,QAAQ;QAAE,OAAO;QAAO,SAAS;YAAE,WAAW;YAAiB,aAAa;QAAQ;QAAG,YAAY;YAAC;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAAgL;YAAE;YAAG;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAAuJ;YAAE;SAAE;IAAC;IAAG,QAAQ;IAAe,SAAS;AAAW;uCACjjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40ant-design/icons/es/icons/PlayCircleOutlined.js"], "sourcesContent": ["function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PlayCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/PlayCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nconst PlayCircleOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n  ref: ref,\n  icon: PlayCircleOutlinedSvg\n}));\n\n/**![play-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIC8+PHBhdGggZD0iTTcxOS40IDQ5OS4xbC0yOTYuMS0yMTVBMTUuOSAxNS45IDAgMDAzOTggMjk3djQzMGMwIDEzLjEgMTQuOCAyMC41IDI1LjMgMTIuOWwyOTYuMS0yMTVhMTUuOSAxNS45IDAgMDAwLTI1Ljh6bS0yNTcuNiAxMzRWMzkwLjlMNjI4LjUgNTEyIDQ2MS44IDYzMy4xeiIgLz48L3N2Zz4=) */\nconst RefIcon = /*#__PURE__*/React.forwardRef(PlayCircleOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PlayCircleOutlined';\n}\nexport default RefIcon;"], "names": [], "mappings": ";;;AAcI;AAbJ,oCAAoC;AACpC,2BAA2B;AAE3B;AACA;AACA;AANA,SAAS;IAAa,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK;IAAkO,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAAY;;;;AAOlV,MAAM,qBAAqB,CAAC,OAAO,MAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,UAAQ,EAAE,SAAS,CAAC,GAAG,OAAO;QACxG,KAAK;QACL,MAAM,qLAAA,CAAA,UAAqB;IAC7B;AAEA,sqBAAsqB,GACtqB,MAAM,UAAU,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC;AAC9C,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40ant-design/icons-svg/es/asn/CheckCircleOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar CheckCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\" } }] }, \"name\": \"check-circle\", \"theme\": \"outlined\" };\nexport default CheckCircleOutlined;\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C,IAAI,sBAAsB;IAAE,QAAQ;QAAE,OAAO;QAAO,SAAS;YAAE,WAAW;YAAiB,aAAa;QAAQ;QAAG,YAAY;YAAC;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAA6L;YAAE;YAAG;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAAgL;YAAE;SAAE;IAAC;IAAG,QAAQ;IAAgB,SAAS;AAAW;uCACzlB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1337, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40ant-design/icons/es/icons/CheckCircleOutlined.js"], "sourcesContent": ["function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CheckCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/CheckCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nconst CheckCircleOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n  ref: ref,\n  icon: CheckCircleOutlinedSvg\n}));\n\n/**![check-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5OSAzNTNoLTQ2LjljLTEwLjIgMC0xOS45IDQuOS0yNS45IDEzLjNMNDY5IDU4NC4zbC03MS4yLTk4LjhjLTYtOC4zLTE1LjYtMTMuMy0yNS45LTEzLjNIMzI1Yy02LjUgMC0xMC4zIDcuNC02LjUgMTIuN2wxMjQuNiAxNzIuOGEzMS44IDMxLjggMCAwMDUxLjcgMGwyMTAuNi0yOTJjMy45LTUuMy4xLTEyLjctNi40LTEyLjd6IiAvPjxwYXRoIGQ9Ik01MTIgNjRDMjY0LjYgNjQgNjQgMjY0LjYgNjQgNTEyczIwMC42IDQ0OCA0NDggNDQ4IDQ0OC0yMDAuNiA0NDgtNDQ4Uzc1OS40IDY0IDUxMiA2NHptMCA4MjBjLTIwNS40IDAtMzcyLTE2Ni42LTM3Mi0zNzJzMTY2LjYtMzcyIDM3Mi0zNzIgMzcyIDE2Ni42IDM3MiAzNzItMTY2LjYgMzcyLTM3MiAzNzJ6IiAvPjwvc3ZnPg==) */\nconst RefIcon = /*#__PURE__*/React.forwardRef(CheckCircleOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CheckCircleOutlined';\n}\nexport default RefIcon;"], "names": [], "mappings": ";;;AAcI;AAbJ,oCAAoC;AACpC,2BAA2B;AAE3B;AACA;AACA;AANA,SAAS;IAAa,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK;IAAkO,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAAY;;;;AAOlV,MAAM,sBAAsB,CAAC,OAAO,MAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,UAAQ,EAAE,SAAS,CAAC,GAAG,OAAO;QACzG,KAAK;QACL,MAAM,sLAAA,CAAA,UAAsB;IAC9B;AAEA,2tBAA2tB,GAC3tB,MAAM,UAAU,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC;AAC9C,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40ant-design/icons-svg/es/asn/CloseCircleOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar CloseCircleOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"fill-rule\": \"evenodd\", \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z\" } }] }, \"name\": \"close-circle\", \"theme\": \"outlined\" };\nexport default CloseCircleOutlined;\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C,IAAI,sBAAsB;IAAE,QAAQ;QAAE,OAAO;QAAO,SAAS;YAAE,aAAa;YAAW,WAAW;YAAiB,aAAa;QAAQ;QAAG,YAAY;YAAC;gBAAE,OAAO;gBAAQ,SAAS;oBAAE,KAAK;gBAAgyB;YAAE;SAAE;IAAC;IAAG,QAAQ;IAAgB,SAAS;AAAW;uCAC9/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40ant-design/icons/es/icons/CloseCircleOutlined.js"], "sourcesContent": ["function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CloseCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/CloseCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nconst CloseCircleOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n  ref: ref,\n  icon: CloseCircleOutlinedSvg\n}));\n\n/**![close-circle](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nconst RefIcon = /*#__PURE__*/React.forwardRef(CloseCircleOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CloseCircleOutlined';\n}\nexport default RefIcon;"], "names": [], "mappings": ";;;AAcI;AAbJ,oCAAoC;AACpC,2BAA2B;AAE3B;AACA;AACA;AANA,SAAS;IAAa,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK;IAAkO,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAAY;;;;AAOlV,MAAM,sBAAsB,CAAC,OAAO,MAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,UAAQ,EAAE,SAAS,CAAC,GAAG,OAAO;QACzG,KAAK;QACL,MAAM,sLAAA,CAAA,UAAsB;IAC9B;AAEA,2yCAA2yC,GAC3yC,MAAM,UAAU,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC;AAC9C,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1440, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40ant-design/icons/es/icons/LoadingOutlined.js"], "sourcesContent": ["function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport LoadingOutlinedSvg from \"@ant-design/icons-svg/es/asn/LoadingOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nconst LoadingOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n  ref: ref,\n  icon: LoadingOutlinedSvg\n}));\n\n/**![loading](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk4OCA1NDhjLTE5LjkgMC0zNi0xNi4xLTM2LTM2IDAtNTkuNC0xMS42LTExNy0zNC42LTE3MS4zYTQ0MC40NSA0NDAuNDUgMCAwMC05NC4zLTEzOS45IDQzNy43MSA0MzcuNzEgMCAwMC0xMzkuOS05NC4zQzYyOSA4My42IDU3MS40IDcyIDUxMiA3MmMtMTkuOSAwLTM2LTE2LjEtMzYtMzZzMTYuMS0zNiAzNi0zNmM2OS4xIDAgMTM2LjIgMTMuNSAxOTkuMyA0MC4zQzc3Mi4zIDY2IDgyNyAxMDMgODc0IDE1MGM0NyA0NyA4My45IDEwMS44IDEwOS43IDE2Mi43IDI2LjcgNjMuMSA0MC4yIDEzMC4yIDQwLjIgMTk5LjMuMSAxOS45LTE2IDM2LTM1LjkgMzZ6IiAvPjwvc3ZnPg==) */\nconst RefIcon = /*#__PURE__*/React.forwardRef(LoadingOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LoadingOutlined';\n}\nexport default RefIcon;"], "names": [], "mappings": ";;;AAcI;AAbJ,oCAAoC;AACpC,2BAA2B;AAE3B;AACA;AACA;AANA,SAAS;IAAa,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK;IAAkO,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAAY;;;;AAOlV,MAAM,kBAAkB,CAAC,OAAO,MAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,2KAAA,CAAA,UAAQ,EAAE,SAAS,CAAC,GAAG,OAAO;QACrG,KAAK;QACL,MAAM,kLAAA,CAAA,UAAkB;IAC1B;AAEA,0oBAA0oB,GAC1oB,MAAM,UAAU,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC;AAC9C,wCAA2C;IACzC,QAAQ,WAAW,GAAG;AACxB;uCACe", "ignoreList": [0], "debugId": null}}]}
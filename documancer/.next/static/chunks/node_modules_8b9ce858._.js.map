{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/make-event-props/dist/index.js"], "sourcesContent": ["// As defined on the list of supported events: https://reactjs.org/docs/events.html\nexport const clipboardEvents = ['onCopy', 'onCut', 'onPaste'];\nexport const compositionEvents = [\n    'onCompositionEnd',\n    'onCompositionStart',\n    'onCompositionUpdate',\n];\nexport const focusEvents = ['onFocus', 'onBlur'];\nexport const formEvents = ['onInput', 'onInvalid', 'onReset', 'onSubmit'];\nexport const imageEvents = ['onLoad', 'onError'];\nexport const keyboardEvents = ['onKeyDown', 'onKeyPress', 'onKeyUp'];\nexport const mediaEvents = [\n    'onAbort',\n    'onCanPlay',\n    'onCanPlayThrough',\n    'onDurationChange',\n    'onEmptied',\n    'onEncrypted',\n    'onEnded',\n    'onError',\n    'onLoadedData',\n    'onLoadedMetadata',\n    'onLoadStart',\n    'onPause',\n    'onPlay',\n    'onPlaying',\n    'onProgress',\n    'onRateChange',\n    'onSeeked',\n    'onSeeking',\n    'onStalled',\n    'onSuspend',\n    'onTimeUpdate',\n    'onVolumeChange',\n    'onWaiting',\n];\nexport const mouseEvents = [\n    'onClick',\n    'onContextMenu',\n    'onDoubleClick',\n    'onMouseDown',\n    'onMouseEnter',\n    'onMouseLeave',\n    'onMouseMove',\n    'onMouseOut',\n    'onMouseOver',\n    'onMouseUp',\n];\nexport const dragEvents = [\n    'onDrag',\n    'onDragEnd',\n    'onDragEnter',\n    'onDragExit',\n    'onDragLeave',\n    'onDragOver',\n    'onDragStart',\n    'onDrop',\n];\nexport const selectionEvents = ['onSelect'];\nexport const touchEvents = ['onTouchCancel', 'onTouchEnd', 'onTouchMove', 'onTouchStart'];\nexport const pointerEvents = [\n    'onPointerDown',\n    'onPointerMove',\n    'onPointerUp',\n    'onPointerCancel',\n    'onGotPointerCapture',\n    'onLostPointerCapture',\n    'onPointerEnter',\n    'onPointerLeave',\n    'onPointerOver',\n    'onPointerOut',\n];\nexport const uiEvents = ['onScroll'];\nexport const wheelEvents = ['onWheel'];\nexport const animationEvents = [\n    'onAnimationStart',\n    'onAnimationEnd',\n    'onAnimationIteration',\n];\nexport const transitionEvents = ['onTransitionEnd'];\nexport const otherEvents = ['onToggle'];\nexport const changeEvents = ['onChange'];\nexport const allEvents = [\n    ...clipboardEvents,\n    ...compositionEvents,\n    ...focusEvents,\n    ...formEvents,\n    ...imageEvents,\n    ...keyboardEvents,\n    ...mediaEvents,\n    ...mouseEvents,\n    ...dragEvents,\n    ...selectionEvents,\n    ...touchEvents,\n    ...pointerEvents,\n    ...uiEvents,\n    ...wheelEvents,\n    ...animationEvents,\n    ...transitionEvents,\n    ...changeEvents,\n    ...otherEvents,\n];\n/**\n * Returns an object with on-event callback props curried with provided args.\n *\n * @template ArgsType Type of arguments to curry on-event callbacks with.\n * @param {PropsType} props Props passed to a component.\n * @param {GetArgs<ArgsType>} [getArgs] A function that returns argument(s) on-event callbacks\n *   shall be curried with.\n */\nexport default function makeEventProps(props, getArgs) {\n    const eventProps = {};\n    for (const eventName of allEvents) {\n        const eventHandler = props[eventName];\n        if (!eventHandler) {\n            continue;\n        }\n        if (getArgs) {\n            eventProps[eventName] = ((event) => eventHandler(event, getArgs(eventName)));\n        }\n        else {\n            eventProps[eventName] = eventHandler;\n        }\n    }\n    return eventProps;\n}\n"], "names": [], "mappings": "AAAA,mFAAmF;;;;;;;;;;;;;;;;;;;;;;;AAC5E,MAAM,kBAAkB;IAAC;IAAU;IAAS;CAAU;AACtD,MAAM,oBAAoB;IAC7B;IACA;IACA;CACH;AACM,MAAM,cAAc;IAAC;IAAW;CAAS;AACzC,MAAM,aAAa;IAAC;IAAW;IAAa;IAAW;CAAW;AAClE,MAAM,cAAc;IAAC;IAAU;CAAU;AACzC,MAAM,iBAAiB;IAAC;IAAa;IAAc;CAAU;AAC7D,MAAM,cAAc;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,MAAM,cAAc;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,MAAM,aAAa;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,MAAM,kBAAkB;IAAC;CAAW;AACpC,MAAM,cAAc;IAAC;IAAiB;IAAc;IAAe;CAAe;AAClF,MAAM,gBAAgB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,MAAM,WAAW;IAAC;CAAW;AAC7B,MAAM,cAAc;IAAC;CAAU;AAC/B,MAAM,kBAAkB;IAC3B;IACA;IACA;CACH;AACM,MAAM,mBAAmB;IAAC;CAAkB;AAC5C,MAAM,cAAc;IAAC;CAAW;AAChC,MAAM,eAAe;IAAC;CAAW;AACjC,MAAM,YAAY;OAClB;OACA;OACA;OACA;OACA;OACA;OACA;OACA;OACA;OACA;OACA;OACA;OACA;OACA;OACA;OACA;OACA;OACA;CACN;AASc,SAAS,eAAe,KAAK,EAAE,OAAO;IACjD,MAAM,aAAa,CAAC;IACpB,KAAK,MAAM,aAAa,UAAW;QAC/B,MAAM,eAAe,KAAK,CAAC,UAAU;QACrC,IAAI,CAAC,cAAc;YACf;QACJ;QACA,IAAI,SAAS;YACT,UAAU,CAAC,UAAU,GAAI,CAAC,QAAU,aAAa,OAAO,QAAQ;QACpE,OACK;YACD,UAAU,CAAC,UAAU,GAAG;QAC5B;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/make-cancellable-promise/dist/index.js"], "sourcesContent": ["export default function makeCancellablePromise(promise) {\n    let isCancelled = false;\n    const wrappedPromise = new Promise((resolve, reject) => {\n        promise\n            .then((value) => !isCancelled && resolve(value))\n            .catch((error) => !isCancelled && reject(error));\n    });\n    return {\n        promise: wrappedPromise,\n        cancel() {\n            isCancelled = true;\n        },\n    };\n}\n"], "names": [], "mappings": ";;;AAAe,SAAS,uBAAuB,OAAO;IAClD,IAAI,cAAc;IAClB,MAAM,iBAAiB,IAAI,QAAQ,CAAC,SAAS;QACzC,QACK,IAAI,CAAC,CAAC,QAAU,CAAC,eAAe,QAAQ,QACxC,KAAK,CAAC,CAAC,QAAU,CAAC,eAAe,OAAO;IACjD;IACA,OAAO;QACH,SAAS;QACT;YACI,cAAc;QAClB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/tiny-invariant/dist/esm/tiny-invariant.js"], "sourcesContent": ["var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n"], "names": [], "mappings": ";;;AAAmB;AAAnB,IAAI,eAAe,oDAAyB;AAC5C,IAAI,SAAS;AACb,SAAS,UAAU,SAAS,EAAE,OAAO;IACjC,IAAI,WAAW;QACX;IACJ;IACA;;IAGA,IAAI,WAAW,OAAO,YAAY,aAAa,YAAY;IAC3D,IAAI,QAAQ,WAAW,GAAG,MAAM,CAAC,QAAQ,MAAM,MAAM,CAAC,YAAY;IAClE,MAAM,IAAI,MAAM;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/warning/warning.js"], "sourcesContent": ["/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\n\nvar __DEV__ = process.env.NODE_ENV !== 'production';\n\nvar warning = function() {};\n\nif (__DEV__) {\n  var printWarning = function printWarning(format, args) {\n    var len = arguments.length;\n    args = new Array(len > 1 ? len - 1 : 0);\n    for (var key = 1; key < len; key++) {\n      args[key - 1] = arguments[key];\n    }\n    var argIndex = 0;\n    var message = 'Warning: ' +\n      format.replace(/%s/g, function() {\n        return args[argIndex++];\n      });\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  }\n\n  warning = function(condition, format, args) {\n    var len = arguments.length;\n    args = new Array(len > 2 ? len - 2 : 0);\n    for (var key = 2; key < len; key++) {\n      args[key - 2] = arguments[key];\n    }\n    if (format === undefined) {\n      throw new Error(\n          '`warning(condition, format, ...args)` requires a warning ' +\n          'message argument'\n      );\n    }\n    if (!condition) {\n      printWarning.apply(null, [format].concat(args));\n    }\n  };\n}\n\nmodule.exports = warning;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAWa;AATd;AAEA;;;;;CAKC,GAED,IAAI,UAAU,oDAAyB;AAEvC,IAAI,UAAU,YAAY;AAE1B,wCAAa;IACX,IAAI,eAAe,SAAS,aAAa,MAAM,EAAE,IAAI;QACnD,IAAI,MAAM,UAAU,MAAM;QAC1B,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI;QACrC,IAAK,IAAI,MAAM,GAAG,MAAM,KAAK,MAAO;YAClC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI;QAChC;QACA,IAAI,WAAW;QACf,IAAI,UAAU,cACZ,OAAO,OAAO,CAAC,OAAO;YACpB,OAAO,IAAI,CAAC,WAAW;QACzB;QACF,IAAI,OAAO,YAAY,aAAa;YAClC,QAAQ,KAAK,CAAC;QAChB;QACA,IAAI;YACF,qCAAqC;YACrC,wEAAwE;YACxE,yDAAyD;YACzD,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,GAAG,CAAC;IACf;IAEA,UAAU,SAAS,SAAS,EAAE,MAAM,EAAE,IAAI;QACxC,IAAI,MAAM,UAAU,MAAM;QAC1B,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI;QACrC,IAAK,IAAI,MAAM,GAAG,MAAM,KAAK,MAAO;YAClC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI;QAChC;QACA,IAAI,WAAW,WAAW;YACxB,MAAM,IAAI,MACN,8DACA;QAEN;QACA,IAAI,CAAC,WAAW;YACd,aAAa,KAAK,CAAC,MAAM;gBAAC;aAAO,CAAC,MAAM,CAAC;QAC3C;IACF;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_apply_descriptor_get.js"], "sourcesContent": ["function _class_apply_descriptor_get(receiver, descriptor) {\n    if (descriptor.get) return descriptor.get.call(receiver);\n\n    return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU;IACrD,IAAI,WAAW,GAAG,EAAE,OAAO,WAAW,GAAG,CAAC,IAAI,CAAC;IAE/C,OAAO,WAAW,KAAK;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_extract_field_descriptor.js"], "sourcesContent": ["function _class_extract_field_descriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n\n    return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gCAAgC,QAAQ,EAAE,UAAU,EAAE,MAAM;IACjE,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU,kBAAkB,SAAS;IAE9E,OAAO,WAAW,GAAG,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_field_get.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_get(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"get\");\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_private_field_get as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU;IAClD,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,OAAO,CAAA,GAAA,yKAAA,CAAA,IAA2B,AAAD,EAAE,UAAU;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_check_private_redeclaration.js"], "sourcesContent": ["function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,6BAA6B,GAAG,EAAE,iBAAiB;IACxD,IAAI,kBAAkB,GAAG,CAAC,MAAM;QAC5B,MAAM,IAAI,UAAU;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_field_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,0BAA0B,GAAG,EAAE,UAAU,EAAE,KAAK;IACrD,CAAA,GAAA,0KAAA,CAAA,IAA4B,AAAD,EAAE,KAAK;IAClC,WAAW,GAAG,CAAC,KAAK;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_apply_descriptor_set.js"], "sourcesContent": ["function _class_apply_descriptor_set(receiver, descriptor, value) {\n    if (descriptor.set) descriptor.set.call(receiver, value);\n    else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n        descriptor.value = value;\n    }\n}\nexport { _class_apply_descriptor_set as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU,EAAE,KAAK;IAC5D,IAAI,WAAW,GAAG,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;SAC7C;QACD,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QACA,WAAW,KAAK,GAAG;IACvB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_field_set.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_set(receiver, privateMap, value) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n    return value;\n}\nexport { _class_private_field_set as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU,EAAE,KAAK;IACzD,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,CAAA,GAAA,yKAAA,CAAA,IAA2B,AAAD,EAAE,UAAU,YAAY;IAClD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_apply_descriptor_update.js"], "sourcesContent": ["function _class_apply_descriptor_update(receiver, descriptor) {\n    if (descriptor.set) {\n        if (!descriptor.get) throw new TypeError(\"attempted to read set only private field\");\n\n        if (!(\"__destrWrapper\" in descriptor)) {\n            descriptor.__destrWrapper = {\n                set value(v) {\n                    descriptor.set.call(receiver, v);\n                },\n                get value() {\n                    return descriptor.get.call(receiver);\n                }\n            };\n        }\n\n        return descriptor.__destrWrapper;\n    } else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n\n        return descriptor;\n    }\n}\nexport { _class_apply_descriptor_update as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,+BAA+B,QAAQ,EAAE,UAAU;IACxD,IAAI,WAAW,GAAG,EAAE;QAChB,IAAI,CAAC,WAAW,GAAG,EAAE,MAAM,IAAI,UAAU;QAEzC,IAAI,CAAC,CAAC,oBAAoB,UAAU,GAAG;YACnC,WAAW,cAAc,GAAG;gBACxB,IAAI,OAAM,EAAG;oBACT,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;gBAClC;gBACA,IAAI,SAAQ;oBACR,OAAO,WAAW,GAAG,CAAC,IAAI,CAAC;gBAC/B;YACJ;QACJ;QAEA,OAAO,WAAW,cAAc;IACpC,OAAO;QACH,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QAEA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_field_update.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_update } from \"./_class_apply_descriptor_update.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_update(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"update\");\n    return _class_apply_descriptor_update(receiver, descriptor);\n}\nexport { _class_private_field_update as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,4BAA4B,QAAQ,EAAE,UAAU;IACrD,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,OAAO,CAAA,GAAA,4KAAA,CAAA,IAA8B,AAAD,EAAE,UAAU;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_method_get.js"], "sourcesContent": ["function _class_private_method_get(receiver, privateSet, fn) {\n    if (!privateSet.has(receiver)) throw new TypeError(\"attempted to get private field on non-instance\");\n\n    return fn;\n}\nexport { _class_private_method_get as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,0BAA0B,QAAQ,EAAE,UAAU,EAAE,EAAE;IACvD,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IAEnD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_method_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_method_init(obj, privateSet) {\n    _check_private_redeclaration(obj, privateSet);\n    privateSet.add(obj);\n}\nexport { _class_private_method_init as _ };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,2BAA2B,GAAG,EAAE,UAAU;IAC/C,CAAA,GAAA,0KAAA,CAAA,IAA4B,AAAD,EAAE,KAAK;IAClC,WAAW,GAAG,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_check_private_static_access.js"], "sourcesContent": ["function _class_check_private_static_access(receiver, classConstructor) {\n    if (receiver !== classConstructor) throw new TypeError(\"Private static access of wrong provenance\");\n}\nexport { _class_check_private_static_access as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,mCAAmC,QAAQ,EAAE,gBAAgB;IAClE,IAAI,aAAa,kBAAkB,MAAM,IAAI,UAAU;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_check_private_static_field_descriptor.js"], "sourcesContent": ["function _class_check_private_static_field_descriptor(descriptor, action) {\n    if (descriptor === undefined) {\n        throw new TypeError(\"attempted to \" + action + \" private static field before its declaration\");\n    }\n}\nexport { _class_check_private_static_field_descriptor as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,6CAA6C,UAAU,EAAE,MAAM;IACpE,IAAI,eAAe,WAAW;QAC1B,MAAM,IAAI,UAAU,kBAAkB,SAAS;IACnD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_static_private_field_spec_get.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_check_private_static_access } from \"./_class_check_private_static_access.js\";\nimport { _ as _class_check_private_static_field_descriptor } from \"./_class_check_private_static_field_descriptor.js\";\n\nfunction _class_static_private_field_spec_get(receiver, classConstructor, descriptor) {\n    _class_check_private_static_access(receiver, classConstructor);\n    _class_check_private_static_field_descriptor(descriptor, \"get\");\n\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_static_private_field_spec_get as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,qCAAqC,QAAQ,EAAE,gBAAgB,EAAE,UAAU;IAChF,CAAA,GAAA,gLAAA,CAAA,IAAkC,AAAD,EAAE,UAAU;IAC7C,CAAA,GAAA,0LAAA,CAAA,IAA4C,AAAD,EAAE,YAAY;IAEzD,OAAO,CAAA,GAAA,yKAAA,CAAA,IAA2B,AAAD,EAAE,UAAU;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_static_private_field_spec_set.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_check_private_static_access } from \"./_class_check_private_static_access.js\";\nimport { _ as _class_check_private_static_field_descriptor } from \"./_class_check_private_static_field_descriptor.js\";\n\nfunction _class_static_private_field_spec_set(receiver, classConstructor, descriptor, value) {\n    _class_check_private_static_access(receiver, classConstructor);\n    _class_check_private_static_field_descriptor(descriptor, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n\n    return value;\n}\nexport { _class_static_private_field_spec_set as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,qCAAqC,QAAQ,EAAE,gBAAgB,EAAE,UAAU,EAAE,KAAK;IACvF,CAAA,GAAA,gLAAA,CAAA,IAAkC,AAAD,EAAE,UAAU;IAC7C,CAAA,GAAA,0LAAA,CAAA,IAA4C,AAAD,EAAE,YAAY;IACzD,CAAA,GAAA,yKAAA,CAAA,IAA2B,AAAD,EAAE,UAAU,YAAY;IAElD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_static_private_field_update.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_update } from \"./_class_apply_descriptor_update.js\";\nimport { _ as _class_check_private_static_access } from \"./_class_check_private_static_access.js\";\nimport { _ as _class_check_private_static_field_descriptor } from \"./_class_check_private_static_field_descriptor.js\";\n\nfunction _class_static_private_field_update(receiver, classConstructor, descriptor) {\n    _class_check_private_static_access(receiver, classConstructor);\n    _class_check_private_static_field_descriptor(descriptor, \"update\");\n\n    return _class_apply_descriptor_update(receiver, descriptor);\n}\nexport { _class_static_private_field_update as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,mCAAmC,QAAQ,EAAE,gBAAgB,EAAE,UAAU;IAC9E,CAAA,GAAA,gLAAA,CAAA,IAAkC,AAAD,EAAE,UAAU;IAC7C,CAAA,GAAA,0LAAA,CAAA,IAA4C,AAAD,EAAE,YAAY;IAEzD,OAAO,CAAA,GAAA,4KAAA,CAAA,IAA8B,AAAD,EAAE,UAAU;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_apply_descriptor_destructure.js"], "sourcesContent": ["function _class_apply_descriptor_destructure(receiver, descriptor) {\n    if (descriptor.set) {\n        if (!(\"__destrObj\" in descriptor)) {\n            descriptor.__destrObj = {\n                set value(v) {\n                    descriptor.set.call(receiver, v);\n                }\n            };\n        }\n\n        return descriptor.__destrObj;\n    } else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n\n        return descriptor;\n    }\n}\nexport { _class_apply_descriptor_destructure as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,oCAAoC,QAAQ,EAAE,UAAU;IAC7D,IAAI,WAAW,GAAG,EAAE;QAChB,IAAI,CAAC,CAAC,gBAAgB,UAAU,GAAG;YAC/B,WAAW,UAAU,GAAG;gBACpB,IAAI,OAAM,EAAG;oBACT,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;gBAClC;YACJ;QACJ;QAEA,OAAO,WAAW,UAAU;IAChC,OAAO;QACH,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QAEA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_field_destructure.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_destructure } from \"./_class_apply_descriptor_destructure.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_destructure(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    return _class_apply_descriptor_destructure(receiver, descriptor);\n}\nexport { _class_private_field_destructure as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,iCAAiC,QAAQ,EAAE,UAAU;IAC1D,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAmC,AAAD,EAAE,UAAU;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_static_private_method_get.js"], "sourcesContent": ["import { _ as _class_check_private_static_access } from \"./_class_check_private_static_access.js\";\n\nfunction _class_static_private_method_get(receiver, classConstructor, method) {\n    _class_check_private_static_access(receiver, classConstructor);\n\n    return method;\n}\nexport { _class_static_private_method_get as _ };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,iCAAiC,QAAQ,EAAE,gBAAgB,EAAE,MAAM;IACxE,CAAA,GAAA,gLAAA,CAAA,IAAkC,AAAD,EAAE,UAAU;IAE7C,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/merge-refs/dist/index.js"], "sourcesContent": ["/**\n * A function that merges React refs into one.\n * Supports both functions and ref objects created using createRef() and useRef().\n *\n * Usage:\n * ```tsx\n * <div ref={mergeRefs(ref1, ref2, ref3)} />\n * ```\n *\n * @param {(React.Ref<T> | undefined)[]} inputRefs Array of refs\n * @returns {React.Ref<T> | React.RefCallback<T>} Merged refs\n */\nexport default function mergeRefs() {\n    var inputRefs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        inputRefs[_i] = arguments[_i];\n    }\n    var filteredInputRefs = inputRefs.filter(Boolean);\n    if (filteredInputRefs.length <= 1) {\n        var firstRef = filteredInputRefs[0];\n        return firstRef || null;\n    }\n    return function mergedRefs(ref) {\n        for (var _i = 0, filteredInputRefs_1 = filteredInputRefs; _i < filteredInputRefs_1.length; _i++) {\n            var inputRef = filteredInputRefs_1[_i];\n            if (typeof inputRef === 'function') {\n                inputRef(ref);\n            }\n            else if (inputRef) {\n                inputRef.current = ref;\n            }\n        }\n    };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;AACc,SAAS;IACpB,IAAI,YAAY,EAAE;IAClB,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;QAC1C,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;IACjC;IACA,IAAI,oBAAoB,UAAU,MAAM,CAAC;IACzC,IAAI,kBAAkB,MAAM,IAAI,GAAG;QAC/B,IAAI,WAAW,iBAAiB,CAAC,EAAE;QACnC,OAAO,YAAY;IACvB;IACA,OAAO,SAAS,WAAW,GAAG;QAC1B,IAAK,IAAI,KAAK,GAAG,sBAAsB,mBAAmB,KAAK,oBAAoB,MAAM,EAAE,KAAM;YAC7F,IAAI,WAAW,mBAAmB,CAAC,GAAG;YACtC,IAAI,OAAO,aAAa,YAAY;gBAChC,SAAS;YACb,OACK,IAAI,UAAU;gBACf,SAAS,OAAO,GAAG;YACvB;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}]}
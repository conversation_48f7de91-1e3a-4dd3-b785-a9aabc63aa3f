(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/react-pdf/dist/DocumentContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
const documentContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const __TURBOPACK__default__export__ = documentContext;
}),
"[project]/node_modules/react-pdf/dist/Message.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>Message
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
;
function Message(param) {
    let { children, type } = param;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: "react-pdf__message react-pdf__message--".concat(type),
        children: children
    });
}
}),
"[project]/node_modules/react-pdf/dist/LinkService.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/* Copyright 2015 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>LinkService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiny-invariant/dist/esm/tiny-invariant.js [app-client] (ecmascript)");
;
const DEFAULT_LINK_REL = 'noopener noreferrer nofollow';
class LinkService {
    setDocument(pdfDocument) {
        this.pdfDocument = pdfDocument;
    }
    setViewer(pdfViewer) {
        this.pdfViewer = pdfViewer;
    }
    setExternalLinkRel(externalLinkRel) {
        this.externalLinkRel = externalLinkRel;
    }
    setExternalLinkTarget(externalLinkTarget) {
        this.externalLinkTarget = externalLinkTarget;
    }
    setHistory() {
    // Intentionally empty
    }
    get pagesCount() {
        return this.pdfDocument ? this.pdfDocument.numPages : 0;
    }
    get page() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.pdfViewer, 'PDF viewer is not initialized.');
        return this.pdfViewer.currentPageNumber || 0;
    }
    set page(value) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.pdfViewer, 'PDF viewer is not initialized.');
        this.pdfViewer.currentPageNumber = value;
    }
    get rotation() {
        return 0;
    }
    set rotation(_value) {
    // Intentionally empty
    }
    goToDestination(dest) {
        return new Promise((resolve)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.pdfDocument, 'PDF document not loaded.');
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(dest, 'Destination is not specified.');
            if (typeof dest === 'string') {
                this.pdfDocument.getDestination(dest).then(resolve);
            } else if (Array.isArray(dest)) {
                resolve(dest);
            } else {
                dest.then(resolve);
            }
        }).then((explicitDest)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Array.isArray(explicitDest), '"'.concat(explicitDest, '" is not a valid destination array.'));
            const destRef = explicitDest[0];
            new Promise((resolve)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.pdfDocument, 'PDF document not loaded.');
                if (destRef instanceof Object) {
                    this.pdfDocument.getPageIndex(destRef).then((pageIndex)=>{
                        resolve(pageIndex);
                    }).catch(()=>{
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, '"'.concat(destRef, '" is not a valid page reference.'));
                    });
                } else if (typeof destRef === 'number') {
                    resolve(destRef);
                } else {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, '"'.concat(destRef, '" is not a valid destination reference.'));
                }
            }).then((pageIndex)=>{
                const pageNumber = pageIndex + 1;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.pdfViewer, 'PDF viewer is not initialized.');
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pageNumber >= 1 && pageNumber <= this.pagesCount, '"'.concat(pageNumber, '" is not a valid page number.'));
                this.pdfViewer.scrollPageIntoView({
                    dest: explicitDest,
                    pageIndex,
                    pageNumber
                });
            });
        });
    }
    navigateTo(dest) {
        this.goToDestination(dest);
    }
    goToPage(pageNumber) {
        const pageIndex = pageNumber - 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.pdfViewer, 'PDF viewer is not initialized.');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pageNumber >= 1 && pageNumber <= this.pagesCount, '"'.concat(pageNumber, '" is not a valid page number.'));
        this.pdfViewer.scrollPageIntoView({
            pageIndex,
            pageNumber
        });
    }
    addLinkAttributes(link, url, newWindow) {
        link.href = url;
        link.rel = this.externalLinkRel || DEFAULT_LINK_REL;
        link.target = newWindow ? '_blank' : this.externalLinkTarget || '';
    }
    getDestinationHash() {
        return '#';
    }
    getAnchorUrl() {
        return '#';
    }
    setHash() {
    // Intentionally empty
    }
    executeNamedAction() {
    // Intentionally empty
    }
    cachePageRef() {
    // Intentionally empty
    }
    isPageVisible() {
        return true;
    }
    isPageCached() {
        return true;
    }
    executeSetOCGState() {
    // Intentionally empty
    }
    constructor(){
        this.externalLinkEnabled = true;
        this.externalLinkRel = undefined;
        this.externalLinkTarget = undefined;
        this.isInPresentationMode = false;
        this.pdfDocument = undefined;
        this.pdfViewer = undefined;
    }
}
;
}),
"[project]/node_modules/react-pdf/dist/PasswordResponses.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// As defined in https://github.com/mozilla/pdf.js/blob/d9fac3459609a807be6506fb3441b5da4b154d14/src/shared/util.js#L371-L374
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const PasswordResponses = {
    NEED_PASSWORD: 1,
    INCORRECT_PASSWORD: 2
};
const __TURBOPACK__default__export__ = PasswordResponses;
}),
"[project]/node_modules/react-pdf/dist/shared/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "cancelRunningTask": ()=>cancelRunningTask,
    "dataURItoByteString": ()=>dataURItoByteString,
    "displayCORSWarning": ()=>displayCORSWarning,
    "displayWorkerWarning": ()=>displayWorkerWarning,
    "getDevicePixelRatio": ()=>getDevicePixelRatio,
    "isArrayBuffer": ()=>isArrayBuffer,
    "isBlob": ()=>isBlob,
    "isBrowser": ()=>isBrowser,
    "isCancelException": ()=>isCancelException,
    "isDataURI": ()=>isDataURI,
    "isDefined": ()=>isDefined,
    "isLocalFileSystem": ()=>isLocalFileSystem,
    "isProvided": ()=>isProvided,
    "isString": ()=>isString,
    "loadFromFile": ()=>loadFromFile,
    "makePageCallback": ()=>makePageCallback
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiny-invariant/dist/esm/tiny-invariant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/warning/warning.js [app-client] (ecmascript)");
;
;
const isBrowser = typeof window !== 'undefined';
const isLocalFileSystem = isBrowser && window.location.protocol === 'file:';
function isDefined(variable) {
    return typeof variable !== 'undefined';
}
function isProvided(variable) {
    return isDefined(variable) && variable !== null;
}
function isString(variable) {
    return typeof variable === 'string';
}
function isArrayBuffer(variable) {
    return variable instanceof ArrayBuffer;
}
function isBlob(variable) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(isBrowser, 'isBlob can only be used in a browser environment');
    return variable instanceof Blob;
}
function isDataURI(variable) {
    return isString(variable) && /^data:/.test(variable);
}
function dataURItoByteString(dataURI) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(isDataURI(dataURI), 'Invalid data URI.');
    const [headersString = '', dataString = ''] = dataURI.split(',');
    const headers = headersString.split(';');
    if (headers.indexOf('base64') !== -1) {
        return atob(dataString);
    }
    return unescape(dataString);
}
function getDevicePixelRatio() {
    return isBrowser && window.devicePixelRatio || 1;
}
const allowFileAccessFromFilesTip = 'On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.';
function displayCORSWarning() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!isLocalFileSystem, "Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. ".concat(allowFileAccessFromFilesTip));
}
function displayWorkerWarning() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!isLocalFileSystem, "Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ".concat(allowFileAccessFromFilesTip));
}
function cancelRunningTask(runningTask) {
    if (runningTask === null || runningTask === void 0 ? void 0 : runningTask.cancel) runningTask.cancel();
}
function makePageCallback(page, scale) {
    Object.defineProperty(page, 'width', {
        get () {
            return this.view[2] * scale;
        },
        configurable: true
    });
    Object.defineProperty(page, 'height', {
        get () {
            return this.view[3] * scale;
        },
        configurable: true
    });
    Object.defineProperty(page, 'originalWidth', {
        get () {
            return this.view[2];
        },
        configurable: true
    });
    Object.defineProperty(page, 'originalHeight', {
        get () {
            return this.view[3];
        },
        configurable: true
    });
    return page;
}
function isCancelException(error) {
    return error.name === 'RenderingCancelledException';
}
function loadFromFile(file) {
    return new Promise((resolve, reject)=>{
        const reader = new FileReader();
        reader.onload = ()=>{
            if (!reader.result) {
                return reject(new Error('Error while reading a file.'));
            }
            resolve(reader.result);
        };
        reader.onerror = (event)=>{
            if (!event.target) {
                return reject(new Error('Error while reading a file.'));
            }
            const { error } = event.target;
            if (!error) {
                return reject(new Error('Error while reading a file.'));
            }
            switch(error.code){
                case error.NOT_FOUND_ERR:
                    return reject(new Error('Error while reading a file: File not found.'));
                case error.SECURITY_ERR:
                    return reject(new Error('Error while reading a file: Security error.'));
                case error.ABORT_ERR:
                    return reject(new Error('Error while reading a file: Aborted.'));
                default:
                    return reject(new Error('Error while reading a file.'));
            }
        };
        reader.readAsArrayBuffer(file);
    });
}
}),
"[project]/node_modules/react-pdf/dist/shared/hooks/useResolver.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>useResolver
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function reducer(state, action) {
    switch(action.type){
        case 'RESOLVE':
            return {
                value: action.value,
                error: undefined
            };
        case 'REJECT':
            return {
                value: false,
                error: action.error
            };
        case 'RESET':
            return {
                value: undefined,
                error: undefined
            };
        default:
            return state;
    }
}
function useResolver() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReducer"])(reducer, {
        value: undefined,
        error: undefined
    });
}
}),
"[project]/node_modules/react-pdf/dist/Document.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$event$2d$props$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/make-event-props/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$cancellable$2d$promise$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/make-cancellable-promise/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiny-invariant/dist/esm/tiny-invariant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/warning/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dequal$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dequal/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$node_modules$2f$pdfjs$2d$dist$2f$build$2f$pdf$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/node_modules/pdfjs-dist/build/pdf.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$DocumentContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/DocumentContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/Message.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$LinkService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/LinkService.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$PasswordResponses$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/PasswordResponses.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/useResolver.js [app-client] (ecmascript)");
'use client';
var __rest = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const { PDFDataRangeTransport } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$node_modules$2f$pdfjs$2d$dist$2f$build$2f$pdf$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__;
const defaultOnPassword = (callback, reason)=>{
    switch(reason){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$PasswordResponses$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].NEED_PASSWORD:
            {
                const password = prompt('Enter the password to open this PDF file.');
                callback(password);
                break;
            }
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$PasswordResponses$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].INCORRECT_PASSWORD:
            {
                const password = prompt('Invalid password. Please try again.');
                callback(password);
                break;
            }
        default:
    }
};
function isParameterObject(file) {
    return typeof file === 'object' && file !== null && ('data' in file || 'range' in file || 'url' in file);
}
/**
 * Loads a document passed using `file` prop.
 */ const Document = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(function Document(_a, ref) {
    var { children, className, error = 'Failed to load PDF file.', externalLinkRel, externalLinkTarget, file, inputRef, imageResourcesPath, loading = 'Loading PDF…', noData = 'No PDF file specified.', onItemClick, onLoadError: onLoadErrorProps, onLoadProgress, onLoadSuccess: onLoadSuccessProps, onPassword = defaultOnPassword, onSourceError: onSourceErrorProps, onSourceSuccess: onSourceSuccessProps, options, renderMode, rotate, scale } = _a, otherProps = __rest(_a, [
        "children",
        "className",
        "error",
        "externalLinkRel",
        "externalLinkTarget",
        "file",
        "inputRef",
        "imageResourcesPath",
        "loading",
        "noData",
        "onItemClick",
        "onLoadError",
        "onLoadProgress",
        "onLoadSuccess",
        "onPassword",
        "onSourceError",
        "onSourceSuccess",
        "options",
        "renderMode",
        "rotate",
        "scale"
    ]);
    const [sourceState, sourceDispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { value: source, error: sourceError } = sourceState;
    const [pdfState, pdfDispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { value: pdf, error: pdfError } = pdfState;
    const linkService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$LinkService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]());
    const pages = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const prevFile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const prevOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    if (file && file !== prevFile.current && isParameterObject(file)) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dequal$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dequal"])(file, prevFile.current), 'File prop passed to <Document /> changed, but it\'s equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "file" prop.');
        prevFile.current = file;
    }
    // Detect non-memoized changes in options prop
    if (options && options !== prevOptions.current) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dequal$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dequal"])(options, prevOptions.current), 'Options prop passed to <Document /> changed, but it\'s equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "options" prop.');
        prevOptions.current = options;
    }
    const viewer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({
        // Handling jumping to internal links target
        scrollPageIntoView: {
            "Document.Document.useRef[viewer]": (args)=>{
                const { dest, pageNumber, pageIndex = pageNumber - 1 } = args;
                // First, check if custom handling of onItemClick was provided
                if (onItemClick) {
                    onItemClick({
                        dest,
                        pageIndex,
                        pageNumber
                    });
                    return;
                }
                // If not, try to look for target page within the <Document>.
                const page = pages.current[pageIndex];
                if (page) {
                    // Scroll to the page automatically
                    page.scrollIntoView();
                    return;
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, "An internal link leading to page ".concat(pageNumber, " was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>."));
            }
        }["Document.Document.useRef[viewer]"]
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "Document.Document.useImperativeHandle": ()=>({
                linkService,
                pages,
                viewer
            })
    }["Document.Document.useImperativeHandle"], []);
    /**
     * Called when a document source is resolved correctly
     */ function onSourceSuccess() {
        if (onSourceSuccessProps) {
            onSourceSuccessProps();
        }
    }
    /**
     * Called when a document source failed to be resolved correctly
     */ function onSourceError() {
        if (!sourceError) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, sourceError.toString());
        if (onSourceErrorProps) {
            onSourceErrorProps(sourceError);
        }
    }
    function resetSource() {
        sourceDispatch({
            type: 'RESET'
        });
    }
    // biome-ignore lint/correctness/useExhaustiveDependencies: See https://github.com/biomejs/biome/issues/3080
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(resetSource, [
        file,
        sourceDispatch
    ]);
    const findDocumentSource = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Document.Document.useCallback[findDocumentSource]": async ()=>{
            if (!file) {
                return null;
            }
            // File is a string
            if (typeof file === 'string') {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDataURI"])(file)) {
                    const fileByteString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dataURItoByteString"])(file);
                    return {
                        data: fileByteString
                    };
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["displayCORSWarning"])();
                return {
                    url: file
                };
            }
            // File is PDFDataRangeTransport
            if (file instanceof PDFDataRangeTransport) {
                return {
                    range: file
                };
            }
            // File is an ArrayBuffer
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isArrayBuffer"])(file)) {
                return {
                    data: file
                };
            }
            /**
         * The cases below are browser-only.
         * If you're running on a non-browser environment, these cases will be of no use.
         */ if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isBrowser"]) {
                // File is a Blob
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isBlob"])(file)) {
                    const data = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadFromFile"])(file);
                    return {
                        data
                    };
                }
            }
            // At this point, file must be an object
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(typeof file === 'object', 'Invalid parameter in file, need either Uint8Array, string or a parameter object');
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(isParameterObject(file), 'Invalid parameter object: need either .data, .range or .url');
            // File .url is a string
            if ('url' in file && typeof file.url === 'string') {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDataURI"])(file.url)) {
                    const { url } = file, otherParams = __rest(file, [
                        "url"
                    ]);
                    const fileByteString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dataURItoByteString"])(url);
                    return Object.assign({
                        data: fileByteString
                    }, otherParams);
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["displayCORSWarning"])();
            }
            return file;
        }
    }["Document.Document.useCallback[findDocumentSource]"], [
        file
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Document.Document.useEffect": ()=>{
            const cancellable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$cancellable$2d$promise$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(findDocumentSource());
            cancellable.promise.then({
                "Document.Document.useEffect": (nextSource)=>{
                    sourceDispatch({
                        type: 'RESOLVE',
                        value: nextSource
                    });
                }
            }["Document.Document.useEffect"]).catch({
                "Document.Document.useEffect": (error)=>{
                    sourceDispatch({
                        type: 'REJECT',
                        error
                    });
                }
            }["Document.Document.useEffect"]);
            return ({
                "Document.Document.useEffect": ()=>{
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelRunningTask"])(cancellable);
                }
            })["Document.Document.useEffect"];
        }
    }["Document.Document.useEffect"], [
        findDocumentSource,
        sourceDispatch
    ]);
    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Document.Document.useEffect": ()=>{
            if (typeof source === 'undefined') {
                return;
            }
            if (source === false) {
                onSourceError();
                return;
            }
            onSourceSuccess();
        }
    }["Document.Document.useEffect"], [
        source
    ]);
    /**
     * Called when a document is read successfully
     */ function onLoadSuccess() {
        if (!pdf) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        if (onLoadSuccessProps) {
            onLoadSuccessProps(pdf);
        }
        pages.current = new Array(pdf.numPages);
        linkService.current.setDocument(pdf);
    }
    /**
     * Called when a document failed to read successfully
     */ function onLoadError() {
        if (!pdfError) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, pdfError.toString());
        if (onLoadErrorProps) {
            onLoadErrorProps(pdfError);
        }
    }
    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on source change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function resetDocument() {
        pdfDispatch({
            type: 'RESET'
        });
    }, [
        pdfDispatch,
        source
    ]);
    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function loadDocument() {
        if (!source) {
            return;
        }
        const documentInitParams = options ? Object.assign(Object.assign({}, source), options) : source;
        const destroyable = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$node_modules$2f$pdfjs$2d$dist$2f$build$2f$pdf$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.getDocument(documentInitParams);
        if (onLoadProgress) {
            destroyable.onProgress = onLoadProgress;
        }
        if (onPassword) {
            destroyable.onPassword = onPassword;
        }
        const loadingTask = destroyable;
        const loadingPromise = loadingTask.promise.then({
            "Document.Document.useEffect.loadDocument.loadingPromise": (nextPdf)=>{
                pdfDispatch({
                    type: 'RESOLVE',
                    value: nextPdf
                });
            }
        }["Document.Document.useEffect.loadDocument.loadingPromise"]).catch({
            "Document.Document.useEffect.loadDocument.loadingPromise": (error)=>{
                if (loadingTask.destroyed) {
                    return;
                }
                pdfDispatch({
                    type: 'REJECT',
                    error
                });
            }
        }["Document.Document.useEffect.loadDocument.loadingPromise"]);
        return ({
            "Document.Document.useEffect.loadDocument": ()=>{
                loadingPromise.finally({
                    "Document.Document.useEffect.loadDocument": ()=>loadingTask.destroy()
                }["Document.Document.useEffect.loadDocument"]);
            }
        })["Document.Document.useEffect.loadDocument"];
    }, [
        options,
        pdfDispatch,
        source
    ]);
    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Document.Document.useEffect": ()=>{
            if (typeof pdf === 'undefined') {
                return;
            }
            if (pdf === false) {
                onLoadError();
                return;
            }
            onLoadSuccess();
        }
    }["Document.Document.useEffect"], [
        pdf
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function setupLinkService() {
        linkService.current.setViewer(viewer.current);
        linkService.current.setExternalLinkRel(externalLinkRel);
        linkService.current.setExternalLinkTarget(externalLinkTarget);
    }, [
        externalLinkRel,
        externalLinkTarget
    ]);
    const registerPage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Document.Document.useCallback[registerPage]": (pageIndex, ref)=>{
            pages.current[pageIndex] = ref;
        }
    }["Document.Document.useCallback[registerPage]"], []);
    const unregisterPage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Document.Document.useCallback[unregisterPage]": (pageIndex)=>{
            delete pages.current[pageIndex];
        }
    }["Document.Document.useCallback[unregisterPage]"], []);
    const childContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Document.Document.useMemo[childContext]": ()=>({
                imageResourcesPath,
                linkService: linkService.current,
                onItemClick,
                pdf,
                registerPage,
                renderMode,
                rotate,
                scale,
                unregisterPage
            })
    }["Document.Document.useMemo[childContext]"], [
        imageResourcesPath,
        onItemClick,
        pdf,
        registerPage,
        renderMode,
        rotate,
        scale,
        unregisterPage
    ]);
    const eventProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Document.Document.useMemo[eventProps]": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$event$2d$props$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(otherProps, {
                "Document.Document.useMemo[eventProps]": ()=>pdf
            }["Document.Document.useMemo[eventProps]"])
    }["Document.Document.useMemo[eventProps]"], // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME
    [
        otherProps,
        pdf
    ]);
    function renderChildren() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$DocumentContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Provider, {
            value: childContext,
            children: children
        });
    }
    function renderContent() {
        if (!file) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                type: "no-data",
                children: typeof noData === 'function' ? noData() : noData
            });
        }
        if (pdf === undefined || pdf === null) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                type: "loading",
                children: typeof loading === 'function' ? loading() : loading
            });
        }
        if (pdf === false) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                type: "error",
                children: typeof error === 'function' ? error() : error
            });
        }
        return renderChildren();
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", Object.assign({
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])('react-pdf__Document', className),
        // Assertion is needed for React 18 compatibility
        ref: inputRef
    }, eventProps, {
        children: renderContent()
    }));
});
const __TURBOPACK__default__export__ = Document;
}),
"[project]/node_modules/react-pdf/dist/Document.js [app-client] (ecmascript) <export default as Document>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Document": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Document$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Document$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/Document.js [app-client] (ecmascript)");
}),
"[project]/node_modules/react-pdf/dist/PageContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
const pageContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const __TURBOPACK__default__export__ = pageContext;
}),
"[project]/node_modules/react-pdf/dist/shared/constants.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// From pdfjs-dist/lib/web/struct_tree_layer_builder.js
__turbopack_context__.s({
    "HEADING_PATTERN": ()=>HEADING_PATTERN,
    "PDF_ROLE_TO_HTML_ROLE": ()=>PDF_ROLE_TO_HTML_ROLE
});
const PDF_ROLE_TO_HTML_ROLE = {
    // Document level structure types
    Document: null,
    DocumentFragment: null,
    // Grouping level structure types
    Part: 'group',
    Sect: 'group',
    Div: 'group',
    Aside: 'note',
    NonStruct: 'none',
    // Block level structure types
    P: null,
    // H<n>,
    H: 'heading',
    Title: null,
    FENote: 'note',
    // Sub-block level structure type
    Sub: 'group',
    // General inline level structure types
    Lbl: null,
    Span: null,
    Em: null,
    Strong: null,
    Link: 'link',
    Annot: 'note',
    Form: 'form',
    // Ruby and Warichu structure types
    Ruby: null,
    RB: null,
    RT: null,
    RP: null,
    Warichu: null,
    WT: null,
    WP: null,
    // List standard structure types
    L: 'list',
    LI: 'listitem',
    LBody: null,
    // Table standard structure types
    Table: 'table',
    TR: 'row',
    TH: 'columnheader',
    TD: 'cell',
    THead: 'columnheader',
    TBody: null,
    TFoot: null,
    // Standard structure type Caption
    Caption: null,
    // Standard structure type Figure
    Figure: 'figure',
    // Standard structure type Formula
    Formula: null,
    // standard structure type Artifact
    Artifact: null
};
const HEADING_PATTERN = /^H(\d+)$/;
}),
"[project]/node_modules/react-pdf/dist/shared/structTreeUtils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getAttributes": ()=>getAttributes,
    "getBaseAttributes": ()=>getBaseAttributes,
    "getRoleAttributes": ()=>getRoleAttributes,
    "isPdfRole": ()=>isPdfRole,
    "isStructTreeNode": ()=>isStructTreeNode,
    "isStructTreeNodeWithOnlyContentChild": ()=>isStructTreeNodeWithOnlyContentChild
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/constants.js [app-client] (ecmascript)");
;
function isPdfRole(role) {
    return role in __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PDF_ROLE_TO_HTML_ROLE"];
}
function isStructTreeNode(node) {
    return 'children' in node;
}
function isStructTreeNodeWithOnlyContentChild(node) {
    if (!isStructTreeNode(node)) {
        return false;
    }
    return node.children.length === 1 && 0 in node.children && 'id' in node.children[0];
}
function getRoleAttributes(node) {
    const attributes = {};
    if (isStructTreeNode(node)) {
        const { role } = node;
        const matches = role.match(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HEADING_PATTERN"]);
        if (matches) {
            attributes.role = 'heading';
            attributes['aria-level'] = Number(matches[1]);
        } else if (isPdfRole(role)) {
            const htmlRole = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PDF_ROLE_TO_HTML_ROLE"][role];
            if (htmlRole) {
                attributes.role = htmlRole;
            }
        }
    }
    return attributes;
}
function getBaseAttributes(node) {
    const attributes = {};
    if (isStructTreeNode(node)) {
        if (node.alt !== undefined) {
            attributes['aria-label'] = node.alt;
        }
        if (node.lang !== undefined) {
            attributes.lang = node.lang;
        }
        if (isStructTreeNodeWithOnlyContentChild(node)) {
            const [child] = node.children;
            if (child) {
                const childAttributes = getBaseAttributes(child);
                return Object.assign(Object.assign({}, attributes), childAttributes);
            }
        }
    } else {
        if ('id' in node) {
            attributes['aria-owns'] = node.id;
        }
    }
    return attributes;
}
function getAttributes(node) {
    if (!node) {
        return null;
    }
    return Object.assign(Object.assign({}, getRoleAttributes(node)), getBaseAttributes(node));
}
}),
"[project]/node_modules/react-pdf/dist/StructTreeItem.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>StructTreeItem
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$structTreeUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/structTreeUtils.js [app-client] (ecmascript)");
;
;
;
function StructTreeItem(param) {
    let { className, node } = param;
    const attributes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "StructTreeItem.useMemo[attributes]": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$structTreeUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAttributes"])(node)
    }["StructTreeItem.useMemo[attributes]"], [
        node
    ]);
    const children = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "StructTreeItem.useMemo[children]": ()=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$structTreeUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isStructTreeNode"])(node)) {
                return null;
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$structTreeUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isStructTreeNodeWithOnlyContentChild"])(node)) {
                return null;
            }
            return node.children.map({
                "StructTreeItem.useMemo[children]": (child, index)=>{
                    return(// biome-ignore lint/suspicious/noArrayIndexKey: index is stable here
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(StructTreeItem, {
                        node: child
                    }, index));
                }
            }["StructTreeItem.useMemo[children]"]);
        }
    }["StructTreeItem.useMemo[children]"], [
        node
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", Object.assign({
        className: className
    }, attributes, {
        children: children
    }));
}
}),
"[project]/node_modules/react-pdf/dist/shared/hooks/usePageContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>usePageContext
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$PageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/PageContext.js [app-client] (ecmascript)");
;
;
function usePageContext() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$PageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
}
}),
"[project]/node_modules/react-pdf/dist/StructTree.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>StructTree
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$cancellable$2d$promise$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/make-cancellable-promise/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiny-invariant/dist/esm/tiny-invariant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/warning/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$StructTreeItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/StructTreeItem.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$usePageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/usePageContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/useResolver.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/utils.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
function StructTree() {
    const pageContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$usePageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pageContext, 'Unable to find Page context.');
    const { onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps } = pageContext;
    const [structTreeState, structTreeDispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { value: structTree, error: structTreeError } = structTreeState;
    const { customTextRenderer, page } = pageContext;
    function onLoadSuccess() {
        if (!structTree) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        if (onGetStructTreeSuccessProps) {
            onGetStructTreeSuccessProps(structTree);
        }
    }
    function onLoadError() {
        if (!structTreeError) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, structTreeError.toString());
        if (onGetStructTreeErrorProps) {
            onGetStructTreeErrorProps(structTreeError);
        }
    }
    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function resetStructTree() {
        structTreeDispatch({
            type: 'RESET'
        });
    }, [
        structTreeDispatch,
        page
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function loadStructTree() {
        if (customTextRenderer) {
            // TODO: Document why this is necessary
            return;
        }
        if (!page) {
            return;
        }
        const cancellable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$cancellable$2d$promise$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(page.getStructTree());
        const runningTask = cancellable;
        cancellable.promise.then({
            "StructTree.useEffect.loadStructTree": (nextStructTree)=>{
                structTreeDispatch({
                    type: 'RESOLVE',
                    value: nextStructTree
                });
            }
        }["StructTree.useEffect.loadStructTree"]).catch({
            "StructTree.useEffect.loadStructTree": (error)=>{
                structTreeDispatch({
                    type: 'REJECT',
                    error
                });
            }
        }["StructTree.useEffect.loadStructTree"]);
        return ({
            "StructTree.useEffect.loadStructTree": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelRunningTask"])(runningTask)
        })["StructTree.useEffect.loadStructTree"];
    }, [
        customTextRenderer,
        page,
        structTreeDispatch
    ]);
    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StructTree.useEffect": ()=>{
            if (structTree === undefined) {
                return;
            }
            if (structTree === false) {
                onLoadError();
                return;
            }
            onLoadSuccess();
        }
    }["StructTree.useEffect"], [
        structTree
    ]);
    if (!structTree) {
        return null;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$StructTreeItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: "react-pdf__Page__structTree structTree",
        node: structTree
    });
}
}),
"[project]/node_modules/react-pdf/dist/Page/Canvas.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>Canvas
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$merge$2d$refs$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/merge-refs/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiny-invariant/dist/esm/tiny-invariant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/warning/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$node_modules$2f$pdfjs$2d$dist$2f$build$2f$pdf$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/node_modules/pdfjs-dist/build/pdf.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$StructTree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/StructTree.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$usePageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/usePageContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/utils.js [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
const ANNOTATION_MODE = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$node_modules$2f$pdfjs$2d$dist$2f$build$2f$pdf$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnnotationMode"];
function Canvas(props) {
    const pageContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$usePageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pageContext, 'Unable to find Page context.');
    const mergedProps = Object.assign(Object.assign({}, pageContext), props);
    const { _className, canvasBackground, devicePixelRatio = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDevicePixelRatio"])(), onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, page, renderForms, renderTextLayer, rotate, scale } = mergedProps;
    const { canvasRef } = props;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(page, 'Attempted to render page canvas, but no page was specified.');
    const canvasElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    /**
     * Called when a page is rendered successfully.
     */ function onRenderSuccess() {
        if (!page) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        if (onRenderSuccessProps) {
            onRenderSuccessProps((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["makePageCallback"])(page, scale));
        }
    }
    /**
     * Called when a page fails to render.
     */ function onRenderError(error) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isCancelException"])(error)) {
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, error.toString());
        if (onRenderErrorProps) {
            onRenderErrorProps(error);
        }
    }
    const renderViewport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Canvas.useMemo[renderViewport]": ()=>page.getViewport({
                scale: scale * devicePixelRatio,
                rotation: rotate
            })
    }["Canvas.useMemo[renderViewport]"], [
        devicePixelRatio,
        page,
        rotate,
        scale
    ]);
    const viewport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Canvas.useMemo[viewport]": ()=>page.getViewport({
                scale,
                rotation: rotate
            })
    }["Canvas.useMemo[viewport]"], [
        page,
        rotate,
        scale
    ]);
    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function drawPageOnCanvas() {
        if (!page) {
            return;
        }
        // Ensures the canvas will be re-rendered from scratch. Otherwise all form data will stay.
        page.cleanup();
        const { current: canvas } = canvasElement;
        if (!canvas) {
            return;
        }
        canvas.width = renderViewport.width;
        canvas.height = renderViewport.height;
        canvas.style.width = "".concat(Math.floor(viewport.width), "px");
        canvas.style.height = "".concat(Math.floor(viewport.height), "px");
        canvas.style.visibility = 'hidden';
        const renderContext = {
            annotationMode: renderForms ? ANNOTATION_MODE.ENABLE_FORMS : ANNOTATION_MODE.ENABLE,
            canvasContext: canvas.getContext('2d', {
                alpha: false
            }),
            viewport: renderViewport
        };
        if (canvasBackground) {
            renderContext.background = canvasBackground;
        }
        const cancellable = page.render(renderContext);
        const runningTask = cancellable;
        cancellable.promise.then({
            "Canvas.useEffect.drawPageOnCanvas": ()=>{
                canvas.style.visibility = '';
                onRenderSuccess();
            }
        }["Canvas.useEffect.drawPageOnCanvas"]).catch(onRenderError);
        return ({
            "Canvas.useEffect.drawPageOnCanvas": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelRunningTask"])(runningTask)
        })["Canvas.useEffect.drawPageOnCanvas"];
    }, [
        canvasBackground,
        page,
        renderForms,
        renderViewport,
        viewport
    ]);
    const cleanup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Canvas.useCallback[cleanup]": ()=>{
            const { current: canvas } = canvasElement;
            /**
         * Zeroing the width and height cause most browsers to release graphics
         * resources immediately, which can greatly reduce memory consumption.
         */ if (canvas) {
                canvas.width = 0;
                canvas.height = 0;
            }
        }
    }["Canvas.useCallback[cleanup]"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Canvas.useEffect": ()=>cleanup
    }["Canvas.useEffect"], [
        cleanup
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("canvas", {
        className: "".concat(_className, "__canvas"),
        dir: "ltr",
        ref: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$merge$2d$refs$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(canvasRef, canvasElement),
        style: {
            display: 'block',
            userSelect: 'none'
        },
        children: renderTextLayer ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$StructTree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}) : null
    });
}
}),
"[project]/node_modules/react-pdf/dist/Page/TextLayer.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>TextLayer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$cancellable$2d$promise$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/make-cancellable-promise/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiny-invariant/dist/esm/tiny-invariant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/warning/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$node_modules$2f$pdfjs$2d$dist$2f$build$2f$pdf$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/node_modules/pdfjs-dist/build/pdf.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$usePageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/usePageContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/useResolver.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/utils.js [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
function isTextItem(item) {
    return 'str' in item;
}
function TextLayer() {
    const pageContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$usePageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pageContext, 'Unable to find Page context.');
    const { customTextRenderer, onGetTextError, onGetTextSuccess, onRenderTextLayerError, onRenderTextLayerSuccess, page, pageIndex, pageNumber, rotate, scale } = pageContext;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(page, 'Attempted to load page text content, but no page was specified.');
    const [textContentState, textContentDispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { value: textContent, error: textContentError } = textContentState;
    const layerElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Number.parseInt(window.getComputedStyle(document.body).getPropertyValue('--react-pdf-text-layer'), 10) === 1, 'TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer');
    /**
     * Called when a page text content is read successfully
     */ function onLoadSuccess() {
        if (!textContent) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        if (onGetTextSuccess) {
            onGetTextSuccess(textContent);
        }
    }
    /**
     * Called when a page text content failed to read successfully
     */ function onLoadError() {
        if (!textContentError) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, textContentError.toString());
        if (onGetTextError) {
            onGetTextError(textContentError);
        }
    }
    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function resetTextContent() {
        textContentDispatch({
            type: 'RESET'
        });
    }, [
        page,
        textContentDispatch
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function loadTextContent() {
        if (!page) {
            return;
        }
        const cancellable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$cancellable$2d$promise$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(page.getTextContent());
        const runningTask = cancellable;
        cancellable.promise.then({
            "TextLayer.useEffect.loadTextContent": (nextTextContent)=>{
                textContentDispatch({
                    type: 'RESOLVE',
                    value: nextTextContent
                });
            }
        }["TextLayer.useEffect.loadTextContent"]).catch({
            "TextLayer.useEffect.loadTextContent": (error)=>{
                textContentDispatch({
                    type: 'REJECT',
                    error
                });
            }
        }["TextLayer.useEffect.loadTextContent"]);
        return ({
            "TextLayer.useEffect.loadTextContent": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelRunningTask"])(runningTask)
        })["TextLayer.useEffect.loadTextContent"];
    }, [
        page,
        textContentDispatch
    ]);
    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TextLayer.useEffect": ()=>{
            if (textContent === undefined) {
                return;
            }
            if (textContent === false) {
                onLoadError();
                return;
            }
            onLoadSuccess();
        }
    }["TextLayer.useEffect"], [
        textContent
    ]);
    /**
     * Called when a text layer is rendered successfully
     */ const onRenderSuccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TextLayer.useCallback[onRenderSuccess]": ()=>{
            if (onRenderTextLayerSuccess) {
                onRenderTextLayerSuccess();
            }
        }
    }["TextLayer.useCallback[onRenderSuccess]"], [
        onRenderTextLayerSuccess
    ]);
    /**
     * Called when a text layer failed to render successfully
     */ const onRenderError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TextLayer.useCallback[onRenderError]": (error)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, error.toString());
            if (onRenderTextLayerError) {
                onRenderTextLayerError(error);
            }
        }
    }["TextLayer.useCallback[onRenderError]"], [
        onRenderTextLayerError
    ]);
    function onMouseDown() {
        const layer = layerElement.current;
        if (!layer) {
            return;
        }
        layer.classList.add('selecting');
    }
    function onMouseUp() {
        const layer = layerElement.current;
        if (!layer) {
            return;
        }
        layer.classList.remove('selecting');
    }
    const viewport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TextLayer.useMemo[viewport]": ()=>page.getViewport({
                scale,
                rotation: rotate
            })
    }["TextLayer.useMemo[viewport]"], [
        page,
        rotate,
        scale
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])(function renderTextLayer() {
        if (!page || !textContent) {
            return;
        }
        const { current: layer } = layerElement;
        if (!layer) {
            return;
        }
        layer.innerHTML = '';
        const textContentSource = page.streamTextContent({
            includeMarkedContent: true
        });
        const parameters = {
            container: layer,
            textContentSource,
            viewport
        };
        const cancellable = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$node_modules$2f$pdfjs$2d$dist$2f$build$2f$pdf$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextLayer"](parameters);
        const runningTask = cancellable;
        cancellable.render().then({
            "TextLayer.useLayoutEffect.renderTextLayer": ()=>{
                const end = document.createElement('div');
                end.className = 'endOfContent';
                layer.append(end);
                const layerChildren = layer.querySelectorAll('[role="presentation"]');
                if (customTextRenderer) {
                    let index = 0;
                    textContent.items.forEach({
                        "TextLayer.useLayoutEffect.renderTextLayer": (item, itemIndex)=>{
                            if (!isTextItem(item)) {
                                return;
                            }
                            const child = layerChildren[index];
                            if (!child) {
                                return;
                            }
                            const content = customTextRenderer(Object.assign({
                                pageIndex,
                                pageNumber,
                                itemIndex
                            }, item));
                            child.innerHTML = content;
                            index += item.str && item.hasEOL ? 2 : 1;
                        }
                    }["TextLayer.useLayoutEffect.renderTextLayer"]);
                }
                // Intentional immediate callback
                onRenderSuccess();
            }
        }["TextLayer.useLayoutEffect.renderTextLayer"]).catch(onRenderError);
        return ({
            "TextLayer.useLayoutEffect.renderTextLayer": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelRunningTask"])(runningTask)
        })["TextLayer.useLayoutEffect.renderTextLayer"];
    }, [
        customTextRenderer,
        onRenderError,
        onRenderSuccess,
        page,
        pageIndex,
        pageNumber,
        textContent,
        viewport
    ]);
    return(// biome-ignore lint/a11y/noStaticElementInteractions: False positive caused by non interactive wrapper listening for bubbling events
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])('react-pdf__Page__textContent', 'textLayer'),
        onMouseUp: onMouseUp,
        onMouseDown: onMouseDown,
        ref: layerElement
    }));
}
}),
"[project]/node_modules/react-pdf/dist/shared/hooks/useDocumentContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>useDocumentContext
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$DocumentContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/DocumentContext.js [app-client] (ecmascript)");
;
;
function useDocumentContext() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$DocumentContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
}
}),
"[project]/node_modules/react-pdf/dist/Page/AnnotationLayer.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>AnnotationLayer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$cancellable$2d$promise$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/make-cancellable-promise/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiny-invariant/dist/esm/tiny-invariant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/warning/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$node_modules$2f$pdfjs$2d$dist$2f$build$2f$pdf$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/node_modules/pdfjs-dist/build/pdf.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useDocumentContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/useDocumentContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$usePageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/usePageContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/useResolver.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/utils.js [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
function AnnotationLayer() {
    const documentContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useDocumentContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const pageContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$usePageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pageContext, 'Unable to find Page context.');
    const mergedProps = Object.assign(Object.assign({}, documentContext), pageContext);
    const { imageResourcesPath, linkService, onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, page, pdf, renderForms, rotate, scale = 1 } = mergedProps;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pdf, 'Attempted to load page annotations, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(page, 'Attempted to load page annotations, but no page was specified.');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(linkService, 'Attempted to load page annotations, but no linkService was specified.');
    const [annotationsState, annotationsDispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { value: annotations, error: annotationsError } = annotationsState;
    const layerElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Number.parseInt(window.getComputedStyle(document.body).getPropertyValue('--react-pdf-annotation-layer'), 10) === 1, 'AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations');
    function onLoadSuccess() {
        if (!annotations) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        if (onGetAnnotationsSuccessProps) {
            onGetAnnotationsSuccessProps(annotations);
        }
    }
    function onLoadError() {
        if (!annotationsError) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, annotationsError.toString());
        if (onGetAnnotationsErrorProps) {
            onGetAnnotationsErrorProps(annotationsError);
        }
    }
    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on page change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function resetAnnotations() {
        annotationsDispatch({
            type: 'RESET'
        });
    }, [
        annotationsDispatch,
        page
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function loadAnnotations() {
        if (!page) {
            return;
        }
        const cancellable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$cancellable$2d$promise$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(page.getAnnotations());
        const runningTask = cancellable;
        cancellable.promise.then({
            "AnnotationLayer.useEffect.loadAnnotations": (nextAnnotations)=>{
                annotationsDispatch({
                    type: 'RESOLVE',
                    value: nextAnnotations
                });
            }
        }["AnnotationLayer.useEffect.loadAnnotations"]).catch({
            "AnnotationLayer.useEffect.loadAnnotations": (error)=>{
                annotationsDispatch({
                    type: 'REJECT',
                    error
                });
            }
        }["AnnotationLayer.useEffect.loadAnnotations"]);
        return ({
            "AnnotationLayer.useEffect.loadAnnotations": ()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelRunningTask"])(runningTask);
            }
        })["AnnotationLayer.useEffect.loadAnnotations"];
    }, [
        annotationsDispatch,
        page
    ]);
    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnnotationLayer.useEffect": ()=>{
            if (annotations === undefined) {
                return;
            }
            if (annotations === false) {
                onLoadError();
                return;
            }
            onLoadSuccess();
        }
    }["AnnotationLayer.useEffect"], [
        annotations
    ]);
    function onRenderSuccess() {
        if (onRenderAnnotationLayerSuccessProps) {
            onRenderAnnotationLayerSuccessProps();
        }
    }
    function onRenderError(error) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, "".concat(error));
        if (onRenderAnnotationLayerErrorProps) {
            onRenderAnnotationLayerErrorProps(error);
        }
    }
    const viewport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "AnnotationLayer.useMemo[viewport]": ()=>page.getViewport({
                scale,
                rotation: rotate
            })
    }["AnnotationLayer.useMemo[viewport]"], [
        page,
        rotate,
        scale
    ]);
    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function renderAnnotationLayer() {
        if (!pdf || !page || !linkService || !annotations) {
            return;
        }
        const { current: layer } = layerElement;
        if (!layer) {
            return;
        }
        const clonedViewport = viewport.clone({
            dontFlip: true
        });
        const annotationLayerParameters = {
            accessibilityManager: null,
            annotationCanvasMap: null,
            annotationEditorUIManager: null,
            div: layer,
            l10n: null,
            page,
            structTreeLayer: null,
            viewport: clonedViewport
        };
        const renderParameters = {
            annotations,
            annotationStorage: pdf.annotationStorage,
            div: layer,
            imageResourcesPath,
            linkService,
            page,
            renderForms,
            viewport: clonedViewport
        };
        layer.innerHTML = '';
        try {
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$node_modules$2f$pdfjs$2d$dist$2f$build$2f$pdf$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnnotationLayer"](annotationLayerParameters).render(renderParameters);
            // Intentional immediate callback
            onRenderSuccess();
        } catch (error) {
            onRenderError(error);
        }
        return ({
            "AnnotationLayer.useEffect.renderAnnotationLayer": ()=>{
            // TODO: Cancel running task?
            }
        })["AnnotationLayer.useEffect.renderAnnotationLayer"];
    }, [
        annotations,
        imageResourcesPath,
        linkService,
        page,
        pdf,
        renderForms,
        viewport
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])('react-pdf__Page__annotations', 'annotationLayer'),
        ref: layerElement
    });
}
}),
"[project]/node_modules/react-pdf/dist/Page.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>Page
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$cancellable$2d$promise$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/make-cancellable-promise/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$event$2d$props$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/make-event-props/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$merge$2d$refs$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/merge-refs/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tiny-invariant/dist/esm/tiny-invariant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/warning/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$PageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/PageContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/Message.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Page$2f$Canvas$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/Page/Canvas.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Page$2f$TextLayer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/Page/TextLayer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Page$2f$AnnotationLayer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/Page/AnnotationLayer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useDocumentContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/useDocumentContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/shared/hooks/useResolver.js [app-client] (ecmascript)");
'use client';
var __rest = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const defaultScale = 1;
function Page(props) {
    const documentContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useDocumentContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const mergedProps = Object.assign(Object.assign({}, documentContext), props);
    const { _className = 'react-pdf__Page', _enableRegisterUnregisterPage = true, canvasBackground, canvasRef, children, className, customRenderer: CustomRenderer, customTextRenderer, devicePixelRatio, error = 'Failed to load the page.', height, inputRef, loading = 'Loading page…', noData = 'No page specified.', onGetAnnotationsError: onGetAnnotationsErrorProps, onGetAnnotationsSuccess: onGetAnnotationsSuccessProps, onGetStructTreeError: onGetStructTreeErrorProps, onGetStructTreeSuccess: onGetStructTreeSuccessProps, onGetTextError: onGetTextErrorProps, onGetTextSuccess: onGetTextSuccessProps, onLoadError: onLoadErrorProps, onLoadSuccess: onLoadSuccessProps, onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps, onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps, onRenderError: onRenderErrorProps, onRenderSuccess: onRenderSuccessProps, onRenderTextLayerError: onRenderTextLayerErrorProps, onRenderTextLayerSuccess: onRenderTextLayerSuccessProps, pageIndex: pageIndexProps, pageNumber: pageNumberProps, pdf, registerPage, renderAnnotationLayer: renderAnnotationLayerProps = true, renderForms = false, renderMode = 'canvas', renderTextLayer: renderTextLayerProps = true, rotate: rotateProps, scale: scaleProps = defaultScale, unregisterPage, width } = mergedProps, otherProps = __rest(mergedProps, [
        "_className",
        "_enableRegisterUnregisterPage",
        "canvasBackground",
        "canvasRef",
        "children",
        "className",
        "customRenderer",
        "customTextRenderer",
        "devicePixelRatio",
        "error",
        "height",
        "inputRef",
        "loading",
        "noData",
        "onGetAnnotationsError",
        "onGetAnnotationsSuccess",
        "onGetStructTreeError",
        "onGetStructTreeSuccess",
        "onGetTextError",
        "onGetTextSuccess",
        "onLoadError",
        "onLoadSuccess",
        "onRenderAnnotationLayerError",
        "onRenderAnnotationLayerSuccess",
        "onRenderError",
        "onRenderSuccess",
        "onRenderTextLayerError",
        "onRenderTextLayerSuccess",
        "pageIndex",
        "pageNumber",
        "pdf",
        "registerPage",
        "renderAnnotationLayer",
        "renderForms",
        "renderMode",
        "renderTextLayer",
        "rotate",
        "scale",
        "unregisterPage",
        "width"
    ]);
    const [pageState, pageDispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$hooks$2f$useResolver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { value: page, error: pageError } = pageState;
    const pageElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pdf, 'Attempted to load a page, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.');
    const pageIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isProvided"])(pageNumberProps) ? pageNumberProps - 1 : pageIndexProps !== null && pageIndexProps !== void 0 ? pageIndexProps : null;
    const pageNumber = pageNumberProps !== null && pageNumberProps !== void 0 ? pageNumberProps : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isProvided"])(pageIndexProps) ? pageIndexProps + 1 : null;
    const rotate = rotateProps !== null && rotateProps !== void 0 ? rotateProps : page ? page.rotate : null;
    const scale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Page.useMemo[scale]": ()=>{
            if (!page) {
                return null;
            }
            // Be default, we'll render page at 100% * scale width.
            let pageScale = 1;
            // Passing scale explicitly null would cause the page not to render
            const scaleWithDefault = scaleProps !== null && scaleProps !== void 0 ? scaleProps : defaultScale;
            // If width/height is defined, calculate the scale of the page so it could be of desired width.
            if (width || height) {
                const viewport = page.getViewport({
                    scale: 1,
                    rotation: rotate
                });
                if (width) {
                    pageScale = width / viewport.width;
                } else if (height) {
                    pageScale = height / viewport.height;
                }
            }
            return scaleWithDefault * pageScale;
        }
    }["Page.useMemo[scale]"], [
        height,
        page,
        rotate,
        scaleProps,
        width
    ]);
    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function hook() {
        return ({
            "Page.useEffect.hook": ()=>{
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isProvided"])(pageIndex)) {
                    // Impossible, but TypeScript doesn't know that
                    return;
                }
                if (_enableRegisterUnregisterPage && unregisterPage) {
                    unregisterPage(pageIndex);
                }
            }
        })["Page.useEffect.hook"];
    }, [
        _enableRegisterUnregisterPage,
        pdf,
        pageIndex,
        unregisterPage
    ]);
    /**
     * Called when a page is loaded successfully
     */ function onLoadSuccess() {
        if (onLoadSuccessProps) {
            if (!page || !scale) {
                // Impossible, but TypeScript doesn't know that
                return;
            }
            onLoadSuccessProps((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["makePageCallback"])(page, scale));
        }
        if (_enableRegisterUnregisterPage && registerPage) {
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isProvided"])(pageIndex) || !pageElement.current) {
                // Impossible, but TypeScript doesn't know that
                return;
            }
            registerPage(pageIndex, pageElement.current);
        }
    }
    /**
     * Called when a page failed to load
     */ function onLoadError() {
        if (!pageError) {
            // Impossible, but TypeScript doesn't know that
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$warning$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, pageError.toString());
        if (onLoadErrorProps) {
            onLoadErrorProps(pageError);
        }
    }
    // biome-ignore lint/correctness/useExhaustiveDependencies: useEffect intentionally triggered on pdf and pageIndex change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function resetPage() {
        pageDispatch({
            type: 'RESET'
        });
    }, [
        pageDispatch,
        pdf,
        pageIndex
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function loadPage() {
        if (!pdf || !pageNumber) {
            return;
        }
        const cancellable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$cancellable$2d$promise$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pdf.getPage(pageNumber));
        const runningTask = cancellable;
        cancellable.promise.then({
            "Page.useEffect.loadPage": (nextPage)=>{
                pageDispatch({
                    type: 'RESOLVE',
                    value: nextPage
                });
            }
        }["Page.useEffect.loadPage"]).catch({
            "Page.useEffect.loadPage": (error)=>{
                pageDispatch({
                    type: 'REJECT',
                    error
                });
            }
        }["Page.useEffect.loadPage"]);
        return ({
            "Page.useEffect.loadPage": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelRunningTask"])(runningTask)
        })["Page.useEffect.loadPage"];
    }, [
        pageDispatch,
        pdf,
        pageNumber
    ]);
    // biome-ignore lint/correctness/useExhaustiveDependencies: Ommitted callbacks so they are not called every time they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Page.useEffect": ()=>{
            if (page === undefined) {
                return;
            }
            if (page === false) {
                onLoadError();
                return;
            }
            onLoadSuccess();
        }
    }["Page.useEffect"], [
        page,
        scale
    ]);
    const childContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Page.useMemo[childContext]": ()=>// Technically there cannot be page without pageIndex, pageNumber, rotate and scale, but TypeScript doesn't know that
            page && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isProvided"])(pageIndex) && pageNumber && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isProvided"])(rotate) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isProvided"])(scale) ? {
                _className,
                canvasBackground,
                customTextRenderer,
                devicePixelRatio,
                onGetAnnotationsError: onGetAnnotationsErrorProps,
                onGetAnnotationsSuccess: onGetAnnotationsSuccessProps,
                onGetStructTreeError: onGetStructTreeErrorProps,
                onGetStructTreeSuccess: onGetStructTreeSuccessProps,
                onGetTextError: onGetTextErrorProps,
                onGetTextSuccess: onGetTextSuccessProps,
                onRenderAnnotationLayerError: onRenderAnnotationLayerErrorProps,
                onRenderAnnotationLayerSuccess: onRenderAnnotationLayerSuccessProps,
                onRenderError: onRenderErrorProps,
                onRenderSuccess: onRenderSuccessProps,
                onRenderTextLayerError: onRenderTextLayerErrorProps,
                onRenderTextLayerSuccess: onRenderTextLayerSuccessProps,
                page,
                pageIndex,
                pageNumber,
                renderForms,
                renderTextLayer: renderTextLayerProps,
                rotate,
                scale
            } : null
    }["Page.useMemo[childContext]"], [
        _className,
        canvasBackground,
        customTextRenderer,
        devicePixelRatio,
        onGetAnnotationsErrorProps,
        onGetAnnotationsSuccessProps,
        onGetStructTreeErrorProps,
        onGetStructTreeSuccessProps,
        onGetTextErrorProps,
        onGetTextSuccessProps,
        onRenderAnnotationLayerErrorProps,
        onRenderAnnotationLayerSuccessProps,
        onRenderErrorProps,
        onRenderSuccessProps,
        onRenderTextLayerErrorProps,
        onRenderTextLayerSuccessProps,
        page,
        pageIndex,
        pageNumber,
        renderForms,
        renderTextLayerProps,
        rotate,
        scale
    ]);
    const eventProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Page.useMemo[eventProps]": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$make$2d$event$2d$props$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(otherProps, {
                "Page.useMemo[eventProps]": ()=>page ? scale ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$shared$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["makePageCallback"])(page, scale) : undefined : page
            }["Page.useMemo[eventProps]"])
    }["Page.useMemo[eventProps]"], // biome-ignore lint/correctness/useExhaustiveDependencies: FIXME
    [
        otherProps,
        page,
        scale
    ]);
    const pageKey = "".concat(pageIndex, "@").concat(scale, "/").concat(rotate);
    function renderMainLayer() {
        switch(renderMode){
            case 'custom':
                {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tiny$2d$invariant$2f$dist$2f$esm$2f$tiny$2d$invariant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(CustomRenderer, 'renderMode was set to "custom", but no customRenderer was passed.');
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(CustomRenderer, {}, "".concat(pageKey, "_custom"));
                }
            case 'none':
                return null;
            case 'canvas':
            default:
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Page$2f$Canvas$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    canvasRef: canvasRef
                }, "".concat(pageKey, "_canvas"));
        }
    }
    function renderTextLayer() {
        if (!renderTextLayerProps) {
            return null;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Page$2f$TextLayer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, "".concat(pageKey, "_text"));
    }
    function renderAnnotationLayer() {
        if (!renderAnnotationLayerProps) {
            return null;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Page$2f$AnnotationLayer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, "".concat(pageKey, "_annotations"));
    }
    function renderChildren() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$PageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Provider, {
            value: childContext,
            children: [
                renderMainLayer(),
                renderTextLayer(),
                renderAnnotationLayer(),
                children
            ]
        });
    }
    function renderContent() {
        if (!pageNumber) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                type: "no-data",
                children: typeof noData === 'function' ? noData() : noData
            });
        }
        if (pdf === null || page === undefined || page === null) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                type: "loading",
                children: typeof loading === 'function' ? loading() : loading
            });
        }
        if (pdf === false || page === false) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                type: "error",
                children: typeof error === 'function' ? error() : error
            });
        }
        return renderChildren();
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", Object.assign({
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_className, className),
        "data-page-number": pageNumber,
        // Assertion is needed for React 18 compatibility
        ref: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$merge$2d$refs$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(inputRef, pageElement),
        style: {
            '--scale-round-x': '1px',
            '--scale-round-y': '1px',
            '--scale-factor': '1',
            '--user-unit': "".concat(scale),
            '--total-scale-factor': 'calc(var(--scale-factor) * var(--user-unit))',
            backgroundColor: canvasBackground || 'white',
            position: 'relative',
            minWidth: 'min-content',
            minHeight: 'min-content'
        }
    }, eventProps, {
        children: renderContent()
    }));
}
}),
"[project]/node_modules/react-pdf/dist/Page.js [app-client] (ecmascript) <export default as Page>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Page": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Page$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$pdf$2f$dist$2f$Page$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-pdf/dist/Page.js [app-client] (ecmascript)");
}),
}]);

//# sourceMappingURL=node_modules_react-pdf_dist_06b4de16._.js.map
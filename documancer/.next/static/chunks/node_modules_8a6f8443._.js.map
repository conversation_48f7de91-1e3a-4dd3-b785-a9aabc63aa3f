{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/make-event-props/dist/esm/index.js"], "sourcesContent": ["var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n// As defined on the list of supported events: https://reactjs.org/docs/events.html\nexport var clipboardEvents = ['onCopy', 'onCut', 'onPaste'];\nexport var compositionEvents = [\n    'onCompositionEnd',\n    'onCompositionStart',\n    'onCompositionUpdate',\n];\nexport var focusEvents = ['onFocus', 'onBlur'];\nexport var formEvents = ['onInput', 'onInvalid', 'onReset', 'onSubmit'];\nexport var imageEvents = ['onLoad', 'onError'];\nexport var keyboardEvents = ['onKeyDown', 'onKeyPress', 'onKeyUp'];\nexport var mediaEvents = [\n    'onAbort',\n    'onCanPlay',\n    'onCanPlayThrough',\n    'onDurationChange',\n    'onEmptied',\n    'onEncrypted',\n    'onEnded',\n    'onError',\n    'onLoadedData',\n    'onLoadedMetadata',\n    'onLoadStart',\n    'onPause',\n    'onPlay',\n    'onPlaying',\n    'onProgress',\n    'onRateChange',\n    'onSeeked',\n    'onSeeking',\n    'onStalled',\n    'onSuspend',\n    'onTimeUpdate',\n    'onVolumeChange',\n    'onWaiting',\n];\nexport var mouseEvents = [\n    'onClick',\n    'onContextMenu',\n    'onDoubleClick',\n    'onMouseDown',\n    'onMouseEnter',\n    'onMouseLeave',\n    'onMouseMove',\n    'onMouseOut',\n    'onMouseOver',\n    'onMouseUp',\n];\nexport var dragEvents = [\n    'onDrag',\n    'onDragEnd',\n    'onDragEnter',\n    'onDragExit',\n    'onDragLeave',\n    'onDragOver',\n    'onDragStart',\n    'onDrop',\n];\nexport var selectionEvents = ['onSelect'];\nexport var touchEvents = ['onTouchCancel', 'onTouchEnd', 'onTouchMove', 'onTouchStart'];\nexport var pointerEvents = [\n    'onPointerDown',\n    'onPointerMove',\n    'onPointerUp',\n    'onPointerCancel',\n    'onGotPointerCapture',\n    'onLostPointerCapture',\n    'onPointerEnter',\n    'onPointerLeave',\n    'onPointerOver',\n    'onPointerOut',\n];\nexport var uiEvents = ['onScroll'];\nexport var wheelEvents = ['onWheel'];\nexport var animationEvents = [\n    'onAnimationStart',\n    'onAnimationEnd',\n    'onAnimationIteration',\n];\nexport var transitionEvents = ['onTransitionEnd'];\nexport var otherEvents = ['onToggle'];\nexport var changeEvents = ['onChange'];\nexport var allEvents = __spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray(__spreadArray([], clipboardEvents, true), compositionEvents, true), focusEvents, true), formEvents, true), imageEvents, true), keyboardEvents, true), mediaEvents, true), mouseEvents, true), dragEvents, true), selectionEvents, true), touchEvents, true), pointerEvents, true), uiEvents, true), wheelEvents, true), animationEvents, true), transitionEvents, true), changeEvents, true), otherEvents, true);\n/**\n * Returns an object with on-event callback props curried with provided args.\n * @param {Object} props Props passed to a component.\n * @param {Function=} getArgs A function that returns argument(s) on-event callbacks\n *   shall be curried with.\n */\nexport default function makeEventProps(props, getArgs) {\n    var eventProps = {};\n    allEvents.forEach(function (eventName) {\n        var eventHandler = props[eventName];\n        if (!eventHandler) {\n            return;\n        }\n        if (getArgs) {\n            eventProps[eventName] = (function (event) {\n                return eventHandler(event, getArgs(eventName));\n            });\n        }\n        else {\n            eventProps[eventName] = eventHandler;\n        }\n    });\n    return eventProps;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,gBAAgB,4CAAS,yCAAK,aAAa,IAAK,SAAU,EAAE,EAAE,IAAI,EAAE,IAAI;IACxE,IAAI,QAAQ,UAAU,MAAM,KAAK,GAAG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,GAAG,IAAK;QACjF,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG;YACpB,IAAI,CAAC,IAAI,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACnB;IACJ;IACA,OAAO,GAAG,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACtD;AAEO,IAAI,kBAAkB;IAAC;IAAU;IAAS;CAAU;AACpD,IAAI,oBAAoB;IAC3B;IACA;IACA;CACH;AACM,IAAI,cAAc;IAAC;IAAW;CAAS;AACvC,IAAI,aAAa;IAAC;IAAW;IAAa;IAAW;CAAW;AAChE,IAAI,cAAc;IAAC;IAAU;CAAU;AACvC,IAAI,iBAAiB;IAAC;IAAa;IAAc;CAAU;AAC3D,IAAI,cAAc;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,IAAI,cAAc;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,IAAI,aAAa;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,IAAI,kBAAkB;IAAC;CAAW;AAClC,IAAI,cAAc;IAAC;IAAiB;IAAc;IAAe;CAAe;AAChF,IAAI,gBAAgB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,IAAI,WAAW;IAAC;CAAW;AAC3B,IAAI,cAAc;IAAC;CAAU;AAC7B,IAAI,kBAAkB;IACzB;IACA;IACA;CACH;AACM,IAAI,mBAAmB;IAAC;CAAkB;AAC1C,IAAI,cAAc;IAAC;CAAW;AAC9B,IAAI,eAAe;IAAC;CAAW;AAC/B,IAAI,YAAY,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,EAAE,EAAE,iBAAiB,OAAO,mBAAmB,OAAO,aAAa,OAAO,YAAY,OAAO,aAAa,OAAO,gBAAgB,OAAO,aAAa,OAAO,aAAa,OAAO,YAAY,OAAO,iBAAiB,OAAO,aAAa,OAAO,eAAe,OAAO,UAAU,OAAO,aAAa,OAAO,iBAAiB,OAAO,kBAAkB,OAAO,cAAc,OAAO,aAAa;AAOjoB,SAAS,eAAe,KAAK,EAAE,OAAO;IACjD,IAAI,aAAa,CAAC;IAClB,UAAU,OAAO,CAAC,SAAU,SAAS;QACjC,IAAI,eAAe,KAAK,CAAC,UAAU;QACnC,IAAI,CAAC,cAAc;YACf;QACJ;QACA,IAAI,SAAS;YACT,UAAU,CAAC,UAAU,GAAI,SAAU,KAAK;gBACpC,OAAO,aAAa,OAAO,QAAQ;YACvC;QACJ,OACK;YACD,UAAU,CAAC,UAAU,GAAG;QAC5B;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/make-cancellable-promise/dist/esm/index.js"], "sourcesContent": ["export default function makeCancellablePromise(promise) {\n    var isCancelled = false;\n    var wrappedPromise = new Promise(function (resolve, reject) {\n        promise\n            .then(function (value) { return !isCancelled && resolve(value); })\n            .catch(function (error) { return !isCancelled && reject(error); });\n    });\n    return {\n        promise: wrappedPromise,\n        cancel: function () {\n            isCancelled = true;\n        },\n    };\n}\n"], "names": [], "mappings": ";;;AAAe,SAAS,uBAAuB,OAAO;IAClD,IAAI,cAAc;IAClB,IAAI,iBAAiB,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;QACtD,QACK,IAAI,CAAC,SAAU,KAAK;YAAI,OAAO,CAAC,eAAe,QAAQ;QAAQ,GAC/D,KAAK,CAAC,SAAU,KAAK;YAAI,OAAO,CAAC,eAAe,OAAO;QAAQ;IACxE;IACA,OAAO;QACH,SAAS;QACT,QAAQ;YACJ,cAAc;QAClB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/tiny-invariant/dist/esm/tiny-invariant.js"], "sourcesContent": ["var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n"], "names": [], "mappings": ";;;AAAmB;AAAnB,IAAI,eAAe,oDAAyB;AAC5C,IAAI,SAAS;AACb,SAAS,UAAU,SAAS,EAAE,OAAO;IACjC,IAAI,WAAW;QACX;IACJ;IACA;;IAGA,IAAI,WAAW,OAAO,YAAY,aAAa,YAAY;IAC3D,IAAI,QAAQ,WAAW,GAAG,MAAM,CAAC,QAAQ,MAAM,MAAM,CAAC,YAAY;IAClE,MAAM,IAAI,MAAM;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/warning/warning.js"], "sourcesContent": ["/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\n\nvar __DEV__ = process.env.NODE_ENV !== 'production';\n\nvar warning = function() {};\n\nif (__DEV__) {\n  var printWarning = function printWarning(format, args) {\n    var len = arguments.length;\n    args = new Array(len > 1 ? len - 1 : 0);\n    for (var key = 1; key < len; key++) {\n      args[key - 1] = arguments[key];\n    }\n    var argIndex = 0;\n    var message = 'Warning: ' +\n      format.replace(/%s/g, function() {\n        return args[argIndex++];\n      });\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  }\n\n  warning = function(condition, format, args) {\n    var len = arguments.length;\n    args = new Array(len > 2 ? len - 2 : 0);\n    for (var key = 2; key < len; key++) {\n      args[key - 2] = arguments[key];\n    }\n    if (format === undefined) {\n      throw new Error(\n          '`warning(condition, format, ...args)` requires a warning ' +\n          'message argument'\n      );\n    }\n    if (!condition) {\n      printWarning.apply(null, [format].concat(args));\n    }\n  };\n}\n\nmodule.exports = warning;\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAWa;AATd;AAEA;;;;;CAKC,GAED,IAAI,UAAU,oDAAyB;AAEvC,IAAI,UAAU,YAAY;AAE1B,wCAAa;IACX,IAAI,eAAe,SAAS,aAAa,MAAM,EAAE,IAAI;QACnD,IAAI,MAAM,UAAU,MAAM;QAC1B,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI;QACrC,IAAK,IAAI,MAAM,GAAG,MAAM,KAAK,MAAO;YAClC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI;QAChC;QACA,IAAI,WAAW;QACf,IAAI,UAAU,cACZ,OAAO,OAAO,CAAC,OAAO;YACpB,OAAO,IAAI,CAAC,WAAW;QACzB;QACF,IAAI,OAAO,YAAY,aAAa;YAClC,QAAQ,KAAK,CAAC;QAChB;QACA,IAAI;YACF,qCAAqC;YACrC,wEAAwE;YACxE,yDAAyD;YACzD,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,GAAG,CAAC;IACf;IAEA,UAAU,SAAS,SAAS,EAAE,MAAM,EAAE,IAAI;QACxC,IAAI,MAAM,UAAU,MAAM;QAC1B,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI;QACrC,IAAK,IAAI,MAAM,GAAG,MAAM,KAAK,MAAO;YAClC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI;QAChC;QACA,IAAI,WAAW,WAAW;YACxB,MAAM,IAAI,MACN,8DACA;QAEN;QACA,IAAI,CAAC,WAAW;YACd,aAAa,KAAK,CAAC,MAAM;gBAAC;aAAO,CAAC,MAAM,CAAC;QAC3C;IACF;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_apply_descriptor_get.js"], "sourcesContent": ["function _class_apply_descriptor_get(receiver, descriptor) {\n    if (descriptor.get) return descriptor.get.call(receiver);\n\n    return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU;IACrD,IAAI,WAAW,GAAG,EAAE,OAAO,WAAW,GAAG,CAAC,IAAI,CAAC;IAE/C,OAAO,WAAW,KAAK;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_extract_field_descriptor.js"], "sourcesContent": ["function _class_extract_field_descriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n\n    return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gCAAgC,QAAQ,EAAE,UAAU,EAAE,MAAM;IACjE,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU,kBAAkB,SAAS;IAE9E,OAAO,WAAW,GAAG,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_field_get.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_get(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"get\");\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_private_field_get as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU;IAClD,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,OAAO,CAAA,GAAA,yKAAA,CAAA,IAA2B,AAAD,EAAE,UAAU;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_check_private_redeclaration.js"], "sourcesContent": ["function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,6BAA6B,GAAG,EAAE,iBAAiB;IACxD,IAAI,kBAAkB,GAAG,CAAC,MAAM;QAC5B,MAAM,IAAI,UAAU;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_field_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,0BAA0B,GAAG,EAAE,UAAU,EAAE,KAAK;IACrD,CAAA,GAAA,0KAAA,CAAA,IAA4B,AAAD,EAAE,KAAK;IAClC,WAAW,GAAG,CAAC,KAAK;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_apply_descriptor_set.js"], "sourcesContent": ["function _class_apply_descriptor_set(receiver, descriptor, value) {\n    if (descriptor.set) descriptor.set.call(receiver, value);\n    else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n        descriptor.value = value;\n    }\n}\nexport { _class_apply_descriptor_set as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU,EAAE,KAAK;IAC5D,IAAI,WAAW,GAAG,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;SAC7C;QACD,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QACA,WAAW,KAAK,GAAG;IACvB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_field_set.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_set(receiver, privateMap, value) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n    return value;\n}\nexport { _class_private_field_set as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU,EAAE,KAAK;IACzD,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,CAAA,GAAA,yKAAA,CAAA,IAA2B,AAAD,EAAE,UAAU,YAAY;IAClD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_apply_descriptor_update.js"], "sourcesContent": ["function _class_apply_descriptor_update(receiver, descriptor) {\n    if (descriptor.set) {\n        if (!descriptor.get) throw new TypeError(\"attempted to read set only private field\");\n\n        if (!(\"__destrWrapper\" in descriptor)) {\n            descriptor.__destrWrapper = {\n                set value(v) {\n                    descriptor.set.call(receiver, v);\n                },\n                get value() {\n                    return descriptor.get.call(receiver);\n                }\n            };\n        }\n\n        return descriptor.__destrWrapper;\n    } else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n\n        return descriptor;\n    }\n}\nexport { _class_apply_descriptor_update as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,+BAA+B,QAAQ,EAAE,UAAU;IACxD,IAAI,WAAW,GAAG,EAAE;QAChB,IAAI,CAAC,WAAW,GAAG,EAAE,MAAM,IAAI,UAAU;QAEzC,IAAI,CAAC,CAAC,oBAAoB,UAAU,GAAG;YACnC,WAAW,cAAc,GAAG;gBACxB,IAAI,OAAM,EAAG;oBACT,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;gBAClC;gBACA,IAAI,SAAQ;oBACR,OAAO,WAAW,GAAG,CAAC,IAAI,CAAC;gBAC/B;YACJ;QACJ;QAEA,OAAO,WAAW,cAAc;IACpC,OAAO;QACH,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QAEA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_field_update.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_update } from \"./_class_apply_descriptor_update.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_update(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"update\");\n    return _class_apply_descriptor_update(receiver, descriptor);\n}\nexport { _class_private_field_update as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,4BAA4B,QAAQ,EAAE,UAAU;IACrD,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,OAAO,CAAA,GAAA,4KAAA,CAAA,IAA8B,AAAD,EAAE,UAAU;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_method_get.js"], "sourcesContent": ["function _class_private_method_get(receiver, privateSet, fn) {\n    if (!privateSet.has(receiver)) throw new TypeError(\"attempted to get private field on non-instance\");\n\n    return fn;\n}\nexport { _class_private_method_get as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,0BAA0B,QAAQ,EAAE,UAAU,EAAE,EAAE;IACvD,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IAEnD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_method_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_method_init(obj, privateSet) {\n    _check_private_redeclaration(obj, privateSet);\n    privateSet.add(obj);\n}\nexport { _class_private_method_init as _ };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,2BAA2B,GAAG,EAAE,UAAU;IAC/C,CAAA,GAAA,0KAAA,CAAA,IAA4B,AAAD,EAAE,KAAK;IAClC,WAAW,GAAG,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_check_private_static_access.js"], "sourcesContent": ["function _class_check_private_static_access(receiver, classConstructor) {\n    if (receiver !== classConstructor) throw new TypeError(\"Private static access of wrong provenance\");\n}\nexport { _class_check_private_static_access as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,mCAAmC,QAAQ,EAAE,gBAAgB;IAClE,IAAI,aAAa,kBAAkB,MAAM,IAAI,UAAU;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_check_private_static_field_descriptor.js"], "sourcesContent": ["function _class_check_private_static_field_descriptor(descriptor, action) {\n    if (descriptor === undefined) {\n        throw new TypeError(\"attempted to \" + action + \" private static field before its declaration\");\n    }\n}\nexport { _class_check_private_static_field_descriptor as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,6CAA6C,UAAU,EAAE,MAAM;IACpE,IAAI,eAAe,WAAW;QAC1B,MAAM,IAAI,UAAU,kBAAkB,SAAS;IACnD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_static_private_field_spec_get.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_check_private_static_access } from \"./_class_check_private_static_access.js\";\nimport { _ as _class_check_private_static_field_descriptor } from \"./_class_check_private_static_field_descriptor.js\";\n\nfunction _class_static_private_field_spec_get(receiver, classConstructor, descriptor) {\n    _class_check_private_static_access(receiver, classConstructor);\n    _class_check_private_static_field_descriptor(descriptor, \"get\");\n\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_static_private_field_spec_get as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,qCAAqC,QAAQ,EAAE,gBAAgB,EAAE,UAAU;IAChF,CAAA,GAAA,gLAAA,CAAA,IAAkC,AAAD,EAAE,UAAU;IAC7C,CAAA,GAAA,0LAAA,CAAA,IAA4C,AAAD,EAAE,YAAY;IAEzD,OAAO,CAAA,GAAA,yKAAA,CAAA,IAA2B,AAAD,EAAE,UAAU;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_static_private_field_spec_set.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_check_private_static_access } from \"./_class_check_private_static_access.js\";\nimport { _ as _class_check_private_static_field_descriptor } from \"./_class_check_private_static_field_descriptor.js\";\n\nfunction _class_static_private_field_spec_set(receiver, classConstructor, descriptor, value) {\n    _class_check_private_static_access(receiver, classConstructor);\n    _class_check_private_static_field_descriptor(descriptor, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n\n    return value;\n}\nexport { _class_static_private_field_spec_set as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,qCAAqC,QAAQ,EAAE,gBAAgB,EAAE,UAAU,EAAE,KAAK;IACvF,CAAA,GAAA,gLAAA,CAAA,IAAkC,AAAD,EAAE,UAAU;IAC7C,CAAA,GAAA,0LAAA,CAAA,IAA4C,AAAD,EAAE,YAAY;IACzD,CAAA,GAAA,yKAAA,CAAA,IAA2B,AAAD,EAAE,UAAU,YAAY;IAElD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_static_private_field_update.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_update } from \"./_class_apply_descriptor_update.js\";\nimport { _ as _class_check_private_static_access } from \"./_class_check_private_static_access.js\";\nimport { _ as _class_check_private_static_field_descriptor } from \"./_class_check_private_static_field_descriptor.js\";\n\nfunction _class_static_private_field_update(receiver, classConstructor, descriptor) {\n    _class_check_private_static_access(receiver, classConstructor);\n    _class_check_private_static_field_descriptor(descriptor, \"update\");\n\n    return _class_apply_descriptor_update(receiver, descriptor);\n}\nexport { _class_static_private_field_update as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,mCAAmC,QAAQ,EAAE,gBAAgB,EAAE,UAAU;IAC9E,CAAA,GAAA,gLAAA,CAAA,IAAkC,AAAD,EAAE,UAAU;IAC7C,CAAA,GAAA,0LAAA,CAAA,IAA4C,AAAD,EAAE,YAAY;IAEzD,OAAO,CAAA,GAAA,4KAAA,CAAA,IAA8B,AAAD,EAAE,UAAU;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_apply_descriptor_destructure.js"], "sourcesContent": ["function _class_apply_descriptor_destructure(receiver, descriptor) {\n    if (descriptor.set) {\n        if (!(\"__destrObj\" in descriptor)) {\n            descriptor.__destrObj = {\n                set value(v) {\n                    descriptor.set.call(receiver, v);\n                }\n            };\n        }\n\n        return descriptor.__destrObj;\n    } else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n\n        return descriptor;\n    }\n}\nexport { _class_apply_descriptor_destructure as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,oCAAoC,QAAQ,EAAE,UAAU;IAC7D,IAAI,WAAW,GAAG,EAAE;QAChB,IAAI,CAAC,CAAC,gBAAgB,UAAU,GAAG;YAC/B,WAAW,UAAU,GAAG;gBACpB,IAAI,OAAM,EAAG;oBACT,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;gBAClC;YACJ;QACJ;QAEA,OAAO,WAAW,UAAU;IAChC,OAAO;QACH,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QAEA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_private_field_destructure.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_destructure } from \"./_class_apply_descriptor_destructure.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_destructure(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    return _class_apply_descriptor_destructure(receiver, descriptor);\n}\nexport { _class_private_field_destructure as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,iCAAiC,QAAQ,EAAE,UAAU;IAC1D,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAmC,AAAD,EAAE,UAAU;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40swc/helpers/esm/_class_static_private_method_get.js"], "sourcesContent": ["import { _ as _class_check_private_static_access } from \"./_class_check_private_static_access.js\";\n\nfunction _class_static_private_method_get(receiver, classConstructor, method) {\n    _class_check_private_static_access(receiver, classConstructor);\n\n    return method;\n}\nexport { _class_static_private_method_get as _ };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,iCAAiC,QAAQ,EAAE,gBAAgB,EAAE,MAAM;IACxE,CAAA,GAAA,gLAAA,CAAA,IAAkC,AAAD,EAAE,UAAU;IAE7C,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/merge-refs/dist/esm/index.js"], "sourcesContent": ["/**\n * A function that merges React refs into one.\n * Supports both functions and ref objects created using createRef() and useRef().\n *\n * Usage:\n * ```tsx\n * <div ref={mergeRefs(ref1, ref2, ref3)} />\n * ```\n *\n * @param {(React.Ref<T> | undefined)[]} inputRefs Array of refs\n * @returns {React.Ref<T> | React.RefCallback<T>} Merged refs\n */\nexport default function mergeRefs() {\n    var inputRefs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        inputRefs[_i] = arguments[_i];\n    }\n    var filteredInputRefs = inputRefs.filter(Boolean);\n    if (filteredInputRefs.length <= 1) {\n        var firstRef = filteredInputRefs[0];\n        return firstRef || null;\n    }\n    return function mergedRefs(ref) {\n        filteredInputRefs.forEach(function (inputRef) {\n            if (typeof inputRef === 'function') {\n                inputRef(ref);\n            }\n            else if (inputRef) {\n                inputRef.current = ref;\n            }\n        });\n    };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;AACc,SAAS;IACpB,IAAI,YAAY,EAAE;IAClB,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;QAC1C,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;IACjC;IACA,IAAI,oBAAoB,UAAU,MAAM,CAAC;IACzC,IAAI,kBAAkB,MAAM,IAAI,GAAG;QAC/B,IAAI,WAAW,iBAAiB,CAAC,EAAE;QACnC,OAAO,YAAY;IACvB;IACA,OAAO,SAAS,WAAW,GAAG;QAC1B,kBAAkB,OAAO,CAAC,SAAU,QAAQ;YACxC,IAAI,OAAO,aAAa,YAAY;gBAChC,SAAS;YACb,OACK,IAAI,UAAU;gBACf,SAAS,OAAO,GAAG;YACvB;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}]}
(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/pdf/PDFDocument.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/017e3_pdfjs-dist_build_pdf_mjs_8faa9d3a._.js",
  "static/chunks/017e3_pdfjs-dist_build_pdf_mjs_6d6045aa._.js",
  "static/chunks/node_modules_react-pdf_dist_06b4de16._.js",
  "static/chunks/node_modules_8b9ce858._.js",
  "static/chunks/src_ae3b111f._.js",
  {
    "path": "static/chunks/node_modules_react-pdf_dist_Page_6e5cdb6d._.css",
    "included": [
      "[project]/node_modules/react-pdf/dist/Page/AnnotationLayer.css [app-client] (css)",
      "[project]/node_modules/react-pdf/dist/Page/TextLayer.css [app-client] (css)"
    ],
    "moduleChunks": [
      "static/chunks/node_modules_react-pdf_dist_Page_AnnotationLayer_css_e59ae46c._.single.css",
      "static/chunks/node_modules_react-pdf_dist_Page_TextLayer_css_e59ae46c._.single.css"
    ]
  },
  "static/chunks/src_components_pdf_PDFDocument_tsx_bbee5312._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/pdf/PDFDocument.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);
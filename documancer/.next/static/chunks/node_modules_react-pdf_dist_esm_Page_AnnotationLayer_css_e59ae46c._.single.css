/* [project]/node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css [app-client] (css) */
:root {
  --react-pdf-annotation-layer: 1;
  --annotation-unfocused-field-background: url("data:image/svg+xml;charset=UTF-8,<svg width='1px' height='1px' xmlns='http://www.w3.org/2000/svg'><rect width='100%' height='100%' style='fill:rgba(0, 54, 255, 0.13);'/></svg>");
  --input-focus-border-color: Highlight;
  --input-focus-outline: 1px solid Canvas;
  --input-unfocused-border-color: transparent;
  --input-disabled-border-color: transparent;
  --input-hover-border-color: black;
  --link-outline: none;
}

@media screen and (forced-colors: active) {
  :root {
    --input-focus-border-color: CanvasText;
    --input-unfocused-border-color: ActiveText;
    --input-disabled-border-color: GrayText;
    --input-hover-border-color: Highlight;
    --link-outline: 1.5px solid LinkText;
  }

  .annotationLayer .choiceWidgetAnnotation select:required {
    outline: 1.5px solid selecteditem;
  }

  .annotationLayer .textWidgetAnnotation :-webkit-any(input, textarea):required {
    outline: 1.5px solid selecteditem;
  }

  .annotationLayer .textWidgetAnnotation :-moz-any(input, textarea):required {
    outline: 1.5px solid selecteditem;
  }

  .annotationLayer .textWidgetAnnotation :is(input, textarea):required {
    outline: 1.5px solid selecteditem;
  }

  .annotationLayer .buttonWidgetAnnotation:-webkit-any(.checkBox, .radioButton) input:required {
    outline: 1.5px solid selecteditem;
  }

  .annotationLayer .buttonWidgetAnnotation:-moz-any(.checkBox, .radioButton) input:required {
    outline: 1.5px solid selecteditem;
  }

  .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:required {
    outline: 1.5px solid selecteditem;
  }

  .annotationLayer .linkAnnotation:hover {
    -webkit-backdrop-filter: invert();
    backdrop-filter: invert();
  }
}

.annotationLayer {
  pointer-events: none;
  transform-origin: 0 0;
  z-index: 3;
  position: absolute;
  top: 0;
  left: 0;
}

.annotationLayer[data-main-rotation="90"] .norotate {
  transform: rotate(270deg)translateX(-100%);
}

.annotationLayer[data-main-rotation="180"] .norotate {
  transform: rotate(180deg)translate(-100%, -100%);
}

.annotationLayer[data-main-rotation="270"] .norotate {
  transform: rotate(90deg)translateY(-100%);
}

.annotationLayer canvas {
  width: 100%;
  height: 100%;
  position: absolute;
}

.annotationLayer section {
  text-align: initial;
  pointer-events: auto;
  box-sizing: border-box;
  transform-origin: 0 0;
  margin: 0;
  position: absolute;
}

.annotationLayer .linkAnnotation {
  outline: var(--link-outline);
}

.annotationLayer :-webkit-any(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a {
  width: 100%;
  height: 100%;
  font-size: 1em;
  position: absolute;
  top: 0;
  left: 0;
}

.annotationLayer :-moz-any(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a {
  width: 100%;
  height: 100%;
  font-size: 1em;
  position: absolute;
  top: 0;
  left: 0;
}

.annotationLayer :is(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a {
  width: 100%;
  height: 100%;
  font-size: 1em;
  position: absolute;
  top: 0;
  left: 0;
}

.annotationLayer :-webkit-any(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a:hover {
  opacity: .2;
  background: #ff0;
  box-shadow: 0 2px 10px #ff0;
}

.annotationLayer :-moz-any(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a:hover {
  opacity: .2;
  background: #ff0;
  box-shadow: 0 2px 10px #ff0;
}

.annotationLayer :is(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a:hover {
  opacity: .2;
  background: #ff0;
  box-shadow: 0 2px 10px #ff0;
}

.annotationLayer .textAnnotation img {
  cursor: pointer;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.annotationLayer .choiceWidgetAnnotation select {
  background-image: var(--annotation-unfocused-field-background);
  border: 2px solid var(--input-unfocused-border-color);
  box-sizing: border-box;
  font: calc(9px * var(--scale-factor)) sans-serif;
  vertical-align: top;
  width: 100%;
  height: 100%;
  margin: 0;
}

.annotationLayer .textWidgetAnnotation :-webkit-any(input, textarea) {
  background-image: var(--annotation-unfocused-field-background);
  border: 2px solid var(--input-unfocused-border-color);
  box-sizing: border-box;
  font: calc(9px * var(--scale-factor)) sans-serif;
  vertical-align: top;
  width: 100%;
  height: 100%;
  margin: 0;
}

.annotationLayer .textWidgetAnnotation :-moz-any(input, textarea) {
  background-image: var(--annotation-unfocused-field-background);
  border: 2px solid var(--input-unfocused-border-color);
  box-sizing: border-box;
  font: calc(9px * var(--scale-factor)) sans-serif;
  vertical-align: top;
  width: 100%;
  height: 100%;
  margin: 0;
}

.annotationLayer .textWidgetAnnotation :is(input, textarea) {
  background-image: var(--annotation-unfocused-field-background);
  border: 2px solid var(--input-unfocused-border-color);
  box-sizing: border-box;
  font: calc(9px * var(--scale-factor)) sans-serif;
  vertical-align: top;
  width: 100%;
  height: 100%;
  margin: 0;
}

.annotationLayer .buttonWidgetAnnotation:-webkit-any(.checkBox, .radioButton) input {
  background-image: var(--annotation-unfocused-field-background);
  border: 2px solid var(--input-unfocused-border-color);
  box-sizing: border-box;
  font: calc(9px * var(--scale-factor)) sans-serif;
  vertical-align: top;
  width: 100%;
  height: 100%;
  margin: 0;
}

.annotationLayer .buttonWidgetAnnotation:-moz-any(.checkBox, .radioButton) input {
  background-image: var(--annotation-unfocused-field-background);
  border: 2px solid var(--input-unfocused-border-color);
  box-sizing: border-box;
  font: calc(9px * var(--scale-factor)) sans-serif;
  vertical-align: top;
  width: 100%;
  height: 100%;
  margin: 0;
}

.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input {
  background-image: var(--annotation-unfocused-field-background);
  border: 2px solid var(--input-unfocused-border-color);
  box-sizing: border-box;
  font: calc(9px * var(--scale-factor)) sans-serif;
  vertical-align: top;
  width: 100%;
  height: 100%;
  margin: 0;
}

.annotationLayer .choiceWidgetAnnotation select:required {
  outline: 1.5px solid red;
}

.annotationLayer .textWidgetAnnotation :-webkit-any(input, textarea):required {
  outline: 1.5px solid red;
}

.annotationLayer .textWidgetAnnotation :-moz-any(input, textarea):required {
  outline: 1.5px solid red;
}

.annotationLayer .textWidgetAnnotation :is(input, textarea):required {
  outline: 1.5px solid red;
}

.annotationLayer .buttonWidgetAnnotation:-webkit-any(.checkBox, .radioButton) input:required {
  outline: 1.5px solid red;
}

.annotationLayer .buttonWidgetAnnotation:-moz-any(.checkBox, .radioButton) input:required {
  outline: 1.5px solid red;
}

.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:required {
  outline: 1.5px solid red;
}

.annotationLayer .choiceWidgetAnnotation select option {
  padding: 0;
}

.annotationLayer .buttonWidgetAnnotation.radioButton input {
  border-radius: 50%;
}

.annotationLayer .textWidgetAnnotation textarea {
  resize: none;
}

.annotationLayer .choiceWidgetAnnotation select[disabled] {
  border: 2px solid var(--input-disabled-border-color);
  cursor: not-allowed;
  background: none;
}

.annotationLayer .textWidgetAnnotation :-webkit-any(input, textarea)[disabled] {
  border: 2px solid var(--input-disabled-border-color);
  cursor: not-allowed;
  background: none;
}

.annotationLayer .textWidgetAnnotation :-moz-any(input, textarea)[disabled] {
  border: 2px solid var(--input-disabled-border-color);
  cursor: not-allowed;
  background: none;
}

.annotationLayer .textWidgetAnnotation :is(input, textarea)[disabled] {
  border: 2px solid var(--input-disabled-border-color);
  cursor: not-allowed;
  background: none;
}

.annotationLayer .buttonWidgetAnnotation:-webkit-any(.checkBox, .radioButton) input[disabled] {
  border: 2px solid var(--input-disabled-border-color);
  cursor: not-allowed;
  background: none;
}

.annotationLayer .buttonWidgetAnnotation:-moz-any(.checkBox, .radioButton) input[disabled] {
  border: 2px solid var(--input-disabled-border-color);
  cursor: not-allowed;
  background: none;
}

.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input[disabled] {
  border: 2px solid var(--input-disabled-border-color);
  cursor: not-allowed;
  background: none;
}

.annotationLayer .choiceWidgetAnnotation select:hover {
  border: 2px solid var(--input-hover-border-color);
}

.annotationLayer .textWidgetAnnotation :-webkit-any(input, textarea):hover {
  border: 2px solid var(--input-hover-border-color);
}

.annotationLayer .textWidgetAnnotation :-moz-any(input, textarea):hover {
  border: 2px solid var(--input-hover-border-color);
}

.annotationLayer .textWidgetAnnotation :is(input, textarea):hover {
  border: 2px solid var(--input-hover-border-color);
}

.annotationLayer .buttonWidgetAnnotation:-webkit-any(.checkBox, .radioButton) input:hover {
  border: 2px solid var(--input-hover-border-color);
}

.annotationLayer .buttonWidgetAnnotation:-moz-any(.checkBox, .radioButton) input:hover {
  border: 2px solid var(--input-hover-border-color);
}

.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:hover {
  border: 2px solid var(--input-hover-border-color);
}

.annotationLayer .choiceWidgetAnnotation select:hover, .annotationLayer .buttonWidgetAnnotation.checkBox input:hover {
  border-radius: 2px;
}

.annotationLayer .textWidgetAnnotation :-webkit-any(input, textarea):hover {
  border-radius: 2px;
}

.annotationLayer .textWidgetAnnotation :-moz-any(input, textarea):hover {
  border-radius: 2px;
}

.annotationLayer .textWidgetAnnotation :is(input, textarea):hover {
  border-radius: 2px;
}

.annotationLayer .choiceWidgetAnnotation select:focus {
  border: 2px solid var(--input-focus-border-color);
  outline: var(--input-focus-outline);
  background: none;
  border-radius: 2px;
}

.annotationLayer .textWidgetAnnotation :-webkit-any(input, textarea):focus {
  border: 2px solid var(--input-focus-border-color);
  outline: var(--input-focus-outline);
  background: none;
  border-radius: 2px;
}

.annotationLayer .textWidgetAnnotation :-moz-any(input, textarea):focus {
  border: 2px solid var(--input-focus-border-color);
  outline: var(--input-focus-outline);
  background: none;
  border-radius: 2px;
}

.annotationLayer .textWidgetAnnotation :is(input, textarea):focus {
  border: 2px solid var(--input-focus-border-color);
  outline: var(--input-focus-outline);
  background: none;
  border-radius: 2px;
}

.annotationLayer .buttonWidgetAnnotation:-webkit-any(.checkBox, .radioButton) :focus {
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

.annotationLayer .buttonWidgetAnnotation:-moz-any(.checkBox, .radioButton) :focus {
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) :focus {
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

.annotationLayer .buttonWidgetAnnotation.checkBox :focus {
  border: 2px solid var(--input-focus-border-color);
  outline: var(--input-focus-outline);
  border-radius: 2px;
}

.annotationLayer .buttonWidgetAnnotation.radioButton :focus {
  border: 2px solid var(--input-focus-border-color);
  outline: var(--input-focus-outline);
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before, .annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after, .annotationLayer .buttonWidgetAnnotation.radioButton input:checked:before {
  content: "";
  background-color: canvastext;
  display: block;
  position: absolute;
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before, .annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after {
  width: 1px;
  height: 80%;
  left: 45%;
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before {
  transform: rotate(45deg);
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after {
  transform: rotate(-45deg);
}

.annotationLayer .buttonWidgetAnnotation.radioButton input:checked:before {
  border-radius: 50%;
  width: 50%;
  height: 50%;
  top: 20%;
  left: 30%;
}

.annotationLayer .textWidgetAnnotation input.comb {
  padding-left: 2px;
  padding-right: 0;
  font-family: monospace;
}

.annotationLayer .textWidgetAnnotation input.comb:focus {
  width: 103%;
}

.annotationLayer .buttonWidgetAnnotation:-webkit-any(.checkBox, .radioButton) input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.annotationLayer .buttonWidgetAnnotation:-moz-any(.checkBox, .radioButton) input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.annotationLayer .popupTriggerArea {
  width: 100%;
  height: 100%;
}

.annotationLayer .fileAttachmentAnnotation .popupTriggerArea {
  position: absolute;
}

.annotationLayer .popupWrapper {
  font-size: calc(9px * var(--scale-factor));
  width: 100%;
  min-width: calc(180px * var(--scale-factor));
  pointer-events: none;
  position: absolute;
}

.annotationLayer .popup {
  max-width: calc(180px * var(--scale-factor));
  box-shadow: 0 calc(2px * var(--scale-factor)) calc(5px * var(--scale-factor)) #888;
  border-radius: calc(2px * var(--scale-factor));
  padding: calc(6px * var(--scale-factor));
  margin-left: calc(5px * var(--scale-factor));
  cursor: pointer;
  font: message-box;
  white-space: normal;
  word-wrap: break-word;
  pointer-events: auto;
  background-color: #ff9;
  position: absolute;
}

.annotationLayer .popup > * {
  font-size: calc(9px * var(--scale-factor));
}

.annotationLayer .popup h1 {
  display: inline-block;
}

.annotationLayer .popupDate {
  margin-left: calc(5px * var(--scale-factor));
  display: inline-block;
}

.annotationLayer .popupContent {
  margin-top: calc(2px * var(--scale-factor));
  padding-top: calc(2px * var(--scale-factor));
  border-top: 1px solid #333;
}

.annotationLayer .richText > * {
  white-space: pre-wrap;
  font-size: calc(9px * var(--scale-factor));
}

.annotationLayer .highlightAnnotation, .annotationLayer .underlineAnnotation, .annotationLayer .squigglyAnnotation, .annotationLayer .strikeoutAnnotation, .annotationLayer .freeTextAnnotation, .annotationLayer .lineAnnotation svg line, .annotationLayer .squareAnnotation svg rect, .annotationLayer .circleAnnotation svg ellipse, .annotationLayer .polylineAnnotation svg polyline, .annotationLayer .polygonAnnotation svg polygon, .annotationLayer .caretAnnotation, .annotationLayer .inkAnnotation svg polyline, .annotationLayer .stampAnnotation, .annotationLayer .fileAttachmentAnnotation {
  cursor: pointer;
}

.annotationLayer section svg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.annotationLayer .annotationTextContent {
  opacity: 0;
  color: rgba(0, 0, 0, 0);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  pointer-events: none;
  width: 100%;
  height: 100%;
  position: absolute;
}

.annotationLayer .annotationTextContent span {
  width: 100%;
  display: inline-block;
}

/*# sourceMappingURL=node_modules_react-pdf_dist_esm_Page_AnnotationLayer_css_e59ae46c._.single.css.map*/
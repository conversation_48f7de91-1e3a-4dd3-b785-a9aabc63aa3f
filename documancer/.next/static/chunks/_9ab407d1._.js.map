{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/pdf/PDFDocument.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, Alert, Button } from 'antd';\nimport { DownloadOutlined, FullscreenOutlined } from '@ant-design/icons';\n\ninterface PDFDocumentProps {\n  file: string;\n  pageNumber: number;\n  scale?: number;\n  onLoadSuccess: (data: { numPages: number }) => void;\n  onLoadError: (error: Error) => void;\n  onPageLoadSuccess?: () => void;\n  onPageLoadError?: (error: Error) => void;\n}\n\nconst PDFDocument: React.FC<PDFDocumentProps> = ({\n  file,\n  pageNumber,\n  scale = 1.0,\n  onLoadSuccess,\n  onLoadError,\n  onPageLoadSuccess,\n  onPageLoadError,\n}) => {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [pdfUrl, setPdfUrl] = useState<string>('');\n\n  useEffect(() => {\n    // Construct the PDF URL\n    const url = file.startsWith('http') ? file : `/api/files/${file}`;\n    setPdfUrl(url);\n\n    // Simulate loading success with estimated page count\n    setTimeout(() => {\n      setLoading(false);\n      onLoadSuccess({ numPages: 10 }); // Default estimate\n      onPageLoadSuccess?.();\n    }, 1000);\n  }, [file, onLoadSuccess, onPageLoadSuccess]);\n\n  const handleIframeLoad = () => {\n    setLoading(false);\n    setError(null);\n  };\n\n  const handleIframeError = () => {\n    setLoading(false);\n    setError('Failed to load PDF document');\n    onLoadError(new Error('Failed to load PDF document'));\n  };\n\n  const handleDownload = () => {\n    const link = document.createElement('a');\n    link.href = pdfUrl;\n    link.download = file.split('/').pop() || 'document.pdf';\n    link.click();\n  };\n\n  const handleFullscreen = () => {\n    window.open(pdfUrl, '_blank');\n  };\n\n  if (error) {\n    return (\n      <div className=\"pdf-document-container\">\n        <Alert\n          message=\"Failed to load PDF\"\n          description={\n            <div>\n              <p>{error}</p>\n              <div className=\"mt-3 space-x-2\">\n                <Button icon={<DownloadOutlined />} onClick={handleDownload}>\n                  Download PDF\n                </Button>\n                <Button icon={<FullscreenOutlined />} onClick={handleFullscreen}>\n                  Open in New Tab\n                </Button>\n              </div>\n            </div>\n          }\n          type=\"error\"\n          showIcon\n          className=\"m-4\"\n        />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"pdf-document-container\">\n      {loading && (\n        <div className=\"flex justify-center items-center h-96\">\n          <Spin size=\"large\" />\n          <span className=\"ml-3\">Loading PDF...</span>\n        </div>\n      )}\n\n      <div className={`pdf-viewer-wrapper ${loading ? 'hidden' : ''}`}>\n        <iframe\n          src={`${pdfUrl}#page=${pageNumber}&zoom=${Math.round(scale * 100)}`}\n          width=\"100%\"\n          height=\"800px\"\n          style={{\n            border: 'none',\n            borderRadius: '8px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n          }}\n          onLoad={handleIframeLoad}\n          onError={handleIframeError}\n          title=\"PDF Document\"\n        />\n\n        <div className=\"pdf-controls mt-4 text-center space-x-2\">\n          <Button icon={<DownloadOutlined />} onClick={handleDownload}>\n            Download\n          </Button>\n          <Button icon={<FullscreenOutlined />} onClick={handleFullscreen}>\n            Open in New Tab\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PDFDocument;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;;;AAJA;;;;AAgBA,MAAM,cAA0C;QAAC,EAC/C,IAAI,EACJ,UAAU,EACV,QAAQ,GAAG,EACX,aAAa,EACb,WAAW,EACX,iBAAiB,EACjB,eAAe,EAChB;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,wBAAwB;YACxB,MAAM,MAAM,KAAK,UAAU,CAAC,UAAU,OAAO,AAAC,cAAkB,OAAL;YAC3D,UAAU;YAEV,qDAAqD;YACrD;yCAAW;oBACT,WAAW;oBACX,cAAc;wBAAE,UAAU;oBAAG,IAAI,mBAAmB;oBACpD,8BAAA,wCAAA;gBACF;wCAAG;QACL;gCAAG;QAAC;QAAM;QAAe;KAAkB;IAE3C,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;IACX;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,SAAS;QACT,YAAY,IAAI,MAAM;IACxB;IAEA,MAAM,iBAAiB;QACrB,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,MAAM;QACzC,KAAK,KAAK;IACZ;IAEA,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,2BACE,6LAAC;;sCACC,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oCAAK,SAAS;8CAAgB;;;;;;8CAG7D,6LAAC,qMAAA,CAAA,SAAM;oCAAC,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;oCAAK,SAAS;8CAAkB;;;;;;;;;;;;;;;;;;gBAMvE,MAAK;gBACL,QAAQ;gBACR,WAAU;;;;;;;;;;;IAIlB;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iLAAA,CAAA,OAAI;wBAAC,MAAK;;;;;;kCACX,6LAAC;wBAAK,WAAU;kCAAO;;;;;;;;;;;;0BAI3B,6LAAC;gBAAI,WAAW,AAAC,sBAA6C,OAAxB,UAAU,WAAW;;kCACzD,6LAAC;wBACC,KAAK,AAAC,GAAiB,OAAf,QAAO,UAA2B,OAAnB,YAAW,UAAgC,OAAxB,KAAK,KAAK,CAAC,QAAQ;wBAC7D,OAAM;wBACN,QAAO;wBACP,OAAO;4BACL,QAAQ;4BACR,cAAc;4BACd,WAAW;wBACb;wBACA,QAAQ;wBACR,SAAS;wBACT,OAAM;;;;;;kCAGR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,SAAM;gCAAC,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gCAAK,SAAS;0CAAgB;;;;;;0CAG7D,6LAAC,qMAAA,CAAA,SAAM;gCAAC,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;gCAAK,SAAS;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAO3E;GA7GM;KAAA;uCA+GS", "debugId": null}}]}
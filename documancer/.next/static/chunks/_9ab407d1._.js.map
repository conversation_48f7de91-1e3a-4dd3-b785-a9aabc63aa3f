{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/pdf/PDFDocument.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { Spin, Card, Typography, Alert } from 'antd';\n\nconst { Title, Paragraph } = Typography;\n\ninterface PDFDocumentProps {\n  file: string;\n  pageNumber: number;\n  onLoadSuccess: (data: { numPages: number }) => void;\n  onLoadError: (error: Error) => void;\n}\n\nconst PDFDocument: React.FC<PDFDocumentProps> = ({\n  file,\n  pageNumber, // Currently unused in mock implementation\n  onLoadSuccess,\n  onLoadError,\n}) => {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [pdfContent, setPdfContent] = useState<string>('');\n\n  useEffect(() => {\n    // Simulate PDF loading and call success callback\n    const timer = setTimeout(() => {\n      try {\n        // Mock PDF content based on our test data\n        const mockContent = `A Comprehensive Study of Machine Learning Applications\n\nAuthors: <AUTHORS>\nDate: December 2024\n\nAbstract\n\nThis paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field. Our study encompasses both theoretical foundations and practical implementations, providing insights into the effectiveness of different approaches.\n\n1. Introduction\n\nMachine learning has revolutionized numerous fields of study, from natural language processing to computer vision. This paper aims to provide a thorough examination of how machine learning techniques are being applied in academic research contexts.\n\nThe rapid advancement of artificial intelligence has created new opportunities for research and development. Our analysis focuses on understanding the impact of these technologies on traditional research methodologies.\n\n2. Methodology\n\nOur research methodology combines systematic literature review with empirical analysis. We collected data from over 500 academic papers published between 2020 and 2024, focusing on machine learning applications across various disciplines.\n\nThe study employed both quantitative and qualitative analysis techniques to ensure comprehensive coverage of the research landscape.\n\n3. Key Findings\n\nOur analysis reveals several important trends:\n\n• Increased adoption of deep learning techniques in research\n• Growing emphasis on interpretable AI models\n• Rising importance of ethical considerations in ML research\n• Enhanced collaboration between different academic disciplines\n\nThese findings suggest a fundamental shift in how research is conducted and validated in the modern academic environment.\n\n4. Discussion\n\nThe implications of our research extend beyond technical improvements to fundamental changes in how research is conducted. The integration of machine learning tools has enabled researchers to process larger datasets and identify patterns that were previously undetectable.\n\nHowever, this technological advancement also raises important questions about research methodology, reproducibility, and the role of human expertise in the research process.\n\n5. Conclusion\n\nThis study demonstrates the transformative impact of machine learning on academic research. Future work should focus on developing more accessible tools and addressing ethical concerns while maintaining scientific rigor.\n\nThe findings contribute to our understanding of how emerging technologies can enhance research capabilities while preserving the fundamental principles of scientific inquiry.\n\nReferences\n\n[1] Smith, J. et al. (2023). \"Machine Learning in Academia: A Survey.\" Journal of Academic Computing, 45(2), 123-145.\n[2] Doe, J. (2024). \"Ethical AI in Research.\" Proceedings of the International Conference on AI Ethics, 67-89.\n[3] Johnson, A. (2023). \"Deep Learning Applications in Scientific Research.\" Nature Machine Intelligence, 12(3), 234-256.`;\n\n        setPdfContent(mockContent);\n        setLoading(false);\n        onLoadSuccess({ numPages: 1 });\n      } catch (err) {\n        const error = err as Error;\n        setError(error.message);\n        setLoading(false);\n        onLoadError(error);\n      }\n    }, 1000);\n\n    return () => clearTimeout(timer);\n  }, [file, onLoadSuccess, onLoadError]);\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-96\">\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Alert\n        message=\"Failed to load PDF\"\n        description={error}\n        type=\"error\"\n        showIcon\n      />\n    );\n  }\n\n  return (\n    <Card className=\"max-w-4xl mx-auto\">\n      <div className=\"prose prose-sm max-w-none\">\n        {pdfContent.split('\\n\\n').map((paragraph, index) => {\n          if (paragraph.trim().startsWith('#') || paragraph.trim().match(/^\\d+\\./)) {\n            return (\n              <Title key={index} level={paragraph.startsWith('##') ? 4 : 3} className=\"mt-6 mb-3\">\n                {paragraph.replace(/^#+\\s*/, '').replace(/^\\d+\\.\\s*/, '')}\n              </Title>\n            );\n          }\n\n          if (paragraph.trim().length === 0) {\n            return null;\n          }\n\n          return (\n            <Paragraph key={index} className=\"mb-4 leading-relaxed\">\n              {paragraph.split('\\n').map((line, lineIndex) => (\n                <span key={lineIndex}>\n                  {line}\n                  {lineIndex < paragraph.split('\\n').length - 1 && <br />}\n                </span>\n              ))}\n            </Paragraph>\n          );\n        })}\n      </div>\n    </Card>\n  );\n};\n\nexport default PDFDocument;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAKA,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AASvC,MAAM,cAA0C;QAAC,EAC/C,IAAI,EACJ,UAAU,EACV,aAAa,EACb,WAAW,EACZ;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,iDAAiD;YACjD,MAAM,QAAQ;+CAAW;oBACvB,IAAI;wBACF,0CAA0C;wBAC1C,MAAM,cAAe;wBAmDrB,cAAc;wBACd,WAAW;wBACX,cAAc;4BAAE,UAAU;wBAAE;oBAC9B,EAAE,OAAO,KAAK;wBACZ,MAAM,QAAQ;wBACd,SAAS,MAAM,OAAO;wBACtB,WAAW;wBACX,YAAY;oBACd;gBACF;8CAAG;YAEH;yCAAO,IAAM,aAAa;;QAC5B;gCAAG;QAAC;QAAM;QAAe;KAAY;IAErC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;gBAAC,MAAK;;;;;;;;;;;IAGjB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC,mLAAA,CAAA,QAAK;YACJ,SAAQ;YACR,aAAa;YACb,MAAK;YACL,QAAQ;;;;;;IAGd;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;sBACZ,WAAW,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,WAAW;gBACxC,IAAI,UAAU,IAAI,GAAG,UAAU,CAAC,QAAQ,UAAU,IAAI,GAAG,KAAK,CAAC,WAAW;oBACxE,qBACE,6LAAC;wBAAkB,OAAO,UAAU,UAAU,CAAC,QAAQ,IAAI;wBAAG,WAAU;kCACrE,UAAU,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,aAAa;uBAD5C;;;;;gBAIhB;gBAEA,IAAI,UAAU,IAAI,GAAG,MAAM,KAAK,GAAG;oBACjC,OAAO;gBACT;gBAEA,qBACE,6LAAC;oBAAsB,WAAU;8BAC9B,UAAU,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,0BAChC,6LAAC;;gCACE;gCACA,YAAY,UAAU,KAAK,CAAC,MAAM,MAAM,GAAG,mBAAK,6LAAC;;;;;;2BAFzC;;;;;mBAFC;;;;;YASpB;;;;;;;;;;;AAIR;GAjIM;KAAA;uCAmIS", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/app/api/files/%5Bfilename%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { readFile } from 'fs/promises';\nimport { join } from 'path';\nimport { existsSync } from 'fs';\n\nexport async function GET(\n  _request: NextRequest,\n  { params }: { params: Promise<{ filename: string }> }\n) {\n  try {\n    const { filename } = await params;\n    \n    // Validate filename to prevent directory traversal\n    if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\\\')) {\n      return NextResponse.json(\n        { success: false, error: 'Invalid filename' },\n        { status: 400 }\n      );\n    }\n\n    // Check if file exists\n    const uploadsDir = join(process.cwd(), 'uploads');\n    const filepath = join(uploadsDir, filename);\n    \n    if (!existsSync(filepath)) {\n      return NextResponse.json(\n        { success: false, error: 'File not found' },\n        { status: 404 }\n      );\n    }\n\n    // Read and return file\n    const fileBuffer = await readFile(filepath);\n    \n    return new NextResponse(fileBuffer, {\n      headers: {\n        'Content-Type': 'application/pdf',\n        'Content-Disposition': `inline; filename=\"${filename}\"`,\n        'Cache-Control': 'public, max-age=31536000',\n      },\n    });\n\n  } catch (error) {\n    console.error('File serving error:', error);\n    return NextResponse.json(\n      { success: false, error: 'Failed to serve file' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IACpB,QAAqB,EACrB,EAAE,MAAM,EAA6C;IAErD,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM;QAE3B,mDAAmD;QACnD,IAAI,CAAC,YAAY,SAAS,QAAQ,CAAC,SAAS,SAAS,QAAQ,CAAC,QAAQ,SAAS,QAAQ,CAAC,OAAO;YAC7F,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAmB,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,aAAa,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI;QACvC,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAElC,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,WAAW;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAiB,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,aAAa,MAAM,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE;QAElC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,YAAY;YAClC,SAAS;gBACP,gBAAgB;gBAChB,uBAAuB,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;gBACvD,iBAAiB;YACnB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAuB,GAChD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
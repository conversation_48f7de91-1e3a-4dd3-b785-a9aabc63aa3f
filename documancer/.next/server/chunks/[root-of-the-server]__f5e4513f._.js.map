{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/pdf-processor.ts"], "sourcesContent": ["// Dynamic import to avoid build issues with pdf-parse test files\nimport { Paper } from './types';\n\nexport interface PDFProcessingResult {\n  text: string;\n  metadata: {\n    title?: string;\n    author?: string;\n    subject?: string;\n    creator?: string;\n    producer?: string;\n    creationDate?: Date;\n    modificationDate?: Date;\n    pages: number;\n  };\n  pages: Array<{\n    pageNumber: number;\n    text: string;\n  }>;\n}\n\nexport class PDFProcessor {\n  static async processPDF(buffer: Buffer): Promise<PDFProcessingResult> {\n    try {\n      // Log buffer size for debugging\n      console.log('Processing PDF buffer of size:', buffer.length);\n\n      // For now, let's create a mock result to test the upload flow\n      // We'll implement real PDF parsing later\n      const mockData = {\n        numpages: 1,\n        text: `A Comprehensive Study of Machine Learning Applications\n\nAuthors: <AUTHORS>\nInstitution: University of Technology\nDate: December 2024\n\nAbstract\nThis paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.\n\n1. Introduction\nMachine learning has revolutionized numerous fields of study, from natural language processing to computer vision.\n\n2. Methodology\nOur research methodology combines systematic literature review with empirical analysis.\n\n3. Key Findings\n• Increased adoption of deep learning techniques in research\n• Growing emphasis on interpretable AI models\n• Rising importance of ethical considerations in ML research\n\n4. Conclusion\nThis study demonstrates the transformative impact of machine learning on academic research.`,\n        info: {\n          Title: 'A Comprehensive Study of Machine Learning Applications',\n          Author: 'Dr. <PERSON>, <PERSON>. Jane Doe, Prof. <PERSON>',\n          Subject: 'Machine Learning Research',\n          Creator: 'Test Creator',\n          Producer: 'Test Producer',\n          CreationDate: new Date(),\n          ModDate: new Date(),\n        }\n      };\n      \n      // Extract basic metadata\n      const metadata = {\n        title: mockData.info?.Title || undefined,\n        author: mockData.info?.Author || undefined,\n        subject: mockData.info?.Subject || undefined,\n        creator: mockData.info?.Creator || undefined,\n        producer: mockData.info?.Producer || undefined,\n        creationDate: mockData.info?.CreationDate ? new Date(mockData.info.CreationDate) : undefined,\n        modificationDate: mockData.info?.ModDate ? new Date(mockData.info.ModDate) : undefined,\n        pages: mockData.numpages,\n      };\n\n      // Split text into pages (this is a simplified approach)\n      const pages = this.splitTextIntoPages(mockData.text, mockData.numpages);\n\n      return {\n        text: mockData.text,\n        metadata,\n        pages,\n      };\n    } catch (error) {\n      console.error('Error processing PDF:', error);\n      throw new Error('Failed to process PDF file');\n    }\n  }\n\n  private static splitTextIntoPages(text: string, numPages: number): Array<{ pageNumber: number; text: string }> {\n    // This is a simplified page splitting - in a real implementation,\n    // you might want to use a more sophisticated PDF library that preserves page boundaries\n    const lines = text.split('\\n');\n    const linesPerPage = Math.ceil(lines.length / numPages);\n    const pages: Array<{ pageNumber: number; text: string }> = [];\n\n    for (let i = 0; i < numPages; i++) {\n      const startLine = i * linesPerPage;\n      const endLine = Math.min((i + 1) * linesPerPage, lines.length);\n      const pageText = lines.slice(startLine, endLine).join('\\n');\n      \n      pages.push({\n        pageNumber: i + 1,\n        text: pageText,\n      });\n    }\n\n    return pages;\n  }\n\n  static extractPaperMetadata(text: string, filename: string): Partial<Paper> {\n    // Extract title (usually the first significant line)\n    const lines = text.split('\\n').filter(line => line.trim().length > 0);\n    let title = filename.replace('.pdf', '');\n    \n    // Try to find a better title from the content\n    for (const line of lines.slice(0, 10)) {\n      if (line.length > 20 && line.length < 200 && !line.includes('@') && !line.includes('http')) {\n        title = line.trim();\n        break;\n      }\n    }\n\n    // Extract authors (look for common patterns)\n    const authors = this.extractAuthors(text);\n    \n    // Extract abstract\n    const abstract = this.extractAbstract(text);\n\n    // Extract keywords/tags\n    const tags = this.extractKeywords(text);\n\n    return {\n      title,\n      authors,\n      abstract,\n      tags,\n    };\n  }\n\n  private static extractAuthors(text: string): string[] {\n    const authors: string[] = [];\n    const lines = text.split('\\n');\n    \n    // Look for author patterns in the first few pages\n    for (let i = 0; i < Math.min(50, lines.length); i++) {\n      const line = lines[i].trim();\n      \n      // Common author patterns\n      if (line.match(/^[A-Z][a-z]+ [A-Z][a-z]+(\\s*,\\s*[A-Z][a-z]+ [A-Z][a-z]+)*$/)) {\n        const authorList = line.split(',').map(author => author.trim());\n        authors.push(...authorList);\n        break;\n      }\n    }\n\n    return authors.length > 0 ? authors : ['Unknown Author'];\n  }\n\n  private static extractAbstract(text: string): string {\n    const abstractMatch = text.match(/(?:ABSTRACT|Abstract)\\s*:?\\s*([\\s\\S]*?)(?:\\n\\s*\\n|\\n\\s*(?:1\\.|I\\.|INTRODUCTION|Introduction))/i);\n    \n    if (abstractMatch) {\n      return abstractMatch[1].trim().substring(0, 1000); // Limit length\n    }\n\n    // Fallback: use first paragraph\n    const paragraphs = text.split('\\n\\n');\n    for (const paragraph of paragraphs) {\n      if (paragraph.length > 100 && paragraph.length < 1000) {\n        return paragraph.trim();\n      }\n    }\n\n    return 'No abstract found';\n  }\n\n  private static extractKeywords(text: string): string[] {\n    const keywords: string[] = [];\n    \n    // Look for explicit keywords section\n    const keywordsMatch = text.match(/(?:Keywords|KEYWORDS|Key words)\\s*:?\\s*(.*?)(?:\\n|$)/i);\n    if (keywordsMatch) {\n      const keywordList = keywordsMatch[1].split(/[,;]/).map(k => k.trim()).filter(k => k.length > 0);\n      keywords.push(...keywordList);\n    }\n\n    // Extract common academic terms\n    const commonTerms = [\n      'machine learning', 'deep learning', 'neural network', 'artificial intelligence',\n      'natural language processing', 'computer vision', 'data mining', 'algorithm',\n      'optimization', 'classification', 'regression', 'clustering', 'reinforcement learning'\n    ];\n\n    for (const term of commonTerms) {\n      if (text.toLowerCase().includes(term)) {\n        keywords.push(term);\n      }\n    }\n\n    return [...new Set(keywords)].slice(0, 10); // Remove duplicates and limit\n  }\n\n  static async validatePDF(buffer: Buffer): Promise<boolean> {\n    try {\n      // Check PDF header\n      const header = buffer.subarray(0, 5).toString();\n      if (!header.startsWith('%PDF-')) {\n        console.log('Invalid PDF header:', header);\n        return false;\n      }\n\n      // For now, if header is valid, we accept the PDF\n      // In production, you would implement proper PDF validation\n      console.log('PDF validation passed (header check)');\n      return true;\n    } catch (error: any) {\n      console.log('PDF validation error:', error?.message || error);\n      return false;\n    }\n  }\n\n  static getFileInfo(buffer: Buffer): { size: number; type: string } {\n    return {\n      size: buffer.length,\n      type: 'application/pdf',\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA,iEAAiE;;;;AAqB1D,MAAM;IACX,aAAa,WAAW,MAAc,EAAgC;QACpE,IAAI;YACF,gCAAgC;YAChC,QAAQ,GAAG,CAAC,kCAAkC,OAAO,MAAM;YAE3D,8DAA8D;YAC9D,yCAAyC;YACzC,MAAM,WAAW;gBACf,UAAU;gBACV,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;2FAqB4E,CAAC;gBACpF,MAAM;oBACJ,OAAO;oBACP,QAAQ;oBACR,SAAS;oBACT,SAAS;oBACT,UAAU;oBACV,cAAc,IAAI;oBAClB,SAAS,IAAI;gBACf;YACF;YAEA,yBAAyB;YACzB,MAAM,WAAW;gBACf,OAAO,SAAS,IAAI,EAAE,SAAS;gBAC/B,QAAQ,SAAS,IAAI,EAAE,UAAU;gBACjC,SAAS,SAAS,IAAI,EAAE,WAAW;gBACnC,SAAS,SAAS,IAAI,EAAE,WAAW;gBACnC,UAAU,SAAS,IAAI,EAAE,YAAY;gBACrC,cAAc,SAAS,IAAI,EAAE,eAAe,IAAI,KAAK,SAAS,IAAI,CAAC,YAAY,IAAI;gBACnF,kBAAkB,SAAS,IAAI,EAAE,UAAU,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,IAAI;gBAC7E,OAAO,SAAS,QAAQ;YAC1B;YAEA,wDAAwD;YACxD,MAAM,QAAQ,IAAI,CAAC,kBAAkB,CAAC,SAAS,IAAI,EAAE,SAAS,QAAQ;YAEtE,OAAO;gBACL,MAAM,SAAS,IAAI;gBACnB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAe,mBAAmB,IAAY,EAAE,QAAgB,EAA+C;QAC7G,kEAAkE;QAClE,wFAAwF;QACxF,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,eAAe,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG;QAC9C,MAAM,QAAqD,EAAE;QAE7D,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;YACjC,MAAM,YAAY,IAAI;YACtB,MAAM,UAAU,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,cAAc,MAAM,MAAM;YAC7D,MAAM,WAAW,MAAM,KAAK,CAAC,WAAW,SAAS,IAAI,CAAC;YAEtD,MAAM,IAAI,CAAC;gBACT,YAAY,IAAI;gBAChB,MAAM;YACR;QACF;QAEA,OAAO;IACT;IAEA,OAAO,qBAAqB,IAAY,EAAE,QAAgB,EAAkB;QAC1E,qDAAqD;QACrD,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,MAAM,GAAG;QACnE,IAAI,QAAQ,SAAS,OAAO,CAAC,QAAQ;QAErC,8CAA8C;QAC9C,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,IAAK;YACrC,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG,OAAO,CAAC,KAAK,QAAQ,CAAC,QAAQ,CAAC,KAAK,QAAQ,CAAC,SAAS;gBAC1F,QAAQ,KAAK,IAAI;gBACjB;YACF;QACF;QAEA,6CAA6C;QAC7C,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC;QAEpC,mBAAmB;QACnB,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QAEtC,wBAAwB;QACxB,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC;QAElC,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;IAEA,OAAe,eAAe,IAAY,EAAY;QACpD,MAAM,UAAoB,EAAE;QAC5B,MAAM,QAAQ,KAAK,KAAK,CAAC;QAEzB,kDAAkD;QAClD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,IAAK;YACnD,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;YAE1B,yBAAyB;YACzB,IAAI,KAAK,KAAK,CAAC,+DAA+D;gBAC5E,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;gBAC5D,QAAQ,IAAI,IAAI;gBAChB;YACF;QACF;QAEA,OAAO,QAAQ,MAAM,GAAG,IAAI,UAAU;YAAC;SAAiB;IAC1D;IAEA,OAAe,gBAAgB,IAAY,EAAU;QACnD,MAAM,gBAAgB,KAAK,KAAK,CAAC;QAEjC,IAAI,eAAe;YACjB,OAAO,aAAa,CAAC,EAAE,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,OAAO,eAAe;QACpE;QAEA,gCAAgC;QAChC,MAAM,aAAa,KAAK,KAAK,CAAC;QAC9B,KAAK,MAAM,aAAa,WAAY;YAClC,IAAI,UAAU,MAAM,GAAG,OAAO,UAAU,MAAM,GAAG,MAAM;gBACrD,OAAO,UAAU,IAAI;YACvB;QACF;QAEA,OAAO;IACT;IAEA,OAAe,gBAAgB,IAAY,EAAY;QACrD,MAAM,WAAqB,EAAE;QAE7B,qCAAqC;QACrC,MAAM,gBAAgB,KAAK,KAAK,CAAC;QACjC,IAAI,eAAe;YACjB,MAAM,cAAc,aAAa,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG;YAC7F,SAAS,IAAI,IAAI;QACnB;QAEA,gCAAgC;QAChC,MAAM,cAAc;YAClB;YAAoB;YAAiB;YAAkB;YACvD;YAA+B;YAAmB;YAAe;YACjE;YAAgB;YAAkB;YAAc;YAAc;SAC/D;QAED,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,OAAO;gBACrC,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,OAAO;eAAI,IAAI,IAAI;SAAU,CAAC,KAAK,CAAC,GAAG,KAAK,8BAA8B;IAC5E;IAEA,aAAa,YAAY,MAAc,EAAoB;QACzD,IAAI;YACF,mBAAmB;YACnB,MAAM,SAAS,OAAO,QAAQ,CAAC,GAAG,GAAG,QAAQ;YAC7C,IAAI,CAAC,OAAO,UAAU,CAAC,UAAU;gBAC/B,QAAQ,GAAG,CAAC,uBAAuB;gBACnC,OAAO;YACT;YAEA,iDAAiD;YACjD,2DAA2D;YAC3D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,GAAG,CAAC,yBAAyB,OAAO,WAAW;YACvD,OAAO;QACT;IACF;IAEA,OAAO,YAAY,MAAc,EAAkC;QACjE,OAAO;YACL,MAAM,OAAO,MAAM;YACnB,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/constants.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_CONFIG = {\n  name: 'DocuMancer',\n  version: '1.0.0',\n  description: 'AI-Powered Academic Paper Reading Assistant',\n  maxFileSize: 50 * 1024 * 1024, // 50MB\n  allowedFileTypes: ['.pdf'],\n  supportedFormats: ['PDF'],\n} as const;\n\nexport const COLORS = {\n  primary: '#1890ff',\n  secondary: '#722ed1',\n  success: '#52c41a',\n  warning: '#faad14',\n  error: '#ff4d4f',\n  text: {\n    primary: '#262626',\n    secondary: '#595959',\n    disabled: '#bfbfbf',\n  },\n  background: {\n    primary: '#ffffff',\n    secondary: '#fafafa',\n    tertiary: '#f5f5f5',\n  },\n  border: '#d9d9d9',\n} as const;\n\nexport const BREAKPOINTS = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600,\n} as const;\n\nexport const ROUTES = {\n  home: '/',\n  library: '/library',\n  reader: '/reader',\n  comparison: '/comparison',\n  analysis: '/analysis',\n  settings: '/settings',\n} as const;\n\nexport const API_ENDPOINTS = {\n  papers: '/api/papers',\n  upload: '/api/upload',\n  chat: '/api/chat',\n  analysis: '/api/analysis',\n  search: '/api/search',\n  comparison: '/api/comparison',\n} as const;\n\nexport const PAPER_FORMATS = {\n  ARXIV: 'arXiv',\n  IEEE: 'IEEE',\n  ACM: 'ACM',\n  SPRINGER: 'Springer',\n  ELSEVIER: 'Elsevier',\n  GENERIC: 'Generic',\n} as const;\n\nexport const ANALYSIS_TYPES = {\n  SUMMARY: 'summary',\n  KEY_FINDINGS: 'key_findings',\n  METHODOLOGY: 'methodology',\n  CONCEPTS: 'concepts',\n  CITATIONS: 'citations',\n  COMPARISON: 'comparison',\n} as const;\n\nexport const MESSAGE_TYPES = {\n  USER: 'user',\n  ASSISTANT: 'assistant',\n  SYSTEM: 'system',\n} as const;\n\nexport const ANNOTATION_TYPES = {\n  HIGHLIGHT: 'highlight',\n  NOTE: 'note',\n  BOOKMARK: 'bookmark',\n} as const;\n\nexport const VIEW_MODES = {\n  READER: 'reader',\n  LIBRARY: 'library',\n  COMPARISON: 'comparison',\n  ANALYSIS: 'analysis',\n} as const;\n\nexport const LOADING_MESSAGES = [\n  'Processing your document...',\n  'Extracting text content...',\n  'Analyzing paper structure...',\n  'Generating insights...',\n  'Almost ready...',\n] as const;\n\nexport const ERROR_MESSAGES = {\n  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',\n  INVALID_FILE_TYPE: 'Only PDF files are supported',\n  UPLOAD_FAILED: 'Failed to upload file. Please try again.',\n  PROCESSING_FAILED: 'Failed to process the document',\n  API_ERROR: 'An error occurred while communicating with the server',\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  GENERIC_ERROR: 'An unexpected error occurred',\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;;;;AAEjB,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,aAAa;IACb,aAAa,KAAK,OAAO;IACzB,kBAAkB;QAAC;KAAO;IAC1B,kBAAkB;QAAC;KAAM;AAC3B;AAEO,MAAM,SAAS;IACpB,SAAS;IACT,WAAW;IACX,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;QACJ,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,YAAY;QACV,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,QAAQ;AACV;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;AACP;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,SAAS;IACT,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,UAAU;AACZ;AAEO,MAAM,gBAAgB;IAC3B,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,UAAU;IACV,QAAQ;IACR,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,OAAO;IACP,MAAM;IACN,KAAK;IACL,UAAU;IACV,UAAU;IACV,SAAS;AACX;AAEO,MAAM,iBAAiB;IAC5B,SAAS;IACT,cAAc;IACd,aAAa;IACb,UAAU;IACV,WAAW;IACX,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,MAAM;IACN,WAAW;IACX,QAAQ;AACV;AAEO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,UAAU;AACZ;AAEO,MAAM,aAAa;IACxB,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,UAAU;AACZ;AAEO,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,mBAAmB;IACnB,eAAe;IACf,mBAAmB;IACnB,WAAW;IACX,eAAe;IACf,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/app/api/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { writeFile, mkdir, readFile } from 'fs/promises';\nimport { join } from 'path';\nimport { existsSync } from 'fs';\nimport { PDFProcessor } from '@/lib/pdf-processor';\nimport { Paper } from '@/lib/types';\nimport { APP_CONFIG, ERROR_MESSAGES } from '@/lib/constants';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      return NextResponse.json(\n        { success: false, error: 'No file provided' },\n        { status: 400 }\n      );\n    }\n\n    // Validate file type\n    if (!file.name.toLowerCase().endsWith('.pdf')) {\n      return NextResponse.json(\n        { success: false, error: ERROR_MESSAGES.INVALID_FILE_TYPE },\n        { status: 400 }\n      );\n    }\n\n    // Validate file size\n    if (file.size > APP_CONFIG.maxFileSize) {\n      return NextResponse.json(\n        { success: false, error: ERROR_MESSAGES.FILE_TOO_LARGE },\n        { status: 400 }\n      );\n    }\n\n    // Convert file to buffer\n    const bytes = await file.arrayBuffer();\n    const buffer = Buffer.from(bytes);\n\n    // Validate PDF format\n    const isValidPDF = await PDFProcessor.validatePDF(buffer);\n    if (!isValidPDF) {\n      return NextResponse.json(\n        { success: false, error: 'Invalid PDF file' },\n        { status: 400 }\n      );\n    }\n\n    // Create uploads directory if it doesn't exist\n    const uploadsDir = join(process.cwd(), 'uploads');\n    try {\n      await mkdir(uploadsDir, { recursive: true });\n    } catch (error) {\n      // Directory might already exist\n    }\n\n    // Generate unique filename\n    const timestamp = Date.now();\n    const filename = `${timestamp}_${file.name}`;\n    const filepath = join(uploadsDir, filename);\n\n    // Save file\n    await writeFile(filepath, buffer);\n\n    // Process PDF\n    const processingResult = await PDFProcessor.processPDF(buffer);\n    const paperMetadata = PDFProcessor.extractPaperMetadata(processingResult.text, file.name);\n\n    // Create paper object\n    const paper: Paper = {\n      id: `paper_${timestamp}`,\n      title: paperMetadata.title || file.name.replace('.pdf', ''),\n      authors: paperMetadata.authors || ['Unknown Author'],\n      abstract: paperMetadata.abstract || 'No abstract available',\n      content: processingResult.text,\n      filePath: `/api/files/${filename}`, // Use API endpoint for file access\n      uploadedAt: new Date(),\n      lastAccessedAt: new Date(),\n      tags: paperMetadata.tags || [],\n    };\n\n    // Save paper to storage\n    try {\n      const dataDir = join(process.cwd(), 'data');\n      const papersFile = join(dataDir, 'papers.json');\n\n      // Ensure data directory exists\n      if (!existsSync(dataDir)) {\n        await mkdir(dataDir, { recursive: true });\n      }\n\n      // Load existing papers\n      let papers: Paper[] = [];\n      if (existsSync(papersFile)) {\n        const data = await readFile(papersFile, 'utf-8');\n        papers = JSON.parse(data);\n      }\n\n      // Add new paper\n      papers.push(paper);\n\n      // Save updated papers\n      await writeFile(papersFile, JSON.stringify(papers, null, 2));\n    } catch (error) {\n      console.error('Error saving paper to storage:', error);\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        paper,\n        metadata: processingResult.metadata,\n        pages: processingResult.pages.length,\n      },\n      message: 'File uploaded and processed successfully',\n    });\n\n  } catch (error) {\n    console.error('Upload error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: ERROR_MESSAGES.UPLOAD_FAILED,\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET() {\n  return NextResponse.json(\n    { \n      success: false, \n      error: 'Method not allowed. Use POST to upload files.' \n    },\n    { status: 405 }\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAEA;;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAmB,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,yHAAA,CAAA,iBAAc,CAAC,iBAAiB;YAAC,GAC1D;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,IAAI,KAAK,IAAI,GAAG,yHAAA,CAAA,aAAU,CAAC,WAAW,EAAE;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,yHAAA,CAAA,iBAAc,CAAC,cAAc;YAAC,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,QAAQ,MAAM,KAAK,WAAW;QACpC,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,sBAAsB;QACtB,MAAM,aAAa,MAAM,gIAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QAClD,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAmB,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,+CAA+C;QAC/C,MAAM,aAAa,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI;QACvC,IAAI;YACF,MAAM,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE,YAAY;gBAAE,WAAW;YAAK;QAC5C,EAAE,OAAO,OAAO;QACd,gCAAgC;QAClC;QAEA,2BAA2B;QAC3B,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,KAAK,IAAI,EAAE;QAC5C,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAElC,YAAY;QACZ,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QAE1B,cAAc;QACd,MAAM,mBAAmB,MAAM,gIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;QACvD,MAAM,gBAAgB,gIAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC,iBAAiB,IAAI,EAAE,KAAK,IAAI;QAExF,sBAAsB;QACtB,MAAM,QAAe;YACnB,IAAI,CAAC,MAAM,EAAE,WAAW;YACxB,OAAO,cAAc,KAAK,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ;YACxD,SAAS,cAAc,OAAO,IAAI;gBAAC;aAAiB;YACpD,UAAU,cAAc,QAAQ,IAAI;YACpC,SAAS,iBAAiB,IAAI;YAC9B,UAAU,CAAC,WAAW,EAAE,UAAU;YAClC,YAAY,IAAI;YAChB,gBAAgB,IAAI;YACpB,MAAM,cAAc,IAAI,IAAI,EAAE;QAChC;QAEA,wBAAwB;QACxB,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI;YACpC,MAAM,aAAa,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,SAAS;YAEjC,+BAA+B;YAC/B,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,UAAU;gBACxB,MAAM,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE,SAAS;oBAAE,WAAW;gBAAK;YACzC;YAEA,uBAAuB;YACvB,IAAI,SAAkB,EAAE;YACxB,IAAI,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,aAAa;gBAC1B,MAAM,OAAO,MAAM,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;gBACxC,SAAS,KAAK,KAAK,CAAC;YACtB;YAEA,gBAAgB;YAChB,OAAO,IAAI,CAAC;YAEZ,sBAAsB;YACtB,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,YAAY,KAAK,SAAS,CAAC,QAAQ,MAAM;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA,UAAU,iBAAiB,QAAQ;gBACnC,OAAO,iBAAiB,KAAK,CAAC,MAAM;YACtC;YACA,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,yHAAA,CAAA,iBAAc,CAAC,aAAa;YACnC,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;IACT,GACA;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}]}
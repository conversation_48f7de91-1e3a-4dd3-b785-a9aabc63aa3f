module.exports = {

"[project]/.next-internal/server/app/api/upload/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs/promises [external] (fs/promises, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs/promises", () => require("fs/promises"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[project]/src/lib/pdf-processor.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Dynamic import to avoid build issues
__turbopack_context__.s({
    "PDFProcessor": ()=>PDFProcessor
});
class PDFProcessor {
    static async processPDF(buffer) {
        try {
            // Log buffer size for debugging
            console.log('Processing PDF buffer of size:', buffer.length);
            // Try to parse PDF using pdf-parse with error handling
            let data;
            try {
                const pdfParse = (await __turbopack_context__.r("[project]/node_modules/pdf-parse/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i)).default;
                data = await pdfParse(buffer);
            } catch (parseError) {
                console.log('PDF parsing failed, using fallback:', parseError?.message);
                // Fallback to basic text extraction
                data = {
                    numpages: 1,
                    text: this.extractBasicText(buffer),
                    info: {
                        Title: 'Extracted Document',
                        Author: 'Unknown',
                        Subject: 'PDF Document',
                        Creator: 'PDF Processor',
                        Producer: 'DocuMancer',
                        CreationDate: new Date(),
                        ModDate: new Date()
                    }
                };
            }
            // Extract basic metadata
            const metadata = {
                title: data.info?.Title || undefined,
                author: data.info?.Author || undefined,
                subject: data.info?.Subject || undefined,
                creator: data.info?.Creator || undefined,
                producer: data.info?.Producer || undefined,
                creationDate: data.info?.CreationDate ? new Date(data.info.CreationDate) : undefined,
                modificationDate: data.info?.ModDate ? new Date(data.info.ModDate) : undefined,
                pages: data.numpages
            };
            // Clean and process the extracted text
            const cleanedText = this.cleanText(data.text);
            // Split text into pages (this is a simplified approach)
            const pages = this.splitTextIntoPages(cleanedText, data.numpages);
            return {
                text: cleanedText,
                metadata,
                pages
            };
        } catch (error) {
            console.error('Error processing PDF:', error);
            throw new Error('Failed to process PDF file');
        }
    }
    static splitTextIntoPages(text, numPages) {
        // This is a simplified page splitting - in a real implementation,
        // you might want to use a more sophisticated PDF library that preserves page boundaries
        const lines = text.split('\n');
        const linesPerPage = Math.ceil(lines.length / numPages);
        const pages = [];
        for(let i = 0; i < numPages; i++){
            const startLine = i * linesPerPage;
            const endLine = Math.min((i + 1) * linesPerPage, lines.length);
            const pageText = lines.slice(startLine, endLine).join('\n');
            pages.push({
                pageNumber: i + 1,
                text: pageText
            });
        }
        return pages;
    }
    static extractPaperMetadata(text, filename) {
        // Extract title (usually the first significant line)
        const lines = text.split('\n').filter((line)=>line.trim().length > 0);
        let title = filename.replace('.pdf', '');
        // Try to find a better title from the content
        for (const line of lines.slice(0, 10)){
            if (line.length > 20 && line.length < 200 && !line.includes('@') && !line.includes('http')) {
                title = line.trim();
                break;
            }
        }
        // Extract authors (look for common patterns)
        const authors = this.extractAuthors(text);
        // Extract abstract
        const abstract = this.extractAbstract(text);
        // Extract keywords/tags
        const tags = this.extractKeywords(text);
        return {
            title,
            authors,
            abstract,
            tags
        };
    }
    static extractAuthors(text) {
        const authors = [];
        const lines = text.split('\n');
        // Look for author patterns in the first few pages
        for(let i = 0; i < Math.min(50, lines.length); i++){
            const line = lines[i].trim();
            // Common author patterns
            if (line.match(/^[A-Z][a-z]+ [A-Z][a-z]+(\s*,\s*[A-Z][a-z]+ [A-Z][a-z]+)*$/)) {
                const authorList = line.split(',').map((author)=>author.trim());
                authors.push(...authorList);
                break;
            }
        }
        return authors.length > 0 ? authors : [
            'Unknown Author'
        ];
    }
    static extractAbstract(text) {
        const abstractMatch = text.match(/(?:ABSTRACT|Abstract)\s*:?\s*([\s\S]*?)(?:\n\s*\n|\n\s*(?:1\.|I\.|INTRODUCTION|Introduction))/i);
        if (abstractMatch) {
            return abstractMatch[1].trim().substring(0, 1000); // Limit length
        }
        // Fallback: use first paragraph
        const paragraphs = text.split('\n\n');
        for (const paragraph of paragraphs){
            if (paragraph.length > 100 && paragraph.length < 1000) {
                return paragraph.trim();
            }
        }
        return 'No abstract found';
    }
    static extractKeywords(text) {
        const keywords = [];
        // Look for explicit keywords section
        const keywordsMatch = text.match(/(?:Keywords|KEYWORDS|Key words)\s*:?\s*(.*?)(?:\n|$)/i);
        if (keywordsMatch) {
            const keywordList = keywordsMatch[1].split(/[,;]/).map((k)=>k.trim()).filter((k)=>k.length > 0);
            keywords.push(...keywordList);
        }
        // Extract common academic terms
        const commonTerms = [
            'machine learning',
            'deep learning',
            'neural network',
            'artificial intelligence',
            'natural language processing',
            'computer vision',
            'data mining',
            'algorithm',
            'optimization',
            'classification',
            'regression',
            'clustering',
            'reinforcement learning'
        ];
        for (const term of commonTerms){
            if (text.toLowerCase().includes(term)) {
                keywords.push(term);
            }
        }
        return [
            ...new Set(keywords)
        ].slice(0, 10); // Remove duplicates and limit
    }
    static extractBasicText(buffer) {
        // Basic text extraction fallback - just return a placeholder
        // In a real implementation, you might use a different PDF library
        return `Document Content

This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.

Title: PDF Document
Pages: 1
Size: ${buffer.length} bytes

The document is ready for AI analysis and annotation.`;
    }
    static cleanText(text) {
        // Remove excessive whitespace and normalize line breaks
        return text.replace(/\r\n/g, '\n').replace(/\r/g, '\n').replace(/\n{3,}/g, '\n\n').replace(/[ \t]{2,}/g, ' ').trim();
    }
    static async validatePDF(buffer) {
        try {
            // Check PDF header
            const header = buffer.subarray(0, 5).toString();
            if (!header.startsWith('%PDF-')) {
                console.log('Invalid PDF header:', header);
                return false;
            }
            // If header is valid, accept the PDF
            // We'll handle parsing errors in the processPDF method
            console.log('PDF validation passed (header check)');
            return true;
        } catch (error) {
            console.log('PDF validation error:', error?.message || error);
            return false;
        }
    }
    static getFileInfo(buffer) {
        return {
            size: buffer.length,
            type: 'application/pdf'
        };
    }
}
}),
"[project]/src/lib/constants.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Application constants
__turbopack_context__.s({
    "ANALYSIS_TYPES": ()=>ANALYSIS_TYPES,
    "ANNOTATION_TYPES": ()=>ANNOTATION_TYPES,
    "API_ENDPOINTS": ()=>API_ENDPOINTS,
    "APP_CONFIG": ()=>APP_CONFIG,
    "BREAKPOINTS": ()=>BREAKPOINTS,
    "COLORS": ()=>COLORS,
    "ERROR_MESSAGES": ()=>ERROR_MESSAGES,
    "LOADING_MESSAGES": ()=>LOADING_MESSAGES,
    "MESSAGE_TYPES": ()=>MESSAGE_TYPES,
    "PAPER_FORMATS": ()=>PAPER_FORMATS,
    "ROUTES": ()=>ROUTES,
    "VIEW_MODES": ()=>VIEW_MODES
});
const APP_CONFIG = {
    name: 'DocuMancer',
    version: '1.0.0',
    description: 'AI-Powered Academic Paper Reading Assistant',
    maxFileSize: 50 * 1024 * 1024,
    allowedFileTypes: [
        '.pdf'
    ],
    supportedFormats: [
        'PDF'
    ]
};
const COLORS = {
    primary: '#1890ff',
    secondary: '#722ed1',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    text: {
        primary: '#262626',
        secondary: '#595959',
        disabled: '#bfbfbf'
    },
    background: {
        primary: '#ffffff',
        secondary: '#fafafa',
        tertiary: '#f5f5f5'
    },
    border: '#d9d9d9'
};
const BREAKPOINTS = {
    xs: 480,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1600
};
const ROUTES = {
    home: '/',
    library: '/library',
    reader: '/reader',
    comparison: '/comparison',
    analysis: '/analysis',
    settings: '/settings'
};
const API_ENDPOINTS = {
    papers: '/api/papers',
    upload: '/api/upload',
    chat: '/api/chat',
    analysis: '/api/analysis',
    search: '/api/search',
    comparison: '/api/comparison'
};
const PAPER_FORMATS = {
    ARXIV: 'arXiv',
    IEEE: 'IEEE',
    ACM: 'ACM',
    SPRINGER: 'Springer',
    ELSEVIER: 'Elsevier',
    GENERIC: 'Generic'
};
const ANALYSIS_TYPES = {
    SUMMARY: 'summary',
    KEY_FINDINGS: 'key_findings',
    METHODOLOGY: 'methodology',
    CONCEPTS: 'concepts',
    CITATIONS: 'citations',
    COMPARISON: 'comparison'
};
const MESSAGE_TYPES = {
    USER: 'user',
    ASSISTANT: 'assistant',
    SYSTEM: 'system'
};
const ANNOTATION_TYPES = {
    HIGHLIGHT: 'highlight',
    NOTE: 'note',
    BOOKMARK: 'bookmark'
};
const VIEW_MODES = {
    READER: 'reader',
    LIBRARY: 'library',
    COMPARISON: 'comparison',
    ANALYSIS: 'analysis'
};
const LOADING_MESSAGES = [
    'Processing your document...',
    'Extracting text content...',
    'Analyzing paper structure...',
    'Generating insights...',
    'Almost ready...'
];
const ERROR_MESSAGES = {
    FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',
    INVALID_FILE_TYPE: 'Only PDF files are supported',
    UPLOAD_FAILED: 'Failed to upload file. Please try again.',
    PROCESSING_FAILED: 'Failed to process the document',
    API_ERROR: 'An error occurred while communicating with the server',
    NETWORK_ERROR: 'Network error. Please check your connection.',
    GENERIC_ERROR: 'An unexpected error occurred'
};
}),
"[project]/src/app/api/upload/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs/promises [external] (fs/promises, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pdf$2d$processor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pdf-processor.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-route] (ecmascript)");
;
;
;
;
;
;
async function POST(request) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        // Validate file type
        if (!file.name.toLowerCase().endsWith('.pdf')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].INVALID_FILE_TYPE
            }, {
                status: 400
            });
        }
        // Validate file size
        if (file.size > __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APP_CONFIG"].maxFileSize) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].FILE_TOO_LARGE
            }, {
                status: 400
            });
        }
        // Convert file to buffer
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        // Validate PDF format
        const isValidPDF = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pdf$2d$processor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PDFProcessor"].validatePDF(buffer);
        if (!isValidPDF) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid PDF file'
            }, {
                status: 400
            });
        }
        // Create uploads directory if it doesn't exist
        const uploadsDir = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(process.cwd(), 'uploads');
        try {
            await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["mkdir"])(uploadsDir, {
                recursive: true
            });
        } catch (error) {
        // Directory might already exist
        }
        // Generate unique filename
        const timestamp = Date.now();
        const filename = `${timestamp}_${file.name}`;
        const filepath = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(uploadsDir, filename);
        // Save file
        await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["writeFile"])(filepath, buffer);
        // Process PDF
        const processingResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pdf$2d$processor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PDFProcessor"].processPDF(buffer);
        const paperMetadata = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pdf$2d$processor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PDFProcessor"].extractPaperMetadata(processingResult.text, file.name);
        // Create paper object
        const paper = {
            id: `paper_${timestamp}`,
            title: paperMetadata.title || file.name.replace('.pdf', ''),
            authors: paperMetadata.authors || [
                'Unknown Author'
            ],
            abstract: paperMetadata.abstract || 'No abstract available',
            content: processingResult.text,
            filePath: `/api/files/${filename}`,
            uploadedAt: new Date(),
            lastAccessedAt: new Date(),
            tags: paperMetadata.tags || []
        };
        // Save paper to storage
        try {
            const dataDir = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(process.cwd(), 'data');
            const papersFile = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(dataDir, 'papers.json');
            // Ensure data directory exists
            if (!(0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["existsSync"])(dataDir)) {
                await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["mkdir"])(dataDir, {
                    recursive: true
                });
            }
            // Load existing papers
            let papers = [];
            if ((0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["existsSync"])(papersFile)) {
                const data = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["readFile"])(papersFile, 'utf-8');
                papers = JSON.parse(data);
            }
            // Add new paper
            papers.push(paper);
            // Save updated papers
            await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["writeFile"])(papersFile, JSON.stringify(papers, null, 2));
        } catch (error) {
            console.error('Error saving paper to storage:', error);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                paper,
                metadata: processingResult.metadata,
                pages: processingResult.pages.length
            },
            message: 'File uploaded and processed successfully'
        });
    } catch (error) {
        console.error('Upload error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].UPLOAD_FAILED,
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function GET() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: false,
        error: 'Method not allowed. Use POST to upload files.'
    }, {
        status: 405
    });
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f5e4513f._.js.map
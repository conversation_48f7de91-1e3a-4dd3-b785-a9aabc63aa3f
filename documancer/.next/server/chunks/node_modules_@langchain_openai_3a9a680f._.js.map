{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/utils/azure.js"], "sourcesContent": ["/**\n * This function generates an endpoint URL for (Azure) OpenAI\n * based on the configuration parameters provided.\n *\n * @param {OpenAIEndpointConfig} config - The configuration object for the (Azure) endpoint.\n *\n * @property {string} config.azureOpenAIApiDeploymentName - The deployment name of Azure OpenAI.\n * @property {string} config.azureOpenAIApiInstanceName - The instance name of Azure OpenAI, e.g. `example-resource`.\n * @property {string} config.azureOpenAIApiKey - The API Key for Azure OpenAI.\n * @property {string} config.azureOpenAIBasePath - The base path for Azure OpenAI, e.g. `https://example-resource.azure.openai.com/openai/deployments/`.\n * @property {string} config.baseURL - Some other custom base path URL.\n * @property {string} config.azureOpenAIEndpoint - The endpoint for the Azure OpenAI instance, e.g. `https://example-resource.azure.openai.com/`.\n *\n * The function operates as follows:\n * - If both `azureOpenAIBasePath` and `azureOpenAIApiDeploymentName` (plus `azureOpenAIApiKey`) are provided, it returns an URL combining these two parameters (`${azureOpenAIBasePath}/${azureOpenAIApiDeploymentName}`).\n * - If both `azureOpenAIEndpoint` and `azureOpenAIApiDeploymentName` (plus `azureOpenAIApiKey`) are provided, it returns an URL combining these two parameters (`${azureOpenAIEndpoint}/openai/deployments/${azureOpenAIApiDeploymentName}`).\n * - If `azureOpenAIApiKey` is provided, it checks for `azureOpenAIApiInstanceName` and `azureOpenAIApiDeploymentName` and throws an error if any of these is missing. If both are provided, it generates an URL incorporating these parameters.\n * - If none of the above conditions are met, return any custom `baseURL`.\n * - The function returns the generated URL as a string, or undefined if no custom paths are specified.\n *\n * @throws Will throw an error if the necessary parameters for generating the URL are missing.\n *\n * @returns {string | undefined} The generated (Azure) OpenAI endpoint URL.\n */\nexport function getEndpoint(config) {\n    const { azureOpenAIApiDeploymentName, azureOpenAIApiInstanceName, azureOpenAIApiKey, azureOpenAIBasePath, baseURL, azureADTokenProvider, azureOpenAIEndpoint, } = config;\n    if ((azureOpenAIApiKey || azureADTokenProvider) &&\n        azureOpenAIBasePath &&\n        azureOpenAIApiDeploymentName) {\n        return `${azureOpenAIBasePath}/${azureOpenAIApiDeploymentName}`;\n    }\n    if ((azureOpenAIApiKey || azureADTokenProvider) &&\n        azureOpenAIEndpoint &&\n        azureOpenAIApiDeploymentName) {\n        return `${azureOpenAIEndpoint}/openai/deployments/${azureOpenAIApiDeploymentName}`;\n    }\n    if (azureOpenAIApiKey || azureADTokenProvider) {\n        if (!azureOpenAIApiInstanceName) {\n            throw new Error(\"azureOpenAIApiInstanceName is required when using azureOpenAIApiKey\");\n        }\n        if (!azureOpenAIApiDeploymentName) {\n            throw new Error(\"azureOpenAIApiDeploymentName is a required parameter when using azureOpenAIApiKey\");\n        }\n        return `https://${azureOpenAIApiInstanceName}.openai.azure.com/openai/deployments/${azureOpenAIApiDeploymentName}`;\n    }\n    return baseURL;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;CAuBC;;;AACM,SAAS,YAAY,MAAM;IAC9B,MAAM,EAAE,4BAA4B,EAAE,0BAA0B,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,OAAO,EAAE,oBAAoB,EAAE,mBAAmB,EAAG,GAAG;IAClK,IAAI,CAAC,qBAAqB,oBAAoB,KAC1C,uBACA,8BAA8B;QAC9B,OAAO,GAAG,oBAAoB,CAAC,EAAE,8BAA8B;IACnE;IACA,IAAI,CAAC,qBAAqB,oBAAoB,KAC1C,uBACA,8BAA8B;QAC9B,OAAO,GAAG,oBAAoB,oBAAoB,EAAE,8BAA8B;IACtF;IACA,IAAI,qBAAqB,sBAAsB;QAC3C,IAAI,CAAC,4BAA4B;YAC7B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,8BAA8B;YAC/B,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,CAAC,QAAQ,EAAE,2BAA2B,qCAAqC,EAAE,8BAA8B;IACtH;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/utils/errors.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable no-param-reassign */\nexport function addLang<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ields(error, lc_error_code) {\n    error.lc_error_code = lc_error_code;\n    error.message = `${error.message}\\n\\nTroubleshooting URL: https://js.langchain.com/docs/troubleshooting/errors/${lc_error_code}/\\n`;\n    return error;\n}\n"], "names": [], "mappings": "AAAA,qDAAqD,GACrD,oCAAoC;;;AAC7B,SAAS,wBAAwB,KAAK,EAAE,aAAa;IACxD,MAAM,aAAa,GAAG;IACtB,MAAM,OAAO,GAAG,GAAG,MAAM,OAAO,CAAC,8EAA8E,EAAE,cAAc,GAAG,CAAC;IACnI,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/utils/openai.js"], "sourcesContent": ["import { APIConnectionTimeoutError, APIUserAbortError, } from \"openai\";\nimport { convertToOpenAIFunction, convertToOpenAITool, } from \"@langchain/core/utils/function_calling\";\nimport { isInteropZodSchema, isZodSchemaV3, isZodSchemaV4, } from \"@langchain/core/utils/types\";\nimport { toJsonSchema } from \"@langchain/core/utils/json_schema\";\nimport { toJSONSchema as toJSONSchemaV4, parse as parseV4 } from \"zod/v4/core\";\nimport { zodResponseFormat } from \"openai/helpers/zod\";\nimport { addLangChainErrorFields } from \"./errors.js\";\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function wrapOpenAIClientError(e) {\n    let error;\n    if (e.constructor.name === APIConnectionTimeoutError.name) {\n        error = new Error(e.message);\n        error.name = \"TimeoutError\";\n    }\n    else if (e.constructor.name === APIUserAbortError.name) {\n        error = new Error(e.message);\n        error.name = \"AbortError\";\n    }\n    else if (e.status === 400 && e.message.includes(\"tool_calls\")) {\n        error = addLangChainErrorFields(e, \"INVALID_TOOL_RESULTS\");\n    }\n    else if (e.status === 401) {\n        error = addLangChainErrorFields(e, \"MODEL_AUTHENTICATION\");\n    }\n    else if (e.status === 429) {\n        error = addLangChainErrorFields(e, \"MODEL_RATE_LIMIT\");\n    }\n    else if (e.status === 404) {\n        error = addLangChainErrorFields(e, \"MODEL_NOT_FOUND\");\n    }\n    else {\n        error = e;\n    }\n    return error;\n}\nexport { convertToOpenAIFunction as formatToOpenAIFunction, convertToOpenAITool as formatToOpenAITool, };\nexport function formatToOpenAIAssistantTool(tool) {\n    return {\n        type: \"function\",\n        function: {\n            name: tool.name,\n            description: tool.description,\n            parameters: isInteropZodSchema(tool.schema)\n                ? toJsonSchema(tool.schema)\n                : tool.schema,\n        },\n    };\n}\nexport function formatToOpenAIToolChoice(toolChoice) {\n    if (!toolChoice) {\n        return undefined;\n    }\n    else if (toolChoice === \"any\" || toolChoice === \"required\") {\n        return \"required\";\n    }\n    else if (toolChoice === \"auto\") {\n        return \"auto\";\n    }\n    else if (toolChoice === \"none\") {\n        return \"none\";\n    }\n    else if (typeof toolChoice === \"string\") {\n        return {\n            type: \"function\",\n            function: {\n                name: toolChoice,\n            },\n        };\n    }\n    else {\n        return toolChoice;\n    }\n}\n// inlined from openai/lib/parser.ts\nfunction makeParseableResponseFormat(response_format, parser) {\n    const obj = { ...response_format };\n    Object.defineProperties(obj, {\n        $brand: {\n            value: \"auto-parseable-response-format\",\n            enumerable: false,\n        },\n        $parseRaw: {\n            value: parser,\n            enumerable: false,\n        },\n    });\n    return obj;\n}\nexport function interopZodResponseFormat(zodSchema, name, props) {\n    if (isZodSchemaV3(zodSchema)) {\n        return zodResponseFormat(zodSchema, name, props);\n    }\n    if (isZodSchemaV4(zodSchema)) {\n        return makeParseableResponseFormat({\n            type: \"json_schema\",\n            json_schema: {\n                ...props,\n                name,\n                strict: true,\n                schema: toJSONSchemaV4(zodSchema, {\n                    cycles: \"ref\", // equivalent to nameStrategy: 'duplicate-ref'\n                    reused: \"ref\", // equivalent to $refStrategy: 'extract-to-root'\n                    override(ctx) {\n                        ctx.jsonSchema.title = name; // equivalent to `name` property\n                        // TODO: implement `nullableStrategy` patch-fix (zod doesn't support openApi3 json schema target)\n                        // TODO: implement `openaiStrictMode` patch-fix (where optional properties without `nullable` are not supported)\n                    },\n                    /// property equivalents from native `zodResponseFormat` fn\n                    // openaiStrictMode: true,\n                    // name,\n                    // nameStrategy: 'duplicate-ref',\n                    // $refStrategy: 'extract-to-root',\n                    // nullableStrategy: 'property',\n                }),\n            },\n        }, (content) => parseV4(zodSchema, JSON.parse(content)));\n    }\n    throw new Error(\"Unsupported schema response format\");\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;;;;;;AAEO,SAAS,sBAAsB,CAAC;IACnC,IAAI;IACJ,IAAI,EAAE,WAAW,CAAC,IAAI,KAAK,mLAAA,CAAA,4BAAyB,CAAC,IAAI,EAAE;QACvD,QAAQ,IAAI,MAAM,EAAE,OAAO;QAC3B,MAAM,IAAI,GAAG;IACjB,OACK,IAAI,EAAE,WAAW,CAAC,IAAI,KAAK,mLAAA,CAAA,oBAAiB,CAAC,IAAI,EAAE;QACpD,QAAQ,IAAI,MAAM,EAAE,OAAO;QAC3B,MAAM,IAAI,GAAG;IACjB,OACK,IAAI,EAAE,MAAM,KAAK,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,eAAe;QAC3D,QAAQ,CAAA,GAAA,kKAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;IACvC,OACK,IAAI,EAAE,MAAM,KAAK,KAAK;QACvB,QAAQ,CAAA,GAAA,kKAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;IACvC,OACK,IAAI,EAAE,MAAM,KAAK,KAAK;QACvB,QAAQ,CAAA,GAAA,kKAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;IACvC,OACK,IAAI,EAAE,MAAM,KAAK,KAAK;QACvB,QAAQ,CAAA,GAAA,kKAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;IACvC,OACK;QACD,QAAQ;IACZ;IACA,OAAO;AACX;;AAEO,SAAS,4BAA4B,IAAI;IAC5C,OAAO;QACH,MAAM;QACN,UAAU;YACN,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,YAAY,CAAA,GAAA,sKAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,MAAM,IACpC,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAE,KAAK,MAAM,IACxB,KAAK,MAAM;QACrB;IACJ;AACJ;AACO,SAAS,yBAAyB,UAAU;IAC/C,IAAI,CAAC,YAAY;QACb,OAAO;IACX,OACK,IAAI,eAAe,SAAS,eAAe,YAAY;QACxD,OAAO;IACX,OACK,IAAI,eAAe,QAAQ;QAC5B,OAAO;IACX,OACK,IAAI,eAAe,QAAQ;QAC5B,OAAO;IACX,OACK,IAAI,OAAO,eAAe,UAAU;QACrC,OAAO;YACH,MAAM;YACN,UAAU;gBACN,MAAM;YACV;QACJ;IACJ,OACK;QACD,OAAO;IACX;AACJ;AACA,oCAAoC;AACpC,SAAS,4BAA4B,eAAe,EAAE,MAAM;IACxD,MAAM,MAAM;QAAE,GAAG,eAAe;IAAC;IACjC,OAAO,gBAAgB,CAAC,KAAK;QACzB,QAAQ;YACJ,OAAO;YACP,YAAY;QAChB;QACA,WAAW;YACP,OAAO;YACP,YAAY;QAChB;IACJ;IACA,OAAO;AACX;AACO,SAAS,yBAAyB,SAAS,EAAE,IAAI,EAAE,KAAK;IAC3D,IAAI,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,YAAY;QAC1B,OAAO,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,MAAM;IAC9C;IACA,IAAI,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE,YAAY;QAC1B,OAAO,4BAA4B;YAC/B,MAAM;YACN,aAAa;gBACT,GAAG,KAAK;gBACR;gBACA,QAAQ;gBACR,QAAQ,CAAA,GAAA,2JAAA,CAAA,eAAc,AAAD,EAAE,WAAW;oBAC9B,QAAQ;oBACR,QAAQ;oBACR,UAAS,GAAG;wBACR,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,gCAAgC;oBAC7D,iGAAiG;oBACjG,gHAAgH;oBACpH;gBAOJ;YACJ;QACJ,GAAG,CAAC,UAAY,CAAA,GAAA,4IAAA,CAAA,QAAO,AAAD,EAAE,WAAW,KAAK,KAAK,CAAC;IAClD;IACA,MAAM,IAAI,MAAM;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/utils/openai-format-fndef.js"], "sourcesContent": ["function isAnyOfProp(prop) {\n    return (prop.anyOf !== undefined &&\n        Array.isArray(prop.anyOf));\n}\n// When OpenAI use functions in the prompt, they format them as TypeScript definitions rather than OpenAPI JSON schemas.\n// This function converts the JSON schemas into TypeScript definitions.\nexport function formatFunctionDefinitions(functions) {\n    const lines = [\"namespace functions {\", \"\"];\n    for (const f of functions) {\n        if (f.description) {\n            lines.push(`// ${f.description}`);\n        }\n        if (Object.keys(f.parameters.properties ?? {}).length > 0) {\n            lines.push(`type ${f.name} = (_: {`);\n            lines.push(formatObjectProperties(f.parameters, 0));\n            lines.push(\"}) => any;\");\n        }\n        else {\n            lines.push(`type ${f.name} = () => any;`);\n        }\n        lines.push(\"\");\n    }\n    lines.push(\"} // namespace functions\");\n    return lines.join(\"\\n\");\n}\n// Format just the properties of an object (not including the surrounding braces)\nfunction formatObjectProperties(obj, indent) {\n    const lines = [];\n    for (const [name, param] of Object.entries(obj.properties ?? {})) {\n        if (param.description && indent < 2) {\n            lines.push(`// ${param.description}`);\n        }\n        if (obj.required?.includes(name)) {\n            lines.push(`${name}: ${formatType(param, indent)},`);\n        }\n        else {\n            lines.push(`${name}?: ${formatType(param, indent)},`);\n        }\n    }\n    return lines.map((line) => \" \".repeat(indent) + line).join(\"\\n\");\n}\n// Format a single property type\nfunction formatType(param, indent) {\n    if (isAnyOfProp(param)) {\n        return param.anyOf.map((v) => formatType(v, indent)).join(\" | \");\n    }\n    switch (param.type) {\n        case \"string\":\n            if (param.enum) {\n                return param.enum.map((v) => `\"${v}\"`).join(\" | \");\n            }\n            return \"string\";\n        case \"number\":\n            if (param.enum) {\n                return param.enum.map((v) => `${v}`).join(\" | \");\n            }\n            return \"number\";\n        case \"integer\":\n            if (param.enum) {\n                return param.enum.map((v) => `${v}`).join(\" | \");\n            }\n            return \"number\";\n        case \"boolean\":\n            return \"boolean\";\n        case \"null\":\n            return \"null\";\n        case \"object\":\n            return [\"{\", formatObjectProperties(param, indent + 2), \"}\"].join(\"\\n\");\n        case \"array\":\n            if (param.items) {\n                return `${formatType(param.items, indent)}[]`;\n            }\n            return \"any[]\";\n        default:\n            return \"\";\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY,IAAI;IACrB,OAAQ,KAAK,KAAK,KAAK,aACnB,MAAM,OAAO,CAAC,KAAK,KAAK;AAChC;AAGO,SAAS,0BAA0B,SAAS;IAC/C,MAAM,QAAQ;QAAC;QAAyB;KAAG;IAC3C,KAAK,MAAM,KAAK,UAAW;QACvB,IAAI,EAAE,WAAW,EAAE;YACf,MAAM,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE;QACpC;QACA,IAAI,OAAO,IAAI,CAAC,EAAE,UAAU,CAAC,UAAU,IAAI,CAAC,GAAG,MAAM,GAAG,GAAG;YACvD,MAAM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC;YACnC,MAAM,IAAI,CAAC,uBAAuB,EAAE,UAAU,EAAE;YAChD,MAAM,IAAI,CAAC;QACf,OACK;YACD,MAAM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC;QAC5C;QACA,MAAM,IAAI,CAAC;IACf;IACA,MAAM,IAAI,CAAC;IACX,OAAO,MAAM,IAAI,CAAC;AACtB;AACA,iFAAiF;AACjF,SAAS,uBAAuB,GAAG,EAAE,MAAM;IACvC,MAAM,QAAQ,EAAE;IAChB,KAAK,MAAM,CAAC,MAAM,MAAM,IAAI,OAAO,OAAO,CAAC,IAAI,UAAU,IAAI,CAAC,GAAI;QAC9D,IAAI,MAAM,WAAW,IAAI,SAAS,GAAG;YACjC,MAAM,IAAI,CAAC,CAAC,GAAG,EAAE,MAAM,WAAW,EAAE;QACxC;QACA,IAAI,IAAI,QAAQ,EAAE,SAAS,OAAO;YAC9B,MAAM,IAAI,CAAC,GAAG,KAAK,EAAE,EAAE,WAAW,OAAO,QAAQ,CAAC,CAAC;QACvD,OACK;YACD,MAAM,IAAI,CAAC,GAAG,KAAK,GAAG,EAAE,WAAW,OAAO,QAAQ,CAAC,CAAC;QACxD;IACJ;IACA,OAAO,MAAM,GAAG,CAAC,CAAC,OAAS,IAAI,MAAM,CAAC,UAAU,MAAM,IAAI,CAAC;AAC/D;AACA,gCAAgC;AAChC,SAAS,WAAW,KAAK,EAAE,MAAM;IAC7B,IAAI,YAAY,QAAQ;QACpB,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,WAAW,GAAG,SAAS,IAAI,CAAC;IAC9D;IACA,OAAQ,MAAM,IAAI;QACd,KAAK;YACD,IAAI,MAAM,IAAI,EAAE;gBACZ,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;YAChD;YACA,OAAO;QACX,KAAK;YACD,IAAI,MAAM,IAAI,EAAE;gBACZ,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,IAAM,GAAG,GAAG,EAAE,IAAI,CAAC;YAC9C;YACA,OAAO;QACX,KAAK;YACD,IAAI,MAAM,IAAI,EAAE;gBACZ,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,IAAM,GAAG,GAAG,EAAE,IAAI,CAAC;YAC9C;YACA,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;gBAAC;gBAAK,uBAAuB,OAAO,SAAS;gBAAI;aAAI,CAAC,IAAI,CAAC;QACtE,KAAK;YACD,IAAI,MAAM,KAAK,EAAE;gBACb,OAAO,GAAG,WAAW,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC;YACjD;YACA,OAAO;QACX;YACI,OAAO;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/utils/tools.js"], "sourcesContent": ["import { isLang<PERSON>hainTool } from \"@langchain/core/utils/function_calling\";\nimport { formatToOpenAITool } from \"./openai.js\";\n/**\n * Formats a tool in either OpenAI format, or LangChain structured tool format\n * into an OpenAI tool format. If the tool is already in OpenAI format, return without\n * any changes. If it is in LangChain structured tool format, convert it to OpenAI tool format\n * using OpenAI's `zodFunction` util, falling back to `convertToOpenAIFunction` if the parameters\n * returned from the `zodFunction` util are not defined.\n *\n * @param {BindToolsInput} tool The tool to convert to an OpenAI tool.\n * @param {Object} [fields] Additional fields to add to the OpenAI tool.\n * @returns {ToolDefinition} The inputted tool in OpenAI tool format.\n */\nexport function _convertToOpenAITool(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntool, fields) {\n    let toolDef;\n    if (isLangChainTool(tool)) {\n        toolDef = formatToOpenAITool(tool);\n    }\n    else {\n        toolDef = tool;\n    }\n    if (fields?.strict !== undefined) {\n        toolDef.function.strict = fields.strict;\n    }\n    return toolDef;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAYO,SAAS,qBAChB,8DAA8D;AAC9D,IAAI,EAAE,MAAM;IACR,IAAI;IACJ,IAAI,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;QACvB,UAAU,CAAA,GAAA,uPAAA,CAAA,qBAAkB,AAAD,EAAE;IACjC,OACK;QACD,UAAU;IACd;IACA,IAAI,QAAQ,WAAW,WAAW;QAC9B,QAAQ,QAAQ,CAAC,MAAM,GAAG,OAAO,MAAM;IAC3C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/chat_models.js"], "sourcesContent": ["import { OpenAI as OpenAIClient } from \"openai\";\nimport { AIMessage, AIMessageChunk, ChatMessage, ChatMessageChunk, FunctionMessageChunk, HumanMessageChunk, SystemMessageChunk, ToolMessageChunk, isAIMessage, parseBase64DataUrl, parseMimeType, convertToProviderContentBlock, isDataContentBlock, } from \"@langchain/core/messages\";\nimport { ChatGenerationChunk, } from \"@langchain/core/outputs\";\nimport { getEnvironmentVariable } from \"@langchain/core/utils/env\";\nimport { BaseChatModel, } from \"@langchain/core/language_models/chat_models\";\nimport { isOpenAITool, } from \"@langchain/core/language_models/base\";\nimport { RunnableLambda, RunnablePassthrough, RunnableSequence, } from \"@langchain/core/runnables\";\nimport { JsonOutputParser, StructuredOutputParser, } from \"@langchain/core/output_parsers\";\nimport { JsonOutputKeyToolsParser, convertLangChainToolCallToOpenAI, makeInvalidToolCall, parseToolCall, } from \"@langchain/core/output_parsers/openai_tools\";\nimport { getSchemaDescription, isInteropZodSchema, } from \"@langchain/core/utils/types\";\nimport { toJsonSchema, } from \"@langchain/core/utils/json_schema\";\nimport { getEndpoint } from \"./utils/azure.js\";\nimport { formatToOpenAIToolChoice, interopZodResponseFormat, wrapOpenAIClientError, } from \"./utils/openai.js\";\nimport { formatFunctionDefinitions, } from \"./utils/openai-format-fndef.js\";\nimport { _convertToOpenAITool } from \"./utils/tools.js\";\nconst _FUNCTION_CALL_IDS_MAP_KEY = \"__openai_function_call_ids__\";\nfunction isBuiltInTool(tool) {\n    return \"type\" in tool && tool.type !== \"function\";\n}\nfunction isBuiltInToolChoice(tool_choice) {\n    return (tool_choice != null &&\n        typeof tool_choice === \"object\" &&\n        \"type\" in tool_choice &&\n        tool_choice.type !== \"function\");\n}\nfunction isReasoningModel(model) {\n    return model && /^o\\d/.test(model);\n}\nfunction isStructuredOutputMethodParams(x\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) {\n    return (x !== undefined &&\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        typeof x.schema ===\n            \"object\");\n}\nfunction extractGenericMessageCustomRole(message) {\n    if (message.role !== \"system\" &&\n        message.role !== \"developer\" &&\n        message.role !== \"assistant\" &&\n        message.role !== \"user\" &&\n        message.role !== \"function\" &&\n        message.role !== \"tool\") {\n        console.warn(`Unknown message role: ${message.role}`);\n    }\n    return message.role;\n}\nexport function messageToOpenAIRole(message) {\n    const type = message._getType();\n    switch (type) {\n        case \"system\":\n            return \"system\";\n        case \"ai\":\n            return \"assistant\";\n        case \"human\":\n            return \"user\";\n        case \"function\":\n            return \"function\";\n        case \"tool\":\n            return \"tool\";\n        case \"generic\": {\n            if (!ChatMessage.isInstance(message))\n                throw new Error(\"Invalid generic chat message\");\n            return extractGenericMessageCustomRole(message);\n        }\n        default:\n            throw new Error(`Unknown message type: ${type}`);\n    }\n}\nconst completionsApiContentBlockConverter = {\n    providerName: \"ChatOpenAI\",\n    fromStandardTextBlock(block) {\n        return { type: \"text\", text: block.text };\n    },\n    fromStandardImageBlock(block) {\n        if (block.source_type === \"url\") {\n            return {\n                type: \"image_url\",\n                image_url: {\n                    url: block.url,\n                    ...(block.metadata?.detail\n                        ? { detail: block.metadata.detail }\n                        : {}),\n                },\n            };\n        }\n        if (block.source_type === \"base64\") {\n            const url = `data:${block.mime_type ?? \"\"};base64,${block.data}`;\n            return {\n                type: \"image_url\",\n                image_url: {\n                    url,\n                    ...(block.metadata?.detail\n                        ? { detail: block.metadata.detail }\n                        : {}),\n                },\n            };\n        }\n        throw new Error(`Image content blocks with source_type ${block.source_type} are not supported for ChatOpenAI`);\n    },\n    fromStandardAudioBlock(block) {\n        if (block.source_type === \"url\") {\n            const data = parseBase64DataUrl({ dataUrl: block.url });\n            if (!data) {\n                throw new Error(`URL audio blocks with source_type ${block.source_type} must be formatted as a data URL for ChatOpenAI`);\n            }\n            const rawMimeType = data.mime_type || block.mime_type || \"\";\n            let mimeType;\n            try {\n                mimeType = parseMimeType(rawMimeType);\n            }\n            catch {\n                throw new Error(`Audio blocks with source_type ${block.source_type} must have mime type of audio/wav or audio/mp3`);\n            }\n            if (mimeType.type !== \"audio\" ||\n                (mimeType.subtype !== \"wav\" && mimeType.subtype !== \"mp3\")) {\n                throw new Error(`Audio blocks with source_type ${block.source_type} must have mime type of audio/wav or audio/mp3`);\n            }\n            return {\n                type: \"input_audio\",\n                input_audio: {\n                    format: mimeType.subtype,\n                    data: data.data,\n                },\n            };\n        }\n        if (block.source_type === \"base64\") {\n            let mimeType;\n            try {\n                mimeType = parseMimeType(block.mime_type ?? \"\");\n            }\n            catch {\n                throw new Error(`Audio blocks with source_type ${block.source_type} must have mime type of audio/wav or audio/mp3`);\n            }\n            if (mimeType.type !== \"audio\" ||\n                (mimeType.subtype !== \"wav\" && mimeType.subtype !== \"mp3\")) {\n                throw new Error(`Audio blocks with source_type ${block.source_type} must have mime type of audio/wav or audio/mp3`);\n            }\n            return {\n                type: \"input_audio\",\n                input_audio: {\n                    format: mimeType.subtype,\n                    data: block.data,\n                },\n            };\n        }\n        throw new Error(`Audio content blocks with source_type ${block.source_type} are not supported for ChatOpenAI`);\n    },\n    fromStandardFileBlock(block) {\n        if (block.source_type === \"url\") {\n            const data = parseBase64DataUrl({ dataUrl: block.url });\n            if (!data) {\n                throw new Error(`URL file blocks with source_type ${block.source_type} must be formatted as a data URL for ChatOpenAI`);\n            }\n            return {\n                type: \"file\",\n                file: {\n                    file_data: block.url, // formatted as base64 data URL\n                    ...(block.metadata?.filename || block.metadata?.name\n                        ? {\n                            filename: (block.metadata?.filename ||\n                                block.metadata?.name),\n                        }\n                        : {}),\n                },\n            };\n        }\n        if (block.source_type === \"base64\") {\n            return {\n                type: \"file\",\n                file: {\n                    file_data: `data:${block.mime_type ?? \"\"};base64,${block.data}`,\n                    ...(block.metadata?.filename ||\n                        block.metadata?.name ||\n                        block.metadata?.title\n                        ? {\n                            filename: (block.metadata?.filename ||\n                                block.metadata?.name ||\n                                block.metadata?.title),\n                        }\n                        : {}),\n                },\n            };\n        }\n        if (block.source_type === \"id\") {\n            return {\n                type: \"file\",\n                file: {\n                    file_id: block.id,\n                },\n            };\n        }\n        throw new Error(`File content blocks with source_type ${block.source_type} are not supported for ChatOpenAI`);\n    },\n};\n// Used in LangSmith, export is important here\n// TODO: put this conversion elsewhere\nexport function _convertMessagesToOpenAIParams(messages, model) {\n    // TODO: Function messages do not support array content, fix cast\n    return messages.flatMap((message) => {\n        let role = messageToOpenAIRole(message);\n        if (role === \"system\" && isReasoningModel(model)) {\n            role = \"developer\";\n        }\n        const content = typeof message.content === \"string\"\n            ? message.content\n            : message.content.map((m) => {\n                if (isDataContentBlock(m)) {\n                    return convertToProviderContentBlock(m, completionsApiContentBlockConverter);\n                }\n                return m;\n            });\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const completionParam = {\n            role,\n            content,\n        };\n        if (message.name != null) {\n            completionParam.name = message.name;\n        }\n        if (message.additional_kwargs.function_call != null) {\n            completionParam.function_call = message.additional_kwargs.function_call;\n            completionParam.content = \"\";\n        }\n        if (isAIMessage(message) && !!message.tool_calls?.length) {\n            completionParam.tool_calls = message.tool_calls.map(convertLangChainToolCallToOpenAI);\n            completionParam.content = \"\";\n        }\n        else {\n            if (message.additional_kwargs.tool_calls != null) {\n                completionParam.tool_calls = message.additional_kwargs.tool_calls;\n            }\n            if (message.tool_call_id != null) {\n                completionParam.tool_call_id = message.tool_call_id;\n            }\n        }\n        if (message.additional_kwargs.audio &&\n            typeof message.additional_kwargs.audio === \"object\" &&\n            \"id\" in message.additional_kwargs.audio) {\n            const audioMessage = {\n                role: \"assistant\",\n                audio: {\n                    id: message.additional_kwargs.audio.id,\n                },\n            };\n            return [\n                completionParam,\n                audioMessage,\n            ];\n        }\n        return completionParam;\n    });\n}\n/** @internal */\nclass BaseChatOpenAI extends BaseChatModel {\n    _llmType() {\n        return \"openai\";\n    }\n    static lc_name() {\n        return \"ChatOpenAI\";\n    }\n    get callKeys() {\n        return [\n            ...super.callKeys,\n            \"options\",\n            \"function_call\",\n            \"functions\",\n            \"tools\",\n            \"tool_choice\",\n            \"promptIndex\",\n            \"response_format\",\n            \"seed\",\n            \"reasoning\",\n            \"service_tier\",\n        ];\n    }\n    get lc_secrets() {\n        return {\n            apiKey: \"OPENAI_API_KEY\",\n            organization: \"OPENAI_ORGANIZATION\",\n        };\n    }\n    get lc_aliases() {\n        return {\n            apiKey: \"openai_api_key\",\n            modelName: \"model\",\n        };\n    }\n    get lc_serializable_keys() {\n        return [\n            \"configuration\",\n            \"logprobs\",\n            \"topLogprobs\",\n            \"prefixMessages\",\n            \"supportsStrictToolCalling\",\n            \"modalities\",\n            \"audio\",\n            \"temperature\",\n            \"maxTokens\",\n            \"topP\",\n            \"frequencyPenalty\",\n            \"presencePenalty\",\n            \"n\",\n            \"logitBias\",\n            \"user\",\n            \"streaming\",\n            \"streamUsage\",\n            \"model\",\n            \"modelName\",\n            \"modelKwargs\",\n            \"stop\",\n            \"stopSequences\",\n            \"timeout\",\n            \"apiKey\",\n            \"cache\",\n            \"maxConcurrency\",\n            \"maxRetries\",\n            \"verbose\",\n            \"callbacks\",\n            \"tags\",\n            \"metadata\",\n            \"disableStreaming\",\n            \"zdrEnabled\",\n            \"reasoning\",\n        ];\n    }\n    getLsParams(options) {\n        const params = this.invocationParams(options);\n        return {\n            ls_provider: \"openai\",\n            ls_model_name: this.model,\n            ls_model_type: \"chat\",\n            ls_temperature: params.temperature ?? undefined,\n            ls_max_tokens: params.max_tokens ?? undefined,\n            ls_stop: options.stop,\n        };\n    }\n    /** @ignore */\n    _identifyingParams() {\n        return {\n            model_name: this.model,\n            ...this.invocationParams(),\n            ...this.clientConfig,\n        };\n    }\n    /**\n     * Get the identifying parameters for the model\n     */\n    identifyingParams() {\n        return this._identifyingParams();\n    }\n    constructor(fields) {\n        super(fields ?? {});\n        Object.defineProperty(this, \"temperature\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"topP\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"frequencyPenalty\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"presencePenalty\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"n\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"logitBias\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"gpt-3.5-turbo\"\n        });\n        Object.defineProperty(this, \"modelKwargs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"stop\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"stopSequences\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"user\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"timeout\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"streaming\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        Object.defineProperty(this, \"streamUsage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"maxTokens\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"logprobs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"topLogprobs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"apiKey\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"organization\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"__includeRawResponse\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"clientConfig\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * Whether the model supports the `strict` argument when passing in tools.\n         * If `undefined` the `strict` argument will not be passed to OpenAI.\n         */\n        Object.defineProperty(this, \"supportsStrictToolCalling\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"audio\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"modalities\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"reasoning\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * Must be set to `true` in tenancies with Zero Data Retention. Setting to `true` will disable\n         * output storage in the Responses API, but this DOES NOT enable Zero Data Retention in your\n         * OpenAI organization or project. This must be configured directly with OpenAI.\n         *\n         * See:\n         * https://help.openai.com/en/articles/10503543-data-residency-for-the-openai-api\n         * https://platform.openai.com/docs/api-reference/responses/create#responses-create-store\n         *\n         * @default false\n         */\n        Object.defineProperty(this, \"zdrEnabled\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * Service tier to use for this request. Can be \"auto\", \"default\", or \"flex\" or \"priority\".\n         * Specifies the service tier for prioritization and latency optimization.\n         */\n        Object.defineProperty(this, \"service_tier\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        this.apiKey =\n            fields?.apiKey ??\n                fields?.configuration?.apiKey ??\n                getEnvironmentVariable(\"OPENAI_API_KEY\");\n        this.organization =\n            fields?.configuration?.organization ??\n                getEnvironmentVariable(\"OPENAI_ORGANIZATION\");\n        this.model = fields?.model ?? fields?.modelName ?? this.model;\n        this.modelKwargs = fields?.modelKwargs ?? {};\n        this.timeout = fields?.timeout;\n        this.temperature = fields?.temperature ?? this.temperature;\n        this.topP = fields?.topP ?? this.topP;\n        this.frequencyPenalty = fields?.frequencyPenalty ?? this.frequencyPenalty;\n        this.presencePenalty = fields?.presencePenalty ?? this.presencePenalty;\n        this.logprobs = fields?.logprobs;\n        this.topLogprobs = fields?.topLogprobs;\n        this.n = fields?.n ?? this.n;\n        this.logitBias = fields?.logitBias;\n        this.stop = fields?.stopSequences ?? fields?.stop;\n        this.stopSequences = this.stop;\n        this.user = fields?.user;\n        this.__includeRawResponse = fields?.__includeRawResponse;\n        this.audio = fields?.audio;\n        this.modalities = fields?.modalities;\n        this.reasoning = fields?.reasoning;\n        this.maxTokens = fields?.maxCompletionTokens ?? fields?.maxTokens;\n        this.disableStreaming = fields?.disableStreaming ?? this.disableStreaming;\n        this.streaming = fields?.streaming ?? false;\n        if (this.disableStreaming)\n            this.streaming = false;\n        this.streamUsage = fields?.streamUsage ?? this.streamUsage;\n        if (this.disableStreaming)\n            this.streamUsage = false;\n        this.clientConfig = {\n            apiKey: this.apiKey,\n            organization: this.organization,\n            dangerouslyAllowBrowser: true,\n            ...fields?.configuration,\n        };\n        // If `supportsStrictToolCalling` is explicitly set, use that value.\n        // Else leave undefined so it's not passed to OpenAI.\n        if (fields?.supportsStrictToolCalling !== undefined) {\n            this.supportsStrictToolCalling = fields.supportsStrictToolCalling;\n        }\n        if (fields?.service_tier !== undefined) {\n            this.service_tier = fields.service_tier;\n        }\n        this.zdrEnabled = fields?.zdrEnabled ?? false;\n    }\n    /**\n     * Returns backwards compatible reasoning parameters from constructor params and call options\n     * @internal\n     */\n    _getReasoningParams(options) {\n        if (!isReasoningModel(this.model)) {\n            return;\n        }\n        // apply options in reverse order of importance -- newer options supersede older options\n        let reasoning;\n        if (this.reasoning !== undefined) {\n            reasoning = {\n                ...reasoning,\n                ...this.reasoning,\n            };\n        }\n        if (options?.reasoning !== undefined) {\n            reasoning = {\n                ...reasoning,\n                ...options.reasoning,\n            };\n        }\n        return reasoning;\n    }\n    /**\n     * Returns an openai compatible response format from a set of options\n     * @internal\n     */\n    _getResponseFormat(resFormat) {\n        if (resFormat &&\n            resFormat.type === \"json_schema\" &&\n            resFormat.json_schema.schema &&\n            isInteropZodSchema(resFormat.json_schema.schema)) {\n            return interopZodResponseFormat(resFormat.json_schema.schema, resFormat.json_schema.name, {\n                description: resFormat.json_schema.description,\n            });\n        }\n        return resFormat;\n    }\n    _getClientOptions(options) {\n        if (!this.client) {\n            const openAIEndpointConfig = {\n                baseURL: this.clientConfig.baseURL,\n            };\n            const endpoint = getEndpoint(openAIEndpointConfig);\n            const params = {\n                ...this.clientConfig,\n                baseURL: endpoint,\n                timeout: this.timeout,\n                maxRetries: 0,\n            };\n            if (!params.baseURL) {\n                delete params.baseURL;\n            }\n            this.client = new OpenAIClient(params);\n        }\n        const requestOptions = {\n            ...this.clientConfig,\n            ...options,\n        };\n        return requestOptions;\n    }\n    // TODO: move to completions class\n    _convertChatOpenAIToolToCompletionsTool(tool, fields) {\n        if (isOpenAITool(tool)) {\n            if (fields?.strict !== undefined) {\n                return {\n                    ...tool,\n                    function: {\n                        ...tool.function,\n                        strict: fields.strict,\n                    },\n                };\n            }\n            return tool;\n        }\n        return _convertToOpenAITool(tool, fields);\n    }\n    bindTools(tools, kwargs) {\n        let strict;\n        if (kwargs?.strict !== undefined) {\n            strict = kwargs.strict;\n        }\n        else if (this.supportsStrictToolCalling !== undefined) {\n            strict = this.supportsStrictToolCalling;\n        }\n        return this.withConfig({\n            tools: tools.map((tool) => isBuiltInTool(tool)\n                ? tool\n                : this._convertChatOpenAIToolToCompletionsTool(tool, { strict })),\n            ...kwargs,\n        });\n    }\n    /** @ignore */\n    _combineLLMOutput(...llmOutputs) {\n        return llmOutputs.reduce((acc, llmOutput) => {\n            if (llmOutput && llmOutput.tokenUsage) {\n                acc.tokenUsage.completionTokens +=\n                    llmOutput.tokenUsage.completionTokens ?? 0;\n                acc.tokenUsage.promptTokens += llmOutput.tokenUsage.promptTokens ?? 0;\n                acc.tokenUsage.totalTokens += llmOutput.tokenUsage.totalTokens ?? 0;\n            }\n            return acc;\n        }, {\n            tokenUsage: {\n                completionTokens: 0,\n                promptTokens: 0,\n                totalTokens: 0,\n            },\n        });\n    }\n    async getNumTokensFromMessages(messages) {\n        let totalCount = 0;\n        let tokensPerMessage = 0;\n        let tokensPerName = 0;\n        // From: https://github.com/openai/openai-cookbook/blob/main/examples/How_to_format_inputs_to_ChatGPT_models.ipynb\n        if (this.model === \"gpt-3.5-turbo-0301\") {\n            tokensPerMessage = 4;\n            tokensPerName = -1;\n        }\n        else {\n            tokensPerMessage = 3;\n            tokensPerName = 1;\n        }\n        const countPerMessage = await Promise.all(messages.map(async (message) => {\n            const textCount = await this.getNumTokens(message.content);\n            const roleCount = await this.getNumTokens(messageToOpenAIRole(message));\n            const nameCount = message.name !== undefined\n                ? tokensPerName + (await this.getNumTokens(message.name))\n                : 0;\n            let count = textCount + tokensPerMessage + roleCount + nameCount;\n            // From: https://github.com/hmarr/openai-chat-tokens/blob/main/src/index.ts messageTokenEstimate\n            const openAIMessage = message;\n            if (openAIMessage._getType() === \"function\") {\n                count -= 2;\n            }\n            if (openAIMessage.additional_kwargs?.function_call) {\n                count += 3;\n            }\n            if (openAIMessage?.additional_kwargs.function_call?.name) {\n                count += await this.getNumTokens(openAIMessage.additional_kwargs.function_call?.name);\n            }\n            if (openAIMessage.additional_kwargs.function_call?.arguments) {\n                try {\n                    count += await this.getNumTokens(\n                    // Remove newlines and spaces\n                    JSON.stringify(JSON.parse(openAIMessage.additional_kwargs.function_call?.arguments)));\n                }\n                catch (error) {\n                    console.error(\"Error parsing function arguments\", error, JSON.stringify(openAIMessage.additional_kwargs.function_call));\n                    count += await this.getNumTokens(openAIMessage.additional_kwargs.function_call?.arguments);\n                }\n            }\n            totalCount += count;\n            return count;\n        }));\n        totalCount += 3; // every reply is primed with <|start|>assistant<|message|>\n        return { totalCount, countPerMessage };\n    }\n    /** @internal */\n    async _getNumTokensFromGenerations(generations) {\n        const generationUsages = await Promise.all(generations.map(async (generation) => {\n            if (generation.message.additional_kwargs?.function_call) {\n                return (await this.getNumTokensFromMessages([generation.message]))\n                    .countPerMessage[0];\n            }\n            else {\n                return await this.getNumTokens(generation.message.content);\n            }\n        }));\n        return generationUsages.reduce((a, b) => a + b, 0);\n    }\n    /** @internal */\n    async _getEstimatedTokenCountFromPrompt(messages, functions, function_call) {\n        // It appears that if functions are present, the first system message is padded with a trailing newline. This\n        // was inferred by trying lots of combinations of messages and functions and seeing what the token counts were.\n        let tokens = (await this.getNumTokensFromMessages(messages)).totalCount;\n        // If there are functions, add the function definitions as they count towards token usage\n        if (functions && function_call !== \"auto\") {\n            const promptDefinitions = formatFunctionDefinitions(functions);\n            tokens += await this.getNumTokens(promptDefinitions);\n            tokens += 9; // Add nine per completion\n        }\n        // If there's a system message _and_ functions are present, subtract four tokens. I assume this is because\n        // functions typically add a system message, but reuse the first one if it's already there. This offsets\n        // the extra 9 tokens added by the function definitions.\n        if (functions && messages.find((m) => m._getType() === \"system\")) {\n            tokens -= 4;\n        }\n        // If function_call is 'none', add one token.\n        // If it's a FunctionCall object, add 4 + the number of tokens in the function name.\n        // If it's undefined or 'auto', don't add anything.\n        if (function_call === \"none\") {\n            tokens += 1;\n        }\n        else if (typeof function_call === \"object\") {\n            tokens += (await this.getNumTokens(function_call.name)) + 4;\n        }\n        return tokens;\n    }\n    withStructuredOutput(outputSchema, config) {\n        // ):\n        // | Runnable<BaseLanguageModelInput, RunOutput>\n        // | Runnable<\n        //     BaseLanguageModelInput,\n        //     { raw: BaseMessage; parsed: RunOutput }\n        //   > {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        let schema;\n        let name;\n        let method;\n        let includeRaw;\n        if (isStructuredOutputMethodParams(outputSchema)) {\n            schema = outputSchema.schema;\n            name = outputSchema.name;\n            method = outputSchema.method;\n            includeRaw = outputSchema.includeRaw;\n        }\n        else {\n            schema = outputSchema;\n            name = config?.name;\n            method = config?.method;\n            includeRaw = config?.includeRaw;\n        }\n        let llm;\n        let outputParser;\n        if (config?.strict !== undefined && method === \"jsonMode\") {\n            throw new Error(\"Argument `strict` is only supported for `method` = 'function_calling'\");\n        }\n        if (!this.model.startsWith(\"gpt-3\") &&\n            !this.model.startsWith(\"gpt-4-\") &&\n            this.model !== \"gpt-4\") {\n            if (method === undefined) {\n                method = \"jsonSchema\";\n            }\n        }\n        else if (method === \"jsonSchema\") {\n            console.warn(`[WARNING]: JSON Schema is not supported for model \"${this.model}\". Falling back to tool calling.`);\n        }\n        if (method === \"jsonMode\") {\n            let outputFormatSchema;\n            if (isInteropZodSchema(schema)) {\n                outputParser = StructuredOutputParser.fromZodSchema(schema);\n                outputFormatSchema = toJsonSchema(schema);\n            }\n            else {\n                outputParser = new JsonOutputParser();\n            }\n            llm = this.withConfig({\n                response_format: { type: \"json_object\" },\n                ls_structured_output_format: {\n                    kwargs: { method: \"jsonMode\" },\n                    schema: outputFormatSchema,\n                },\n            });\n        }\n        else if (method === \"jsonSchema\") {\n            llm = this.withConfig({\n                response_format: {\n                    type: \"json_schema\",\n                    json_schema: {\n                        name: name ?? \"extract\",\n                        description: getSchemaDescription(schema),\n                        schema,\n                        strict: config?.strict,\n                    },\n                },\n                ls_structured_output_format: {\n                    kwargs: { method: \"jsonSchema\" },\n                    schema: toJsonSchema(schema),\n                },\n            });\n            if (isInteropZodSchema(schema)) {\n                const altParser = StructuredOutputParser.fromZodSchema(schema);\n                outputParser = RunnableLambda.from((aiMessage) => {\n                    if (\"parsed\" in aiMessage.additional_kwargs) {\n                        return aiMessage.additional_kwargs.parsed;\n                    }\n                    return altParser;\n                });\n            }\n            else {\n                outputParser = new JsonOutputParser();\n            }\n        }\n        else {\n            let functionName = name ?? \"extract\";\n            // Is function calling\n            if (isInteropZodSchema(schema)) {\n                const asJsonSchema = toJsonSchema(schema);\n                llm = this.withConfig({\n                    tools: [\n                        {\n                            type: \"function\",\n                            function: {\n                                name: functionName,\n                                description: asJsonSchema.description,\n                                parameters: asJsonSchema,\n                            },\n                        },\n                    ],\n                    tool_choice: {\n                        type: \"function\",\n                        function: {\n                            name: functionName,\n                        },\n                    },\n                    ls_structured_output_format: {\n                        kwargs: { method: \"functionCalling\" },\n                        schema: asJsonSchema,\n                    },\n                    // Do not pass `strict` argument to OpenAI if `config.strict` is undefined\n                    ...(config?.strict !== undefined ? { strict: config.strict } : {}),\n                });\n                outputParser = new JsonOutputKeyToolsParser({\n                    returnSingle: true,\n                    keyName: functionName,\n                    zodSchema: schema,\n                });\n            }\n            else {\n                let openAIFunctionDefinition;\n                if (typeof schema.name === \"string\" &&\n                    typeof schema.parameters === \"object\" &&\n                    schema.parameters != null) {\n                    openAIFunctionDefinition = schema;\n                    functionName = schema.name;\n                }\n                else {\n                    functionName = schema.title ?? functionName;\n                    openAIFunctionDefinition = {\n                        name: functionName,\n                        description: schema.description ?? \"\",\n                        parameters: schema,\n                    };\n                }\n                llm = this.withConfig({\n                    tools: [\n                        {\n                            type: \"function\",\n                            function: openAIFunctionDefinition,\n                        },\n                    ],\n                    tool_choice: {\n                        type: \"function\",\n                        function: {\n                            name: functionName,\n                        },\n                    },\n                    ls_structured_output_format: {\n                        kwargs: { method: \"functionCalling\" },\n                        schema: toJsonSchema(schema),\n                    },\n                    // Do not pass `strict` argument to OpenAI if `config.strict` is undefined\n                    ...(config?.strict !== undefined ? { strict: config.strict } : {}),\n                });\n                outputParser = new JsonOutputKeyToolsParser({\n                    returnSingle: true,\n                    keyName: functionName,\n                });\n            }\n        }\n        if (!includeRaw) {\n            return llm.pipe(outputParser);\n        }\n        const parserAssign = RunnablePassthrough.assign({\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            parsed: (input, config) => outputParser.invoke(input.raw, config),\n        });\n        const parserNone = RunnablePassthrough.assign({\n            parsed: () => null,\n        });\n        const parsedWithFallback = parserAssign.withFallbacks({\n            fallbacks: [parserNone],\n        });\n        return RunnableSequence.from([{ raw: llm }, parsedWithFallback]);\n    }\n}\n/**\n * OpenAI Responses API implementation.\n *\n * Will be exported in a later version of @langchain/openai.\n *\n * @internal\n */\nexport class ChatOpenAIResponses extends BaseChatOpenAI {\n    invocationParams(options) {\n        let strict;\n        if (options?.strict !== undefined) {\n            strict = options.strict;\n        }\n        else if (this.supportsStrictToolCalling !== undefined) {\n            strict = this.supportsStrictToolCalling;\n        }\n        const params = {\n            model: this.model,\n            temperature: this.temperature,\n            top_p: this.topP,\n            user: this.user,\n            // if include_usage is set or streamUsage then stream must be set to true.\n            stream: this.streaming,\n            previous_response_id: options?.previous_response_id,\n            truncation: options?.truncation,\n            include: options?.include,\n            tools: options?.tools?.length\n                ? this._reduceChatOpenAITools(options.tools, {\n                    stream: this.streaming,\n                    strict,\n                })\n                : undefined,\n            tool_choice: isBuiltInToolChoice(options?.tool_choice)\n                ? options?.tool_choice\n                : (() => {\n                    const formatted = formatToOpenAIToolChoice(options?.tool_choice);\n                    if (typeof formatted === \"object\" && \"type\" in formatted) {\n                        return { type: \"function\", name: formatted.function.name };\n                    }\n                    else {\n                        return undefined;\n                    }\n                })(),\n            text: (() => {\n                if (options?.text)\n                    return options.text;\n                const format = this._getResponseFormat(options?.response_format);\n                if (format?.type === \"json_schema\") {\n                    if (format.json_schema.schema != null) {\n                        return {\n                            format: {\n                                type: \"json_schema\",\n                                schema: format.json_schema.schema,\n                                description: format.json_schema.description,\n                                name: format.json_schema.name,\n                                strict: format.json_schema.strict,\n                            },\n                        };\n                    }\n                    return undefined;\n                }\n                return { format };\n            })(),\n            parallel_tool_calls: options?.parallel_tool_calls,\n            max_output_tokens: this.maxTokens === -1 ? undefined : this.maxTokens,\n            ...(this.zdrEnabled ? { store: false } : {}),\n            ...this.modelKwargs,\n        };\n        const reasoning = this._getReasoningParams(options);\n        if (reasoning !== undefined) {\n            params.reasoning = reasoning;\n        }\n        return params;\n    }\n    async _generate(messages, options) {\n        const invocationParams = this.invocationParams(options);\n        if (invocationParams.stream) {\n            const stream = this._streamResponseChunks(messages, options);\n            let finalChunk;\n            for await (const chunk of stream) {\n                chunk.message.response_metadata = {\n                    ...chunk.generationInfo,\n                    ...chunk.message.response_metadata,\n                };\n                finalChunk = finalChunk?.concat(chunk) ?? chunk;\n            }\n            return {\n                generations: finalChunk ? [finalChunk] : [],\n                llmOutput: {\n                    estimatedTokenUsage: finalChunk?.message\n                        ?.usage_metadata,\n                },\n            };\n        }\n        else {\n            const input = this._convertMessagesToResponsesParams(messages);\n            const data = await this.completionWithRetry({\n                input,\n                ...invocationParams,\n                stream: false,\n            }, { signal: options?.signal, ...options?.options });\n            return {\n                generations: [\n                    {\n                        text: data.output_text,\n                        message: this._convertResponsesMessageToBaseMessage(data),\n                    },\n                ],\n                llmOutput: {\n                    id: data.id,\n                    estimatedTokenUsage: data.usage\n                        ? {\n                            promptTokens: data.usage.input_tokens,\n                            completionTokens: data.usage.output_tokens,\n                            totalTokens: data.usage.total_tokens,\n                        }\n                        : undefined,\n                },\n            };\n        }\n    }\n    async *_streamResponseChunks(messages, options) {\n        const streamIterable = await this.completionWithRetry({\n            ...this.invocationParams(options),\n            input: this._convertMessagesToResponsesParams(messages),\n            stream: true,\n        }, options);\n        for await (const data of streamIterable) {\n            const chunk = this._convertResponsesDeltaToBaseMessageChunk(data);\n            if (chunk == null)\n                continue;\n            yield chunk;\n        }\n    }\n    async completionWithRetry(request, requestOptions) {\n        return this.caller.call(async () => {\n            const clientOptions = this._getClientOptions(requestOptions);\n            try {\n                // use parse if dealing with json_schema\n                if (request.text?.format?.type === \"json_schema\" && !request.stream) {\n                    return await this.client.responses.parse(request, clientOptions);\n                }\n                return await this.client.responses.create(request, clientOptions);\n            }\n            catch (e) {\n                const error = wrapOpenAIClientError(e);\n                throw error;\n            }\n        });\n    }\n    /** @internal */\n    _convertResponsesMessageToBaseMessage(response) {\n        if (response.error) {\n            // TODO: add support for `addLangChainErrorFields`\n            const error = new Error(response.error.message);\n            error.name = response.error.code;\n            throw error;\n        }\n        let messageId;\n        const content = [];\n        const tool_calls = [];\n        const invalid_tool_calls = [];\n        const response_metadata = {\n            model: response.model,\n            created_at: response.created_at,\n            id: response.id,\n            incomplete_details: response.incomplete_details,\n            metadata: response.metadata,\n            object: response.object,\n            status: response.status,\n            user: response.user,\n            service_tier: response.service_tier,\n            // for compatibility with chat completion calls.\n            model_name: response.model,\n        };\n        const additional_kwargs = {};\n        for (const item of response.output) {\n            if (item.type === \"message\") {\n                messageId = item.id;\n                content.push(...item.content.flatMap((part) => {\n                    if (part.type === \"output_text\") {\n                        if (\"parsed\" in part && part.parsed != null) {\n                            additional_kwargs.parsed = part.parsed;\n                        }\n                        return {\n                            type: \"text\",\n                            text: part.text,\n                            annotations: part.annotations,\n                        };\n                    }\n                    if (part.type === \"refusal\") {\n                        additional_kwargs.refusal = part.refusal;\n                        return [];\n                    }\n                    return part;\n                }));\n            }\n            else if (item.type === \"function_call\") {\n                const fnAdapter = {\n                    function: { name: item.name, arguments: item.arguments },\n                    id: item.call_id,\n                };\n                try {\n                    tool_calls.push(parseToolCall(fnAdapter, { returnId: true }));\n                }\n                catch (e) {\n                    let errMessage;\n                    if (typeof e === \"object\" &&\n                        e != null &&\n                        \"message\" in e &&\n                        typeof e.message === \"string\") {\n                        errMessage = e.message;\n                    }\n                    invalid_tool_calls.push(makeInvalidToolCall(fnAdapter, errMessage));\n                }\n                additional_kwargs[_FUNCTION_CALL_IDS_MAP_KEY] ??= {};\n                if (item.id) {\n                    additional_kwargs[_FUNCTION_CALL_IDS_MAP_KEY][item.call_id] = item.id;\n                }\n            }\n            else if (item.type === \"reasoning\") {\n                additional_kwargs.reasoning = item;\n            }\n            else {\n                additional_kwargs.tool_outputs ??= [];\n                additional_kwargs.tool_outputs.push(item);\n            }\n        }\n        return new AIMessage({\n            id: messageId,\n            content,\n            tool_calls,\n            invalid_tool_calls,\n            usage_metadata: response.usage,\n            additional_kwargs,\n            response_metadata,\n        });\n    }\n    /** @internal */\n    _convertResponsesDeltaToBaseMessageChunk(chunk) {\n        const content = [];\n        let generationInfo = {};\n        let usage_metadata;\n        const tool_call_chunks = [];\n        const response_metadata = {};\n        const additional_kwargs = {};\n        let id;\n        if (chunk.type === \"response.output_text.delta\") {\n            content.push({\n                type: \"text\",\n                text: chunk.delta,\n                index: chunk.content_index,\n            });\n        }\n        else if (chunk.type === \"response.output_text_annotation.added\") {\n            content.push({\n                type: \"text\",\n                text: \"\",\n                annotations: [chunk.annotation],\n                index: chunk.content_index,\n            });\n        }\n        else if (chunk.type === \"response.output_item.added\" &&\n            chunk.item.type === \"message\") {\n            id = chunk.item.id;\n        }\n        else if (chunk.type === \"response.output_item.added\" &&\n            chunk.item.type === \"function_call\") {\n            tool_call_chunks.push({\n                type: \"tool_call_chunk\",\n                name: chunk.item.name,\n                args: chunk.item.arguments,\n                id: chunk.item.call_id,\n                index: chunk.output_index,\n            });\n            additional_kwargs[_FUNCTION_CALL_IDS_MAP_KEY] = {\n                [chunk.item.call_id]: chunk.item.id,\n            };\n        }\n        else if (chunk.type === \"response.output_item.done\" &&\n            [\n                \"web_search_call\",\n                \"file_search_call\",\n                \"computer_call\",\n                \"code_interpreter_call\",\n                \"mcp_call\",\n                \"mcp_list_tools\",\n                \"mcp_approval_request\",\n                \"image_generation_call\",\n            ].includes(chunk.item.type)) {\n            additional_kwargs.tool_outputs = [chunk.item];\n        }\n        else if (chunk.type === \"response.created\") {\n            response_metadata.id = chunk.response.id;\n            response_metadata.model_name = chunk.response.model;\n            response_metadata.model = chunk.response.model;\n        }\n        else if (chunk.type === \"response.completed\") {\n            const msg = this._convertResponsesMessageToBaseMessage(chunk.response);\n            usage_metadata = chunk.response.usage;\n            if (chunk.response.text?.format?.type === \"json_schema\") {\n                additional_kwargs.parsed ??= JSON.parse(msg.text);\n            }\n            for (const [key, value] of Object.entries(chunk.response)) {\n                if (key !== \"id\")\n                    response_metadata[key] = value;\n            }\n        }\n        else if (chunk.type === \"response.function_call_arguments.delta\") {\n            tool_call_chunks.push({\n                type: \"tool_call_chunk\",\n                args: chunk.delta,\n                index: chunk.output_index,\n            });\n        }\n        else if (chunk.type === \"response.web_search_call.completed\" ||\n            chunk.type === \"response.file_search_call.completed\") {\n            generationInfo = {\n                tool_outputs: {\n                    id: chunk.item_id,\n                    type: chunk.type.replace(\"response.\", \"\").replace(\".completed\", \"\"),\n                    status: \"completed\",\n                },\n            };\n        }\n        else if (chunk.type === \"response.refusal.done\") {\n            additional_kwargs.refusal = chunk.refusal;\n        }\n        else if (chunk.type === \"response.output_item.added\" &&\n            \"item\" in chunk &&\n            chunk.item.type === \"reasoning\") {\n            const summary = chunk\n                .item.summary\n                ? chunk.item.summary.map((s, index) => ({\n                    ...s,\n                    index,\n                }))\n                : undefined;\n            additional_kwargs.reasoning = {\n                // We only capture ID in the first chunk or else the concatenated result of all chunks will\n                // have an ID field that is repeated once per chunk. There is special handling for the `type`\n                // field that prevents this, however.\n                id: chunk.item.id,\n                type: chunk.item.type,\n                ...(summary ? { summary } : {}),\n            };\n        }\n        else if (chunk.type === \"response.reasoning_summary_part.added\") {\n            additional_kwargs.reasoning = {\n                type: \"reasoning\",\n                summary: [{ ...chunk.part, index: chunk.summary_index }],\n            };\n        }\n        else if (chunk.type === \"response.reasoning_summary_text.delta\") {\n            additional_kwargs.reasoning = {\n                type: \"reasoning\",\n                summary: [\n                    {\n                        text: chunk.delta,\n                        type: \"summary_text\",\n                        index: chunk.summary_index,\n                    },\n                ],\n            };\n        }\n        else if (chunk.type === \"response.image_generation_call.partial_image\") {\n            // noop/fixme: retaining partial images in a message chunk means that _all_\n            // partial images get kept in history, so we don't do anything here.\n            return null;\n        }\n        else {\n            return null;\n        }\n        return new ChatGenerationChunk({\n            // Legacy reasons, `onLLMNewToken` should pulls this out\n            text: content.map((part) => part.text).join(\"\"),\n            message: new AIMessageChunk({\n                id,\n                content,\n                tool_call_chunks,\n                usage_metadata,\n                additional_kwargs,\n                response_metadata,\n            }),\n            generationInfo,\n        });\n    }\n    /** @internal */\n    _convertMessagesToResponsesParams(messages) {\n        return messages.flatMap((lcMsg) => {\n            const additional_kwargs = lcMsg.additional_kwargs;\n            let role = messageToOpenAIRole(lcMsg);\n            if (role === \"system\" && isReasoningModel(this.model))\n                role = \"developer\";\n            if (role === \"function\") {\n                throw new Error(\"Function messages are not supported in Responses API\");\n            }\n            if (role === \"tool\") {\n                const toolMessage = lcMsg;\n                // Handle computer call output\n                if (additional_kwargs?.type === \"computer_call_output\") {\n                    const output = (() => {\n                        if (typeof toolMessage.content === \"string\") {\n                            return {\n                                type: \"computer_screenshot\",\n                                image_url: toolMessage.content,\n                            };\n                        }\n                        if (Array.isArray(toolMessage.content)) {\n                            const oaiScreenshot = toolMessage.content.find((i) => i.type === \"computer_screenshot\");\n                            if (oaiScreenshot)\n                                return oaiScreenshot;\n                            const lcImage = toolMessage.content.find((i) => i.type === \"image_url\");\n                            if (lcImage) {\n                                return {\n                                    type: \"computer_screenshot\",\n                                    image_url: typeof lcImage.image_url === \"string\"\n                                        ? lcImage.image_url\n                                        : lcImage.image_url.url,\n                                };\n                            }\n                        }\n                        throw new Error(\"Invalid computer call output\");\n                    })();\n                    return {\n                        type: \"computer_call_output\",\n                        output,\n                        call_id: toolMessage.tool_call_id,\n                    };\n                }\n                return {\n                    type: \"function_call_output\",\n                    call_id: toolMessage.tool_call_id,\n                    id: toolMessage.id?.startsWith(\"fc_\") ? toolMessage.id : undefined,\n                    output: typeof toolMessage.content !== \"string\"\n                        ? JSON.stringify(toolMessage.content)\n                        : toolMessage.content,\n                };\n            }\n            if (role === \"assistant\") {\n                // if we have the original response items, just reuse them\n                if (!this.zdrEnabled &&\n                    lcMsg.response_metadata.output != null &&\n                    Array.isArray(lcMsg.response_metadata.output) &&\n                    lcMsg.response_metadata.output.length > 0 &&\n                    lcMsg.response_metadata.output.every((item) => \"type\" in item)) {\n                    return lcMsg.response_metadata.output;\n                }\n                // otherwise, try to reconstruct the response from what we have\n                const input = [];\n                // reasoning items\n                if (additional_kwargs?.reasoning && !this.zdrEnabled) {\n                    const reasoningItem = this._convertReasoningSummary(additional_kwargs.reasoning);\n                    input.push(reasoningItem);\n                }\n                // ai content\n                let { content } = lcMsg;\n                if (additional_kwargs?.refusal) {\n                    if (typeof content === \"string\") {\n                        content = [\n                            { type: \"output_text\", text: content, annotations: [] },\n                        ];\n                    }\n                    content = [\n                        ...content,\n                        { type: \"refusal\", refusal: additional_kwargs.refusal },\n                    ];\n                }\n                input.push({\n                    type: \"message\",\n                    role: \"assistant\",\n                    ...(lcMsg.id && !this.zdrEnabled && lcMsg.id.startsWith(\"msg_\")\n                        ? { id: lcMsg.id }\n                        : {}),\n                    content: typeof content === \"string\"\n                        ? content\n                        : content.flatMap((item) => {\n                            if (item.type === \"text\") {\n                                return {\n                                    type: \"output_text\",\n                                    text: item.text,\n                                    // @ts-expect-error TODO: add types for `annotations`\n                                    annotations: item.annotations ?? [],\n                                };\n                            }\n                            if (item.type === \"output_text\" ||\n                                item.type === \"refusal\") {\n                                return item;\n                            }\n                            return [];\n                        }),\n                });\n                const functionCallIds = additional_kwargs?.[_FUNCTION_CALL_IDS_MAP_KEY];\n                if (isAIMessage(lcMsg) && !!lcMsg.tool_calls?.length) {\n                    input.push(...lcMsg.tool_calls.map((toolCall) => ({\n                        type: \"function_call\",\n                        name: toolCall.name,\n                        arguments: JSON.stringify(toolCall.args),\n                        call_id: toolCall.id,\n                        ...(this.zdrEnabled\n                            ? { id: functionCallIds?.[toolCall.id] }\n                            : {}),\n                    })));\n                }\n                else if (additional_kwargs?.tool_calls) {\n                    input.push(...additional_kwargs.tool_calls.map((toolCall) => ({\n                        type: \"function_call\",\n                        name: toolCall.function.name,\n                        call_id: toolCall.id,\n                        arguments: toolCall.function.arguments,\n                        ...(this.zdrEnabled\n                            ? { id: functionCallIds?.[toolCall.id] }\n                            : {}),\n                    })));\n                }\n                const toolOutputs = lcMsg.response_metadata.output?.length\n                    ? lcMsg.response_metadata.output\n                    : additional_kwargs.tool_outputs;\n                const fallthroughCallTypes = [\n                    \"computer_call\",\n                    \"mcp_call\",\n                    \"code_interpreter_call\",\n                    \"image_generation_call\",\n                ];\n                if (toolOutputs != null) {\n                    const castToolOutputs = toolOutputs;\n                    const fallthroughCalls = castToolOutputs?.filter((item) => fallthroughCallTypes.includes(item.type));\n                    if (fallthroughCalls.length > 0)\n                        input.push(...fallthroughCalls);\n                }\n                return input;\n            }\n            if (role === \"user\" || role === \"system\" || role === \"developer\") {\n                if (typeof lcMsg.content === \"string\") {\n                    return { type: \"message\", role, content: lcMsg.content };\n                }\n                const messages = [];\n                const content = lcMsg.content.flatMap((item) => {\n                    if (item.type === \"mcp_approval_response\") {\n                        messages.push({\n                            type: \"mcp_approval_response\",\n                            approval_request_id: item.approval_request_id,\n                            approve: item.approve,\n                        });\n                    }\n                    if (isDataContentBlock(item)) {\n                        return convertToProviderContentBlock(item, completionsApiContentBlockConverter);\n                    }\n                    if (item.type === \"text\") {\n                        return {\n                            type: \"input_text\",\n                            text: item.text,\n                        };\n                    }\n                    if (item.type === \"image_url\") {\n                        return {\n                            type: \"input_image\",\n                            image_url: typeof item.image_url === \"string\"\n                                ? item.image_url\n                                : item.image_url.url,\n                            detail: typeof item.image_url === \"string\"\n                                ? \"auto\"\n                                : item.image_url.detail,\n                        };\n                    }\n                    if (item.type === \"input_text\" ||\n                        item.type === \"input_image\" ||\n                        item.type === \"input_file\") {\n                        return item;\n                    }\n                    return [];\n                });\n                if (content.length > 0) {\n                    messages.push({ type: \"message\", role, content });\n                }\n                return messages;\n            }\n            console.warn(`Unsupported role found when converting to OpenAI Responses API: ${role}`);\n            return [];\n        });\n    }\n    /** @internal */\n    _convertReasoningSummary(reasoning) {\n        // combine summary parts that have the the same index and then remove the indexes\n        const summary = (reasoning.summary.length > 1\n            ? reasoning.summary.reduce((acc, curr) => {\n                const last = acc.at(-1);\n                if (last.index === curr.index) {\n                    last.text += curr.text;\n                }\n                else {\n                    acc.push(curr);\n                }\n                return acc;\n            }, [{ ...reasoning.summary[0] }])\n            : reasoning.summary).map((s) => Object.fromEntries(Object.entries(s).filter(([k]) => k !== \"index\")));\n        return {\n            ...reasoning,\n            summary,\n        };\n    }\n    /** @internal */\n    _reduceChatOpenAITools(tools, fields) {\n        const reducedTools = [];\n        for (const tool of tools) {\n            if (isBuiltInTool(tool)) {\n                if (tool.type === \"image_generation\" && fields?.stream) {\n                    // OpenAI sends a 400 error if partial_images is not set and we want to stream.\n                    // We also set it to 1 since we don't support partial images yet.\n                    tool.partial_images = 1;\n                }\n                reducedTools.push(tool);\n            }\n            else if (isOpenAITool(tool)) {\n                reducedTools.push({\n                    type: \"function\",\n                    name: tool.function.name,\n                    parameters: tool.function.parameters,\n                    description: tool.function.description,\n                    strict: fields?.strict ?? null,\n                });\n            }\n        }\n        return reducedTools;\n    }\n}\n/**\n * OpenAI Completions API implementation.\n * @internal\n */\nexport class ChatOpenAICompletions extends BaseChatOpenAI {\n    /** @internal */\n    invocationParams(options, extra) {\n        let strict;\n        if (options?.strict !== undefined) {\n            strict = options.strict;\n        }\n        else if (this.supportsStrictToolCalling !== undefined) {\n            strict = this.supportsStrictToolCalling;\n        }\n        let streamOptionsConfig = {};\n        if (options?.stream_options !== undefined) {\n            streamOptionsConfig = { stream_options: options.stream_options };\n        }\n        else if (this.streamUsage && (this.streaming || extra?.streaming)) {\n            streamOptionsConfig = { stream_options: { include_usage: true } };\n        }\n        const params = {\n            model: this.model,\n            temperature: this.temperature,\n            top_p: this.topP,\n            frequency_penalty: this.frequencyPenalty,\n            presence_penalty: this.presencePenalty,\n            logprobs: this.logprobs,\n            top_logprobs: this.topLogprobs,\n            n: this.n,\n            logit_bias: this.logitBias,\n            stop: options?.stop ?? this.stopSequences,\n            user: this.user,\n            // if include_usage is set or streamUsage then stream must be set to true.\n            stream: this.streaming,\n            functions: options?.functions,\n            function_call: options?.function_call,\n            tools: options?.tools?.length\n                ? options.tools.map((tool) => this._convertChatOpenAIToolToCompletionsTool(tool, { strict }))\n                : undefined,\n            tool_choice: formatToOpenAIToolChoice(options?.tool_choice),\n            response_format: this._getResponseFormat(options?.response_format),\n            seed: options?.seed,\n            ...streamOptionsConfig,\n            parallel_tool_calls: options?.parallel_tool_calls,\n            ...(this.audio || options?.audio\n                ? { audio: this.audio || options?.audio }\n                : {}),\n            ...(this.modalities || options?.modalities\n                ? { modalities: this.modalities || options?.modalities }\n                : {}),\n            ...this.modelKwargs,\n        };\n        if (options?.prediction !== undefined) {\n            params.prediction = options.prediction;\n        }\n        if (this.service_tier !== undefined) {\n            params.service_tier = this.service_tier;\n        }\n        if (options?.service_tier !== undefined) {\n            params.service_tier = options.service_tier;\n        }\n        const reasoning = this._getReasoningParams(options);\n        if (reasoning !== undefined && reasoning.effort !== undefined) {\n            params.reasoning_effort = reasoning.effort;\n        }\n        if (isReasoningModel(params.model)) {\n            params.max_completion_tokens =\n                this.maxTokens === -1 ? undefined : this.maxTokens;\n        }\n        else {\n            params.max_tokens = this.maxTokens === -1 ? undefined : this.maxTokens;\n        }\n        return params;\n    }\n    async _generate(messages, options, runManager) {\n        const usageMetadata = {};\n        const params = this.invocationParams(options);\n        const messagesMapped = _convertMessagesToOpenAIParams(messages, this.model);\n        if (params.stream) {\n            const stream = this._streamResponseChunks(messages, options, runManager);\n            const finalChunks = {};\n            for await (const chunk of stream) {\n                chunk.message.response_metadata = {\n                    ...chunk.generationInfo,\n                    ...chunk.message.response_metadata,\n                };\n                const index = chunk.generationInfo?.completion ?? 0;\n                if (finalChunks[index] === undefined) {\n                    finalChunks[index] = chunk;\n                }\n                else {\n                    finalChunks[index] = finalChunks[index].concat(chunk);\n                }\n            }\n            const generations = Object.entries(finalChunks)\n                .sort(([aKey], [bKey]) => parseInt(aKey, 10) - parseInt(bKey, 10))\n                .map(([_, value]) => value);\n            const { functions, function_call } = this.invocationParams(options);\n            // OpenAI does not support token usage report under stream mode,\n            // fallback to estimation.\n            const promptTokenUsage = await this._getEstimatedTokenCountFromPrompt(messages, functions, function_call);\n            const completionTokenUsage = await this._getNumTokensFromGenerations(generations);\n            usageMetadata.input_tokens = promptTokenUsage;\n            usageMetadata.output_tokens = completionTokenUsage;\n            usageMetadata.total_tokens = promptTokenUsage + completionTokenUsage;\n            return {\n                generations,\n                llmOutput: {\n                    estimatedTokenUsage: {\n                        promptTokens: usageMetadata.input_tokens,\n                        completionTokens: usageMetadata.output_tokens,\n                        totalTokens: usageMetadata.total_tokens,\n                    },\n                },\n            };\n        }\n        else {\n            const data = await this.completionWithRetry({\n                ...params,\n                stream: false,\n                messages: messagesMapped,\n            }, {\n                signal: options?.signal,\n                ...options?.options,\n            });\n            const { completion_tokens: completionTokens, prompt_tokens: promptTokens, total_tokens: totalTokens, prompt_tokens_details: promptTokensDetails, completion_tokens_details: completionTokensDetails, } = data?.usage ?? {};\n            if (completionTokens) {\n                usageMetadata.output_tokens =\n                    (usageMetadata.output_tokens ?? 0) + completionTokens;\n            }\n            if (promptTokens) {\n                usageMetadata.input_tokens =\n                    (usageMetadata.input_tokens ?? 0) + promptTokens;\n            }\n            if (totalTokens) {\n                usageMetadata.total_tokens =\n                    (usageMetadata.total_tokens ?? 0) + totalTokens;\n            }\n            if (promptTokensDetails?.audio_tokens !== null ||\n                promptTokensDetails?.cached_tokens !== null) {\n                usageMetadata.input_token_details = {\n                    ...(promptTokensDetails?.audio_tokens !== null && {\n                        audio: promptTokensDetails?.audio_tokens,\n                    }),\n                    ...(promptTokensDetails?.cached_tokens !== null && {\n                        cache_read: promptTokensDetails?.cached_tokens,\n                    }),\n                };\n            }\n            if (completionTokensDetails?.audio_tokens !== null ||\n                completionTokensDetails?.reasoning_tokens !== null) {\n                usageMetadata.output_token_details = {\n                    ...(completionTokensDetails?.audio_tokens !== null && {\n                        audio: completionTokensDetails?.audio_tokens,\n                    }),\n                    ...(completionTokensDetails?.reasoning_tokens !== null && {\n                        reasoning: completionTokensDetails?.reasoning_tokens,\n                    }),\n                };\n            }\n            const generations = [];\n            for (const part of data?.choices ?? []) {\n                const text = part.message?.content ?? \"\";\n                const generation = {\n                    text,\n                    message: this._convertCompletionsMessageToBaseMessage(part.message ?? { role: \"assistant\" }, data),\n                };\n                generation.generationInfo = {\n                    ...(part.finish_reason ? { finish_reason: part.finish_reason } : {}),\n                    ...(part.logprobs ? { logprobs: part.logprobs } : {}),\n                };\n                if (isAIMessage(generation.message)) {\n                    generation.message.usage_metadata = usageMetadata;\n                }\n                // Fields are not serialized unless passed to the constructor\n                // Doing this ensures all fields on the message are serialized\n                generation.message = new AIMessage(Object.fromEntries(Object.entries(generation.message).filter(([key]) => !key.startsWith(\"lc_\"))));\n                generations.push(generation);\n            }\n            return {\n                generations,\n                llmOutput: {\n                    tokenUsage: {\n                        promptTokens: usageMetadata.input_tokens,\n                        completionTokens: usageMetadata.output_tokens,\n                        totalTokens: usageMetadata.total_tokens,\n                    },\n                },\n            };\n        }\n    }\n    async *_streamResponseChunks(messages, options, runManager) {\n        const messagesMapped = _convertMessagesToOpenAIParams(messages, this.model);\n        const params = {\n            ...this.invocationParams(options, {\n                streaming: true,\n            }),\n            messages: messagesMapped,\n            stream: true,\n        };\n        let defaultRole;\n        const streamIterable = await this.completionWithRetry(params, options);\n        let usage;\n        for await (const data of streamIterable) {\n            const choice = data?.choices?.[0];\n            if (data.usage) {\n                usage = data.usage;\n            }\n            if (!choice) {\n                continue;\n            }\n            const { delta } = choice;\n            if (!delta) {\n                continue;\n            }\n            const chunk = this._convertCompletionsDeltaToBaseMessageChunk(delta, data, defaultRole);\n            defaultRole = delta.role ?? defaultRole;\n            const newTokenIndices = {\n                prompt: options.promptIndex ?? 0,\n                completion: choice.index ?? 0,\n            };\n            if (typeof chunk.content !== \"string\") {\n                console.log(\"[WARNING]: Received non-string content from OpenAI. This is currently not supported.\");\n                continue;\n            }\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const generationInfo = { ...newTokenIndices };\n            if (choice.finish_reason != null) {\n                generationInfo.finish_reason = choice.finish_reason;\n                // Only include system fingerprint in the last chunk for now\n                // to avoid concatenation issues\n                generationInfo.system_fingerprint = data.system_fingerprint;\n                generationInfo.model_name = data.model;\n                generationInfo.service_tier = data.service_tier;\n            }\n            if (this.logprobs) {\n                generationInfo.logprobs = choice.logprobs;\n            }\n            const generationChunk = new ChatGenerationChunk({\n                message: chunk,\n                text: chunk.content,\n                generationInfo,\n            });\n            yield generationChunk;\n            await runManager?.handleLLMNewToken(generationChunk.text ?? \"\", newTokenIndices, undefined, undefined, undefined, { chunk: generationChunk });\n        }\n        if (usage) {\n            const inputTokenDetails = {\n                ...(usage.prompt_tokens_details?.audio_tokens !== null && {\n                    audio: usage.prompt_tokens_details?.audio_tokens,\n                }),\n                ...(usage.prompt_tokens_details?.cached_tokens !== null && {\n                    cache_read: usage.prompt_tokens_details?.cached_tokens,\n                }),\n            };\n            const outputTokenDetails = {\n                ...(usage.completion_tokens_details?.audio_tokens !== null && {\n                    audio: usage.completion_tokens_details?.audio_tokens,\n                }),\n                ...(usage.completion_tokens_details?.reasoning_tokens !== null && {\n                    reasoning: usage.completion_tokens_details?.reasoning_tokens,\n                }),\n            };\n            const generationChunk = new ChatGenerationChunk({\n                message: new AIMessageChunk({\n                    content: \"\",\n                    response_metadata: {\n                        usage: { ...usage },\n                    },\n                    usage_metadata: {\n                        input_tokens: usage.prompt_tokens,\n                        output_tokens: usage.completion_tokens,\n                        total_tokens: usage.total_tokens,\n                        ...(Object.keys(inputTokenDetails).length > 0 && {\n                            input_token_details: inputTokenDetails,\n                        }),\n                        ...(Object.keys(outputTokenDetails).length > 0 && {\n                            output_token_details: outputTokenDetails,\n                        }),\n                    },\n                }),\n                text: \"\",\n            });\n            yield generationChunk;\n        }\n        if (options.signal?.aborted) {\n            throw new Error(\"AbortError\");\n        }\n    }\n    async completionWithRetry(request, requestOptions) {\n        const clientOptions = this._getClientOptions(requestOptions);\n        const isParseableFormat = request.response_format && request.response_format.type === \"json_schema\";\n        return this.caller.call(async () => {\n            try {\n                if (isParseableFormat && !request.stream) {\n                    return await this.client.chat.completions.parse(request, clientOptions);\n                }\n                else {\n                    return await this.client.chat.completions.create(request, clientOptions);\n                }\n            }\n            catch (e) {\n                const error = wrapOpenAIClientError(e);\n                throw error;\n            }\n        });\n    }\n    /** @internal */\n    _convertCompletionsMessageToBaseMessage(message, rawResponse) {\n        const rawToolCalls = message.tool_calls;\n        switch (message.role) {\n            case \"assistant\": {\n                const toolCalls = [];\n                const invalidToolCalls = [];\n                for (const rawToolCall of rawToolCalls ?? []) {\n                    try {\n                        toolCalls.push(parseToolCall(rawToolCall, { returnId: true }));\n                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    }\n                    catch (e) {\n                        invalidToolCalls.push(makeInvalidToolCall(rawToolCall, e.message));\n                    }\n                }\n                const additional_kwargs = {\n                    function_call: message.function_call,\n                    tool_calls: rawToolCalls,\n                };\n                if (this.__includeRawResponse !== undefined) {\n                    additional_kwargs.__raw_response = rawResponse;\n                }\n                const response_metadata = {\n                    model_name: rawResponse.model,\n                    ...(rawResponse.system_fingerprint\n                        ? {\n                            usage: { ...rawResponse.usage },\n                            system_fingerprint: rawResponse.system_fingerprint,\n                        }\n                        : {}),\n                };\n                if (message.audio) {\n                    additional_kwargs.audio = message.audio;\n                }\n                return new AIMessage({\n                    content: message.content || \"\",\n                    tool_calls: toolCalls,\n                    invalid_tool_calls: invalidToolCalls,\n                    additional_kwargs,\n                    response_metadata,\n                    id: rawResponse.id,\n                });\n            }\n            default:\n                return new ChatMessage(message.content || \"\", message.role ?? \"unknown\");\n        }\n    }\n    /** @internal */\n    _convertCompletionsDeltaToBaseMessageChunk(\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    delta, rawResponse, defaultRole) {\n        const role = delta.role ?? defaultRole;\n        const content = delta.content ?? \"\";\n        let additional_kwargs;\n        if (delta.function_call) {\n            additional_kwargs = {\n                function_call: delta.function_call,\n            };\n        }\n        else if (delta.tool_calls) {\n            additional_kwargs = {\n                tool_calls: delta.tool_calls,\n            };\n        }\n        else {\n            additional_kwargs = {};\n        }\n        if (this.__includeRawResponse) {\n            additional_kwargs.__raw_response = rawResponse;\n        }\n        if (delta.audio) {\n            additional_kwargs.audio = {\n                ...delta.audio,\n                index: rawResponse.choices[0].index,\n            };\n        }\n        const response_metadata = { usage: { ...rawResponse.usage } };\n        if (role === \"user\") {\n            return new HumanMessageChunk({ content, response_metadata });\n        }\n        else if (role === \"assistant\") {\n            const toolCallChunks = [];\n            if (Array.isArray(delta.tool_calls)) {\n                for (const rawToolCall of delta.tool_calls) {\n                    toolCallChunks.push({\n                        name: rawToolCall.function?.name,\n                        args: rawToolCall.function?.arguments,\n                        id: rawToolCall.id,\n                        index: rawToolCall.index,\n                        type: \"tool_call_chunk\",\n                    });\n                }\n            }\n            return new AIMessageChunk({\n                content,\n                tool_call_chunks: toolCallChunks,\n                additional_kwargs,\n                id: rawResponse.id,\n                response_metadata,\n            });\n        }\n        else if (role === \"system\") {\n            return new SystemMessageChunk({ content, response_metadata });\n        }\n        else if (role === \"developer\") {\n            return new SystemMessageChunk({\n                content,\n                response_metadata,\n                additional_kwargs: {\n                    __openai_role__: \"developer\",\n                },\n            });\n        }\n        else if (role === \"function\") {\n            return new FunctionMessageChunk({\n                content,\n                additional_kwargs,\n                name: delta.name,\n                response_metadata,\n            });\n        }\n        else if (role === \"tool\") {\n            return new ToolMessageChunk({\n                content,\n                additional_kwargs,\n                tool_call_id: delta.tool_call_id,\n                response_metadata,\n            });\n        }\n        else {\n            return new ChatMessageChunk({ content, role, response_metadata });\n        }\n    }\n}\n/**\n * OpenAI chat model integration.\n *\n * To use with Azure, import the `AzureChatOpenAI` class.\n *\n * Setup:\n * Install `@langchain/openai` and set an environment variable named `OPENAI_API_KEY`.\n *\n * ```bash\n * npm install @langchain/openai\n * export OPENAI_API_KEY=\"your-api-key\"\n * ```\n *\n * ## [Constructor args](https://api.js.langchain.com/classes/langchain_openai.ChatOpenAI.html#constructor)\n *\n * ## [Runtime args](https://api.js.langchain.com/interfaces/langchain_openai.ChatOpenAICallOptions.html)\n *\n * Runtime args can be passed as the second argument to any of the base runnable methods `.invoke`. `.stream`, `.batch`, etc.\n * They can also be passed via `.withConfig`, or the second arg in `.bindTools`, like shown in the examples below:\n *\n * ```typescript\n * // When calling `.withConfig`, call options should be passed via the first argument\n * const llmWithArgsBound = llm.withConfig({\n *   stop: [\"\\n\"],\n *   tools: [...],\n * });\n *\n * // When calling `.bindTools`, call options should be passed via the second argument\n * const llmWithTools = llm.bindTools(\n *   [...],\n *   {\n *     tool_choice: \"auto\",\n *   }\n * );\n * ```\n *\n * ## Examples\n *\n * <details open>\n * <summary><strong>Instantiate</strong></summary>\n *\n * ```typescript\n * import { ChatOpenAI } from '@langchain/openai';\n *\n * const llm = new ChatOpenAI({\n *   model: \"gpt-4o-mini\",\n *   temperature: 0,\n *   maxTokens: undefined,\n *   timeout: undefined,\n *   maxRetries: 2,\n *   // apiKey: \"...\",\n *   // baseUrl: \"...\",\n *   // organization: \"...\",\n *   // other params...\n * });\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Invoking</strong></summary>\n *\n * ```typescript\n * const input = `Translate \"I love programming\" into French.`;\n *\n * // Models also accept a list of chat messages or a formatted prompt\n * const result = await llm.invoke(input);\n * console.log(result);\n * ```\n *\n * ```txt\n * AIMessage {\n *   \"id\": \"chatcmpl-9u4Mpu44CbPjwYFkTbeoZgvzB00Tz\",\n *   \"content\": \"J'adore la programmation.\",\n *   \"response_metadata\": {\n *     \"tokenUsage\": {\n *       \"completionTokens\": 5,\n *       \"promptTokens\": 28,\n *       \"totalTokens\": 33\n *     },\n *     \"finish_reason\": \"stop\",\n *     \"system_fingerprint\": \"fp_3aa7262c27\"\n *   },\n *   \"usage_metadata\": {\n *     \"input_tokens\": 28,\n *     \"output_tokens\": 5,\n *     \"total_tokens\": 33\n *   }\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Streaming Chunks</strong></summary>\n *\n * ```typescript\n * for await (const chunk of await llm.stream(input)) {\n *   console.log(chunk);\n * }\n * ```\n *\n * ```txt\n * AIMessageChunk {\n *   \"id\": \"chatcmpl-9u4NWB7yUeHCKdLr6jP3HpaOYHTqs\",\n *   \"content\": \"\"\n * }\n * AIMessageChunk {\n *   \"content\": \"J\"\n * }\n * AIMessageChunk {\n *   \"content\": \"'adore\"\n * }\n * AIMessageChunk {\n *   \"content\": \" la\"\n * }\n * AIMessageChunk {\n *   \"content\": \" programmation\",,\n * }\n * AIMessageChunk {\n *   \"content\": \".\",,\n * }\n * AIMessageChunk {\n *   \"content\": \"\",\n *   \"response_metadata\": {\n *     \"finish_reason\": \"stop\",\n *     \"system_fingerprint\": \"fp_c9aa9c0491\"\n *   },\n * }\n * AIMessageChunk {\n *   \"content\": \"\",\n *   \"usage_metadata\": {\n *     \"input_tokens\": 28,\n *     \"output_tokens\": 5,\n *     \"total_tokens\": 33\n *   }\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Aggregate Streamed Chunks</strong></summary>\n *\n * ```typescript\n * import { AIMessageChunk } from '@langchain/core/messages';\n * import { concat } from '@langchain/core/utils/stream';\n *\n * const stream = await llm.stream(input);\n * let full: AIMessageChunk | undefined;\n * for await (const chunk of stream) {\n *   full = !full ? chunk : concat(full, chunk);\n * }\n * console.log(full);\n * ```\n *\n * ```txt\n * AIMessageChunk {\n *   \"id\": \"chatcmpl-9u4PnX6Fy7OmK46DASy0bH6cxn5Xu\",\n *   \"content\": \"J'adore la programmation.\",\n *   \"response_metadata\": {\n *     \"prompt\": 0,\n *     \"completion\": 0,\n *     \"finish_reason\": \"stop\",\n *   },\n *   \"usage_metadata\": {\n *     \"input_tokens\": 28,\n *     \"output_tokens\": 5,\n *     \"total_tokens\": 33\n *   }\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Bind tools</strong></summary>\n *\n * ```typescript\n * import { z } from 'zod';\n *\n * const GetWeather = {\n *   name: \"GetWeather\",\n *   description: \"Get the current weather in a given location\",\n *   schema: z.object({\n *     location: z.string().describe(\"The city and state, e.g. San Francisco, CA\")\n *   }),\n * }\n *\n * const GetPopulation = {\n *   name: \"GetPopulation\",\n *   description: \"Get the current population in a given location\",\n *   schema: z.object({\n *     location: z.string().describe(\"The city and state, e.g. San Francisco, CA\")\n *   }),\n * }\n *\n * const llmWithTools = llm.bindTools(\n *   [GetWeather, GetPopulation],\n *   {\n *     // strict: true  // enforce tool args schema is respected\n *   }\n * );\n * const aiMsg = await llmWithTools.invoke(\n *   \"Which city is hotter today and which is bigger: LA or NY?\"\n * );\n * console.log(aiMsg.tool_calls);\n * ```\n *\n * ```txt\n * [\n *   {\n *     name: 'GetWeather',\n *     args: { location: 'Los Angeles, CA' },\n *     type: 'tool_call',\n *     id: 'call_uPU4FiFzoKAtMxfmPnfQL6UK'\n *   },\n *   {\n *     name: 'GetWeather',\n *     args: { location: 'New York, NY' },\n *     type: 'tool_call',\n *     id: 'call_UNkEwuQsHrGYqgDQuH9nPAtX'\n *   },\n *   {\n *     name: 'GetPopulation',\n *     args: { location: 'Los Angeles, CA' },\n *     type: 'tool_call',\n *     id: 'call_kL3OXxaq9OjIKqRTpvjaCH14'\n *   },\n *   {\n *     name: 'GetPopulation',\n *     args: { location: 'New York, NY' },\n *     type: 'tool_call',\n *     id: 'call_s9KQB1UWj45LLGaEnjz0179q'\n *   }\n * ]\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Structured Output</strong></summary>\n *\n * ```typescript\n * import { z } from 'zod';\n *\n * const Joke = z.object({\n *   setup: z.string().describe(\"The setup of the joke\"),\n *   punchline: z.string().describe(\"The punchline to the joke\"),\n *   rating: z.number().nullable().describe(\"How funny the joke is, from 1 to 10\")\n * }).describe('Joke to tell user.');\n *\n * const structuredLlm = llm.withStructuredOutput(Joke, {\n *   name: \"Joke\",\n *   strict: true, // Optionally enable OpenAI structured outputs\n * });\n * const jokeResult = await structuredLlm.invoke(\"Tell me a joke about cats\");\n * console.log(jokeResult);\n * ```\n *\n * ```txt\n * {\n *   setup: 'Why was the cat sitting on the computer?',\n *   punchline: 'Because it wanted to keep an eye on the mouse!',\n *   rating: 7\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>JSON Object Response Format</strong></summary>\n *\n * ```typescript\n * const jsonLlm = llm.withConfig({ response_format: { type: \"json_object\" } });\n * const jsonLlmAiMsg = await jsonLlm.invoke(\n *   \"Return a JSON object with key 'randomInts' and a value of 10 random ints in [0-99]\"\n * );\n * console.log(jsonLlmAiMsg.content);\n * ```\n *\n * ```txt\n * {\n *   \"randomInts\": [23, 87, 45, 12, 78, 34, 56, 90, 11, 67]\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Multimodal</strong></summary>\n *\n * ```typescript\n * import { HumanMessage } from '@langchain/core/messages';\n *\n * const imageUrl = \"https://example.com/image.jpg\";\n * const imageData = await fetch(imageUrl).then(res => res.arrayBuffer());\n * const base64Image = Buffer.from(imageData).toString('base64');\n *\n * const message = new HumanMessage({\n *   content: [\n *     { type: \"text\", text: \"describe the weather in this image\" },\n *     {\n *       type: \"image_url\",\n *       image_url: { url: `data:image/jpeg;base64,${base64Image}` },\n *     },\n *   ]\n * });\n *\n * const imageDescriptionAiMsg = await llm.invoke([message]);\n * console.log(imageDescriptionAiMsg.content);\n * ```\n *\n * ```txt\n * The weather in the image appears to be clear and sunny. The sky is mostly blue with a few scattered white clouds, indicating fair weather. The bright sunlight is casting shadows on the green, grassy hill, suggesting it is a pleasant day with good visibility. There are no signs of rain or stormy conditions.\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Usage Metadata</strong></summary>\n *\n * ```typescript\n * const aiMsgForMetadata = await llm.invoke(input);\n * console.log(aiMsgForMetadata.usage_metadata);\n * ```\n *\n * ```txt\n * { input_tokens: 28, output_tokens: 5, total_tokens: 33 }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Logprobs</strong></summary>\n *\n * ```typescript\n * const logprobsLlm = new ChatOpenAI({ model: \"gpt-4o-mini\", logprobs: true });\n * const aiMsgForLogprobs = await logprobsLlm.invoke(input);\n * console.log(aiMsgForLogprobs.response_metadata.logprobs);\n * ```\n *\n * ```txt\n * {\n *   content: [\n *     {\n *       token: 'J',\n *       logprob: -0.000050616763,\n *       bytes: [Array],\n *       top_logprobs: []\n *     },\n *     {\n *       token: \"'\",\n *       logprob: -0.01868736,\n *       bytes: [Array],\n *       top_logprobs: []\n *     },\n *     {\n *       token: 'ad',\n *       logprob: -0.0000030545007,\n *       bytes: [Array],\n *       top_logprobs: []\n *     },\n *     { token: 'ore', logprob: 0, bytes: [Array], top_logprobs: [] },\n *     {\n *       token: ' la',\n *       logprob: -0.515404,\n *       bytes: [Array],\n *       top_logprobs: []\n *     },\n *     {\n *       token: ' programm',\n *       logprob: -0.0000118755715,\n *       bytes: [Array],\n *       top_logprobs: []\n *     },\n *     { token: 'ation', logprob: 0, bytes: [Array], top_logprobs: [] },\n *     {\n *       token: '.',\n *       logprob: -0.0000037697225,\n *       bytes: [Array],\n *       top_logprobs: []\n *     }\n *   ],\n *   refusal: null\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Response Metadata</strong></summary>\n *\n * ```typescript\n * const aiMsgForResponseMetadata = await llm.invoke(input);\n * console.log(aiMsgForResponseMetadata.response_metadata);\n * ```\n *\n * ```txt\n * {\n *   tokenUsage: { completionTokens: 5, promptTokens: 28, totalTokens: 33 },\n *   finish_reason: 'stop',\n *   system_fingerprint: 'fp_3aa7262c27'\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>JSON Schema Structured Output</strong></summary>\n *\n * ```typescript\n * const llmForJsonSchema = new ChatOpenAI({\n *   model: \"gpt-4o-2024-08-06\",\n * }).withStructuredOutput(\n *   z.object({\n *     command: z.string().describe(\"The command to execute\"),\n *     expectedOutput: z.string().describe(\"The expected output of the command\"),\n *     options: z\n *       .array(z.string())\n *       .describe(\"The options you can pass to the command\"),\n *   }),\n *   {\n *     method: \"jsonSchema\",\n *     strict: true, // Optional when using the `jsonSchema` method\n *   }\n * );\n *\n * const jsonSchemaRes = await llmForJsonSchema.invoke(\n *   \"What is the command to list files in a directory?\"\n * );\n * console.log(jsonSchemaRes);\n * ```\n *\n * ```txt\n * {\n *   command: 'ls',\n *   expectedOutput: 'A list of files and subdirectories within the specified directory.',\n *   options: [\n *     '-a: include directory entries whose names begin with a dot (.).',\n *     '-l: use a long listing format.',\n *     '-h: with -l, print sizes in human readable format (e.g., 1K, 234M, 2G).',\n *     '-t: sort by time, newest first.',\n *     '-r: reverse order while sorting.',\n *     '-S: sort by file size, largest first.',\n *     '-R: list subdirectories recursively.'\n *   ]\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Audio Outputs</strong></summary>\n *\n * ```typescript\n * import { ChatOpenAI } from \"@langchain/openai\";\n *\n * const modelWithAudioOutput = new ChatOpenAI({\n *   model: \"gpt-4o-audio-preview\",\n *   // You may also pass these fields to `.withConfig` as a call argument.\n *   modalities: [\"text\", \"audio\"], // Specifies that the model should output audio.\n *   audio: {\n *     voice: \"alloy\",\n *     format: \"wav\",\n *   },\n * });\n *\n * const audioOutputResult = await modelWithAudioOutput.invoke(\"Tell me a joke about cats.\");\n * const castMessageContent = audioOutputResult.content[0] as Record<string, any>;\n *\n * console.log({\n *   ...castMessageContent,\n *   data: castMessageContent.data.slice(0, 100) // Sliced for brevity\n * })\n * ```\n *\n * ```txt\n * {\n *   id: 'audio_67117718c6008190a3afad3e3054b9b6',\n *   data: 'UklGRqYwBgBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAATElTVBoAAABJTkZPSVNGVA4AAABMYXZmNTguMjkuMTAwAGRhdGFg',\n *   expires_at: 1729201448,\n *   transcript: 'Sure! Why did the cat sit on the computer? Because it wanted to keep an eye on the mouse!'\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Audio Outputs</strong></summary>\n *\n * ```typescript\n * import { ChatOpenAI } from \"@langchain/openai\";\n *\n * const modelWithAudioOutput = new ChatOpenAI({\n *   model: \"gpt-4o-audio-preview\",\n *   // You may also pass these fields to `.withConfig` as a call argument.\n *   modalities: [\"text\", \"audio\"], // Specifies that the model should output audio.\n *   audio: {\n *     voice: \"alloy\",\n *     format: \"wav\",\n *   },\n * });\n *\n * const audioOutputResult = await modelWithAudioOutput.invoke(\"Tell me a joke about cats.\");\n * const castAudioContent = audioOutputResult.additional_kwargs.audio as Record<string, any>;\n *\n * console.log({\n *   ...castAudioContent,\n *   data: castAudioContent.data.slice(0, 100) // Sliced for brevity\n * })\n * ```\n *\n * ```txt\n * {\n *   id: 'audio_67117718c6008190a3afad3e3054b9b6',\n *   data: 'UklGRqYwBgBXQVZFZm10IBAAAAABAAEAwF0AAIC7AAACABAATElTVBoAAABJTkZPSVNGVA4AAABMYXZmNTguMjkuMTAwAGRhdGFg',\n *   expires_at: 1729201448,\n *   transcript: 'Sure! Why did the cat sit on the computer? Because it wanted to keep an eye on the mouse!'\n * }\n * ```\n * </details>\n *\n * <br />\n */\nexport class ChatOpenAI extends BaseChatOpenAI {\n    get lc_serializable_keys() {\n        return [...super.lc_serializable_keys, \"useResponsesApi\"];\n    }\n    constructor(fields) {\n        super(fields ?? {});\n        /**\n         * Whether to use the responses API for all requests. If `false` the responses API will be used\n         * only when required in order to fulfill the request.\n         */\n        Object.defineProperty(this, \"useResponsesApi\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        Object.defineProperty(this, \"responses\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"completions\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.useResponsesApi = fields?.useResponsesApi ?? false;\n        this.responses = new ChatOpenAIResponses(fields);\n        this.completions = new ChatOpenAICompletions(fields);\n    }\n    _useResponsesApi(options) {\n        const usesBuiltInTools = options?.tools?.some(isBuiltInTool);\n        const hasResponsesOnlyKwargs = options?.previous_response_id != null ||\n            options?.text != null ||\n            options?.truncation != null ||\n            options?.include != null ||\n            options?.reasoning?.summary != null ||\n            this.reasoning?.summary != null;\n        return this.useResponsesApi || usesBuiltInTools || hasResponsesOnlyKwargs;\n    }\n    /** @ignore */\n    async _generate(messages, options, runManager) {\n        if (this._useResponsesApi(options)) {\n            return this.responses._generate(messages, options);\n        }\n        return this.completions._generate(messages, options, runManager);\n    }\n    async *_streamResponseChunks(messages, options, runManager) {\n        if (this._useResponsesApi(options)) {\n            yield* this.responses._streamResponseChunks(messages, options);\n            return;\n        }\n        yield* this.completions._streamResponseChunks(messages, options, runManager);\n    }\n    withConfig(config) {\n        // FIXME: assigning additional config options to the inner chat classes this way\n        // is awkward, but it's the only way to preserve the original object identity\n        // and still thread config options, which is important since this class is a \"proxy\"\n        // for the inner chat classes. This will be fixed in a later version of langchain\n        // when the core runnable interface is improved (0.4) to support this out of the box.\n        const bindChatOpenAIConfig = (cls, config) => {\n            const oldGenerate = cls._generate;\n            const oldStreamResponseChunks = cls._streamResponseChunks;\n            return Object.assign(cls, {\n                _generate(messages, options, runManager) {\n                    return oldGenerate.call(cls, messages, { ...options, ...config }, runManager);\n                },\n                _streamResponseChunks(messages, options, runManager) {\n                    return oldStreamResponseChunks.call(cls, messages, { ...options, ...config }, runManager);\n                },\n            });\n        };\n        this.responses = bindChatOpenAIConfig(this.responses, config);\n        this.completions = bindChatOpenAIConfig(this.completions, config);\n        // Proxy chat class is also bound for `_useResponsesApi`,\n        return bindChatOpenAIConfig(this, config);\n    }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;;;;;;;;AACA,MAAM,6BAA6B;AACnC,SAAS,cAAc,IAAI;IACvB,OAAO,UAAU,QAAQ,KAAK,IAAI,KAAK;AAC3C;AACA,SAAS,oBAAoB,WAAW;IACpC,OAAQ,eAAe,QACnB,OAAO,gBAAgB,YACvB,UAAU,eACV,YAAY,IAAI,KAAK;AAC7B;AACA,SAAS,iBAAiB,KAAK;IAC3B,OAAO,SAAS,OAAO,IAAI,CAAC;AAChC;AACA,SAAS,+BAA+B,CAAC;IAGrC,OAAQ,MAAM,aACV,8DAA8D;IAC9D,OAAO,EAAE,MAAM,KACX;AACZ;AACA,SAAS,gCAAgC,OAAO;IAC5C,IAAI,QAAQ,IAAI,KAAK,YACjB,QAAQ,IAAI,KAAK,eACjB,QAAQ,IAAI,KAAK,eACjB,QAAQ,IAAI,KAAK,UACjB,QAAQ,IAAI,KAAK,cACjB,QAAQ,IAAI,KAAK,QAAQ;QACzB,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE,QAAQ,IAAI,EAAE;IACxD;IACA,OAAO,QAAQ,IAAI;AACvB;AACO,SAAS,oBAAoB,OAAO;IACvC,MAAM,OAAO,QAAQ,QAAQ;IAC7B,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YAAW;gBACZ,IAAI,CAAC,iKAAA,CAAA,cAAW,CAAC,UAAU,CAAC,UACxB,MAAM,IAAI,MAAM;gBACpB,OAAO,gCAAgC;YAC3C;QACA;YACI,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,MAAM;IACvD;AACJ;AACA,MAAM,sCAAsC;IACxC,cAAc;IACd,uBAAsB,KAAK;QACvB,OAAO;YAAE,MAAM;YAAQ,MAAM,MAAM,IAAI;QAAC;IAC5C;IACA,wBAAuB,KAAK;QACxB,IAAI,MAAM,WAAW,KAAK,OAAO;YAC7B,OAAO;gBACH,MAAM;gBACN,WAAW;oBACP,KAAK,MAAM,GAAG;oBACd,GAAI,MAAM,QAAQ,EAAE,SACd;wBAAE,QAAQ,MAAM,QAAQ,CAAC,MAAM;oBAAC,IAChC,CAAC,CAAC;gBACZ;YACJ;QACJ;QACA,IAAI,MAAM,WAAW,KAAK,UAAU;YAChC,MAAM,MAAM,CAAC,KAAK,EAAE,MAAM,SAAS,IAAI,GAAG,QAAQ,EAAE,MAAM,IAAI,EAAE;YAChE,OAAO;gBACH,MAAM;gBACN,WAAW;oBACP;oBACA,GAAI,MAAM,QAAQ,EAAE,SACd;wBAAE,QAAQ,MAAM,QAAQ,CAAC,MAAM;oBAAC,IAChC,CAAC,CAAC;gBACZ;YACJ;QACJ;QACA,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,MAAM,WAAW,CAAC,iCAAiC,CAAC;IACjH;IACA,wBAAuB,KAAK;QACxB,IAAI,MAAM,WAAW,KAAK,OAAO;YAC7B,MAAM,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE,SAAS,MAAM,GAAG;YAAC;YACrD,IAAI,CAAC,MAAM;gBACP,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,MAAM,WAAW,CAAC,+CAA+C,CAAC;YAC3H;YACA,MAAM,cAAc,KAAK,SAAS,IAAI,MAAM,SAAS,IAAI;YACzD,IAAI;YACJ,IAAI;gBACA,WAAW,CAAA,GAAA,2KAAA,CAAA,gBAAa,AAAD,EAAE;YAC7B,EACA,OAAM;gBACF,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,MAAM,WAAW,CAAC,8CAA8C,CAAC;YACtH;YACA,IAAI,SAAS,IAAI,KAAK,WACjB,SAAS,OAAO,KAAK,SAAS,SAAS,OAAO,KAAK,OAAQ;gBAC5D,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,MAAM,WAAW,CAAC,8CAA8C,CAAC;YACtH;YACA,OAAO;gBACH,MAAM;gBACN,aAAa;oBACT,QAAQ,SAAS,OAAO;oBACxB,MAAM,KAAK,IAAI;gBACnB;YACJ;QACJ;QACA,IAAI,MAAM,WAAW,KAAK,UAAU;YAChC,IAAI;YACJ,IAAI;gBACA,WAAW,CAAA,GAAA,2KAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,SAAS,IAAI;YAChD,EACA,OAAM;gBACF,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,MAAM,WAAW,CAAC,8CAA8C,CAAC;YACtH;YACA,IAAI,SAAS,IAAI,KAAK,WACjB,SAAS,OAAO,KAAK,SAAS,SAAS,OAAO,KAAK,OAAQ;gBAC5D,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,MAAM,WAAW,CAAC,8CAA8C,CAAC;YACtH;YACA,OAAO;gBACH,MAAM;gBACN,aAAa;oBACT,QAAQ,SAAS,OAAO;oBACxB,MAAM,MAAM,IAAI;gBACpB;YACJ;QACJ;QACA,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,MAAM,WAAW,CAAC,iCAAiC,CAAC;IACjH;IACA,uBAAsB,KAAK;QACvB,IAAI,MAAM,WAAW,KAAK,OAAO;YAC7B,MAAM,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE;gBAAE,SAAS,MAAM,GAAG;YAAC;YACrD,IAAI,CAAC,MAAM;gBACP,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,MAAM,WAAW,CAAC,+CAA+C,CAAC;YAC1H;YACA,OAAO;gBACH,MAAM;gBACN,MAAM;oBACF,WAAW,MAAM,GAAG;oBACpB,GAAI,MAAM,QAAQ,EAAE,YAAY,MAAM,QAAQ,EAAE,OAC1C;wBACE,UAAW,MAAM,QAAQ,EAAE,YACvB,MAAM,QAAQ,EAAE;oBACxB,IACE,CAAC,CAAC;gBACZ;YACJ;QACJ;QACA,IAAI,MAAM,WAAW,KAAK,UAAU;YAChC,OAAO;gBACH,MAAM;gBACN,MAAM;oBACF,WAAW,CAAC,KAAK,EAAE,MAAM,SAAS,IAAI,GAAG,QAAQ,EAAE,MAAM,IAAI,EAAE;oBAC/D,GAAI,MAAM,QAAQ,EAAE,YAChB,MAAM,QAAQ,EAAE,QAChB,MAAM,QAAQ,EAAE,QACd;wBACE,UAAW,MAAM,QAAQ,EAAE,YACvB,MAAM,QAAQ,EAAE,QAChB,MAAM,QAAQ,EAAE;oBACxB,IACE,CAAC,CAAC;gBACZ;YACJ;QACJ;QACA,IAAI,MAAM,WAAW,KAAK,MAAM;YAC5B,OAAO;gBACH,MAAM;gBACN,MAAM;oBACF,SAAS,MAAM,EAAE;gBACrB;YACJ;QACJ;QACA,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,MAAM,WAAW,CAAC,iCAAiC,CAAC;IAChH;AACJ;AAGO,SAAS,+BAA+B,QAAQ,EAAE,KAAK;IAC1D,iEAAiE;IACjE,OAAO,SAAS,OAAO,CAAC,CAAC;QACrB,IAAI,OAAO,oBAAoB;QAC/B,IAAI,SAAS,YAAY,iBAAiB,QAAQ;YAC9C,OAAO;QACX;QACA,MAAM,UAAU,OAAO,QAAQ,OAAO,KAAK,WACrC,QAAQ,OAAO,GACf,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI;gBACvB,OAAO,CAAA,GAAA,2KAAA,CAAA,gCAA6B,AAAD,EAAE,GAAG;YAC5C;YACA,OAAO;QACX;QACJ,8DAA8D;QAC9D,MAAM,kBAAkB;YACpB;YACA;QACJ;QACA,IAAI,QAAQ,IAAI,IAAI,MAAM;YACtB,gBAAgB,IAAI,GAAG,QAAQ,IAAI;QACvC;QACA,IAAI,QAAQ,iBAAiB,CAAC,aAAa,IAAI,MAAM;YACjD,gBAAgB,aAAa,GAAG,QAAQ,iBAAiB,CAAC,aAAa;YACvE,gBAAgB,OAAO,GAAG;QAC9B;QACA,IAAI,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE,YAAY,CAAC,CAAC,QAAQ,UAAU,EAAE,QAAQ;YACtD,gBAAgB,UAAU,GAAG,QAAQ,UAAU,CAAC,GAAG,CAAC,4MAAA,CAAA,mCAAgC;YACpF,gBAAgB,OAAO,GAAG;QAC9B,OACK;YACD,IAAI,QAAQ,iBAAiB,CAAC,UAAU,IAAI,MAAM;gBAC9C,gBAAgB,UAAU,GAAG,QAAQ,iBAAiB,CAAC,UAAU;YACrE;YACA,IAAI,QAAQ,YAAY,IAAI,MAAM;gBAC9B,gBAAgB,YAAY,GAAG,QAAQ,YAAY;YACvD;QACJ;QACA,IAAI,QAAQ,iBAAiB,CAAC,KAAK,IAC/B,OAAO,QAAQ,iBAAiB,CAAC,KAAK,KAAK,YAC3C,QAAQ,QAAQ,iBAAiB,CAAC,KAAK,EAAE;YACzC,MAAM,eAAe;gBACjB,MAAM;gBACN,OAAO;oBACH,IAAI,QAAQ,iBAAiB,CAAC,KAAK,CAAC,EAAE;gBAC1C;YACJ;YACA,OAAO;gBACH;gBACA;aACH;QACL;QACA,OAAO;IACX;AACJ;AACA,cAAc,GACd,MAAM,uBAAuB,+KAAA,CAAA,gBAAa;IACtC,WAAW;QACP,OAAO;IACX;IACA,OAAO,UAAU;QACb,OAAO;IACX;IACA,IAAI,WAAW;QACX,OAAO;eACA,KAAK,CAAC;YACT;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH;IACL;IACA,IAAI,aAAa;QACb,OAAO;YACH,QAAQ;YACR,cAAc;QAClB;IACJ;IACA,IAAI,aAAa;QACb,OAAO;YACH,QAAQ;YACR,WAAW;QACf;IACJ;IACA,IAAI,uBAAuB;QACvB,OAAO;YACH;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH;IACL;IACA,YAAY,OAAO,EAAE;QACjB,MAAM,SAAS,IAAI,CAAC,gBAAgB,CAAC;QACrC,OAAO;YACH,aAAa;YACb,eAAe,IAAI,CAAC,KAAK;YACzB,eAAe;YACf,gBAAgB,OAAO,WAAW,IAAI;YACtC,eAAe,OAAO,UAAU,IAAI;YACpC,SAAS,QAAQ,IAAI;QACzB;IACJ;IACA,YAAY,GACZ,qBAAqB;QACjB,OAAO;YACH,YAAY,IAAI,CAAC,KAAK;YACtB,GAAG,IAAI,CAAC,gBAAgB,EAAE;YAC1B,GAAG,IAAI,CAAC,YAAY;QACxB;IACJ;IACA;;KAEC,GACD,oBAAoB;QAChB,OAAO,IAAI,CAAC,kBAAkB;IAClC;IACA,YAAY,MAAM,CAAE;QAChB,KAAK,CAAC,UAAU,CAAC;QACjB,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,oBAAoB;YAC5C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,mBAAmB;YAC3C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;YAC7B,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,iBAAiB;YACzC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;YACnC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YACpC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,wBAAwB;YAChD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA;;;SAGC,GACD,OAAO,cAAc,CAAC,IAAI,EAAE,6BAA6B;YACrD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA;;;;;;;;;;SAUC,GACD,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA;;;SAGC,GACD,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,mBAAmB;YAC3C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,IAAI,CAAC,MAAM,GACP,QAAQ,UACJ,QAAQ,eAAe,UACvB,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,YAAY,GACb,QAAQ,eAAe,gBACnB,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,KAAK,GAAG,QAAQ,SAAS,QAAQ,aAAa,IAAI,CAAC,KAAK;QAC7D,IAAI,CAAC,WAAW,GAAG,QAAQ,eAAe,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,QAAQ;QACvB,IAAI,CAAC,WAAW,GAAG,QAAQ,eAAe,IAAI,CAAC,WAAW;QAC1D,IAAI,CAAC,IAAI,GAAG,QAAQ,QAAQ,IAAI,CAAC,IAAI;QACrC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,oBAAoB,IAAI,CAAC,gBAAgB;QACzE,IAAI,CAAC,eAAe,GAAG,QAAQ,mBAAmB,IAAI,CAAC,eAAe;QACtE,IAAI,CAAC,QAAQ,GAAG,QAAQ;QACxB,IAAI,CAAC,WAAW,GAAG,QAAQ;QAC3B,IAAI,CAAC,CAAC,GAAG,QAAQ,KAAK,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ;QACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,iBAAiB,QAAQ;QAC7C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,IAAI,GAAG,QAAQ;QACpB,IAAI,CAAC,oBAAoB,GAAG,QAAQ;QACpC,IAAI,CAAC,KAAK,GAAG,QAAQ;QACrB,IAAI,CAAC,UAAU,GAAG,QAAQ;QAC1B,IAAI,CAAC,SAAS,GAAG,QAAQ;QACzB,IAAI,CAAC,SAAS,GAAG,QAAQ,uBAAuB,QAAQ;QACxD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,oBAAoB,IAAI,CAAC,gBAAgB;QACzE,IAAI,CAAC,SAAS,GAAG,QAAQ,aAAa;QACtC,IAAI,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,SAAS,GAAG;QACrB,IAAI,CAAC,WAAW,GAAG,QAAQ,eAAe,IAAI,CAAC,WAAW;QAC1D,IAAI,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,WAAW,GAAG;QACvB,IAAI,CAAC,YAAY,GAAG;YAChB,QAAQ,IAAI,CAAC,MAAM;YACnB,cAAc,IAAI,CAAC,YAAY;YAC/B,yBAAyB;YACzB,GAAG,QAAQ,aAAa;QAC5B;QACA,oEAAoE;QACpE,qDAAqD;QACrD,IAAI,QAAQ,8BAA8B,WAAW;YACjD,IAAI,CAAC,yBAAyB,GAAG,OAAO,yBAAyB;QACrE;QACA,IAAI,QAAQ,iBAAiB,WAAW;YACpC,IAAI,CAAC,YAAY,GAAG,OAAO,YAAY;QAC3C;QACA,IAAI,CAAC,UAAU,GAAG,QAAQ,cAAc;IAC5C;IACA;;;KAGC,GACD,oBAAoB,OAAO,EAAE;QACzB,IAAI,CAAC,iBAAiB,IAAI,CAAC,KAAK,GAAG;YAC/B;QACJ;QACA,wFAAwF;QACxF,IAAI;QACJ,IAAI,IAAI,CAAC,SAAS,KAAK,WAAW;YAC9B,YAAY;gBACR,GAAG,SAAS;gBACZ,GAAG,IAAI,CAAC,SAAS;YACrB;QACJ;QACA,IAAI,SAAS,cAAc,WAAW;YAClC,YAAY;gBACR,GAAG,SAAS;gBACZ,GAAG,QAAQ,SAAS;YACxB;QACJ;QACA,OAAO;IACX;IACA;;;KAGC,GACD,mBAAmB,SAAS,EAAE;QAC1B,IAAI,aACA,UAAU,IAAI,KAAK,iBACnB,UAAU,WAAW,CAAC,MAAM,IAC5B,CAAA,GAAA,sKAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,WAAW,CAAC,MAAM,GAAG;YAClD,OAAO,CAAA,GAAA,kLAAA,CAAA,2BAAwB,AAAD,EAAE,UAAU,WAAW,CAAC,MAAM,EAAE,UAAU,WAAW,CAAC,IAAI,EAAE;gBACtF,aAAa,UAAU,WAAW,CAAC,WAAW;YAClD;QACJ;QACA,OAAO;IACX;IACA,kBAAkB,OAAO,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,MAAM,uBAAuB;gBACzB,SAAS,IAAI,CAAC,YAAY,CAAC,OAAO;YACtC;YACA,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,MAAM,SAAS;gBACX,GAAG,IAAI,CAAC,YAAY;gBACpB,SAAS;gBACT,SAAS,IAAI,CAAC,OAAO;gBACrB,YAAY;YAChB;YACA,IAAI,CAAC,OAAO,OAAO,EAAE;gBACjB,OAAO,OAAO,OAAO;YACzB;YACA,IAAI,CAAC,MAAM,GAAG,IAAI,4KAAA,CAAA,SAAY,CAAC;QACnC;QACA,MAAM,iBAAiB;YACnB,GAAG,IAAI,CAAC,YAAY;YACpB,GAAG,OAAO;QACd;QACA,OAAO;IACX;IACA,kCAAkC;IAClC,wCAAwC,IAAI,EAAE,MAAM,EAAE;QAClD,IAAI,CAAA,GAAA,wKAAA,CAAA,eAAY,AAAD,EAAE,OAAO;YACpB,IAAI,QAAQ,WAAW,WAAW;gBAC9B,OAAO;oBACH,GAAG,IAAI;oBACP,UAAU;wBACN,GAAG,KAAK,QAAQ;wBAChB,QAAQ,OAAO,MAAM;oBACzB;gBACJ;YACJ;YACA,OAAO;QACX;QACA,OAAO,CAAA,GAAA,iKAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM;IACtC;IACA,UAAU,KAAK,EAAE,MAAM,EAAE;QACrB,IAAI;QACJ,IAAI,QAAQ,WAAW,WAAW;YAC9B,SAAS,OAAO,MAAM;QAC1B,OACK,IAAI,IAAI,CAAC,yBAAyB,KAAK,WAAW;YACnD,SAAS,IAAI,CAAC,yBAAyB;QAC3C;QACA,OAAO,IAAI,CAAC,UAAU,CAAC;YACnB,OAAO,MAAM,GAAG,CAAC,CAAC,OAAS,cAAc,QACnC,OACA,IAAI,CAAC,uCAAuC,CAAC,MAAM;oBAAE;gBAAO;YAClE,GAAG,MAAM;QACb;IACJ;IACA,YAAY,GACZ,kBAAkB,GAAG,UAAU,EAAE;QAC7B,OAAO,WAAW,MAAM,CAAC,CAAC,KAAK;YAC3B,IAAI,aAAa,UAAU,UAAU,EAAE;gBACnC,IAAI,UAAU,CAAC,gBAAgB,IAC3B,UAAU,UAAU,CAAC,gBAAgB,IAAI;gBAC7C,IAAI,UAAU,CAAC,YAAY,IAAI,UAAU,UAAU,CAAC,YAAY,IAAI;gBACpE,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,UAAU,CAAC,WAAW,IAAI;YACtE;YACA,OAAO;QACX,GAAG;YACC,YAAY;gBACR,kBAAkB;gBAClB,cAAc;gBACd,aAAa;YACjB;QACJ;IACJ;IACA,MAAM,yBAAyB,QAAQ,EAAE;QACrC,IAAI,aAAa;QACjB,IAAI,mBAAmB;QACvB,IAAI,gBAAgB;QACpB,kHAAkH;QAClH,IAAI,IAAI,CAAC,KAAK,KAAK,sBAAsB;YACrC,mBAAmB;YACnB,gBAAgB,CAAC;QACrB,OACK;YACD,mBAAmB;YACnB,gBAAgB;QACpB;QACA,MAAM,kBAAkB,MAAM,QAAQ,GAAG,CAAC,SAAS,GAAG,CAAC,OAAO;YAC1D,MAAM,YAAY,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,OAAO;YACzD,MAAM,YAAY,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB;YAC9D,MAAM,YAAY,QAAQ,IAAI,KAAK,YAC7B,gBAAiB,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,IACrD;YACN,IAAI,QAAQ,YAAY,mBAAmB,YAAY;YACvD,gGAAgG;YAChG,MAAM,gBAAgB;YACtB,IAAI,cAAc,QAAQ,OAAO,YAAY;gBACzC,SAAS;YACb;YACA,IAAI,cAAc,iBAAiB,EAAE,eAAe;gBAChD,SAAS;YACb;YACA,IAAI,eAAe,kBAAkB,eAAe,MAAM;gBACtD,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,iBAAiB,CAAC,aAAa,EAAE;YACpF;YACA,IAAI,cAAc,iBAAiB,CAAC,aAAa,EAAE,WAAW;gBAC1D,IAAI;oBACA,SAAS,MAAM,IAAI,CAAC,YAAY,CAChC,6BAA6B;oBAC7B,KAAK,SAAS,CAAC,KAAK,KAAK,CAAC,cAAc,iBAAiB,CAAC,aAAa,EAAE;gBAC7E,EACA,OAAO,OAAO;oBACV,QAAQ,KAAK,CAAC,oCAAoC,OAAO,KAAK,SAAS,CAAC,cAAc,iBAAiB,CAAC,aAAa;oBACrH,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,iBAAiB,CAAC,aAAa,EAAE;gBACpF;YACJ;YACA,cAAc;YACd,OAAO;QACX;QACA,cAAc,GAAG,2DAA2D;QAC5E,OAAO;YAAE;YAAY;QAAgB;IACzC;IACA,cAAc,GACd,MAAM,6BAA6B,WAAW,EAAE;QAC5C,MAAM,mBAAmB,MAAM,QAAQ,GAAG,CAAC,YAAY,GAAG,CAAC,OAAO;YAC9D,IAAI,WAAW,OAAO,CAAC,iBAAiB,EAAE,eAAe;gBACrD,OAAO,CAAC,MAAM,IAAI,CAAC,wBAAwB,CAAC;oBAAC,WAAW,OAAO;iBAAC,CAAC,EAC5D,eAAe,CAAC,EAAE;YAC3B,OACK;gBACD,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,OAAO,CAAC,OAAO;YAC7D;QACJ;QACA,OAAO,iBAAiB,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG;IACpD;IACA,cAAc,GACd,MAAM,kCAAkC,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE;QACxE,6GAA6G;QAC7G,+GAA+G;QAC/G,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,UAAU;QACvE,yFAAyF;QACzF,IAAI,aAAa,kBAAkB,QAAQ;YACvC,MAAM,oBAAoB,CAAA,GAAA,qLAAA,CAAA,4BAAyB,AAAD,EAAE;YACpD,UAAU,MAAM,IAAI,CAAC,YAAY,CAAC;YAClC,UAAU,GAAG,0BAA0B;QAC3C;QACA,0GAA0G;QAC1G,wGAAwG;QACxG,wDAAwD;QACxD,IAAI,aAAa,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,QAAQ,OAAO,WAAW;YAC9D,UAAU;QACd;QACA,6CAA6C;QAC7C,oFAAoF;QACpF,mDAAmD;QACnD,IAAI,kBAAkB,QAAQ;YAC1B,UAAU;QACd,OACK,IAAI,OAAO,kBAAkB,UAAU;YACxC,UAAU,AAAC,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,IAAI,IAAK;QAC9D;QACA,OAAO;IACX;IACA,qBAAqB,YAAY,EAAE,MAAM,EAAE;QACvC,KAAK;QACL,gDAAgD;QAChD,cAAc;QACd,8BAA8B;QAC9B,8CAA8C;QAC9C,QAAQ;QACR,8DAA8D;QAC9D,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,+BAA+B,eAAe;YAC9C,SAAS,aAAa,MAAM;YAC5B,OAAO,aAAa,IAAI;YACxB,SAAS,aAAa,MAAM;YAC5B,aAAa,aAAa,UAAU;QACxC,OACK;YACD,SAAS;YACT,OAAO,QAAQ;YACf,SAAS,QAAQ;YACjB,aAAa,QAAQ;QACzB;QACA,IAAI;QACJ,IAAI;QACJ,IAAI,QAAQ,WAAW,aAAa,WAAW,YAAY;YACvD,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,YACvB,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,aACvB,IAAI,CAAC,KAAK,KAAK,SAAS;YACxB,IAAI,WAAW,WAAW;gBACtB,SAAS;YACb;QACJ,OACK,IAAI,WAAW,cAAc;YAC9B,QAAQ,IAAI,CAAC,CAAC,mDAAmD,EAAE,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC;QACnH;QACA,IAAI,WAAW,YAAY;YACvB,IAAI;YACJ,IAAI,CAAA,GAAA,sKAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;gBAC5B,eAAe,6KAAA,CAAA,yBAAsB,CAAC,aAAa,CAAC;gBACpD,qBAAqB,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAE;YACtC,OACK;gBACD,eAAe,IAAI,uLAAA,CAAA,mBAAgB;YACvC;YACA,MAAM,IAAI,CAAC,UAAU,CAAC;gBAClB,iBAAiB;oBAAE,MAAM;gBAAc;gBACvC,6BAA6B;oBACzB,QAAQ;wBAAE,QAAQ;oBAAW;oBAC7B,QAAQ;gBACZ;YACJ;QACJ,OACK,IAAI,WAAW,cAAc;YAC9B,MAAM,IAAI,CAAC,UAAU,CAAC;gBAClB,iBAAiB;oBACb,MAAM;oBACN,aAAa;wBACT,MAAM,QAAQ;wBACd,aAAa,CAAA,GAAA,sKAAA,CAAA,uBAAoB,AAAD,EAAE;wBAClC;wBACA,QAAQ,QAAQ;oBACpB;gBACJ;gBACA,6BAA6B;oBACzB,QAAQ;wBAAE,QAAQ;oBAAa;oBAC/B,QAAQ,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAE;gBACzB;YACJ;YACA,IAAI,CAAA,GAAA,sKAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;gBAC5B,MAAM,YAAY,6KAAA,CAAA,yBAAsB,CAAC,aAAa,CAAC;gBACvD,eAAe,kKAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,CAAC;oBAChC,IAAI,YAAY,UAAU,iBAAiB,EAAE;wBACzC,OAAO,UAAU,iBAAiB,CAAC,MAAM;oBAC7C;oBACA,OAAO;gBACX;YACJ,OACK;gBACD,eAAe,IAAI,uLAAA,CAAA,mBAAgB;YACvC;QACJ,OACK;YACD,IAAI,eAAe,QAAQ;YAC3B,sBAAsB;YACtB,IAAI,CAAA,GAAA,sKAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;gBAC5B,MAAM,eAAe,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAE;gBAClC,MAAM,IAAI,CAAC,UAAU,CAAC;oBAClB,OAAO;wBACH;4BACI,MAAM;4BACN,UAAU;gCACN,MAAM;gCACN,aAAa,aAAa,WAAW;gCACrC,YAAY;4BAChB;wBACJ;qBACH;oBACD,aAAa;wBACT,MAAM;wBACN,UAAU;4BACN,MAAM;wBACV;oBACJ;oBACA,6BAA6B;wBACzB,QAAQ;4BAAE,QAAQ;wBAAkB;wBACpC,QAAQ;oBACZ;oBACA,0EAA0E;oBAC1E,GAAI,QAAQ,WAAW,YAAY;wBAAE,QAAQ,OAAO,MAAM;oBAAC,IAAI,CAAC,CAAC;gBACrE;gBACA,eAAe,IAAI,4MAAA,CAAA,2BAAwB,CAAC;oBACxC,cAAc;oBACd,SAAS;oBACT,WAAW;gBACf;YACJ,OACK;gBACD,IAAI;gBACJ,IAAI,OAAO,OAAO,IAAI,KAAK,YACvB,OAAO,OAAO,UAAU,KAAK,YAC7B,OAAO,UAAU,IAAI,MAAM;oBAC3B,2BAA2B;oBAC3B,eAAe,OAAO,IAAI;gBAC9B,OACK;oBACD,eAAe,OAAO,KAAK,IAAI;oBAC/B,2BAA2B;wBACvB,MAAM;wBACN,aAAa,OAAO,WAAW,IAAI;wBACnC,YAAY;oBAChB;gBACJ;gBACA,MAAM,IAAI,CAAC,UAAU,CAAC;oBAClB,OAAO;wBACH;4BACI,MAAM;4BACN,UAAU;wBACd;qBACH;oBACD,aAAa;wBACT,MAAM;wBACN,UAAU;4BACN,MAAM;wBACV;oBACJ;oBACA,6BAA6B;wBACzB,QAAQ;4BAAE,QAAQ;wBAAkB;wBACpC,QAAQ,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAE;oBACzB;oBACA,0EAA0E;oBAC1E,GAAI,QAAQ,WAAW,YAAY;wBAAE,QAAQ,OAAO,MAAM;oBAAC,IAAI,CAAC,CAAC;gBACrE;gBACA,eAAe,IAAI,4MAAA,CAAA,2BAAwB,CAAC;oBACxC,cAAc;oBACd,SAAS;gBACb;YACJ;QACJ;QACA,IAAI,CAAC,YAAY;YACb,OAAO,IAAI,IAAI,CAAC;QACpB;QACA,MAAM,eAAe,yKAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;YAC5C,8DAA8D;YAC9D,QAAQ,CAAC,OAAO,SAAW,aAAa,MAAM,CAAC,MAAM,GAAG,EAAE;QAC9D;QACA,MAAM,aAAa,yKAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;YAC1C,QAAQ,IAAM;QAClB;QACA,MAAM,qBAAqB,aAAa,aAAa,CAAC;YAClD,WAAW;gBAAC;aAAW;QAC3B;QACA,OAAO,kKAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC;YAAC;gBAAE,KAAK;YAAI;YAAG;SAAmB;IACnE;AACJ;AAQO,MAAM,4BAA4B;IACrC,iBAAiB,OAAO,EAAE;QACtB,IAAI;QACJ,IAAI,SAAS,WAAW,WAAW;YAC/B,SAAS,QAAQ,MAAM;QAC3B,OACK,IAAI,IAAI,CAAC,yBAAyB,KAAK,WAAW;YACnD,SAAS,IAAI,CAAC,yBAAyB;QAC3C;QACA,MAAM,SAAS;YACX,OAAO,IAAI,CAAC,KAAK;YACjB,aAAa,IAAI,CAAC,WAAW;YAC7B,OAAO,IAAI,CAAC,IAAI;YAChB,MAAM,IAAI,CAAC,IAAI;YACf,0EAA0E;YAC1E,QAAQ,IAAI,CAAC,SAAS;YACtB,sBAAsB,SAAS;YAC/B,YAAY,SAAS;YACrB,SAAS,SAAS;YAClB,OAAO,SAAS,OAAO,SACjB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,KAAK,EAAE;gBACzC,QAAQ,IAAI,CAAC,SAAS;gBACtB;YACJ,KACE;YACN,aAAa,oBAAoB,SAAS,eACpC,SAAS,cACT,CAAC;gBACC,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS;gBACpD,IAAI,OAAO,cAAc,YAAY,UAAU,WAAW;oBACtD,OAAO;wBAAE,MAAM;wBAAY,MAAM,UAAU,QAAQ,CAAC,IAAI;oBAAC;gBAC7D,OACK;oBACD,OAAO;gBACX;YACJ,CAAC;YACL,MAAM,CAAC;gBACH,IAAI,SAAS,MACT,OAAO,QAAQ,IAAI;gBACvB,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC,SAAS;gBAChD,IAAI,QAAQ,SAAS,eAAe;oBAChC,IAAI,OAAO,WAAW,CAAC,MAAM,IAAI,MAAM;wBACnC,OAAO;4BACH,QAAQ;gCACJ,MAAM;gCACN,QAAQ,OAAO,WAAW,CAAC,MAAM;gCACjC,aAAa,OAAO,WAAW,CAAC,WAAW;gCAC3C,MAAM,OAAO,WAAW,CAAC,IAAI;gCAC7B,QAAQ,OAAO,WAAW,CAAC,MAAM;4BACrC;wBACJ;oBACJ;oBACA,OAAO;gBACX;gBACA,OAAO;oBAAE;gBAAO;YACpB,CAAC;YACD,qBAAqB,SAAS;YAC9B,mBAAmB,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,YAAY,IAAI,CAAC,SAAS;YACrE,GAAI,IAAI,CAAC,UAAU,GAAG;gBAAE,OAAO;YAAM,IAAI,CAAC,CAAC;YAC3C,GAAG,IAAI,CAAC,WAAW;QACvB;QACA,MAAM,YAAY,IAAI,CAAC,mBAAmB,CAAC;QAC3C,IAAI,cAAc,WAAW;YACzB,OAAO,SAAS,GAAG;QACvB;QACA,OAAO;IACX;IACA,MAAM,UAAU,QAAQ,EAAE,OAAO,EAAE;QAC/B,MAAM,mBAAmB,IAAI,CAAC,gBAAgB,CAAC;QAC/C,IAAI,iBAAiB,MAAM,EAAE;YACzB,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,UAAU;YACpD,IAAI;YACJ,WAAW,MAAM,SAAS,OAAQ;gBAC9B,MAAM,OAAO,CAAC,iBAAiB,GAAG;oBAC9B,GAAG,MAAM,cAAc;oBACvB,GAAG,MAAM,OAAO,CAAC,iBAAiB;gBACtC;gBACA,aAAa,YAAY,OAAO,UAAU;YAC9C;YACA,OAAO;gBACH,aAAa,aAAa;oBAAC;iBAAW,GAAG,EAAE;gBAC3C,WAAW;oBACP,qBAAqB,YAAY,SAC3B;gBACV;YACJ;QACJ,OACK;YACD,MAAM,QAAQ,IAAI,CAAC,iCAAiC,CAAC;YACrD,MAAM,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC;gBACxC;gBACA,GAAG,gBAAgB;gBACnB,QAAQ;YACZ,GAAG;gBAAE,QAAQ,SAAS;gBAAQ,GAAG,SAAS,OAAO;YAAC;YAClD,OAAO;gBACH,aAAa;oBACT;wBACI,MAAM,KAAK,WAAW;wBACtB,SAAS,IAAI,CAAC,qCAAqC,CAAC;oBACxD;iBACH;gBACD,WAAW;oBACP,IAAI,KAAK,EAAE;oBACX,qBAAqB,KAAK,KAAK,GACzB;wBACE,cAAc,KAAK,KAAK,CAAC,YAAY;wBACrC,kBAAkB,KAAK,KAAK,CAAC,aAAa;wBAC1C,aAAa,KAAK,KAAK,CAAC,YAAY;oBACxC,IACE;gBACV;YACJ;QACJ;IACJ;IACA,OAAO,sBAAsB,QAAQ,EAAE,OAAO,EAAE;QAC5C,MAAM,iBAAiB,MAAM,IAAI,CAAC,mBAAmB,CAAC;YAClD,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ;YACjC,OAAO,IAAI,CAAC,iCAAiC,CAAC;YAC9C,QAAQ;QACZ,GAAG;QACH,WAAW,MAAM,QAAQ,eAAgB;YACrC,MAAM,QAAQ,IAAI,CAAC,wCAAwC,CAAC;YAC5D,IAAI,SAAS,MACT;YACJ,MAAM;QACV;IACJ;IACA,MAAM,oBAAoB,OAAO,EAAE,cAAc,EAAE;QAC/C,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACpB,MAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC;YAC7C,IAAI;gBACA,wCAAwC;gBACxC,IAAI,QAAQ,IAAI,EAAE,QAAQ,SAAS,iBAAiB,CAAC,QAAQ,MAAM,EAAE;oBACjE,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS;gBACtD;gBACA,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS;YACvD,EACA,OAAO,GAAG;gBACN,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,wBAAqB,AAAD,EAAE;gBACpC,MAAM;YACV;QACJ;IACJ;IACA,cAAc,GACd,sCAAsC,QAAQ,EAAE;QAC5C,IAAI,SAAS,KAAK,EAAE;YAChB,kDAAkD;YAClD,MAAM,QAAQ,IAAI,MAAM,SAAS,KAAK,CAAC,OAAO;YAC9C,MAAM,IAAI,GAAG,SAAS,KAAK,CAAC,IAAI;YAChC,MAAM;QACV;QACA,IAAI;QACJ,MAAM,UAAU,EAAE;QAClB,MAAM,aAAa,EAAE;QACrB,MAAM,qBAAqB,EAAE;QAC7B,MAAM,oBAAoB;YACtB,OAAO,SAAS,KAAK;YACrB,YAAY,SAAS,UAAU;YAC/B,IAAI,SAAS,EAAE;YACf,oBAAoB,SAAS,kBAAkB;YAC/C,UAAU,SAAS,QAAQ;YAC3B,QAAQ,SAAS,MAAM;YACvB,QAAQ,SAAS,MAAM;YACvB,MAAM,SAAS,IAAI;YACnB,cAAc,SAAS,YAAY;YACnC,gDAAgD;YAChD,YAAY,SAAS,KAAK;QAC9B;QACA,MAAM,oBAAoB,CAAC;QAC3B,KAAK,MAAM,QAAQ,SAAS,MAAM,CAAE;YAChC,IAAI,KAAK,IAAI,KAAK,WAAW;gBACzB,YAAY,KAAK,EAAE;gBACnB,QAAQ,IAAI,IAAI,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;oBAClC,IAAI,KAAK,IAAI,KAAK,eAAe;wBAC7B,IAAI,YAAY,QAAQ,KAAK,MAAM,IAAI,MAAM;4BACzC,kBAAkB,MAAM,GAAG,KAAK,MAAM;wBAC1C;wBACA,OAAO;4BACH,MAAM;4BACN,MAAM,KAAK,IAAI;4BACf,aAAa,KAAK,WAAW;wBACjC;oBACJ;oBACA,IAAI,KAAK,IAAI,KAAK,WAAW;wBACzB,kBAAkB,OAAO,GAAG,KAAK,OAAO;wBACxC,OAAO,EAAE;oBACb;oBACA,OAAO;gBACX;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,iBAAiB;gBACpC,MAAM,YAAY;oBACd,UAAU;wBAAE,MAAM,KAAK,IAAI;wBAAE,WAAW,KAAK,SAAS;oBAAC;oBACvD,IAAI,KAAK,OAAO;gBACpB;gBACA,IAAI;oBACA,WAAW,IAAI,CAAC,CAAA,GAAA,4MAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;wBAAE,UAAU;oBAAK;gBAC9D,EACA,OAAO,GAAG;oBACN,IAAI;oBACJ,IAAI,OAAO,MAAM,YACb,KAAK,QACL,aAAa,KACb,OAAO,EAAE,OAAO,KAAK,UAAU;wBAC/B,aAAa,EAAE,OAAO;oBAC1B;oBACA,mBAAmB,IAAI,CAAC,CAAA,GAAA,4MAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;gBAC3D;gBACA,iBAAiB,CAAC,2BAA2B,KAAK,CAAC;gBACnD,IAAI,KAAK,EAAE,EAAE;oBACT,iBAAiB,CAAC,2BAA2B,CAAC,KAAK,OAAO,CAAC,GAAG,KAAK,EAAE;gBACzE;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,aAAa;gBAChC,kBAAkB,SAAS,GAAG;YAClC,OACK;gBACD,kBAAkB,YAAY,KAAK,EAAE;gBACrC,kBAAkB,YAAY,CAAC,IAAI,CAAC;YACxC;QACJ;QACA,OAAO,IAAI,+JAAA,CAAA,YAAS,CAAC;YACjB,IAAI;YACJ;YACA;YACA;YACA,gBAAgB,SAAS,KAAK;YAC9B;YACA;QACJ;IACJ;IACA,cAAc,GACd,yCAAyC,KAAK,EAAE;QAC5C,MAAM,UAAU,EAAE;QAClB,IAAI,iBAAiB,CAAC;QACtB,IAAI;QACJ,MAAM,mBAAmB,EAAE;QAC3B,MAAM,oBAAoB,CAAC;QAC3B,MAAM,oBAAoB,CAAC;QAC3B,IAAI;QACJ,IAAI,MAAM,IAAI,KAAK,8BAA8B;YAC7C,QAAQ,IAAI,CAAC;gBACT,MAAM;gBACN,MAAM,MAAM,KAAK;gBACjB,OAAO,MAAM,aAAa;YAC9B;QACJ,OACK,IAAI,MAAM,IAAI,KAAK,yCAAyC;YAC7D,QAAQ,IAAI,CAAC;gBACT,MAAM;gBACN,MAAM;gBACN,aAAa;oBAAC,MAAM,UAAU;iBAAC;gBAC/B,OAAO,MAAM,aAAa;YAC9B;QACJ,OACK,IAAI,MAAM,IAAI,KAAK,gCACpB,MAAM,IAAI,CAAC,IAAI,KAAK,WAAW;YAC/B,KAAK,MAAM,IAAI,CAAC,EAAE;QACtB,OACK,IAAI,MAAM,IAAI,KAAK,gCACpB,MAAM,IAAI,CAAC,IAAI,KAAK,iBAAiB;YACrC,iBAAiB,IAAI,CAAC;gBAClB,MAAM;gBACN,MAAM,MAAM,IAAI,CAAC,IAAI;gBACrB,MAAM,MAAM,IAAI,CAAC,SAAS;gBAC1B,IAAI,MAAM,IAAI,CAAC,OAAO;gBACtB,OAAO,MAAM,YAAY;YAC7B;YACA,iBAAiB,CAAC,2BAA2B,GAAG;gBAC5C,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC,EAAE;YACvC;QACJ,OACK,IAAI,MAAM,IAAI,KAAK,+BACpB;YACI;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG;YAC7B,kBAAkB,YAAY,GAAG;gBAAC,MAAM,IAAI;aAAC;QACjD,OACK,IAAI,MAAM,IAAI,KAAK,oBAAoB;YACxC,kBAAkB,EAAE,GAAG,MAAM,QAAQ,CAAC,EAAE;YACxC,kBAAkB,UAAU,GAAG,MAAM,QAAQ,CAAC,KAAK;YACnD,kBAAkB,KAAK,GAAG,MAAM,QAAQ,CAAC,KAAK;QAClD,OACK,IAAI,MAAM,IAAI,KAAK,sBAAsB;YAC1C,MAAM,MAAM,IAAI,CAAC,qCAAqC,CAAC,MAAM,QAAQ;YACrE,iBAAiB,MAAM,QAAQ,CAAC,KAAK;YACrC,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,QAAQ,SAAS,eAAe;gBACrD,kBAAkB,MAAM,KAAK,KAAK,KAAK,CAAC,IAAI,IAAI;YACpD;YACA,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,MAAM,QAAQ,EAAG;gBACvD,IAAI,QAAQ,MACR,iBAAiB,CAAC,IAAI,GAAG;YACjC;QACJ,OACK,IAAI,MAAM,IAAI,KAAK,0CAA0C;YAC9D,iBAAiB,IAAI,CAAC;gBAClB,MAAM;gBACN,MAAM,MAAM,KAAK;gBACjB,OAAO,MAAM,YAAY;YAC7B;QACJ,OACK,IAAI,MAAM,IAAI,KAAK,wCACpB,MAAM,IAAI,KAAK,uCAAuC;YACtD,iBAAiB;gBACb,cAAc;oBACV,IAAI,MAAM,OAAO;oBACjB,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,cAAc;oBAChE,QAAQ;gBACZ;YACJ;QACJ,OACK,IAAI,MAAM,IAAI,KAAK,yBAAyB;YAC7C,kBAAkB,OAAO,GAAG,MAAM,OAAO;QAC7C,OACK,IAAI,MAAM,IAAI,KAAK,gCACpB,UAAU,SACV,MAAM,IAAI,CAAC,IAAI,KAAK,aAAa;YACjC,MAAM,UAAU,MACX,IAAI,CAAC,OAAO,GACX,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,QAAU,CAAC;oBACpC,GAAG,CAAC;oBACJ;gBACJ,CAAC,KACC;YACN,kBAAkB,SAAS,GAAG;gBAC1B,2FAA2F;gBAC3F,6FAA6F;gBAC7F,qCAAqC;gBACrC,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,MAAM,MAAM,IAAI,CAAC,IAAI;gBACrB,GAAI,UAAU;oBAAE;gBAAQ,IAAI,CAAC,CAAC;YAClC;QACJ,OACK,IAAI,MAAM,IAAI,KAAK,yCAAyC;YAC7D,kBAAkB,SAAS,GAAG;gBAC1B,MAAM;gBACN,SAAS;oBAAC;wBAAE,GAAG,MAAM,IAAI;wBAAE,OAAO,MAAM,aAAa;oBAAC;iBAAE;YAC5D;QACJ,OACK,IAAI,MAAM,IAAI,KAAK,yCAAyC;YAC7D,kBAAkB,SAAS,GAAG;gBAC1B,MAAM;gBACN,SAAS;oBACL;wBACI,MAAM,MAAM,KAAK;wBACjB,MAAM;wBACN,OAAO,MAAM,aAAa;oBAC9B;iBACH;YACL;QACJ,OACK,IAAI,MAAM,IAAI,KAAK,gDAAgD;YACpE,2EAA2E;YAC3E,oEAAoE;YACpE,OAAO;QACX,OACK;YACD,OAAO;QACX;QACA,OAAO,IAAI,wJAAA,CAAA,sBAAmB,CAAC;YAC3B,wDAAwD;YACxD,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI,EAAE,IAAI,CAAC;YAC5C,SAAS,IAAI,+JAAA,CAAA,iBAAc,CAAC;gBACxB;gBACA;gBACA;gBACA;gBACA;gBACA;YACJ;YACA;QACJ;IACJ;IACA,cAAc,GACd,kCAAkC,QAAQ,EAAE;QACxC,OAAO,SAAS,OAAO,CAAC,CAAC;YACrB,MAAM,oBAAoB,MAAM,iBAAiB;YACjD,IAAI,OAAO,oBAAoB;YAC/B,IAAI,SAAS,YAAY,iBAAiB,IAAI,CAAC,KAAK,GAChD,OAAO;YACX,IAAI,SAAS,YAAY;gBACrB,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,SAAS,QAAQ;gBACjB,MAAM,cAAc;gBACpB,8BAA8B;gBAC9B,IAAI,mBAAmB,SAAS,wBAAwB;oBACpD,MAAM,SAAS,CAAC;wBACZ,IAAI,OAAO,YAAY,OAAO,KAAK,UAAU;4BACzC,OAAO;gCACH,MAAM;gCACN,WAAW,YAAY,OAAO;4BAClC;wBACJ;wBACA,IAAI,MAAM,OAAO,CAAC,YAAY,OAAO,GAAG;4BACpC,MAAM,gBAAgB,YAAY,OAAO,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;4BACjE,IAAI,eACA,OAAO;4BACX,MAAM,UAAU,YAAY,OAAO,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;4BAC3D,IAAI,SAAS;gCACT,OAAO;oCACH,MAAM;oCACN,WAAW,OAAO,QAAQ,SAAS,KAAK,WAClC,QAAQ,SAAS,GACjB,QAAQ,SAAS,CAAC,GAAG;gCAC/B;4BACJ;wBACJ;wBACA,MAAM,IAAI,MAAM;oBACpB,CAAC;oBACD,OAAO;wBACH,MAAM;wBACN;wBACA,SAAS,YAAY,YAAY;oBACrC;gBACJ;gBACA,OAAO;oBACH,MAAM;oBACN,SAAS,YAAY,YAAY;oBACjC,IAAI,YAAY,EAAE,EAAE,WAAW,SAAS,YAAY,EAAE,GAAG;oBACzD,QAAQ,OAAO,YAAY,OAAO,KAAK,WACjC,KAAK,SAAS,CAAC,YAAY,OAAO,IAClC,YAAY,OAAO;gBAC7B;YACJ;YACA,IAAI,SAAS,aAAa;gBACtB,0DAA0D;gBAC1D,IAAI,CAAC,IAAI,CAAC,UAAU,IAChB,MAAM,iBAAiB,CAAC,MAAM,IAAI,QAClC,MAAM,OAAO,CAAC,MAAM,iBAAiB,CAAC,MAAM,KAC5C,MAAM,iBAAiB,CAAC,MAAM,CAAC,MAAM,GAAG,KACxC,MAAM,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAS,UAAU,OAAO;oBAChE,OAAO,MAAM,iBAAiB,CAAC,MAAM;gBACzC;gBACA,+DAA+D;gBAC/D,MAAM,QAAQ,EAAE;gBAChB,kBAAkB;gBAClB,IAAI,mBAAmB,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE;oBAClD,MAAM,gBAAgB,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,SAAS;oBAC/E,MAAM,IAAI,CAAC;gBACf;gBACA,aAAa;gBACb,IAAI,EAAE,OAAO,EAAE,GAAG;gBAClB,IAAI,mBAAmB,SAAS;oBAC5B,IAAI,OAAO,YAAY,UAAU;wBAC7B,UAAU;4BACN;gCAAE,MAAM;gCAAe,MAAM;gCAAS,aAAa,EAAE;4BAAC;yBACzD;oBACL;oBACA,UAAU;2BACH;wBACH;4BAAE,MAAM;4BAAW,SAAS,kBAAkB,OAAO;wBAAC;qBACzD;gBACL;gBACA,MAAM,IAAI,CAAC;oBACP,MAAM;oBACN,MAAM;oBACN,GAAI,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,UAClD;wBAAE,IAAI,MAAM,EAAE;oBAAC,IACf,CAAC,CAAC;oBACR,SAAS,OAAO,YAAY,WACtB,UACA,QAAQ,OAAO,CAAC,CAAC;wBACf,IAAI,KAAK,IAAI,KAAK,QAAQ;4BACtB,OAAO;gCACH,MAAM;gCACN,MAAM,KAAK,IAAI;gCACf,qDAAqD;gCACrD,aAAa,KAAK,WAAW,IAAI,EAAE;4BACvC;wBACJ;wBACA,IAAI,KAAK,IAAI,KAAK,iBACd,KAAK,IAAI,KAAK,WAAW;4BACzB,OAAO;wBACX;wBACA,OAAO,EAAE;oBACb;gBACR;gBACA,MAAM,kBAAkB,mBAAmB,CAAC,2BAA2B;gBACvE,IAAI,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE,UAAU,CAAC,CAAC,MAAM,UAAU,EAAE,QAAQ;oBAClD,MAAM,IAAI,IAAI,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,WAAa,CAAC;4BAC9C,MAAM;4BACN,MAAM,SAAS,IAAI;4BACnB,WAAW,KAAK,SAAS,CAAC,SAAS,IAAI;4BACvC,SAAS,SAAS,EAAE;4BACpB,GAAI,IAAI,CAAC,UAAU,GACb;gCAAE,IAAI,iBAAiB,CAAC,SAAS,EAAE,CAAC;4BAAC,IACrC,CAAC,CAAC;wBACZ,CAAC;gBACL,OACK,IAAI,mBAAmB,YAAY;oBACpC,MAAM,IAAI,IAAI,kBAAkB,UAAU,CAAC,GAAG,CAAC,CAAC,WAAa,CAAC;4BAC1D,MAAM;4BACN,MAAM,SAAS,QAAQ,CAAC,IAAI;4BAC5B,SAAS,SAAS,EAAE;4BACpB,WAAW,SAAS,QAAQ,CAAC,SAAS;4BACtC,GAAI,IAAI,CAAC,UAAU,GACb;gCAAE,IAAI,iBAAiB,CAAC,SAAS,EAAE,CAAC;4BAAC,IACrC,CAAC,CAAC;wBACZ,CAAC;gBACL;gBACA,MAAM,cAAc,MAAM,iBAAiB,CAAC,MAAM,EAAE,SAC9C,MAAM,iBAAiB,CAAC,MAAM,GAC9B,kBAAkB,YAAY;gBACpC,MAAM,uBAAuB;oBACzB;oBACA;oBACA;oBACA;iBACH;gBACD,IAAI,eAAe,MAAM;oBACrB,MAAM,kBAAkB;oBACxB,MAAM,mBAAmB,iBAAiB,OAAO,CAAC,OAAS,qBAAqB,QAAQ,CAAC,KAAK,IAAI;oBAClG,IAAI,iBAAiB,MAAM,GAAG,GAC1B,MAAM,IAAI,IAAI;gBACtB;gBACA,OAAO;YACX;YACA,IAAI,SAAS,UAAU,SAAS,YAAY,SAAS,aAAa;gBAC9D,IAAI,OAAO,MAAM,OAAO,KAAK,UAAU;oBACnC,OAAO;wBAAE,MAAM;wBAAW;wBAAM,SAAS,MAAM,OAAO;oBAAC;gBAC3D;gBACA,MAAM,WAAW,EAAE;gBACnB,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC;oBACnC,IAAI,KAAK,IAAI,KAAK,yBAAyB;wBACvC,SAAS,IAAI,CAAC;4BACV,MAAM;4BACN,qBAAqB,KAAK,mBAAmB;4BAC7C,SAAS,KAAK,OAAO;wBACzB;oBACJ;oBACA,IAAI,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;wBAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,gCAA6B,AAAD,EAAE,MAAM;oBAC/C;oBACA,IAAI,KAAK,IAAI,KAAK,QAAQ;wBACtB,OAAO;4BACH,MAAM;4BACN,MAAM,KAAK,IAAI;wBACnB;oBACJ;oBACA,IAAI,KAAK,IAAI,KAAK,aAAa;wBAC3B,OAAO;4BACH,MAAM;4BACN,WAAW,OAAO,KAAK,SAAS,KAAK,WAC/B,KAAK,SAAS,GACd,KAAK,SAAS,CAAC,GAAG;4BACxB,QAAQ,OAAO,KAAK,SAAS,KAAK,WAC5B,SACA,KAAK,SAAS,CAAC,MAAM;wBAC/B;oBACJ;oBACA,IAAI,KAAK,IAAI,KAAK,gBACd,KAAK,IAAI,KAAK,iBACd,KAAK,IAAI,KAAK,cAAc;wBAC5B,OAAO;oBACX;oBACA,OAAO,EAAE;gBACb;gBACA,IAAI,QAAQ,MAAM,GAAG,GAAG;oBACpB,SAAS,IAAI,CAAC;wBAAE,MAAM;wBAAW;wBAAM;oBAAQ;gBACnD;gBACA,OAAO;YACX;YACA,QAAQ,IAAI,CAAC,CAAC,gEAAgE,EAAE,MAAM;YACtF,OAAO,EAAE;QACb;IACJ;IACA,cAAc,GACd,yBAAyB,SAAS,EAAE;QAChC,iFAAiF;QACjF,MAAM,UAAU,CAAC,UAAU,OAAO,CAAC,MAAM,GAAG,IACtC,UAAU,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;YAC7B,MAAM,OAAO,IAAI,EAAE,CAAC,CAAC;YACrB,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;gBAC3B,KAAK,IAAI,IAAI,KAAK,IAAI;YAC1B,OACK;gBACD,IAAI,IAAI,CAAC;YACb;YACA,OAAO;QACX,GAAG;YAAC;gBAAE,GAAG,UAAU,OAAO,CAAC,EAAE;YAAC;SAAE,IAC9B,UAAU,OAAO,EAAE,GAAG,CAAC,CAAC,IAAM,OAAO,WAAW,CAAC,OAAO,OAAO,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAK,MAAM;QAC/F,OAAO;YACH,GAAG,SAAS;YACZ;QACJ;IACJ;IACA,cAAc,GACd,uBAAuB,KAAK,EAAE,MAAM,EAAE;QAClC,MAAM,eAAe,EAAE;QACvB,KAAK,MAAM,QAAQ,MAAO;YACtB,IAAI,cAAc,OAAO;gBACrB,IAAI,KAAK,IAAI,KAAK,sBAAsB,QAAQ,QAAQ;oBACpD,+EAA+E;oBAC/E,iEAAiE;oBACjE,KAAK,cAAc,GAAG;gBAC1B;gBACA,aAAa,IAAI,CAAC;YACtB,OACK,IAAI,CAAA,GAAA,wKAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBACzB,aAAa,IAAI,CAAC;oBACd,MAAM;oBACN,MAAM,KAAK,QAAQ,CAAC,IAAI;oBACxB,YAAY,KAAK,QAAQ,CAAC,UAAU;oBACpC,aAAa,KAAK,QAAQ,CAAC,WAAW;oBACtC,QAAQ,QAAQ,UAAU;gBAC9B;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AAKO,MAAM,8BAA8B;IACvC,cAAc,GACd,iBAAiB,OAAO,EAAE,KAAK,EAAE;QAC7B,IAAI;QACJ,IAAI,SAAS,WAAW,WAAW;YAC/B,SAAS,QAAQ,MAAM;QAC3B,OACK,IAAI,IAAI,CAAC,yBAAyB,KAAK,WAAW;YACnD,SAAS,IAAI,CAAC,yBAAyB;QAC3C;QACA,IAAI,sBAAsB,CAAC;QAC3B,IAAI,SAAS,mBAAmB,WAAW;YACvC,sBAAsB;gBAAE,gBAAgB,QAAQ,cAAc;YAAC;QACnE,OACK,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,OAAO,SAAS,GAAG;YAC/D,sBAAsB;gBAAE,gBAAgB;oBAAE,eAAe;gBAAK;YAAE;QACpE;QACA,MAAM,SAAS;YACX,OAAO,IAAI,CAAC,KAAK;YACjB,aAAa,IAAI,CAAC,WAAW;YAC7B,OAAO,IAAI,CAAC,IAAI;YAChB,mBAAmB,IAAI,CAAC,gBAAgB;YACxC,kBAAkB,IAAI,CAAC,eAAe;YACtC,UAAU,IAAI,CAAC,QAAQ;YACvB,cAAc,IAAI,CAAC,WAAW;YAC9B,GAAG,IAAI,CAAC,CAAC;YACT,YAAY,IAAI,CAAC,SAAS;YAC1B,MAAM,SAAS,QAAQ,IAAI,CAAC,aAAa;YACzC,MAAM,IAAI,CAAC,IAAI;YACf,0EAA0E;YAC1E,QAAQ,IAAI,CAAC,SAAS;YACtB,WAAW,SAAS;YACpB,eAAe,SAAS;YACxB,OAAO,SAAS,OAAO,SACjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,IAAI,CAAC,uCAAuC,CAAC,MAAM;oBAAE;gBAAO,MACxF;YACN,aAAa,CAAA,GAAA,kLAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS;YAC/C,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,SAAS;YAClD,MAAM,SAAS;YACf,GAAG,mBAAmB;YACtB,qBAAqB,SAAS;YAC9B,GAAI,IAAI,CAAC,KAAK,IAAI,SAAS,QACrB;gBAAE,OAAO,IAAI,CAAC,KAAK,IAAI,SAAS;YAAM,IACtC,CAAC,CAAC;YACR,GAAI,IAAI,CAAC,UAAU,IAAI,SAAS,aAC1B;gBAAE,YAAY,IAAI,CAAC,UAAU,IAAI,SAAS;YAAW,IACrD,CAAC,CAAC;YACR,GAAG,IAAI,CAAC,WAAW;QACvB;QACA,IAAI,SAAS,eAAe,WAAW;YACnC,OAAO,UAAU,GAAG,QAAQ,UAAU;QAC1C;QACA,IAAI,IAAI,CAAC,YAAY,KAAK,WAAW;YACjC,OAAO,YAAY,GAAG,IAAI,CAAC,YAAY;QAC3C;QACA,IAAI,SAAS,iBAAiB,WAAW;YACrC,OAAO,YAAY,GAAG,QAAQ,YAAY;QAC9C;QACA,MAAM,YAAY,IAAI,CAAC,mBAAmB,CAAC;QAC3C,IAAI,cAAc,aAAa,UAAU,MAAM,KAAK,WAAW;YAC3D,OAAO,gBAAgB,GAAG,UAAU,MAAM;QAC9C;QACA,IAAI,iBAAiB,OAAO,KAAK,GAAG;YAChC,OAAO,qBAAqB,GACxB,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,YAAY,IAAI,CAAC,SAAS;QAC1D,OACK;YACD,OAAO,UAAU,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,YAAY,IAAI,CAAC,SAAS;QAC1E;QACA,OAAO;IACX;IACA,MAAM,UAAU,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE;QAC3C,MAAM,gBAAgB,CAAC;QACvB,MAAM,SAAS,IAAI,CAAC,gBAAgB,CAAC;QACrC,MAAM,iBAAiB,+BAA+B,UAAU,IAAI,CAAC,KAAK;QAC1E,IAAI,OAAO,MAAM,EAAE;YACf,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,UAAU,SAAS;YAC7D,MAAM,cAAc,CAAC;YACrB,WAAW,MAAM,SAAS,OAAQ;gBAC9B,MAAM,OAAO,CAAC,iBAAiB,GAAG;oBAC9B,GAAG,MAAM,cAAc;oBACvB,GAAG,MAAM,OAAO,CAAC,iBAAiB;gBACtC;gBACA,MAAM,QAAQ,MAAM,cAAc,EAAE,cAAc;gBAClD,IAAI,WAAW,CAAC,MAAM,KAAK,WAAW;oBAClC,WAAW,CAAC,MAAM,GAAG;gBACzB,OACK;oBACD,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;gBACnD;YACJ;YACA,MAAM,cAAc,OAAO,OAAO,CAAC,aAC9B,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,GAAK,SAAS,MAAM,MAAM,SAAS,MAAM,KAC7D,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK;YACzB,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAC3D,gEAAgE;YAChE,0BAA0B;YAC1B,MAAM,mBAAmB,MAAM,IAAI,CAAC,iCAAiC,CAAC,UAAU,WAAW;YAC3F,MAAM,uBAAuB,MAAM,IAAI,CAAC,4BAA4B,CAAC;YACrE,cAAc,YAAY,GAAG;YAC7B,cAAc,aAAa,GAAG;YAC9B,cAAc,YAAY,GAAG,mBAAmB;YAChD,OAAO;gBACH;gBACA,WAAW;oBACP,qBAAqB;wBACjB,cAAc,cAAc,YAAY;wBACxC,kBAAkB,cAAc,aAAa;wBAC7C,aAAa,cAAc,YAAY;oBAC3C;gBACJ;YACJ;QACJ,OACK;YACD,MAAM,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC;gBACxC,GAAG,MAAM;gBACT,QAAQ;gBACR,UAAU;YACd,GAAG;gBACC,QAAQ,SAAS;gBACjB,GAAG,SAAS,OAAO;YACvB;YACA,MAAM,EAAE,mBAAmB,gBAAgB,EAAE,eAAe,YAAY,EAAE,cAAc,WAAW,EAAE,uBAAuB,mBAAmB,EAAE,2BAA2B,uBAAuB,EAAG,GAAG,MAAM,SAAS,CAAC;YACzN,IAAI,kBAAkB;gBAClB,cAAc,aAAa,GACvB,CAAC,cAAc,aAAa,IAAI,CAAC,IAAI;YAC7C;YACA,IAAI,cAAc;gBACd,cAAc,YAAY,GACtB,CAAC,cAAc,YAAY,IAAI,CAAC,IAAI;YAC5C;YACA,IAAI,aAAa;gBACb,cAAc,YAAY,GACtB,CAAC,cAAc,YAAY,IAAI,CAAC,IAAI;YAC5C;YACA,IAAI,qBAAqB,iBAAiB,QACtC,qBAAqB,kBAAkB,MAAM;gBAC7C,cAAc,mBAAmB,GAAG;oBAChC,GAAI,qBAAqB,iBAAiB,QAAQ;wBAC9C,OAAO,qBAAqB;oBAChC,CAAC;oBACD,GAAI,qBAAqB,kBAAkB,QAAQ;wBAC/C,YAAY,qBAAqB;oBACrC,CAAC;gBACL;YACJ;YACA,IAAI,yBAAyB,iBAAiB,QAC1C,yBAAyB,qBAAqB,MAAM;gBACpD,cAAc,oBAAoB,GAAG;oBACjC,GAAI,yBAAyB,iBAAiB,QAAQ;wBAClD,OAAO,yBAAyB;oBACpC,CAAC;oBACD,GAAI,yBAAyB,qBAAqB,QAAQ;wBACtD,WAAW,yBAAyB;oBACxC,CAAC;gBACL;YACJ;YACA,MAAM,cAAc,EAAE;YACtB,KAAK,MAAM,QAAQ,MAAM,WAAW,EAAE,CAAE;gBACpC,MAAM,OAAO,KAAK,OAAO,EAAE,WAAW;gBACtC,MAAM,aAAa;oBACf;oBACA,SAAS,IAAI,CAAC,uCAAuC,CAAC,KAAK,OAAO,IAAI;wBAAE,MAAM;oBAAY,GAAG;gBACjG;gBACA,WAAW,cAAc,GAAG;oBACxB,GAAI,KAAK,aAAa,GAAG;wBAAE,eAAe,KAAK,aAAa;oBAAC,IAAI,CAAC,CAAC;oBACnE,GAAI,KAAK,QAAQ,GAAG;wBAAE,UAAU,KAAK,QAAQ;oBAAC,IAAI,CAAC,CAAC;gBACxD;gBACA,IAAI,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE,WAAW,OAAO,GAAG;oBACjC,WAAW,OAAO,CAAC,cAAc,GAAG;gBACxC;gBACA,6DAA6D;gBAC7D,8DAA8D;gBAC9D,WAAW,OAAO,GAAG,IAAI,+JAAA,CAAA,YAAS,CAAC,OAAO,WAAW,CAAC,OAAO,OAAO,CAAC,WAAW,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK,CAAC,IAAI,UAAU,CAAC;gBAC3H,YAAY,IAAI,CAAC;YACrB;YACA,OAAO;gBACH;gBACA,WAAW;oBACP,YAAY;wBACR,cAAc,cAAc,YAAY;wBACxC,kBAAkB,cAAc,aAAa;wBAC7C,aAAa,cAAc,YAAY;oBAC3C;gBACJ;YACJ;QACJ;IACJ;IACA,OAAO,sBAAsB,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE;QACxD,MAAM,iBAAiB,+BAA+B,UAAU,IAAI,CAAC,KAAK;QAC1E,MAAM,SAAS;YACX,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS;gBAC9B,WAAW;YACf,EAAE;YACF,UAAU;YACV,QAAQ;QACZ;QACA,IAAI;QACJ,MAAM,iBAAiB,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ;QAC9D,IAAI;QACJ,WAAW,MAAM,QAAQ,eAAgB;YACrC,MAAM,SAAS,MAAM,SAAS,CAAC,EAAE;YACjC,IAAI,KAAK,KAAK,EAAE;gBACZ,QAAQ,KAAK,KAAK;YACtB;YACA,IAAI,CAAC,QAAQ;gBACT;YACJ;YACA,MAAM,EAAE,KAAK,EAAE,GAAG;YAClB,IAAI,CAAC,OAAO;gBACR;YACJ;YACA,MAAM,QAAQ,IAAI,CAAC,0CAA0C,CAAC,OAAO,MAAM;YAC3E,cAAc,MAAM,IAAI,IAAI;YAC5B,MAAM,kBAAkB;gBACpB,QAAQ,QAAQ,WAAW,IAAI;gBAC/B,YAAY,OAAO,KAAK,IAAI;YAChC;YACA,IAAI,OAAO,MAAM,OAAO,KAAK,UAAU;gBACnC,QAAQ,GAAG,CAAC;gBACZ;YACJ;YACA,8DAA8D;YAC9D,MAAM,iBAAiB;gBAAE,GAAG,eAAe;YAAC;YAC5C,IAAI,OAAO,aAAa,IAAI,MAAM;gBAC9B,eAAe,aAAa,GAAG,OAAO,aAAa;gBACnD,4DAA4D;gBAC5D,gCAAgC;gBAChC,eAAe,kBAAkB,GAAG,KAAK,kBAAkB;gBAC3D,eAAe,UAAU,GAAG,KAAK,KAAK;gBACtC,eAAe,YAAY,GAAG,KAAK,YAAY;YACnD;YACA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,eAAe,QAAQ,GAAG,OAAO,QAAQ;YAC7C;YACA,MAAM,kBAAkB,IAAI,wJAAA,CAAA,sBAAmB,CAAC;gBAC5C,SAAS;gBACT,MAAM,MAAM,OAAO;gBACnB;YACJ;YACA,MAAM;YACN,MAAM,YAAY,kBAAkB,gBAAgB,IAAI,IAAI,IAAI,iBAAiB,WAAW,WAAW,WAAW;gBAAE,OAAO;YAAgB;QAC/I;QACA,IAAI,OAAO;YACP,MAAM,oBAAoB;gBACtB,GAAI,MAAM,qBAAqB,EAAE,iBAAiB,QAAQ;oBACtD,OAAO,MAAM,qBAAqB,EAAE;gBACxC,CAAC;gBACD,GAAI,MAAM,qBAAqB,EAAE,kBAAkB,QAAQ;oBACvD,YAAY,MAAM,qBAAqB,EAAE;gBAC7C,CAAC;YACL;YACA,MAAM,qBAAqB;gBACvB,GAAI,MAAM,yBAAyB,EAAE,iBAAiB,QAAQ;oBAC1D,OAAO,MAAM,yBAAyB,EAAE;gBAC5C,CAAC;gBACD,GAAI,MAAM,yBAAyB,EAAE,qBAAqB,QAAQ;oBAC9D,WAAW,MAAM,yBAAyB,EAAE;gBAChD,CAAC;YACL;YACA,MAAM,kBAAkB,IAAI,wJAAA,CAAA,sBAAmB,CAAC;gBAC5C,SAAS,IAAI,+JAAA,CAAA,iBAAc,CAAC;oBACxB,SAAS;oBACT,mBAAmB;wBACf,OAAO;4BAAE,GAAG,KAAK;wBAAC;oBACtB;oBACA,gBAAgB;wBACZ,cAAc,MAAM,aAAa;wBACjC,eAAe,MAAM,iBAAiB;wBACtC,cAAc,MAAM,YAAY;wBAChC,GAAI,OAAO,IAAI,CAAC,mBAAmB,MAAM,GAAG,KAAK;4BAC7C,qBAAqB;wBACzB,CAAC;wBACD,GAAI,OAAO,IAAI,CAAC,oBAAoB,MAAM,GAAG,KAAK;4BAC9C,sBAAsB;wBAC1B,CAAC;oBACL;gBACJ;gBACA,MAAM;YACV;YACA,MAAM;QACV;QACA,IAAI,QAAQ,MAAM,EAAE,SAAS;YACzB,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,MAAM,oBAAoB,OAAO,EAAE,cAAc,EAAE;QAC/C,MAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC;QAC7C,MAAM,oBAAoB,QAAQ,eAAe,IAAI,QAAQ,eAAe,CAAC,IAAI,KAAK;QACtF,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACpB,IAAI;gBACA,IAAI,qBAAqB,CAAC,QAAQ,MAAM,EAAE;oBACtC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS;gBAC7D,OACK;oBACD,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS;gBAC9D;YACJ,EACA,OAAO,GAAG;gBACN,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,wBAAqB,AAAD,EAAE;gBACpC,MAAM;YACV;QACJ;IACJ;IACA,cAAc,GACd,wCAAwC,OAAO,EAAE,WAAW,EAAE;QAC1D,MAAM,eAAe,QAAQ,UAAU;QACvC,OAAQ,QAAQ,IAAI;YAChB,KAAK;gBAAa;oBACd,MAAM,YAAY,EAAE;oBACpB,MAAM,mBAAmB,EAAE;oBAC3B,KAAK,MAAM,eAAe,gBAAgB,EAAE,CAAE;wBAC1C,IAAI;4BACA,UAAU,IAAI,CAAC,CAAA,GAAA,4MAAA,CAAA,gBAAa,AAAD,EAAE,aAAa;gCAAE,UAAU;4BAAK;wBAC3D,8DAA8D;wBAClE,EACA,OAAO,GAAG;4BACN,iBAAiB,IAAI,CAAC,CAAA,GAAA,4MAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,EAAE,OAAO;wBACpE;oBACJ;oBACA,MAAM,oBAAoB;wBACtB,eAAe,QAAQ,aAAa;wBACpC,YAAY;oBAChB;oBACA,IAAI,IAAI,CAAC,oBAAoB,KAAK,WAAW;wBACzC,kBAAkB,cAAc,GAAG;oBACvC;oBACA,MAAM,oBAAoB;wBACtB,YAAY,YAAY,KAAK;wBAC7B,GAAI,YAAY,kBAAkB,GAC5B;4BACE,OAAO;gCAAE,GAAG,YAAY,KAAK;4BAAC;4BAC9B,oBAAoB,YAAY,kBAAkB;wBACtD,IACE,CAAC,CAAC;oBACZ;oBACA,IAAI,QAAQ,KAAK,EAAE;wBACf,kBAAkB,KAAK,GAAG,QAAQ,KAAK;oBAC3C;oBACA,OAAO,IAAI,+JAAA,CAAA,YAAS,CAAC;wBACjB,SAAS,QAAQ,OAAO,IAAI;wBAC5B,YAAY;wBACZ,oBAAoB;wBACpB;wBACA;wBACA,IAAI,YAAY,EAAE;oBACtB;gBACJ;YACA;gBACI,OAAO,IAAI,iKAAA,CAAA,cAAW,CAAC,QAAQ,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI;QACtE;IACJ;IACA,cAAc,GACd,2CACA,8DAA8D;IAC9D,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE;QAC7B,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,UAAU,MAAM,OAAO,IAAI;QACjC,IAAI;QACJ,IAAI,MAAM,aAAa,EAAE;YACrB,oBAAoB;gBAChB,eAAe,MAAM,aAAa;YACtC;QACJ,OACK,IAAI,MAAM,UAAU,EAAE;YACvB,oBAAoB;gBAChB,YAAY,MAAM,UAAU;YAChC;QACJ,OACK;YACD,oBAAoB,CAAC;QACzB;QACA,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,kBAAkB,cAAc,GAAG;QACvC;QACA,IAAI,MAAM,KAAK,EAAE;YACb,kBAAkB,KAAK,GAAG;gBACtB,GAAG,MAAM,KAAK;gBACd,OAAO,YAAY,OAAO,CAAC,EAAE,CAAC,KAAK;YACvC;QACJ;QACA,MAAM,oBAAoB;YAAE,OAAO;gBAAE,GAAG,YAAY,KAAK;YAAC;QAAE;QAC5D,IAAI,SAAS,QAAQ;YACjB,OAAO,IAAI,kKAAA,CAAA,oBAAiB,CAAC;gBAAE;gBAAS;YAAkB;QAC9D,OACK,IAAI,SAAS,aAAa;YAC3B,MAAM,iBAAiB,EAAE;YACzB,IAAI,MAAM,OAAO,CAAC,MAAM,UAAU,GAAG;gBACjC,KAAK,MAAM,eAAe,MAAM,UAAU,CAAE;oBACxC,eAAe,IAAI,CAAC;wBAChB,MAAM,YAAY,QAAQ,EAAE;wBAC5B,MAAM,YAAY,QAAQ,EAAE;wBAC5B,IAAI,YAAY,EAAE;wBAClB,OAAO,YAAY,KAAK;wBACxB,MAAM;oBACV;gBACJ;YACJ;YACA,OAAO,IAAI,+JAAA,CAAA,iBAAc,CAAC;gBACtB;gBACA,kBAAkB;gBAClB;gBACA,IAAI,YAAY,EAAE;gBAClB;YACJ;QACJ,OACK,IAAI,SAAS,UAAU;YACxB,OAAO,IAAI,mKAAA,CAAA,qBAAkB,CAAC;gBAAE;gBAAS;YAAkB;QAC/D,OACK,IAAI,SAAS,aAAa;YAC3B,OAAO,IAAI,mKAAA,CAAA,qBAAkB,CAAC;gBAC1B;gBACA;gBACA,mBAAmB;oBACf,iBAAiB;gBACrB;YACJ;QACJ,OACK,IAAI,SAAS,YAAY;YAC1B,OAAO,IAAI,qKAAA,CAAA,uBAAoB,CAAC;gBAC5B;gBACA;gBACA,MAAM,MAAM,IAAI;gBAChB;YACJ;QACJ,OACK,IAAI,SAAS,QAAQ;YACtB,OAAO,IAAI,iKAAA,CAAA,mBAAgB,CAAC;gBACxB;gBACA;gBACA,cAAc,MAAM,YAAY;gBAChC;YACJ;QACJ,OACK;YACD,OAAO,IAAI,iKAAA,CAAA,mBAAgB,CAAC;gBAAE;gBAAS;gBAAM;YAAkB;QACnE;IACJ;AACJ;AA2hBO,MAAM,mBAAmB;IAC5B,IAAI,uBAAuB;QACvB,OAAO;eAAI,KAAK,CAAC;YAAsB;SAAkB;IAC7D;IACA,YAAY,MAAM,CAAE;QAChB,KAAK,CAAC,UAAU,CAAC;QACjB;;;SAGC,GACD,OAAO,cAAc,CAAC,IAAI,EAAE,mBAAmB;YAC3C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,IAAI,CAAC,eAAe,GAAG,QAAQ,mBAAmB;QAClD,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAoB;QACzC,IAAI,CAAC,WAAW,GAAG,IAAI,sBAAsB;IACjD;IACA,iBAAiB,OAAO,EAAE;QACtB,MAAM,mBAAmB,SAAS,OAAO,KAAK;QAC9C,MAAM,yBAAyB,SAAS,wBAAwB,QAC5D,SAAS,QAAQ,QACjB,SAAS,cAAc,QACvB,SAAS,WAAW,QACpB,SAAS,WAAW,WAAW,QAC/B,IAAI,CAAC,SAAS,EAAE,WAAW;QAC/B,OAAO,IAAI,CAAC,eAAe,IAAI,oBAAoB;IACvD;IACA,YAAY,GACZ,MAAM,UAAU,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE;QAC3C,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU;YAChC,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU;QAC9C;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,UAAU,SAAS;IACzD;IACA,OAAO,sBAAsB,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE;QACxD,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU;YAChC,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU;YACtD;QACJ;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,UAAU,SAAS;IACrE;IACA,WAAW,MAAM,EAAE;QACf,gFAAgF;QAChF,6EAA6E;QAC7E,oFAAoF;QACpF,iFAAiF;QACjF,qFAAqF;QACrF,MAAM,uBAAuB,CAAC,KAAK;YAC/B,MAAM,cAAc,IAAI,SAAS;YACjC,MAAM,0BAA0B,IAAI,qBAAqB;YACzD,OAAO,OAAO,MAAM,CAAC,KAAK;gBACtB,WAAU,QAAQ,EAAE,OAAO,EAAE,UAAU;oBACnC,OAAO,YAAY,IAAI,CAAC,KAAK,UAAU;wBAAE,GAAG,OAAO;wBAAE,GAAG,MAAM;oBAAC,GAAG;gBACtE;gBACA,uBAAsB,QAAQ,EAAE,OAAO,EAAE,UAAU;oBAC/C,OAAO,wBAAwB,IAAI,CAAC,KAAK,UAAU;wBAAE,GAAG,OAAO;wBAAE,GAAG,MAAM;oBAAC,GAAG;gBAClF;YACJ;QACJ;QACA,IAAI,CAAC,SAAS,GAAG,qBAAqB,IAAI,CAAC,SAAS,EAAE;QACtD,IAAI,CAAC,WAAW,GAAG,qBAAqB,IAAI,CAAC,WAAW,EAAE;QAC1D,yDAAyD;QACzD,OAAO,qBAAqB,IAAI,EAAE;IACtC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2407, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/utils/headers.js"], "sourcesContent": ["const iife = (fn) => fn();\nexport function isHeaders(headers) {\n    return (typeof Headers !== \"undefined\" &&\n        headers !== null &&\n        typeof headers === \"object\" &&\n        Object.prototype.toString.call(headers) === \"[object Headers]\");\n}\nexport function normalizeHeaders(headers) {\n    const output = iife(() => {\n        // If headers is a Headers instance\n        if (isHeaders(headers)) {\n            return headers;\n        }\n        // If headers is an array of [key, value] pairs\n        else if (Array.isArray(headers)) {\n            return new Headers(headers);\n        }\n        // If headers is a NullableHeaders-like object (has 'values' property that is a Headers)\n        else if (typeof headers === \"object\" &&\n            headers !== null &&\n            \"values\" in headers &&\n            isHeaders(headers.values)) {\n            return headers.values;\n        }\n        // If headers is a plain object\n        else if (typeof headers === \"object\" && headers !== null) {\n            const entries = Object.entries(headers)\n                .filter(([, v]) => typeof v === \"string\")\n                .map(([k, v]) => [k, v]);\n            return new Headers(entries);\n        }\n        return new Headers();\n    });\n    return Object.fromEntries(output.entries());\n}\n"], "names": [], "mappings": ";;;;AAAA,MAAM,OAAO,CAAC,KAAO;AACd,SAAS,UAAU,OAAO;IAC7B,OAAQ,OAAO,YAAY,eACvB,YAAY,QACZ,OAAO,YAAY,YACnB,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa;AACpD;AACO,SAAS,iBAAiB,OAAO;IACpC,MAAM,SAAS,KAAK;QAChB,mCAAmC;QACnC,IAAI,UAAU,UAAU;YACpB,OAAO;QACX,OAEK,IAAI,MAAM,OAAO,CAAC,UAAU;YAC7B,OAAO,IAAI,QAAQ;QACvB,OAEK,IAAI,OAAO,YAAY,YACxB,YAAY,QACZ,YAAY,WACZ,UAAU,QAAQ,MAAM,GAAG;YAC3B,OAAO,QAAQ,MAAM;QACzB,OAEK,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;YACtD,MAAM,UAAU,OAAO,OAAO,CAAC,SAC1B,MAAM,CAAC,CAAC,GAAG,EAAE,GAAK,OAAO,MAAM,UAC/B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAK;oBAAC;oBAAG;iBAAE;YAC3B,OAAO,IAAI,QAAQ;QACvB;QACA,OAAO,IAAI;IACf;IACA,OAAO,OAAO,WAAW,CAAC,OAAO,OAAO;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2439, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/azure/chat_models.js"], "sourcesContent": ["import { AzureOpenAI as AzureOpenAIClient } from \"openai\";\nimport { getEnv, getEnvironmentVariable } from \"@langchain/core/utils/env\";\nimport { ChatOpenAI } from \"../chat_models.js\";\nimport { getEndpoint } from \"../utils/azure.js\";\nimport { normalizeHeaders } from \"../utils/headers.js\";\n/**\n * Azure OpenAI chat model integration.\n *\n * Setup:\n * Install `@langchain/openai` and set the following environment variables:\n *\n * ```bash\n * npm install @langchain/openai\n * export AZURE_OPENAI_API_KEY=\"your-api-key\"\n * export AZURE_OPENAI_API_DEPLOYMENT_NAME=\"your-deployment-name\"\n * export AZURE_OPENAI_API_VERSION=\"your-version\"\n * export AZURE_OPENAI_BASE_PATH=\"your-base-path\"\n * ```\n *\n * ## [Constructor args](https://api.js.langchain.com/classes/langchain_openai.AzureChatOpenAI.html#constructor)\n *\n * ## [Runtime args](https://api.js.langchain.com/interfaces/langchain_openai.ChatOpenAICallOptions.html)\n *\n * Runtime args can be passed as the second argument to any of the base runnable methods `.invoke`. `.stream`, `.batch`, etc.\n * They can also be passed via `.withConfig`, or the second arg in `.bindTools`, like shown in the examples below:\n *\n * ```typescript\n * // When calling `.withConfig`, call options should be passed via the first argument\n * const llmWithArgsBound = llm.withConfig({\n *   stop: [\"\\n\"],\n *   tools: [...],\n * });\n *\n * // When calling `.bindTools`, call options should be passed via the second argument\n * const llmWithTools = llm.bindTools(\n *   [...],\n *   {\n *     tool_choice: \"auto\",\n *   }\n * );\n * ```\n *\n * ## Examples\n *\n * <details open>\n * <summary><strong>Instantiate</strong></summary>\n *\n * ```typescript\n * import { AzureChatOpenAI } from '@langchain/openai';\n *\n * const llm = new AzureChatOpenAI({\n *   azureOpenAIApiKey: process.env.AZURE_OPENAI_API_KEY, // In Node.js defaults to process.env.AZURE_OPENAI_API_KEY\n *   azureOpenAIApiInstanceName: process.env.AZURE_OPENAI_API_INSTANCE_NAME, // In Node.js defaults to process.env.AZURE_OPENAI_API_INSTANCE_NAME\n *   azureOpenAIApiDeploymentName: process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME, // In Node.js defaults to process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME\n *   azureOpenAIApiVersion: process.env.AZURE_OPENAI_API_VERSION, // In Node.js defaults to process.env.AZURE_OPENAI_API_VERSION\n *   temperature: 0,\n *   maxTokens: undefined,\n *   timeout: undefined,\n *   maxRetries: 2,\n *   // apiKey: \"...\",\n *   // baseUrl: \"...\",\n *   // other params...\n * });\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Invoking</strong></summary>\n *\n * ```typescript\n * const input = `Translate \"I love programming\" into French.`;\n *\n * // Models also accept a list of chat messages or a formatted prompt\n * const result = await llm.invoke(input);\n * console.log(result);\n * ```\n *\n * ```txt\n * AIMessage {\n *   \"id\": \"chatcmpl-9u4Mpu44CbPjwYFkTbeoZgvzB00Tz\",\n *   \"content\": \"J'adore la programmation.\",\n *   \"response_metadata\": {\n *     \"tokenUsage\": {\n *       \"completionTokens\": 5,\n *       \"promptTokens\": 28,\n *       \"totalTokens\": 33\n *     },\n *     \"finish_reason\": \"stop\",\n *     \"system_fingerprint\": \"fp_3aa7262c27\"\n *   },\n *   \"usage_metadata\": {\n *     \"input_tokens\": 28,\n *     \"output_tokens\": 5,\n *     \"total_tokens\": 33\n *   }\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Streaming Chunks</strong></summary>\n *\n * ```typescript\n * for await (const chunk of await llm.stream(input)) {\n *   console.log(chunk);\n * }\n * ```\n *\n * ```txt\n * AIMessageChunk {\n *   \"id\": \"chatcmpl-9u4NWB7yUeHCKdLr6jP3HpaOYHTqs\",\n *   \"content\": \"\"\n * }\n * AIMessageChunk {\n *   \"content\": \"J\"\n * }\n * AIMessageChunk {\n *   \"content\": \"'adore\"\n * }\n * AIMessageChunk {\n *   \"content\": \" la\"\n * }\n * AIMessageChunk {\n *   \"content\": \" programmation\",,\n * }\n * AIMessageChunk {\n *   \"content\": \".\",,\n * }\n * AIMessageChunk {\n *   \"content\": \"\",\n *   \"response_metadata\": {\n *     \"finish_reason\": \"stop\",\n *     \"system_fingerprint\": \"fp_c9aa9c0491\"\n *   },\n * }\n * AIMessageChunk {\n *   \"content\": \"\",\n *   \"usage_metadata\": {\n *     \"input_tokens\": 28,\n *     \"output_tokens\": 5,\n *     \"total_tokens\": 33\n *   }\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Aggregate Streamed Chunks</strong></summary>\n *\n * ```typescript\n * import { AIMessageChunk } from '@langchain/core/messages';\n * import { concat } from '@langchain/core/utils/stream';\n *\n * const stream = await llm.stream(input);\n * let full: AIMessageChunk | undefined;\n * for await (const chunk of stream) {\n *   full = !full ? chunk : concat(full, chunk);\n * }\n * console.log(full);\n * ```\n *\n * ```txt\n * AIMessageChunk {\n *   \"id\": \"chatcmpl-9u4PnX6Fy7OmK46DASy0bH6cxn5Xu\",\n *   \"content\": \"J'adore la programmation.\",\n *   \"response_metadata\": {\n *     \"prompt\": 0,\n *     \"completion\": 0,\n *     \"finish_reason\": \"stop\",\n *   },\n *   \"usage_metadata\": {\n *     \"input_tokens\": 28,\n *     \"output_tokens\": 5,\n *     \"total_tokens\": 33\n *   }\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Bind tools</strong></summary>\n *\n * ```typescript\n * import { z } from 'zod';\n *\n * const GetWeather = {\n *   name: \"GetWeather\",\n *   description: \"Get the current weather in a given location\",\n *   schema: z.object({\n *     location: z.string().describe(\"The city and state, e.g. San Francisco, CA\")\n *   }),\n * }\n *\n * const GetPopulation = {\n *   name: \"GetPopulation\",\n *   description: \"Get the current population in a given location\",\n *   schema: z.object({\n *     location: z.string().describe(\"The city and state, e.g. San Francisco, CA\")\n *   }),\n * }\n *\n * const llmWithTools = llm.bindTools([GetWeather, GetPopulation]);\n * const aiMsg = await llmWithTools.invoke(\n *   \"Which city is hotter today and which is bigger: LA or NY?\"\n * );\n * console.log(aiMsg.tool_calls);\n * ```\n *\n * ```txt\n * [\n *   {\n *     name: 'GetWeather',\n *     args: { location: 'Los Angeles, CA' },\n *     type: 'tool_call',\n *     id: 'call_uPU4FiFzoKAtMxfmPnfQL6UK'\n *   },\n *   {\n *     name: 'GetWeather',\n *     args: { location: 'New York, NY' },\n *     type: 'tool_call',\n *     id: 'call_UNkEwuQsHrGYqgDQuH9nPAtX'\n *   },\n *   {\n *     name: 'GetPopulation',\n *     args: { location: 'Los Angeles, CA' },\n *     type: 'tool_call',\n *     id: 'call_kL3OXxaq9OjIKqRTpvjaCH14'\n *   },\n *   {\n *     name: 'GetPopulation',\n *     args: { location: 'New York, NY' },\n *     type: 'tool_call',\n *     id: 'call_s9KQB1UWj45LLGaEnjz0179q'\n *   }\n * ]\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Structured Output</strong></summary>\n *\n * ```typescript\n * import { z } from 'zod';\n *\n * const Joke = z.object({\n *   setup: z.string().describe(\"The setup of the joke\"),\n *   punchline: z.string().describe(\"The punchline to the joke\"),\n *   rating: z.number().nullable().describe(\"How funny the joke is, from 1 to 10\")\n * }).describe('Joke to tell user.');\n *\n * const structuredLlm = llm.withStructuredOutput(Joke, { name: \"Joke\" });\n * const jokeResult = await structuredLlm.invoke(\"Tell me a joke about cats\");\n * console.log(jokeResult);\n * ```\n *\n * ```txt\n * {\n *   setup: 'Why was the cat sitting on the computer?',\n *   punchline: 'Because it wanted to keep an eye on the mouse!',\n *   rating: 7\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>JSON Object Response Format</strong></summary>\n *\n * ```typescript\n * const jsonLlm = llm.withConfig({ response_format: { type: \"json_object\" } });\n * const jsonLlmAiMsg = await jsonLlm.invoke(\n *   \"Return a JSON object with key 'randomInts' and a value of 10 random ints in [0-99]\"\n * );\n * console.log(jsonLlmAiMsg.content);\n * ```\n *\n * ```txt\n * {\n *   \"randomInts\": [23, 87, 45, 12, 78, 34, 56, 90, 11, 67]\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Multimodal</strong></summary>\n *\n * ```typescript\n * import { HumanMessage } from '@langchain/core/messages';\n *\n * const imageUrl = \"https://example.com/image.jpg\";\n * const imageData = await fetch(imageUrl).then(res => res.arrayBuffer());\n * const base64Image = Buffer.from(imageData).toString('base64');\n *\n * const message = new HumanMessage({\n *   content: [\n *     { type: \"text\", text: \"describe the weather in this image\" },\n *     {\n *       type: \"image_url\",\n *       image_url: { url: `data:image/jpeg;base64,${base64Image}` },\n *     },\n *   ]\n * });\n *\n * const imageDescriptionAiMsg = await llm.invoke([message]);\n * console.log(imageDescriptionAiMsg.content);\n * ```\n *\n * ```txt\n * The weather in the image appears to be clear and sunny. The sky is mostly blue with a few scattered white clouds, indicating fair weather. The bright sunlight is casting shadows on the green, grassy hill, suggesting it is a pleasant day with good visibility. There are no signs of rain or stormy conditions.\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Usage Metadata</strong></summary>\n *\n * ```typescript\n * const aiMsgForMetadata = await llm.invoke(input);\n * console.log(aiMsgForMetadata.usage_metadata);\n * ```\n *\n * ```txt\n * { input_tokens: 28, output_tokens: 5, total_tokens: 33 }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Logprobs</strong></summary>\n *\n * ```typescript\n * const logprobsLlm = new ChatOpenAI({ model: \"gpt-4o-mini\", logprobs: true });\n * const aiMsgForLogprobs = await logprobsLlm.invoke(input);\n * console.log(aiMsgForLogprobs.response_metadata.logprobs);\n * ```\n *\n * ```txt\n * {\n *   content: [\n *     {\n *       token: 'J',\n *       logprob: -0.000050616763,\n *       bytes: [Array],\n *       top_logprobs: []\n *     },\n *     {\n *       token: \"'\",\n *       logprob: -0.01868736,\n *       bytes: [Array],\n *       top_logprobs: []\n *     },\n *     {\n *       token: 'ad',\n *       logprob: -0.0000030545007,\n *       bytes: [Array],\n *       top_logprobs: []\n *     },\n *     { token: 'ore', logprob: 0, bytes: [Array], top_logprobs: [] },\n *     {\n *       token: ' la',\n *       logprob: -0.515404,\n *       bytes: [Array],\n *       top_logprobs: []\n *     },\n *     {\n *       token: ' programm',\n *       logprob: -0.0000118755715,\n *       bytes: [Array],\n *       top_logprobs: []\n *     },\n *     { token: 'ation', logprob: 0, bytes: [Array], top_logprobs: [] },\n *     {\n *       token: '.',\n *       logprob: -0.0000037697225,\n *       bytes: [Array],\n *       top_logprobs: []\n *     }\n *   ],\n *   refusal: null\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Response Metadata</strong></summary>\n *\n * ```typescript\n * const aiMsgForResponseMetadata = await llm.invoke(input);\n * console.log(aiMsgForResponseMetadata.response_metadata);\n * ```\n *\n * ```txt\n * {\n *   tokenUsage: { completionTokens: 5, promptTokens: 28, totalTokens: 33 },\n *   finish_reason: 'stop',\n *   system_fingerprint: 'fp_3aa7262c27'\n * }\n * ```\n * </details>\n */\nexport class AzureChatOpenAI extends ChatOpenAI {\n    _llmType() {\n        return \"azure_openai\";\n    }\n    get lc_aliases() {\n        return {\n            ...super.lc_aliases,\n            openAIApiKey: \"openai_api_key\",\n            openAIApiVersion: \"openai_api_version\",\n            openAIBasePath: \"openai_api_base\",\n            deploymentName: \"deployment_name\",\n            azureOpenAIEndpoint: \"azure_endpoint\",\n            azureOpenAIApiVersion: \"openai_api_version\",\n            azureOpenAIBasePath: \"openai_api_base\",\n            azureOpenAIApiDeploymentName: \"deployment_name\",\n        };\n    }\n    get lc_secrets() {\n        return {\n            ...super.lc_secrets,\n            azureOpenAIApiKey: \"AZURE_OPENAI_API_KEY\",\n        };\n    }\n    get lc_serializable_keys() {\n        return [\n            ...super.lc_serializable_keys,\n            \"azureOpenAIApiKey\",\n            \"azureOpenAIApiVersion\",\n            \"azureOpenAIBasePath\",\n            \"azureOpenAIEndpoint\",\n            \"azureOpenAIApiInstanceName\",\n            \"azureOpenAIApiDeploymentName\",\n            \"deploymentName\",\n            \"openAIApiKey\",\n            \"openAIApiVersion\",\n        ];\n    }\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"azureOpenAIApiVersion\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIApiKey\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureADTokenProvider\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIApiInstanceName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIApiDeploymentName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIBasePath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIEndpoint\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.azureOpenAIApiKey =\n            fields?.azureOpenAIApiKey ??\n                fields?.openAIApiKey ??\n                fields?.apiKey ??\n                getEnvironmentVariable(\"AZURE_OPENAI_API_KEY\");\n        this.azureOpenAIApiInstanceName =\n            fields?.azureOpenAIApiInstanceName ??\n                getEnvironmentVariable(\"AZURE_OPENAI_API_INSTANCE_NAME\");\n        this.azureOpenAIApiDeploymentName =\n            fields?.azureOpenAIApiDeploymentName ??\n                fields?.deploymentName ??\n                getEnvironmentVariable(\"AZURE_OPENAI_API_DEPLOYMENT_NAME\");\n        this.azureOpenAIApiVersion =\n            fields?.azureOpenAIApiVersion ??\n                fields?.openAIApiVersion ??\n                getEnvironmentVariable(\"AZURE_OPENAI_API_VERSION\");\n        this.azureOpenAIBasePath =\n            fields?.azureOpenAIBasePath ??\n                getEnvironmentVariable(\"AZURE_OPENAI_BASE_PATH\");\n        this.azureOpenAIEndpoint =\n            fields?.azureOpenAIEndpoint ??\n                getEnvironmentVariable(\"AZURE_OPENAI_ENDPOINT\");\n        this.azureADTokenProvider = fields?.azureADTokenProvider;\n        if (!this.azureOpenAIApiKey && !this.apiKey && !this.azureADTokenProvider) {\n            throw new Error(\"Azure OpenAI API key or Token Provider not found\");\n        }\n    }\n    getLsParams(options) {\n        const params = super.getLsParams(options);\n        params.ls_provider = \"azure\";\n        return params;\n    }\n    _getClientOptions(options) {\n        if (!this.client) {\n            const openAIEndpointConfig = {\n                azureOpenAIApiDeploymentName: this.azureOpenAIApiDeploymentName,\n                azureOpenAIApiInstanceName: this.azureOpenAIApiInstanceName,\n                azureOpenAIApiKey: this.azureOpenAIApiKey,\n                azureOpenAIBasePath: this.azureOpenAIBasePath,\n                azureADTokenProvider: this.azureADTokenProvider,\n                baseURL: this.clientConfig.baseURL,\n                azureOpenAIEndpoint: this.azureOpenAIEndpoint,\n            };\n            const endpoint = getEndpoint(openAIEndpointConfig);\n            const params = {\n                ...this.clientConfig,\n                baseURL: endpoint,\n                timeout: this.timeout,\n                maxRetries: 0,\n            };\n            if (!this.azureADTokenProvider) {\n                params.apiKey = openAIEndpointConfig.azureOpenAIApiKey;\n            }\n            if (!params.baseURL) {\n                delete params.baseURL;\n            }\n            let env = getEnv();\n            if (env === \"node\" || env === \"deno\") {\n                env = `(${env}/${process.version}; ${process.platform}; ${process.arch})`;\n            }\n            const defaultHeaders = normalizeHeaders(params.defaultHeaders);\n            params.defaultHeaders = {\n                ...params.defaultHeaders,\n                \"User-Agent\": defaultHeaders[\"User-Agent\"]\n                    ? `langchainjs-azure-openai/2.0.0 (${env})${defaultHeaders[\"User-Agent\"]}`\n                    : `langchainjs-azure-openai/2.0.0 (${env})`,\n            };\n            this.client = new AzureOpenAIClient({\n                apiVersion: this.azureOpenAIApiVersion,\n                azureADTokenProvider: this.azureADTokenProvider,\n                deployment: this.azureOpenAIApiDeploymentName,\n                ...params,\n            });\n        }\n        const requestOptions = {\n            ...this.clientConfig,\n            ...options,\n        };\n        if (this.azureOpenAIApiKey) {\n            requestOptions.headers = {\n                \"api-key\": this.azureOpenAIApiKey,\n                ...requestOptions.headers,\n            };\n            requestOptions.query = {\n                \"api-version\": this.azureOpenAIApiVersion,\n                ...requestOptions.query,\n            };\n        }\n        return requestOptions;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    toJSON() {\n        const json = super.toJSON();\n        function isRecord(obj) {\n            return typeof obj === \"object\" && obj != null;\n        }\n        if (isRecord(json) && isRecord(json.kwargs)) {\n            delete json.kwargs.azure_openai_base_path;\n            delete json.kwargs.azure_openai_api_deployment_name;\n            delete json.kwargs.azure_openai_api_key;\n            delete json.kwargs.azure_openai_api_version;\n            delete json.kwargs.azure_open_ai_base_path;\n            if (!json.kwargs.azure_endpoint && this.azureOpenAIEndpoint) {\n                json.kwargs.azure_endpoint = this.azureOpenAIEndpoint;\n            }\n            if (!json.kwargs.azure_endpoint && this.azureOpenAIBasePath) {\n                const parts = this.azureOpenAIBasePath.split(\"/openai/deployments/\");\n                if (parts.length === 2 && parts[0].startsWith(\"http\")) {\n                    const [endpoint] = parts;\n                    json.kwargs.azure_endpoint = endpoint;\n                }\n            }\n            if (!json.kwargs.azure_endpoint && this.azureOpenAIApiInstanceName) {\n                json.kwargs.azure_endpoint = `https://${this.azureOpenAIApiInstanceName}.openai.azure.com/`;\n            }\n            if (!json.kwargs.deployment_name && this.azureOpenAIApiDeploymentName) {\n                json.kwargs.deployment_name = this.azureOpenAIApiDeploymentName;\n            }\n            if (!json.kwargs.deployment_name && this.azureOpenAIBasePath) {\n                const parts = this.azureOpenAIBasePath.split(\"/openai/deployments/\");\n                if (parts.length === 2) {\n                    const [, deployment] = parts;\n                    json.kwargs.deployment_name = deployment;\n                }\n            }\n            if (json.kwargs.azure_endpoint &&\n                json.kwargs.deployment_name &&\n                json.kwargs.openai_api_base) {\n                delete json.kwargs.openai_api_base;\n            }\n            if (json.kwargs.azure_openai_api_instance_name &&\n                json.kwargs.azure_endpoint) {\n                delete json.kwargs.azure_openai_api_instance_name;\n            }\n        }\n        return json;\n    }\n    withStructuredOutput(outputSchema, config) {\n        const ensuredConfig = { ...config };\n        // Not all Azure gpt-4o deployments models support jsonSchema yet\n        if (this.model.startsWith(\"gpt-4o\")) {\n            if (ensuredConfig?.method === undefined) {\n                ensuredConfig.method = \"functionCalling\";\n            }\n        }\n        return super.withStructuredOutput(outputSchema, ensuredConfig);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AA6ZO,MAAM,wBAAwB,8JAAA,CAAA,aAAU;IAC3C,WAAW;QACP,OAAO;IACX;IACA,IAAI,aAAa;QACb,OAAO;YACH,GAAG,KAAK,CAAC,UAAU;YACnB,cAAc;YACd,kBAAkB;YAClB,gBAAgB;YAChB,gBAAgB;YAChB,qBAAqB;YACrB,uBAAuB;YACvB,qBAAqB;YACrB,8BAA8B;QAClC;IACJ;IACA,IAAI,aAAa;QACb,OAAO;YACH,GAAG,KAAK,CAAC,UAAU;YACnB,mBAAmB;QACvB;IACJ;IACA,IAAI,uBAAuB;QACvB,OAAO;eACA,KAAK,CAAC;YACT;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH;IACL;IACA,YAAY,MAAM,CAAE;QAChB,KAAK,CAAC;QACN,OAAO,cAAc,CAAC,IAAI,EAAE,yBAAyB;YACjD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,qBAAqB;YAC7C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,wBAAwB;YAChD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,8BAA8B;YACtD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gCAAgC;YACxD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,uBAAuB;YAC/C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,uBAAuB;YAC/C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,IAAI,CAAC,iBAAiB,GAClB,QAAQ,qBACJ,QAAQ,gBACR,QAAQ,UACR,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,0BAA0B,GAC3B,QAAQ,8BACJ,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,4BAA4B,GAC7B,QAAQ,gCACJ,QAAQ,kBACR,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,qBAAqB,GACtB,QAAQ,yBACJ,QAAQ,oBACR,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,mBAAmB,GACpB,QAAQ,uBACJ,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,mBAAmB,GACpB,QAAQ,uBACJ,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,oBAAoB,GAAG,QAAQ;QACpC,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACvE,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,YAAY,OAAO,EAAE;QACjB,MAAM,SAAS,KAAK,CAAC,YAAY;QACjC,OAAO,WAAW,GAAG;QACrB,OAAO;IACX;IACA,kBAAkB,OAAO,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,MAAM,uBAAuB;gBACzB,8BAA8B,IAAI,CAAC,4BAA4B;gBAC/D,4BAA4B,IAAI,CAAC,0BAA0B;gBAC3D,mBAAmB,IAAI,CAAC,iBAAiB;gBACzC,qBAAqB,IAAI,CAAC,mBAAmB;gBAC7C,sBAAsB,IAAI,CAAC,oBAAoB;gBAC/C,SAAS,IAAI,CAAC,YAAY,CAAC,OAAO;gBAClC,qBAAqB,IAAI,CAAC,mBAAmB;YACjD;YACA,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,MAAM,SAAS;gBACX,GAAG,IAAI,CAAC,YAAY;gBACpB,SAAS;gBACT,SAAS,IAAI,CAAC,OAAO;gBACrB,YAAY;YAChB;YACA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC5B,OAAO,MAAM,GAAG,qBAAqB,iBAAiB;YAC1D;YACA,IAAI,CAAC,OAAO,OAAO,EAAE;gBACjB,OAAO,OAAO,OAAO;YACzB;YACA,IAAI,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;YACf,IAAI,QAAQ,UAAU,QAAQ,QAAQ;gBAClC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,EAAE,QAAQ,QAAQ,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,CAAC,CAAC;YAC7E;YACA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,cAAc;YAC7D,OAAO,cAAc,GAAG;gBACpB,GAAG,OAAO,cAAc;gBACxB,cAAc,cAAc,CAAC,aAAa,GACpC,CAAC,gCAAgC,EAAE,IAAI,CAAC,EAAE,cAAc,CAAC,aAAa,EAAE,GACxE,CAAC,gCAAgC,EAAE,IAAI,CAAC,CAAC;YACnD;YACA,IAAI,CAAC,MAAM,GAAG,IAAI,2KAAA,CAAA,cAAiB,CAAC;gBAChC,YAAY,IAAI,CAAC,qBAAqB;gBACtC,sBAAsB,IAAI,CAAC,oBAAoB;gBAC/C,YAAY,IAAI,CAAC,4BAA4B;gBAC7C,GAAG,MAAM;YACb;QACJ;QACA,MAAM,iBAAiB;YACnB,GAAG,IAAI,CAAC,YAAY;YACpB,GAAG,OAAO;QACd;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,eAAe,OAAO,GAAG;gBACrB,WAAW,IAAI,CAAC,iBAAiB;gBACjC,GAAG,eAAe,OAAO;YAC7B;YACA,eAAe,KAAK,GAAG;gBACnB,eAAe,IAAI,CAAC,qBAAqB;gBACzC,GAAG,eAAe,KAAK;YAC3B;QACJ;QACA,OAAO;IACX;IACA,8DAA8D;IAC9D,SAAS;QACL,MAAM,OAAO,KAAK,CAAC;QACnB,SAAS,SAAS,GAAG;YACjB,OAAO,OAAO,QAAQ,YAAY,OAAO;QAC7C;QACA,IAAI,SAAS,SAAS,SAAS,KAAK,MAAM,GAAG;YACzC,OAAO,KAAK,MAAM,CAAC,sBAAsB;YACzC,OAAO,KAAK,MAAM,CAAC,gCAAgC;YACnD,OAAO,KAAK,MAAM,CAAC,oBAAoB;YACvC,OAAO,KAAK,MAAM,CAAC,wBAAwB;YAC3C,OAAO,KAAK,MAAM,CAAC,uBAAuB;YAC1C,IAAI,CAAC,KAAK,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBACzD,KAAK,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB;YACzD;YACA,IAAI,CAAC,KAAK,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBACzD,MAAM,QAAQ,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;gBAC7C,IAAI,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS;oBACnD,MAAM,CAAC,SAAS,GAAG;oBACnB,KAAK,MAAM,CAAC,cAAc,GAAG;gBACjC;YACJ;YACA,IAAI,CAAC,KAAK,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,0BAA0B,EAAE;gBAChE,KAAK,MAAM,CAAC,cAAc,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,CAAC;YAC/F;YACA,IAAI,CAAC,KAAK,MAAM,CAAC,eAAe,IAAI,IAAI,CAAC,4BAA4B,EAAE;gBACnE,KAAK,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,4BAA4B;YACnE;YACA,IAAI,CAAC,KAAK,MAAM,CAAC,eAAe,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1D,MAAM,QAAQ,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;gBAC7C,IAAI,MAAM,MAAM,KAAK,GAAG;oBACpB,MAAM,GAAG,WAAW,GAAG;oBACvB,KAAK,MAAM,CAAC,eAAe,GAAG;gBAClC;YACJ;YACA,IAAI,KAAK,MAAM,CAAC,cAAc,IAC1B,KAAK,MAAM,CAAC,eAAe,IAC3B,KAAK,MAAM,CAAC,eAAe,EAAE;gBAC7B,OAAO,KAAK,MAAM,CAAC,eAAe;YACtC;YACA,IAAI,KAAK,MAAM,CAAC,8BAA8B,IAC1C,KAAK,MAAM,CAAC,cAAc,EAAE;gBAC5B,OAAO,KAAK,MAAM,CAAC,8BAA8B;YACrD;QACJ;QACA,OAAO;IACX;IACA,qBAAqB,YAAY,EAAE,MAAM,EAAE;QACvC,MAAM,gBAAgB;YAAE,GAAG,MAAM;QAAC;QAClC,iEAAiE;QACjE,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW;YACjC,IAAI,eAAe,WAAW,WAAW;gBACrC,cAAc,MAAM,GAAG;YAC3B;QACJ;QACA,OAAO,KAAK,CAAC,qBAAqB,cAAc;IACpD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2668, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/llms.js"], "sourcesContent": ["import { OpenAI as OpenAIClient } from \"openai\";\nimport { calculateMaxTokens } from \"@langchain/core/language_models/base\";\nimport { GenerationChunk } from \"@langchain/core/outputs\";\nimport { getEnvironmentVariable } from \"@langchain/core/utils/env\";\nimport { BaseLLM, } from \"@langchain/core/language_models/llms\";\nimport { chunkArray } from \"@langchain/core/utils/chunk_array\";\nimport { getEndpoint } from \"./utils/azure.js\";\nimport { wrapOpenAIClientError } from \"./utils/openai.js\";\n/**\n * Wrapper around OpenAI large language models.\n *\n * To use you should have the `openai` package installed, with the\n * `OPENAI_API_KEY` environment variable set.\n *\n * To use with Azure, import the `AzureOpenAI` class.\n *\n * @remarks\n * Any parameters that are valid to be passed to {@link\n * https://platform.openai.com/docs/api-reference/completions/create |\n * `openai.createCompletion`} can be passed through {@link modelKwargs}, even\n * if not explicitly available on this class.\n * @example\n * ```typescript\n * const model = new OpenAI({\n *   modelName: \"gpt-4\",\n *   temperature: 0.7,\n *   maxTokens: 1000,\n *   maxRetries: 5,\n * });\n *\n * const res = await model.invoke(\n *   \"Question: What would be a good company name for a company that makes colorful socks?\\nAnswer:\"\n * );\n * console.log({ res });\n * ```\n */\nexport class OpenAI extends BaseLLM {\n    static lc_name() {\n        return \"OpenAI\";\n    }\n    get callKeys() {\n        return [...super.callKeys, \"options\"];\n    }\n    get lc_secrets() {\n        return {\n            openAIApiKey: \"OPENAI_API_KEY\",\n            apiKey: \"OPENAI_API_KEY\",\n            organization: \"OPENAI_ORGANIZATION\",\n        };\n    }\n    get lc_aliases() {\n        return {\n            modelName: \"model\",\n            openAIApiKey: \"openai_api_key\",\n            apiKey: \"openai_api_key\",\n        };\n    }\n    constructor(fields) {\n        super(fields ?? {});\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"temperature\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"maxTokens\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"topP\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"frequencyPenalty\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"presencePenalty\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"n\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 1\n        });\n        Object.defineProperty(this, \"bestOf\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"logitBias\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"gpt-3.5-turbo-instruct\"\n        });\n        /** @deprecated Use \"model\" instead */\n        Object.defineProperty(this, \"modelName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"modelKwargs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"batchSize\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 20\n        });\n        Object.defineProperty(this, \"timeout\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"stop\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"stopSequences\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"user\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"streaming\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        Object.defineProperty(this, \"openAIApiKey\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"apiKey\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"organization\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"clientConfig\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.openAIApiKey =\n            fields?.apiKey ??\n                fields?.openAIApiKey ??\n                getEnvironmentVariable(\"OPENAI_API_KEY\");\n        this.apiKey = this.openAIApiKey;\n        this.organization =\n            fields?.configuration?.organization ??\n                getEnvironmentVariable(\"OPENAI_ORGANIZATION\");\n        this.model = fields?.model ?? fields?.modelName ?? this.model;\n        if ((this.model?.startsWith(\"gpt-3.5-turbo\") ||\n            this.model?.startsWith(\"gpt-4\") ||\n            this.model?.startsWith(\"o1\")) &&\n            !this.model?.includes(\"-instruct\")) {\n            throw new Error([\n                `Your chosen OpenAI model, \"${this.model}\", is a chat model and not a text-in/text-out LLM.`,\n                `Passing it into the \"OpenAI\" class is no longer supported.`,\n                `Please use the \"ChatOpenAI\" class instead.`,\n                \"\",\n                `See this page for more information:`,\n                \"|\",\n                `└> https://js.langchain.com/docs/integrations/chat/openai`,\n            ].join(\"\\n\"));\n        }\n        this.modelName = this.model;\n        this.modelKwargs = fields?.modelKwargs ?? {};\n        this.batchSize = fields?.batchSize ?? this.batchSize;\n        this.timeout = fields?.timeout;\n        this.temperature = fields?.temperature ?? this.temperature;\n        this.maxTokens = fields?.maxTokens ?? this.maxTokens;\n        this.topP = fields?.topP ?? this.topP;\n        this.frequencyPenalty = fields?.frequencyPenalty ?? this.frequencyPenalty;\n        this.presencePenalty = fields?.presencePenalty ?? this.presencePenalty;\n        this.n = fields?.n ?? this.n;\n        this.bestOf = fields?.bestOf ?? this.bestOf;\n        this.logitBias = fields?.logitBias;\n        this.stop = fields?.stopSequences ?? fields?.stop;\n        this.stopSequences = this.stop;\n        this.user = fields?.user;\n        this.streaming = fields?.streaming ?? false;\n        if (this.streaming && this.bestOf && this.bestOf > 1) {\n            throw new Error(\"Cannot stream results when bestOf > 1\");\n        }\n        this.clientConfig = {\n            apiKey: this.apiKey,\n            organization: this.organization,\n            dangerouslyAllowBrowser: true,\n            ...fields?.configuration,\n        };\n    }\n    /**\n     * Get the parameters used to invoke the model\n     */\n    invocationParams(options) {\n        return {\n            model: this.model,\n            temperature: this.temperature,\n            max_tokens: this.maxTokens,\n            top_p: this.topP,\n            frequency_penalty: this.frequencyPenalty,\n            presence_penalty: this.presencePenalty,\n            n: this.n,\n            best_of: this.bestOf,\n            logit_bias: this.logitBias,\n            stop: options?.stop ?? this.stopSequences,\n            user: this.user,\n            stream: this.streaming,\n            ...this.modelKwargs,\n        };\n    }\n    /** @ignore */\n    _identifyingParams() {\n        return {\n            model_name: this.model,\n            ...this.invocationParams(),\n            ...this.clientConfig,\n        };\n    }\n    /**\n     * Get the identifying parameters for the model\n     */\n    identifyingParams() {\n        return this._identifyingParams();\n    }\n    /**\n     * Call out to OpenAI's endpoint with k unique prompts\n     *\n     * @param [prompts] - The prompts to pass into the model.\n     * @param [options] - Optional list of stop words to use when generating.\n     * @param [runManager] - Optional callback manager to use when generating.\n     *\n     * @returns The full LLM output.\n     *\n     * @example\n     * ```ts\n     * import { OpenAI } from \"langchain/llms/openai\";\n     * const openai = new OpenAI();\n     * const response = await openai.generate([\"Tell me a joke.\"]);\n     * ```\n     */\n    async _generate(prompts, options, runManager) {\n        const subPrompts = chunkArray(prompts, this.batchSize);\n        const choices = [];\n        const tokenUsage = {};\n        const params = this.invocationParams(options);\n        if (params.max_tokens === -1) {\n            if (prompts.length !== 1) {\n                throw new Error(\"max_tokens set to -1 not supported for multiple inputs\");\n            }\n            params.max_tokens = await calculateMaxTokens({\n                prompt: prompts[0],\n                // Cast here to allow for other models that may not fit the union\n                modelName: this.model,\n            });\n        }\n        for (let i = 0; i < subPrompts.length; i += 1) {\n            const data = params.stream\n                ? await (async () => {\n                    const choices = [];\n                    let response;\n                    const stream = await this.completionWithRetry({\n                        ...params,\n                        stream: true,\n                        prompt: subPrompts[i],\n                    }, options);\n                    for await (const message of stream) {\n                        // on the first message set the response properties\n                        if (!response) {\n                            response = {\n                                id: message.id,\n                                object: message.object,\n                                created: message.created,\n                                model: message.model,\n                            };\n                        }\n                        // on all messages, update choice\n                        for (const part of message.choices) {\n                            if (!choices[part.index]) {\n                                choices[part.index] = part;\n                            }\n                            else {\n                                const choice = choices[part.index];\n                                choice.text += part.text;\n                                choice.finish_reason = part.finish_reason;\n                                choice.logprobs = part.logprobs;\n                            }\n                            void runManager?.handleLLMNewToken(part.text, {\n                                prompt: Math.floor(part.index / this.n),\n                                completion: part.index % this.n,\n                            });\n                        }\n                    }\n                    if (options.signal?.aborted) {\n                        throw new Error(\"AbortError\");\n                    }\n                    return { ...response, choices };\n                })()\n                : await this.completionWithRetry({\n                    ...params,\n                    stream: false,\n                    prompt: subPrompts[i],\n                }, {\n                    signal: options.signal,\n                    ...options.options,\n                });\n            choices.push(...data.choices);\n            const { completion_tokens: completionTokens, prompt_tokens: promptTokens, total_tokens: totalTokens, } = data.usage\n                ? data.usage\n                : {\n                    completion_tokens: undefined,\n                    prompt_tokens: undefined,\n                    total_tokens: undefined,\n                };\n            if (completionTokens) {\n                tokenUsage.completionTokens =\n                    (tokenUsage.completionTokens ?? 0) + completionTokens;\n            }\n            if (promptTokens) {\n                tokenUsage.promptTokens = (tokenUsage.promptTokens ?? 0) + promptTokens;\n            }\n            if (totalTokens) {\n                tokenUsage.totalTokens = (tokenUsage.totalTokens ?? 0) + totalTokens;\n            }\n        }\n        const generations = chunkArray(choices, this.n).map((promptChoices) => promptChoices.map((choice) => ({\n            text: choice.text ?? \"\",\n            generationInfo: {\n                finishReason: choice.finish_reason,\n                logprobs: choice.logprobs,\n            },\n        })));\n        return {\n            generations,\n            llmOutput: { tokenUsage },\n        };\n    }\n    // TODO(jacoblee): Refactor with _generate(..., {stream: true}) implementation?\n    async *_streamResponseChunks(input, options, runManager) {\n        const params = {\n            ...this.invocationParams(options),\n            prompt: input,\n            stream: true,\n        };\n        const stream = await this.completionWithRetry(params, options);\n        for await (const data of stream) {\n            const choice = data?.choices[0];\n            if (!choice) {\n                continue;\n            }\n            const chunk = new GenerationChunk({\n                text: choice.text,\n                generationInfo: {\n                    finishReason: choice.finish_reason,\n                },\n            });\n            yield chunk;\n            // eslint-disable-next-line no-void\n            void runManager?.handleLLMNewToken(chunk.text ?? \"\");\n        }\n        if (options.signal?.aborted) {\n            throw new Error(\"AbortError\");\n        }\n    }\n    async completionWithRetry(request, options) {\n        const requestOptions = this._getClientOptions(options);\n        return this.caller.call(async () => {\n            try {\n                const res = await this.client.completions.create(request, requestOptions);\n                return res;\n            }\n            catch (e) {\n                const error = wrapOpenAIClientError(e);\n                throw error;\n            }\n        });\n    }\n    /**\n     * Calls the OpenAI API with retry logic in case of failures.\n     * @param request The request to send to the OpenAI API.\n     * @param options Optional configuration for the API call.\n     * @returns The response from the OpenAI API.\n     */\n    _getClientOptions(options) {\n        if (!this.client) {\n            const openAIEndpointConfig = {\n                baseURL: this.clientConfig.baseURL,\n            };\n            const endpoint = getEndpoint(openAIEndpointConfig);\n            const params = {\n                ...this.clientConfig,\n                baseURL: endpoint,\n                timeout: this.timeout,\n                maxRetries: 0,\n            };\n            if (!params.baseURL) {\n                delete params.baseURL;\n            }\n            this.client = new OpenAIClient(params);\n        }\n        const requestOptions = {\n            ...this.clientConfig,\n            ...options,\n        };\n        return requestOptions;\n    }\n    _llmType() {\n        return \"openai\";\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;;;;;;;;;AA6BO,MAAM,eAAe,wKAAA,CAAA,UAAO;IAC/B,OAAO,UAAU;QACb,OAAO;IACX;IACA,IAAI,WAAW;QACX,OAAO;eAAI,KAAK,CAAC;YAAU;SAAU;IACzC;IACA,IAAI,aAAa;QACb,OAAO;YACH,cAAc;YACd,QAAQ;YACR,cAAc;QAClB;IACJ;IACA,IAAI,aAAa;QACb,OAAO;YACH,WAAW;YACX,cAAc;YACd,QAAQ;QACZ;IACJ;IACA,YAAY,MAAM,CAAE;QAChB,KAAK,CAAC,UAAU,CAAC;QACjB,OAAO,cAAc,CAAC,IAAI,EAAE,mBAAmB;YAC3C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,oBAAoB;YAC5C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,mBAAmB;YAC3C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;YAC7B,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,oCAAoC,GACpC,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;YACnC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,iBAAiB;YACzC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,IAAI,CAAC,YAAY,GACb,QAAQ,UACJ,QAAQ,gBACR,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY;QAC/B,IAAI,CAAC,YAAY,GACb,QAAQ,eAAe,gBACnB,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,KAAK,GAAG,QAAQ,SAAS,QAAQ,aAAa,IAAI,CAAC,KAAK;QAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,oBACxB,IAAI,CAAC,KAAK,EAAE,WAAW,YACvB,IAAI,CAAC,KAAK,EAAE,WAAW,KAAK,KAC5B,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,cAAc;YACpC,MAAM,IAAI,MAAM;gBACZ,CAAC,2BAA2B,EAAE,IAAI,CAAC,KAAK,CAAC,kDAAkD,CAAC;gBAC5F,CAAC,0DAA0D,CAAC;gBAC5D,CAAC,0CAA0C,CAAC;gBAC5C;gBACA,CAAC,mCAAmC,CAAC;gBACrC;gBACA,CAAC,yDAAyD,CAAC;aAC9D,CAAC,IAAI,CAAC;QACX;QACA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK;QAC3B,IAAI,CAAC,WAAW,GAAG,QAAQ,eAAe,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,QAAQ,aAAa,IAAI,CAAC,SAAS;QACpD,IAAI,CAAC,OAAO,GAAG,QAAQ;QACvB,IAAI,CAAC,WAAW,GAAG,QAAQ,eAAe,IAAI,CAAC,WAAW;QAC1D,IAAI,CAAC,SAAS,GAAG,QAAQ,aAAa,IAAI,CAAC,SAAS;QACpD,IAAI,CAAC,IAAI,GAAG,QAAQ,QAAQ,IAAI,CAAC,IAAI;QACrC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,oBAAoB,IAAI,CAAC,gBAAgB;QACzE,IAAI,CAAC,eAAe,GAAG,QAAQ,mBAAmB,IAAI,CAAC,eAAe;QACtE,IAAI,CAAC,CAAC,GAAG,QAAQ,KAAK,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,QAAQ,UAAU,IAAI,CAAC,MAAM;QAC3C,IAAI,CAAC,SAAS,GAAG,QAAQ;QACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,iBAAiB,QAAQ;QAC7C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI;QAC9B,IAAI,CAAC,IAAI,GAAG,QAAQ;QACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,aAAa;QACtC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG;YAClD,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,YAAY,GAAG;YAChB,QAAQ,IAAI,CAAC,MAAM;YACnB,cAAc,IAAI,CAAC,YAAY;YAC/B,yBAAyB;YACzB,GAAG,QAAQ,aAAa;QAC5B;IACJ;IACA;;KAEC,GACD,iBAAiB,OAAO,EAAE;QACtB,OAAO;YACH,OAAO,IAAI,CAAC,KAAK;YACjB,aAAa,IAAI,CAAC,WAAW;YAC7B,YAAY,IAAI,CAAC,SAAS;YAC1B,OAAO,IAAI,CAAC,IAAI;YAChB,mBAAmB,IAAI,CAAC,gBAAgB;YACxC,kBAAkB,IAAI,CAAC,eAAe;YACtC,GAAG,IAAI,CAAC,CAAC;YACT,SAAS,IAAI,CAAC,MAAM;YACpB,YAAY,IAAI,CAAC,SAAS;YAC1B,MAAM,SAAS,QAAQ,IAAI,CAAC,aAAa;YACzC,MAAM,IAAI,CAAC,IAAI;YACf,QAAQ,IAAI,CAAC,SAAS;YACtB,GAAG,IAAI,CAAC,WAAW;QACvB;IACJ;IACA,YAAY,GACZ,qBAAqB;QACjB,OAAO;YACH,YAAY,IAAI,CAAC,KAAK;YACtB,GAAG,IAAI,CAAC,gBAAgB,EAAE;YAC1B,GAAG,IAAI,CAAC,YAAY;QACxB;IACJ;IACA;;KAEC,GACD,oBAAoB;QAChB,OAAO,IAAI,CAAC,kBAAkB;IAClC;IACA;;;;;;;;;;;;;;;KAeC,GACD,MAAM,UAAU,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE;QAC1C,MAAM,aAAa,CAAA,GAAA,qKAAA,CAAA,aAAU,AAAD,EAAE,SAAS,IAAI,CAAC,SAAS;QACrD,MAAM,UAAU,EAAE;QAClB,MAAM,aAAa,CAAC;QACpB,MAAM,SAAS,IAAI,CAAC,gBAAgB,CAAC;QACrC,IAAI,OAAO,UAAU,KAAK,CAAC,GAAG;YAC1B,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACtB,MAAM,IAAI,MAAM;YACpB;YACA,OAAO,UAAU,GAAG,MAAM,CAAA,GAAA,wKAAA,CAAA,qBAAkB,AAAD,EAAE;gBACzC,QAAQ,OAAO,CAAC,EAAE;gBAClB,iEAAiE;gBACjE,WAAW,IAAI,CAAC,KAAK;YACzB;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;YAC3C,MAAM,OAAO,OAAO,MAAM,GACpB,MAAM,CAAC;gBACL,MAAM,UAAU,EAAE;gBAClB,IAAI;gBACJ,MAAM,SAAS,MAAM,IAAI,CAAC,mBAAmB,CAAC;oBAC1C,GAAG,MAAM;oBACT,QAAQ;oBACR,QAAQ,UAAU,CAAC,EAAE;gBACzB,GAAG;gBACH,WAAW,MAAM,WAAW,OAAQ;oBAChC,mDAAmD;oBACnD,IAAI,CAAC,UAAU;wBACX,WAAW;4BACP,IAAI,QAAQ,EAAE;4BACd,QAAQ,QAAQ,MAAM;4BACtB,SAAS,QAAQ,OAAO;4BACxB,OAAO,QAAQ,KAAK;wBACxB;oBACJ;oBACA,iCAAiC;oBACjC,KAAK,MAAM,QAAQ,QAAQ,OAAO,CAAE;wBAChC,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE;4BACtB,OAAO,CAAC,KAAK,KAAK,CAAC,GAAG;wBAC1B,OACK;4BACD,MAAM,SAAS,OAAO,CAAC,KAAK,KAAK,CAAC;4BAClC,OAAO,IAAI,IAAI,KAAK,IAAI;4BACxB,OAAO,aAAa,GAAG,KAAK,aAAa;4BACzC,OAAO,QAAQ,GAAG,KAAK,QAAQ;wBACnC;wBACA,KAAK,YAAY,kBAAkB,KAAK,IAAI,EAAE;4BAC1C,QAAQ,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,CAAC;4BACtC,YAAY,KAAK,KAAK,GAAG,IAAI,CAAC,CAAC;wBACnC;oBACJ;gBACJ;gBACA,IAAI,QAAQ,MAAM,EAAE,SAAS;oBACzB,MAAM,IAAI,MAAM;gBACpB;gBACA,OAAO;oBAAE,GAAG,QAAQ;oBAAE;gBAAQ;YAClC,CAAC,MACC,MAAM,IAAI,CAAC,mBAAmB,CAAC;gBAC7B,GAAG,MAAM;gBACT,QAAQ;gBACR,QAAQ,UAAU,CAAC,EAAE;YACzB,GAAG;gBACC,QAAQ,QAAQ,MAAM;gBACtB,GAAG,QAAQ,OAAO;YACtB;YACJ,QAAQ,IAAI,IAAI,KAAK,OAAO;YAC5B,MAAM,EAAE,mBAAmB,gBAAgB,EAAE,eAAe,YAAY,EAAE,cAAc,WAAW,EAAG,GAAG,KAAK,KAAK,GAC7G,KAAK,KAAK,GACV;gBACE,mBAAmB;gBACnB,eAAe;gBACf,cAAc;YAClB;YACJ,IAAI,kBAAkB;gBAClB,WAAW,gBAAgB,GACvB,CAAC,WAAW,gBAAgB,IAAI,CAAC,IAAI;YAC7C;YACA,IAAI,cAAc;gBACd,WAAW,YAAY,GAAG,CAAC,WAAW,YAAY,IAAI,CAAC,IAAI;YAC/D;YACA,IAAI,aAAa;gBACb,WAAW,WAAW,GAAG,CAAC,WAAW,WAAW,IAAI,CAAC,IAAI;YAC7D;QACJ;QACA,MAAM,cAAc,CAAA,GAAA,qKAAA,CAAA,aAAU,AAAD,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,gBAAkB,cAAc,GAAG,CAAC,CAAC,SAAW,CAAC;oBAClG,MAAM,OAAO,IAAI,IAAI;oBACrB,gBAAgB;wBACZ,cAAc,OAAO,aAAa;wBAClC,UAAU,OAAO,QAAQ;oBAC7B;gBACJ,CAAC;QACD,OAAO;YACH;YACA,WAAW;gBAAE;YAAW;QAC5B;IACJ;IACA,+EAA+E;IAC/E,OAAO,sBAAsB,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE;QACrD,MAAM,SAAS;YACX,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ;YACjC,QAAQ;YACR,QAAQ;QACZ;QACA,MAAM,SAAS,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ;QACtD,WAAW,MAAM,QAAQ,OAAQ;YAC7B,MAAM,SAAS,MAAM,OAAO,CAAC,EAAE;YAC/B,IAAI,CAAC,QAAQ;gBACT;YACJ;YACA,MAAM,QAAQ,IAAI,wJAAA,CAAA,kBAAe,CAAC;gBAC9B,MAAM,OAAO,IAAI;gBACjB,gBAAgB;oBACZ,cAAc,OAAO,aAAa;gBACtC;YACJ;YACA,MAAM;YACN,mCAAmC;YACnC,KAAK,YAAY,kBAAkB,MAAM,IAAI,IAAI;QACrD;QACA,IAAI,QAAQ,MAAM,EAAE,SAAS;YACzB,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,MAAM,oBAAoB,OAAO,EAAE,OAAO,EAAE;QACxC,MAAM,iBAAiB,IAAI,CAAC,iBAAiB,CAAC;QAC9C,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACpB,IAAI;gBACA,MAAM,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS;gBAC1D,OAAO;YACX,EACA,OAAO,GAAG;gBACN,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,wBAAqB,AAAD,EAAE;gBACpC,MAAM;YACV;QACJ;IACJ;IACA;;;;;KAKC,GACD,kBAAkB,OAAO,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,MAAM,uBAAuB;gBACzB,SAAS,IAAI,CAAC,YAAY,CAAC,OAAO;YACtC;YACA,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,MAAM,SAAS;gBACX,GAAG,IAAI,CAAC,YAAY;gBACpB,SAAS;gBACT,SAAS,IAAI,CAAC,OAAO;gBACrB,YAAY;YAChB;YACA,IAAI,CAAC,OAAO,OAAO,EAAE;gBACjB,OAAO,OAAO,OAAO;YACzB;YACA,IAAI,CAAC,MAAM,GAAG,IAAI,4KAAA,CAAA,SAAY,CAAC;QACnC;QACA,MAAM,iBAAiB;YACnB,GAAG,IAAI,CAAC,YAAY;YACpB,GAAG,OAAO;QACd;QACA,OAAO;IACX;IACA,WAAW;QACP,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3115, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/azure/llms.js"], "sourcesContent": ["import { AzureOpenAI as AzureOpenAIClient } from \"openai\";\nimport { getEnvironmentVariable } from \"@langchain/core/utils/env\";\nimport { OpenAI } from \"../llms.js\";\nimport { getEndpoint } from \"../utils/azure.js\";\nimport { normalizeHeaders } from \"../utils/headers.js\";\nexport class AzureOpenAI extends OpenAI {\n    get lc_aliases() {\n        return {\n            ...super.lc_aliases,\n            openAIApiKey: \"openai_api_key\",\n            openAIApiVersion: \"openai_api_version\",\n            openAIBasePath: \"openai_api_base\",\n            deploymentName: \"deployment_name\",\n            azureOpenAIEndpoint: \"azure_endpoint\",\n            azureOpenAIApiVersion: \"openai_api_version\",\n            azureOpenAIBasePath: \"openai_api_base\",\n            azureOpenAIApiDeploymentName: \"deployment_name\",\n        };\n    }\n    get lc_secrets() {\n        return {\n            ...super.lc_secrets,\n            azureOpenAIApiKey: \"AZURE_OPENAI_API_KEY\",\n        };\n    }\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"azureOpenAIApiVersion\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIApiKey\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureADTokenProvider\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIApiInstanceName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIApiDeploymentName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIBasePath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIEndpoint\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.azureOpenAIApiDeploymentName =\n            (fields?.azureOpenAIApiCompletionsDeploymentName ||\n                fields?.azureOpenAIApiDeploymentName) ??\n                (getEnvironmentVariable(\"AZURE_OPENAI_API_COMPLETIONS_DEPLOYMENT_NAME\") ||\n                    getEnvironmentVariable(\"AZURE_OPENAI_API_DEPLOYMENT_NAME\"));\n        this.azureOpenAIApiKey =\n            fields?.azureOpenAIApiKey ??\n                fields?.openAIApiKey ??\n                fields?.apiKey ??\n                getEnvironmentVariable(\"AZURE_OPENAI_API_KEY\");\n        this.azureOpenAIApiInstanceName =\n            fields?.azureOpenAIApiInstanceName ??\n                getEnvironmentVariable(\"AZURE_OPENAI_API_INSTANCE_NAME\");\n        this.azureOpenAIApiVersion =\n            fields?.azureOpenAIApiVersion ??\n                fields?.openAIApiVersion ??\n                getEnvironmentVariable(\"AZURE_OPENAI_API_VERSION\");\n        this.azureOpenAIBasePath =\n            fields?.azureOpenAIBasePath ??\n                getEnvironmentVariable(\"AZURE_OPENAI_BASE_PATH\");\n        this.azureOpenAIEndpoint =\n            fields?.azureOpenAIEndpoint ??\n                getEnvironmentVariable(\"AZURE_OPENAI_ENDPOINT\");\n        this.azureADTokenProvider = fields?.azureADTokenProvider;\n        if (!this.azureOpenAIApiKey && !this.apiKey && !this.azureADTokenProvider) {\n            throw new Error(\"Azure OpenAI API key or Token Provider not found\");\n        }\n    }\n    _getClientOptions(options) {\n        if (!this.client) {\n            const openAIEndpointConfig = {\n                azureOpenAIApiDeploymentName: this.azureOpenAIApiDeploymentName,\n                azureOpenAIApiInstanceName: this.azureOpenAIApiInstanceName,\n                azureOpenAIApiKey: this.azureOpenAIApiKey,\n                azureOpenAIBasePath: this.azureOpenAIBasePath,\n                azureADTokenProvider: this.azureADTokenProvider,\n                baseURL: this.clientConfig.baseURL,\n            };\n            const endpoint = getEndpoint(openAIEndpointConfig);\n            const params = {\n                ...this.clientConfig,\n                baseURL: endpoint,\n                timeout: this.timeout,\n                maxRetries: 0,\n            };\n            if (!this.azureADTokenProvider) {\n                params.apiKey = openAIEndpointConfig.azureOpenAIApiKey;\n            }\n            if (!params.baseURL) {\n                delete params.baseURL;\n            }\n            const defaultHeaders = normalizeHeaders(params.defaultHeaders);\n            params.defaultHeaders = {\n                ...params.defaultHeaders,\n                \"User-Agent\": defaultHeaders[\"User-Agent\"]\n                    ? `${defaultHeaders[\"User-Agent\"]}: langchainjs-azure-openai-v2`\n                    : `langchainjs-azure-openai-v2`,\n            };\n            this.client = new AzureOpenAIClient({\n                apiVersion: this.azureOpenAIApiVersion,\n                azureADTokenProvider: this.azureADTokenProvider,\n                ...params,\n            });\n        }\n        const requestOptions = {\n            ...this.clientConfig,\n            ...options,\n        };\n        if (this.azureOpenAIApiKey) {\n            requestOptions.headers = {\n                \"api-key\": this.azureOpenAIApiKey,\n                ...requestOptions.headers,\n            };\n            requestOptions.query = {\n                \"api-version\": this.azureOpenAIApiVersion,\n                ...requestOptions.query,\n            };\n        }\n        return requestOptions;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    toJSON() {\n        const json = super.toJSON();\n        function isRecord(obj) {\n            return typeof obj === \"object\" && obj != null;\n        }\n        if (isRecord(json) && isRecord(json.kwargs)) {\n            delete json.kwargs.azure_openai_base_path;\n            delete json.kwargs.azure_openai_api_deployment_name;\n            delete json.kwargs.azure_openai_api_key;\n            delete json.kwargs.azure_openai_api_version;\n            delete json.kwargs.azure_open_ai_base_path;\n        }\n        return json;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AACO,MAAM,oBAAoB,uJAAA,CAAA,SAAM;IACnC,IAAI,aAAa;QACb,OAAO;YACH,GAAG,KAAK,CAAC,UAAU;YACnB,cAAc;YACd,kBAAkB;YAClB,gBAAgB;YAChB,gBAAgB;YAChB,qBAAqB;YACrB,uBAAuB;YACvB,qBAAqB;YACrB,8BAA8B;QAClC;IACJ;IACA,IAAI,aAAa;QACb,OAAO;YACH,GAAG,KAAK,CAAC,UAAU;YACnB,mBAAmB;QACvB;IACJ;IACA,YAAY,MAAM,CAAE;QAChB,KAAK,CAAC;QACN,OAAO,cAAc,CAAC,IAAI,EAAE,yBAAyB;YACjD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,qBAAqB;YAC7C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,wBAAwB;YAChD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,8BAA8B;YACtD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gCAAgC;YACxD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,uBAAuB;YAC/C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,uBAAuB;YAC/C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,IAAI,CAAC,4BAA4B,GAC7B,CAAC,QAAQ,2CACL,QAAQ,4BAA4B,KACpC,CAAC,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE,mDACpB,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE,mCAAmC;QACtE,IAAI,CAAC,iBAAiB,GAClB,QAAQ,qBACJ,QAAQ,gBACR,QAAQ,UACR,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,0BAA0B,GAC3B,QAAQ,8BACJ,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,qBAAqB,GACtB,QAAQ,yBACJ,QAAQ,oBACR,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,mBAAmB,GACpB,QAAQ,uBACJ,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,mBAAmB,GACpB,QAAQ,uBACJ,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,oBAAoB,GAAG,QAAQ;QACpC,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACvE,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,kBAAkB,OAAO,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,MAAM,uBAAuB;gBACzB,8BAA8B,IAAI,CAAC,4BAA4B;gBAC/D,4BAA4B,IAAI,CAAC,0BAA0B;gBAC3D,mBAAmB,IAAI,CAAC,iBAAiB;gBACzC,qBAAqB,IAAI,CAAC,mBAAmB;gBAC7C,sBAAsB,IAAI,CAAC,oBAAoB;gBAC/C,SAAS,IAAI,CAAC,YAAY,CAAC,OAAO;YACtC;YACA,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,MAAM,SAAS;gBACX,GAAG,IAAI,CAAC,YAAY;gBACpB,SAAS;gBACT,SAAS,IAAI,CAAC,OAAO;gBACrB,YAAY;YAChB;YACA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC5B,OAAO,MAAM,GAAG,qBAAqB,iBAAiB;YAC1D;YACA,IAAI,CAAC,OAAO,OAAO,EAAE;gBACjB,OAAO,OAAO,OAAO;YACzB;YACA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,cAAc;YAC7D,OAAO,cAAc,GAAG;gBACpB,GAAG,OAAO,cAAc;gBACxB,cAAc,cAAc,CAAC,aAAa,GACpC,GAAG,cAAc,CAAC,aAAa,CAAC,6BAA6B,CAAC,GAC9D,CAAC,2BAA2B,CAAC;YACvC;YACA,IAAI,CAAC,MAAM,GAAG,IAAI,2KAAA,CAAA,cAAiB,CAAC;gBAChC,YAAY,IAAI,CAAC,qBAAqB;gBACtC,sBAAsB,IAAI,CAAC,oBAAoB;gBAC/C,GAAG,MAAM;YACb;QACJ;QACA,MAAM,iBAAiB;YACnB,GAAG,IAAI,CAAC,YAAY;YACpB,GAAG,OAAO;QACd;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,eAAe,OAAO,GAAG;gBACrB,WAAW,IAAI,CAAC,iBAAiB;gBACjC,GAAG,eAAe,OAAO;YAC7B;YACA,eAAe,KAAK,GAAG;gBACnB,eAAe,IAAI,CAAC,qBAAqB;gBACzC,GAAG,eAAe,KAAK;YAC3B;QACJ;QACA,OAAO;IACX;IACA,8DAA8D;IAC9D,SAAS;QACL,MAAM,OAAO,KAAK,CAAC;QACnB,SAAS,SAAS,GAAG;YACjB,OAAO,OAAO,QAAQ,YAAY,OAAO;QAC7C;QACA,IAAI,SAAS,SAAS,SAAS,KAAK,MAAM,GAAG;YACzC,OAAO,KAAK,MAAM,CAAC,sBAAsB;YACzC,OAAO,KAAK,MAAM,CAAC,gCAAgC;YACnD,OAAO,KAAK,MAAM,CAAC,oBAAoB;YACvC,OAAO,KAAK,MAAM,CAAC,wBAAwB;YAC3C,OAAO,KAAK,MAAM,CAAC,uBAAuB;QAC9C;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3275, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/embeddings.js"], "sourcesContent": ["import { OpenAI as OpenAIClient } from \"openai\";\nimport { getEnvironmentVariable } from \"@langchain/core/utils/env\";\nimport { Embeddings } from \"@langchain/core/embeddings\";\nimport { chunkArray } from \"@langchain/core/utils/chunk_array\";\nimport { getEndpoint } from \"./utils/azure.js\";\nimport { wrapOpenAIClientError } from \"./utils/openai.js\";\n/**\n * Class for generating embeddings using the OpenAI API.\n *\n * To use with Azure, import the `AzureOpenAIEmbeddings` class.\n *\n * @example\n * ```typescript\n * // Embed a query using OpenAIEmbeddings to generate embeddings for a given text\n * const model = new OpenAIEmbeddings();\n * const res = await model.embedQuery(\n *   \"What would be a good company name for a company that makes colorful socks?\",\n * );\n * console.log({ res });\n *\n * ```\n */\nexport class OpenAIEmbeddings extends Embeddings {\n    constructor(fields) {\n        const fieldsWithDefaults = { maxConcurrency: 2, ...fields };\n        super(fieldsWithDefaults);\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"text-embedding-ada-002\"\n        });\n        /** @deprecated Use \"model\" instead */\n        Object.defineProperty(this, \"modelName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"batchSize\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 512\n        });\n        // TODO: Update to `false` on next minor release (see: https://github.com/langchain-ai/langchainjs/pull/3612)\n        Object.defineProperty(this, \"stripNewLines\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        /**\n         * The number of dimensions the resulting output embeddings should have.\n         * Only supported in `text-embedding-3` and later models.\n         */\n        Object.defineProperty(this, \"dimensions\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"timeout\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"organization\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"clientConfig\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        const apiKey = fieldsWithDefaults?.apiKey ??\n            fieldsWithDefaults?.openAIApiKey ??\n            getEnvironmentVariable(\"OPENAI_API_KEY\");\n        this.organization =\n            fieldsWithDefaults?.configuration?.organization ??\n                getEnvironmentVariable(\"OPENAI_ORGANIZATION\");\n        this.model =\n            fieldsWithDefaults?.model ?? fieldsWithDefaults?.modelName ?? this.model;\n        this.modelName = this.model;\n        this.batchSize = fieldsWithDefaults?.batchSize ?? this.batchSize;\n        this.stripNewLines =\n            fieldsWithDefaults?.stripNewLines ?? this.stripNewLines;\n        this.timeout = fieldsWithDefaults?.timeout;\n        this.dimensions = fieldsWithDefaults?.dimensions;\n        this.clientConfig = {\n            apiKey,\n            organization: this.organization,\n            dangerouslyAllowBrowser: true,\n            ...fields?.configuration,\n        };\n    }\n    /**\n     * Method to generate embeddings for an array of documents. Splits the\n     * documents into batches and makes requests to the OpenAI API to generate\n     * embeddings.\n     * @param texts Array of documents to generate embeddings for.\n     * @returns Promise that resolves to a 2D array of embeddings for each document.\n     */\n    async embedDocuments(texts) {\n        const batches = chunkArray(this.stripNewLines ? texts.map((t) => t.replace(/\\n/g, \" \")) : texts, this.batchSize);\n        const batchRequests = batches.map((batch) => {\n            const params = {\n                model: this.model,\n                input: batch,\n            };\n            if (this.dimensions) {\n                params.dimensions = this.dimensions;\n            }\n            return this.embeddingWithRetry(params);\n        });\n        const batchResponses = await Promise.all(batchRequests);\n        const embeddings = [];\n        for (let i = 0; i < batchResponses.length; i += 1) {\n            const batch = batches[i];\n            const { data: batchResponse } = batchResponses[i];\n            for (let j = 0; j < batch.length; j += 1) {\n                embeddings.push(batchResponse[j].embedding);\n            }\n        }\n        return embeddings;\n    }\n    /**\n     * Method to generate an embedding for a single document. Calls the\n     * embeddingWithRetry method with the document as the input.\n     * @param text Document to generate an embedding for.\n     * @returns Promise that resolves to an embedding for the document.\n     */\n    async embedQuery(text) {\n        const params = {\n            model: this.model,\n            input: this.stripNewLines ? text.replace(/\\n/g, \" \") : text,\n        };\n        if (this.dimensions) {\n            params.dimensions = this.dimensions;\n        }\n        const { data } = await this.embeddingWithRetry(params);\n        return data[0].embedding;\n    }\n    /**\n     * Private method to make a request to the OpenAI API to generate\n     * embeddings. Handles the retry logic and returns the response from the\n     * API.\n     * @param request Request to send to the OpenAI API.\n     * @returns Promise that resolves to the response from the API.\n     */\n    async embeddingWithRetry(request) {\n        if (!this.client) {\n            const openAIEndpointConfig = {\n                baseURL: this.clientConfig.baseURL,\n            };\n            const endpoint = getEndpoint(openAIEndpointConfig);\n            const params = {\n                ...this.clientConfig,\n                baseURL: endpoint,\n                timeout: this.timeout,\n                maxRetries: 0,\n            };\n            if (!params.baseURL) {\n                delete params.baseURL;\n            }\n            this.client = new OpenAIClient(params);\n        }\n        const requestOptions = {};\n        return this.caller.call(async () => {\n            try {\n                const res = await this.client.embeddings.create(request, requestOptions);\n                return res;\n            }\n            catch (e) {\n                const error = wrapOpenAIClientError(e);\n                throw error;\n            }\n        });\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;;;;;;;AAiBO,MAAM,yBAAyB,2JAAA,CAAA,aAAU;IAC5C,YAAY,MAAM,CAAE;QAChB,MAAM,qBAAqB;YAAE,gBAAgB;YAAG,GAAG,MAAM;QAAC;QAC1D,KAAK,CAAC;QACN,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,oCAAoC,GACpC,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,6GAA6G;QAC7G,OAAO,cAAc,CAAC,IAAI,EAAE,iBAAiB;YACzC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA;;;SAGC,GACD,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;YACnC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,MAAM,SAAS,oBAAoB,UAC/B,oBAAoB,gBACpB,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC3B,IAAI,CAAC,YAAY,GACb,oBAAoB,eAAe,gBAC/B,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,KAAK,GACN,oBAAoB,SAAS,oBAAoB,aAAa,IAAI,CAAC,KAAK;QAC5E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK;QAC3B,IAAI,CAAC,SAAS,GAAG,oBAAoB,aAAa,IAAI,CAAC,SAAS;QAChE,IAAI,CAAC,aAAa,GACd,oBAAoB,iBAAiB,IAAI,CAAC,aAAa;QAC3D,IAAI,CAAC,OAAO,GAAG,oBAAoB;QACnC,IAAI,CAAC,UAAU,GAAG,oBAAoB;QACtC,IAAI,CAAC,YAAY,GAAG;YAChB;YACA,cAAc,IAAI,CAAC,YAAY;YAC/B,yBAAyB;YACzB,GAAG,QAAQ,aAAa;QAC5B;IACJ;IACA;;;;;;KAMC,GACD,MAAM,eAAe,KAAK,EAAE;QACxB,MAAM,UAAU,CAAA,GAAA,qKAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,aAAa,GAAG,MAAM,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO,CAAC,OAAO,QAAQ,OAAO,IAAI,CAAC,SAAS;QAC/G,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAC;YAC/B,MAAM,SAAS;gBACX,OAAO,IAAI,CAAC,KAAK;gBACjB,OAAO;YACX;YACA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,OAAO,UAAU,GAAG,IAAI,CAAC,UAAU;YACvC;YACA,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACnC;QACA,MAAM,iBAAiB,MAAM,QAAQ,GAAG,CAAC;QACzC,MAAM,aAAa,EAAE;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,KAAK,EAAG;YAC/C,MAAM,QAAQ,OAAO,CAAC,EAAE;YACxB,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,cAAc,CAAC,EAAE;YACjD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;gBACtC,WAAW,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,SAAS;YAC9C;QACJ;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,MAAM,WAAW,IAAI,EAAE;QACnB,MAAM,SAAS;YACX,OAAO,IAAI,CAAC,KAAK;YACjB,OAAO,IAAI,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,OAAO,OAAO;QAC3D;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,UAAU,GAAG,IAAI,CAAC,UAAU;QACvC;QACA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC;QAC/C,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS;IAC5B;IACA;;;;;;KAMC,GACD,MAAM,mBAAmB,OAAO,EAAE;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,MAAM,uBAAuB;gBACzB,SAAS,IAAI,CAAC,YAAY,CAAC,OAAO;YACtC;YACA,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,MAAM,SAAS;gBACX,GAAG,IAAI,CAAC,YAAY;gBACpB,SAAS;gBACT,SAAS,IAAI,CAAC,OAAO;gBACrB,YAAY;YAChB;YACA,IAAI,CAAC,OAAO,OAAO,EAAE;gBACjB,OAAO,OAAO,OAAO;YACzB;YACA,IAAI,CAAC,MAAM,GAAG,IAAI,4KAAA,CAAA,SAAY,CAAC;QACnC;QACA,MAAM,iBAAiB,CAAC;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACpB,IAAI;gBACA,MAAM,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS;gBACzD,OAAO;YACX,EACA,OAAO,GAAG;gBACN,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,wBAAqB,AAAD,EAAE;gBACpC,MAAM;YACV;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3459, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/azure/embeddings.js"], "sourcesContent": ["import { AzureOpenAI as AzureOpenAIClient, } from \"openai\";\nimport { getEnvironmentVariable } from \"@langchain/core/utils/env\";\nimport { OpenAIEmbeddings } from \"../embeddings.js\";\nimport { getEndpoint } from \"../utils/azure.js\";\nimport { wrapOpenAIClientError } from \"../utils/openai.js\";\nimport { normalizeHeaders } from \"../utils/headers.js\";\nexport class AzureOpenAIEmbeddings extends OpenAIEmbeddings {\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"azureOpenAIApiVersion\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIApiKey\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureADTokenProvider\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIApiInstanceName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIApiDeploymentName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"azureOpenAIBasePath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.batchSize = fields?.batchSize ?? 1;\n        this.azureOpenAIApiKey =\n            fields?.azureOpenAIApiKey ??\n                fields?.apiKey ??\n                getEnvironmentVariable(\"AZURE_OPENAI_API_KEY\");\n        this.azureOpenAIApiVersion =\n            fields?.azureOpenAIApiVersion ??\n                fields?.openAIApiVersion ??\n                getEnvironmentVariable(\"AZURE_OPENAI_API_VERSION\");\n        this.azureOpenAIBasePath =\n            fields?.azureOpenAIBasePath ??\n                getEnvironmentVariable(\"AZURE_OPENAI_BASE_PATH\");\n        this.azureOpenAIApiInstanceName =\n            fields?.azureOpenAIApiInstanceName ??\n                getEnvironmentVariable(\"AZURE_OPENAI_API_INSTANCE_NAME\");\n        this.azureOpenAIApiDeploymentName =\n            (fields?.azureOpenAIApiEmbeddingsDeploymentName ||\n                fields?.azureOpenAIApiDeploymentName) ??\n                (getEnvironmentVariable(\"AZURE_OPENAI_API_EMBEDDINGS_DEPLOYMENT_NAME\") ||\n                    getEnvironmentVariable(\"AZURE_OPENAI_API_DEPLOYMENT_NAME\"));\n        this.azureADTokenProvider = fields?.azureADTokenProvider;\n    }\n    async embeddingWithRetry(request) {\n        if (!this.client) {\n            const openAIEndpointConfig = {\n                azureOpenAIApiDeploymentName: this.azureOpenAIApiDeploymentName,\n                azureOpenAIApiInstanceName: this.azureOpenAIApiInstanceName,\n                azureOpenAIApiKey: this.azureOpenAIApiKey,\n                azureOpenAIBasePath: this.azureOpenAIBasePath,\n                azureADTokenProvider: this.azureADTokenProvider,\n                baseURL: this.clientConfig.baseURL,\n            };\n            const endpoint = getEndpoint(openAIEndpointConfig);\n            const params = {\n                ...this.clientConfig,\n                baseURL: endpoint,\n                timeout: this.timeout,\n                maxRetries: 0,\n            };\n            if (!this.azureADTokenProvider) {\n                params.apiKey = openAIEndpointConfig.azureOpenAIApiKey;\n            }\n            if (!params.baseURL) {\n                delete params.baseURL;\n            }\n            const defaultHeaders = normalizeHeaders(params.defaultHeaders);\n            params.defaultHeaders = {\n                ...params.defaultHeaders,\n                \"User-Agent\": defaultHeaders[\"User-Agent\"]\n                    ? `${defaultHeaders[\"User-Agent\"]}: langchainjs-azure-openai-v2`\n                    : `langchainjs-azure-openai-v2`,\n            };\n            this.client = new AzureOpenAIClient({\n                apiVersion: this.azureOpenAIApiVersion,\n                azureADTokenProvider: this.azureADTokenProvider,\n                deployment: this.azureOpenAIApiDeploymentName,\n                ...params,\n            });\n        }\n        const requestOptions = {};\n        if (this.azureOpenAIApiKey) {\n            requestOptions.headers = {\n                \"api-key\": this.azureOpenAIApiKey,\n                ...requestOptions.headers,\n            };\n            requestOptions.query = {\n                \"api-version\": this.azureOpenAIApiVersion,\n                ...requestOptions.query,\n            };\n        }\n        return this.caller.call(async () => {\n            try {\n                const res = await this.client.embeddings.create(request, requestOptions);\n                return res;\n            }\n            catch (e) {\n                const error = wrapOpenAIClientError(e);\n                throw error;\n            }\n        });\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;;;;;;AACO,MAAM,8BAA8B,6JAAA,CAAA,mBAAgB;IACvD,YAAY,MAAM,CAAE;QAChB,KAAK,CAAC;QACN,OAAO,cAAc,CAAC,IAAI,EAAE,yBAAyB;YACjD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,qBAAqB;YAC7C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,wBAAwB;YAChD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,8BAA8B;YACtD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gCAAgC;YACxD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,uBAAuB;YAC/C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,IAAI,CAAC,SAAS,GAAG,QAAQ,aAAa;QACtC,IAAI,CAAC,iBAAiB,GAClB,QAAQ,qBACJ,QAAQ,UACR,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,qBAAqB,GACtB,QAAQ,yBACJ,QAAQ,oBACR,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,mBAAmB,GACpB,QAAQ,uBACJ,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,0BAA0B,GAC3B,QAAQ,8BACJ,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC/B,IAAI,CAAC,4BAA4B,GAC7B,CAAC,QAAQ,0CACL,QAAQ,4BAA4B,KACpC,CAAC,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE,kDACpB,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE,mCAAmC;QACtE,IAAI,CAAC,oBAAoB,GAAG,QAAQ;IACxC;IACA,MAAM,mBAAmB,OAAO,EAAE;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,MAAM,uBAAuB;gBACzB,8BAA8B,IAAI,CAAC,4BAA4B;gBAC/D,4BAA4B,IAAI,CAAC,0BAA0B;gBAC3D,mBAAmB,IAAI,CAAC,iBAAiB;gBACzC,qBAAqB,IAAI,CAAC,mBAAmB;gBAC7C,sBAAsB,IAAI,CAAC,oBAAoB;gBAC/C,SAAS,IAAI,CAAC,YAAY,CAAC,OAAO;YACtC;YACA,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,MAAM,SAAS;gBACX,GAAG,IAAI,CAAC,YAAY;gBACpB,SAAS;gBACT,SAAS,IAAI,CAAC,OAAO;gBACrB,YAAY;YAChB;YACA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC5B,OAAO,MAAM,GAAG,qBAAqB,iBAAiB;YAC1D;YACA,IAAI,CAAC,OAAO,OAAO,EAAE;gBACjB,OAAO,OAAO,OAAO;YACzB;YACA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,cAAc;YAC7D,OAAO,cAAc,GAAG;gBACpB,GAAG,OAAO,cAAc;gBACxB,cAAc,cAAc,CAAC,aAAa,GACpC,GAAG,cAAc,CAAC,aAAa,CAAC,6BAA6B,CAAC,GAC9D,CAAC,2BAA2B,CAAC;YACvC;YACA,IAAI,CAAC,MAAM,GAAG,IAAI,2KAAA,CAAA,cAAiB,CAAC;gBAChC,YAAY,IAAI,CAAC,qBAAqB;gBACtC,sBAAsB,IAAI,CAAC,oBAAoB;gBAC/C,YAAY,IAAI,CAAC,4BAA4B;gBAC7C,GAAG,MAAM;YACb;QACJ;QACA,MAAM,iBAAiB,CAAC;QACxB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,eAAe,OAAO,GAAG;gBACrB,WAAW,IAAI,CAAC,iBAAiB;gBACjC,GAAG,eAAe,OAAO;YAC7B;YACA,eAAe,KAAK,GAAG;gBACnB,eAAe,IAAI,CAAC,qBAAqB;gBACzC,GAAG,eAAe,KAAK;YAC3B;QACJ;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACpB,IAAI;gBACA,MAAM,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS;gBACzD,OAAO;YACX,EACA,OAAO,GAAG;gBACN,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,wBAAqB,AAAD,EAAE;gBACpC,MAAM;YACV;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3585, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3591, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/tools/dalle.js"], "sourcesContent": ["/* eslint-disable no-param-reassign */\nimport { getEnvironmentVariable } from \"@langchain/core/utils/env\";\nimport { OpenAI as OpenAIClient } from \"openai\";\nimport { Tool } from \"@langchain/core/tools\";\n/**\n * A tool for generating images with Open AIs Dall-E 2 or 3 API.\n */\nexport class DallEAPIWrapper extends Tool {\n    static lc_name() {\n        return \"DallEAPIWrapper\";\n    }\n    constructor(fields) {\n        // Shim for new base tool param name\n        if (fields?.responseFormat !== undefined &&\n            [\"url\", \"b64_json\"].includes(fields.responseFormat)) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            fields.dallEResponseFormat = fields.responseFormat;\n            fields.responseFormat = \"content\";\n        }\n        super(fields);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"dalle_api_wrapper\"\n        });\n        Object.defineProperty(this, \"description\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"A wrapper around OpenAI DALL-E API. Useful for when you need to generate images from a text description. Input should be an image description.\"\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"dall-e-3\"\n        });\n        Object.defineProperty(this, \"style\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"vivid\"\n        });\n        Object.defineProperty(this, \"quality\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"standard\"\n        });\n        Object.defineProperty(this, \"n\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 1\n        });\n        Object.defineProperty(this, \"size\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"1024x1024\"\n        });\n        Object.defineProperty(this, \"dallEResponseFormat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"url\"\n        });\n        Object.defineProperty(this, \"user\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        const openAIApiKey = fields?.apiKey ??\n            fields?.openAIApiKey ??\n            getEnvironmentVariable(\"OPENAI_API_KEY\");\n        const organization = fields?.organization ?? getEnvironmentVariable(\"OPENAI_ORGANIZATION\");\n        const clientConfig = {\n            apiKey: openAIApiKey,\n            organization,\n            dangerouslyAllowBrowser: true,\n            baseURL: fields?.baseUrl,\n        };\n        this.client = new OpenAIClient(clientConfig);\n        this.model = fields?.model ?? fields?.modelName ?? this.model;\n        this.style = fields?.style ?? this.style;\n        this.quality = fields?.quality ?? this.quality;\n        this.n = fields?.n ?? this.n;\n        this.size = fields?.size ?? this.size;\n        this.dallEResponseFormat =\n            fields?.dallEResponseFormat ?? this.dallEResponseFormat;\n        this.user = fields?.user;\n    }\n    /**\n     * Processes the API response if multiple images are generated.\n     * Returns a list of MessageContentImageUrl objects. If the response\n     * format is `url`, then the `image_url` field will contain the URL.\n     * If it is `b64_json`, then the `image_url` field will contain an object\n     * with a `url` field with the base64 encoded image.\n     *\n     * @param {OpenAIClient.Images.ImagesResponse[]} response The API response\n     * @returns {MessageContentImageUrl[]}\n     */\n    processMultipleGeneratedUrls(response) {\n        if (this.dallEResponseFormat === \"url\") {\n            return response.flatMap((res) => {\n                const imageUrlContent = res.data\n                    ?.flatMap((item) => {\n                    if (!item.url)\n                        return [];\n                    return {\n                        type: \"image_url\",\n                        image_url: item.url,\n                    };\n                })\n                    .filter((item) => item !== undefined &&\n                    item.type === \"image_url\" &&\n                    typeof item.image_url === \"string\" &&\n                    item.image_url !== undefined) ?? [];\n                return imageUrlContent;\n            });\n        }\n        else {\n            return response.flatMap((res) => {\n                const b64Content = res.data\n                    ?.flatMap((item) => {\n                    if (!item.b64_json)\n                        return [];\n                    return {\n                        type: \"image_url\",\n                        image_url: {\n                            url: item.b64_json,\n                        },\n                    };\n                })\n                    .filter((item) => item !== undefined &&\n                    item.type === \"image_url\" &&\n                    typeof item.image_url === \"object\" &&\n                    \"url\" in item.image_url &&\n                    typeof item.image_url.url === \"string\" &&\n                    item.image_url.url !== undefined) ?? [];\n                return b64Content;\n            });\n        }\n    }\n    /** @ignore */\n    async _call(input) {\n        const generateImageFields = {\n            model: this.model,\n            prompt: input,\n            n: 1,\n            size: this.size,\n            response_format: this.dallEResponseFormat,\n            style: this.style,\n            quality: this.quality,\n            user: this.user,\n        };\n        if (this.n > 1) {\n            const results = await Promise.all(Array.from({ length: this.n }).map(() => this.client.images.generate(generateImageFields)));\n            return this.processMultipleGeneratedUrls(results);\n        }\n        const response = await this.client.images.generate(generateImageFields);\n        let data = \"\";\n        if (this.dallEResponseFormat === \"url\") {\n            [data] =\n                response.data\n                    ?.map((item) => item.url)\n                    .filter((url) => url !== \"undefined\") ?? [];\n        }\n        else {\n            [data] =\n                response.data\n                    ?.map((item) => item.b64_json)\n                    .filter((b64_json) => b64_json !== \"undefined\") ??\n                    [];\n        }\n        return data;\n    }\n}\nObject.defineProperty(DallEAPIWrapper, \"toolName\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: \"dalle_api_wrapper\"\n});\n"], "names": [], "mappings": "AAAA,oCAAoC;;;AACpC;AAAA;AACA;AAAA;AACA;AAAA;;;;AAIO,MAAM,wBAAwB,+KAAA,CAAA,OAAI;IACrC,OAAO,UAAU;QACb,OAAO;IACX;IACA,YAAY,MAAM,CAAE;QAChB,oCAAoC;QACpC,IAAI,QAAQ,mBAAmB,aAC3B;YAAC;YAAO;SAAW,CAAC,QAAQ,CAAC,OAAO,cAAc,GAAG;YACrD,8DAA8D;YAC9D,OAAO,mBAAmB,GAAG,OAAO,cAAc;YAClD,OAAO,cAAc,GAAG;QAC5B;QACA,KAAK,CAAC;QACN,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;YACnC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;YAC7B,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,uBAAuB;YAC/C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,MAAM,eAAe,QAAQ,UACzB,QAAQ,gBACR,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QAC3B,MAAM,eAAe,QAAQ,gBAAgB,CAAA,GAAA,6JAAA,CAAA,yBAAsB,AAAD,EAAE;QACpE,MAAM,eAAe;YACjB,QAAQ;YACR;YACA,yBAAyB;YACzB,SAAS,QAAQ;QACrB;QACA,IAAI,CAAC,MAAM,GAAG,IAAI,4KAAA,CAAA,SAAY,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,QAAQ,SAAS,QAAQ,aAAa,IAAI,CAAC,KAAK;QAC7D,IAAI,CAAC,KAAK,GAAG,QAAQ,SAAS,IAAI,CAAC,KAAK;QACxC,IAAI,CAAC,OAAO,GAAG,QAAQ,WAAW,IAAI,CAAC,OAAO;QAC9C,IAAI,CAAC,CAAC,GAAG,QAAQ,KAAK,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG,QAAQ,QAAQ,IAAI,CAAC,IAAI;QACrC,IAAI,CAAC,mBAAmB,GACpB,QAAQ,uBAAuB,IAAI,CAAC,mBAAmB;QAC3D,IAAI,CAAC,IAAI,GAAG,QAAQ;IACxB;IACA;;;;;;;;;KASC,GACD,6BAA6B,QAAQ,EAAE;QACnC,IAAI,IAAI,CAAC,mBAAmB,KAAK,OAAO;YACpC,OAAO,SAAS,OAAO,CAAC,CAAC;gBACrB,MAAM,kBAAkB,IAAI,IAAI,EAC1B,QAAQ,CAAC;oBACX,IAAI,CAAC,KAAK,GAAG,EACT,OAAO,EAAE;oBACb,OAAO;wBACH,MAAM;wBACN,WAAW,KAAK,GAAG;oBACvB;gBACJ,GACK,OAAO,CAAC,OAAS,SAAS,aAC3B,KAAK,IAAI,KAAK,eACd,OAAO,KAAK,SAAS,KAAK,YAC1B,KAAK,SAAS,KAAK,cAAc,EAAE;gBACvC,OAAO;YACX;QACJ,OACK;YACD,OAAO,SAAS,OAAO,CAAC,CAAC;gBACrB,MAAM,aAAa,IAAI,IAAI,EACrB,QAAQ,CAAC;oBACX,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO,EAAE;oBACb,OAAO;wBACH,MAAM;wBACN,WAAW;4BACP,KAAK,KAAK,QAAQ;wBACtB;oBACJ;gBACJ,GACK,OAAO,CAAC,OAAS,SAAS,aAC3B,KAAK,IAAI,KAAK,eACd,OAAO,KAAK,SAAS,KAAK,YAC1B,SAAS,KAAK,SAAS,IACvB,OAAO,KAAK,SAAS,CAAC,GAAG,KAAK,YAC9B,KAAK,SAAS,CAAC,GAAG,KAAK,cAAc,EAAE;gBAC3C,OAAO;YACX;QACJ;IACJ;IACA,YAAY,GACZ,MAAM,MAAM,KAAK,EAAE;QACf,MAAM,sBAAsB;YACxB,OAAO,IAAI,CAAC,KAAK;YACjB,QAAQ;YACR,GAAG;YACH,MAAM,IAAI,CAAC,IAAI;YACf,iBAAiB,IAAI,CAAC,mBAAmB;YACzC,OAAO,IAAI,CAAC,KAAK;YACjB,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM,IAAI,CAAC,IAAI;QACnB;QACA,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG;YACZ,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC;gBAAE,QAAQ,IAAI,CAAC,CAAC;YAAC,GAAG,GAAG,CAAC,IAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YACvG,OAAO,IAAI,CAAC,4BAA4B,CAAC;QAC7C;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;QACnD,IAAI,OAAO;QACX,IAAI,IAAI,CAAC,mBAAmB,KAAK,OAAO;YACpC,CAAC,KAAK,GACF,SAAS,IAAI,EACP,IAAI,CAAC,OAAS,KAAK,GAAG,EACvB,OAAO,CAAC,MAAQ,QAAQ,gBAAgB,EAAE;QACvD,OACK;YACD,CAAC,KAAK,GACF,SAAS,IAAI,EACP,IAAI,CAAC,OAAS,KAAK,QAAQ,EAC5B,OAAO,CAAC,WAAa,aAAa,gBACnC,EAAE;QACd;QACA,OAAO;IACX;AACJ;AACA,OAAO,cAAc,CAAC,iBAAiB,YAAY;IAC/C,YAAY;IACZ,cAAc;IACd,UAAU;IACV,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3768, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/tools/index.js"], "sourcesContent": ["export * from \"./dalle.js\";\n"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3782, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/utils/prompts.js"], "sourcesContent": ["import { _convertMessagesToOpenAIParams } from \"../chat_models.js\";\n/**\n * Convert a formatted LangChain prompt (e.g. pulled from the hub) into\n * a format expected by OpenAI's JS SDK.\n *\n * Requires the \"@langchain/openai\" package to be installed in addition\n * to the OpenAI SDK.\n *\n * @example\n * ```ts\n * import { convertPromptToOpenAI } from \"langsmith/utils/hub/openai\";\n * import { pull } from \"langchain/hub\";\n *\n * import OpenAI from 'openai';\n *\n * const prompt = await pull(\"jacob/joke-generator\");\n * const formattedPrompt = await prompt.invoke({\n *   topic: \"cats\",\n * });\n *\n * const { messages } = convertPromptToOpenAI(formattedPrompt);\n *\n * const openAIClient = new OpenAI();\n *\n * const openaiResponse = await openAIClient.chat.completions.create({\n *   model: \"gpt-4o-mini\",\n *   messages,\n * });\n * ```\n * @param formattedPrompt\n * @returns A partial OpenAI payload.\n */\nexport function convertPromptToOpenAI(formattedPrompt) {\n    const messages = formattedPrompt.toChatMessages();\n    return {\n        messages: _convertMessagesToOpenAIParams(messages),\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAgCO,SAAS,sBAAsB,eAAe;IACjD,MAAM,WAAW,gBAAgB,cAAc;IAC/C,OAAO;QACH,UAAU,CAAA,GAAA,8JAAA,CAAA,iCAA8B,AAAD,EAAE;IAC7C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3797, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/dist/index.js"], "sourcesContent": ["export { OpenAI as OpenAIClient, toFile } from \"openai\";\nexport * from \"./chat_models.js\";\nexport * from \"./azure/chat_models.js\";\nexport * from \"./llms.js\";\nexport * from \"./azure/llms.js\";\nexport * from \"./azure/embeddings.js\";\nexport * from \"./embeddings.js\";\nexport * from \"./types.js\";\nexport * from \"./utils/openai.js\";\nexport * from \"./utils/azure.js\";\nexport * from \"./tools/index.js\";\nexport { convertPromptToOpenAI } from \"./utils/prompts.js\";\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3844, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/%40langchain/openai/index.js"], "sourcesContent": ["export * from './dist/index.js'"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}]}
module.exports = {

"[project]/node_modules/@langchain/openai/dist/utils/azure.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * This function generates an endpoint URL for (Azure) OpenAI
 * based on the configuration parameters provided.
 *
 * @param {OpenAIEndpointConfig} config - The configuration object for the (Azure) endpoint.
 *
 * @property {string} config.azureOpenAIApiDeploymentName - The deployment name of Azure OpenAI.
 * @property {string} config.azureOpenAIApiInstanceName - The instance name of Azure OpenAI, e.g. `example-resource`.
 * @property {string} config.azureOpenAIApiKey - The API Key for Azure OpenAI.
 * @property {string} config.azureOpenAIBasePath - The base path for Azure OpenAI, e.g. `https://example-resource.azure.openai.com/openai/deployments/`.
 * @property {string} config.baseURL - Some other custom base path URL.
 * @property {string} config.azureOpenAIEndpoint - The endpoint for the Azure OpenAI instance, e.g. `https://example-resource.azure.openai.com/`.
 *
 * The function operates as follows:
 * - If both `azureOpenAIBasePath` and `azureOpenAIApiDeploymentName` (plus `azureOpenAIApiKey`) are provided, it returns an URL combining these two parameters (`${azureOpenAIBasePath}/${azureOpenAIApiDeploymentName}`).
 * - If both `azureOpenAIEndpoint` and `azureOpenAIApiDeploymentName` (plus `azureOpenAIApiKey`) are provided, it returns an URL combining these two parameters (`${azureOpenAIEndpoint}/openai/deployments/${azureOpenAIApiDeploymentName}`).
 * - If `azureOpenAIApiKey` is provided, it checks for `azureOpenAIApiInstanceName` and `azureOpenAIApiDeploymentName` and throws an error if any of these is missing. If both are provided, it generates an URL incorporating these parameters.
 * - If none of the above conditions are met, return any custom `baseURL`.
 * - The function returns the generated URL as a string, or undefined if no custom paths are specified.
 *
 * @throws Will throw an error if the necessary parameters for generating the URL are missing.
 *
 * @returns {string | undefined} The generated (Azure) OpenAI endpoint URL.
 */ __turbopack_context__.s({
    "getEndpoint": ()=>getEndpoint
});
function getEndpoint(config) {
    const { azureOpenAIApiDeploymentName, azureOpenAIApiInstanceName, azureOpenAIApiKey, azureOpenAIBasePath, baseURL, azureADTokenProvider, azureOpenAIEndpoint } = config;
    if ((azureOpenAIApiKey || azureADTokenProvider) && azureOpenAIBasePath && azureOpenAIApiDeploymentName) {
        return `${azureOpenAIBasePath}/${azureOpenAIApiDeploymentName}`;
    }
    if ((azureOpenAIApiKey || azureADTokenProvider) && azureOpenAIEndpoint && azureOpenAIApiDeploymentName) {
        return `${azureOpenAIEndpoint}/openai/deployments/${azureOpenAIApiDeploymentName}`;
    }
    if (azureOpenAIApiKey || azureADTokenProvider) {
        if (!azureOpenAIApiInstanceName) {
            throw new Error("azureOpenAIApiInstanceName is required when using azureOpenAIApiKey");
        }
        if (!azureOpenAIApiDeploymentName) {
            throw new Error("azureOpenAIApiDeploymentName is a required parameter when using azureOpenAIApiKey");
        }
        return `https://${azureOpenAIApiInstanceName}.openai.azure.com/openai/deployments/${azureOpenAIApiDeploymentName}`;
    }
    return baseURL;
}
}),
"[project]/node_modules/@langchain/openai/dist/utils/errors.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable no-param-reassign */ __turbopack_context__.s({
    "addLangChainErrorFields": ()=>addLangChainErrorFields
});
function addLangChainErrorFields(error, lc_error_code) {
    error.lc_error_code = lc_error_code;
    error.message = `${error.message}\n\nTroubleshooting URL: https://js.langchain.com/docs/troubleshooting/errors/${lc_error_code}/\n`;
    return error;
}
}),
"[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "formatToOpenAIAssistantTool": ()=>formatToOpenAIAssistantTool,
    "formatToOpenAIToolChoice": ()=>formatToOpenAIToolChoice,
    "interopZodResponseFormat": ()=>interopZodResponseFormat,
    "wrapOpenAIClientError": ()=>wrapOpenAIClientError
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$core$2f$error$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/core/error.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$function_calling$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/function_calling.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/types.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$types$2f$zod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/types/zod.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$json_schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/json_schema.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$json_schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/json_schema.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$core$2f$to$2d$json$2d$schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/core/to-json-schema.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$core$2f$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/core/parse.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$helpers$2f$zod$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/helpers/zod.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/errors.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
function wrapOpenAIClientError(e) {
    let error;
    if (e.constructor.name === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$core$2f$error$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APIConnectionTimeoutError"].name) {
        error = new Error(e.message);
        error.name = "TimeoutError";
    } else if (e.constructor.name === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$core$2f$error$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APIUserAbortError"].name) {
        error = new Error(e.message);
        error.name = "AbortError";
    } else if (e.status === 400 && e.message.includes("tool_calls")) {
        error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addLangChainErrorFields"])(e, "INVALID_TOOL_RESULTS");
    } else if (e.status === 401) {
        error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addLangChainErrorFields"])(e, "MODEL_AUTHENTICATION");
    } else if (e.status === 429) {
        error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addLangChainErrorFields"])(e, "MODEL_RATE_LIMIT");
    } else if (e.status === 404) {
        error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addLangChainErrorFields"])(e, "MODEL_NOT_FOUND");
    } else {
        error = e;
    }
    return error;
}
;
function formatToOpenAIAssistantTool(tool) {
    return {
        type: "function",
        function: {
            name: tool.name,
            description: tool.description,
            parameters: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$types$2f$zod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInteropZodSchema"])(tool.schema) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$json_schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])(tool.schema) : tool.schema
        }
    };
}
function formatToOpenAIToolChoice(toolChoice) {
    if (!toolChoice) {
        return undefined;
    } else if (toolChoice === "any" || toolChoice === "required") {
        return "required";
    } else if (toolChoice === "auto") {
        return "auto";
    } else if (toolChoice === "none") {
        return "none";
    } else if (typeof toolChoice === "string") {
        return {
            type: "function",
            function: {
                name: toolChoice
            }
        };
    } else {
        return toolChoice;
    }
}
// inlined from openai/lib/parser.ts
function makeParseableResponseFormat(response_format, parser) {
    const obj = {
        ...response_format
    };
    Object.defineProperties(obj, {
        $brand: {
            value: "auto-parseable-response-format",
            enumerable: false
        },
        $parseRaw: {
            value: parser,
            enumerable: false
        }
    });
    return obj;
}
function interopZodResponseFormat(zodSchema, name, props) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$types$2f$zod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isZodSchemaV3"])(zodSchema)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$helpers$2f$zod$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["zodResponseFormat"])(zodSchema, name, props);
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$types$2f$zod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isZodSchemaV4"])(zodSchema)) {
        return makeParseableResponseFormat({
            type: "json_schema",
            json_schema: {
                ...props,
                name,
                strict: true,
                schema: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$core$2f$to$2d$json$2d$schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["toJSONSchema"])(zodSchema, {
                    cycles: "ref",
                    reused: "ref",
                    override (ctx) {
                        ctx.jsonSchema.title = name; // equivalent to `name` property
                    // TODO: implement `nullableStrategy` patch-fix (zod doesn't support openApi3 json schema target)
                    // TODO: implement `openaiStrictMode` patch-fix (where optional properties without `nullable` are not supported)
                    }
                })
            }
        }, (content)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$core$2f$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(zodSchema, JSON.parse(content)));
    }
    throw new Error("Unsupported schema response format");
}
}),
"[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$function_calling$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/function_calling.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/types.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$json_schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/json_schema.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$helpers$2f$zod$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/helpers/zod.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$errors$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/errors.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <locals>");
}),
"[project]/node_modules/@langchain/openai/dist/utils/openai-format-fndef.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "formatFunctionDefinitions": ()=>formatFunctionDefinitions
});
function isAnyOfProp(prop) {
    return prop.anyOf !== undefined && Array.isArray(prop.anyOf);
}
function formatFunctionDefinitions(functions) {
    const lines = [
        "namespace functions {",
        ""
    ];
    for (const f of functions){
        if (f.description) {
            lines.push(`// ${f.description}`);
        }
        if (Object.keys(f.parameters.properties ?? {}).length > 0) {
            lines.push(`type ${f.name} = (_: {`);
            lines.push(formatObjectProperties(f.parameters, 0));
            lines.push("}) => any;");
        } else {
            lines.push(`type ${f.name} = () => any;`);
        }
        lines.push("");
    }
    lines.push("} // namespace functions");
    return lines.join("\n");
}
// Format just the properties of an object (not including the surrounding braces)
function formatObjectProperties(obj, indent) {
    const lines = [];
    for (const [name, param] of Object.entries(obj.properties ?? {})){
        if (param.description && indent < 2) {
            lines.push(`// ${param.description}`);
        }
        if (obj.required?.includes(name)) {
            lines.push(`${name}: ${formatType(param, indent)},`);
        } else {
            lines.push(`${name}?: ${formatType(param, indent)},`);
        }
    }
    return lines.map((line)=>" ".repeat(indent) + line).join("\n");
}
// Format a single property type
function formatType(param, indent) {
    if (isAnyOfProp(param)) {
        return param.anyOf.map((v)=>formatType(v, indent)).join(" | ");
    }
    switch(param.type){
        case "string":
            if (param.enum) {
                return param.enum.map((v)=>`"${v}"`).join(" | ");
            }
            return "string";
        case "number":
            if (param.enum) {
                return param.enum.map((v)=>`${v}`).join(" | ");
            }
            return "number";
        case "integer":
            if (param.enum) {
                return param.enum.map((v)=>`${v}`).join(" | ");
            }
            return "number";
        case "boolean":
            return "boolean";
        case "null":
            return "null";
        case "object":
            return [
                "{",
                formatObjectProperties(param, indent + 2),
                "}"
            ].join("\n");
        case "array":
            if (param.items) {
                return `${formatType(param.items, indent)}[]`;
            }
            return "any[]";
        default:
            return "";
    }
}
}),
"[project]/node_modules/@langchain/openai/dist/utils/tools.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "_convertToOpenAITool": ()=>_convertToOpenAITool
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$function_calling$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/function_calling.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$tools$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/tools/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$function_calling$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__convertToOpenAITool__as__formatToOpenAITool$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/function_calling.js [app-route] (ecmascript) <locals> <export convertToOpenAITool as formatToOpenAITool>");
;
;
function _convertToOpenAITool(// eslint-disable-next-line @typescript-eslint/no-explicit-any
tool, fields) {
    let toolDef;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$tools$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isLangChainTool"])(tool)) {
        toolDef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$function_calling$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__convertToOpenAITool__as__formatToOpenAITool$3e$__["formatToOpenAITool"])(tool);
    } else {
        toolDef = tool;
    }
    if (fields?.strict !== undefined) {
        toolDef.function.strict = fields.strict;
    }
    return toolDef;
}
}),
"[project]/node_modules/@langchain/openai/dist/chat_models.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ChatOpenAI": ()=>ChatOpenAI,
    "ChatOpenAICompletions": ()=>ChatOpenAICompletions,
    "ChatOpenAIResponses": ()=>ChatOpenAIResponses,
    "_convertMessagesToOpenAIParams": ()=>_convertMessagesToOpenAIParams,
    "messageToOpenAIRole": ()=>messageToOpenAIRole
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/client.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$messages$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/messages.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$ai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/messages/ai.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$chat$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/messages/chat.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$function$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/messages/function.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$human$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/messages/human.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$system$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/messages/system.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$tool$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/messages/tool.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$content_blocks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/messages/content_blocks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$outputs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/outputs.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$outputs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/outputs.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/env.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$language_models$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/language_models/chat_models.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$language_models$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/language_models/chat_models.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$language_models$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/language_models/base.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$language_models$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/language_models/base.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$runnables$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/runnables.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$runnables$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/runnables/base.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$runnables$2f$passthrough$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/runnables/passthrough.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$output_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/output_parsers.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/output_parsers/json.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$structured$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/output_parsers/structured.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$output_parsers$2f$openai_tools$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/output_parsers/openai_tools.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$openai_tools$2f$json_output_tools_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/output_parsers/openai_tools/json_output_tools_parsers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/types.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$types$2f$zod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/types/zod.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$json_schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/json_schema.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$json_schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/json_schema.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/azure.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2d$format$2d$fndef$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai-format-fndef.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$tools$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/tools.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const _FUNCTION_CALL_IDS_MAP_KEY = "__openai_function_call_ids__";
function isBuiltInTool(tool) {
    return "type" in tool && tool.type !== "function";
}
function isBuiltInToolChoice(tool_choice) {
    return tool_choice != null && typeof tool_choice === "object" && "type" in tool_choice && tool_choice.type !== "function";
}
function isReasoningModel(model) {
    return model && /^o\d/.test(model);
}
function isStructuredOutputMethodParams(x) {
    return x !== undefined && // eslint-disable-next-line @typescript-eslint/no-explicit-any
    typeof x.schema === "object";
}
function extractGenericMessageCustomRole(message) {
    if (message.role !== "system" && message.role !== "developer" && message.role !== "assistant" && message.role !== "user" && message.role !== "function" && message.role !== "tool") {
        console.warn(`Unknown message role: ${message.role}`);
    }
    return message.role;
}
function messageToOpenAIRole(message) {
    const type = message._getType();
    switch(type){
        case "system":
            return "system";
        case "ai":
            return "assistant";
        case "human":
            return "user";
        case "function":
            return "function";
        case "tool":
            return "tool";
        case "generic":
            {
                if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$chat$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ChatMessage"].isInstance(message)) throw new Error("Invalid generic chat message");
                return extractGenericMessageCustomRole(message);
            }
        default:
            throw new Error(`Unknown message type: ${type}`);
    }
}
const completionsApiContentBlockConverter = {
    providerName: "ChatOpenAI",
    fromStandardTextBlock (block) {
        return {
            type: "text",
            text: block.text
        };
    },
    fromStandardImageBlock (block) {
        if (block.source_type === "url") {
            return {
                type: "image_url",
                image_url: {
                    url: block.url,
                    ...block.metadata?.detail ? {
                        detail: block.metadata.detail
                    } : {}
                }
            };
        }
        if (block.source_type === "base64") {
            const url = `data:${block.mime_type ?? ""};base64,${block.data}`;
            return {
                type: "image_url",
                image_url: {
                    url,
                    ...block.metadata?.detail ? {
                        detail: block.metadata.detail
                    } : {}
                }
            };
        }
        throw new Error(`Image content blocks with source_type ${block.source_type} are not supported for ChatOpenAI`);
    },
    fromStandardAudioBlock (block) {
        if (block.source_type === "url") {
            const data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$content_blocks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseBase64DataUrl"])({
                dataUrl: block.url
            });
            if (!data) {
                throw new Error(`URL audio blocks with source_type ${block.source_type} must be formatted as a data URL for ChatOpenAI`);
            }
            const rawMimeType = data.mime_type || block.mime_type || "";
            let mimeType;
            try {
                mimeType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$content_blocks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseMimeType"])(rawMimeType);
            } catch  {
                throw new Error(`Audio blocks with source_type ${block.source_type} must have mime type of audio/wav or audio/mp3`);
            }
            if (mimeType.type !== "audio" || mimeType.subtype !== "wav" && mimeType.subtype !== "mp3") {
                throw new Error(`Audio blocks with source_type ${block.source_type} must have mime type of audio/wav or audio/mp3`);
            }
            return {
                type: "input_audio",
                input_audio: {
                    format: mimeType.subtype,
                    data: data.data
                }
            };
        }
        if (block.source_type === "base64") {
            let mimeType;
            try {
                mimeType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$content_blocks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseMimeType"])(block.mime_type ?? "");
            } catch  {
                throw new Error(`Audio blocks with source_type ${block.source_type} must have mime type of audio/wav or audio/mp3`);
            }
            if (mimeType.type !== "audio" || mimeType.subtype !== "wav" && mimeType.subtype !== "mp3") {
                throw new Error(`Audio blocks with source_type ${block.source_type} must have mime type of audio/wav or audio/mp3`);
            }
            return {
                type: "input_audio",
                input_audio: {
                    format: mimeType.subtype,
                    data: block.data
                }
            };
        }
        throw new Error(`Audio content blocks with source_type ${block.source_type} are not supported for ChatOpenAI`);
    },
    fromStandardFileBlock (block) {
        if (block.source_type === "url") {
            const data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$content_blocks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseBase64DataUrl"])({
                dataUrl: block.url
            });
            if (!data) {
                throw new Error(`URL file blocks with source_type ${block.source_type} must be formatted as a data URL for ChatOpenAI`);
            }
            return {
                type: "file",
                file: {
                    file_data: block.url,
                    ...block.metadata?.filename || block.metadata?.name ? {
                        filename: block.metadata?.filename || block.metadata?.name
                    } : {}
                }
            };
        }
        if (block.source_type === "base64") {
            return {
                type: "file",
                file: {
                    file_data: `data:${block.mime_type ?? ""};base64,${block.data}`,
                    ...block.metadata?.filename || block.metadata?.name || block.metadata?.title ? {
                        filename: block.metadata?.filename || block.metadata?.name || block.metadata?.title
                    } : {}
                }
            };
        }
        if (block.source_type === "id") {
            return {
                type: "file",
                file: {
                    file_id: block.id
                }
            };
        }
        throw new Error(`File content blocks with source_type ${block.source_type} are not supported for ChatOpenAI`);
    }
};
function _convertMessagesToOpenAIParams(messages, model) {
    // TODO: Function messages do not support array content, fix cast
    return messages.flatMap((message)=>{
        let role = messageToOpenAIRole(message);
        if (role === "system" && isReasoningModel(model)) {
            role = "developer";
        }
        const content = typeof message.content === "string" ? message.content : message.content.map((m)=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$content_blocks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDataContentBlock"])(m)) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$content_blocks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["convertToProviderContentBlock"])(m, completionsApiContentBlockConverter);
            }
            return m;
        });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const completionParam = {
            role,
            content
        };
        if (message.name != null) {
            completionParam.name = message.name;
        }
        if (message.additional_kwargs.function_call != null) {
            completionParam.function_call = message.additional_kwargs.function_call;
            completionParam.content = "";
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$ai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isAIMessage"])(message) && !!message.tool_calls?.length) {
            completionParam.tool_calls = message.tool_calls.map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$openai_tools$2f$json_output_tools_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["convertLangChainToolCallToOpenAI"]);
            completionParam.content = "";
        } else {
            if (message.additional_kwargs.tool_calls != null) {
                completionParam.tool_calls = message.additional_kwargs.tool_calls;
            }
            if (message.tool_call_id != null) {
                completionParam.tool_call_id = message.tool_call_id;
            }
        }
        if (message.additional_kwargs.audio && typeof message.additional_kwargs.audio === "object" && "id" in message.additional_kwargs.audio) {
            const audioMessage = {
                role: "assistant",
                audio: {
                    id: message.additional_kwargs.audio.id
                }
            };
            return [
                completionParam,
                audioMessage
            ];
        }
        return completionParam;
    });
}
/** @internal */ class BaseChatOpenAI extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$language_models$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseChatModel"] {
    _llmType() {
        return "openai";
    }
    static lc_name() {
        return "ChatOpenAI";
    }
    get callKeys() {
        return [
            ...super.callKeys,
            "options",
            "function_call",
            "functions",
            "tools",
            "tool_choice",
            "promptIndex",
            "response_format",
            "seed",
            "reasoning",
            "service_tier"
        ];
    }
    get lc_secrets() {
        return {
            apiKey: "OPENAI_API_KEY",
            organization: "OPENAI_ORGANIZATION"
        };
    }
    get lc_aliases() {
        return {
            apiKey: "openai_api_key",
            modelName: "model"
        };
    }
    get lc_serializable_keys() {
        return [
            "configuration",
            "logprobs",
            "topLogprobs",
            "prefixMessages",
            "supportsStrictToolCalling",
            "modalities",
            "audio",
            "temperature",
            "maxTokens",
            "topP",
            "frequencyPenalty",
            "presencePenalty",
            "n",
            "logitBias",
            "user",
            "streaming",
            "streamUsage",
            "model",
            "modelName",
            "modelKwargs",
            "stop",
            "stopSequences",
            "timeout",
            "apiKey",
            "cache",
            "maxConcurrency",
            "maxRetries",
            "verbose",
            "callbacks",
            "tags",
            "metadata",
            "disableStreaming",
            "zdrEnabled",
            "reasoning"
        ];
    }
    getLsParams(options) {
        const params = this.invocationParams(options);
        return {
            ls_provider: "openai",
            ls_model_name: this.model,
            ls_model_type: "chat",
            ls_temperature: params.temperature ?? undefined,
            ls_max_tokens: params.max_tokens ?? undefined,
            ls_stop: options.stop
        };
    }
    /** @ignore */ _identifyingParams() {
        return {
            model_name: this.model,
            ...this.invocationParams(),
            ...this.clientConfig
        };
    }
    /**
     * Get the identifying parameters for the model
     */ identifyingParams() {
        return this._identifyingParams();
    }
    constructor(fields){
        super(fields ?? {});
        Object.defineProperty(this, "temperature", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "topP", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "frequencyPenalty", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "presencePenalty", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "n", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "logitBias", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "model", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "gpt-3.5-turbo"
        });
        Object.defineProperty(this, "modelKwargs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "stop", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "stopSequences", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "user", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "timeout", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "streaming", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "streamUsage", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
        Object.defineProperty(this, "maxTokens", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "logprobs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "topLogprobs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "apiKey", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "organization", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "__includeRawResponse", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "client", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "clientConfig", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * Whether the model supports the `strict` argument when passing in tools.
         * If `undefined` the `strict` argument will not be passed to OpenAI.
         */ Object.defineProperty(this, "supportsStrictToolCalling", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "audio", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "modalities", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "reasoning", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * Must be set to `true` in tenancies with Zero Data Retention. Setting to `true` will disable
         * output storage in the Responses API, but this DOES NOT enable Zero Data Retention in your
         * OpenAI organization or project. This must be configured directly with OpenAI.
         *
         * See:
         * https://help.openai.com/en/articles/10503543-data-residency-for-the-openai-api
         * https://platform.openai.com/docs/api-reference/responses/create#responses-create-store
         *
         * @default false
         */ Object.defineProperty(this, "zdrEnabled", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * Service tier to use for this request. Can be "auto", "default", or "flex" or "priority".
         * Specifies the service tier for prioritization and latency optimization.
         */ Object.defineProperty(this, "service_tier", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "lc_serializable", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
        this.apiKey = fields?.apiKey ?? fields?.configuration?.apiKey ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("OPENAI_API_KEY");
        this.organization = fields?.configuration?.organization ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("OPENAI_ORGANIZATION");
        this.model = fields?.model ?? fields?.modelName ?? this.model;
        this.modelKwargs = fields?.modelKwargs ?? {};
        this.timeout = fields?.timeout;
        this.temperature = fields?.temperature ?? this.temperature;
        this.topP = fields?.topP ?? this.topP;
        this.frequencyPenalty = fields?.frequencyPenalty ?? this.frequencyPenalty;
        this.presencePenalty = fields?.presencePenalty ?? this.presencePenalty;
        this.logprobs = fields?.logprobs;
        this.topLogprobs = fields?.topLogprobs;
        this.n = fields?.n ?? this.n;
        this.logitBias = fields?.logitBias;
        this.stop = fields?.stopSequences ?? fields?.stop;
        this.stopSequences = this.stop;
        this.user = fields?.user;
        this.__includeRawResponse = fields?.__includeRawResponse;
        this.audio = fields?.audio;
        this.modalities = fields?.modalities;
        this.reasoning = fields?.reasoning;
        this.maxTokens = fields?.maxCompletionTokens ?? fields?.maxTokens;
        this.disableStreaming = fields?.disableStreaming ?? this.disableStreaming;
        this.streaming = fields?.streaming ?? false;
        if (this.disableStreaming) this.streaming = false;
        this.streamUsage = fields?.streamUsage ?? this.streamUsage;
        if (this.disableStreaming) this.streamUsage = false;
        this.clientConfig = {
            apiKey: this.apiKey,
            organization: this.organization,
            dangerouslyAllowBrowser: true,
            ...fields?.configuration
        };
        // If `supportsStrictToolCalling` is explicitly set, use that value.
        // Else leave undefined so it's not passed to OpenAI.
        if (fields?.supportsStrictToolCalling !== undefined) {
            this.supportsStrictToolCalling = fields.supportsStrictToolCalling;
        }
        if (fields?.service_tier !== undefined) {
            this.service_tier = fields.service_tier;
        }
        this.zdrEnabled = fields?.zdrEnabled ?? false;
    }
    /**
     * Returns backwards compatible reasoning parameters from constructor params and call options
     * @internal
     */ _getReasoningParams(options) {
        if (!isReasoningModel(this.model)) {
            return;
        }
        // apply options in reverse order of importance -- newer options supersede older options
        let reasoning;
        if (this.reasoning !== undefined) {
            reasoning = {
                ...reasoning,
                ...this.reasoning
            };
        }
        if (options?.reasoning !== undefined) {
            reasoning = {
                ...reasoning,
                ...options.reasoning
            };
        }
        return reasoning;
    }
    /**
     * Returns an openai compatible response format from a set of options
     * @internal
     */ _getResponseFormat(resFormat) {
        if (resFormat && resFormat.type === "json_schema" && resFormat.json_schema.schema && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$types$2f$zod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInteropZodSchema"])(resFormat.json_schema.schema)) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["interopZodResponseFormat"])(resFormat.json_schema.schema, resFormat.json_schema.name, {
                description: resFormat.json_schema.description
            });
        }
        return resFormat;
    }
    _getClientOptions(options) {
        if (!this.client) {
            const openAIEndpointConfig = {
                baseURL: this.clientConfig.baseURL
            };
            const endpoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEndpoint"])(openAIEndpointConfig);
            const params = {
                ...this.clientConfig,
                baseURL: endpoint,
                timeout: this.timeout,
                maxRetries: 0
            };
            if (!params.baseURL) {
                delete params.baseURL;
            }
            this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenAI"](params);
        }
        const requestOptions = {
            ...this.clientConfig,
            ...options
        };
        return requestOptions;
    }
    // TODO: move to completions class
    _convertChatOpenAIToolToCompletionsTool(tool, fields) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$language_models$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isOpenAITool"])(tool)) {
            if (fields?.strict !== undefined) {
                return {
                    ...tool,
                    function: {
                        ...tool.function,
                        strict: fields.strict
                    }
                };
            }
            return tool;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$tools$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_convertToOpenAITool"])(tool, fields);
    }
    bindTools(tools, kwargs) {
        let strict;
        if (kwargs?.strict !== undefined) {
            strict = kwargs.strict;
        } else if (this.supportsStrictToolCalling !== undefined) {
            strict = this.supportsStrictToolCalling;
        }
        return this.withConfig({
            tools: tools.map((tool)=>isBuiltInTool(tool) ? tool : this._convertChatOpenAIToolToCompletionsTool(tool, {
                    strict
                })),
            ...kwargs
        });
    }
    /** @ignore */ _combineLLMOutput(...llmOutputs) {
        return llmOutputs.reduce((acc, llmOutput)=>{
            if (llmOutput && llmOutput.tokenUsage) {
                acc.tokenUsage.completionTokens += llmOutput.tokenUsage.completionTokens ?? 0;
                acc.tokenUsage.promptTokens += llmOutput.tokenUsage.promptTokens ?? 0;
                acc.tokenUsage.totalTokens += llmOutput.tokenUsage.totalTokens ?? 0;
            }
            return acc;
        }, {
            tokenUsage: {
                completionTokens: 0,
                promptTokens: 0,
                totalTokens: 0
            }
        });
    }
    async getNumTokensFromMessages(messages) {
        let totalCount = 0;
        let tokensPerMessage = 0;
        let tokensPerName = 0;
        // From: https://github.com/openai/openai-cookbook/blob/main/examples/How_to_format_inputs_to_ChatGPT_models.ipynb
        if (this.model === "gpt-3.5-turbo-0301") {
            tokensPerMessage = 4;
            tokensPerName = -1;
        } else {
            tokensPerMessage = 3;
            tokensPerName = 1;
        }
        const countPerMessage = await Promise.all(messages.map(async (message)=>{
            const textCount = await this.getNumTokens(message.content);
            const roleCount = await this.getNumTokens(messageToOpenAIRole(message));
            const nameCount = message.name !== undefined ? tokensPerName + await this.getNumTokens(message.name) : 0;
            let count = textCount + tokensPerMessage + roleCount + nameCount;
            // From: https://github.com/hmarr/openai-chat-tokens/blob/main/src/index.ts messageTokenEstimate
            const openAIMessage = message;
            if (openAIMessage._getType() === "function") {
                count -= 2;
            }
            if (openAIMessage.additional_kwargs?.function_call) {
                count += 3;
            }
            if (openAIMessage?.additional_kwargs.function_call?.name) {
                count += await this.getNumTokens(openAIMessage.additional_kwargs.function_call?.name);
            }
            if (openAIMessage.additional_kwargs.function_call?.arguments) {
                try {
                    count += await this.getNumTokens(// Remove newlines and spaces
                    JSON.stringify(JSON.parse(openAIMessage.additional_kwargs.function_call?.arguments)));
                } catch (error) {
                    console.error("Error parsing function arguments", error, JSON.stringify(openAIMessage.additional_kwargs.function_call));
                    count += await this.getNumTokens(openAIMessage.additional_kwargs.function_call?.arguments);
                }
            }
            totalCount += count;
            return count;
        }));
        totalCount += 3; // every reply is primed with <|start|>assistant<|message|>
        return {
            totalCount,
            countPerMessage
        };
    }
    /** @internal */ async _getNumTokensFromGenerations(generations) {
        const generationUsages = await Promise.all(generations.map(async (generation)=>{
            if (generation.message.additional_kwargs?.function_call) {
                return (await this.getNumTokensFromMessages([
                    generation.message
                ])).countPerMessage[0];
            } else {
                return await this.getNumTokens(generation.message.content);
            }
        }));
        return generationUsages.reduce((a, b)=>a + b, 0);
    }
    /** @internal */ async _getEstimatedTokenCountFromPrompt(messages, functions, function_call) {
        // It appears that if functions are present, the first system message is padded with a trailing newline. This
        // was inferred by trying lots of combinations of messages and functions and seeing what the token counts were.
        let tokens = (await this.getNumTokensFromMessages(messages)).totalCount;
        // If there are functions, add the function definitions as they count towards token usage
        if (functions && function_call !== "auto") {
            const promptDefinitions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2d$format$2d$fndef$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatFunctionDefinitions"])(functions);
            tokens += await this.getNumTokens(promptDefinitions);
            tokens += 9; // Add nine per completion
        }
        // If there's a system message _and_ functions are present, subtract four tokens. I assume this is because
        // functions typically add a system message, but reuse the first one if it's already there. This offsets
        // the extra 9 tokens added by the function definitions.
        if (functions && messages.find((m)=>m._getType() === "system")) {
            tokens -= 4;
        }
        // If function_call is 'none', add one token.
        // If it's a FunctionCall object, add 4 + the number of tokens in the function name.
        // If it's undefined or 'auto', don't add anything.
        if (function_call === "none") {
            tokens += 1;
        } else if (typeof function_call === "object") {
            tokens += await this.getNumTokens(function_call.name) + 4;
        }
        return tokens;
    }
    withStructuredOutput(outputSchema, config) {
        // ):
        // | Runnable<BaseLanguageModelInput, RunOutput>
        // | Runnable<
        //     BaseLanguageModelInput,
        //     { raw: BaseMessage; parsed: RunOutput }
        //   > {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let schema;
        let name;
        let method;
        let includeRaw;
        if (isStructuredOutputMethodParams(outputSchema)) {
            schema = outputSchema.schema;
            name = outputSchema.name;
            method = outputSchema.method;
            includeRaw = outputSchema.includeRaw;
        } else {
            schema = outputSchema;
            name = config?.name;
            method = config?.method;
            includeRaw = config?.includeRaw;
        }
        let llm;
        let outputParser;
        if (config?.strict !== undefined && method === "jsonMode") {
            throw new Error("Argument `strict` is only supported for `method` = 'function_calling'");
        }
        if (!this.model.startsWith("gpt-3") && !this.model.startsWith("gpt-4-") && this.model !== "gpt-4") {
            if (method === undefined) {
                method = "jsonSchema";
            }
        } else if (method === "jsonSchema") {
            console.warn(`[WARNING]: JSON Schema is not supported for model "${this.model}". Falling back to tool calling.`);
        }
        if (method === "jsonMode") {
            let outputFormatSchema;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$types$2f$zod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInteropZodSchema"])(schema)) {
                outputParser = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$structured$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["StructuredOutputParser"].fromZodSchema(schema);
                outputFormatSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$json_schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])(schema);
            } else {
                outputParser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["JsonOutputParser"]();
            }
            llm = this.withConfig({
                response_format: {
                    type: "json_object"
                },
                ls_structured_output_format: {
                    kwargs: {
                        method: "jsonMode"
                    },
                    schema: outputFormatSchema
                }
            });
        } else if (method === "jsonSchema") {
            llm = this.withConfig({
                response_format: {
                    type: "json_schema",
                    json_schema: {
                        name: name ?? "extract",
                        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$types$2f$zod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getSchemaDescription"])(schema),
                        schema,
                        strict: config?.strict
                    }
                },
                ls_structured_output_format: {
                    kwargs: {
                        method: "jsonSchema"
                    },
                    schema: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$json_schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])(schema)
                }
            });
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$types$2f$zod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInteropZodSchema"])(schema)) {
                const altParser = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$structured$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["StructuredOutputParser"].fromZodSchema(schema);
                outputParser = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$runnables$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RunnableLambda"].from((aiMessage)=>{
                    if ("parsed" in aiMessage.additional_kwargs) {
                        return aiMessage.additional_kwargs.parsed;
                    }
                    return altParser;
                });
            } else {
                outputParser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$json$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["JsonOutputParser"]();
            }
        } else {
            let functionName = name ?? "extract";
            // Is function calling
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$types$2f$zod$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInteropZodSchema"])(schema)) {
                const asJsonSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$json_schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])(schema);
                llm = this.withConfig({
                    tools: [
                        {
                            type: "function",
                            function: {
                                name: functionName,
                                description: asJsonSchema.description,
                                parameters: asJsonSchema
                            }
                        }
                    ],
                    tool_choice: {
                        type: "function",
                        function: {
                            name: functionName
                        }
                    },
                    ls_structured_output_format: {
                        kwargs: {
                            method: "functionCalling"
                        },
                        schema: asJsonSchema
                    },
                    // Do not pass `strict` argument to OpenAI if `config.strict` is undefined
                    ...config?.strict !== undefined ? {
                        strict: config.strict
                    } : {}
                });
                outputParser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$openai_tools$2f$json_output_tools_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["JsonOutputKeyToolsParser"]({
                    returnSingle: true,
                    keyName: functionName,
                    zodSchema: schema
                });
            } else {
                let openAIFunctionDefinition;
                if (typeof schema.name === "string" && typeof schema.parameters === "object" && schema.parameters != null) {
                    openAIFunctionDefinition = schema;
                    functionName = schema.name;
                } else {
                    functionName = schema.title ?? functionName;
                    openAIFunctionDefinition = {
                        name: functionName,
                        description: schema.description ?? "",
                        parameters: schema
                    };
                }
                llm = this.withConfig({
                    tools: [
                        {
                            type: "function",
                            function: openAIFunctionDefinition
                        }
                    ],
                    tool_choice: {
                        type: "function",
                        function: {
                            name: functionName
                        }
                    },
                    ls_structured_output_format: {
                        kwargs: {
                            method: "functionCalling"
                        },
                        schema: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$json_schema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toJsonSchema"])(schema)
                    },
                    // Do not pass `strict` argument to OpenAI if `config.strict` is undefined
                    ...config?.strict !== undefined ? {
                        strict: config.strict
                    } : {}
                });
                outputParser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$openai_tools$2f$json_output_tools_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["JsonOutputKeyToolsParser"]({
                    returnSingle: true,
                    keyName: functionName
                });
            }
        }
        if (!includeRaw) {
            return llm.pipe(outputParser);
        }
        const parserAssign = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$runnables$2f$passthrough$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RunnablePassthrough"].assign({
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            parsed: (input, config)=>outputParser.invoke(input.raw, config)
        });
        const parserNone = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$runnables$2f$passthrough$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RunnablePassthrough"].assign({
            parsed: ()=>null
        });
        const parsedWithFallback = parserAssign.withFallbacks({
            fallbacks: [
                parserNone
            ]
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$runnables$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RunnableSequence"].from([
            {
                raw: llm
            },
            parsedWithFallback
        ]);
    }
}
class ChatOpenAIResponses extends BaseChatOpenAI {
    invocationParams(options) {
        let strict;
        if (options?.strict !== undefined) {
            strict = options.strict;
        } else if (this.supportsStrictToolCalling !== undefined) {
            strict = this.supportsStrictToolCalling;
        }
        const params = {
            model: this.model,
            temperature: this.temperature,
            top_p: this.topP,
            user: this.user,
            // if include_usage is set or streamUsage then stream must be set to true.
            stream: this.streaming,
            previous_response_id: options?.previous_response_id,
            truncation: options?.truncation,
            include: options?.include,
            tools: options?.tools?.length ? this._reduceChatOpenAITools(options.tools, {
                stream: this.streaming,
                strict
            }) : undefined,
            tool_choice: isBuiltInToolChoice(options?.tool_choice) ? options?.tool_choice : (()=>{
                const formatted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["formatToOpenAIToolChoice"])(options?.tool_choice);
                if (typeof formatted === "object" && "type" in formatted) {
                    return {
                        type: "function",
                        name: formatted.function.name
                    };
                } else {
                    return undefined;
                }
            })(),
            text: (()=>{
                if (options?.text) return options.text;
                const format = this._getResponseFormat(options?.response_format);
                if (format?.type === "json_schema") {
                    if (format.json_schema.schema != null) {
                        return {
                            format: {
                                type: "json_schema",
                                schema: format.json_schema.schema,
                                description: format.json_schema.description,
                                name: format.json_schema.name,
                                strict: format.json_schema.strict
                            }
                        };
                    }
                    return undefined;
                }
                return {
                    format
                };
            })(),
            parallel_tool_calls: options?.parallel_tool_calls,
            max_output_tokens: this.maxTokens === -1 ? undefined : this.maxTokens,
            ...this.zdrEnabled ? {
                store: false
            } : {},
            ...this.modelKwargs
        };
        const reasoning = this._getReasoningParams(options);
        if (reasoning !== undefined) {
            params.reasoning = reasoning;
        }
        return params;
    }
    async _generate(messages, options) {
        const invocationParams = this.invocationParams(options);
        if (invocationParams.stream) {
            const stream = this._streamResponseChunks(messages, options);
            let finalChunk;
            for await (const chunk of stream){
                chunk.message.response_metadata = {
                    ...chunk.generationInfo,
                    ...chunk.message.response_metadata
                };
                finalChunk = finalChunk?.concat(chunk) ?? chunk;
            }
            return {
                generations: finalChunk ? [
                    finalChunk
                ] : [],
                llmOutput: {
                    estimatedTokenUsage: finalChunk?.message?.usage_metadata
                }
            };
        } else {
            const input = this._convertMessagesToResponsesParams(messages);
            const data = await this.completionWithRetry({
                input,
                ...invocationParams,
                stream: false
            }, {
                signal: options?.signal,
                ...options?.options
            });
            return {
                generations: [
                    {
                        text: data.output_text,
                        message: this._convertResponsesMessageToBaseMessage(data)
                    }
                ],
                llmOutput: {
                    id: data.id,
                    estimatedTokenUsage: data.usage ? {
                        promptTokens: data.usage.input_tokens,
                        completionTokens: data.usage.output_tokens,
                        totalTokens: data.usage.total_tokens
                    } : undefined
                }
            };
        }
    }
    async *_streamResponseChunks(messages, options) {
        const streamIterable = await this.completionWithRetry({
            ...this.invocationParams(options),
            input: this._convertMessagesToResponsesParams(messages),
            stream: true
        }, options);
        for await (const data of streamIterable){
            const chunk = this._convertResponsesDeltaToBaseMessageChunk(data);
            if (chunk == null) continue;
            yield chunk;
        }
    }
    async completionWithRetry(request, requestOptions) {
        return this.caller.call(async ()=>{
            const clientOptions = this._getClientOptions(requestOptions);
            try {
                // use parse if dealing with json_schema
                if (request.text?.format?.type === "json_schema" && !request.stream) {
                    return await this.client.responses.parse(request, clientOptions);
                }
                return await this.client.responses.create(request, clientOptions);
            } catch (e) {
                const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["wrapOpenAIClientError"])(e);
                throw error;
            }
        });
    }
    /** @internal */ _convertResponsesMessageToBaseMessage(response) {
        if (response.error) {
            // TODO: add support for `addLangChainErrorFields`
            const error = new Error(response.error.message);
            error.name = response.error.code;
            throw error;
        }
        let messageId;
        const content = [];
        const tool_calls = [];
        const invalid_tool_calls = [];
        const response_metadata = {
            model: response.model,
            created_at: response.created_at,
            id: response.id,
            incomplete_details: response.incomplete_details,
            metadata: response.metadata,
            object: response.object,
            status: response.status,
            user: response.user,
            service_tier: response.service_tier,
            // for compatibility with chat completion calls.
            model_name: response.model
        };
        const additional_kwargs = {};
        for (const item of response.output){
            if (item.type === "message") {
                messageId = item.id;
                content.push(...item.content.flatMap((part)=>{
                    if (part.type === "output_text") {
                        if ("parsed" in part && part.parsed != null) {
                            additional_kwargs.parsed = part.parsed;
                        }
                        return {
                            type: "text",
                            text: part.text,
                            annotations: part.annotations
                        };
                    }
                    if (part.type === "refusal") {
                        additional_kwargs.refusal = part.refusal;
                        return [];
                    }
                    return part;
                }));
            } else if (item.type === "function_call") {
                const fnAdapter = {
                    function: {
                        name: item.name,
                        arguments: item.arguments
                    },
                    id: item.call_id
                };
                try {
                    tool_calls.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$openai_tools$2f$json_output_tools_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseToolCall"])(fnAdapter, {
                        returnId: true
                    }));
                } catch (e) {
                    let errMessage;
                    if (typeof e === "object" && e != null && "message" in e && typeof e.message === "string") {
                        errMessage = e.message;
                    }
                    invalid_tool_calls.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$openai_tools$2f$json_output_tools_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["makeInvalidToolCall"])(fnAdapter, errMessage));
                }
                additional_kwargs[_FUNCTION_CALL_IDS_MAP_KEY] ??= {};
                if (item.id) {
                    additional_kwargs[_FUNCTION_CALL_IDS_MAP_KEY][item.call_id] = item.id;
                }
            } else if (item.type === "reasoning") {
                additional_kwargs.reasoning = item;
            } else {
                additional_kwargs.tool_outputs ??= [];
                additional_kwargs.tool_outputs.push(item);
            }
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$ai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AIMessage"]({
            id: messageId,
            content,
            tool_calls,
            invalid_tool_calls,
            usage_metadata: response.usage,
            additional_kwargs,
            response_metadata
        });
    }
    /** @internal */ _convertResponsesDeltaToBaseMessageChunk(chunk) {
        const content = [];
        let generationInfo = {};
        let usage_metadata;
        const tool_call_chunks = [];
        const response_metadata = {};
        const additional_kwargs = {};
        let id;
        if (chunk.type === "response.output_text.delta") {
            content.push({
                type: "text",
                text: chunk.delta,
                index: chunk.content_index
            });
        } else if (chunk.type === "response.output_text_annotation.added") {
            content.push({
                type: "text",
                text: "",
                annotations: [
                    chunk.annotation
                ],
                index: chunk.content_index
            });
        } else if (chunk.type === "response.output_item.added" && chunk.item.type === "message") {
            id = chunk.item.id;
        } else if (chunk.type === "response.output_item.added" && chunk.item.type === "function_call") {
            tool_call_chunks.push({
                type: "tool_call_chunk",
                name: chunk.item.name,
                args: chunk.item.arguments,
                id: chunk.item.call_id,
                index: chunk.output_index
            });
            additional_kwargs[_FUNCTION_CALL_IDS_MAP_KEY] = {
                [chunk.item.call_id]: chunk.item.id
            };
        } else if (chunk.type === "response.output_item.done" && [
            "web_search_call",
            "file_search_call",
            "computer_call",
            "code_interpreter_call",
            "mcp_call",
            "mcp_list_tools",
            "mcp_approval_request",
            "image_generation_call"
        ].includes(chunk.item.type)) {
            additional_kwargs.tool_outputs = [
                chunk.item
            ];
        } else if (chunk.type === "response.created") {
            response_metadata.id = chunk.response.id;
            response_metadata.model_name = chunk.response.model;
            response_metadata.model = chunk.response.model;
        } else if (chunk.type === "response.completed") {
            const msg = this._convertResponsesMessageToBaseMessage(chunk.response);
            usage_metadata = chunk.response.usage;
            if (chunk.response.text?.format?.type === "json_schema") {
                additional_kwargs.parsed ??= JSON.parse(msg.text);
            }
            for (const [key, value] of Object.entries(chunk.response)){
                if (key !== "id") response_metadata[key] = value;
            }
        } else if (chunk.type === "response.function_call_arguments.delta") {
            tool_call_chunks.push({
                type: "tool_call_chunk",
                args: chunk.delta,
                index: chunk.output_index
            });
        } else if (chunk.type === "response.web_search_call.completed" || chunk.type === "response.file_search_call.completed") {
            generationInfo = {
                tool_outputs: {
                    id: chunk.item_id,
                    type: chunk.type.replace("response.", "").replace(".completed", ""),
                    status: "completed"
                }
            };
        } else if (chunk.type === "response.refusal.done") {
            additional_kwargs.refusal = chunk.refusal;
        } else if (chunk.type === "response.output_item.added" && "item" in chunk && chunk.item.type === "reasoning") {
            const summary = chunk.item.summary ? chunk.item.summary.map((s, index)=>({
                    ...s,
                    index
                })) : undefined;
            additional_kwargs.reasoning = {
                // We only capture ID in the first chunk or else the concatenated result of all chunks will
                // have an ID field that is repeated once per chunk. There is special handling for the `type`
                // field that prevents this, however.
                id: chunk.item.id,
                type: chunk.item.type,
                ...summary ? {
                    summary
                } : {}
            };
        } else if (chunk.type === "response.reasoning_summary_part.added") {
            additional_kwargs.reasoning = {
                type: "reasoning",
                summary: [
                    {
                        ...chunk.part,
                        index: chunk.summary_index
                    }
                ]
            };
        } else if (chunk.type === "response.reasoning_summary_text.delta") {
            additional_kwargs.reasoning = {
                type: "reasoning",
                summary: [
                    {
                        text: chunk.delta,
                        type: "summary_text",
                        index: chunk.summary_index
                    }
                ]
            };
        } else if (chunk.type === "response.image_generation_call.partial_image") {
            // noop/fixme: retaining partial images in a message chunk means that _all_
            // partial images get kept in history, so we don't do anything here.
            return null;
        } else {
            return null;
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$outputs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ChatGenerationChunk"]({
            // Legacy reasons, `onLLMNewToken` should pulls this out
            text: content.map((part)=>part.text).join(""),
            message: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$ai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AIMessageChunk"]({
                id,
                content,
                tool_call_chunks,
                usage_metadata,
                additional_kwargs,
                response_metadata
            }),
            generationInfo
        });
    }
    /** @internal */ _convertMessagesToResponsesParams(messages) {
        return messages.flatMap((lcMsg)=>{
            const additional_kwargs = lcMsg.additional_kwargs;
            let role = messageToOpenAIRole(lcMsg);
            if (role === "system" && isReasoningModel(this.model)) role = "developer";
            if (role === "function") {
                throw new Error("Function messages are not supported in Responses API");
            }
            if (role === "tool") {
                const toolMessage = lcMsg;
                // Handle computer call output
                if (additional_kwargs?.type === "computer_call_output") {
                    const output = (()=>{
                        if (typeof toolMessage.content === "string") {
                            return {
                                type: "computer_screenshot",
                                image_url: toolMessage.content
                            };
                        }
                        if (Array.isArray(toolMessage.content)) {
                            const oaiScreenshot = toolMessage.content.find((i)=>i.type === "computer_screenshot");
                            if (oaiScreenshot) return oaiScreenshot;
                            const lcImage = toolMessage.content.find((i)=>i.type === "image_url");
                            if (lcImage) {
                                return {
                                    type: "computer_screenshot",
                                    image_url: typeof lcImage.image_url === "string" ? lcImage.image_url : lcImage.image_url.url
                                };
                            }
                        }
                        throw new Error("Invalid computer call output");
                    })();
                    return {
                        type: "computer_call_output",
                        output,
                        call_id: toolMessage.tool_call_id
                    };
                }
                return {
                    type: "function_call_output",
                    call_id: toolMessage.tool_call_id,
                    id: toolMessage.id?.startsWith("fc_") ? toolMessage.id : undefined,
                    output: typeof toolMessage.content !== "string" ? JSON.stringify(toolMessage.content) : toolMessage.content
                };
            }
            if (role === "assistant") {
                // if we have the original response items, just reuse them
                if (!this.zdrEnabled && lcMsg.response_metadata.output != null && Array.isArray(lcMsg.response_metadata.output) && lcMsg.response_metadata.output.length > 0 && lcMsg.response_metadata.output.every((item)=>"type" in item)) {
                    return lcMsg.response_metadata.output;
                }
                // otherwise, try to reconstruct the response from what we have
                const input = [];
                // reasoning items
                if (additional_kwargs?.reasoning && !this.zdrEnabled) {
                    const reasoningItem = this._convertReasoningSummary(additional_kwargs.reasoning);
                    input.push(reasoningItem);
                }
                // ai content
                let { content } = lcMsg;
                if (additional_kwargs?.refusal) {
                    if (typeof content === "string") {
                        content = [
                            {
                                type: "output_text",
                                text: content,
                                annotations: []
                            }
                        ];
                    }
                    content = [
                        ...content,
                        {
                            type: "refusal",
                            refusal: additional_kwargs.refusal
                        }
                    ];
                }
                input.push({
                    type: "message",
                    role: "assistant",
                    ...lcMsg.id && !this.zdrEnabled && lcMsg.id.startsWith("msg_") ? {
                        id: lcMsg.id
                    } : {},
                    content: typeof content === "string" ? content : content.flatMap((item)=>{
                        if (item.type === "text") {
                            return {
                                type: "output_text",
                                text: item.text,
                                // @ts-expect-error TODO: add types for `annotations`
                                annotations: item.annotations ?? []
                            };
                        }
                        if (item.type === "output_text" || item.type === "refusal") {
                            return item;
                        }
                        return [];
                    })
                });
                const functionCallIds = additional_kwargs?.[_FUNCTION_CALL_IDS_MAP_KEY];
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$ai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isAIMessage"])(lcMsg) && !!lcMsg.tool_calls?.length) {
                    input.push(...lcMsg.tool_calls.map((toolCall)=>({
                            type: "function_call",
                            name: toolCall.name,
                            arguments: JSON.stringify(toolCall.args),
                            call_id: toolCall.id,
                            ...this.zdrEnabled ? {
                                id: functionCallIds?.[toolCall.id]
                            } : {}
                        })));
                } else if (additional_kwargs?.tool_calls) {
                    input.push(...additional_kwargs.tool_calls.map((toolCall)=>({
                            type: "function_call",
                            name: toolCall.function.name,
                            call_id: toolCall.id,
                            arguments: toolCall.function.arguments,
                            ...this.zdrEnabled ? {
                                id: functionCallIds?.[toolCall.id]
                            } : {}
                        })));
                }
                const toolOutputs = lcMsg.response_metadata.output?.length ? lcMsg.response_metadata.output : additional_kwargs.tool_outputs;
                const fallthroughCallTypes = [
                    "computer_call",
                    "mcp_call",
                    "code_interpreter_call",
                    "image_generation_call"
                ];
                if (toolOutputs != null) {
                    const castToolOutputs = toolOutputs;
                    const fallthroughCalls = castToolOutputs?.filter((item)=>fallthroughCallTypes.includes(item.type));
                    if (fallthroughCalls.length > 0) input.push(...fallthroughCalls);
                }
                return input;
            }
            if (role === "user" || role === "system" || role === "developer") {
                if (typeof lcMsg.content === "string") {
                    return {
                        type: "message",
                        role,
                        content: lcMsg.content
                    };
                }
                const messages = [];
                const content = lcMsg.content.flatMap((item)=>{
                    if (item.type === "mcp_approval_response") {
                        messages.push({
                            type: "mcp_approval_response",
                            approval_request_id: item.approval_request_id,
                            approve: item.approve
                        });
                    }
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$content_blocks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDataContentBlock"])(item)) {
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$content_blocks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["convertToProviderContentBlock"])(item, completionsApiContentBlockConverter);
                    }
                    if (item.type === "text") {
                        return {
                            type: "input_text",
                            text: item.text
                        };
                    }
                    if (item.type === "image_url") {
                        return {
                            type: "input_image",
                            image_url: typeof item.image_url === "string" ? item.image_url : item.image_url.url,
                            detail: typeof item.image_url === "string" ? "auto" : item.image_url.detail
                        };
                    }
                    if (item.type === "input_text" || item.type === "input_image" || item.type === "input_file") {
                        return item;
                    }
                    return [];
                });
                if (content.length > 0) {
                    messages.push({
                        type: "message",
                        role,
                        content
                    });
                }
                return messages;
            }
            console.warn(`Unsupported role found when converting to OpenAI Responses API: ${role}`);
            return [];
        });
    }
    /** @internal */ _convertReasoningSummary(reasoning) {
        // combine summary parts that have the the same index and then remove the indexes
        const summary = (reasoning.summary.length > 1 ? reasoning.summary.reduce((acc, curr)=>{
            const last = acc.at(-1);
            if (last.index === curr.index) {
                last.text += curr.text;
            } else {
                acc.push(curr);
            }
            return acc;
        }, [
            {
                ...reasoning.summary[0]
            }
        ]) : reasoning.summary).map((s)=>Object.fromEntries(Object.entries(s).filter(([k])=>k !== "index")));
        return {
            ...reasoning,
            summary
        };
    }
    /** @internal */ _reduceChatOpenAITools(tools, fields) {
        const reducedTools = [];
        for (const tool of tools){
            if (isBuiltInTool(tool)) {
                if (tool.type === "image_generation" && fields?.stream) {
                    // OpenAI sends a 400 error if partial_images is not set and we want to stream.
                    // We also set it to 1 since we don't support partial images yet.
                    tool.partial_images = 1;
                }
                reducedTools.push(tool);
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$language_models$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isOpenAITool"])(tool)) {
                reducedTools.push({
                    type: "function",
                    name: tool.function.name,
                    parameters: tool.function.parameters,
                    description: tool.function.description,
                    strict: fields?.strict ?? null
                });
            }
        }
        return reducedTools;
    }
}
class ChatOpenAICompletions extends BaseChatOpenAI {
    /** @internal */ invocationParams(options, extra) {
        let strict;
        if (options?.strict !== undefined) {
            strict = options.strict;
        } else if (this.supportsStrictToolCalling !== undefined) {
            strict = this.supportsStrictToolCalling;
        }
        let streamOptionsConfig = {};
        if (options?.stream_options !== undefined) {
            streamOptionsConfig = {
                stream_options: options.stream_options
            };
        } else if (this.streamUsage && (this.streaming || extra?.streaming)) {
            streamOptionsConfig = {
                stream_options: {
                    include_usage: true
                }
            };
        }
        const params = {
            model: this.model,
            temperature: this.temperature,
            top_p: this.topP,
            frequency_penalty: this.frequencyPenalty,
            presence_penalty: this.presencePenalty,
            logprobs: this.logprobs,
            top_logprobs: this.topLogprobs,
            n: this.n,
            logit_bias: this.logitBias,
            stop: options?.stop ?? this.stopSequences,
            user: this.user,
            // if include_usage is set or streamUsage then stream must be set to true.
            stream: this.streaming,
            functions: options?.functions,
            function_call: options?.function_call,
            tools: options?.tools?.length ? options.tools.map((tool)=>this._convertChatOpenAIToolToCompletionsTool(tool, {
                    strict
                })) : undefined,
            tool_choice: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["formatToOpenAIToolChoice"])(options?.tool_choice),
            response_format: this._getResponseFormat(options?.response_format),
            seed: options?.seed,
            ...streamOptionsConfig,
            parallel_tool_calls: options?.parallel_tool_calls,
            ...this.audio || options?.audio ? {
                audio: this.audio || options?.audio
            } : {},
            ...this.modalities || options?.modalities ? {
                modalities: this.modalities || options?.modalities
            } : {},
            ...this.modelKwargs
        };
        if (options?.prediction !== undefined) {
            params.prediction = options.prediction;
        }
        if (this.service_tier !== undefined) {
            params.service_tier = this.service_tier;
        }
        if (options?.service_tier !== undefined) {
            params.service_tier = options.service_tier;
        }
        const reasoning = this._getReasoningParams(options);
        if (reasoning !== undefined && reasoning.effort !== undefined) {
            params.reasoning_effort = reasoning.effort;
        }
        if (isReasoningModel(params.model)) {
            params.max_completion_tokens = this.maxTokens === -1 ? undefined : this.maxTokens;
        } else {
            params.max_tokens = this.maxTokens === -1 ? undefined : this.maxTokens;
        }
        return params;
    }
    async _generate(messages, options, runManager) {
        const usageMetadata = {};
        const params = this.invocationParams(options);
        const messagesMapped = _convertMessagesToOpenAIParams(messages, this.model);
        if (params.stream) {
            const stream = this._streamResponseChunks(messages, options, runManager);
            const finalChunks = {};
            for await (const chunk of stream){
                chunk.message.response_metadata = {
                    ...chunk.generationInfo,
                    ...chunk.message.response_metadata
                };
                const index = chunk.generationInfo?.completion ?? 0;
                if (finalChunks[index] === undefined) {
                    finalChunks[index] = chunk;
                } else {
                    finalChunks[index] = finalChunks[index].concat(chunk);
                }
            }
            const generations = Object.entries(finalChunks).sort(([aKey], [bKey])=>parseInt(aKey, 10) - parseInt(bKey, 10)).map(([_, value])=>value);
            const { functions, function_call } = this.invocationParams(options);
            // OpenAI does not support token usage report under stream mode,
            // fallback to estimation.
            const promptTokenUsage = await this._getEstimatedTokenCountFromPrompt(messages, functions, function_call);
            const completionTokenUsage = await this._getNumTokensFromGenerations(generations);
            usageMetadata.input_tokens = promptTokenUsage;
            usageMetadata.output_tokens = completionTokenUsage;
            usageMetadata.total_tokens = promptTokenUsage + completionTokenUsage;
            return {
                generations,
                llmOutput: {
                    estimatedTokenUsage: {
                        promptTokens: usageMetadata.input_tokens,
                        completionTokens: usageMetadata.output_tokens,
                        totalTokens: usageMetadata.total_tokens
                    }
                }
            };
        } else {
            const data = await this.completionWithRetry({
                ...params,
                stream: false,
                messages: messagesMapped
            }, {
                signal: options?.signal,
                ...options?.options
            });
            const { completion_tokens: completionTokens, prompt_tokens: promptTokens, total_tokens: totalTokens, prompt_tokens_details: promptTokensDetails, completion_tokens_details: completionTokensDetails } = data?.usage ?? {};
            if (completionTokens) {
                usageMetadata.output_tokens = (usageMetadata.output_tokens ?? 0) + completionTokens;
            }
            if (promptTokens) {
                usageMetadata.input_tokens = (usageMetadata.input_tokens ?? 0) + promptTokens;
            }
            if (totalTokens) {
                usageMetadata.total_tokens = (usageMetadata.total_tokens ?? 0) + totalTokens;
            }
            if (promptTokensDetails?.audio_tokens !== null || promptTokensDetails?.cached_tokens !== null) {
                usageMetadata.input_token_details = {
                    ...promptTokensDetails?.audio_tokens !== null && {
                        audio: promptTokensDetails?.audio_tokens
                    },
                    ...promptTokensDetails?.cached_tokens !== null && {
                        cache_read: promptTokensDetails?.cached_tokens
                    }
                };
            }
            if (completionTokensDetails?.audio_tokens !== null || completionTokensDetails?.reasoning_tokens !== null) {
                usageMetadata.output_token_details = {
                    ...completionTokensDetails?.audio_tokens !== null && {
                        audio: completionTokensDetails?.audio_tokens
                    },
                    ...completionTokensDetails?.reasoning_tokens !== null && {
                        reasoning: completionTokensDetails?.reasoning_tokens
                    }
                };
            }
            const generations = [];
            for (const part of data?.choices ?? []){
                const text = part.message?.content ?? "";
                const generation = {
                    text,
                    message: this._convertCompletionsMessageToBaseMessage(part.message ?? {
                        role: "assistant"
                    }, data)
                };
                generation.generationInfo = {
                    ...part.finish_reason ? {
                        finish_reason: part.finish_reason
                    } : {},
                    ...part.logprobs ? {
                        logprobs: part.logprobs
                    } : {}
                };
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$ai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isAIMessage"])(generation.message)) {
                    generation.message.usage_metadata = usageMetadata;
                }
                // Fields are not serialized unless passed to the constructor
                // Doing this ensures all fields on the message are serialized
                generation.message = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$ai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AIMessage"](Object.fromEntries(Object.entries(generation.message).filter(([key])=>!key.startsWith("lc_"))));
                generations.push(generation);
            }
            return {
                generations,
                llmOutput: {
                    tokenUsage: {
                        promptTokens: usageMetadata.input_tokens,
                        completionTokens: usageMetadata.output_tokens,
                        totalTokens: usageMetadata.total_tokens
                    }
                }
            };
        }
    }
    async *_streamResponseChunks(messages, options, runManager) {
        const messagesMapped = _convertMessagesToOpenAIParams(messages, this.model);
        const params = {
            ...this.invocationParams(options, {
                streaming: true
            }),
            messages: messagesMapped,
            stream: true
        };
        let defaultRole;
        const streamIterable = await this.completionWithRetry(params, options);
        let usage;
        for await (const data of streamIterable){
            const choice = data?.choices?.[0];
            if (data.usage) {
                usage = data.usage;
            }
            if (!choice) {
                continue;
            }
            const { delta } = choice;
            if (!delta) {
                continue;
            }
            const chunk = this._convertCompletionsDeltaToBaseMessageChunk(delta, data, defaultRole);
            defaultRole = delta.role ?? defaultRole;
            const newTokenIndices = {
                prompt: options.promptIndex ?? 0,
                completion: choice.index ?? 0
            };
            if (typeof chunk.content !== "string") {
                console.log("[WARNING]: Received non-string content from OpenAI. This is currently not supported.");
                continue;
            }
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const generationInfo = {
                ...newTokenIndices
            };
            if (choice.finish_reason != null) {
                generationInfo.finish_reason = choice.finish_reason;
                // Only include system fingerprint in the last chunk for now
                // to avoid concatenation issues
                generationInfo.system_fingerprint = data.system_fingerprint;
                generationInfo.model_name = data.model;
                generationInfo.service_tier = data.service_tier;
            }
            if (this.logprobs) {
                generationInfo.logprobs = choice.logprobs;
            }
            const generationChunk = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$outputs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ChatGenerationChunk"]({
                message: chunk,
                text: chunk.content,
                generationInfo
            });
            yield generationChunk;
            await runManager?.handleLLMNewToken(generationChunk.text ?? "", newTokenIndices, undefined, undefined, undefined, {
                chunk: generationChunk
            });
        }
        if (usage) {
            const inputTokenDetails = {
                ...usage.prompt_tokens_details?.audio_tokens !== null && {
                    audio: usage.prompt_tokens_details?.audio_tokens
                },
                ...usage.prompt_tokens_details?.cached_tokens !== null && {
                    cache_read: usage.prompt_tokens_details?.cached_tokens
                }
            };
            const outputTokenDetails = {
                ...usage.completion_tokens_details?.audio_tokens !== null && {
                    audio: usage.completion_tokens_details?.audio_tokens
                },
                ...usage.completion_tokens_details?.reasoning_tokens !== null && {
                    reasoning: usage.completion_tokens_details?.reasoning_tokens
                }
            };
            const generationChunk = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$outputs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ChatGenerationChunk"]({
                message: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$ai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AIMessageChunk"]({
                    content: "",
                    response_metadata: {
                        usage: {
                            ...usage
                        }
                    },
                    usage_metadata: {
                        input_tokens: usage.prompt_tokens,
                        output_tokens: usage.completion_tokens,
                        total_tokens: usage.total_tokens,
                        ...Object.keys(inputTokenDetails).length > 0 && {
                            input_token_details: inputTokenDetails
                        },
                        ...Object.keys(outputTokenDetails).length > 0 && {
                            output_token_details: outputTokenDetails
                        }
                    }
                }),
                text: ""
            });
            yield generationChunk;
        }
        if (options.signal?.aborted) {
            throw new Error("AbortError");
        }
    }
    async completionWithRetry(request, requestOptions) {
        const clientOptions = this._getClientOptions(requestOptions);
        const isParseableFormat = request.response_format && request.response_format.type === "json_schema";
        return this.caller.call(async ()=>{
            try {
                if (isParseableFormat && !request.stream) {
                    return await this.client.chat.completions.parse(request, clientOptions);
                } else {
                    return await this.client.chat.completions.create(request, clientOptions);
                }
            } catch (e) {
                const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["wrapOpenAIClientError"])(e);
                throw error;
            }
        });
    }
    /** @internal */ _convertCompletionsMessageToBaseMessage(message, rawResponse) {
        const rawToolCalls = message.tool_calls;
        switch(message.role){
            case "assistant":
                {
                    const toolCalls = [];
                    const invalidToolCalls = [];
                    for (const rawToolCall of rawToolCalls ?? []){
                        try {
                            toolCalls.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$openai_tools$2f$json_output_tools_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseToolCall"])(rawToolCall, {
                                returnId: true
                            }));
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        } catch (e) {
                            invalidToolCalls.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$openai_tools$2f$json_output_tools_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["makeInvalidToolCall"])(rawToolCall, e.message));
                        }
                    }
                    const additional_kwargs = {
                        function_call: message.function_call,
                        tool_calls: rawToolCalls
                    };
                    if (this.__includeRawResponse !== undefined) {
                        additional_kwargs.__raw_response = rawResponse;
                    }
                    const response_metadata = {
                        model_name: rawResponse.model,
                        ...rawResponse.system_fingerprint ? {
                            usage: {
                                ...rawResponse.usage
                            },
                            system_fingerprint: rawResponse.system_fingerprint
                        } : {}
                    };
                    if (message.audio) {
                        additional_kwargs.audio = message.audio;
                    }
                    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$ai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AIMessage"]({
                        content: message.content || "",
                        tool_calls: toolCalls,
                        invalid_tool_calls: invalidToolCalls,
                        additional_kwargs,
                        response_metadata,
                        id: rawResponse.id
                    });
                }
            default:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$chat$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ChatMessage"](message.content || "", message.role ?? "unknown");
        }
    }
    /** @internal */ _convertCompletionsDeltaToBaseMessageChunk(// eslint-disable-next-line @typescript-eslint/no-explicit-any
    delta, rawResponse, defaultRole) {
        const role = delta.role ?? defaultRole;
        const content = delta.content ?? "";
        let additional_kwargs;
        if (delta.function_call) {
            additional_kwargs = {
                function_call: delta.function_call
            };
        } else if (delta.tool_calls) {
            additional_kwargs = {
                tool_calls: delta.tool_calls
            };
        } else {
            additional_kwargs = {};
        }
        if (this.__includeRawResponse) {
            additional_kwargs.__raw_response = rawResponse;
        }
        if (delta.audio) {
            additional_kwargs.audio = {
                ...delta.audio,
                index: rawResponse.choices[0].index
            };
        }
        const response_metadata = {
            usage: {
                ...rawResponse.usage
            }
        };
        if (role === "user") {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$human$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HumanMessageChunk"]({
                content,
                response_metadata
            });
        } else if (role === "assistant") {
            const toolCallChunks = [];
            if (Array.isArray(delta.tool_calls)) {
                for (const rawToolCall of delta.tool_calls){
                    toolCallChunks.push({
                        name: rawToolCall.function?.name,
                        args: rawToolCall.function?.arguments,
                        id: rawToolCall.id,
                        index: rawToolCall.index,
                        type: "tool_call_chunk"
                    });
                }
            }
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$ai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AIMessageChunk"]({
                content,
                tool_call_chunks: toolCallChunks,
                additional_kwargs,
                id: rawResponse.id,
                response_metadata
            });
        } else if (role === "system") {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$system$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SystemMessageChunk"]({
                content,
                response_metadata
            });
        } else if (role === "developer") {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$system$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SystemMessageChunk"]({
                content,
                response_metadata,
                additional_kwargs: {
                    __openai_role__: "developer"
                }
            });
        } else if (role === "function") {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$function$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["FunctionMessageChunk"]({
                content,
                additional_kwargs,
                name: delta.name,
                response_metadata
            });
        } else if (role === "tool") {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$tool$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ToolMessageChunk"]({
                content,
                additional_kwargs,
                tool_call_id: delta.tool_call_id,
                response_metadata
            });
        } else {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$messages$2f$chat$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ChatMessageChunk"]({
                content,
                role,
                response_metadata
            });
        }
    }
}
class ChatOpenAI extends BaseChatOpenAI {
    get lc_serializable_keys() {
        return [
            ...super.lc_serializable_keys,
            "useResponsesApi"
        ];
    }
    constructor(fields){
        super(fields ?? {});
        /**
         * Whether to use the responses API for all requests. If `false` the responses API will be used
         * only when required in order to fulfill the request.
         */ Object.defineProperty(this, "useResponsesApi", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "responses", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "completions", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.useResponsesApi = fields?.useResponsesApi ?? false;
        this.responses = new ChatOpenAIResponses(fields);
        this.completions = new ChatOpenAICompletions(fields);
    }
    _useResponsesApi(options) {
        const usesBuiltInTools = options?.tools?.some(isBuiltInTool);
        const hasResponsesOnlyKwargs = options?.previous_response_id != null || options?.text != null || options?.truncation != null || options?.include != null || options?.reasoning?.summary != null || this.reasoning?.summary != null;
        return this.useResponsesApi || usesBuiltInTools || hasResponsesOnlyKwargs;
    }
    /** @ignore */ async _generate(messages, options, runManager) {
        if (this._useResponsesApi(options)) {
            return this.responses._generate(messages, options);
        }
        return this.completions._generate(messages, options, runManager);
    }
    async *_streamResponseChunks(messages, options, runManager) {
        if (this._useResponsesApi(options)) {
            yield* this.responses._streamResponseChunks(messages, options);
            return;
        }
        yield* this.completions._streamResponseChunks(messages, options, runManager);
    }
    withConfig(config) {
        // FIXME: assigning additional config options to the inner chat classes this way
        // is awkward, but it's the only way to preserve the original object identity
        // and still thread config options, which is important since this class is a "proxy"
        // for the inner chat classes. This will be fixed in a later version of langchain
        // when the core runnable interface is improved (0.4) to support this out of the box.
        const bindChatOpenAIConfig = (cls, config)=>{
            const oldGenerate = cls._generate;
            const oldStreamResponseChunks = cls._streamResponseChunks;
            return Object.assign(cls, {
                _generate (messages, options, runManager) {
                    return oldGenerate.call(cls, messages, {
                        ...options,
                        ...config
                    }, runManager);
                },
                _streamResponseChunks (messages, options, runManager) {
                    return oldStreamResponseChunks.call(cls, messages, {
                        ...options,
                        ...config
                    }, runManager);
                }
            });
        };
        this.responses = bindChatOpenAIConfig(this.responses, config);
        this.completions = bindChatOpenAIConfig(this.completions, config);
        // Proxy chat class is also bound for `_useResponsesApi`,
        return bindChatOpenAIConfig(this, config);
    }
}
}),
"[project]/node_modules/@langchain/openai/dist/utils/headers.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "isHeaders": ()=>isHeaders,
    "normalizeHeaders": ()=>normalizeHeaders
});
const iife = (fn)=>fn();
function isHeaders(headers) {
    return typeof Headers !== "undefined" && headers !== null && typeof headers === "object" && Object.prototype.toString.call(headers) === "[object Headers]";
}
function normalizeHeaders(headers) {
    const output = iife(()=>{
        // If headers is a Headers instance
        if (isHeaders(headers)) {
            return headers;
        } else if (Array.isArray(headers)) {
            return new Headers(headers);
        } else if (typeof headers === "object" && headers !== null && "values" in headers && isHeaders(headers.values)) {
            return headers.values;
        } else if (typeof headers === "object" && headers !== null) {
            const entries = Object.entries(headers).filter(([, v])=>typeof v === "string").map(([k, v])=>[
                    k,
                    v
                ]);
            return new Headers(entries);
        }
        return new Headers();
    });
    return Object.fromEntries(output.entries());
}
}),
"[project]/node_modules/@langchain/openai/dist/azure/chat_models.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AzureChatOpenAI": ()=>AzureChatOpenAI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$azure$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/azure.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/env.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/chat_models.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/azure.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/headers.js [app-route] (ecmascript)");
;
;
;
;
;
class AzureChatOpenAI extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ChatOpenAI"] {
    _llmType() {
        return "azure_openai";
    }
    get lc_aliases() {
        return {
            ...super.lc_aliases,
            openAIApiKey: "openai_api_key",
            openAIApiVersion: "openai_api_version",
            openAIBasePath: "openai_api_base",
            deploymentName: "deployment_name",
            azureOpenAIEndpoint: "azure_endpoint",
            azureOpenAIApiVersion: "openai_api_version",
            azureOpenAIBasePath: "openai_api_base",
            azureOpenAIApiDeploymentName: "deployment_name"
        };
    }
    get lc_secrets() {
        return {
            ...super.lc_secrets,
            azureOpenAIApiKey: "AZURE_OPENAI_API_KEY"
        };
    }
    get lc_serializable_keys() {
        return [
            ...super.lc_serializable_keys,
            "azureOpenAIApiKey",
            "azureOpenAIApiVersion",
            "azureOpenAIBasePath",
            "azureOpenAIEndpoint",
            "azureOpenAIApiInstanceName",
            "azureOpenAIApiDeploymentName",
            "deploymentName",
            "openAIApiKey",
            "openAIApiVersion"
        ];
    }
    constructor(fields){
        super(fields);
        Object.defineProperty(this, "azureOpenAIApiVersion", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIApiKey", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureADTokenProvider", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIApiInstanceName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIApiDeploymentName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIBasePath", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIEndpoint", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.azureOpenAIApiKey = fields?.azureOpenAIApiKey ?? fields?.openAIApiKey ?? fields?.apiKey ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_KEY");
        this.azureOpenAIApiInstanceName = fields?.azureOpenAIApiInstanceName ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_INSTANCE_NAME");
        this.azureOpenAIApiDeploymentName = fields?.azureOpenAIApiDeploymentName ?? fields?.deploymentName ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_DEPLOYMENT_NAME");
        this.azureOpenAIApiVersion = fields?.azureOpenAIApiVersion ?? fields?.openAIApiVersion ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_VERSION");
        this.azureOpenAIBasePath = fields?.azureOpenAIBasePath ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_BASE_PATH");
        this.azureOpenAIEndpoint = fields?.azureOpenAIEndpoint ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_ENDPOINT");
        this.azureADTokenProvider = fields?.azureADTokenProvider;
        if (!this.azureOpenAIApiKey && !this.apiKey && !this.azureADTokenProvider) {
            throw new Error("Azure OpenAI API key or Token Provider not found");
        }
    }
    getLsParams(options) {
        const params = super.getLsParams(options);
        params.ls_provider = "azure";
        return params;
    }
    _getClientOptions(options) {
        if (!this.client) {
            const openAIEndpointConfig = {
                azureOpenAIApiDeploymentName: this.azureOpenAIApiDeploymentName,
                azureOpenAIApiInstanceName: this.azureOpenAIApiInstanceName,
                azureOpenAIApiKey: this.azureOpenAIApiKey,
                azureOpenAIBasePath: this.azureOpenAIBasePath,
                azureADTokenProvider: this.azureADTokenProvider,
                baseURL: this.clientConfig.baseURL,
                azureOpenAIEndpoint: this.azureOpenAIEndpoint
            };
            const endpoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEndpoint"])(openAIEndpointConfig);
            const params = {
                ...this.clientConfig,
                baseURL: endpoint,
                timeout: this.timeout,
                maxRetries: 0
            };
            if (!this.azureADTokenProvider) {
                params.apiKey = openAIEndpointConfig.azureOpenAIApiKey;
            }
            if (!params.baseURL) {
                delete params.baseURL;
            }
            let env = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnv"])();
            if (env === "node" || env === "deno") {
                env = `(${env}/${process.version}; ${process.platform}; ${process.arch})`;
            }
            const defaultHeaders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["normalizeHeaders"])(params.defaultHeaders);
            params.defaultHeaders = {
                ...params.defaultHeaders,
                "User-Agent": defaultHeaders["User-Agent"] ? `langchainjs-azure-openai/2.0.0 (${env})${defaultHeaders["User-Agent"]}` : `langchainjs-azure-openai/2.0.0 (${env})`
            };
            this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$azure$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AzureOpenAI"]({
                apiVersion: this.azureOpenAIApiVersion,
                azureADTokenProvider: this.azureADTokenProvider,
                deployment: this.azureOpenAIApiDeploymentName,
                ...params
            });
        }
        const requestOptions = {
            ...this.clientConfig,
            ...options
        };
        if (this.azureOpenAIApiKey) {
            requestOptions.headers = {
                "api-key": this.azureOpenAIApiKey,
                ...requestOptions.headers
            };
            requestOptions.query = {
                "api-version": this.azureOpenAIApiVersion,
                ...requestOptions.query
            };
        }
        return requestOptions;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    toJSON() {
        const json = super.toJSON();
        function isRecord(obj) {
            return typeof obj === "object" && obj != null;
        }
        if (isRecord(json) && isRecord(json.kwargs)) {
            delete json.kwargs.azure_openai_base_path;
            delete json.kwargs.azure_openai_api_deployment_name;
            delete json.kwargs.azure_openai_api_key;
            delete json.kwargs.azure_openai_api_version;
            delete json.kwargs.azure_open_ai_base_path;
            if (!json.kwargs.azure_endpoint && this.azureOpenAIEndpoint) {
                json.kwargs.azure_endpoint = this.azureOpenAIEndpoint;
            }
            if (!json.kwargs.azure_endpoint && this.azureOpenAIBasePath) {
                const parts = this.azureOpenAIBasePath.split("/openai/deployments/");
                if (parts.length === 2 && parts[0].startsWith("http")) {
                    const [endpoint] = parts;
                    json.kwargs.azure_endpoint = endpoint;
                }
            }
            if (!json.kwargs.azure_endpoint && this.azureOpenAIApiInstanceName) {
                json.kwargs.azure_endpoint = `https://${this.azureOpenAIApiInstanceName}.openai.azure.com/`;
            }
            if (!json.kwargs.deployment_name && this.azureOpenAIApiDeploymentName) {
                json.kwargs.deployment_name = this.azureOpenAIApiDeploymentName;
            }
            if (!json.kwargs.deployment_name && this.azureOpenAIBasePath) {
                const parts = this.azureOpenAIBasePath.split("/openai/deployments/");
                if (parts.length === 2) {
                    const [, deployment] = parts;
                    json.kwargs.deployment_name = deployment;
                }
            }
            if (json.kwargs.azure_endpoint && json.kwargs.deployment_name && json.kwargs.openai_api_base) {
                delete json.kwargs.openai_api_base;
            }
            if (json.kwargs.azure_openai_api_instance_name && json.kwargs.azure_endpoint) {
                delete json.kwargs.azure_openai_api_instance_name;
            }
        }
        return json;
    }
    withStructuredOutput(outputSchema, config) {
        const ensuredConfig = {
            ...config
        };
        // Not all Azure gpt-4o deployments models support jsonSchema yet
        if (this.model.startsWith("gpt-4o")) {
            if (ensuredConfig?.method === undefined) {
                ensuredConfig.method = "functionCalling";
            }
        }
        return super.withStructuredOutput(outputSchema, ensuredConfig);
    }
}
}),
"[project]/node_modules/@langchain/openai/dist/llms.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "OpenAI": ()=>OpenAI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/client.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$language_models$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/language_models/base.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$language_models$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/language_models/base.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$outputs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/outputs.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$outputs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/outputs.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/env.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$language_models$2f$llms$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/language_models/llms.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$language_models$2f$llms$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/language_models/llms.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$chunk_array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/chunk_array.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$chunk_array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/chunk_array.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/azure.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <locals>");
;
;
;
;
;
;
;
;
class OpenAI extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$language_models$2f$llms$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseLLM"] {
    static lc_name() {
        return "OpenAI";
    }
    get callKeys() {
        return [
            ...super.callKeys,
            "options"
        ];
    }
    get lc_secrets() {
        return {
            openAIApiKey: "OPENAI_API_KEY",
            apiKey: "OPENAI_API_KEY",
            organization: "OPENAI_ORGANIZATION"
        };
    }
    get lc_aliases() {
        return {
            modelName: "model",
            openAIApiKey: "openai_api_key",
            apiKey: "openai_api_key"
        };
    }
    constructor(fields){
        super(fields ?? {});
        Object.defineProperty(this, "lc_serializable", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
        Object.defineProperty(this, "temperature", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "maxTokens", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "topP", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "frequencyPenalty", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "presencePenalty", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "n", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 1
        });
        Object.defineProperty(this, "bestOf", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "logitBias", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "model", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "gpt-3.5-turbo-instruct"
        });
        /** @deprecated Use "model" instead */ Object.defineProperty(this, "modelName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "modelKwargs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "batchSize", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 20
        });
        Object.defineProperty(this, "timeout", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "stop", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "stopSequences", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "user", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "streaming", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "openAIApiKey", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "apiKey", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "organization", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "client", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "clientConfig", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.openAIApiKey = fields?.apiKey ?? fields?.openAIApiKey ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("OPENAI_API_KEY");
        this.apiKey = this.openAIApiKey;
        this.organization = fields?.configuration?.organization ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("OPENAI_ORGANIZATION");
        this.model = fields?.model ?? fields?.modelName ?? this.model;
        if ((this.model?.startsWith("gpt-3.5-turbo") || this.model?.startsWith("gpt-4") || this.model?.startsWith("o1")) && !this.model?.includes("-instruct")) {
            throw new Error([
                `Your chosen OpenAI model, "${this.model}", is a chat model and not a text-in/text-out LLM.`,
                `Passing it into the "OpenAI" class is no longer supported.`,
                `Please use the "ChatOpenAI" class instead.`,
                "",
                `See this page for more information:`,
                "|",
                `└> https://js.langchain.com/docs/integrations/chat/openai`
            ].join("\n"));
        }
        this.modelName = this.model;
        this.modelKwargs = fields?.modelKwargs ?? {};
        this.batchSize = fields?.batchSize ?? this.batchSize;
        this.timeout = fields?.timeout;
        this.temperature = fields?.temperature ?? this.temperature;
        this.maxTokens = fields?.maxTokens ?? this.maxTokens;
        this.topP = fields?.topP ?? this.topP;
        this.frequencyPenalty = fields?.frequencyPenalty ?? this.frequencyPenalty;
        this.presencePenalty = fields?.presencePenalty ?? this.presencePenalty;
        this.n = fields?.n ?? this.n;
        this.bestOf = fields?.bestOf ?? this.bestOf;
        this.logitBias = fields?.logitBias;
        this.stop = fields?.stopSequences ?? fields?.stop;
        this.stopSequences = this.stop;
        this.user = fields?.user;
        this.streaming = fields?.streaming ?? false;
        if (this.streaming && this.bestOf && this.bestOf > 1) {
            throw new Error("Cannot stream results when bestOf > 1");
        }
        this.clientConfig = {
            apiKey: this.apiKey,
            organization: this.organization,
            dangerouslyAllowBrowser: true,
            ...fields?.configuration
        };
    }
    /**
     * Get the parameters used to invoke the model
     */ invocationParams(options) {
        return {
            model: this.model,
            temperature: this.temperature,
            max_tokens: this.maxTokens,
            top_p: this.topP,
            frequency_penalty: this.frequencyPenalty,
            presence_penalty: this.presencePenalty,
            n: this.n,
            best_of: this.bestOf,
            logit_bias: this.logitBias,
            stop: options?.stop ?? this.stopSequences,
            user: this.user,
            stream: this.streaming,
            ...this.modelKwargs
        };
    }
    /** @ignore */ _identifyingParams() {
        return {
            model_name: this.model,
            ...this.invocationParams(),
            ...this.clientConfig
        };
    }
    /**
     * Get the identifying parameters for the model
     */ identifyingParams() {
        return this._identifyingParams();
    }
    /**
     * Call out to OpenAI's endpoint with k unique prompts
     *
     * @param [prompts] - The prompts to pass into the model.
     * @param [options] - Optional list of stop words to use when generating.
     * @param [runManager] - Optional callback manager to use when generating.
     *
     * @returns The full LLM output.
     *
     * @example
     * ```ts
     * import { OpenAI } from "langchain/llms/openai";
     * const openai = new OpenAI();
     * const response = await openai.generate(["Tell me a joke."]);
     * ```
     */ async _generate(prompts, options, runManager) {
        const subPrompts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$chunk_array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chunkArray"])(prompts, this.batchSize);
        const choices = [];
        const tokenUsage = {};
        const params = this.invocationParams(options);
        if (params.max_tokens === -1) {
            if (prompts.length !== 1) {
                throw new Error("max_tokens set to -1 not supported for multiple inputs");
            }
            params.max_tokens = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$language_models$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateMaxTokens"])({
                prompt: prompts[0],
                // Cast here to allow for other models that may not fit the union
                modelName: this.model
            });
        }
        for(let i = 0; i < subPrompts.length; i += 1){
            const data = params.stream ? await (async ()=>{
                const choices = [];
                let response;
                const stream = await this.completionWithRetry({
                    ...params,
                    stream: true,
                    prompt: subPrompts[i]
                }, options);
                for await (const message of stream){
                    // on the first message set the response properties
                    if (!response) {
                        response = {
                            id: message.id,
                            object: message.object,
                            created: message.created,
                            model: message.model
                        };
                    }
                    // on all messages, update choice
                    for (const part of message.choices){
                        if (!choices[part.index]) {
                            choices[part.index] = part;
                        } else {
                            const choice = choices[part.index];
                            choice.text += part.text;
                            choice.finish_reason = part.finish_reason;
                            choice.logprobs = part.logprobs;
                        }
                        void runManager?.handleLLMNewToken(part.text, {
                            prompt: Math.floor(part.index / this.n),
                            completion: part.index % this.n
                        });
                    }
                }
                if (options.signal?.aborted) {
                    throw new Error("AbortError");
                }
                return {
                    ...response,
                    choices
                };
            })() : await this.completionWithRetry({
                ...params,
                stream: false,
                prompt: subPrompts[i]
            }, {
                signal: options.signal,
                ...options.options
            });
            choices.push(...data.choices);
            const { completion_tokens: completionTokens, prompt_tokens: promptTokens, total_tokens: totalTokens } = data.usage ? data.usage : {
                completion_tokens: undefined,
                prompt_tokens: undefined,
                total_tokens: undefined
            };
            if (completionTokens) {
                tokenUsage.completionTokens = (tokenUsage.completionTokens ?? 0) + completionTokens;
            }
            if (promptTokens) {
                tokenUsage.promptTokens = (tokenUsage.promptTokens ?? 0) + promptTokens;
            }
            if (totalTokens) {
                tokenUsage.totalTokens = (tokenUsage.totalTokens ?? 0) + totalTokens;
            }
        }
        const generations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$chunk_array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chunkArray"])(choices, this.n).map((promptChoices)=>promptChoices.map((choice)=>({
                    text: choice.text ?? "",
                    generationInfo: {
                        finishReason: choice.finish_reason,
                        logprobs: choice.logprobs
                    }
                })));
        return {
            generations,
            llmOutput: {
                tokenUsage
            }
        };
    }
    // TODO(jacoblee): Refactor with _generate(..., {stream: true}) implementation?
    async *_streamResponseChunks(input, options, runManager) {
        const params = {
            ...this.invocationParams(options),
            prompt: input,
            stream: true
        };
        const stream = await this.completionWithRetry(params, options);
        for await (const data of stream){
            const choice = data?.choices[0];
            if (!choice) {
                continue;
            }
            const chunk = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$outputs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenerationChunk"]({
                text: choice.text,
                generationInfo: {
                    finishReason: choice.finish_reason
                }
            });
            yield chunk;
            // eslint-disable-next-line no-void
            void runManager?.handleLLMNewToken(chunk.text ?? "");
        }
        if (options.signal?.aborted) {
            throw new Error("AbortError");
        }
    }
    async completionWithRetry(request, options) {
        const requestOptions = this._getClientOptions(options);
        return this.caller.call(async ()=>{
            try {
                const res = await this.client.completions.create(request, requestOptions);
                return res;
            } catch (e) {
                const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["wrapOpenAIClientError"])(e);
                throw error;
            }
        });
    }
    /**
     * Calls the OpenAI API with retry logic in case of failures.
     * @param request The request to send to the OpenAI API.
     * @param options Optional configuration for the API call.
     * @returns The response from the OpenAI API.
     */ _getClientOptions(options) {
        if (!this.client) {
            const openAIEndpointConfig = {
                baseURL: this.clientConfig.baseURL
            };
            const endpoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEndpoint"])(openAIEndpointConfig);
            const params = {
                ...this.clientConfig,
                baseURL: endpoint,
                timeout: this.timeout,
                maxRetries: 0
            };
            if (!params.baseURL) {
                delete params.baseURL;
            }
            this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenAI"](params);
        }
        const requestOptions = {
            ...this.clientConfig,
            ...options
        };
        return requestOptions;
    }
    _llmType() {
        return "openai";
    }
}
}),
"[project]/node_modules/@langchain/openai/dist/azure/llms.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AzureOpenAI": ()=>AzureOpenAI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$azure$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/azure.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/env.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$llms$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/llms.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/azure.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/headers.js [app-route] (ecmascript)");
;
;
;
;
;
class AzureOpenAI extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$llms$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenAI"] {
    get lc_aliases() {
        return {
            ...super.lc_aliases,
            openAIApiKey: "openai_api_key",
            openAIApiVersion: "openai_api_version",
            openAIBasePath: "openai_api_base",
            deploymentName: "deployment_name",
            azureOpenAIEndpoint: "azure_endpoint",
            azureOpenAIApiVersion: "openai_api_version",
            azureOpenAIBasePath: "openai_api_base",
            azureOpenAIApiDeploymentName: "deployment_name"
        };
    }
    get lc_secrets() {
        return {
            ...super.lc_secrets,
            azureOpenAIApiKey: "AZURE_OPENAI_API_KEY"
        };
    }
    constructor(fields){
        super(fields);
        Object.defineProperty(this, "azureOpenAIApiVersion", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIApiKey", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureADTokenProvider", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIApiInstanceName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIApiDeploymentName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIBasePath", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIEndpoint", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.azureOpenAIApiDeploymentName = (fields?.azureOpenAIApiCompletionsDeploymentName || fields?.azureOpenAIApiDeploymentName) ?? ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_COMPLETIONS_DEPLOYMENT_NAME") || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_DEPLOYMENT_NAME"));
        this.azureOpenAIApiKey = fields?.azureOpenAIApiKey ?? fields?.openAIApiKey ?? fields?.apiKey ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_KEY");
        this.azureOpenAIApiInstanceName = fields?.azureOpenAIApiInstanceName ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_INSTANCE_NAME");
        this.azureOpenAIApiVersion = fields?.azureOpenAIApiVersion ?? fields?.openAIApiVersion ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_VERSION");
        this.azureOpenAIBasePath = fields?.azureOpenAIBasePath ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_BASE_PATH");
        this.azureOpenAIEndpoint = fields?.azureOpenAIEndpoint ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_ENDPOINT");
        this.azureADTokenProvider = fields?.azureADTokenProvider;
        if (!this.azureOpenAIApiKey && !this.apiKey && !this.azureADTokenProvider) {
            throw new Error("Azure OpenAI API key or Token Provider not found");
        }
    }
    _getClientOptions(options) {
        if (!this.client) {
            const openAIEndpointConfig = {
                azureOpenAIApiDeploymentName: this.azureOpenAIApiDeploymentName,
                azureOpenAIApiInstanceName: this.azureOpenAIApiInstanceName,
                azureOpenAIApiKey: this.azureOpenAIApiKey,
                azureOpenAIBasePath: this.azureOpenAIBasePath,
                azureADTokenProvider: this.azureADTokenProvider,
                baseURL: this.clientConfig.baseURL
            };
            const endpoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEndpoint"])(openAIEndpointConfig);
            const params = {
                ...this.clientConfig,
                baseURL: endpoint,
                timeout: this.timeout,
                maxRetries: 0
            };
            if (!this.azureADTokenProvider) {
                params.apiKey = openAIEndpointConfig.azureOpenAIApiKey;
            }
            if (!params.baseURL) {
                delete params.baseURL;
            }
            const defaultHeaders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["normalizeHeaders"])(params.defaultHeaders);
            params.defaultHeaders = {
                ...params.defaultHeaders,
                "User-Agent": defaultHeaders["User-Agent"] ? `${defaultHeaders["User-Agent"]}: langchainjs-azure-openai-v2` : `langchainjs-azure-openai-v2`
            };
            this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$azure$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AzureOpenAI"]({
                apiVersion: this.azureOpenAIApiVersion,
                azureADTokenProvider: this.azureADTokenProvider,
                ...params
            });
        }
        const requestOptions = {
            ...this.clientConfig,
            ...options
        };
        if (this.azureOpenAIApiKey) {
            requestOptions.headers = {
                "api-key": this.azureOpenAIApiKey,
                ...requestOptions.headers
            };
            requestOptions.query = {
                "api-version": this.azureOpenAIApiVersion,
                ...requestOptions.query
            };
        }
        return requestOptions;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    toJSON() {
        const json = super.toJSON();
        function isRecord(obj) {
            return typeof obj === "object" && obj != null;
        }
        if (isRecord(json) && isRecord(json.kwargs)) {
            delete json.kwargs.azure_openai_base_path;
            delete json.kwargs.azure_openai_api_deployment_name;
            delete json.kwargs.azure_openai_api_key;
            delete json.kwargs.azure_openai_api_version;
            delete json.kwargs.azure_open_ai_base_path;
        }
        return json;
    }
}
}),
"[project]/node_modules/@langchain/openai/dist/embeddings.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "OpenAIEmbeddings": ()=>OpenAIEmbeddings
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/client.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/env.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$embeddings$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/embeddings.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$embeddings$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/embeddings.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$chunk_array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/chunk_array.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$chunk_array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/chunk_array.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/azure.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <locals>");
;
;
;
;
;
;
class OpenAIEmbeddings extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$embeddings$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Embeddings"] {
    constructor(fields){
        const fieldsWithDefaults = {
            maxConcurrency: 2,
            ...fields
        };
        super(fieldsWithDefaults);
        Object.defineProperty(this, "model", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "text-embedding-ada-002"
        });
        /** @deprecated Use "model" instead */ Object.defineProperty(this, "modelName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "batchSize", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 512
        });
        // TODO: Update to `false` on next minor release (see: https://github.com/langchain-ai/langchainjs/pull/3612)
        Object.defineProperty(this, "stripNewLines", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
        /**
         * The number of dimensions the resulting output embeddings should have.
         * Only supported in `text-embedding-3` and later models.
         */ Object.defineProperty(this, "dimensions", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "timeout", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "organization", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "client", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "clientConfig", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        const apiKey = fieldsWithDefaults?.apiKey ?? fieldsWithDefaults?.openAIApiKey ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("OPENAI_API_KEY");
        this.organization = fieldsWithDefaults?.configuration?.organization ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("OPENAI_ORGANIZATION");
        this.model = fieldsWithDefaults?.model ?? fieldsWithDefaults?.modelName ?? this.model;
        this.modelName = this.model;
        this.batchSize = fieldsWithDefaults?.batchSize ?? this.batchSize;
        this.stripNewLines = fieldsWithDefaults?.stripNewLines ?? this.stripNewLines;
        this.timeout = fieldsWithDefaults?.timeout;
        this.dimensions = fieldsWithDefaults?.dimensions;
        this.clientConfig = {
            apiKey,
            organization: this.organization,
            dangerouslyAllowBrowser: true,
            ...fields?.configuration
        };
    }
    /**
     * Method to generate embeddings for an array of documents. Splits the
     * documents into batches and makes requests to the OpenAI API to generate
     * embeddings.
     * @param texts Array of documents to generate embeddings for.
     * @returns Promise that resolves to a 2D array of embeddings for each document.
     */ async embedDocuments(texts) {
        const batches = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$chunk_array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chunkArray"])(this.stripNewLines ? texts.map((t)=>t.replace(/\n/g, " ")) : texts, this.batchSize);
        const batchRequests = batches.map((batch)=>{
            const params = {
                model: this.model,
                input: batch
            };
            if (this.dimensions) {
                params.dimensions = this.dimensions;
            }
            return this.embeddingWithRetry(params);
        });
        const batchResponses = await Promise.all(batchRequests);
        const embeddings = [];
        for(let i = 0; i < batchResponses.length; i += 1){
            const batch = batches[i];
            const { data: batchResponse } = batchResponses[i];
            for(let j = 0; j < batch.length; j += 1){
                embeddings.push(batchResponse[j].embedding);
            }
        }
        return embeddings;
    }
    /**
     * Method to generate an embedding for a single document. Calls the
     * embeddingWithRetry method with the document as the input.
     * @param text Document to generate an embedding for.
     * @returns Promise that resolves to an embedding for the document.
     */ async embedQuery(text) {
        const params = {
            model: this.model,
            input: this.stripNewLines ? text.replace(/\n/g, " ") : text
        };
        if (this.dimensions) {
            params.dimensions = this.dimensions;
        }
        const { data } = await this.embeddingWithRetry(params);
        return data[0].embedding;
    }
    /**
     * Private method to make a request to the OpenAI API to generate
     * embeddings. Handles the retry logic and returns the response from the
     * API.
     * @param request Request to send to the OpenAI API.
     * @returns Promise that resolves to the response from the API.
     */ async embeddingWithRetry(request) {
        if (!this.client) {
            const openAIEndpointConfig = {
                baseURL: this.clientConfig.baseURL
            };
            const endpoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEndpoint"])(openAIEndpointConfig);
            const params = {
                ...this.clientConfig,
                baseURL: endpoint,
                timeout: this.timeout,
                maxRetries: 0
            };
            if (!params.baseURL) {
                delete params.baseURL;
            }
            this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenAI"](params);
        }
        const requestOptions = {};
        return this.caller.call(async ()=>{
            try {
                const res = await this.client.embeddings.create(request, requestOptions);
                return res;
            } catch (e) {
                const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["wrapOpenAIClientError"])(e);
                throw error;
            }
        });
    }
}
}),
"[project]/node_modules/@langchain/openai/dist/azure/embeddings.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AzureOpenAIEmbeddings": ()=>AzureOpenAIEmbeddings
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$azure$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/azure.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/env.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$embeddings$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/embeddings.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/azure.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/headers.js [app-route] (ecmascript)");
;
;
;
;
;
;
class AzureOpenAIEmbeddings extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$embeddings$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenAIEmbeddings"] {
    constructor(fields){
        super(fields);
        Object.defineProperty(this, "azureOpenAIApiVersion", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIApiKey", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureADTokenProvider", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIApiInstanceName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIApiDeploymentName", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "azureOpenAIBasePath", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.batchSize = fields?.batchSize ?? 1;
        this.azureOpenAIApiKey = fields?.azureOpenAIApiKey ?? fields?.apiKey ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_KEY");
        this.azureOpenAIApiVersion = fields?.azureOpenAIApiVersion ?? fields?.openAIApiVersion ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_VERSION");
        this.azureOpenAIBasePath = fields?.azureOpenAIBasePath ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_BASE_PATH");
        this.azureOpenAIApiInstanceName = fields?.azureOpenAIApiInstanceName ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_INSTANCE_NAME");
        this.azureOpenAIApiDeploymentName = (fields?.azureOpenAIApiEmbeddingsDeploymentName || fields?.azureOpenAIApiDeploymentName) ?? ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_EMBEDDINGS_DEPLOYMENT_NAME") || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("AZURE_OPENAI_API_DEPLOYMENT_NAME"));
        this.azureADTokenProvider = fields?.azureADTokenProvider;
    }
    async embeddingWithRetry(request) {
        if (!this.client) {
            const openAIEndpointConfig = {
                azureOpenAIApiDeploymentName: this.azureOpenAIApiDeploymentName,
                azureOpenAIApiInstanceName: this.azureOpenAIApiInstanceName,
                azureOpenAIApiKey: this.azureOpenAIApiKey,
                azureOpenAIBasePath: this.azureOpenAIBasePath,
                azureADTokenProvider: this.azureADTokenProvider,
                baseURL: this.clientConfig.baseURL
            };
            const endpoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEndpoint"])(openAIEndpointConfig);
            const params = {
                ...this.clientConfig,
                baseURL: endpoint,
                timeout: this.timeout,
                maxRetries: 0
            };
            if (!this.azureADTokenProvider) {
                params.apiKey = openAIEndpointConfig.azureOpenAIApiKey;
            }
            if (!params.baseURL) {
                delete params.baseURL;
            }
            const defaultHeaders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["normalizeHeaders"])(params.defaultHeaders);
            params.defaultHeaders = {
                ...params.defaultHeaders,
                "User-Agent": defaultHeaders["User-Agent"] ? `${defaultHeaders["User-Agent"]}: langchainjs-azure-openai-v2` : `langchainjs-azure-openai-v2`
            };
            this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$azure$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AzureOpenAI"]({
                apiVersion: this.azureOpenAIApiVersion,
                azureADTokenProvider: this.azureADTokenProvider,
                deployment: this.azureOpenAIApiDeploymentName,
                ...params
            });
        }
        const requestOptions = {};
        if (this.azureOpenAIApiKey) {
            requestOptions.headers = {
                "api-key": this.azureOpenAIApiKey,
                ...requestOptions.headers
            };
            requestOptions.query = {
                "api-version": this.azureOpenAIApiVersion,
                ...requestOptions.query
            };
        }
        return this.caller.call(async ()=>{
            try {
                const res = await this.client.embeddings.create(request, requestOptions);
                return res;
            } catch (e) {
                const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["wrapOpenAIClientError"])(e);
                throw error;
            }
        });
    }
}
}),
"[project]/node_modules/@langchain/openai/dist/types.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
;
}),
"[project]/node_modules/@langchain/openai/dist/tools/dalle.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/* eslint-disable no-param-reassign */ __turbopack_context__.s({
    "DallEAPIWrapper": ()=>DallEAPIWrapper
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/utils/env.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/utils/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/client.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$tools$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/tools.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$tools$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/tools/index.js [app-route] (ecmascript) <locals>");
;
;
;
class DallEAPIWrapper extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$tools$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Tool"] {
    static lc_name() {
        return "DallEAPIWrapper";
    }
    constructor(fields){
        // Shim for new base tool param name
        if (fields?.responseFormat !== undefined && [
            "url",
            "b64_json"
        ].includes(fields.responseFormat)) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            fields.dallEResponseFormat = fields.responseFormat;
            fields.responseFormat = "content";
        }
        super(fields);
        Object.defineProperty(this, "name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "dalle_api_wrapper"
        });
        Object.defineProperty(this, "description", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "A wrapper around OpenAI DALL-E API. Useful for when you need to generate images from a text description. Input should be an image description."
        });
        Object.defineProperty(this, "client", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "model", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "dall-e-3"
        });
        Object.defineProperty(this, "style", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "vivid"
        });
        Object.defineProperty(this, "quality", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "standard"
        });
        Object.defineProperty(this, "n", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 1
        });
        Object.defineProperty(this, "size", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "1024x1024"
        });
        Object.defineProperty(this, "dallEResponseFormat", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "url"
        });
        Object.defineProperty(this, "user", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        const openAIApiKey = fields?.apiKey ?? fields?.openAIApiKey ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("OPENAI_API_KEY");
        const organization = fields?.organization ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("OPENAI_ORGANIZATION");
        const clientConfig = {
            apiKey: openAIApiKey,
            organization,
            dangerouslyAllowBrowser: true,
            baseURL: fields?.baseUrl
        };
        this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenAI"](clientConfig);
        this.model = fields?.model ?? fields?.modelName ?? this.model;
        this.style = fields?.style ?? this.style;
        this.quality = fields?.quality ?? this.quality;
        this.n = fields?.n ?? this.n;
        this.size = fields?.size ?? this.size;
        this.dallEResponseFormat = fields?.dallEResponseFormat ?? this.dallEResponseFormat;
        this.user = fields?.user;
    }
    /**
     * Processes the API response if multiple images are generated.
     * Returns a list of MessageContentImageUrl objects. If the response
     * format is `url`, then the `image_url` field will contain the URL.
     * If it is `b64_json`, then the `image_url` field will contain an object
     * with a `url` field with the base64 encoded image.
     *
     * @param {OpenAIClient.Images.ImagesResponse[]} response The API response
     * @returns {MessageContentImageUrl[]}
     */ processMultipleGeneratedUrls(response) {
        if (this.dallEResponseFormat === "url") {
            return response.flatMap((res)=>{
                const imageUrlContent = res.data?.flatMap((item)=>{
                    if (!item.url) return [];
                    return {
                        type: "image_url",
                        image_url: item.url
                    };
                }).filter((item)=>item !== undefined && item.type === "image_url" && typeof item.image_url === "string" && item.image_url !== undefined) ?? [];
                return imageUrlContent;
            });
        } else {
            return response.flatMap((res)=>{
                const b64Content = res.data?.flatMap((item)=>{
                    if (!item.b64_json) return [];
                    return {
                        type: "image_url",
                        image_url: {
                            url: item.b64_json
                        }
                    };
                }).filter((item)=>item !== undefined && item.type === "image_url" && typeof item.image_url === "object" && "url" in item.image_url && typeof item.image_url.url === "string" && item.image_url.url !== undefined) ?? [];
                return b64Content;
            });
        }
    }
    /** @ignore */ async _call(input) {
        const generateImageFields = {
            model: this.model,
            prompt: input,
            n: 1,
            size: this.size,
            response_format: this.dallEResponseFormat,
            style: this.style,
            quality: this.quality,
            user: this.user
        };
        if (this.n > 1) {
            const results = await Promise.all(Array.from({
                length: this.n
            }).map(()=>this.client.images.generate(generateImageFields)));
            return this.processMultipleGeneratedUrls(results);
        }
        const response = await this.client.images.generate(generateImageFields);
        let data = "";
        if (this.dallEResponseFormat === "url") {
            [data] = response.data?.map((item)=>item.url).filter((url)=>url !== "undefined") ?? [];
        } else {
            [data] = response.data?.map((item)=>item.b64_json).filter((b64_json)=>b64_json !== "undefined") ?? [];
        }
        return data;
    }
}
Object.defineProperty(DallEAPIWrapper, "toolName", {
    enumerable: true,
    configurable: true,
    writable: true,
    value: "dalle_api_wrapper"
});
}),
"[project]/node_modules/@langchain/openai/dist/tools/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$tools$2f$dalle$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/tools/dalle.js [app-route] (ecmascript)");
;
}),
"[project]/node_modules/@langchain/openai/dist/tools/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$tools$2f$dalle$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/tools/dalle.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$tools$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/tools/index.js [app-route] (ecmascript) <locals>");
}),
"[project]/node_modules/@langchain/openai/dist/utils/prompts.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "convertPromptToOpenAI": ()=>convertPromptToOpenAI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/chat_models.js [app-route] (ecmascript)");
;
function convertPromptToOpenAI(formattedPrompt) {
    const messages = formattedPrompt.toChatMessages();
    return {
        messages: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_convertMessagesToOpenAIParams"])(messages)
    };
}
}),
"[project]/node_modules/@langchain/openai/dist/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/chat_models.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$azure$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/azure/chat_models.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$llms$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/llms.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$azure$2f$llms$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/azure/llms.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$azure$2f$embeddings$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/azure/embeddings.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$embeddings$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/embeddings.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/azure.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$tools$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/tools/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/prompts.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
}),
"[project]/node_modules/@langchain/openai/dist/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/chat_models.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$azure$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/azure/chat_models.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$llms$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/llms.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$azure$2f$llms$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/azure/llms.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$azure$2f$embeddings$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/azure/embeddings.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$embeddings$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/embeddings.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$openai$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/openai.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$azure$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/azure.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$tools$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/tools/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/utils/prompts.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/index.js [app-route] (ecmascript) <locals>");
}),
"[project]/node_modules/@langchain/openai/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/index.js [app-route] (ecmascript) <module evaluation>");
;
}),
"[project]/node_modules/@langchain/openai/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/index.js [app-route] (ecmascript) <locals>");
}),

};

//# sourceMappingURL=node_modules_%40langchain_openai_3a9a680f._.js.map
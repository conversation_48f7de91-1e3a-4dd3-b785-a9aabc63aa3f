{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/langchain.ts"], "sourcesContent": ["import { Chat<PERSON>penAI } from '@langchain/openai';\nimport { PromptTemplate } from '@langchain/core/prompts';\nimport { StringOutputParser } from '@langchain/core/output_parsers';\nimport { RunnableSequence } from '@langchain/core/runnables';\n\n// Initialize DeepSeek model (using OpenAI-compatible API)\nexport const createDeepSeekModel = () => {\n  return new ChatOpenAI({\n    model: 'deepseek-chat',\n    apiKey: process.env.DEEPSEEK_API_KEY,\n    configuration: {\n      baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1',\n    },\n    temperature: 0.1,\n    maxTokens: 4000,\n  });\n};\n\n// Enhanced prompt templates for comprehensive analysis\nexport const PROMPTS = {\n  COMPREHENSIVE_ANALYSIS: PromptTemplate.fromTemplate(`\n    You are an expert academic paper analyst with deep knowledge across multiple research domains.\n    Analyze the following research paper comprehensively and provide detailed insights.\n\n    Paper Content:\n    {content}\n\n    Please provide a thorough analysis in the following structured format:\n\n    ## EXECUTIVE SUMMARY\n    - Provide a 3-4 sentence summary of the paper's core contribution\n    - State the main research question and hypothesis\n\n    ## KEY FINDINGS\n    - List 5-7 most significant findings with brief explanations\n    - Include quantitative results where available\n    - Highlight novel discoveries or insights\n\n    ## METHODOLOGY ANALYSIS\n    - Describe the research approach and experimental design\n    - Evaluate the appropriateness of methods used\n    - Identify any methodological innovations or limitations\n\n    ## MAIN CONTRIBUTIONS\n    - List theoretical contributions to the field\n    - Identify practical applications and implications\n    - Note any new frameworks, models, or algorithms introduced\n\n    ## CRITICAL ANALYSIS\n    ### Strengths:\n    - What are the paper's strongest points?\n    - What makes this research valuable?\n\n    ### Weaknesses:\n    - What are potential limitations or gaps?\n    - What could be improved?\n\n    ### Implications:\n    - How does this work advance the field?\n    - What are the broader implications?\n\n    ## TECHNICAL DETAILS\n    ### Algorithms/Methods:\n    - List key algorithms or computational methods\n\n    ### Datasets:\n    - Identify datasets used for experiments\n\n    ### Evaluation Metrics:\n    - List performance metrics and evaluation criteria\n\n    ### Tools/Technologies:\n    - Note software, frameworks, or tools mentioned\n\n    ## CITATIONS AND REFERENCES\n    - Identify 3-5 most important references cited\n    - Note influential authors or research groups mentioned\n    - Estimate the paper's position in the research landscape\n\n    Be thorough, accurate, and provide specific details from the paper.\n  `),\n\n  EXTRACT_CONCEPTS: PromptTemplate.fromTemplate(`\n    Extract and analyze key concepts, terminology, and technical terms from this research paper.\n\n    Paper Content:\n    {content}\n\n    For each important concept, provide:\n    1. The term/concept name\n    2. A clear definition or explanation\n    3. Its importance level (high/medium/low)\n    4. Related concepts or dependencies\n    5. Context within the paper\n\n    Focus on:\n    - Technical terminology\n    - Theoretical concepts\n    - Methodological approaches\n    - Domain-specific terms\n    - Novel concepts introduced\n\n    Format as a structured list with explanations.\n  `),\n\n  EXPLAIN_METHODOLOGY: PromptTemplate.fromTemplate(`\n    Analyze the methodology section of this research paper and provide a clear explanation.\n\n    Paper Content:\n    {content}\n\n    Please explain:\n    1. Research design and approach\n    2. Data collection methods\n    3. Analysis techniques used\n    4. Tools and technologies employed\n    5. Experimental setup (if applicable)\n    6. Validation methods\n    7. Limitations of the methodology\n\n    Make the explanation accessible to researchers in related fields.\n  `),\n\n  GENERATE_QUESTIONS: PromptTemplate.fromTemplate(`\n    Generate thoughtful questions about this research paper that would help readers better understand the work.\n\n    Paper Content:\n    {content}\n\n    Generate 8-10 questions covering:\n    1. Clarification questions about methodology\n    2. Questions about implications and applications\n    3. Questions about limitations and future work\n    4. Questions comparing to related work\n    5. Questions about technical details\n\n    Format as a numbered list with clear, specific questions.\n  `),\n\n  SUMMARIZE: PromptTemplate.fromTemplate(`\n    Provide a concise summary of this research paper.\n\n    Paper Content:\n    {content}\n\n    Include:\n    1. Main research question and objectives\n    2. Key methodology\n    3. Primary findings\n    4. Significance and implications\n\n    Keep it concise but comprehensive.\n  `),\n\n  EXTRACT_KEY_FINDINGS: PromptTemplate.fromTemplate(`\n    Extract the key findings and contributions from this research paper.\n\n    Paper Content:\n    {content}\n\n    List:\n    1. Main research findings\n    2. Novel contributions\n    3. Experimental results\n    4. Theoretical insights\n    5. Practical applications\n\n    Be specific and detailed.\n  `),\n\n  ANSWER_QUESTION: PromptTemplate.fromTemplate(`\n    You are an AI assistant helping researchers understand academic papers. Answer the following question based on the paper content provided.\n\n    Paper Content:\n    {content}\n\n    Question: {question}\n\n    Please provide a comprehensive answer that:\n    1. Directly addresses the question\n    2. References specific sections of the paper when relevant\n    3. Provides context and background if needed\n    4. Mentions any limitations or uncertainties\n    5. Suggests related questions or areas for further exploration\n\n    If the question cannot be answered from the provided content, clearly state this and explain why.\n  `),\n\n  COMPARE_PAPERS: PromptTemplate.fromTemplate(`\n    Compare and analyze the following research papers. Identify similarities, differences, and relationships between them.\n\n    Paper 1:\n    {paper1}\n\n    Paper 2:\n    {paper2}\n\n    Please provide:\n    1. Common themes and research areas\n    2. Methodological similarities and differences\n    3. Complementary findings or conflicting results\n    4. How the papers build upon or relate to each other\n    5. Gaps that could be addressed by combining insights\n    6. Recommendations for researchers interested in this area\n\n    Structure your comparison clearly with specific examples from both papers.\n  `),\n\n  ANALYZE_CITATIONS: PromptTemplate.fromTemplate(`\n    Analyze the citations and references in this research paper to understand its academic context.\n\n    Paper Content:\n    {content}\n\n    Please identify:\n    1. Key foundational works cited\n    2. Recent developments referenced\n    3. Main research communities or schools of thought\n    4. Gaps in the literature identified by the authors\n    5. How this work positions itself relative to existing research\n    6. Potential future research directions suggested\n\n    Focus on understanding the academic landscape and research trajectory.\n  `),\n};\n\n// Create analysis chains\nexport const createAnalysisChain = (promptTemplate: PromptTemplate) => {\n  const model = createDeepSeekModel();\n  const outputParser = new StringOutputParser();\n\n  return RunnableSequence.from([\n    promptTemplate,\n    model,\n    outputParser,\n  ]);\n};\n\n// Specific analysis functions\nexport const summarizePaper = async (content: string) => {\n  const chain = createAnalysisChain(PROMPTS.SUMMARIZE);\n  return await chain.invoke({ content });\n};\n\nexport const extractKeyFindings = async (content: string) => {\n  const chain = createAnalysisChain(PROMPTS.EXTRACT_KEY_FINDINGS);\n  return await chain.invoke({ content });\n};\n\nexport const explainMethodology = async (content: string) => {\n  const chain = createAnalysisChain(PROMPTS.EXPLAIN_METHODOLOGY);\n  return await chain.invoke({ content });\n};\n\nexport const extractConcepts = async (content: string) => {\n  const chain = createAnalysisChain(PROMPTS.EXTRACT_CONCEPTS);\n  return await chain.invoke({ content });\n};\n\nexport const answerQuestion = async (content: string, question: string) => {\n  const chain = createAnalysisChain(PROMPTS.ANSWER_QUESTION);\n  return await chain.invoke({ content, question });\n};\n\nexport const comparePapers = async (paper1: string, paper2: string) => {\n  const chain = createAnalysisChain(PROMPTS.COMPARE_PAPERS);\n  return await chain.invoke({ paper1, paper2 });\n};\n\nexport const analyzeCitations = async (content: string) => {\n  const chain = createAnalysisChain(PROMPTS.ANALYZE_CITATIONS);\n  return await chain.invoke({ content });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAGO,MAAM,sBAAsB;IACjC,OAAO,IAAI,8JAAA,CAAA,aAAU,CAAC;QACpB,OAAO;QACP,QAAQ,QAAQ,GAAG,CAAC,gBAAgB;QACpC,eAAe;YACb,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI;QAC5C;QACA,aAAa;QACb,WAAW;IACb;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4DrD,CAAC;IAED,kBAAkB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;EAqB/C,CAAC;IAED,qBAAqB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;;;EAgBlD,CAAC;IAED,oBAAoB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;EAcjD,CAAC;IAED,WAAW,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;EAaxC,CAAC;IAED,sBAAsB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;EAcnD,CAAC;IAED,iBAAiB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;;;EAgB9C,CAAC;IAED,gBAAgB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;;;;;EAkB7C,CAAC;IAED,mBAAmB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;;EAehD,CAAC;AACH;AAGO,MAAM,sBAAsB,CAAC;IAClC,MAAM,QAAQ;IACd,MAAM,eAAe,IAAI,yKAAA,CAAA,qBAAkB;IAE3C,OAAO,kKAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC;QAC3B;QACA;QACA;KACD;AACH;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,QAAQ,oBAAoB,QAAQ,SAAS;IACnD,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;IAAQ;AACtC;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,QAAQ,oBAAoB,QAAQ,oBAAoB;IAC9D,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;IAAQ;AACtC;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,QAAQ,oBAAoB,QAAQ,mBAAmB;IAC7D,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;IAAQ;AACtC;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,QAAQ,oBAAoB,QAAQ,gBAAgB;IAC1D,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;IAAQ;AACtC;AAEO,MAAM,iBAAiB,OAAO,SAAiB;IACpD,MAAM,QAAQ,oBAAoB,QAAQ,eAAe;IACzD,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;QAAS;IAAS;AAChD;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,MAAM,QAAQ,oBAAoB,QAAQ,cAAc;IACxD,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;QAAQ;IAAO;AAC7C;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,QAAQ,oBAAoB,QAAQ,iBAAiB;IAC3D,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;IAAQ;AACtC", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/ai-analysis-service.ts"], "sourcesContent": ["import { createDeepSeekModel, PROMPTS, createAnalysisChain } from './langchain';\n\nexport interface ComprehensiveAnalysis {\n  summary: string;\n  keyFindings: string[];\n  methodology: string;\n  mainContributions: string[];\n  criticalAnalysis: {\n    strengths: string[];\n    weaknesses: string[];\n    limitations: string[];\n    implications: string[];\n  };\n  technicalDetails: {\n    algorithms: string[];\n    datasets: string[];\n    metrics: string[];\n    tools: string[];\n  };\n  citations: {\n    keyReferences: string[];\n    citationCount: number;\n    referencedAuthors: <AUTHORS>\n  };\n  concepts: ConceptExtraction[];\n  questions: string[];\n  relatedTopics: string[];\n}\n\nexport interface ConceptExtraction {\n  term: string;\n  definition: string;\n  importance: 'high' | 'medium' | 'low';\n  relatedTerms: string[];\n  context: string;\n}\n\nexport interface QuestionAnswer {\n  question: string;\n  answer: string;\n  confidence: number;\n  sources: string[];\n}\n\nclass AIAnalysisService {\n  private model = createDeepSeekModel();\n\n  async analyzeDocument(content: string): Promise<ComprehensiveAnalysis> {\n    try {\n      console.log('Starting comprehensive document analysis...');\n      \n      // Run comprehensive analysis\n      const comprehensiveChain = createAnalysisChain(PROMPTS.COMPREHENSIVE_ANALYSIS);\n      const comprehensiveResult = await comprehensiveChain.invoke({ content });\n      \n      // Parse the comprehensive analysis result\n      const analysis = this.parseComprehensiveAnalysis(comprehensiveResult);\n      \n      // Extract concepts separately for better accuracy\n      const concepts = await this.extractConcepts(content);\n      \n      // Generate questions\n      const questions = await this.generateQuestions(content);\n      \n      return {\n        ...analysis,\n        concepts,\n        questions,\n      };\n    } catch (error) {\n      console.error('Error in document analysis:', error);\n      throw new Error('Failed to analyze document');\n    }\n  }\n\n  async extractConcepts(content: string): Promise<ConceptExtraction[]> {\n    try {\n      const conceptChain = createAnalysisChain(PROMPTS.EXTRACT_CONCEPTS);\n      const result = await conceptChain.invoke({ content });\n      \n      return this.parseConceptsResult(result);\n    } catch (error) {\n      console.error('Error extracting concepts:', error);\n      return [];\n    }\n  }\n\n  async generateQuestions(content: string): Promise<string[]> {\n    try {\n      const questionChain = createAnalysisChain(PROMPTS.GENERATE_QUESTIONS);\n      const result = await questionChain.invoke({ content });\n      \n      return this.parseQuestionsResult(result);\n    } catch (error) {\n      console.error('Error generating questions:', error);\n      return [];\n    }\n  }\n\n  async answerQuestion(content: string, question: string): Promise<QuestionAnswer> {\n    try {\n      const answerChain = createAnalysisChain(PROMPTS.ANSWER_QUESTION);\n      const result = await answerChain.invoke({ content, question });\n      \n      return {\n        question,\n        answer: result,\n        confidence: 0.8, // This would be calculated based on model confidence\n        sources: [], // This would be extracted from the content\n      };\n    } catch (error) {\n      console.error('Error answering question:', error);\n      throw new Error('Failed to answer question');\n    }\n  }\n\n  async summarizeDocument(content: string): Promise<string> {\n    try {\n      const summaryChain = createAnalysisChain(PROMPTS.SUMMARIZE);\n      return await summaryChain.invoke({ content });\n    } catch (error) {\n      console.error('Error summarizing document:', error);\n      throw new Error('Failed to summarize document');\n    }\n  }\n\n  async extractKeyFindings(content: string): Promise<string[]> {\n    try {\n      const findingsChain = createAnalysisChain(PROMPTS.EXTRACT_KEY_FINDINGS);\n      const result = await findingsChain.invoke({ content });\n      \n      return this.parseListResult(result);\n    } catch (error) {\n      console.error('Error extracting key findings:', error);\n      return [];\n    }\n  }\n\n  private parseComprehensiveAnalysis(result: string): Partial<ComprehensiveAnalysis> {\n    const analysis: Partial<ComprehensiveAnalysis> = {\n      summary: '',\n      keyFindings: [],\n      methodology: '',\n      mainContributions: [],\n      criticalAnalysis: {\n        strengths: [],\n        weaknesses: [],\n        limitations: [],\n        implications: [],\n      },\n      technicalDetails: {\n        algorithms: [],\n        datasets: [],\n        metrics: [],\n        tools: [],\n      },\n      citations: {\n        keyReferences: [],\n        citationCount: 0,\n        referencedAuthors: <AUTHORS>\n      },\n      relatedTopics: [],\n    };\n\n    try {\n      // Parse sections using regex patterns\n      const summaryMatch = result.match(/## EXECUTIVE SUMMARY\\s*([\\s\\S]*?)(?=##|$)/);\n      if (summaryMatch) {\n        analysis.summary = summaryMatch[1].trim();\n      }\n\n      const findingsMatch = result.match(/## KEY FINDINGS\\s*([\\s\\S]*?)(?=##|$)/);\n      if (findingsMatch) {\n        analysis.keyFindings = this.parseListFromText(findingsMatch[1]);\n      }\n\n      const methodologyMatch = result.match(/## METHODOLOGY ANALYSIS\\s*([\\s\\S]*?)(?=##|$)/);\n      if (methodologyMatch) {\n        analysis.methodology = methodologyMatch[1].trim();\n      }\n\n      const contributionsMatch = result.match(/## MAIN CONTRIBUTIONS\\s*([\\s\\S]*?)(?=##|$)/);\n      if (contributionsMatch) {\n        analysis.mainContributions = this.parseListFromText(contributionsMatch[1]);\n      }\n\n      // Parse critical analysis\n      const strengthsMatch = result.match(/### Strengths:\\s*([\\s\\S]*?)(?=###|##|$)/);\n      if (strengthsMatch) {\n        analysis.criticalAnalysis!.strengths = this.parseListFromText(strengthsMatch[1]);\n      }\n\n      const weaknessesMatch = result.match(/### Weaknesses:\\s*([\\s\\S]*?)(?=###|##|$)/);\n      if (weaknessesMatch) {\n        analysis.criticalAnalysis!.weaknesses = this.parseListFromText(weaknessesMatch[1]);\n      }\n\n      const implicationsMatch = result.match(/### Implications:\\s*([\\s\\S]*?)(?=###|##|$)/);\n      if (implicationsMatch) {\n        analysis.criticalAnalysis!.implications = this.parseListFromText(implicationsMatch[1]);\n      }\n\n      // Parse technical details\n      const algorithmsMatch = result.match(/### Algorithms\\/Methods:\\s*([\\s\\S]*?)(?=###|##|$)/);\n      if (algorithmsMatch) {\n        analysis.technicalDetails!.algorithms = this.parseListFromText(algorithmsMatch[1]);\n      }\n\n      const datasetsMatch = result.match(/### Datasets:\\s*([\\s\\S]*?)(?=###|##|$)/);\n      if (datasetsMatch) {\n        analysis.technicalDetails!.datasets = this.parseListFromText(datasetsMatch[1]);\n      }\n\n      const metricsMatch = result.match(/### Evaluation Metrics:\\s*([\\s\\S]*?)(?=###|##|$)/);\n      if (metricsMatch) {\n        analysis.technicalDetails!.metrics = this.parseListFromText(metricsMatch[1]);\n      }\n\n      const toolsMatch = result.match(/### Tools\\/Technologies:\\s*([\\s\\S]*?)(?=###|##|$)/);\n      if (toolsMatch) {\n        analysis.technicalDetails!.tools = this.parseListFromText(toolsMatch[1]);\n      }\n\n    } catch (error) {\n      console.error('Error parsing comprehensive analysis:', error);\n    }\n\n    return analysis;\n  }\n\n  private parseConceptsResult(result: string): ConceptExtraction[] {\n    const concepts: ConceptExtraction[] = [];\n    \n    try {\n      // Split by numbered items or bullet points\n      const items = result.split(/\\d+\\.|[-•]\\s/).filter(item => item.trim());\n      \n      for (const item of items) {\n        const lines = item.trim().split('\\n').filter(line => line.trim());\n        if (lines.length > 0) {\n          const term = lines[0].replace(/[:\\-]/g, '').trim();\n          const definition = lines.slice(1).join(' ').trim();\n          \n          concepts.push({\n            term,\n            definition: definition || 'No definition provided',\n            importance: this.determineImportance(definition),\n            relatedTerms: [],\n            context: definition,\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Error parsing concepts:', error);\n    }\n    \n    return concepts;\n  }\n\n  private parseQuestionsResult(result: string): string[] {\n    try {\n      return result\n        .split(/\\d+\\./)\n        .map(q => q.trim())\n        .filter(q => q.length > 0 && q.includes('?'));\n    } catch (error) {\n      console.error('Error parsing questions:', error);\n      return [];\n    }\n  }\n\n  private parseListResult(result: string): string[] {\n    try {\n      return result\n        .split(/\\d+\\.|[-•]\\s/)\n        .map(item => item.trim())\n        .filter(item => item.length > 0);\n    } catch (error) {\n      console.error('Error parsing list result:', error);\n      return [];\n    }\n  }\n\n  private parseListFromText(text: string): string[] {\n    return text\n      .split(/\\d+\\.|[-•]\\s/)\n      .map(item => item.trim())\n      .filter(item => item.length > 0);\n  }\n\n  private determineImportance(definition: string): 'high' | 'medium' | 'low' {\n    const highKeywords = ['key', 'main', 'primary', 'central', 'core', 'fundamental', 'critical'];\n    const lowKeywords = ['minor', 'secondary', 'auxiliary', 'supplementary'];\n    \n    const lowerDef = definition.toLowerCase();\n    \n    if (highKeywords.some(keyword => lowerDef.includes(keyword))) {\n      return 'high';\n    } else if (lowKeywords.some(keyword => lowerDef.includes(keyword))) {\n      return 'low';\n    }\n    \n    return 'medium';\n  }\n}\n\nexport const aiAnalysisService = new AIAnalysisService();\n"], "names": [], "mappings": ";;;AAAA;;AA4CA,MAAM;IACI,QAAQ,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,IAAI;IAEtC,MAAM,gBAAgB,OAAe,EAAkC;QACrE,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,6BAA6B;YAC7B,MAAM,qBAAqB,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,yHAAA,CAAA,UAAO,CAAC,sBAAsB;YAC7E,MAAM,sBAAsB,MAAM,mBAAmB,MAAM,CAAC;gBAAE;YAAQ;YAEtE,0CAA0C;YAC1C,MAAM,WAAW,IAAI,CAAC,0BAA0B,CAAC;YAEjD,kDAAkD;YAClD,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC;YAE5C,qBAAqB;YACrB,MAAM,YAAY,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAE/C,OAAO;gBACL,GAAG,QAAQ;gBACX;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,gBAAgB,OAAe,EAAgC;QACnE,IAAI;YACF,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,yHAAA,CAAA,UAAO,CAAC,gBAAgB;YACjE,MAAM,SAAS,MAAM,aAAa,MAAM,CAAC;gBAAE;YAAQ;YAEnD,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,EAAE;QACX;IACF;IAEA,MAAM,kBAAkB,OAAe,EAAqB;QAC1D,IAAI;YACF,MAAM,gBAAgB,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,yHAAA,CAAA,UAAO,CAAC,kBAAkB;YACpE,MAAM,SAAS,MAAM,cAAc,MAAM,CAAC;gBAAE;YAAQ;YAEpD,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACX;IACF;IAEA,MAAM,eAAe,OAAe,EAAE,QAAgB,EAA2B;QAC/E,IAAI;YACF,MAAM,cAAc,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,yHAAA,CAAA,UAAO,CAAC,eAAe;YAC/D,MAAM,SAAS,MAAM,YAAY,MAAM,CAAC;gBAAE;gBAAS;YAAS;YAE5D,OAAO;gBACL;gBACA,QAAQ;gBACR,YAAY;gBACZ,SAAS,EAAE;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,kBAAkB,OAAe,EAAmB;QACxD,IAAI;YACF,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,yHAAA,CAAA,UAAO,CAAC,SAAS;YAC1D,OAAO,MAAM,aAAa,MAAM,CAAC;gBAAE;YAAQ;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,mBAAmB,OAAe,EAAqB;QAC3D,IAAI;YACF,MAAM,gBAAgB,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,yHAAA,CAAA,UAAO,CAAC,oBAAoB;YACtE,MAAM,SAAS,MAAM,cAAc,MAAM,CAAC;gBAAE;YAAQ;YAEpD,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,EAAE;QACX;IACF;IAEQ,2BAA2B,MAAc,EAAkC;QACjF,MAAM,WAA2C;YAC/C,SAAS;YACT,aAAa,EAAE;YACf,aAAa;YACb,mBAAmB,EAAE;YACrB,kBAAkB;gBAChB,WAAW,EAAE;gBACb,YAAY,EAAE;gBACd,aAAa,EAAE;gBACf,cAAc,EAAE;YAClB;YACA,kBAAkB;gBAChB,YAAY,EAAE;gBACd,UAAU,EAAE;gBACZ,SAAS,EAAE;gBACX,OAAO,EAAE;YACX;YACA,WAAW;gBACT,eAAe,EAAE;gBACjB,eAAe;gBACf,mBAAmB,EAAE;YACvB;YACA,eAAe,EAAE;QACnB;QAEA,IAAI;YACF,sCAAsC;YACtC,MAAM,eAAe,OAAO,KAAK,CAAC;YAClC,IAAI,cAAc;gBAChB,SAAS,OAAO,GAAG,YAAY,CAAC,EAAE,CAAC,IAAI;YACzC;YAEA,MAAM,gBAAgB,OAAO,KAAK,CAAC;YACnC,IAAI,eAAe;gBACjB,SAAS,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE;YAChE;YAEA,MAAM,mBAAmB,OAAO,KAAK,CAAC;YACtC,IAAI,kBAAkB;gBACpB,SAAS,WAAW,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI;YACjD;YAEA,MAAM,qBAAqB,OAAO,KAAK,CAAC;YACxC,IAAI,oBAAoB;gBACtB,SAAS,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,EAAE;YAC3E;YAEA,0BAA0B;YAC1B,MAAM,iBAAiB,OAAO,KAAK,CAAC;YACpC,IAAI,gBAAgB;gBAClB,SAAS,gBAAgB,CAAE,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,EAAE;YACjF;YAEA,MAAM,kBAAkB,OAAO,KAAK,CAAC;YACrC,IAAI,iBAAiB;gBACnB,SAAS,gBAAgB,CAAE,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE;YACnF;YAEA,MAAM,oBAAoB,OAAO,KAAK,CAAC;YACvC,IAAI,mBAAmB;gBACrB,SAAS,gBAAgB,CAAE,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,EAAE;YACvF;YAEA,0BAA0B;YAC1B,MAAM,kBAAkB,OAAO,KAAK,CAAC;YACrC,IAAI,iBAAiB;gBACnB,SAAS,gBAAgB,CAAE,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE;YACnF;YAEA,MAAM,gBAAgB,OAAO,KAAK,CAAC;YACnC,IAAI,eAAe;gBACjB,SAAS,gBAAgB,CAAE,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE;YAC/E;YAEA,MAAM,eAAe,OAAO,KAAK,CAAC;YAClC,IAAI,cAAc;gBAChB,SAAS,gBAAgB,CAAE,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE;YAC7E;YAEA,MAAM,aAAa,OAAO,KAAK,CAAC;YAChC,IAAI,YAAY;gBACd,SAAS,gBAAgB,CAAE,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;YACzE;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD;QAEA,OAAO;IACT;IAEQ,oBAAoB,MAAc,EAAuB;QAC/D,MAAM,WAAgC,EAAE;QAExC,IAAI;YACF,2CAA2C;YAC3C,MAAM,QAAQ,OAAO,KAAK,CAAC,gBAAgB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;YAEnE,KAAK,MAAM,QAAQ,MAAO;gBACxB,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;gBAC9D,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI;oBAChD,MAAM,aAAa,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI;oBAEhD,SAAS,IAAI,CAAC;wBACZ;wBACA,YAAY,cAAc;wBAC1B,YAAY,IAAI,CAAC,mBAAmB,CAAC;wBACrC,cAAc,EAAE;wBAChB,SAAS;oBACX;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;QAEA,OAAO;IACT;IAEQ,qBAAqB,MAAc,EAAY;QACrD,IAAI;YACF,OAAO,OACJ,KAAK,CAAC,SACN,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IACf,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,KAAK,EAAE,QAAQ,CAAC;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,EAAE;QACX;IACF;IAEQ,gBAAgB,MAAc,EAAY;QAChD,IAAI;YACF,OAAO,OACJ,KAAK,CAAC,gBACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,EAAE;QACX;IACF;IAEQ,kBAAkB,IAAY,EAAY;QAChD,OAAO,KACJ,KAAK,CAAC,gBACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAClC;IAEQ,oBAAoB,UAAkB,EAA6B;QACzE,MAAM,eAAe;YAAC;YAAO;YAAQ;YAAW;YAAW;YAAQ;YAAe;SAAW;QAC7F,MAAM,cAAc;YAAC;YAAS;YAAa;YAAa;SAAgB;QAExE,MAAM,WAAW,WAAW,WAAW;QAEvC,IAAI,aAAa,IAAI,CAAC,CAAA,UAAW,SAAS,QAAQ,CAAC,WAAW;YAC5D,OAAO;QACT,OAAO,IAAI,YAAY,IAAI,CAAC,CAAA,UAAW,SAAS,QAAQ,CAAC,WAAW;YAClE,OAAO;QACT;QAEA,OAAO;IACT;AACF;AAEO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/constants.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_CONFIG = {\n  name: 'DocuMancer',\n  version: '1.0.0',\n  description: 'AI-Powered Academic Paper Reading Assistant',\n  maxFileSize: 50 * 1024 * 1024, // 50MB\n  allowedFileTypes: ['.pdf'],\n  supportedFormats: ['PDF'],\n} as const;\n\nexport const COLORS = {\n  primary: '#1890ff',\n  secondary: '#722ed1',\n  success: '#52c41a',\n  warning: '#faad14',\n  error: '#ff4d4f',\n  text: {\n    primary: '#262626',\n    secondary: '#595959',\n    disabled: '#bfbfbf',\n  },\n  background: {\n    primary: '#ffffff',\n    secondary: '#fafafa',\n    tertiary: '#f5f5f5',\n  },\n  border: '#d9d9d9',\n} as const;\n\nexport const BREAKPOINTS = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600,\n} as const;\n\nexport const ROUTES = {\n  home: '/',\n  library: '/library',\n  reader: '/reader',\n  comparison: '/comparison',\n  analysis: '/analysis',\n  settings: '/settings',\n} as const;\n\nexport const API_ENDPOINTS = {\n  papers: '/api/papers',\n  upload: '/api/upload',\n  chat: '/api/chat',\n  analysis: '/api/analysis',\n  search: '/api/search',\n  comparison: '/api/comparison',\n} as const;\n\nexport const PAPER_FORMATS = {\n  ARXIV: 'arXiv',\n  IEEE: 'IEEE',\n  ACM: 'ACM',\n  SPRINGER: 'Springer',\n  ELSEVIER: 'Elsevier',\n  GENERIC: 'Generic',\n} as const;\n\nexport const ANALYSIS_TYPES = {\n  SUMMARY: 'summary',\n  KEY_FINDINGS: 'key_findings',\n  METHODOLOGY: 'methodology',\n  CONCEPTS: 'concepts',\n  CITATIONS: 'citations',\n  COMPARISON: 'comparison',\n} as const;\n\nexport const MESSAGE_TYPES = {\n  USER: 'user',\n  ASSISTANT: 'assistant',\n  SYSTEM: 'system',\n} as const;\n\nexport const ANNOTATION_TYPES = {\n  HIGHLIGHT: 'highlight',\n  NOTE: 'note',\n  BOOKMARK: 'bookmark',\n} as const;\n\nexport const VIEW_MODES = {\n  READER: 'reader',\n  LIBRARY: 'library',\n  COMPARISON: 'comparison',\n  ANALYSIS: 'analysis',\n} as const;\n\nexport const LOADING_MESSAGES = [\n  'Processing your document...',\n  'Extracting text content...',\n  'Analyzing paper structure...',\n  'Generating insights...',\n  'Almost ready...',\n] as const;\n\nexport const ERROR_MESSAGES = {\n  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',\n  INVALID_FILE_TYPE: 'Only PDF files are supported',\n  UPLOAD_FAILED: 'Failed to upload file. Please try again.',\n  PROCESSING_FAILED: 'Failed to process the document',\n  API_ERROR: 'An error occurred while communicating with the server',\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  GENERIC_ERROR: 'An unexpected error occurred',\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;;;;AAEjB,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,aAAa;IACb,aAAa,KAAK,OAAO;IACzB,kBAAkB;QAAC;KAAO;IAC1B,kBAAkB;QAAC;KAAM;AAC3B;AAEO,MAAM,SAAS;IACpB,SAAS;IACT,WAAW;IACX,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;QACJ,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,YAAY;QACV,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,QAAQ;AACV;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;AACP;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,SAAS;IACT,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,UAAU;AACZ;AAEO,MAAM,gBAAgB;IAC3B,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,UAAU;IACV,QAAQ;IACR,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,OAAO;IACP,MAAM;IACN,KAAK;IACL,UAAU;IACV,UAAU;IACV,SAAS;AACX;AAEO,MAAM,iBAAiB;IAC5B,SAAS;IACT,cAAc;IACd,aAAa;IACb,UAAU;IACV,WAAW;IACX,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,MAAM;IACN,WAAW;IACX,QAAQ;AACV;AAEO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,UAAU;AACZ;AAEO,MAAM,aAAa;IACxB,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,UAAU;AACZ;AAEO,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,mBAAmB;IACnB,eAAe;IACf,mBAAmB;IACnB,WAAW;IACX,eAAe;IACf,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { aiAnalysisService } from '@/lib/ai-analysis-service';\nimport { ChatMessage } from '@/lib/types';\nimport { ERROR_MESSAGES } from '@/lib/constants';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { message, paperId, paperContent, context } = body;\n\n    if (!message) {\n      return NextResponse.json(\n        { success: false, error: 'Message is required' },\n        { status: 400 }\n      );\n    }\n\n    if (!paperContent) {\n      return NextResponse.json(\n        { success: false, error: 'Paper content is required for analysis' },\n        { status: 400 }\n      );\n    }\n\n    // Generate response using AI service\n    const answerResult = await aiAnalysisService.answerQuestion(paperContent, message);\n    const response = answerResult.answer;\n\n    // Create response message\n    const responseMessage: ChatMessage = {\n      id: `msg_${Date.now()}_assistant`,\n      role: 'assistant',\n      content: response,\n      timestamp: new Date(),\n      paperId,\n      context,\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        message: responseMessage,\n      },\n    });\n\n  } catch (error) {\n    console.error('Chat error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: ERROR_MESSAGES.API_ERROR,\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// Handle streaming responses for real-time chat\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const message = searchParams.get('message');\n  const paperContent = searchParams.get('content');\n\n  if (!message || !paperContent) {\n    return NextResponse.json(\n      { success: false, error: 'Message and content are required' },\n      { status: 400 }\n    );\n  }\n\n  try {\n    // Create a streaming response\n    const encoder = new TextEncoder();\n    const stream = new ReadableStream({\n      async start(controller) {\n        try {\n          const answerResult = await aiAnalysisService.answerQuestion(paperContent, message);\n          const response = answerResult.answer;\n          \n          // Simulate streaming by sending chunks\n          const chunks = response.split(' ');\n          for (let i = 0; i < chunks.length; i++) {\n            const chunk = chunks[i] + (i < chunks.length - 1 ? ' ' : '');\n            controller.enqueue(encoder.encode(`data: ${JSON.stringify({ chunk })}\\n\\n`));\n            \n            // Add small delay to simulate streaming\n            await new Promise(resolve => setTimeout(resolve, 50));\n          }\n          \n          controller.enqueue(encoder.encode('data: [DONE]\\n\\n'));\n          controller.close();\n        } catch (error) {\n          controller.error(error);\n        }\n      },\n    });\n\n    return new Response(stream, {\n      headers: {\n        'Content-Type': 'text/event-stream',\n        'Cache-Control': 'no-cache',\n        'Connection': 'keep-alive',\n      },\n    });\n\n  } catch (error) {\n    console.error('Streaming chat error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: ERROR_MESSAGES.API_ERROR,\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG;QAEpD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAsB,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyC,GAClE;gBAAE,QAAQ;YAAI;QAElB;QAEA,qCAAqC;QACrC,MAAM,eAAe,MAAM,yIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,cAAc;QAC1E,MAAM,WAAW,aAAa,MAAM;QAEpC,0BAA0B;QAC1B,MAAM,kBAA+B;YACnC,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,UAAU,CAAC;YACjC,MAAM;YACN,SAAS;YACT,WAAW,IAAI;YACf;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,yHAAA,CAAA,iBAAc,CAAC,SAAS;YAC/B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;IACjC,MAAM,eAAe,aAAa,GAAG,CAAC;IAEtC,IAAI,CAAC,WAAW,CAAC,cAAc;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAmC,GAC5D;YAAE,QAAQ;QAAI;IAElB;IAEA,IAAI;QACF,8BAA8B;QAC9B,MAAM,UAAU,IAAI;QACpB,MAAM,SAAS,IAAI,eAAe;YAChC,MAAM,OAAM,UAAU;gBACpB,IAAI;oBACF,MAAM,eAAe,MAAM,yIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,cAAc;oBAC1E,MAAM,WAAW,aAAa,MAAM;oBAEpC,uCAAuC;oBACvC,MAAM,SAAS,SAAS,KAAK,CAAC;oBAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;wBACtC,MAAM,QAAQ,MAAM,CAAC,EAAE,GAAG,CAAC,IAAI,OAAO,MAAM,GAAG,IAAI,MAAM,EAAE;wBAC3D,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;4BAAE;wBAAM,GAAG,IAAI,CAAC;wBAE1E,wCAAwC;wBACxC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACnD;oBAEA,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;oBAClC,WAAW,KAAK;gBAClB,EAAE,OAAO,OAAO;oBACd,WAAW,KAAK,CAAC;gBACnB;YACF;QACF;QAEA,OAAO,IAAI,SAAS,QAAQ;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;gBACjB,cAAc;YAChB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,yHAAA,CAAA,iBAAc,CAAC,SAAS;YAC/B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
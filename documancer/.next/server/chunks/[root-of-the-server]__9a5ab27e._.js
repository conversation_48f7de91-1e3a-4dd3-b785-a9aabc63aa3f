module.exports = {

"[project]/.next-internal/server/app/api/chat/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/src/lib/langchain.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PROMPTS": ()=>PROMPTS,
    "analyzeCitations": ()=>analyzeCitations,
    "answerQuestion": ()=>answerQuestion,
    "comparePapers": ()=>comparePapers,
    "createAnalysisChain": ()=>createAnalysisChain,
    "createDeepSeekModel": ()=>createDeepSeekModel,
    "explainMethodology": ()=>explainMethodology,
    "extractConcepts": ()=>extractConcepts,
    "extractKeyFindings": ()=>extractKeyFindings,
    "summarizePaper": ()=>summarizePaper
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/chat_models.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/prompts.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/prompts/prompt.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$output_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/output_parsers.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$string$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/output_parsers/string.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$runnables$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/runnables.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$runnables$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/runnables/base.js [app-route] (ecmascript)");
;
;
;
;
const createDeepSeekModel = ()=>{
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ChatOpenAI"]({
        model: 'deepseek-chat',
        apiKey: process.env.DEEPSEEK_API_KEY,
        configuration: {
            baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1'
        },
        temperature: 0.1,
        maxTokens: 4000
    });
};
const PROMPTS = {
    COMPREHENSIVE_ANALYSIS: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    You are an expert academic paper analyst with deep knowledge across multiple research domains.
    Analyze the following research paper comprehensively and provide detailed insights.

    Paper Content:
    {content}

    Please provide a thorough analysis in the following structured format:

    ## EXECUTIVE SUMMARY
    - Provide a 3-4 sentence summary of the paper's core contribution
    - State the main research question and hypothesis

    ## KEY FINDINGS
    - List 5-7 most significant findings with brief explanations
    - Include quantitative results where available
    - Highlight novel discoveries or insights

    ## METHODOLOGY ANALYSIS
    - Describe the research approach and experimental design
    - Evaluate the appropriateness of methods used
    - Identify any methodological innovations or limitations

    ## MAIN CONTRIBUTIONS
    - List theoretical contributions to the field
    - Identify practical applications and implications
    - Note any new frameworks, models, or algorithms introduced

    ## CRITICAL ANALYSIS
    ### Strengths:
    - What are the paper's strongest points?
    - What makes this research valuable?

    ### Weaknesses:
    - What are potential limitations or gaps?
    - What could be improved?

    ### Implications:
    - How does this work advance the field?
    - What are the broader implications?

    ## TECHNICAL DETAILS
    ### Algorithms/Methods:
    - List key algorithms or computational methods

    ### Datasets:
    - Identify datasets used for experiments

    ### Evaluation Metrics:
    - List performance metrics and evaluation criteria

    ### Tools/Technologies:
    - Note software, frameworks, or tools mentioned

    ## CITATIONS AND REFERENCES
    - Identify 3-5 most important references cited
    - Note influential authors or research groups mentioned
    - Estimate the paper's position in the research landscape

    Be thorough, accurate, and provide specific details from the paper.
  `),
    EXTRACT_CONCEPTS: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Extract and analyze key concepts, terminology, and technical terms from this research paper.

    Paper Content:
    {content}

    For each important concept, provide:
    1. The term/concept name
    2. A clear definition or explanation
    3. Its importance level (high/medium/low)
    4. Related concepts or dependencies
    5. Context within the paper

    Focus on:
    - Technical terminology
    - Theoretical concepts
    - Methodological approaches
    - Domain-specific terms
    - Novel concepts introduced

    Format as a structured list with explanations.
  `),
    EXPLAIN_METHODOLOGY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Analyze the methodology section of this research paper and provide a clear explanation.

    Paper Content:
    {content}

    Please explain:
    1. Research design and approach
    2. Data collection methods
    3. Analysis techniques used
    4. Tools and technologies employed
    5. Experimental setup (if applicable)
    6. Validation methods
    7. Limitations of the methodology

    Make the explanation accessible to researchers in related fields.
  `),
    GENERATE_QUESTIONS: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Generate thoughtful questions about this research paper that would help readers better understand the work.

    Paper Content:
    {content}

    Generate 8-10 questions covering:
    1. Clarification questions about methodology
    2. Questions about implications and applications
    3. Questions about limitations and future work
    4. Questions comparing to related work
    5. Questions about technical details

    Format as a numbered list with clear, specific questions.
  `),
    SUMMARIZE: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Provide a concise summary of this research paper.

    Paper Content:
    {content}

    Include:
    1. Main research question and objectives
    2. Key methodology
    3. Primary findings
    4. Significance and implications

    Keep it concise but comprehensive.
  `),
    EXTRACT_KEY_FINDINGS: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Extract the key findings and contributions from this research paper.

    Paper Content:
    {content}

    List:
    1. Main research findings
    2. Novel contributions
    3. Experimental results
    4. Theoretical insights
    5. Practical applications

    Be specific and detailed.
  `),
    ANSWER_QUESTION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    You are an AI assistant helping researchers understand academic papers. Answer the following question based on the paper content provided.

    Paper Content:
    {content}

    Question: {question}

    Please provide a comprehensive answer that:
    1. Directly addresses the question
    2. References specific sections of the paper when relevant
    3. Provides context and background if needed
    4. Mentions any limitations or uncertainties
    5. Suggests related questions or areas for further exploration

    If the question cannot be answered from the provided content, clearly state this and explain why.
  `),
    COMPARE_PAPERS: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Compare and analyze the following research papers. Identify similarities, differences, and relationships between them.

    Paper 1:
    {paper1}

    Paper 2:
    {paper2}

    Please provide:
    1. Common themes and research areas
    2. Methodological similarities and differences
    3. Complementary findings or conflicting results
    4. How the papers build upon or relate to each other
    5. Gaps that could be addressed by combining insights
    6. Recommendations for researchers interested in this area

    Structure your comparison clearly with specific examples from both papers.
  `),
    ANALYZE_CITATIONS: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Analyze the citations and references in this research paper to understand its academic context.

    Paper Content:
    {content}

    Please identify:
    1. Key foundational works cited
    2. Recent developments referenced
    3. Main research communities or schools of thought
    4. Gaps in the literature identified by the authors
    5. How this work positions itself relative to existing research
    6. Potential future research directions suggested

    Focus on understanding the academic landscape and research trajectory.
  `)
};
const createAnalysisChain = (promptTemplate)=>{
    const model = createDeepSeekModel();
    const outputParser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$string$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["StringOutputParser"]();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$runnables$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RunnableSequence"].from([
        promptTemplate,
        model,
        outputParser
    ]);
};
const summarizePaper = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.SUMMARIZE);
    return await chain.invoke({
        content
    });
};
const extractKeyFindings = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.EXTRACT_KEY_FINDINGS);
    return await chain.invoke({
        content
    });
};
const explainMethodology = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.EXPLAIN_METHODOLOGY);
    return await chain.invoke({
        content
    });
};
const extractConcepts = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.EXTRACT_CONCEPTS);
    return await chain.invoke({
        content
    });
};
const answerQuestion = async (content, question)=>{
    const chain = createAnalysisChain(PROMPTS.ANSWER_QUESTION);
    return await chain.invoke({
        content,
        question
    });
};
const comparePapers = async (paper1, paper2)=>{
    const chain = createAnalysisChain(PROMPTS.COMPARE_PAPERS);
    return await chain.invoke({
        paper1,
        paper2
    });
};
const analyzeCitations = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.ANALYZE_CITATIONS);
    return await chain.invoke({
        content
    });
};
}),
"[project]/src/lib/ai-analysis-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "aiAnalysisService": ()=>aiAnalysisService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/langchain.ts [app-route] (ecmascript)");
;
class AIAnalysisService {
    model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createDeepSeekModel"])();
    async analyzeDocument(content) {
        try {
            console.log('Starting comprehensive document analysis...');
            // Run comprehensive analysis
            const comprehensiveChain = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAnalysisChain"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PROMPTS"].COMPREHENSIVE_ANALYSIS);
            const comprehensiveResult = await comprehensiveChain.invoke({
                content
            });
            // Parse the comprehensive analysis result
            const analysis = this.parseComprehensiveAnalysis(comprehensiveResult);
            // Extract concepts separately for better accuracy
            const concepts = await this.extractConcepts(content);
            // Generate questions
            const questions = await this.generateQuestions(content);
            return {
                ...analysis,
                concepts,
                questions
            };
        } catch (error) {
            console.error('Error in document analysis:', error);
            throw new Error('Failed to analyze document');
        }
    }
    async extractConcepts(content) {
        try {
            const conceptChain = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAnalysisChain"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PROMPTS"].EXTRACT_CONCEPTS);
            const result = await conceptChain.invoke({
                content
            });
            return this.parseConceptsResult(result);
        } catch (error) {
            console.error('Error extracting concepts:', error);
            return [];
        }
    }
    async generateQuestions(content) {
        try {
            const questionChain = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAnalysisChain"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PROMPTS"].GENERATE_QUESTIONS);
            const result = await questionChain.invoke({
                content
            });
            return this.parseQuestionsResult(result);
        } catch (error) {
            console.error('Error generating questions:', error);
            return [];
        }
    }
    async answerQuestion(content, question) {
        try {
            const answerChain = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAnalysisChain"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PROMPTS"].ANSWER_QUESTION);
            const result = await answerChain.invoke({
                content,
                question
            });
            return {
                question,
                answer: result,
                confidence: 0.8,
                sources: []
            };
        } catch (error) {
            console.error('Error answering question:', error);
            throw new Error('Failed to answer question');
        }
    }
    async summarizeDocument(content) {
        try {
            const summaryChain = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAnalysisChain"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PROMPTS"].SUMMARIZE);
            return await summaryChain.invoke({
                content
            });
        } catch (error) {
            console.error('Error summarizing document:', error);
            throw new Error('Failed to summarize document');
        }
    }
    async extractKeyFindings(content) {
        try {
            const findingsChain = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAnalysisChain"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PROMPTS"].EXTRACT_KEY_FINDINGS);
            const result = await findingsChain.invoke({
                content
            });
            return this.parseListResult(result);
        } catch (error) {
            console.error('Error extracting key findings:', error);
            return [];
        }
    }
    parseComprehensiveAnalysis(result) {
        const analysis = {
            summary: '',
            keyFindings: [],
            methodology: '',
            mainContributions: [],
            criticalAnalysis: {
                strengths: [],
                weaknesses: [],
                limitations: [],
                implications: []
            },
            technicalDetails: {
                algorithms: [],
                datasets: [],
                metrics: [],
                tools: []
            },
            citations: {
                keyReferences: [],
                citationCount: 0,
                referencedAuthors: <AUTHORS>
            },
            relatedTopics: []
        };
        try {
            // Parse sections using regex patterns
            const summaryMatch = result.match(/## EXECUTIVE SUMMARY\s*([\s\S]*?)(?=##|$)/);
            if (summaryMatch) {
                analysis.summary = summaryMatch[1].trim();
            }
            const findingsMatch = result.match(/## KEY FINDINGS\s*([\s\S]*?)(?=##|$)/);
            if (findingsMatch) {
                analysis.keyFindings = this.parseListFromText(findingsMatch[1]);
            }
            const methodologyMatch = result.match(/## METHODOLOGY ANALYSIS\s*([\s\S]*?)(?=##|$)/);
            if (methodologyMatch) {
                analysis.methodology = methodologyMatch[1].trim();
            }
            const contributionsMatch = result.match(/## MAIN CONTRIBUTIONS\s*([\s\S]*?)(?=##|$)/);
            if (contributionsMatch) {
                analysis.mainContributions = this.parseListFromText(contributionsMatch[1]);
            }
            // Parse critical analysis
            const strengthsMatch = result.match(/### Strengths:\s*([\s\S]*?)(?=###|##|$)/);
            if (strengthsMatch) {
                analysis.criticalAnalysis.strengths = this.parseListFromText(strengthsMatch[1]);
            }
            const weaknessesMatch = result.match(/### Weaknesses:\s*([\s\S]*?)(?=###|##|$)/);
            if (weaknessesMatch) {
                analysis.criticalAnalysis.weaknesses = this.parseListFromText(weaknessesMatch[1]);
            }
            const implicationsMatch = result.match(/### Implications:\s*([\s\S]*?)(?=###|##|$)/);
            if (implicationsMatch) {
                analysis.criticalAnalysis.implications = this.parseListFromText(implicationsMatch[1]);
            }
            // Parse technical details
            const algorithmsMatch = result.match(/### Algorithms\/Methods:\s*([\s\S]*?)(?=###|##|$)/);
            if (algorithmsMatch) {
                analysis.technicalDetails.algorithms = this.parseListFromText(algorithmsMatch[1]);
            }
            const datasetsMatch = result.match(/### Datasets:\s*([\s\S]*?)(?=###|##|$)/);
            if (datasetsMatch) {
                analysis.technicalDetails.datasets = this.parseListFromText(datasetsMatch[1]);
            }
            const metricsMatch = result.match(/### Evaluation Metrics:\s*([\s\S]*?)(?=###|##|$)/);
            if (metricsMatch) {
                analysis.technicalDetails.metrics = this.parseListFromText(metricsMatch[1]);
            }
            const toolsMatch = result.match(/### Tools\/Technologies:\s*([\s\S]*?)(?=###|##|$)/);
            if (toolsMatch) {
                analysis.technicalDetails.tools = this.parseListFromText(toolsMatch[1]);
            }
        } catch (error) {
            console.error('Error parsing comprehensive analysis:', error);
        }
        return analysis;
    }
    parseConceptsResult(result) {
        const concepts = [];
        try {
            // Split by numbered items or bullet points
            const items = result.split(/\d+\.|[-•]\s/).filter((item)=>item.trim());
            for (const item of items){
                const lines = item.trim().split('\n').filter((line)=>line.trim());
                if (lines.length > 0) {
                    const term = lines[0].replace(/[:\-]/g, '').trim();
                    const definition = lines.slice(1).join(' ').trim();
                    concepts.push({
                        term,
                        definition: definition || 'No definition provided',
                        importance: this.determineImportance(definition),
                        relatedTerms: [],
                        context: definition
                    });
                }
            }
        } catch (error) {
            console.error('Error parsing concepts:', error);
        }
        return concepts;
    }
    parseQuestionsResult(result) {
        try {
            return result.split(/\d+\./).map((q)=>q.trim()).filter((q)=>q.length > 0 && q.includes('?'));
        } catch (error) {
            console.error('Error parsing questions:', error);
            return [];
        }
    }
    parseListResult(result) {
        try {
            return result.split(/\d+\.|[-•]\s/).map((item)=>item.trim()).filter((item)=>item.length > 0);
        } catch (error) {
            console.error('Error parsing list result:', error);
            return [];
        }
    }
    parseListFromText(text) {
        return text.split(/\d+\.|[-•]\s/).map((item)=>item.trim()).filter((item)=>item.length > 0);
    }
    determineImportance(definition) {
        const highKeywords = [
            'key',
            'main',
            'primary',
            'central',
            'core',
            'fundamental',
            'critical'
        ];
        const lowKeywords = [
            'minor',
            'secondary',
            'auxiliary',
            'supplementary'
        ];
        const lowerDef = definition.toLowerCase();
        if (highKeywords.some((keyword)=>lowerDef.includes(keyword))) {
            return 'high';
        } else if (lowKeywords.some((keyword)=>lowerDef.includes(keyword))) {
            return 'low';
        }
        return 'medium';
    }
}
const aiAnalysisService = new AIAnalysisService();
}),
"[project]/src/lib/constants.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Application constants
__turbopack_context__.s({
    "ANALYSIS_TYPES": ()=>ANALYSIS_TYPES,
    "ANNOTATION_TYPES": ()=>ANNOTATION_TYPES,
    "API_ENDPOINTS": ()=>API_ENDPOINTS,
    "APP_CONFIG": ()=>APP_CONFIG,
    "BREAKPOINTS": ()=>BREAKPOINTS,
    "COLORS": ()=>COLORS,
    "ERROR_MESSAGES": ()=>ERROR_MESSAGES,
    "LOADING_MESSAGES": ()=>LOADING_MESSAGES,
    "MESSAGE_TYPES": ()=>MESSAGE_TYPES,
    "PAPER_FORMATS": ()=>PAPER_FORMATS,
    "ROUTES": ()=>ROUTES,
    "VIEW_MODES": ()=>VIEW_MODES
});
const APP_CONFIG = {
    name: 'DocuMancer',
    version: '1.0.0',
    description: 'AI-Powered Academic Paper Reading Assistant',
    maxFileSize: 50 * 1024 * 1024,
    allowedFileTypes: [
        '.pdf'
    ],
    supportedFormats: [
        'PDF'
    ]
};
const COLORS = {
    primary: '#1890ff',
    secondary: '#722ed1',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    text: {
        primary: '#262626',
        secondary: '#595959',
        disabled: '#bfbfbf'
    },
    background: {
        primary: '#ffffff',
        secondary: '#fafafa',
        tertiary: '#f5f5f5'
    },
    border: '#d9d9d9'
};
const BREAKPOINTS = {
    xs: 480,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1600
};
const ROUTES = {
    home: '/',
    library: '/library',
    reader: '/reader',
    comparison: '/comparison',
    analysis: '/analysis',
    settings: '/settings'
};
const API_ENDPOINTS = {
    papers: '/api/papers',
    upload: '/api/upload',
    chat: '/api/chat',
    analysis: '/api/analysis',
    search: '/api/search',
    comparison: '/api/comparison'
};
const PAPER_FORMATS = {
    ARXIV: 'arXiv',
    IEEE: 'IEEE',
    ACM: 'ACM',
    SPRINGER: 'Springer',
    ELSEVIER: 'Elsevier',
    GENERIC: 'Generic'
};
const ANALYSIS_TYPES = {
    SUMMARY: 'summary',
    KEY_FINDINGS: 'key_findings',
    METHODOLOGY: 'methodology',
    CONCEPTS: 'concepts',
    CITATIONS: 'citations',
    COMPARISON: 'comparison'
};
const MESSAGE_TYPES = {
    USER: 'user',
    ASSISTANT: 'assistant',
    SYSTEM: 'system'
};
const ANNOTATION_TYPES = {
    HIGHLIGHT: 'highlight',
    NOTE: 'note',
    BOOKMARK: 'bookmark'
};
const VIEW_MODES = {
    READER: 'reader',
    LIBRARY: 'library',
    COMPARISON: 'comparison',
    ANALYSIS: 'analysis'
};
const LOADING_MESSAGES = [
    'Processing your document...',
    'Extracting text content...',
    'Analyzing paper structure...',
    'Generating insights...',
    'Almost ready...'
];
const ERROR_MESSAGES = {
    FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',
    INVALID_FILE_TYPE: 'Only PDF files are supported',
    UPLOAD_FAILED: 'Failed to upload file. Please try again.',
    PROCESSING_FAILED: 'Failed to process the document',
    API_ERROR: 'An error occurred while communicating with the server',
    NETWORK_ERROR: 'Network error. Please check your connection.',
    GENERIC_ERROR: 'An unexpected error occurred'
};
}),
"[project]/src/app/api/chat/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$analysis$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-analysis-service.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { message, paperId, paperContent, context } = body;
        if (!message) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Message is required'
            }, {
                status: 400
            });
        }
        if (!paperContent) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Paper content is required for analysis'
            }, {
                status: 400
            });
        }
        // Generate response using AI service
        const answerResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$analysis$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiAnalysisService"].answerQuestion(paperContent, message);
        const response = answerResult.answer;
        // Create response message
        const responseMessage = {
            id: `msg_${Date.now()}_assistant`,
            role: 'assistant',
            content: response,
            timestamp: new Date(),
            paperId,
            context
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                message: responseMessage
            }
        });
    } catch (error) {
        console.error('Chat error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].API_ERROR,
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function GET(request) {
    const { searchParams } = new URL(request.url);
    const message = searchParams.get('message');
    const paperContent = searchParams.get('content');
    if (!message || !paperContent) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Message and content are required'
        }, {
            status: 400
        });
    }
    try {
        // Create a streaming response
        const encoder = new TextEncoder();
        const stream = new ReadableStream({
            async start (controller) {
                try {
                    const answerResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$analysis$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiAnalysisService"].answerQuestion(paperContent, message);
                    const response = answerResult.answer;
                    // Simulate streaming by sending chunks
                    const chunks = response.split(' ');
                    for(let i = 0; i < chunks.length; i++){
                        const chunk = chunks[i] + (i < chunks.length - 1 ? ' ' : '');
                        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                            chunk
                        })}\n\n`));
                        // Add small delay to simulate streaming
                        await new Promise((resolve)=>setTimeout(resolve, 50));
                    }
                    controller.enqueue(encoder.encode('data: [DONE]\n\n'));
                    controller.close();
                } catch (error) {
                    controller.error(error);
                }
            }
        });
        return new Response(stream, {
            headers: {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive'
            }
        });
    } catch (error) {
        console.error('Streaming chat error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].API_ERROR,
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9a5ab27e._.js.map
module.exports = {

"[project]/node_modules/pdf-parse/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_100_build_pdf_worker_52c9fc57.js",
  "server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_100_build_pdf_71c3c8b3.js",
  "server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_88_build_pdf_worker_ea871991.js",
  "server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_88_build_pdf_36252b84.js",
  "server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_9_426_build_pdf_worker_c153a02e.js",
  "server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_9_426_build_pdf_fe79cad9.js",
  "server/chunks/node_modules_pdf-parse_lib_pdf_js_v2_0_550_build_pdf_worker_4a586fdb.js",
  "server/chunks/node_modules_pdf-parse_lib_pdf_js_v2_0_550_build_pdf_8d84ce2e.js",
  "server/chunks/node_modules_pdf-parse_lib_pdf-parse_30edfd6e.js",
  "server/chunks/node_modules_pdf-parse_index_f716ad8d.js",
  "server/chunks/node_modules_node-ensure_index_7f950531.js",
  "server/chunks/[root-of-the-server]__85d7419c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/pdf-parse/index.js [app-route] (ecmascript)");
    });
});
}),

};
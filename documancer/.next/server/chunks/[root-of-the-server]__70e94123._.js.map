{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/app/api/annotations/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { writeFile, readFile, mkdir } from 'fs/promises';\nimport { join } from 'path';\nimport { existsSync } from 'fs';\nimport { Annotation, AnnotationGroup } from '@/lib/annotation-types';\n\nconst ANNOTATIONS_DIR = join(process.cwd(), 'data', 'annotations');\n\n// Ensure annotations directory exists\nasync function ensureAnnotationsDir() {\n  if (!existsSync(ANNOTATIONS_DIR)) {\n    await mkdir(ANNOTATIONS_DIR, { recursive: true });\n  }\n}\n\n// Get annotations file path for a paper\nfunction getAnnotationsFilePath(paperId: string): string {\n  return join(ANNOTATIONS_DIR, `${paperId}.json`);\n}\n\n// Load annotations for a paper\nasync function loadAnnotations(paperId: string): Promise<Annotation[]> {\n  const filePath = getAnnotationsFilePath(paperId);\n  \n  if (!existsSync(filePath)) {\n    return [];\n  }\n\n  try {\n    const data = await readFile(filePath, 'utf-8');\n    const group: AnnotationGroup = JSON.parse(data);\n    return group.annotations || [];\n  } catch (error) {\n    console.error('Failed to load annotations:', error);\n    return [];\n  }\n}\n\n// Save annotations for a paper\nasync function saveAnnotations(paperId: string, annotations: Annotation[]): Promise<void> {\n  await ensureAnnotationsDir();\n  \n  const group: AnnotationGroup = {\n    paperId,\n    annotations,\n    lastModified: new Date(),\n  };\n\n  const filePath = getAnnotationsFilePath(paperId);\n  await writeFile(filePath, JSON.stringify(group, null, 2));\n}\n\n// GET /api/annotations?paperId=xxx - Get all annotations for a paper\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const paperId = searchParams.get('paperId');\n\n    if (!paperId) {\n      return NextResponse.json(\n        { success: false, error: 'Paper ID is required' },\n        { status: 400 }\n      );\n    }\n\n    const annotations = await loadAnnotations(paperId);\n\n    return NextResponse.json({\n      success: true,\n      data: annotations,\n    });\n  } catch (error) {\n    console.error('Failed to get annotations:', error);\n    return NextResponse.json(\n      { success: false, error: 'Failed to load annotations' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/annotations - Create or update an annotation\nexport async function POST(request: NextRequest) {\n  try {\n    const annotation: Annotation = await request.json();\n\n    if (!annotation.paperId || !annotation.id) {\n      return NextResponse.json(\n        { success: false, error: 'Paper ID and annotation ID are required' },\n        { status: 400 }\n      );\n    }\n\n    // Load existing annotations\n    const annotations = await loadAnnotations(annotation.paperId);\n\n    // Find existing annotation or add new one\n    const existingIndex = annotations.findIndex(a => a.id === annotation.id);\n    \n    if (existingIndex >= 0) {\n      // Update existing annotation\n      annotations[existingIndex] = {\n        ...annotation,\n        updatedAt: new Date(),\n      };\n    } else {\n      // Add new annotation\n      annotations.push({\n        ...annotation,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      });\n    }\n\n    // Save annotations\n    await saveAnnotations(annotation.paperId, annotations);\n\n    return NextResponse.json({\n      success: true,\n      data: annotation,\n    });\n  } catch (error) {\n    console.error('Failed to save annotation:', error);\n    return NextResponse.json(\n      { success: false, error: 'Failed to save annotation' },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/annotations - Update an annotation\nexport async function PUT(request: NextRequest) {\n  return POST(request); // Same logic as POST for upsert\n}\n\n// DELETE /api/annotations?paperId=xxx&annotationId=xxx - Delete an annotation\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const paperId = searchParams.get('paperId');\n    const annotationId = searchParams.get('annotationId');\n\n    if (!paperId || !annotationId) {\n      return NextResponse.json(\n        { success: false, error: 'Paper ID and annotation ID are required' },\n        { status: 400 }\n      );\n    }\n\n    // Load existing annotations\n    const annotations = await loadAnnotations(paperId);\n\n    // Filter out the annotation to delete\n    const filteredAnnotations = annotations.filter(a => a.id !== annotationId);\n\n    if (filteredAnnotations.length === annotations.length) {\n      return NextResponse.json(\n        { success: false, error: 'Annotation not found' },\n        { status: 404 }\n      );\n    }\n\n    // Save updated annotations\n    await saveAnnotations(paperId, filteredAnnotations);\n\n    return NextResponse.json({\n      success: true,\n      message: 'Annotation deleted successfully',\n    });\n  } catch (error) {\n    console.error('Failed to delete annotation:', error);\n    return NextResponse.json(\n      { success: false, error: 'Failed to delete annotation' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAGA,MAAM,kBAAkB,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI,QAAQ;AAEpD,sCAAsC;AACtC,eAAe;IACb,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;QAChC,MAAM,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE,iBAAiB;YAAE,WAAW;QAAK;IACjD;AACF;AAEA,wCAAwC;AACxC,SAAS,uBAAuB,OAAe;IAC7C,OAAO,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,GAAG,QAAQ,KAAK,CAAC;AAChD;AAEA,+BAA+B;AAC/B,eAAe,gBAAgB,OAAe;IAC5C,MAAM,WAAW,uBAAuB;IAExC,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,WAAW;QACzB,OAAO,EAAE;IACX;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;QACtC,MAAM,QAAyB,KAAK,KAAK,CAAC;QAC1C,OAAO,MAAM,WAAW,IAAI,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,EAAE;IACX;AACF;AAEA,+BAA+B;AAC/B,eAAe,gBAAgB,OAAe,EAAE,WAAyB;IACvE,MAAM;IAEN,MAAM,QAAyB;QAC7B;QACA;QACA,cAAc,IAAI;IACpB;IAEA,MAAM,WAAW,uBAAuB;IACxC,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,UAAU,KAAK,SAAS,CAAC,OAAO,MAAM;AACxD;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAuB,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,MAAM,gBAAgB;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA6B,GACtD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,aAAyB,MAAM,QAAQ,IAAI;QAEjD,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,EAAE,EAAE;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA0C,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,4BAA4B;QAC5B,MAAM,cAAc,MAAM,gBAAgB,WAAW,OAAO;QAE5D,0CAA0C;QAC1C,MAAM,gBAAgB,YAAY,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;QAEvE,IAAI,iBAAiB,GAAG;YACtB,6BAA6B;YAC7B,WAAW,CAAC,cAAc,GAAG;gBAC3B,GAAG,UAAU;gBACb,WAAW,IAAI;YACjB;QACF,OAAO;YACL,qBAAqB;YACrB,YAAY,IAAI,CAAC;gBACf,GAAG,UAAU;gBACb,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;QACF;QAEA,mBAAmB;QACnB,MAAM,gBAAgB,WAAW,OAAO,EAAE;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA4B,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,OAAO,KAAK,UAAU,gCAAgC;AACxD;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,MAAM,eAAe,aAAa,GAAG,CAAC;QAEtC,IAAI,CAAC,WAAW,CAAC,cAAc;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA0C,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,4BAA4B;QAC5B,MAAM,cAAc,MAAM,gBAAgB;QAE1C,sCAAsC;QACtC,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAE7D,IAAI,oBAAoB,MAAM,KAAK,YAAY,MAAM,EAAE;YACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAuB,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,MAAM,gBAAgB,SAAS;QAE/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA8B,GACvD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
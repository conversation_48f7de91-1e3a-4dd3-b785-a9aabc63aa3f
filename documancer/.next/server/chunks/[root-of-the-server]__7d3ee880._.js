module.exports = {

"[project]/.next-internal/server/app/api/chat/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/langchain.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Mock LangChain implementation for demo purposes
// In production, you would use the actual LangChain imports
__turbopack_context__.s({
    "PROMPTS": ()=>PROMPTS,
    "analyzeCitations": ()=>analyzeCitations,
    "answerQuestion": ()=>answerQuestion,
    "comparePapers": ()=>comparePapers,
    "createAnalysisChain": ()=>createAnalysisChain,
    "createDeepSeekModel": ()=>createDeepSeekModel,
    "explainMethodology": ()=>explainMethodology,
    "extractConcepts": ()=>extractConcepts,
    "extractKeyFindings": ()=>extractKeyFindings,
    "summarizePaper": ()=>summarizePaper
});
class MockChatOpenAI {
    constructor(config){
    // Mock constructor
    }
    async invoke(messages) {
        // Mock AI response - in production this would call DeepSeek API
        const prompt = typeof messages === 'string' ? messages : messages.content || '';
        // Simple mock responses based on prompt content
        if (prompt.includes('summarize') || prompt.includes('summary')) {
            return `## Summary

This paper presents a comprehensive analysis of the research topic. The main objectives include:

1. **Research Question**: The authors investigate the fundamental aspects of the problem domain
2. **Methodology**: A systematic approach was employed using established research methods
3. **Key Findings**: The study reveals significant insights that contribute to the field
4. **Implications**: The results have important implications for future research and practical applications

The work builds upon existing literature while introducing novel perspectives and methodologies.`;
        }
        if (prompt.includes('key findings') || prompt.includes('findings')) {
            return `## Key Findings

1. **Primary Discovery**: The research demonstrates significant improvements in the target domain
2. **Novel Contribution**: Introduction of innovative approaches that advance the field
3. **Experimental Results**: Comprehensive evaluation shows promising outcomes
4. **Theoretical Insights**: New theoretical frameworks that enhance understanding
5. **Practical Applications**: Direct applications that can benefit real-world scenarios

These findings represent important contributions to the academic community and provide foundation for future research.`;
        }
        if (prompt.includes('methodology') || prompt.includes('methods')) {
            return `## Methodology

**Research Design**: The study employs a comprehensive research design that combines multiple approaches:

1. **Data Collection**: Systematic data gathering using established protocols
2. **Analysis Techniques**: Advanced analytical methods for processing and interpretation
3. **Tools and Technologies**: State-of-the-art tools and frameworks
4. **Experimental Setup**: Controlled experimental environment with proper validation
5. **Validation Methods**: Rigorous validation procedures to ensure reliability
6. **Limitations**: Acknowledged limitations and their potential impact

The methodology follows best practices in the field and ensures reproducible results.`;
        }
        if (prompt.includes('concepts') || prompt.includes('terminology')) {
            return `## Key Concepts

**Machine Learning**
Definition: A subset of artificial intelligence that enables systems to learn from data
Importance: High
Related: AI, Deep Learning, Neural Networks

**Neural Networks**
Definition: Computing systems inspired by biological neural networks
Importance: High
Related: Deep Learning, Machine Learning, AI

**Algorithm**
Definition: A set of rules or instructions for solving problems
Importance: Medium
Related: Programming, Computer Science, Mathematics

**Data Mining**
Definition: Process of discovering patterns in large datasets
Importance: Medium
Related: Machine Learning, Statistics, Big Data`;
        }
        // Default response for questions
        return `Based on the paper content, I can provide the following insights:

This appears to be a well-researched academic paper that addresses important questions in the field. The authors present their findings in a structured manner, providing both theoretical contributions and practical implications.

Key points to consider:
- The research methodology is sound and follows established practices
- The findings contribute meaningfully to the existing body of knowledge
- The implications extend beyond the immediate research domain
- Future research directions are clearly identified

Would you like me to elaborate on any specific aspect of the paper?`;
    }
}
class MockPromptTemplate {
    template;
    constructor(template){
        this.template = template;
    }
    static fromTemplate(template) {
        return new MockPromptTemplate(template);
    }
    format(variables) {
        let formatted = this.template;
        Object.entries(variables).forEach(([key, value])=>{
            formatted = formatted.replace(new RegExp(`{${key}}`, 'g'), value);
        });
        return formatted;
    }
}
class MockStringOutputParser {
    parse(text) {
        return text;
    }
}
class MockRunnableSequence {
    steps;
    constructor(steps){
        this.steps = steps;
    }
    static from(steps) {
        return new MockRunnableSequence(steps);
    }
    async invoke(input) {
        // Mock the chain execution
        const model = this.steps.find((step)=>step instanceof MockChatOpenAI);
        if (model) {
            return await model.invoke(input);
        }
        return 'Mock response';
    }
}
const createDeepSeekModel = ()=>{
    return new MockChatOpenAI({
        modelName: 'deepseek-chat',
        openAIApiKey: process.env.DEEPSEEK_API_KEY,
        configuration: {
            baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1'
        },
        temperature: 0.1,
        maxTokens: 4000
    });
};
const PROMPTS = {
    SUMMARIZE: MockPromptTemplate.fromTemplate(`
    You are an expert academic paper analyst. Please provide a comprehensive summary of the following research paper.

    Paper Content:
    {content}

    Please provide:
    1. A concise abstract summary (2-3 sentences)
    2. Main research question and objectives
    3. Key methodology used
    4. Primary findings and contributions
    5. Significance and implications

    Format your response in clear, structured sections.
  `),
    EXTRACT_KEY_FINDINGS: MockPromptTemplate.fromTemplate(`
    Analyze the following research paper and extract the key findings and contributions.

    Paper Content:
    {content}

    Please identify and list:
    1. Main research findings (numbered list)
    2. Novel contributions to the field
    3. Experimental results and their significance
    4. Theoretical insights or frameworks introduced
    5. Practical applications or implications

    Be specific and cite relevant sections when possible.
  `),
    EXPLAIN_METHODOLOGY: MockPromptTemplate.fromTemplate(`
    Analyze the methodology section of this research paper and provide a clear explanation.

    Paper Content:
    {content}

    Please explain:
    1. Research design and approach
    2. Data collection methods
    3. Analysis techniques used
    4. Tools and technologies employed
    5. Experimental setup (if applicable)
    6. Validation methods
    7. Limitations of the methodology

    Make the explanation accessible to researchers in related fields.
  `),
    EXTRACT_CONCEPTS: MockPromptTemplate.fromTemplate(`
    Identify and explain the key concepts, terms, and technical vocabulary from this research paper.

    Paper Content:
    {content}

    For each important concept, provide:
    1. Term/Concept name
    2. Clear definition in context
    3. Importance level (High/Medium/Low)
    4. Related terms or concepts
    5. How it's used in this specific paper

    Focus on domain-specific terminology and novel concepts introduced.
  `),
    ANSWER_QUESTION: MockPromptTemplate.fromTemplate(`
    You are an AI assistant helping researchers understand academic papers. Answer the following question based on the paper content provided.

    Paper Content:
    {content}

    Question: {question}

    Please provide a comprehensive answer that:
    1. Directly addresses the question
    2. References specific sections of the paper when relevant
    3. Provides context and background if needed
    4. Mentions any limitations or uncertainties
    5. Suggests related questions or areas for further exploration

    If the question cannot be answered from the provided content, clearly state this and explain why.
  `),
    COMPARE_PAPERS: MockPromptTemplate.fromTemplate(`
    Compare and analyze the following research papers. Identify similarities, differences, and relationships between them.

    Paper 1:
    {paper1}

    Paper 2:
    {paper2}

    Please provide:
    1. Common themes and research areas
    2. Methodological similarities and differences
    3. Complementary findings or conflicting results
    4. How the papers build upon or relate to each other
    5. Gaps that could be addressed by combining insights
    6. Recommendations for researchers interested in this area

    Structure your comparison clearly with specific examples from both papers.
  `),
    ANALYZE_CITATIONS: MockPromptTemplate.fromTemplate(`
    Analyze the citations and references in this research paper to understand its academic context.

    Paper Content:
    {content}

    Please identify:
    1. Key foundational works cited
    2. Recent developments referenced
    3. Main research communities or schools of thought
    4. Gaps in the literature identified by the authors
    5. How this work positions itself relative to existing research
    6. Potential future research directions suggested

    Focus on understanding the academic landscape and research trajectory.
  `)
};
const createAnalysisChain = (promptTemplate)=>{
    const model = createDeepSeekModel();
    const outputParser = new MockStringOutputParser();
    return MockRunnableSequence.from([
        promptTemplate,
        model,
        outputParser
    ]);
};
const summarizePaper = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.SUMMARIZE);
    return await chain.invoke({
        content
    });
};
const extractKeyFindings = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.EXTRACT_KEY_FINDINGS);
    return await chain.invoke({
        content
    });
};
const explainMethodology = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.EXPLAIN_METHODOLOGY);
    return await chain.invoke({
        content
    });
};
const extractConcepts = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.EXTRACT_CONCEPTS);
    return await chain.invoke({
        content
    });
};
const answerQuestion = async (content, question)=>{
    const chain = createAnalysisChain(PROMPTS.ANSWER_QUESTION);
    return await chain.invoke({
        content,
        question
    });
};
const comparePapers = async (paper1, paper2)=>{
    const chain = createAnalysisChain(PROMPTS.COMPARE_PAPERS);
    return await chain.invoke({
        paper1,
        paper2
    });
};
const analyzeCitations = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.ANALYZE_CITATIONS);
    return await chain.invoke({
        content
    });
};
}),
"[project]/src/lib/constants.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Application constants
__turbopack_context__.s({
    "ANALYSIS_TYPES": ()=>ANALYSIS_TYPES,
    "ANNOTATION_TYPES": ()=>ANNOTATION_TYPES,
    "API_ENDPOINTS": ()=>API_ENDPOINTS,
    "APP_CONFIG": ()=>APP_CONFIG,
    "BREAKPOINTS": ()=>BREAKPOINTS,
    "COLORS": ()=>COLORS,
    "ERROR_MESSAGES": ()=>ERROR_MESSAGES,
    "LOADING_MESSAGES": ()=>LOADING_MESSAGES,
    "MESSAGE_TYPES": ()=>MESSAGE_TYPES,
    "PAPER_FORMATS": ()=>PAPER_FORMATS,
    "ROUTES": ()=>ROUTES,
    "VIEW_MODES": ()=>VIEW_MODES
});
const APP_CONFIG = {
    name: 'DocuMancer',
    version: '1.0.0',
    description: 'AI-Powered Academic Paper Reading Assistant',
    maxFileSize: 50 * 1024 * 1024,
    allowedFileTypes: [
        '.pdf'
    ],
    supportedFormats: [
        'PDF'
    ]
};
const COLORS = {
    primary: '#1890ff',
    secondary: '#722ed1',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    text: {
        primary: '#262626',
        secondary: '#595959',
        disabled: '#bfbfbf'
    },
    background: {
        primary: '#ffffff',
        secondary: '#fafafa',
        tertiary: '#f5f5f5'
    },
    border: '#d9d9d9'
};
const BREAKPOINTS = {
    xs: 480,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1600
};
const ROUTES = {
    home: '/',
    library: '/library',
    reader: '/reader',
    comparison: '/comparison',
    analysis: '/analysis',
    settings: '/settings'
};
const API_ENDPOINTS = {
    papers: '/api/papers',
    upload: '/api/upload',
    chat: '/api/chat',
    analysis: '/api/analysis',
    search: '/api/search',
    comparison: '/api/comparison'
};
const PAPER_FORMATS = {
    ARXIV: 'arXiv',
    IEEE: 'IEEE',
    ACM: 'ACM',
    SPRINGER: 'Springer',
    ELSEVIER: 'Elsevier',
    GENERIC: 'Generic'
};
const ANALYSIS_TYPES = {
    SUMMARY: 'summary',
    KEY_FINDINGS: 'key_findings',
    METHODOLOGY: 'methodology',
    CONCEPTS: 'concepts',
    CITATIONS: 'citations',
    COMPARISON: 'comparison'
};
const MESSAGE_TYPES = {
    USER: 'user',
    ASSISTANT: 'assistant',
    SYSTEM: 'system'
};
const ANNOTATION_TYPES = {
    HIGHLIGHT: 'highlight',
    NOTE: 'note',
    BOOKMARK: 'bookmark'
};
const VIEW_MODES = {
    READER: 'reader',
    LIBRARY: 'library',
    COMPARISON: 'comparison',
    ANALYSIS: 'analysis'
};
const LOADING_MESSAGES = [
    'Processing your document...',
    'Extracting text content...',
    'Analyzing paper structure...',
    'Generating insights...',
    'Almost ready...'
];
const ERROR_MESSAGES = {
    FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',
    INVALID_FILE_TYPE: 'Only PDF files are supported',
    UPLOAD_FAILED: 'Failed to upload file. Please try again.',
    PROCESSING_FAILED: 'Failed to process the document',
    API_ERROR: 'An error occurred while communicating with the server',
    NETWORK_ERROR: 'Network error. Please check your connection.',
    GENERIC_ERROR: 'An unexpected error occurred'
};
}),
"[project]/src/app/api/chat/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/langchain.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { message, paperId, paperContent, context } = body;
        if (!message) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Message is required'
            }, {
                status: 400
            });
        }
        if (!paperContent) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Paper content is required for analysis'
            }, {
                status: 400
            });
        }
        // Generate response using LangChain
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["answerQuestion"])(paperContent, message);
        // Create response message
        const responseMessage = {
            id: `msg_${Date.now()}_assistant`,
            role: 'assistant',
            content: response,
            timestamp: new Date(),
            paperId,
            context
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                message: responseMessage
            }
        });
    } catch (error) {
        console.error('Chat error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].API_ERROR,
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function GET(request) {
    const { searchParams } = new URL(request.url);
    const message = searchParams.get('message');
    const paperContent = searchParams.get('content');
    if (!message || !paperContent) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Message and content are required'
        }, {
            status: 400
        });
    }
    try {
        // Create a streaming response
        const encoder = new TextEncoder();
        const stream = new ReadableStream({
            async start (controller) {
                try {
                    const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["answerQuestion"])(paperContent, message);
                    // Simulate streaming by sending chunks
                    const chunks = response.split(' ');
                    for(let i = 0; i < chunks.length; i++){
                        const chunk = chunks[i] + (i < chunks.length - 1 ? ' ' : '');
                        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                            chunk
                        })}\n\n`));
                        // Add small delay to simulate streaming
                        await new Promise((resolve)=>setTimeout(resolve, 50));
                    }
                    controller.enqueue(encoder.encode('data: [DONE]\n\n'));
                    controller.close();
                } catch (error) {
                    controller.error(error);
                }
            }
        });
        return new Response(stream, {
            headers: {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive'
            }
        });
    } catch (error) {
        console.error('Streaming chat error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].API_ERROR,
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__7d3ee880._.js.map
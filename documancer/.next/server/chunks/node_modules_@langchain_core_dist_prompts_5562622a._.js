module.exports = {

"[project]/node_modules/@langchain/core/dist/prompts/prompt.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@langchain/core/dist/prompts/prompt.js [app-route] (ecmascript)");
    });
});
}),
"[project]/node_modules/@langchain/core/dist/prompts/few_shot.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@langchain/core/dist/prompts/few_shot.js [app-route] (ecmascript)");
    });
});
}),

};
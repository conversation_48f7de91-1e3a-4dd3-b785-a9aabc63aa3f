{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/common/ErrorBoundary.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/common/ErrorBoundary.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/ErrorBoundary.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/common/ErrorBoundary.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/common/ErrorBoundary.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/ErrorBoundary.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport { ConfigProvider } from 'antd';\nimport ErrorBoundary from '@/components/common/ErrorBoundary';\nimport \"./globals.css\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"DocuMancer - AI-Powered Academic Paper Reading Assistant\",\n  description: \"Sophisticated paper reading assistant with AI-powered analysis, summarization, and Q&A capabilities\",\n  keywords: \"academic papers, AI assistant, research, PDF reader, paper analysis\",\n  authors: [{ name: \"DocuMancer Team\" }],\n  viewport: \"width=device-width, initial-scale=1\",\n};\n\nconst antdTheme = {\n  token: {\n    colorPrimary: '#1890ff',\n    colorSuccess: '#52c41a',\n    colorWarning: '#faad14',\n    colorError: '#ff4d4f',\n    colorInfo: '#1890ff',\n    borderRadius: 8,\n    fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", sans-serif',\n  },\n  components: {\n    Layout: {\n      headerBg: '#ffffff',\n      siderBg: '#ffffff',\n      bodyBg: '#fafafa',\n    },\n    Menu: {\n      itemBg: 'transparent',\n      itemSelectedBg: 'rgba(24, 144, 255, 0.1)',\n      itemHoverBg: 'rgba(0, 0, 0, 0.04)',\n    },\n    Button: {\n      primaryShadow: '0 2px 8px rgba(24, 144, 255, 0.2)',\n    },\n    Card: {\n      boxShadowTertiary: '0 2px 8px rgba(0, 0, 0, 0.06)',\n    },\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        <ErrorBoundary>\n          <ConfigProvider theme={antdTheme}>\n            {children}\n          </ConfigProvider>\n        </ErrorBoundary>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;;;;;AAaO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAkB;KAAE;IACtC,UAAU;AACZ;AAEA,MAAM,YAAY;IAChB,OAAO;QACL,cAAc;QACd,cAAc;QACd,cAAc;QACd,YAAY;QACZ,WAAW;QACX,cAAc;QACd,YAAY;IACd;IACA,YAAY;QACV,QAAQ;YACN,UAAU;YACV,SAAS;YACT,QAAQ;QACV;QACA,MAAM;YACJ,QAAQ;YACR,gBAAgB;YAChB,aAAa;QACf;QACA,QAAQ;YACN,eAAe;QACjB;QACA,MAAM;YACJ,mBAAmB;QACrB;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAEpE,cAAA,8OAAC,6IAAA,CAAA,UAAa;0BACZ,cAAA,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACpB;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/antd/es/index.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Affix = registerClientReference(\n    function() { throw new Error(\"Attempted to call Affix() from the server but Affix is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Affix\",\n);\nexport const Alert = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON><PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Alert\",\n);\nexport const Anchor = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON><PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Anchor\",\n);\nexport const App = registerClientReference(\n    function() { throw new Error(\"Attempted to call App() from the server but App is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"App\",\n);\nexport const AutoComplete = registerClientReference(\n    function() { throw new Error(\"Attempted to call AutoComplete() from the server but AutoComplete is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"AutoComplete\",\n);\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Avatar\",\n);\nexport const BackTop = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackTop() from the server but BackTop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"BackTop\",\n);\nexport const Badge = registerClientReference(\n    function() { throw new Error(\"Attempted to call Badge() from the server but Badge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Badge\",\n);\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Breadcrumb\",\n);\nexport const Button = registerClientReference(\n    function() { throw new Error(\"Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Button\",\n);\nexport const Calendar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Calendar() from the server but Calendar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Calendar\",\n);\nexport const Card = registerClientReference(\n    function() { throw new Error(\"Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Card\",\n);\nexport const Carousel = registerClientReference(\n    function() { throw new Error(\"Attempted to call Carousel() from the server but Carousel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Carousel\",\n);\nexport const Cascader = registerClientReference(\n    function() { throw new Error(\"Attempted to call Cascader() from the server but Cascader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Cascader\",\n);\nexport const Checkbox = registerClientReference(\n    function() { throw new Error(\"Attempted to call Checkbox() from the server but Checkbox is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Checkbox\",\n);\nexport const Col = registerClientReference(\n    function() { throw new Error(\"Attempted to call Col() from the server but Col is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Col\",\n);\nexport const Collapse = registerClientReference(\n    function() { throw new Error(\"Attempted to call Collapse() from the server but Collapse is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Collapse\",\n);\nexport const ColorPicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call ColorPicker() from the server but ColorPicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"ColorPicker\",\n);\nexport const ConfigProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConfigProvider() from the server but ConfigProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"ConfigProvider\",\n);\nexport const DatePicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call DatePicker() from the server but DatePicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"DatePicker\",\n);\nexport const Descriptions = registerClientReference(\n    function() { throw new Error(\"Attempted to call Descriptions() from the server but Descriptions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Descriptions\",\n);\nexport const Divider = registerClientReference(\n    function() { throw new Error(\"Attempted to call Divider() from the server but Divider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Divider\",\n);\nexport const Drawer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Drawer() from the server but Drawer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Drawer\",\n);\nexport const Dropdown = registerClientReference(\n    function() { throw new Error(\"Attempted to call Dropdown() from the server but Dropdown is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Dropdown\",\n);\nexport const Empty = registerClientReference(\n    function() { throw new Error(\"Attempted to call Empty() from the server but Empty is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Empty\",\n);\nexport const Flex = registerClientReference(\n    function() { throw new Error(\"Attempted to call Flex() from the server but Flex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Flex\",\n);\nexport const FloatButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FloatButton() from the server but FloatButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"FloatButton\",\n);\nexport const Form = registerClientReference(\n    function() { throw new Error(\"Attempted to call Form() from the server but Form is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Form\",\n);\nexport const Grid = registerClientReference(\n    function() { throw new Error(\"Attempted to call Grid() from the server but Grid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Grid\",\n);\nexport const Image = registerClientReference(\n    function() { throw new Error(\"Attempted to call Image() from the server but Image is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Image\",\n);\nexport const Input = registerClientReference(\n    function() { throw new Error(\"Attempted to call Input() from the server but Input is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Input\",\n);\nexport const InputNumber = registerClientReference(\n    function() { throw new Error(\"Attempted to call InputNumber() from the server but InputNumber is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"InputNumber\",\n);\nexport const Layout = registerClientReference(\n    function() { throw new Error(\"Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Layout\",\n);\nexport const List = registerClientReference(\n    function() { throw new Error(\"Attempted to call List() from the server but List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"List\",\n);\nexport const Mentions = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mentions() from the server but Mentions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Mentions\",\n);\nexport const Menu = registerClientReference(\n    function() { throw new Error(\"Attempted to call Menu() from the server but Menu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Menu\",\n);\nexport const Modal = registerClientReference(\n    function() { throw new Error(\"Attempted to call Modal() from the server but Modal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Modal\",\n);\nexport const Pagination = registerClientReference(\n    function() { throw new Error(\"Attempted to call Pagination() from the server but Pagination is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Pagination\",\n);\nexport const Popconfirm = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popconfirm() from the server but Popconfirm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Popconfirm\",\n);\nexport const Popover = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Popover\",\n);\nexport const Progress = registerClientReference(\n    function() { throw new Error(\"Attempted to call Progress() from the server but Progress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Progress\",\n);\nexport const QRCode = registerClientReference(\n    function() { throw new Error(\"Attempted to call QRCode() from the server but QRCode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"QRCode\",\n);\nexport const Radio = registerClientReference(\n    function() { throw new Error(\"Attempted to call Radio() from the server but Radio is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Radio\",\n);\nexport const Rate = registerClientReference(\n    function() { throw new Error(\"Attempted to call Rate() from the server but Rate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Rate\",\n);\nexport const Result = registerClientReference(\n    function() { throw new Error(\"Attempted to call Result() from the server but Result is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Result\",\n);\nexport const Row = registerClientReference(\n    function() { throw new Error(\"Attempted to call Row() from the server but Row is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Row\",\n);\nexport const Segmented = registerClientReference(\n    function() { throw new Error(\"Attempted to call Segmented() from the server but Segmented is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Segmented\",\n);\nexport const Select = registerClientReference(\n    function() { throw new Error(\"Attempted to call Select() from the server but Select is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Select\",\n);\nexport const Skeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call Skeleton() from the server but Skeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Skeleton\",\n);\nexport const Slider = registerClientReference(\n    function() { throw new Error(\"Attempted to call Slider() from the server but Slider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Slider\",\n);\nexport const Space = registerClientReference(\n    function() { throw new Error(\"Attempted to call Space() from the server but Space is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Space\",\n);\nexport const Spin = registerClientReference(\n    function() { throw new Error(\"Attempted to call Spin() from the server but Spin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Spin\",\n);\nexport const Splitter = registerClientReference(\n    function() { throw new Error(\"Attempted to call Splitter() from the server but Splitter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Splitter\",\n);\nexport const Statistic = registerClientReference(\n    function() { throw new Error(\"Attempted to call Statistic() from the server but Statistic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Statistic\",\n);\nexport const Steps = registerClientReference(\n    function() { throw new Error(\"Attempted to call Steps() from the server but Steps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Steps\",\n);\nexport const Switch = registerClientReference(\n    function() { throw new Error(\"Attempted to call Switch() from the server but Switch is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Switch\",\n);\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Table\",\n);\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Tabs\",\n);\nexport const Tag = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tag() from the server but Tag is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Tag\",\n);\nexport const TimePicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call TimePicker() from the server but TimePicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"TimePicker\",\n);\nexport const Timeline = registerClientReference(\n    function() { throw new Error(\"Attempted to call Timeline() from the server but Timeline is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Timeline\",\n);\nexport const Tooltip = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Tooltip\",\n);\nexport const Tour = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tour() from the server but Tour is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Tour\",\n);\nexport const Transfer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Transfer() from the server but Transfer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Transfer\",\n);\nexport const Tree = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tree() from the server but Tree is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Tree\",\n);\nexport const TreeSelect = registerClientReference(\n    function() { throw new Error(\"Attempted to call TreeSelect() from the server but TreeSelect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"TreeSelect\",\n);\nexport const Typography = registerClientReference(\n    function() { throw new Error(\"Attempted to call Typography() from the server but Typography is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Typography\",\n);\nexport const Upload = registerClientReference(\n    function() { throw new Error(\"Attempted to call Upload() from the server but Upload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Upload\",\n);\nexport const Watermark = registerClientReference(\n    function() { throw new Error(\"Attempted to call Watermark() from the server but Watermark is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"Watermark\",\n);\nexport const message = registerClientReference(\n    function() { throw new Error(\"Attempted to call message() from the server but message is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"message\",\n);\nexport const notification = registerClientReference(\n    function() { throw new Error(\"Attempted to call notification() from the server but notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"notification\",\n);\nexport const theme = registerClientReference(\n    function() { throw new Error(\"Attempted to call theme() from the server but theme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"theme\",\n);\nexport const unstableSetRender = registerClientReference(\n    function() { throw new Error(\"Attempted to call unstableSetRender() from the server but unstableSetRender is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"unstableSetRender\",\n);\nexport const version = registerClientReference(\n    function() { throw new Error(\"Attempted to call version() from the server but version is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js <module evaluation>\",\n    \"version\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,+DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,+DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/antd/es/index.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Affix = registerClientReference(\n    function() { throw new Error(\"Attempted to call Affix() from the server but Affix is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Affix\",\n);\nexport const Alert = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON><PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Alert\",\n);\nexport const Anchor = registerClientReference(\n    function() { throw new Error(\"Attempted to call Anchor() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Anchor\",\n);\nexport const App = registerClientReference(\n    function() { throw new Error(\"Attempted to call App() from the server but App is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"App\",\n);\nexport const AutoComplete = registerClientReference(\n    function() { throw new Error(\"Attempted to call AutoComplete() from the server but AutoComplete is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"AutoComplete\",\n);\nexport const Avatar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Avatar\",\n);\nexport const BackTop = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackTop() from the server but BackTop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"BackTop\",\n);\nexport const Badge = registerClientReference(\n    function() { throw new Error(\"Attempted to call Badge() from the server but Badge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Badge\",\n);\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Breadcrumb\",\n);\nexport const Button = registerClientReference(\n    function() { throw new Error(\"Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Button\",\n);\nexport const Calendar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Calendar() from the server but Calendar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Calendar\",\n);\nexport const Card = registerClientReference(\n    function() { throw new Error(\"Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Card\",\n);\nexport const Carousel = registerClientReference(\n    function() { throw new Error(\"Attempted to call Carousel() from the server but Carousel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Carousel\",\n);\nexport const Cascader = registerClientReference(\n    function() { throw new Error(\"Attempted to call Cascader() from the server but Cascader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Cascader\",\n);\nexport const Checkbox = registerClientReference(\n    function() { throw new Error(\"Attempted to call Checkbox() from the server but Checkbox is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Checkbox\",\n);\nexport const Col = registerClientReference(\n    function() { throw new Error(\"Attempted to call Col() from the server but Col is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Col\",\n);\nexport const Collapse = registerClientReference(\n    function() { throw new Error(\"Attempted to call Collapse() from the server but Collapse is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Collapse\",\n);\nexport const ColorPicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call ColorPicker() from the server but ColorPicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"ColorPicker\",\n);\nexport const ConfigProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ConfigProvider() from the server but ConfigProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"ConfigProvider\",\n);\nexport const DatePicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call DatePicker() from the server but DatePicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"DatePicker\",\n);\nexport const Descriptions = registerClientReference(\n    function() { throw new Error(\"Attempted to call Descriptions() from the server but Descriptions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Descriptions\",\n);\nexport const Divider = registerClientReference(\n    function() { throw new Error(\"Attempted to call Divider() from the server but Divider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Divider\",\n);\nexport const Drawer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Drawer() from the server but Drawer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Drawer\",\n);\nexport const Dropdown = registerClientReference(\n    function() { throw new Error(\"Attempted to call Dropdown() from the server but Dropdown is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Dropdown\",\n);\nexport const Empty = registerClientReference(\n    function() { throw new Error(\"Attempted to call Empty() from the server but Empty is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Empty\",\n);\nexport const Flex = registerClientReference(\n    function() { throw new Error(\"Attempted to call Flex() from the server but Flex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Flex\",\n);\nexport const FloatButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call FloatButton() from the server but FloatButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"FloatButton\",\n);\nexport const Form = registerClientReference(\n    function() { throw new Error(\"Attempted to call Form() from the server but Form is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Form\",\n);\nexport const Grid = registerClientReference(\n    function() { throw new Error(\"Attempted to call Grid() from the server but Grid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Grid\",\n);\nexport const Image = registerClientReference(\n    function() { throw new Error(\"Attempted to call Image() from the server but Image is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Image\",\n);\nexport const Input = registerClientReference(\n    function() { throw new Error(\"Attempted to call Input() from the server but Input is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Input\",\n);\nexport const InputNumber = registerClientReference(\n    function() { throw new Error(\"Attempted to call InputNumber() from the server but InputNumber is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"InputNumber\",\n);\nexport const Layout = registerClientReference(\n    function() { throw new Error(\"Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Layout\",\n);\nexport const List = registerClientReference(\n    function() { throw new Error(\"Attempted to call List() from the server but List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"List\",\n);\nexport const Mentions = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mentions() from the server but Mentions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Mentions\",\n);\nexport const Menu = registerClientReference(\n    function() { throw new Error(\"Attempted to call Menu() from the server but Menu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Menu\",\n);\nexport const Modal = registerClientReference(\n    function() { throw new Error(\"Attempted to call Modal() from the server but Modal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Modal\",\n);\nexport const Pagination = registerClientReference(\n    function() { throw new Error(\"Attempted to call Pagination() from the server but Pagination is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Pagination\",\n);\nexport const Popconfirm = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popconfirm() from the server but Popconfirm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Popconfirm\",\n);\nexport const Popover = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Popover\",\n);\nexport const Progress = registerClientReference(\n    function() { throw new Error(\"Attempted to call Progress() from the server but Progress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Progress\",\n);\nexport const QRCode = registerClientReference(\n    function() { throw new Error(\"Attempted to call QRCode() from the server but QRCode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"QRCode\",\n);\nexport const Radio = registerClientReference(\n    function() { throw new Error(\"Attempted to call Radio() from the server but Radio is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Radio\",\n);\nexport const Rate = registerClientReference(\n    function() { throw new Error(\"Attempted to call Rate() from the server but Rate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Rate\",\n);\nexport const Result = registerClientReference(\n    function() { throw new Error(\"Attempted to call Result() from the server but Result is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Result\",\n);\nexport const Row = registerClientReference(\n    function() { throw new Error(\"Attempted to call Row() from the server but Row is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Row\",\n);\nexport const Segmented = registerClientReference(\n    function() { throw new Error(\"Attempted to call Segmented() from the server but Segmented is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Segmented\",\n);\nexport const Select = registerClientReference(\n    function() { throw new Error(\"Attempted to call Select() from the server but Select is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Select\",\n);\nexport const Skeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call Skeleton() from the server but Skeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Skeleton\",\n);\nexport const Slider = registerClientReference(\n    function() { throw new Error(\"Attempted to call Slider() from the server but Slider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Slider\",\n);\nexport const Space = registerClientReference(\n    function() { throw new Error(\"Attempted to call Space() from the server but Space is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Space\",\n);\nexport const Spin = registerClientReference(\n    function() { throw new Error(\"Attempted to call Spin() from the server but Spin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Spin\",\n);\nexport const Splitter = registerClientReference(\n    function() { throw new Error(\"Attempted to call Splitter() from the server but Splitter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Splitter\",\n);\nexport const Statistic = registerClientReference(\n    function() { throw new Error(\"Attempted to call Statistic() from the server but Statistic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Statistic\",\n);\nexport const Steps = registerClientReference(\n    function() { throw new Error(\"Attempted to call Steps() from the server but Steps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Steps\",\n);\nexport const Switch = registerClientReference(\n    function() { throw new Error(\"Attempted to call Switch() from the server but Switch is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Switch\",\n);\nexport const Table = registerClientReference(\n    function() { throw new Error(\"Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Table\",\n);\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Tabs\",\n);\nexport const Tag = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tag() from the server but Tag is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Tag\",\n);\nexport const TimePicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call TimePicker() from the server but TimePicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"TimePicker\",\n);\nexport const Timeline = registerClientReference(\n    function() { throw new Error(\"Attempted to call Timeline() from the server but Timeline is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Timeline\",\n);\nexport const Tooltip = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Tooltip\",\n);\nexport const Tour = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tour() from the server but Tour is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Tour\",\n);\nexport const Transfer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Transfer() from the server but Transfer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Transfer\",\n);\nexport const Tree = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tree() from the server but Tree is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Tree\",\n);\nexport const TreeSelect = registerClientReference(\n    function() { throw new Error(\"Attempted to call TreeSelect() from the server but TreeSelect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"TreeSelect\",\n);\nexport const Typography = registerClientReference(\n    function() { throw new Error(\"Attempted to call Typography() from the server but Typography is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Typography\",\n);\nexport const Upload = registerClientReference(\n    function() { throw new Error(\"Attempted to call Upload() from the server but Upload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Upload\",\n);\nexport const Watermark = registerClientReference(\n    function() { throw new Error(\"Attempted to call Watermark() from the server but Watermark is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"Watermark\",\n);\nexport const message = registerClientReference(\n    function() { throw new Error(\"Attempted to call message() from the server but message is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"message\",\n);\nexport const notification = registerClientReference(\n    function() { throw new Error(\"Attempted to call notification() from the server but notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"notification\",\n);\nexport const theme = registerClientReference(\n    function() { throw new Error(\"Attempted to call theme() from the server but theme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"theme\",\n);\nexport const unstableSetRender = registerClientReference(\n    function() { throw new Error(\"Attempted to call unstableSetRender() from the server but unstableSetRender is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"unstableSetRender\",\n);\nexport const version = registerClientReference(\n    function() { throw new Error(\"Attempted to call version() from the server but version is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/antd/es/index.js\",\n    \"version\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,2CACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,2CACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2CACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,2CACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2CACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}
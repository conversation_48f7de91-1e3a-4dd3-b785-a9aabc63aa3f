{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/common/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Component, ErrorInfo, ReactNode } from 'react';\nimport { Result, Button } from 'antd';\nimport { ReloadOutlined, HomeOutlined } from '@ant-design/icons';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: ErrorInfo;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo);\n    this.setState({ error, errorInfo });\n  }\n\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  handleGoHome = () => {\n    window.location.href = '/';\n  };\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <Result\n            status=\"error\"\n            title=\"Something went wrong\"\n            subTitle=\"We're sorry, but something unexpected happened. Please try refreshing the page or go back to the home page.\"\n            extra={[\n              <Button \n                type=\"primary\" \n                icon={<ReloadOutlined />} \n                onClick={this.handleReload}\n                key=\"reload\"\n              >\n                Reload Page\n              </Button>,\n              <Button \n                icon={<HomeOutlined />} \n                onClick={this.handleGoHome}\n                key=\"home\"\n              >\n                Go Home\n              </Button>,\n            ]}\n          >\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <div className=\"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\">\n                <details className=\"text-left\">\n                  <summary className=\"cursor-pointer font-medium text-red-800 mb-2\">\n                    Error Details (Development Only)\n                  </summary>\n                  <pre className=\"text-sm text-red-700 whitespace-pre-wrap overflow-auto\">\n                    {this.state.error.toString()}\n                    {this.state.errorInfo?.componentStack}\n                  </pre>\n                </details>\n              </div>\n            )}\n          </Result>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAJA;;;;;AAiBA,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAE;QACpD,QAAQ,KAAK,CAAC,6BAA6B,OAAO;QAClD,IAAI,CAAC,QAAQ,CAAC;YAAE;YAAO;QAAU;IACnC;IAEA,eAAe;QACb,OAAO,QAAQ,CAAC,MAAM;IACxB,EAAE;IAEF,eAAe;QACb,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kLAAA,CAAA,SAAM;oBACL,QAAO;oBACP,OAAM;oBACN,UAAS;oBACT,OAAO;sCACL,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4BACrB,SAAS,IAAI,CAAC,YAAY;sCAE3B;2BADK;;;;;sCAIN,8OAAC,kMAAA,CAAA,SAAM;4BACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAI,CAAC,YAAY;sCAE3B;2BADK;;;;;qBAIP;8BAEA,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAQ,WAAU;8CAA+C;;;;;;8CAGlE,8OAAC;oCAAI,WAAU;;wCACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ;wCACzB,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAQvC;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/providers/PDFProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { pdfjs } from 'react-pdf';\n\n// Configure PDF.js worker\nconst configurePDFJS = () => {\n  if (typeof window !== 'undefined') {\n    // Use CDN worker that matches the installed react-pdf version\n    pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;\n    \n    console.log('PDF.js configured with version:', pdfjs.version);\n    console.log('Worker source:', pdfjs.GlobalWorkerOptions.workerSrc);\n  }\n};\n\nexport default function PDFProvider({ children }: { children: React.ReactNode }) {\n  useEffect(() => {\n    configurePDFJS();\n  }, []);\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;;AAKA,0BAA0B;AAC1B,MAAM,iBAAiB;IACrB;;AAOF;AAEe,SAAS,YAAY,EAAE,QAAQ,EAAiC;IAC7E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ", "debugId": null}}]}
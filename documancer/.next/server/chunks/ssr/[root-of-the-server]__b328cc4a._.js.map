{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/constants.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_CONFIG = {\n  name: 'DocuMancer',\n  version: '1.0.0',\n  description: 'AI-Powered Academic Paper Reading Assistant',\n  maxFileSize: 50 * 1024 * 1024, // 50MB\n  allowedFileTypes: ['.pdf'],\n  supportedFormats: ['PDF'],\n} as const;\n\nexport const COLORS = {\n  primary: '#1890ff',\n  secondary: '#722ed1',\n  success: '#52c41a',\n  warning: '#faad14',\n  error: '#ff4d4f',\n  text: {\n    primary: '#262626',\n    secondary: '#595959',\n    disabled: '#bfbfbf',\n  },\n  background: {\n    primary: '#ffffff',\n    secondary: '#fafafa',\n    tertiary: '#f5f5f5',\n  },\n  border: '#d9d9d9',\n} as const;\n\nexport const BREAKPOINTS = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600,\n} as const;\n\nexport const ROUTES = {\n  home: '/',\n  library: '/library',\n  reader: '/reader',\n  comparison: '/comparison',\n  analysis: '/analysis',\n  settings: '/settings',\n} as const;\n\nexport const API_ENDPOINTS = {\n  papers: '/api/papers',\n  upload: '/api/upload',\n  chat: '/api/chat',\n  analysis: '/api/analysis',\n  search: '/api/search',\n  comparison: '/api/comparison',\n} as const;\n\nexport const PAPER_FORMATS = {\n  ARXIV: 'arXiv',\n  IEEE: 'IEEE',\n  ACM: 'ACM',\n  SPRINGER: 'Springer',\n  ELSEVIER: 'Elsevier',\n  GENERIC: 'Generic',\n} as const;\n\nexport const ANALYSIS_TYPES = {\n  SUMMARY: 'summary',\n  KEY_FINDINGS: 'key_findings',\n  METHODOLOGY: 'methodology',\n  CONCEPTS: 'concepts',\n  CITATIONS: 'citations',\n  COMPARISON: 'comparison',\n} as const;\n\nexport const MESSAGE_TYPES = {\n  USER: 'user',\n  ASSISTANT: 'assistant',\n  SYSTEM: 'system',\n} as const;\n\nexport const ANNOTATION_TYPES = {\n  HIGHLIGHT: 'highlight',\n  NOTE: 'note',\n  BOOKMARK: 'bookmark',\n} as const;\n\nexport const VIEW_MODES = {\n  READER: 'reader',\n  LIBRARY: 'library',\n  COMPARISON: 'comparison',\n  ANALYSIS: 'analysis',\n} as const;\n\nexport const LOADING_MESSAGES = [\n  'Processing your document...',\n  'Extracting text content...',\n  'Analyzing paper structure...',\n  'Generating insights...',\n  'Almost ready...',\n] as const;\n\nexport const ERROR_MESSAGES = {\n  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',\n  INVALID_FILE_TYPE: 'Only PDF files are supported',\n  UPLOAD_FAILED: 'Failed to upload file. Please try again.',\n  PROCESSING_FAILED: 'Failed to process the document',\n  API_ERROR: 'An error occurred while communicating with the server',\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  GENERIC_ERROR: 'An unexpected error occurred',\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;;;;AAEjB,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,aAAa;IACb,aAAa,KAAK,OAAO;IACzB,kBAAkB;QAAC;KAAO;IAC1B,kBAAkB;QAAC;KAAM;AAC3B;AAEO,MAAM,SAAS;IACpB,SAAS;IACT,WAAW;IACX,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;QACJ,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,YAAY;QACV,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,QAAQ;AACV;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;AACP;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,SAAS;IACT,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,UAAU;AACZ;AAEO,MAAM,gBAAgB;IAC3B,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,UAAU;IACV,QAAQ;IACR,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,OAAO;IACP,MAAM;IACN,KAAK;IACL,UAAU;IACV,UAAU;IACV,SAAS;AACX;AAEO,MAAM,iBAAiB;IAC5B,SAAS;IACT,cAAc;IACd,aAAa;IACb,UAAU;IACV,WAAW;IACX,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,MAAM;IACN,WAAW;IACX,QAAQ;AACV;AAEO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,UAAU;AACZ;AAEO,MAAM,aAAa;IACxB,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,UAAU;AACZ;AAEO,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,mBAAmB;IACnB,eAAe;IACf,mBAAmB;IACnB,WAAW;IACX,eAAe;IACf,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/store/useAppStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { Paper, ChatMessage, UIState, ReadingSession, Annotation } from '@/lib/types';\nimport { VIEW_MODES } from '@/lib/constants';\n\ninterface AppState extends UIState {\n  // Papers\n  papers: Paper[];\n  currentPaperId: string | null;\n  \n  // Chat\n  chatMessages: ChatMessage[];\n  \n  // Reading sessions\n  readingSessions: ReadingSession[];\n  currentSession: ReadingSession | null;\n  \n  // Annotations\n  annotations: Annotation[];\n  \n  // Actions\n  setPapers: (papers: Paper[]) => void;\n  addPaper: (paper: Paper) => void;\n  removePaper: (paperId: string) => void;\n  setCurrentPaper: (paperId: string | null) => void;\n  \n  addChatMessage: (message: ChatMessage) => void;\n  clearChatMessages: () => void;\n  \n  setCurrentView: (view: UIState['currentView']) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSidebarCollapsed: (collapsed: boolean) => void;\n  \n  addAnnotation: (annotation: Annotation) => void;\n  removeAnnotation: (annotationId: string) => void;\n  updateAnnotation: (annotationId: string, updates: Partial<Annotation>) => void;\n  \n  startReadingSession: (paperId: string) => void;\n  endReadingSession: () => void;\n  updateReadingProgress: (progress: number) => void;\n  \n  selectPaper: (paperId: string) => void;\n  deselectPaper: (paperId: string) => void;\n  clearSelectedPapers: () => void;\n}\n\nexport const useAppStore = create<AppState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        // Initial state\n        papers: [],\n        currentPaperId: null,\n        currentPaper: null,\n        selectedPapers: [],\n        chatMessages: [],\n        readingSessions: [],\n        currentSession: null,\n        annotations: [],\n        isLoading: false,\n        error: null,\n        sidebarCollapsed: false,\n        currentView: VIEW_MODES.LIBRARY,\n        \n        // Paper actions\n        setPapers: (papers) => set({ papers }),\n        \n        addPaper: (paper) => set((state) => ({\n          papers: [...state.papers, paper]\n        })),\n        \n        removePaper: (paperId) => set((state) => ({\n          papers: state.papers.filter(p => p.id !== paperId),\n          selectedPapers: state.selectedPapers.filter(id => id !== paperId),\n          currentPaperId: state.currentPaperId === paperId ? null : state.currentPaperId,\n          currentPaper: state.currentPaperId === paperId ? null : state.currentPaper,\n        })),\n        \n        setCurrentPaper: (paperId) => {\n          const paper = paperId ? get().papers.find(p => p.id === paperId) : null;\n          set({ \n            currentPaperId: paperId, \n            currentPaper: paper || null \n          });\n        },\n        \n        // Chat actions\n        addChatMessage: (message) => set((state) => ({\n          chatMessages: [...state.chatMessages, message]\n        })),\n        \n        clearChatMessages: () => set({ chatMessages: [] }),\n        \n        // UI actions\n        setCurrentView: (view) => set({ currentView: view }),\n        setLoading: (isLoading) => set({ isLoading }),\n        setError: (error) => set({ error }),\n        setSidebarCollapsed: (sidebarCollapsed) => set({ sidebarCollapsed }),\n        \n        // Annotation actions\n        addAnnotation: (annotation) => set((state) => ({\n          annotations: [...state.annotations, annotation]\n        })),\n        \n        removeAnnotation: (annotationId) => set((state) => ({\n          annotations: state.annotations.filter(a => a.id !== annotationId)\n        })),\n        \n        updateAnnotation: (annotationId, updates) => set((state) => ({\n          annotations: state.annotations.map(a => \n            a.id === annotationId ? { ...a, ...updates } : a\n          )\n        })),\n        \n        // Reading session actions\n        startReadingSession: (paperId) => {\n          const session: ReadingSession = {\n            id: `session_${Date.now()}`,\n            paperId,\n            startTime: new Date(),\n            progress: 0,\n            notes: [],\n            bookmarks: [],\n          };\n          set((state) => ({\n            currentSession: session,\n            readingSessions: [...state.readingSessions, session]\n          }));\n        },\n        \n        endReadingSession: () => set((state) => {\n          if (state.currentSession) {\n            const updatedSession = {\n              ...state.currentSession,\n              endTime: new Date()\n            };\n            return {\n              currentSession: null,\n              readingSessions: state.readingSessions.map(s => \n                s.id === updatedSession.id ? updatedSession : s\n              )\n            };\n          }\n          return state;\n        }),\n        \n        updateReadingProgress: (progress) => set((state) => {\n          if (state.currentSession) {\n            const updatedSession = { ...state.currentSession, progress };\n            return {\n              currentSession: updatedSession,\n              readingSessions: state.readingSessions.map(s => \n                s.id === updatedSession.id ? updatedSession : s\n              )\n            };\n          }\n          return state;\n        }),\n        \n        // Selection actions\n        selectPaper: (paperId) => set((state) => ({\n          selectedPapers: state.selectedPapers.includes(paperId) \n            ? state.selectedPapers \n            : [...state.selectedPapers, paperId]\n        })),\n        \n        deselectPaper: (paperId) => set((state) => ({\n          selectedPapers: state.selectedPapers.filter(id => id !== paperId)\n        })),\n        \n        clearSelectedPapers: () => set({ selectedPapers: [] }),\n      }),\n      {\n        name: 'documancer-store',\n        partialize: (state) => ({\n          papers: state.papers,\n          readingSessions: state.readingSessions,\n          annotations: state.annotations,\n          sidebarCollapsed: state.sidebarCollapsed,\n        }),\n      }\n    ),\n    { name: 'documancer-store' }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AA4CO,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,QAAQ,EAAE;QACV,gBAAgB;QAChB,cAAc;QACd,gBAAgB,EAAE;QAClB,cAAc,EAAE;QAChB,iBAAiB,EAAE;QACnB,gBAAgB;QAChB,aAAa,EAAE;QACf,WAAW;QACX,OAAO;QACP,kBAAkB;QAClB,aAAa,uHAAA,CAAA,aAAU,CAAC,OAAO;QAE/B,gBAAgB;QAChB,WAAW,CAAC,SAAW,IAAI;gBAAE;YAAO;QAEpC,UAAU,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBACnC,QAAQ;2BAAI,MAAM,MAAM;wBAAE;qBAAM;gBAClC,CAAC;QAED,aAAa,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBACxC,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC1C,gBAAgB,MAAM,cAAc,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;oBACzD,gBAAgB,MAAM,cAAc,KAAK,UAAU,OAAO,MAAM,cAAc;oBAC9E,cAAc,MAAM,cAAc,KAAK,UAAU,OAAO,MAAM,YAAY;gBAC5E,CAAC;QAED,iBAAiB,CAAC;YAChB,MAAM,QAAQ,UAAU,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW;YACnE,IAAI;gBACF,gBAAgB;gBAChB,cAAc,SAAS;YACzB;QACF;QAEA,eAAe;QACf,gBAAgB,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3C,cAAc;2BAAI,MAAM,YAAY;wBAAE;qBAAQ;gBAChD,CAAC;QAED,mBAAmB,IAAM,IAAI;gBAAE,cAAc,EAAE;YAAC;QAEhD,aAAa;QACb,gBAAgB,CAAC,OAAS,IAAI;gBAAE,aAAa;YAAK;QAClD,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,qBAAqB,CAAC,mBAAqB,IAAI;gBAAE;YAAiB;QAElE,qBAAqB;QACrB,eAAe,CAAC,aAAe,IAAI,CAAC,QAAU,CAAC;oBAC7C,aAAa;2BAAI,MAAM,WAAW;wBAAE;qBAAW;gBACjD,CAAC;QAED,kBAAkB,CAAC,eAAiB,IAAI,CAAC,QAAU,CAAC;oBAClD,aAAa,MAAM,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACtD,CAAC;QAED,kBAAkB,CAAC,cAAc,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,aAAa,MAAM,WAAW,CAAC,GAAG,CAAC,CAAA,IACjC,EAAE,EAAE,KAAK,eAAe;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEnD,CAAC;QAED,0BAA0B;QAC1B,qBAAqB,CAAC;YACpB,MAAM,UAA0B;gBAC9B,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;gBAC3B;gBACA,WAAW,IAAI;gBACf,UAAU;gBACV,OAAO,EAAE;gBACT,WAAW,EAAE;YACf;YACA,IAAI,CAAC,QAAU,CAAC;oBACd,gBAAgB;oBAChB,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;qBAAQ;gBACtD,CAAC;QACH;QAEA,mBAAmB,IAAM,IAAI,CAAC;gBAC5B,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,iBAAiB;wBACrB,GAAG,MAAM,cAAc;wBACvB,SAAS,IAAI;oBACf;oBACA,OAAO;wBACL,gBAAgB;wBAChB,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,eAAe,EAAE,GAAG,iBAAiB;oBAElD;gBACF;gBACA,OAAO;YACT;QAEA,uBAAuB,CAAC,WAAa,IAAI,CAAC;gBACxC,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,iBAAiB;wBAAE,GAAG,MAAM,cAAc;wBAAE;oBAAS;oBAC3D,OAAO;wBACL,gBAAgB;wBAChB,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,eAAe,EAAE,GAAG,iBAAiB;oBAElD;gBACF;gBACA,OAAO;YACT;QAEA,oBAAoB;QACpB,aAAa,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBACxC,gBAAgB,MAAM,cAAc,CAAC,QAAQ,CAAC,WAC1C,MAAM,cAAc,GACpB;2BAAI,MAAM,cAAc;wBAAE;qBAAQ;gBACxC,CAAC;QAED,eAAe,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC1C,gBAAgB,MAAM,cAAc,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;gBAC3D,CAAC;QAED,qBAAqB,IAAM,IAAI;gBAAE,gBAAgB,EAAE;YAAC;IACtD,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,QAAQ,MAAM,MAAM;YACpB,iBAAiB,MAAM,eAAe;YACtC,aAAa,MAAM,WAAW;YAC9B,kBAAkB,MAAM,gBAAgB;QAC1C,CAAC;AACH,IAEF;IAAE,MAAM;AAAmB", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Layout, Menu, Button, Avatar, Dropdown, Space, Typography, Badge } from 'antd';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  BookOutlined,\n  FileTextOutlined,\n  BarChartOutlined,\n  SettingOutlined,\n  UserOutlined,\n  BellOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store/useAppStore';\nimport { VIEW_MODES } from '@/lib/constants';\n\nconst { Header, Sider, Content } = Layout;\nconst { Title } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const { \n    sidebarCollapsed, \n    setSidebarCollapsed, \n    currentView, \n    setCurrentView,\n    papers\n  } = useAppStore();\n\n  const menuItems = [\n    {\n      key: VIEW_MODES.LIBRARY,\n      icon: <BookOutlined />,\n      label: 'Library',\n    },\n    {\n      key: VIEW_MODES.READER,\n      icon: <FileTextOutlined />,\n      label: 'Reader',\n    },\n    {\n      key: VIEW_MODES.COMPARISON,\n      icon: <span>⚖️</span>,\n      label: 'Compare',\n    },\n    {\n      key: VIEW_MODES.ANALYSIS,\n      icon: <BarChartOutlined />,\n      label: 'Analysis',\n    },\n  ];\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      label: 'Profile',\n      icon: <UserOutlined />,\n    },\n    {\n      key: 'settings',\n      label: 'Settings',\n      icon: <SettingOutlined />,\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      label: 'Logout',\n      danger: true,\n    },\n  ];\n\n  const handleMenuClick = (key: string) => {\n    setCurrentView(key as any);\n  };\n\n  const handleUserMenuClick = ({ key }: { key: string }) => {\n    switch (key) {\n      case 'profile':\n        // Handle profile\n        break;\n      case 'settings':\n        // Handle settings\n        break;\n      case 'logout':\n        // Handle logout\n        break;\n    }\n  };\n\n  return (\n    <Layout className=\"min-h-screen\">\n      <Sider\n        trigger={null}\n        collapsible\n        collapsed={sidebarCollapsed}\n        width={240}\n        className=\"bg-white border-r border-gray-200\"\n        style={{\n          boxShadow: '2px 0 8px rgba(0,0,0,0.06)',\n        }}\n      >\n        <div className=\"flex items-center justify-center h-16 border-b border-gray-200\">\n          {!sidebarCollapsed ? (\n            <Title level={3} className=\"text-gradient m-0\">\n              DocuMancer\n            </Title>\n          ) : (\n            <div className=\"text-2xl font-bold text-gradient\">D</div>\n          )}\n        </div>\n        \n        <Menu\n          mode=\"inline\"\n          selectedKeys={[currentView]}\n          items={menuItems}\n          onClick={({ key }) => handleMenuClick(key)}\n          className=\"border-none\"\n          style={{ height: 'calc(100vh - 64px)' }}\n        />\n      </Sider>\n\n      <Layout>\n        <Header className=\"bg-white border-b border-gray-200 px-4 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              type=\"text\"\n              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}\n              className=\"text-lg\"\n            />\n            \n            <div className=\"flex items-center space-x-2 text-gray-600\">\n              <BookOutlined />\n              <span>{papers.length} Papers</span>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              type=\"text\"\n              icon={<SearchOutlined />}\n              className=\"text-lg\"\n            />\n            \n            <Badge count={5} size=\"small\">\n              <Button\n                type=\"text\"\n                icon={<BellOutlined />}\n                className=\"text-lg\"\n              />\n            </Badge>\n\n            <Dropdown\n              menu={{\n                items: userMenuItems,\n                onClick: handleUserMenuClick,\n              }}\n              placement=\"bottomRight\"\n              arrow\n            >\n              <Space className=\"cursor-pointer hover:bg-gray-50 px-2 py-1 rounded\">\n                <Avatar size=\"small\" icon={<UserOutlined />} />\n                <span className=\"text-sm font-medium\">User</span>\n              </Space>\n            </Dropdown>\n          </div>\n        </Header>\n\n        <Content className=\"bg-gray-50 overflow-hidden\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAhBA;;;;;;AAkBA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,KAAK,EAAE,GAAG,0LAAA,CAAA,aAAU;AAM5B,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE;IACzD,MAAM,EACJ,gBAAgB,EAChB,mBAAmB,EACnB,WAAW,EACX,cAAc,EACd,MAAM,EACP,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,YAAY;QAChB;YACE,KAAK,uHAAA,CAAA,aAAU,CAAC,OAAO;YACvB,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;QACA;YACE,KAAK,uHAAA,CAAA,aAAU,CAAC,MAAM;YACtB,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;QACT;QACA;YACE,KAAK,uHAAA,CAAA,aAAU,CAAC,UAAU;YAC1B,oBAAM,8OAAC;0BAAK;;;;;;YACZ,OAAO;QACT;QACA;YACE,KAAK,uHAAA,CAAA,aAAU,CAAC,QAAQ;YACxB,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;QACT;KACD;IAED,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;QACrB;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;QACxB;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ;QACV;KACD;IAED,MAAM,kBAAkB,CAAC;QACvB,eAAe;IACjB;IAEA,MAAM,sBAAsB,CAAC,EAAE,GAAG,EAAmB;QACnD,OAAQ;YACN,KAAK;gBAEH;YACF,KAAK;gBAEH;YACF,KAAK;gBAEH;QACJ;IACF;IAEA,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,WAAU;;0BAChB,8OAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,WAAU;gBACV,OAAO;oBACL,WAAW;gBACb;;kCAEA,8OAAC;wBAAI,WAAU;kCACZ,CAAC,iCACA,8OAAC;4BAAM,OAAO;4BAAG,WAAU;sCAAoB;;;;;qFAI/C,8OAAC;4BAAI,WAAU;sCAAmC;;;;;;;;;;;kCAItD,8OAAC,8KAAA,CAAA,OAAI;wBACH,MAAK;wBACL,cAAc;4BAAC;yBAAY;wBAC3B,OAAO;wBACP,SAAS,CAAC,EAAE,GAAG,EAAE,GAAK,gBAAgB;wBACtC,WAAU;wBACV,OAAO;4BAAE,QAAQ;wBAAqB;;;;;;;;;;;;0BAI1C,8OAAC,kLAAA,CAAA,SAAM;;kCACL,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,iCAAmB,8OAAC,8NAAA,CAAA,qBAAkB;;;;mEAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCACnE,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,eAAY;;;;;0DACb,8OAAC;;oDAAM,OAAO,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;0CAIzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,WAAU;;;;;;kDAGZ,8OAAC,gLAAA,CAAA,QAAK;wCAAC,OAAO;wCAAG,MAAK;kDACpB,cAAA,8OAAC,kMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4CACnB,WAAU;;;;;;;;;;;kDAId,8OAAC,sLAAA,CAAA,WAAQ;wCACP,MAAM;4CACJ,OAAO;4CACP,SAAS;wCACX;wCACA,WAAU;wCACV,KAAK;kDAEL,cAAA,8OAAC,gMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,kLAAA,CAAA,SAAM;oDAAC,MAAK;oDAAQ,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;8DACxC,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,8OAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/file-utils.ts"], "sourcesContent": ["import { APP_CONFIG, ERROR_MESSAGES } from './constants';\n\nexport interface FileValidationResult {\n  isValid: boolean;\n  error?: string;\n  fileInfo?: {\n    name: string;\n    size: number;\n    type: string;\n    lastModified: number;\n  };\n}\n\nexport class FileUtils {\n  static validateFile(file: File): FileValidationResult {\n    // Check if file exists\n    if (!file) {\n      return {\n        isValid: false,\n        error: 'No file provided',\n      };\n    }\n\n    // Check file type\n    const fileName = file.name.toLowerCase();\n    const isValidType = APP_CONFIG.allowedFileTypes.some(type => \n      fileName.endsWith(type.toLowerCase())\n    );\n\n    if (!isValidType) {\n      return {\n        isValid: false,\n        error: ERROR_MESSAGES.INVALID_FILE_TYPE,\n      };\n    }\n\n    // Check file size\n    if (file.size > APP_CONFIG.maxFileSize) {\n      return {\n        isValid: false,\n        error: ERROR_MESSAGES.FILE_TOO_LARGE,\n      };\n    }\n\n    // Check if file is empty\n    if (file.size === 0) {\n      return {\n        isValid: false,\n        error: 'File is empty',\n      };\n    }\n\n    return {\n      isValid: true,\n      fileInfo: {\n        name: file.name,\n        size: file.size,\n        type: file.type,\n        lastModified: file.lastModified,\n      },\n    };\n  }\n\n  static formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  static getFileExtension(filename: string): string {\n    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);\n  }\n\n  static generateUniqueFileName(originalName: string): string {\n    const timestamp = Date.now();\n    const randomString = Math.random().toString(36).substring(2, 15);\n    const extension = this.getFileExtension(originalName);\n    const nameWithoutExt = originalName.replace(`.${extension}`, '');\n    \n    return `${timestamp}_${randomString}_${nameWithoutExt}.${extension}`;\n  }\n\n  static async fileToBuffer(file: File): Promise<Buffer> {\n    const arrayBuffer = await file.arrayBuffer();\n    return Buffer.from(arrayBuffer);\n  }\n\n  static async fileToBase64(file: File): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        const result = reader.result as string;\n        // Remove the data URL prefix (e.g., \"data:application/pdf;base64,\")\n        const base64 = result.split(',')[1];\n        resolve(base64);\n      };\n      reader.onerror = error => reject(error);\n    });\n  }\n\n  static createFileFromBuffer(buffer: Buffer, filename: string, mimeType: string): File {\n    const blob = new Blob([buffer], { type: mimeType });\n    return new File([blob], filename, { type: mimeType });\n  }\n\n  static downloadFile(content: string | Blob, filename: string, mimeType: string = 'text/plain') {\n    const blob = content instanceof Blob ? content : new Blob([content], { type: mimeType });\n    const url = URL.createObjectURL(blob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    \n    // Cleanup\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  }\n\n  static async compressFile(file: File, quality: number = 0.8): Promise<File> {\n    // For PDF files, we don't compress as it might affect readability\n    // This is a placeholder for future image compression if needed\n    if (file.type === 'application/pdf') {\n      return file;\n    }\n\n    // For other file types, return as-is for now\n    return file;\n  }\n\n  static getFileIcon(filename: string): string {\n    const extension = this.getFileExtension(filename).toLowerCase();\n    \n    switch (extension) {\n      case 'pdf':\n        return '📄';\n      case 'doc':\n      case 'docx':\n        return '📝';\n      case 'txt':\n        return '📃';\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n      case 'gif':\n        return '🖼️';\n      default:\n        return '📁';\n    }\n  }\n\n  static truncateFileName(filename: string, maxLength: number = 30): string {\n    if (filename.length <= maxLength) {\n      return filename;\n    }\n\n    const extension = this.getFileExtension(filename);\n    const nameWithoutExt = filename.replace(`.${extension}`, '');\n    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4);\n    \n    return `${truncatedName}...${extension}`;\n  }\n\n  static isValidPDFHeader(buffer: Buffer): boolean {\n    // Check if the buffer starts with PDF header\n    const header = buffer.slice(0, 5).toString();\n    return header === '%PDF-';\n  }\n\n  static extractPDFVersion(buffer: Buffer): string | null {\n    // Extract PDF version from header (e.g., %PDF-1.4)\n    const header = buffer.slice(0, 8).toString();\n    const match = header.match(/%PDF-(\\d+\\.\\d+)/);\n    return match ? match[1] : null;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAaO,MAAM;IACX,OAAO,aAAa,IAAU,EAAwB;QACpD,uBAAuB;QACvB,IAAI,CAAC,MAAM;YACT,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,kBAAkB;QAClB,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;QACtC,MAAM,cAAc,uHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,OACnD,SAAS,QAAQ,CAAC,KAAK,WAAW;QAGpC,IAAI,CAAC,aAAa;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO,uHAAA,CAAA,iBAAc,CAAC,iBAAiB;YACzC;QACF;QAEA,kBAAkB;QAClB,IAAI,KAAK,IAAI,GAAG,uHAAA,CAAA,aAAU,CAAC,WAAW,EAAE;YACtC,OAAO;gBACL,SAAS;gBACT,OAAO,uHAAA,CAAA,iBAAc,CAAC,cAAc;YACtC;QACF;QAEA,yBAAyB;QACzB,IAAI,KAAK,IAAI,KAAK,GAAG;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,UAAU;gBACR,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,cAAc,KAAK,YAAY;YACjC;QACF;IACF;IAEA,OAAO,eAAe,KAAa,EAAU;QAC3C,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,OAAO,iBAAiB,QAAgB,EAAU;QAChD,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;IAChE;IAEA,OAAO,uBAAuB,YAAoB,EAAU;QAC1D,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,eAAe,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;QAC7D,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,MAAM,iBAAiB,aAAa,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE;QAE7D,OAAO,GAAG,UAAU,CAAC,EAAE,aAAa,CAAC,EAAE,eAAe,CAAC,EAAE,WAAW;IACtE;IAEA,aAAa,aAAa,IAAU,EAAmB;QACrD,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,OAAO,OAAO,IAAI,CAAC;IACrB;IAEA,aAAa,aAAa,IAAU,EAAmB;QACrD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,aAAa,CAAC;YACrB,OAAO,MAAM,GAAG;gBACd,MAAM,SAAS,OAAO,MAAM;gBAC5B,oEAAoE;gBACpE,MAAM,SAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;gBACnC,QAAQ;YACV;YACA,OAAO,OAAO,GAAG,CAAA,QAAS,OAAO;QACnC;IACF;IAEA,OAAO,qBAAqB,MAAc,EAAE,QAAgB,EAAE,QAAgB,EAAQ;QACpF,MAAM,OAAO,IAAI,KAAK;YAAC;SAAO,EAAE;YAAE,MAAM;QAAS;QACjD,OAAO,IAAI,KAAK;YAAC;SAAK,EAAE,UAAU;YAAE,MAAM;QAAS;IACrD;IAEA,OAAO,aAAa,OAAsB,EAAE,QAAgB,EAAE,WAAmB,YAAY,EAAE;QAC7F,MAAM,OAAO,mBAAmB,OAAO,UAAU,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAS;QACtF,MAAM,MAAM,IAAI,eAAe,CAAC;QAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QAEV,UAAU;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,aAAa,aAAa,IAAU,EAAE,UAAkB,GAAG,EAAiB;QAC1E,kEAAkE;QAClE,+DAA+D;QAC/D,IAAI,KAAK,IAAI,KAAK,mBAAmB;YACnC,OAAO;QACT;QAEA,6CAA6C;QAC7C,OAAO;IACT;IAEA,OAAO,YAAY,QAAgB,EAAU;QAC3C,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,UAAU,WAAW;QAE7D,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,OAAO,iBAAiB,QAAgB,EAAE,YAAoB,EAAE,EAAU;QACxE,IAAI,SAAS,MAAM,IAAI,WAAW;YAChC,OAAO;QACT;QAEA,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,MAAM,iBAAiB,SAAS,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE;QACzD,MAAM,gBAAgB,eAAe,SAAS,CAAC,GAAG,YAAY,UAAU,MAAM,GAAG;QAEjF,OAAO,GAAG,cAAc,GAAG,EAAE,WAAW;IAC1C;IAEA,OAAO,iBAAiB,MAAc,EAAW;QAC/C,6CAA6C;QAC7C,MAAM,SAAS,OAAO,KAAK,CAAC,GAAG,GAAG,QAAQ;QAC1C,OAAO,WAAW;IACpB;IAEA,OAAO,kBAAkB,MAAc,EAAiB;QACtD,mDAAmD;QACnD,MAAM,SAAS,OAAO,KAAK,CAAC,GAAG,GAAG,QAAQ;QAC1C,MAAM,QAAQ,OAAO,KAAK,CAAC;QAC3B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/hooks/useFileUpload.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { FileUtils, FileValidationResult } from '@/lib/file-utils';\nimport { Paper, FileUploadProgress } from '@/lib/types';\nimport { API_ENDPOINTS, ERROR_MESSAGES } from '@/lib/constants';\nimport { useAppStore } from '@/store/useAppStore';\n\ninterface UseFileUploadReturn {\n  uploadFile: (file: File) => Promise<Paper | null>;\n  uploadProgress: FileUploadProgress | null;\n  isUploading: boolean;\n  error: string | null;\n  clearError: () => void;\n}\n\nexport const useFileUpload = (): UseFileUploadReturn => {\n  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress | null>(null);\n  const [isUploading, setIsUploading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  \n  const { addPaper, setError: setGlobalError } = useAppStore();\n\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  const uploadFile = useCallback(async (file: File): Promise<Paper | null> => {\n    try {\n      setIsUploading(true);\n      setError(null);\n      \n      // Validate file\n      const validation: FileValidationResult = FileUtils.validateFile(file);\n      if (!validation.isValid) {\n        throw new Error(validation.error);\n      }\n\n      // Initialize progress\n      setUploadProgress({\n        fileName: file.name,\n        progress: 0,\n        status: 'uploading',\n      });\n\n      // Create form data\n      const formData = new FormData();\n      formData.append('file', file);\n\n      // Upload file with progress tracking\n      const response = await fetch(API_ENDPOINTS.upload, {\n        method: 'POST',\n        body: formData,\n      });\n\n      // Update progress\n      setUploadProgress(prev => prev ? {\n        ...prev,\n        progress: 50,\n        status: 'processing',\n      } : null);\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || ERROR_MESSAGES.UPLOAD_FAILED);\n      }\n\n      const result = await response.json();\n      \n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.UPLOAD_FAILED);\n      }\n\n      // Update progress\n      setUploadProgress(prev => prev ? {\n        ...prev,\n        progress: 90,\n      } : null);\n\n      const paper: Paper = result.data.paper;\n      \n      // Add paper to store\n      addPaper(paper);\n\n      // Save paper to backend\n      await fetch(API_ENDPOINTS.papers, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(paper),\n      });\n\n      // Complete progress\n      setUploadProgress(prev => prev ? {\n        ...prev,\n        progress: 100,\n        status: 'completed',\n      } : null);\n\n      // Clear progress after a delay\n      setTimeout(() => {\n        setUploadProgress(null);\n      }, 2000);\n\n      return paper;\n\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.UPLOAD_FAILED;\n      \n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      \n      setUploadProgress(prev => prev ? {\n        ...prev,\n        status: 'error',\n        error: errorMessage,\n      } : null);\n\n      return null;\n    } finally {\n      setIsUploading(false);\n    }\n  }, [addPaper, setGlobalError]);\n\n  return {\n    uploadFile,\n    uploadProgress,\n    isUploading,\n    error,\n    clearError,\n  };\n};\n\n// Hook for drag and drop functionality\nexport const useDragAndDrop = (onFileDrop: (files: File[]) => void) => {\n  const [isDragOver, setIsDragOver] = useState(false);\n\n  const handleDragEnter = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragOver(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragOver(false);\n  }, []);\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsDragOver(false);\n\n    const files = Array.from(e.dataTransfer.files);\n    if (files.length > 0) {\n      onFileDrop(files);\n    }\n  }, [onFileDrop]);\n\n  return {\n    isDragOver,\n    dragHandlers: {\n      onDragEnter: handleDragEnter,\n      onDragLeave: handleDragLeave,\n      onDragOver: handleDragOver,\n      onDrop: handleDrop,\n    },\n  };\n};\n\n// Hook for batch file upload\nexport const useBatchUpload = () => {\n  const [uploads, setUploads] = useState<Map<string, FileUploadProgress>>(new Map());\n  const { uploadFile } = useFileUpload();\n\n  const uploadFiles = useCallback(async (files: File[]) => {\n    const uploadPromises = files.map(async (file) => {\n      const fileId = `${file.name}_${Date.now()}`;\n      \n      // Initialize progress for this file\n      setUploads(prev => new Map(prev.set(fileId, {\n        fileName: file.name,\n        progress: 0,\n        status: 'uploading',\n      })));\n\n      try {\n        const result = await uploadFile(file);\n        \n        // Update success status\n        setUploads(prev => new Map(prev.set(fileId, {\n          fileName: file.name,\n          progress: 100,\n          status: 'completed',\n        })));\n\n        return result;\n      } catch (error) {\n        // Update error status\n        setUploads(prev => new Map(prev.set(fileId, {\n          fileName: file.name,\n          progress: 0,\n          status: 'error',\n          error: error instanceof Error ? error.message : 'Upload failed',\n        })));\n\n        return null;\n      }\n    });\n\n    const results = await Promise.all(uploadPromises);\n    \n    // Clear uploads after a delay\n    setTimeout(() => {\n      setUploads(new Map());\n    }, 5000);\n\n    return results.filter(Boolean) as Paper[];\n  }, [uploadFile]);\n\n  const clearUploads = useCallback(() => {\n    setUploads(new Map());\n  }, []);\n\n  return {\n    uploadFiles,\n    uploads: Array.from(uploads.values()),\n    clearUploads,\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AACA;;;;;AAUO,MAAM,gBAAgB;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAChF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,QAAQ,EAAE,UAAU,cAAc,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAEzD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;IACX,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI;YACF,eAAe;YACf,SAAS;YAET,gBAAgB;YAChB,MAAM,aAAmC,2HAAA,CAAA,YAAS,CAAC,YAAY,CAAC;YAChE,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,IAAI,MAAM,WAAW,KAAK;YAClC;YAEA,sBAAsB;YACtB,kBAAkB;gBAChB,UAAU,KAAK,IAAI;gBACnB,UAAU;gBACV,QAAQ;YACV;YAEA,mBAAmB;YACnB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,qCAAqC;YACrC,MAAM,WAAW,MAAM,MAAM,uHAAA,CAAA,gBAAa,CAAC,MAAM,EAAE;gBACjD,QAAQ;gBACR,MAAM;YACR;YAEA,kBAAkB;YAClB,kBAAkB,CAAA,OAAQ,OAAO;oBAC/B,GAAG,IAAI;oBACP,UAAU;oBACV,QAAQ;gBACV,IAAI;YAEJ,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,aAAa;YACjE;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,aAAa;YAC9D;YAEA,kBAAkB;YAClB,kBAAkB,CAAA,OAAQ,OAAO;oBAC/B,GAAG,IAAI;oBACP,UAAU;gBACZ,IAAI;YAEJ,MAAM,QAAe,OAAO,IAAI,CAAC,KAAK;YAEtC,qBAAqB;YACrB,SAAS;YAET,wBAAwB;YACxB,MAAM,MAAM,uHAAA,CAAA,gBAAa,CAAC,MAAM,EAAE;gBAChC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,oBAAoB;YACpB,kBAAkB,CAAA,OAAQ,OAAO;oBAC/B,GAAG,IAAI;oBACP,UAAU;oBACV,QAAQ;gBACV,IAAI;YAEJ,+BAA+B;YAC/B,WAAW;gBACT,kBAAkB;YACpB,GAAG;YAEH,OAAO;QAET,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,uHAAA,CAAA,iBAAc,CAAC,aAAa;YAEtF,SAAS;YACT,eAAe;YAEf,kBAAkB,CAAA,OAAQ,OAAO;oBAC/B,GAAG,IAAI;oBACP,QAAQ;oBACR,OAAO;gBACT,IAAI;YAEJ,OAAO;QACT,SAAU;YACR,eAAe;QACjB;IACF,GAAG;QAAC;QAAU;KAAe;IAE7B,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,EAAE,cAAc;QAChB,EAAE,eAAe;IACnB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;QAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,WAAW;QACb;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA,cAAc;YACZ,aAAa;YACb,aAAa;YACb,YAAY;YACZ,QAAQ;QACV;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC,IAAI;IAC5E,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,MAAM,iBAAiB,MAAM,GAAG,CAAC,OAAO;YACtC,MAAM,SAAS,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;YAE3C,oCAAoC;YACpC,WAAW,CAAA,OAAQ,IAAI,IAAI,KAAK,GAAG,CAAC,QAAQ;oBAC1C,UAAU,KAAK,IAAI;oBACnB,UAAU;oBACV,QAAQ;gBACV;YAEA,IAAI;gBACF,MAAM,SAAS,MAAM,WAAW;gBAEhC,wBAAwB;gBACxB,WAAW,CAAA,OAAQ,IAAI,IAAI,KAAK,GAAG,CAAC,QAAQ;wBAC1C,UAAU,KAAK,IAAI;wBACnB,UAAU;wBACV,QAAQ;oBACV;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,sBAAsB;gBACtB,WAAW,CAAA,OAAQ,IAAI,IAAI,KAAK,GAAG,CAAC,QAAQ;wBAC1C,UAAU,KAAK,IAAI;wBACnB,UAAU;wBACV,QAAQ;wBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAClD;gBAEA,OAAO;YACT;QACF;QAEA,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;QAElC,8BAA8B;QAC9B,WAAW;YACT,WAAW,IAAI;QACjB,GAAG;QAEH,OAAO,QAAQ,MAAM,CAAC;IACxB,GAAG;QAAC;KAAW;IAEf,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,WAAW,IAAI;IACjB,GAAG,EAAE;IAEL,OAAO;QACL;QACA,SAAS,MAAM,IAAI,CAAC,QAAQ,MAAM;QAClC;IACF;AACF", "debugId": null}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/upload/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { Upload, Button, Progress, message, Card, Typography, Space, Alert, List, Tooltip } from 'antd';\nimport { InboxOutlined, UploadOutlined, FileTextOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined, InfoCircleOutlined } from '@ant-design/icons';\nimport { useFileUpload, useDragAndDrop } from '@/hooks/useFileUpload';\nimport { FileUtils } from '@/lib/file-utils';\nimport { APP_CONFIG } from '@/lib/constants';\n\nconst { Dragger } = Upload;\nconst { Title, Text } = Typography;\n\ninterface FileUploadProps {\n  onUploadSuccess?: (paperId: string) => void;\n  className?: string;\n  showProgress?: boolean;\n  maxFileSize?: number;\n  allowedTypes?: string[];\n  showFileInfo?: boolean;\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({\n  onUploadSuccess,\n  className = '',\n  showProgress = true,\n  maxFileSize = APP_CONFIG.maxFileSize,\n  allowedTypes = ['.pdf'],\n  showFileInfo = true,\n}) => {\n  const { uploadFile, uploadProgress, isUploading, error, clearError } = useFileUpload();\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');\n\n  const handleFileSelect = useCallback(async (file: File) => {\n    // Validate file\n    const validation = FileUtils.validateFile(file);\n    if (!validation.isValid) {\n      message.error(validation.error);\n      setUploadStatus('error');\n      return false;\n    }\n\n    setSelectedFile(file);\n    setUploadStatus('uploading');\n    clearError();\n\n    try {\n      // Upload file\n      const paper = await uploadFile(file);\n      if (paper) {\n        message.success('File uploaded successfully!');\n        setUploadStatus('success');\n        onUploadSuccess?.(paper.id);\n\n        // Reset after a delay\n        setTimeout(() => {\n          setSelectedFile(null);\n          setUploadStatus('idle');\n        }, 2000);\n      } else {\n        setUploadStatus('error');\n      }\n    } catch (error) {\n      console.error('Upload error:', error);\n      setUploadStatus('error');\n    }\n\n    return false; // Prevent default upload behavior\n  }, [uploadFile, onUploadSuccess, clearError]);\n\n  const { isDragOver, dragHandlers } = useDragAndDrop((files) => {\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  });\n\n  const uploadProps = {\n    name: 'file',\n    multiple: false,\n    accept: APP_CONFIG.allowedFileTypes.join(','),\n    beforeUpload: handleFileSelect,\n    showUploadList: false,\n    ...dragHandlers,\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      <Card className={`${isDragOver ? 'border-blue-400 bg-blue-50' : ''} transition-all duration-200`}>\n        <Dragger {...uploadProps} className=\"border-dashed\">\n          <div className=\"py-8\">\n            <p className=\"ant-upload-drag-icon\">\n              <InboxOutlined className=\"text-4xl text-blue-500\" />\n            </p>\n            <Title level={4} className=\"ant-upload-text\">\n              Click or drag PDF files to upload\n            </Title>\n            <Text className=\"ant-upload-hint text-gray-500\">\n              Support for academic papers in PDF format. Maximum file size: {FileUtils.formatFileSize(APP_CONFIG.maxFileSize)}\n            </Text>\n          </div>\n        </Dragger>\n      </Card>\n\n      {/* Upload Progress and Status */}\n      {showProgress && (uploadProgress || selectedFile) && (\n        <Card className=\"fade-in\">\n          <Space direction=\"vertical\" className=\"w-full\">\n            <div className=\"flex items-center space-x-3\">\n              {uploadStatus === 'uploading' && <LoadingOutlined className=\"text-blue-500\" />}\n              {uploadStatus === 'success' && <CheckCircleOutlined className=\"text-green-500\" />}\n              {uploadStatus === 'error' && <CloseCircleOutlined className=\"text-red-500\" />}\n              {uploadStatus === 'idle' && <FileTextOutlined className=\"text-blue-500\" />}\n\n              <div className=\"flex-1\">\n                <Text strong>{selectedFile?.name || uploadProgress?.fileName}</Text>\n                <div className=\"text-sm text-gray-500 capitalize\">\n                  {uploadStatus === 'uploading' && 'Uploading...'}\n                  {uploadStatus === 'success' && 'Upload completed successfully'}\n                  {uploadStatus === 'error' && 'Upload failed'}\n                  {uploadStatus === 'idle' && uploadProgress?.status.replace('_', ' ')}\n                </div>\n              </div>\n\n              {selectedFile && showFileInfo && (\n                <Tooltip title=\"File Information\">\n                  <div className=\"text-right text-sm text-gray-500\">\n                    <div>{FileUtils.formatFileSize(selectedFile.size)}</div>\n                    <div>{selectedFile.type || 'PDF'}</div>\n                  </div>\n                </Tooltip>\n              )}\n            </div>\n\n            {uploadProgress && (\n              <Progress\n                percent={uploadProgress.progress}\n                status={\n                  uploadProgress.status === 'error'\n                    ? 'exception'\n                    : uploadProgress.status === 'completed'\n                      ? 'success'\n                      : 'active'\n                }\n                strokeColor={{\n                  '0%': '#108ee9',\n                  '100%': '#87d068',\n                }}\n              />\n            )}\n\n            {uploadProgress?.error && (\n              <Text type=\"danger\" className=\"text-sm\">\n                {uploadProgress.error}\n              </Text>\n            )}\n          </Space>\n        </Card>\n      )}\n\n      {/* Error Display */}\n      {error && (\n        <Card className=\"border-red-200 bg-red-50\">\n          <Text type=\"danger\">{error}</Text>\n        </Card>\n      )}\n\n      {/* Alternative Upload Button */}\n      <div className=\"text-center\">\n        <Upload {...uploadProps}>\n          <Button \n            icon={<UploadOutlined />} \n            loading={isUploading}\n            size=\"large\"\n            type=\"primary\"\n          >\n            Choose File\n          </Button>\n        </Upload>\n      </div>\n\n      {/* File Info */}\n      <div className=\"text-center text-sm text-gray-500\">\n        <div>Supported formats: {APP_CONFIG.supportedFormats.join(', ')}</div>\n        <div>Maximum size: {FileUtils.formatFileSize(APP_CONFIG.maxFileSize)}</div>\n      </div>\n    </div>\n  );\n};\n\nexport default FileUpload;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AAC1B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAWlC,MAAM,aAAwC,CAAC,EAC7C,eAAe,EACf,YAAY,EAAE,EACd,eAAe,IAAI,EACnB,cAAc,uHAAA,CAAA,aAAU,CAAC,WAAW,EACpC,eAAe;IAAC;CAAO,EACvB,eAAe,IAAI,EACpB;IACC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8C;IAE7F,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,gBAAgB;QAChB,MAAM,aAAa,2HAAA,CAAA,YAAS,CAAC,YAAY,CAAC;QAC1C,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,WAAW,KAAK;YAC9B,gBAAgB;YAChB,OAAO;QACT;QAEA,gBAAgB;QAChB,gBAAgB;QAChB;QAEA,IAAI;YACF,cAAc;YACd,MAAM,QAAQ,MAAM,WAAW;YAC/B,IAAI,OAAO;gBACT,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,gBAAgB;gBAChB,kBAAkB,MAAM,EAAE;gBAE1B,sBAAsB;gBACtB,WAAW;oBACT,gBAAgB;oBAChB,gBAAgB;gBAClB,GAAG;YACL,OAAO;gBACL,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,gBAAgB;QAClB;QAEA,OAAO,OAAO,kCAAkC;IAClD,GAAG;QAAC;QAAY;QAAiB;KAAW;IAE5C,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC;QACnD,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,cAAc;QAClB,MAAM;QACN,UAAU;QACV,QAAQ,uHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC;QACzC,cAAc;QACd,gBAAgB;QAChB,GAAG,YAAY;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAW,GAAG,aAAa,+BAA+B,GAAG,4BAA4B,CAAC;0BAC9F,cAAA,8OAAC;oBAAS,GAAG,WAAW;oBAAE,WAAU;8BAClC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CACX,cAAA,8OAAC,oNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAE3B,8OAAC;gCAAM,OAAO;gCAAG,WAAU;0CAAkB;;;;;;0CAG7C,8OAAC;gCAAK,WAAU;;oCAAgC;oCACiB,2HAAA,CAAA,YAAS,CAAC,cAAc,CAAC,uHAAA,CAAA,aAAU,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;YAOrH,gBAAgB,CAAC,kBAAkB,YAAY,mBAC9C,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAW,WAAU;;sCACpC,8OAAC;4BAAI,WAAU;;gCACZ,iBAAiB,6BAAe,8OAAC,wNAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;gCAC3D,iBAAiB,2BAAa,8OAAC,gOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;gCAC7D,iBAAiB,yBAAW,8OAAC,gOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;gCAC3D,iBAAiB,wBAAU,8OAAC,0NAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;8CAExD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,MAAM;sDAAE,cAAc,QAAQ,gBAAgB;;;;;;sDACpD,8OAAC;4CAAI,WAAU;;gDACZ,iBAAiB,eAAe;gDAChC,iBAAiB,aAAa;gDAC9B,iBAAiB,WAAW;gDAC5B,iBAAiB,UAAU,gBAAgB,OAAO,QAAQ,KAAK;;;;;;;;;;;;;gCAInE,gBAAgB,8BACf,8OAAC,oLAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK,2HAAA,CAAA,YAAS,CAAC,cAAc,CAAC,aAAa,IAAI;;;;;;0DAChD,8OAAC;0DAAK,aAAa,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;;wBAMlC,gCACC,8OAAC,sLAAA,CAAA,WAAQ;4BACP,SAAS,eAAe,QAAQ;4BAChC,QACE,eAAe,MAAM,KAAK,UACtB,cACA,eAAe,MAAM,KAAK,cACxB,YACA;4BAER,aAAa;gCACX,MAAM;gCACN,QAAQ;4BACV;;;;;;wBAIH,gBAAgB,uBACf,8OAAC;4BAAK,MAAK;4BAAS,WAAU;sCAC3B,eAAe,KAAK;;;;;;;;;;;;;;;;;YAQ9B,uBACC,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC;oBAAK,MAAK;8BAAU;;;;;;;;;;;0BAKzB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kLAAA,CAAA,SAAM;oBAAE,GAAG,WAAW;8BACrB,cAAA,8OAAC,kMAAA,CAAA,SAAM;wBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wBACrB,SAAS;wBACT,MAAK;wBACL,MAAK;kCACN;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAI;4BAAoB,uHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC;;;;;;;kCAC1D,8OAAC;;4BAAI;4BAAe,2HAAA,CAAA,YAAS,CAAC,cAAc,CAAC,uHAAA,CAAA,aAAU,CAAC,WAAW;;;;;;;;;;;;;;;;;;;AAI3E;uCAEe", "debugId": null}}, {"offset": {"line": 1363, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/common/LoadingStates.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Spin, Progress, Card, Typography, Space, Skeleton } from 'antd';\nimport {\n  LoadingOutlined,\n  FileTextOutlined,\n  CloudUploadOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\n\nconst { Text, Title } = Typography;\n\ninterface LoadingSpinnerProps {\n  size?: 'small' | 'default' | 'large';\n  message?: string;\n  className?: string;\n}\n\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n  size = 'default',\n  message,\n  className = '',\n}) => {\n  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 32 : size === 'small' ? 16 : 24 }} spin />;\n\n  return (\n    <div className={`flex flex-col items-center justify-center p-8 ${className}`}>\n      <Spin indicator={antIcon} size={size} />\n      {message && (\n        <Text className=\"mt-4 text-gray-600 text-center\">\n          {message}\n        </Text>\n      )}\n    </div>\n  );\n};\n\ninterface ProgressLoadingProps {\n  progress: number;\n  message?: string;\n  subMessage?: string;\n  className?: string;\n}\n\nexport const ProgressLoading: React.FC<ProgressLoadingProps> = ({\n  progress,\n  message,\n  subMessage,\n  className = '',\n}) => {\n  return (\n    <div className={`flex flex-col items-center justify-center p-8 ${className}`}>\n      <div className=\"w-full max-w-md space-y-4\">\n        <div className=\"text-center\">\n          {message && (\n            <Text strong className=\"text-lg\">\n              {message}\n            </Text>\n          )}\n          {subMessage && (\n            <div className=\"text-sm text-gray-600 mt-1\">\n              {subMessage}\n            </div>\n          )}\n        </div>\n        \n        <Progress\n          percent={progress}\n          strokeColor={{\n            '0%': '#108ee9',\n            '100%': '#87d068',\n          }}\n          trailColor=\"#f0f0f0\"\n          strokeWidth={8}\n          className=\"progress-loading\"\n        />\n        \n        <div className=\"text-center text-sm text-gray-500\">\n          {progress}% complete\n        </div>\n      </div>\n    </div>\n  );\n};\n\ninterface StepLoadingProps {\n  steps: Array<{\n    title: string;\n    description?: string;\n    icon?: React.ReactNode;\n  }>;\n  currentStep: number;\n  className?: string;\n}\n\nexport const StepLoading: React.FC<StepLoadingProps> = ({\n  steps,\n  currentStep,\n  className = '',\n}) => {\n  return (\n    <div className={`p-8 ${className}`}>\n      <div className=\"max-w-md mx-auto space-y-4\">\n        {steps.map((step, index) => {\n          const isActive = index === currentStep;\n          const isCompleted = index < currentStep;\n          \n          return (\n            <div\n              key={index}\n              className={`flex items-center space-x-3 p-3 rounded-lg transition-all ${\n                isActive\n                  ? 'bg-blue-50 border border-blue-200'\n                  : isCompleted\n                  ? 'bg-green-50 border border-green-200'\n                  : 'bg-gray-50 border border-gray-200'\n              }`}\n            >\n              <div\n                className={`flex items-center justify-center w-8 h-8 rounded-full ${\n                  isActive\n                    ? 'bg-blue-500 text-white'\n                    : isCompleted\n                    ? 'bg-green-500 text-white'\n                    : 'bg-gray-300 text-gray-600'\n                }`}\n              >\n                {isActive ? (\n                  <Spin size=\"small\" />\n                ) : isCompleted ? (\n                  '✓'\n                ) : (\n                  step.icon || index + 1\n                )}\n              </div>\n              \n              <div className=\"flex-1\">\n                <div\n                  className={`font-medium ${\n                    isActive\n                      ? 'text-blue-700'\n                      : isCompleted\n                      ? 'text-green-700'\n                      : 'text-gray-600'\n                  }`}\n                >\n                  {step.title}\n                </div>\n                {step.description && (\n                  <div className=\"text-sm text-gray-500\">\n                    {step.description}\n                  </div>\n                )}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\n// Specific loading components for different features\nexport const PDFProcessingLoader: React.FC<{ fileName?: string }> = ({ fileName }) => {\n  const steps = [\n    {\n      title: 'Uploading file',\n      description: 'Transferring your PDF to the server',\n      icon: <CloudUploadOutlined />,\n    },\n    {\n      title: 'Processing PDF',\n      description: 'Extracting text and metadata',\n      icon: <FileTextOutlined />,\n    },\n    {\n      title: 'Analyzing content',\n      description: 'Preparing for AI analysis',\n      icon: <BrainOutlined />,\n    },\n  ];\n\n  return (\n    <Card className=\"max-w-lg mx-auto\">\n      <div className=\"text-center mb-6\">\n        <Title level={4}>Processing PDF</Title>\n        {fileName && (\n          <Text type=\"secondary\">\n            {fileName}\n          </Text>\n        )}\n      </div>\n      <StepLoading steps={steps} currentStep={1} />\n    </Card>\n  );\n};\n\nexport const AIAnalysisLoader: React.FC<{ analysisType?: string }> = ({ analysisType }) => {\n  return (\n    <div className=\"text-center p-8\">\n      <div className=\"mb-4\">\n        <div className=\"text-4xl text-blue-500 animate-pulse\">🧠</div>\n      </div>\n      <Title level={4}>AI Analysis in Progress</Title>\n      <Text type=\"secondary\">\n        {analysisType ? `Performing ${analysisType.toLowerCase()}...` : 'Analyzing your document...'}\n      </Text>\n      <div className=\"mt-4\">\n        <LoadingSpinner size=\"large\" />\n      </div>\n    </div>\n  );\n};\n\nexport const SearchLoader: React.FC<{ query?: string }> = ({ query }) => {\n  return (\n    <div className=\"text-center p-8\">\n      <div className=\"mb-4\">\n        <SearchOutlined className=\"text-4xl text-green-500 animate-pulse\" />\n      </div>\n      <Title level={4}>Searching...</Title>\n      {query && (\n        <Text type=\"secondary\">\n          Looking for \"{query}\"\n        </Text>\n      )}\n      <div className=\"mt-4\">\n        <LoadingSpinner />\n      </div>\n    </div>\n  );\n};\n\n// Skeleton loaders for different content types\nexport const PaperCardSkeleton: React.FC = () => {\n  return (\n    <Card className=\"h-full\">\n      <Skeleton\n        active\n        avatar={{ size: 'large', shape: 'square' }}\n        paragraph={{ rows: 4 }}\n        title={{ width: '80%' }}\n      />\n      <div className=\"mt-4 flex space-x-2\">\n        <Skeleton.Button size=\"small\" />\n        <Skeleton.Button size=\"small\" />\n        <Skeleton.Button size=\"small\" />\n      </div>\n    </Card>\n  );\n};\n\nexport const ChatMessageSkeleton: React.FC = () => {\n  return (\n    <div className=\"flex space-x-3 mb-4\">\n      <Skeleton.Avatar size=\"default\" />\n      <div className=\"flex-1\">\n        <Skeleton\n          active\n          paragraph={{ rows: 2, width: ['60%', '40%'] }}\n          title={false}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport const LibraryViewSkeleton: React.FC = () => {\n  return (\n    <div className=\"p-6\">\n      {/* Header skeleton */}\n      <div className=\"mb-6\">\n        <Skeleton.Input style={{ width: 200, height: 32 }} active />\n        <div className=\"mt-4\">\n          <Skeleton.Input style={{ width: '100%', height: 40 }} active />\n        </div>\n      </div>\n\n      {/* Cards grid skeleton */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n        {Array.from({ length: 8 }).map((_, index) => (\n          <PaperCardSkeleton key={index} />\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport const ReaderViewSkeleton: React.FC = () => {\n  return (\n    <div className=\"h-full flex\">\n      {/* PDF viewer skeleton */}\n      <div className=\"flex-1 p-4\">\n        <div className=\"mb-4\">\n          <Skeleton.Input style={{ width: 150, height: 32 }} active />\n        </div>\n        <Card className=\"h-full\">\n          <Skeleton\n            active\n            paragraph={{ rows: 20 }}\n            title={{ width: '60%' }}\n          />\n        </Card>\n      </div>\n\n      {/* Sidebar skeleton */}\n      <div className=\"w-96 border-l p-4\">\n        <div className=\"space-y-4\">\n          <Skeleton.Input style={{ width: '100%', height: 40 }} active />\n          {Array.from({ length: 5 }).map((_, index) => (\n            <ChatMessageSkeleton key={index} />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Loading overlay component\nexport const LoadingOverlay: React.FC<{\n  loading: boolean;\n  children: React.ReactNode;\n  message?: string;\n}> = ({ loading, children, message }) => {\n  return (\n    <Spin spinning={loading} tip={message}>\n      {children}\n    </Spin>\n  );\n};\n\nexport default LoadingSpinner;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAJA;;;;AAWA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,0LAAA,CAAA,aAAU;AAQ3B,MAAM,iBAAgD,CAAC,EAC5D,OAAO,SAAS,EAChB,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,wBAAU,8OAAC,wNAAA,CAAA,kBAAe;QAAC,OAAO;YAAE,UAAU,SAAS,UAAU,KAAK,SAAS,UAAU,KAAK;QAAG;QAAG,IAAI;;;;;;IAE9G,qBACE,8OAAC;QAAI,WAAW,CAAC,8CAA8C,EAAE,WAAW;;0BAC1E,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAW;gBAAS,MAAM;;;;;;YAC/B,yBACC,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAKX;AASO,MAAM,kBAAkD,CAAC,EAC9D,QAAQ,EACR,OAAO,EACP,UAAU,EACV,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,8CAA8C,EAAE,WAAW;kBAC1E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;wBACZ,yBACC,8OAAC;4BAAK,MAAM;4BAAC,WAAU;sCACpB;;;;;;wBAGJ,4BACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;8BAKP,8OAAC,sLAAA,CAAA,WAAQ;oBACP,SAAS;oBACT,aAAa;wBACX,MAAM;wBACN,QAAQ;oBACV;oBACA,YAAW;oBACX,aAAa;oBACb,WAAU;;;;;;8BAGZ,8OAAC;oBAAI,WAAU;;wBACZ;wBAAS;;;;;;;;;;;;;;;;;;AAKpB;AAYO,MAAM,cAA0C,CAAC,EACtD,KAAK,EACL,WAAW,EACX,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,IAAI,EAAE,WAAW;kBAChC,cAAA,8OAAC;YAAI,WAAU;sBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gBAChB,MAAM,WAAW,UAAU;gBAC3B,MAAM,cAAc,QAAQ;gBAE5B,qBACE,8OAAC;oBAEC,WAAW,CAAC,0DAA0D,EACpE,WACI,sCACA,cACA,wCACA,qCACJ;;sCAEF,8OAAC;4BACC,WAAW,CAAC,sDAAsD,EAChE,WACI,2BACA,cACA,4BACA,6BACJ;sCAED,yBACC,8OAAC,8KAAA,CAAA,OAAI;gCAAC,MAAK;;;;;2EACT,cACF,MAEA,KAAK,IAAI,IAAI,QAAQ;;;;;;sCAIzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAW,CAAC,YAAY,EACtB,WACI,kBACA,cACA,mBACA,iBACJ;8CAED,KAAK,KAAK;;;;;;gCAEZ,KAAK,WAAW,kBACf,8OAAC;oCAAI,WAAU;8CACZ,KAAK,WAAW;;;;;;;;;;;;;mBAzClB;;;;;YA+CX;;;;;;;;;;;AAIR;AAGO,MAAM,sBAAuD,CAAC,EAAE,QAAQ,EAAE;IAC/E,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;QAC5B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;QACzB;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC;;;;;QACT;KACD;IAED,qBACE,8OAAC,8KAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;kCAAG;;;;;;oBAChB,0BACC,8OAAC;wBAAK,MAAK;kCACR;;;;;;;;;;;;0BAIP,8OAAC;gBAAY,OAAO;gBAAO,aAAa;;;;;;;;;;;;AAG9C;AAEO,MAAM,mBAAwD,CAAC,EAAE,YAAY,EAAE;IACpF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAuC;;;;;;;;;;;0BAExD,8OAAC;gBAAM,OAAO;0BAAG;;;;;;0BACjB,8OAAC;gBAAK,MAAK;0BACR,eAAe,CAAC,WAAW,EAAE,aAAa,WAAW,GAAG,GAAG,CAAC,GAAG;;;;;;0BAElE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAe,MAAK;;;;;;;;;;;;;;;;;AAI7B;AAEO,MAAM,eAA6C,CAAC,EAAE,KAAK,EAAE;IAClE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sNAAA,CAAA,iBAAc;oBAAC,WAAU;;;;;;;;;;;0BAE5B,8OAAC;gBAAM,OAAO;0BAAG;;;;;;YAChB,uBACC,8OAAC;gBAAK,MAAK;;oBAAY;oBACP;oBAAM;;;;;;;0BAGxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;;;;;;;;;;;;;;;AAIT;AAGO,MAAM,oBAA8B;IACzC,qBACE,8OAAC,8KAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,sLAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,QAAQ;oBAAE,MAAM;oBAAS,OAAO;gBAAS;gBACzC,WAAW;oBAAE,MAAM;gBAAE;gBACrB,OAAO;oBAAE,OAAO;gBAAM;;;;;;0BAExB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sLAAA,CAAA,WAAQ,CAAC,MAAM;wBAAC,MAAK;;;;;;kCACtB,8OAAC,sLAAA,CAAA,WAAQ,CAAC,MAAM;wBAAC,MAAK;;;;;;kCACtB,8OAAC,sLAAA,CAAA,WAAQ,CAAC,MAAM;wBAAC,MAAK;;;;;;;;;;;;;;;;;;AAI9B;AAEO,MAAM,sBAAgC;IAC3C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sLAAA,CAAA,WAAQ,CAAC,MAAM;gBAAC,MAAK;;;;;;0BACtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sLAAA,CAAA,WAAQ;oBACP,MAAM;oBACN,WAAW;wBAAE,MAAM;wBAAG,OAAO;4BAAC;4BAAO;yBAAM;oBAAC;oBAC5C,OAAO;;;;;;;;;;;;;;;;;AAKjB;AAEO,MAAM,sBAAgC;IAC3C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sLAAA,CAAA,WAAQ,CAAC,KAAK;wBAAC,OAAO;4BAAE,OAAO;4BAAK,QAAQ;wBAAG;wBAAG,MAAM;;;;;;kCACzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sLAAA,CAAA,WAAQ,CAAC,KAAK;4BAAC,OAAO;gCAAE,OAAO;gCAAQ,QAAQ;4BAAG;4BAAG,MAAM;;;;;;;;;;;;;;;;;0BAKhE,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC,uBAAuB;;;;;;;;;;;;;;;;AAKlC;AAEO,MAAM,qBAA+B;IAC1C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sLAAA,CAAA,WAAQ,CAAC,KAAK;4BAAC,OAAO;gCAAE,OAAO;gCAAK,QAAQ;4BAAG;4BAAG,MAAM;;;;;;;;;;;kCAE3D,8OAAC,8KAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,sLAAA,CAAA,WAAQ;4BACP,MAAM;4BACN,WAAW;gCAAE,MAAM;4BAAG;4BACtB,OAAO;gCAAE,OAAO;4BAAM;;;;;;;;;;;;;;;;;0BAM5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sLAAA,CAAA,WAAQ,CAAC,KAAK;4BAAC,OAAO;gCAAE,OAAO;gCAAQ,QAAQ;4BAAG;4BAAG,MAAM;;;;;;wBAC3D,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC,yBAAyB;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAGO,MAAM,iBAIR,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE;IAClC,qBACE,8OAAC,8KAAA,CAAA,OAAI;QAAC,UAAU;QAAS,KAAK;kBAC3B;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 2021, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/library/PaperCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Card, Typography, Tag, Button, Space, Dropdown, Progress, Tooltip, Avatar } from 'antd';\nimport {\n  FileTextOutlined,\n  UserOutlined,\n  CalendarOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  MoreOutlined,\n  StarOutlined,\n  StarFilled,\n  DownloadOutlined,\n  ShareAltOutlined,\n} from '@ant-design/icons';\nimport { Paper } from '@/lib/types';\nimport { useAppStore } from '@/store/useAppStore';\n\nconst { Title, Text, Paragraph } = Typography;\n\ninterface PaperCardProps {\n  paper: Paper;\n  onView: (paper: Paper) => void;\n  onEdit?: (paper: Paper) => void;\n  onDelete?: (paper: Paper) => void;\n  className?: string;\n}\n\nconst PaperCard: React.FC<PaperCardProps> = ({\n  paper,\n  onView,\n  onEdit,\n  onDelete,\n  className = '',\n}) => {\n  const [isFavorite, setIsFavorite] = useState(false);\n  const { readingSessions } = useAppStore();\n\n  // Get reading progress for this paper\n  const paperSessions = readingSessions.filter(s => s.paperId === paper.id);\n  const latestSession = paperSessions[paperSessions.length - 1];\n  const readingProgress = latestSession?.progress || 0;\n\n  const handleFavoriteToggle = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setIsFavorite(!isFavorite);\n  };\n\n  const handleDownload = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    // In a real implementation, you would download the PDF\n    const link = document.createElement('a');\n    link.href = paper.filePath;\n    link.download = `${paper.title}.pdf`;\n    link.click();\n  };\n\n  const handleShare = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    // In a real implementation, you would implement sharing functionality\n    navigator.clipboard.writeText(`Check out this paper: ${paper.title}`);\n  };\n\n  const menuItems = [\n    {\n      key: 'view',\n      label: 'View Paper',\n      icon: <EyeOutlined />,\n      onClick: () => onView(paper),\n    },\n    {\n      key: 'edit',\n      label: 'Edit Details',\n      icon: <EditOutlined />,\n      onClick: () => onEdit?.(paper),\n    },\n    {\n      key: 'download',\n      label: 'Download PDF',\n      icon: <DownloadOutlined />,\n      onClick: handleDownload,\n    },\n    {\n      key: 'share',\n      label: 'Share',\n      icon: <ShareAltOutlined />,\n      onClick: handleShare,\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'delete',\n      label: 'Delete',\n      icon: <DeleteOutlined />,\n      danger: true,\n      onClick: () => onDelete?.(paper),\n    },\n  ];\n\n  const formatDate = (date: Date) => {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n    });\n  };\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <Card\n      hoverable\n      className={`paper-card hover-lift ${className}`}\n      onClick={() => onView(paper)}\n      actions={[\n        <Button \n          type=\"text\" \n          icon={<EyeOutlined />}\n          onClick={(e) => {\n            e.stopPropagation();\n            onView(paper);\n          }}\n          key=\"view\"\n        >\n          View\n        </Button>,\n        <Tooltip title={isFavorite ? 'Remove from favorites' : 'Add to favorites'} key=\"favorite\">\n          <Button\n            type=\"text\"\n            icon={isFavorite ? <StarFilled className=\"text-yellow-500\" /> : <StarOutlined />}\n            onClick={handleFavoriteToggle}\n          />\n        </Tooltip>,\n        <Dropdown \n          menu={{ items: menuItems }}\n          trigger={['click']}\n          key=\"more\"\n        >\n          <Button \n            type=\"text\" \n            icon={<MoreOutlined />}\n            onClick={(e) => e.stopPropagation()}\n          />\n        </Dropdown>,\n      ]}\n    >\n      <div className=\"flex flex-col h-full\">\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-3\">\n          <div className=\"flex items-center space-x-2\">\n            <FileTextOutlined className=\"text-blue-500 text-xl\" />\n            {readingProgress > 0 && (\n              <Progress\n                type=\"circle\"\n                size={20}\n                percent={readingProgress}\n                showInfo={false}\n                strokeColor=\"#52c41a\"\n              />\n            )}\n          </div>\n          <Text type=\"secondary\" className=\"text-xs\">\n            {formatDate(paper.uploadedAt)}\n          </Text>\n        </div>\n        \n        {/* Title */}\n        <Title level={5} className=\"mb-2 line-clamp-2 min-h-[2.5rem]\">\n          {paper.title}\n        </Title>\n        \n        {/* Authors */}\n        <div className=\"flex items-center mb-3\">\n          <div className=\"flex -space-x-1 mr-2\">\n            {paper.authors.slice(0, 3).map((author, index) => (\n              <Tooltip title={author} key={index}>\n                <Avatar \n                  size=\"small\" \n                  className=\"border-2 border-white bg-blue-500\"\n                >\n                  {getInitials(author)}\n                </Avatar>\n              </Tooltip>\n            ))}\n            {paper.authors.length > 3 && (\n              <Avatar size=\"small\" className=\"border-2 border-white bg-gray-400\">\n                +{paper.authors.length - 3}\n              </Avatar>\n            )}\n          </div>\n          <Text className=\"text-sm text-gray-600 truncate flex-1\">\n            {paper.authors.slice(0, 2).join(', ')}\n            {paper.authors.length > 2 && ` +${paper.authors.length - 2} more`}\n          </Text>\n        </div>\n        \n        {/* Abstract */}\n        <Paragraph \n          className=\"text-sm text-gray-600 flex-1 mb-3\"\n          ellipsis={{ rows: 3, tooltip: paper.abstract }}\n        >\n          {paper.abstract}\n        </Paragraph>\n        \n        {/* Reading Progress */}\n        {readingProgress > 0 && (\n          <div className=\"mb-3\">\n            <div className=\"flex items-center justify-between mb-1\">\n              <Text className=\"text-xs text-gray-500\">Reading Progress</Text>\n              <Text className=\"text-xs text-gray-500\">{readingProgress}%</Text>\n            </div>\n            <Progress \n              percent={readingProgress} \n              size=\"small\" \n              showInfo={false}\n              strokeColor=\"#1890ff\"\n            />\n          </div>\n        )}\n        \n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-1\">\n          {paper.tags.slice(0, 3).map((tag) => (\n            <Tag \n              key={tag} \n              size=\"small\" \n              color=\"blue\"\n              className=\"cursor-pointer hover:bg-blue-100\"\n              onClick={(e) => {\n                e.stopPropagation();\n                // In a real implementation, you would filter by this tag\n              }}\n            >\n              {tag}\n            </Tag>\n          ))}\n          {paper.tags.length > 3 && (\n            <Tag size=\"small\" className=\"cursor-pointer\">\n              +{paper.tags.length - 3}\n            </Tag>\n          )}\n        </div>\n\n        {/* Footer Info */}\n        <div className=\"flex items-center justify-between mt-3 pt-2 border-t border-gray-100\">\n          <div className=\"flex items-center space-x-3 text-xs text-gray-500\">\n            <span className=\"flex items-center\">\n              <CalendarOutlined className=\"mr-1\" />\n              {formatDate(paper.lastAccessedAt)}\n            </span>\n          </div>\n          \n          {paperSessions.length > 0 && (\n            <Text className=\"text-xs text-gray-500\">\n              {paperSessions.length} session{paperSessions.length > 1 ? 's' : ''}\n            </Text>\n          )}\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default PaperCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAlBA;;;;;;AAoBA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAU7C,MAAM,YAAsC,CAAC,EAC3C,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,YAAY,EAAE,EACf;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAEtC,sCAAsC;IACtC,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,MAAM,EAAE;IACxE,MAAM,gBAAgB,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;IAC7D,MAAM,kBAAkB,eAAe,YAAY;IAEnD,MAAM,uBAAuB,CAAC;QAC5B,EAAE,eAAe;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,eAAe;QACjB,uDAAuD;QACvD,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,MAAM,QAAQ;QAC1B,KAAK,QAAQ,GAAG,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC;QACpC,KAAK,KAAK;IACZ;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,eAAe;QACjB,sEAAsE;QACtE,UAAU,SAAS,CAAC,SAAS,CAAC,CAAC,sBAAsB,EAAE,MAAM,KAAK,EAAE;IACtE;IAEA,MAAM,YAAY;QAChB;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;YAClB,SAAS,IAAM,OAAO;QACxB;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,SAAS,IAAM,SAAS;QAC1B;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,SAAS;QACX;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,SAAS;QACX;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,QAAQ;YACR,SAAS,IAAM,WAAW;QAC5B;KACD;IAED,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAChD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,8OAAC,8KAAA,CAAA,OAAI;QACH,SAAS;QACT,WAAW,CAAC,sBAAsB,EAAE,WAAW;QAC/C,SAAS,IAAM,OAAO;QACtB,SAAS;0BACP,8OAAC,kMAAA,CAAA,SAAM;gBACL,MAAK;gBACL,oBAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;gBAClB,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,OAAO;gBACT;0BAED;eADK;;;;;0BAIN,8OAAC,oLAAA,CAAA,UAAO;gBAAC,OAAO,aAAa,0BAA0B;0BACrD,cAAA,8OAAC,kMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,MAAM,2BAAa,8OAAC,8MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;+CAAuB,8OAAC,kNAAA,CAAA,eAAY;;;;;oBAC7E,SAAS;;;;;;eAJkE;;;;;0BAO/E,8OAAC,sLAAA,CAAA,WAAQ;gBACP,MAAM;oBAAE,OAAO;gBAAU;gBACzB,SAAS;oBAAC;iBAAQ;0BAGlB,cAAA,8OAAC,kMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;eAL/B;;;;;SAQP;kBAED,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0NAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;gCAC3B,kBAAkB,mBACjB,8OAAC,sLAAA,CAAA,WAAQ;oCACP,MAAK;oCACL,MAAM;oCACN,SAAS;oCACT,UAAU;oCACV,aAAY;;;;;;;;;;;;sCAIlB,8OAAC;4BAAK,MAAK;4BAAY,WAAU;sCAC9B,WAAW,MAAM,UAAU;;;;;;;;;;;;8BAKhC,8OAAC;oBAAM,OAAO;oBAAG,WAAU;8BACxB,MAAM,KAAK;;;;;;8BAId,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACtC,8OAAC,oLAAA,CAAA,UAAO;wCAAC,OAAO;kDACd,cAAA,8OAAC,kLAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;sDAET,YAAY;;;;;;uCALY;;;;;gCAS9B,MAAM,OAAO,CAAC,MAAM,GAAG,mBACtB,8OAAC,kLAAA,CAAA,SAAM;oCAAC,MAAK;oCAAQ,WAAU;;wCAAoC;wCAC/D,MAAM,OAAO,CAAC,MAAM,GAAG;;;;;;;;;;;;;sCAI/B,8OAAC;4BAAK,WAAU;;gCACb,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;gCAC/B,MAAM,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC;;;;;;;;;;;;;8BAKrE,8OAAC;oBACC,WAAU;oBACV,UAAU;wBAAE,MAAM;wBAAG,SAAS,MAAM,QAAQ;oBAAC;8BAE5C,MAAM,QAAQ;;;;;;gBAIhB,kBAAkB,mBACjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAK,WAAU;;wCAAyB;wCAAgB;;;;;;;;;;;;;sCAE3D,8OAAC,sLAAA,CAAA,WAAQ;4BACP,SAAS;4BACT,MAAK;4BACL,UAAU;4BACV,aAAY;;;;;;;;;;;;8BAMlB,8OAAC;oBAAI,WAAU;;wBACZ,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC3B,8OAAC,4KAAA,CAAA,MAAG;gCAEF,MAAK;gCACL,OAAM;gCACN,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;gCACjB,yDAAyD;gCAC3D;0CAEC;+BATI;;;;;wBAYR,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,MAAK;4BAAQ,WAAU;;gCAAiB;gCACzC,MAAM,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8BAM5B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,0NAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;oCAC3B,WAAW,MAAM,cAAc;;;;;;;;;;;;wBAInC,cAAc,MAAM,GAAG,mBACtB,8OAAC;4BAAK,WAAU;;gCACb,cAAc,MAAM;gCAAC;gCAAS,cAAc,MAAM,GAAG,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAO9E;uCAEe", "debugId": null}}, {"offset": {"line": 2486, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/hooks/usePapers.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { Paper, ApiResponse } from '@/lib/types';\nimport { API_ENDPOINTS, ERROR_MESSAGES } from '@/lib/constants';\nimport { useAppStore } from '@/store/useAppStore';\n\ninterface UsePapersReturn {\n  papers: Paper[];\n  loading: boolean;\n  error: string | null;\n  fetchPapers: () => Promise<void>;\n  fetchPaper: (id: string) => Promise<Paper | null>;\n  updatePaper: (id: string, updates: Partial<Paper>) => Promise<boolean>;\n  deletePaper: (id: string) => Promise<boolean>;\n  searchPapers: (query: string, filters?: any[]) => Promise<Paper[]>;\n  refreshPapers: () => Promise<void>;\n}\n\nexport const usePapers = (): UsePapersReturn => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  \n  const { \n    papers, \n    setPapers, \n    addPaper, \n    removePaper,\n    setError: setGlobalError \n  } = useAppStore();\n\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  const fetchPapers = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch(API_ENDPOINTS.papers);\n      const result: ApiResponse<{ papers: Paper[] }> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      if (result.data) {\n        setPapers(result.data.papers);\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [setPapers, setGlobalError]);\n\n  const fetchPaper = useCallback(async (id: string): Promise<Paper | null> => {\n    try {\n      setError(null);\n\n      const response = await fetch(`${API_ENDPOINTS.papers}?id=${id}`);\n      const result: ApiResponse<Paper> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      return result.data || null;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      return null;\n    }\n  }, []);\n\n  const updatePaper = useCallback(async (id: string, updates: Partial<Paper>): Promise<boolean> => {\n    try {\n      setError(null);\n\n      const existingPaper = papers.find(p => p.id === id);\n      if (!existingPaper) {\n        throw new Error('Paper not found');\n      }\n\n      const updatedPaper = { ...existingPaper, ...updates };\n\n      const response = await fetch(API_ENDPOINTS.papers, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(updatedPaper),\n      });\n\n      const result: ApiResponse<Paper> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      // Update local state\n      setPapers(papers.map(p => p.id === id ? updatedPaper : p));\n      \n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      return false;\n    }\n  }, [papers, setPapers, setGlobalError]);\n\n  const deletePaper = useCallback(async (id: string): Promise<boolean> => {\n    try {\n      setError(null);\n\n      const response = await fetch(`${API_ENDPOINTS.papers}?id=${id}`, {\n        method: 'DELETE',\n      });\n\n      const result: ApiResponse<Paper> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      // Update local state\n      removePaper(id);\n      \n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      return false;\n    }\n  }, [removePaper, setGlobalError]);\n\n  const searchPapers = useCallback(async (query: string, filters: any[] = []): Promise<Paper[]> => {\n    try {\n      setError(null);\n\n      const searchParams = new URLSearchParams({\n        q: query,\n        filters: JSON.stringify(filters),\n      });\n\n      const response = await fetch(`${API_ENDPOINTS.search}?${searchParams}`);\n      const result = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      // Convert search results back to papers\n      const searchResults = result.data.results;\n      const foundPapers = searchResults\n        .map((searchResult: any) => papers.find(p => p.id === searchResult.paperId))\n        .filter(Boolean);\n\n      return foundPapers;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      return [];\n    }\n  }, [papers]);\n\n  const refreshPapers = useCallback(async () => {\n    await fetchPapers();\n  }, [fetchPapers]);\n\n  // Load papers on mount\n  useEffect(() => {\n    if (papers.length === 0) {\n      fetchPapers();\n    }\n  }, [fetchPapers, papers.length]);\n\n  return {\n    papers,\n    loading,\n    error,\n    fetchPapers,\n    fetchPaper,\n    updatePaper,\n    deletePaper,\n    searchPapers,\n    refreshPapers,\n  };\n};\n\n// Hook for managing paper state\nexport const usePaperState = (paperId: string | null) => {\n  const { papers, currentPaper, setCurrentPaper } = useAppStore();\n  const [paper, setPaper] = useState<Paper | null>(null);\n\n  useEffect(() => {\n    if (paperId) {\n      const foundPaper = papers.find(p => p.id === paperId);\n      setPaper(foundPaper || null);\n      \n      if (foundPaper && currentPaper?.id !== paperId) {\n        setCurrentPaper(paperId);\n      }\n    } else {\n      setPaper(null);\n    }\n  }, [paperId, papers, currentPaper, setCurrentPaper]);\n\n  return paper;\n};\n\n// Hook for paper statistics\nexport const usePaperStats = () => {\n  const { papers, readingSessions, annotations } = useAppStore();\n\n  const stats = {\n    totalPapers: papers.length,\n    totalSessions: readingSessions.length,\n    totalAnnotations: annotations.length,\n    averageReadingProgress: readingSessions.length > 0 \n      ? readingSessions.reduce((sum, session) => sum + session.progress, 0) / readingSessions.length\n      : 0,\n    papersWithAnnotations: new Set(annotations.map(a => a.paperId)).size,\n    mostActiveDay: getMostActiveDay(readingSessions),\n    topAuthors: <AUTHORS>\n    topTags: getTopTags(papers),\n  };\n\n  return stats;\n};\n\nfunction getMostActiveDay(sessions: any[]): string {\n  const dayCount: { [key: string]: number } = {};\n  \n  sessions.forEach(session => {\n    const day = new Date(session.startTime).toLocaleDateString('en-US', { weekday: 'long' });\n    dayCount[day] = (dayCount[day] || 0) + 1;\n  });\n\n  return Object.entries(dayCount).reduce((a, b) => dayCount[a[0]] > dayCount[b[0]] ? a : b)?.[0] || 'No data';\n}\n\nfunction getTopAuthors(papers: Paper[]): Array<{ name: string; count: number }> {\n  const authorCount: { [key: string]: number } = {};\n  \n  papers.forEach(paper => {\n    paper.authors.forEach(author => {\n      authorCount[author] = (authorCount[author] || 0) + 1;\n    });\n  });\n\n  return Object.entries(authorCount)\n    .map(([name, count]) => ({ name, count }))\n    .sort((a, b) => b.count - a.count)\n    .slice(0, 5);\n}\n\nfunction getTopTags(papers: Paper[]): Array<{ tag: string; count: number }> {\n  const tagCount: { [key: string]: number } = {};\n  \n  papers.forEach(paper => {\n    paper.tags.forEach(tag => {\n      tagCount[tag] = (tagCount[tag] || 0) + 1;\n    });\n  });\n\n  return Object.entries(tagCount)\n    .map(([tag, count]) => ({ tag, count }))\n    .sort((a, b) => b.count - a.count)\n    .slice(0, 10);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;;;;AAcO,MAAM,YAAY;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EACJ,MAAM,EACN,SAAS,EACT,QAAQ,EACR,WAAW,EACX,UAAU,cAAc,EACzB,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;IACX,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,uHAAA,CAAA,gBAAa,CAAC,MAAM;YACjD,MAAM,SAA2C,MAAM,SAAS,IAAI;YAEpE,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC1D;YAEA,IAAI,OAAO,IAAI,EAAE;gBACf,UAAU,OAAO,IAAI,CAAC,MAAM;YAC9B;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAClF,SAAS;YACT,eAAe;QACjB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAW;KAAe;IAE9B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI;YACF,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,GAAG,uHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI;YAC/D,MAAM,SAA6B,MAAM,SAAS,IAAI;YAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC1D;YAEA,OAAO,OAAO,IAAI,IAAI;QACxB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAClF,SAAS;YACT,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QACjD,IAAI;YACF,SAAS;YAET,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAChD,IAAI,CAAC,eAAe;gBAClB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,eAAe;gBAAE,GAAG,aAAa;gBAAE,GAAG,OAAO;YAAC;YAEpD,MAAM,WAAW,MAAM,MAAM,uHAAA,CAAA,gBAAa,CAAC,MAAM,EAAE;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;YAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC1D;YAEA,qBAAqB;YACrB,UAAU,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,eAAe;YAEvD,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAClF,SAAS;YACT,eAAe;YACf,OAAO;QACT;IACF,GAAG;QAAC;QAAQ;QAAW;KAAe;IAEtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,GAAG,uHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE;gBAC/D,QAAQ;YACV;YAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;YAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC1D;YAEA,qBAAqB;YACrB,YAAY;YAEZ,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAClF,SAAS;YACT,eAAe;YACf,OAAO;QACT;IACF,GAAG;QAAC;QAAa;KAAe;IAEhC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,OAAe,UAAiB,EAAE;QACxE,IAAI;YACF,SAAS;YAET,MAAM,eAAe,IAAI,gBAAgB;gBACvC,GAAG;gBACH,SAAS,KAAK,SAAS,CAAC;YAC1B;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,uHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc;YACtE,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC1D;YAEA,wCAAwC;YACxC,MAAM,gBAAgB,OAAO,IAAI,CAAC,OAAO;YACzC,MAAM,cAAc,cACjB,GAAG,CAAC,CAAC,eAAsB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,OAAO,GACzE,MAAM,CAAC;YAEV,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAClF,SAAS;YACT,OAAO,EAAE;QACX;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,MAAM;IACR,GAAG;QAAC;KAAY;IAEhB,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB;QACF;IACF,GAAG;QAAC;QAAa,OAAO,MAAM;KAAC;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC5D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,MAAM,aAAa,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC7C,SAAS,cAAc;YAEvB,IAAI,cAAc,cAAc,OAAO,SAAS;gBAC9C,gBAAgB;YAClB;QACF,OAAO;YACL,SAAS;QACX;IACF,GAAG;QAAC;QAAS;QAAQ;QAAc;KAAgB;IAEnD,OAAO;AACT;AAGO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE3D,MAAM,QAAQ;QACZ,aAAa,OAAO,MAAM;QAC1B,eAAe,gBAAgB,MAAM;QACrC,kBAAkB,YAAY,MAAM;QACpC,wBAAwB,gBAAgB,MAAM,GAAG,IAC7C,gBAAgB,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,QAAQ,EAAE,KAAK,gBAAgB,MAAM,GAC5F;QACJ,uBAAuB,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,GAAG,IAAI;QACpE,eAAe,iBAAiB;QAChC,YAAY,cAAc;QAC1B,SAAS,WAAW;IACtB;IAEA,OAAO;AACT;AAEA,SAAS,iBAAiB,QAAe;IACvC,MAAM,WAAsC,CAAC;IAE7C,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,MAAM,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC,SAAS;YAAE,SAAS;QAAO;QACtF,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI;IACzC;IAEA,OAAO,OAAO,OAAO,CAAC,UAAU,MAAM,CAAC,CAAC,GAAG,IAAM,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,IAAI;AACpG;AAEA,SAAS,cAAc,MAAe;IACpC,MAAM,cAAyC,CAAC;IAEhD,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,OAAO,CAAC,OAAO,CAAC,CAAA;YACpB,WAAW,CAAC,OAAO,GAAG,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,IAAI;QACrD;IACF;IAEA,OAAO,OAAO,OAAO,CAAC,aACnB,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;YAAE;YAAM;QAAM,CAAC,GACvC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG;AACd;AAEA,SAAS,WAAW,MAAe;IACjC,MAAM,WAAsC,CAAC;IAE7C,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,IAAI,CAAC,OAAO,CAAC,CAAA;YACjB,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAI;QACzC;IACF;IAEA,OAAO,OAAO,OAAO,CAAC,UACnB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,CAAC;YAAE;YAAK;QAAM,CAAC,GACrC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 2724, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/views/LibraryView.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Input, Button, Empty, Typography, Dropdown, Pagination } from 'antd';\nimport {\n  SearchOutlined,\n  PlusOutlined,\n  FilterOutlined,\n  SortAscendingOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store/useAppStore';\nimport { Paper } from '@/lib/types';\nimport FileUpload from '@/components/upload/FileUpload';\nimport { LibraryViewSkeleton } from '@/components/common/LoadingStates';\nimport PaperCard from '@/components/library/PaperCard';\nimport { VIEW_MODES } from '@/lib/constants';\nimport { usePapers } from '@/hooks/usePapers';\n\nconst { Search } = Input;\nconst { Title } = Typography;\n\nconst LibraryView: React.FC = () => {\n  const {\n    setCurrentPaper,\n    setCurrentView,\n    isLoading,\n  } = useAppStore();\n\n  const { papers, deletePaper } = usePapers();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredPapers, setFilteredPapers] = useState<Paper[]>(papers);\n  const [showUpload, setShowUpload] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize] = useState(12);\n\n  useEffect(() => {\n    // Filter papers based on search term\n    if (searchTerm) {\n      const filtered = papers.filter(paper =>\n        paper.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        paper.authors.some(author => author.toLowerCase().includes(searchTerm.toLowerCase())) ||\n        paper.abstract.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        paper.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n      setFilteredPapers(filtered);\n    } else {\n      setFilteredPapers(papers);\n    }\n    setCurrentPage(1);\n  }, [searchTerm, papers]);\n\n  const handlePaperClick = (paper: Paper) => {\n    setCurrentPaper(paper.id);\n    setCurrentView(VIEW_MODES.READER);\n  };\n\n  const handleUploadSuccess = (paperId: string) => {\n    setShowUpload(false);\n    // Optionally navigate to the uploaded paper\n    setCurrentPaper(paperId);\n    setCurrentView(VIEW_MODES.READER);\n  };\n\n  const handleDeletePaper = async (paper: Paper) => {\n    await deletePaper(paper.id);\n  };\n\n\n\n  const sortMenuItems = [\n    {\n      key: 'title',\n      label: 'Sort by Title',\n    },\n    {\n      key: 'date',\n      label: 'Sort by Date',\n    },\n    {\n      key: 'author',\n      label: 'Sort by Author',\n    },\n  ];\n\n  const filterMenuItems = [\n    {\n      key: 'all',\n      label: 'All Papers',\n    },\n    {\n      key: 'recent',\n      label: 'Recently Added',\n    },\n    {\n      key: 'favorites',\n      label: 'Favorites',\n    },\n  ];\n\n  // Pagination\n  const startIndex = (currentPage - 1) * pageSize;\n  const endIndex = startIndex + pageSize;\n  const paginatedPapers = filteredPapers.slice(startIndex, endIndex);\n\n  if (isLoading) {\n    return <LibraryViewSkeleton />;\n  }\n\n  return (\n    <div className=\"p-6 h-full overflow-auto\">\n      {/* Header */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <Title level={2} className=\"m-0\">\n            Paper Library\n          </Title>\n          <Button \n            type=\"primary\" \n            icon={<PlusOutlined />}\n            onClick={() => setShowUpload(!showUpload)}\n            size=\"large\"\n          >\n            Add Paper\n          </Button>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"flex items-center space-x-4 mb-4\">\n          <Search\n            placeholder=\"Search papers, authors, or keywords...\"\n            allowClear\n            size=\"large\"\n            className=\"flex-1\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            prefix={<SearchOutlined />}\n          />\n          \n          <Dropdown menu={{ items: sortMenuItems }} placement=\"bottomRight\">\n            <Button icon={<SortAscendingOutlined />} size=\"large\">\n              Sort\n            </Button>\n          </Dropdown>\n          \n          <Dropdown menu={{ items: filterMenuItems }} placement=\"bottomRight\">\n            <Button icon={<FilterOutlined />} size=\"large\">\n              Filter\n            </Button>\n          </Dropdown>\n        </div>\n\n        {/* Stats */}\n        <div className=\"flex items-center space-x-6 text-gray-600\">\n          <span>{filteredPapers.length} papers</span>\n          {searchTerm && (\n            <span>Filtered from {papers.length} total</span>\n          )}\n        </div>\n      </div>\n\n      {/* Upload Section */}\n      {showUpload && (\n        <Card className=\"mb-6\">\n          <FileUpload onUploadSuccess={handleUploadSuccess} />\n        </Card>\n      )}\n\n      {/* Papers Grid */}\n      {paginatedPapers.length === 0 ? (\n        <Empty\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\n          description={\n            searchTerm ? 'No papers found matching your search' : 'No papers in your library yet'\n          }\n        >\n          {!searchTerm && (\n            <Button \n              type=\"primary\" \n              icon={<PlusOutlined />}\n              onClick={() => setShowUpload(true)}\n            >\n              Add Your First Paper\n            </Button>\n          )}\n        </Empty>\n      ) : (\n        <>\n          <Row gutter={[16, 16]}>\n            {paginatedPapers.map((paper) => (\n              <Col xs={24} sm={12} lg={8} xl={6} key={paper.id}>\n                <PaperCard\n                  paper={paper}\n                  onView={handlePaperClick}\n                  onDelete={handleDeletePaper}\n                  className=\"h-full\"\n                />\n              </Col>\n            ))}\n          </Row>\n\n          {/* Pagination */}\n          {filteredPapers.length > pageSize && (\n            <div className=\"flex justify-center mt-8\">\n              <Pagination\n                current={currentPage}\n                total={filteredPapers.length}\n                pageSize={pageSize}\n                onChange={setCurrentPage}\n                showSizeChanger={false}\n                showQuickJumper\n                showTotal={(total, range) => \n                  `${range[0]}-${range[1]} of ${total} papers`\n                }\n              />\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default LibraryView;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAMA;AAEA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;AAkBA,MAAM,EAAE,MAAM,EAAE,GAAG,gLAAA,CAAA,QAAK;AACxB,MAAM,EAAE,KAAK,EAAE,GAAG,0LAAA,CAAA,aAAU;AAE5B,MAAM,cAAwB;IAC5B,MAAM,EACJ,eAAe,EACf,cAAc,EACd,SAAS,EACV,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IACxC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,IAAI,YAAY;YACd,MAAM,WAAW,OAAO,MAAM,CAAC,CAAA,QAC7B,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,QACjF,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAE1E,kBAAkB;QACpB,OAAO;YACL,kBAAkB;QACpB;QACA,eAAe;IACjB,GAAG;QAAC;QAAY;KAAO;IAEvB,MAAM,mBAAmB,CAAC;QACxB,gBAAgB,MAAM,EAAE;QACxB,eAAe,uHAAA,CAAA,aAAU,CAAC,MAAM;IAClC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,4CAA4C;QAC5C,gBAAgB;QAChB,eAAe,uHAAA,CAAA,aAAU,CAAC,MAAM;IAClC;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,YAAY,MAAM,EAAE;IAC5B;IAIA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;QACA;YACE,KAAK;YACL,OAAO;QACT;KACD;IAED,aAAa;IACb,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAC9B,MAAM,kBAAkB,eAAe,KAAK,CAAC,YAAY;IAEzD,IAAI,WAAW;QACb,qBAAO,8OAAC,6IAAA,CAAA,sBAAmB;;;;;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,OAAO;gCAAG,WAAU;0CAAM;;;;;;0CAGjC,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACnB,SAAS,IAAM,cAAc,CAAC;gCAC9B,MAAK;0CACN;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,aAAY;gCACZ,UAAU;gCACV,MAAK;gCACL,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;;;;;;0CAGzB,8OAAC,sLAAA,CAAA,WAAQ;gCAAC,MAAM;oCAAE,OAAO;gCAAc;gCAAG,WAAU;0CAClD,cAAA,8OAAC,kMAAA,CAAA,SAAM;oCAAC,oBAAM,8OAAC,oOAAA,CAAA,wBAAqB;;;;;oCAAK,MAAK;8CAAQ;;;;;;;;;;;0CAKxD,8OAAC,sLAAA,CAAA,WAAQ;gCAAC,MAAM;oCAAE,OAAO;gCAAgB;gCAAG,WAAU;0CACpD,cAAA,8OAAC,kMAAA,CAAA,SAAM;oCAAC,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;oCAAK,MAAK;8CAAQ;;;;;;;;;;;;;;;;;kCAOnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAM,eAAe,MAAM;oCAAC;;;;;;;4BAC5B,4BACC,8OAAC;;oCAAK;oCAAe,OAAO,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;YAMxC,4BACC,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,0IAAA,CAAA,UAAU;oBAAC,iBAAiB;;;;;;;;;;;YAKhC,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC,gLAAA,CAAA,QAAK;gBACJ,OAAO,gLAAA,CAAA,QAAK,CAAC,sBAAsB;gBACnC,aACE,aAAa,yCAAyC;0BAGvD,CAAC,4BACA,8OAAC,kMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,SAAS,IAAM,cAAc;8BAC9B;;;;;;;;;;yEAML;;kCACE,8OAAC,4KAAA,CAAA,MAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;kCAClB,gBAAgB,GAAG,CAAC,CAAC,sBACpB,8OAAC,4KAAA,CAAA,MAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CAC9B,cAAA,8OAAC,0IAAA,CAAA,UAAS;oCACR,OAAO;oCACP,QAAQ;oCACR,UAAU;oCACV,WAAU;;;;;;+BAL0B,MAAM,EAAE;;;;;;;;;;oBAYnD,eAAe,MAAM,GAAG,0BACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,aAAU;4BACT,SAAS;4BACT,OAAO,eAAe,MAAM;4BAC5B,UAAU;4BACV,UAAU;4BACV,iBAAiB;4BACjB,eAAe;4BACf,WAAW,CAAC,OAAO,QACjB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC;;;;;;;;;;;;;;;;;;;AAS9D;uCAEe", "debugId": null}}, {"offset": {"line": 3090, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/annotation-storage.ts"], "sourcesContent": ["import { Annotation, AnnotationGroup, AnnotationFilter, AnnotationStats } from './annotation-types';\n\nclass AnnotationStorage {\n  private readonly STORAGE_KEY = 'documancer_annotations';\n  private readonly API_BASE = '/api/annotations';\n\n  // Local storage methods\n  private getLocalAnnotations(): Record<string, AnnotationGroup> {\n    if (typeof window === 'undefined') return {};\n    \n    try {\n      const stored = localStorage.getItem(this.STORAGE_KEY);\n      return stored ? JSON.parse(stored) : {};\n    } catch (error) {\n      console.error('Failed to load annotations from localStorage:', error);\n      return {};\n    }\n  }\n\n  private saveLocalAnnotations(annotations: Record<string, AnnotationGroup>): void {\n    if (typeof window === 'undefined') return;\n    \n    try {\n      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(annotations));\n    } catch (error) {\n      console.error('Failed to save annotations to localStorage:', error);\n    }\n  }\n\n  // API methods for server-side persistence\n  private async apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {\n    try {\n      const response = await fetch(`${this.API_BASE}${endpoint}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`API request failed: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error('API request failed:', error);\n      throw error;\n    }\n  }\n\n  // Public methods\n  async getAnnotations(paperId: string): Promise<Annotation[]> {\n    try {\n      // Try to get from server first\n      const response = await this.apiRequest(`/${paperId}`);\n      return response.data || [];\n    } catch (error) {\n      // Fallback to local storage\n      const localAnnotations = this.getLocalAnnotations();\n      return localAnnotations[paperId]?.annotations || [];\n    }\n  }\n\n  async saveAnnotation(annotation: Annotation): Promise<Annotation> {\n    try {\n      // Save to server\n      const response = await this.apiRequest('', {\n        method: 'POST',\n        body: JSON.stringify(annotation),\n      });\n      \n      return response.data;\n    } catch (error) {\n      // Fallback to local storage\n      const localAnnotations = this.getLocalAnnotations();\n      \n      if (!localAnnotations[annotation.paperId]) {\n        localAnnotations[annotation.paperId] = {\n          paperId: annotation.paperId,\n          annotations: [],\n          lastModified: new Date(),\n        };\n      }\n\n      const existingIndex = localAnnotations[annotation.paperId].annotations.findIndex(\n        a => a.id === annotation.id\n      );\n\n      if (existingIndex >= 0) {\n        localAnnotations[annotation.paperId].annotations[existingIndex] = annotation;\n      } else {\n        localAnnotations[annotation.paperId].annotations.push(annotation);\n      }\n\n      localAnnotations[annotation.paperId].lastModified = new Date();\n      this.saveLocalAnnotations(localAnnotations);\n      \n      return annotation;\n    }\n  }\n\n  async updateAnnotation(annotation: Annotation): Promise<Annotation> {\n    annotation.updatedAt = new Date();\n    return this.saveAnnotation(annotation);\n  }\n\n  async deleteAnnotation(paperId: string, annotationId: string): Promise<void> {\n    try {\n      // Delete from server\n      await this.apiRequest(`/${annotationId}`, {\n        method: 'DELETE',\n      });\n    } catch (error) {\n      // Fallback to local storage\n      const localAnnotations = this.getLocalAnnotations();\n      \n      if (localAnnotations[paperId]) {\n        localAnnotations[paperId].annotations = localAnnotations[paperId].annotations.filter(\n          a => a.id !== annotationId\n        );\n        localAnnotations[paperId].lastModified = new Date();\n        this.saveLocalAnnotations(localAnnotations);\n      }\n    }\n  }\n\n  async filterAnnotations(paperId: string, filter: AnnotationFilter): Promise<Annotation[]> {\n    const annotations = await this.getAnnotations(paperId);\n    \n    return annotations.filter(annotation => {\n      if (filter.type && annotation.type !== filter.type) return false;\n      if (filter.pageNumber && annotation.pageNumber !== filter.pageNumber) return false;\n      if (filter.searchText) {\n        const searchLower = filter.searchText.toLowerCase();\n        const contentMatch = annotation.content.toLowerCase().includes(searchLower);\n        const selectionMatch = annotation.selection?.selectedText.toLowerCase().includes(searchLower);\n        if (!contentMatch && !selectionMatch) return false;\n      }\n      if (filter.dateRange) {\n        const createdAt = new Date(annotation.createdAt);\n        if (createdAt < filter.dateRange.start || createdAt > filter.dateRange.end) return false;\n      }\n      return true;\n    });\n  }\n\n  async getAnnotationStats(paperId: string): Promise<AnnotationStats> {\n    const annotations = await this.getAnnotations(paperId);\n    \n    const stats: AnnotationStats = {\n      totalAnnotations: annotations.length,\n      highlightCount: annotations.filter(a => a.type === 'highlight').length,\n      noteCount: annotations.filter(a => a.type === 'note').length,\n      bookmarkCount: annotations.filter(a => a.type === 'bookmark').length,\n      pagesWithAnnotations: [...new Set(annotations.map(a => a.pageNumber))].sort((a, b) => a - b),\n    };\n\n    return stats;\n  }\n\n  // Bulk operations\n  async exportAnnotations(paperId: string): Promise<string> {\n    const annotations = await this.getAnnotations(paperId);\n    return JSON.stringify(annotations, null, 2);\n  }\n\n  async importAnnotations(paperId: string, annotationsJson: string): Promise<void> {\n    try {\n      const annotations: Annotation[] = JSON.parse(annotationsJson);\n      \n      for (const annotation of annotations) {\n        annotation.paperId = paperId; // Ensure correct paper ID\n        await this.saveAnnotation(annotation);\n      }\n    } catch (error) {\n      throw new Error('Invalid annotations format');\n    }\n  }\n\n  // Sync methods\n  async syncWithServer(paperId: string): Promise<void> {\n    try {\n      const localAnnotations = this.getLocalAnnotations();\n      const localGroup = localAnnotations[paperId];\n      \n      if (!localGroup) return;\n\n      // Upload local annotations to server\n      await this.apiRequest('/sync', {\n        method: 'POST',\n        body: JSON.stringify(localGroup),\n      });\n\n      console.log('Annotations synced with server');\n    } catch (error) {\n      console.error('Failed to sync annotations:', error);\n    }\n  }\n}\n\nexport const annotationStorage = new AnnotationStorage();\n"], "names": [], "mappings": ";;;AAEA,MAAM;IACa,cAAc,yBAAyB;IACvC,WAAW,mBAAmB;IAE/C,wBAAwB;IAChB,sBAAuD;QAC7D,wCAAmC,OAAO,CAAC;;;IAS7C;IAEQ,qBAAqB,WAA4C,EAAQ;QAC/E,wCAAmC;;;IAOrC;IAEA,0CAA0C;IAC1C,MAAc,WAAW,QAAgB,EAAE,UAAuB,CAAC,CAAC,EAAgB;QAClF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,UAAU,EAAE;gBAC1D,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,UAAU,EAAE;YAC9D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,MAAM,eAAe,OAAe,EAAyB;QAC3D,IAAI;YACF,+BAA+B;YAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,SAAS;YACpD,OAAO,SAAS,IAAI,IAAI,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,4BAA4B;YAC5B,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;YACjD,OAAO,gBAAgB,CAAC,QAAQ,EAAE,eAAe,EAAE;QACrD;IACF;IAEA,MAAM,eAAe,UAAsB,EAAuB;QAChE,IAAI;YACF,iBAAiB;YACjB,MAAM,WAAW,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI;gBACzC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,4BAA4B;YAC5B,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;YAEjD,IAAI,CAAC,gBAAgB,CAAC,WAAW,OAAO,CAAC,EAAE;gBACzC,gBAAgB,CAAC,WAAW,OAAO,CAAC,GAAG;oBACrC,SAAS,WAAW,OAAO;oBAC3B,aAAa,EAAE;oBACf,cAAc,IAAI;gBACpB;YACF;YAEA,MAAM,gBAAgB,gBAAgB,CAAC,WAAW,OAAO,CAAC,CAAC,WAAW,CAAC,SAAS,CAC9E,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;YAG7B,IAAI,iBAAiB,GAAG;gBACtB,gBAAgB,CAAC,WAAW,OAAO,CAAC,CAAC,WAAW,CAAC,cAAc,GAAG;YACpE,OAAO;gBACL,gBAAgB,CAAC,WAAW,OAAO,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC;YACxD;YAEA,gBAAgB,CAAC,WAAW,OAAO,CAAC,CAAC,YAAY,GAAG,IAAI;YACxD,IAAI,CAAC,oBAAoB,CAAC;YAE1B,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,UAAsB,EAAuB;QAClE,WAAW,SAAS,GAAG,IAAI;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,MAAM,iBAAiB,OAAe,EAAE,YAAoB,EAAiB;QAC3E,IAAI;YACF,qBAAqB;YACrB,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,cAAc,EAAE;gBACxC,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,4BAA4B;YAC5B,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;YAEjD,IAAI,gBAAgB,CAAC,QAAQ,EAAE;gBAC7B,gBAAgB,CAAC,QAAQ,CAAC,WAAW,GAAG,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAClF,CAAA,IAAK,EAAE,EAAE,KAAK;gBAEhB,gBAAgB,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI;gBAC7C,IAAI,CAAC,oBAAoB,CAAC;YAC5B;QACF;IACF;IAEA,MAAM,kBAAkB,OAAe,EAAE,MAAwB,EAAyB;QACxF,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,OAAO,YAAY,MAAM,CAAC,CAAA;YACxB,IAAI,OAAO,IAAI,IAAI,WAAW,IAAI,KAAK,OAAO,IAAI,EAAE,OAAO;YAC3D,IAAI,OAAO,UAAU,IAAI,WAAW,UAAU,KAAK,OAAO,UAAU,EAAE,OAAO;YAC7E,IAAI,OAAO,UAAU,EAAE;gBACrB,MAAM,cAAc,OAAO,UAAU,CAAC,WAAW;gBACjD,MAAM,eAAe,WAAW,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;gBAC/D,MAAM,iBAAiB,WAAW,SAAS,EAAE,aAAa,cAAc,SAAS;gBACjF,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,OAAO;YAC/C;YACA,IAAI,OAAO,SAAS,EAAE;gBACpB,MAAM,YAAY,IAAI,KAAK,WAAW,SAAS;gBAC/C,IAAI,YAAY,OAAO,SAAS,CAAC,KAAK,IAAI,YAAY,OAAO,SAAS,CAAC,GAAG,EAAE,OAAO;YACrF;YACA,OAAO;QACT;IACF;IAEA,MAAM,mBAAmB,OAAe,EAA4B;QAClE,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,MAAM,QAAyB;YAC7B,kBAAkB,YAAY,MAAM;YACpC,gBAAgB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;YACtE,WAAW,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;YAC5D,eAAe,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,MAAM;YACpE,sBAAsB;mBAAI,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;aAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAC5F;QAEA,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,kBAAkB,OAAe,EAAmB;QACxD,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAC9C,OAAO,KAAK,SAAS,CAAC,aAAa,MAAM;IAC3C;IAEA,MAAM,kBAAkB,OAAe,EAAE,eAAuB,EAAiB;QAC/E,IAAI;YACF,MAAM,cAA4B,KAAK,KAAK,CAAC;YAE7C,KAAK,MAAM,cAAc,YAAa;gBACpC,WAAW,OAAO,GAAG,SAAS,0BAA0B;gBACxD,MAAM,IAAI,CAAC,cAAc,CAAC;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,eAAe;IACf,MAAM,eAAe,OAAe,EAAiB;QACnD,IAAI;YACF,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;YACjD,MAAM,aAAa,gBAAgB,CAAC,QAAQ;YAE5C,IAAI,CAAC,YAAY;YAEjB,qCAAqC;YACrC,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS;gBAC7B,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;AACF;AAEO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 3256, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/hooks/useAnnotations.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { Annotation, AnnotationFilter, AnnotationStats, TextSelection, AnnotationColor } from '@/lib/annotation-types';\nimport { annotationStorage } from '@/lib/annotation-storage';\nimport { message } from 'antd';\n\nexport function useAnnotations(paperId: string) {\n  const [annotations, setAnnotations] = useState<Annotation[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedAnnotation, setSelectedAnnotation] = useState<Annotation | null>(null);\n  const [filter, setFilter] = useState<AnnotationFilter>({});\n\n  // Load annotations\n  const loadAnnotations = useCallback(async () => {\n    if (!paperId) return;\n    \n    try {\n      setLoading(true);\n      setError(null);\n      \n      const loadedAnnotations = await annotationStorage.getAnnotations(paperId);\n      setAnnotations(loadedAnnotations);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to load annotations';\n      setError(errorMessage);\n      message.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [paperId]);\n\n  // Create annotation\n  const createAnnotation = useCallback(async (\n    type: Annotation['type'],\n    content: string,\n    selection?: TextSelection,\n    color?: AnnotationColor\n  ): Promise<Annotation | null> => {\n    try {\n      const annotation: Annotation = {\n        id: `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        paperId,\n        type,\n        pageNumber: selection?.pageNumber || 1,\n        selection,\n        content,\n        color,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      const savedAnnotation = await annotationStorage.saveAnnotation(annotation);\n      setAnnotations(prev => [...prev, savedAnnotation]);\n      \n      message.success(`${type.charAt(0).toUpperCase() + type.slice(1)} created successfully`);\n      return savedAnnotation;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to create annotation';\n      message.error(errorMessage);\n      return null;\n    }\n  }, [paperId]);\n\n  // Update annotation\n  const updateAnnotation = useCallback(async (\n    annotationId: string,\n    updates: Partial<Annotation>\n  ): Promise<boolean> => {\n    try {\n      const existingAnnotation = annotations.find(a => a.id === annotationId);\n      if (!existingAnnotation) {\n        throw new Error('Annotation not found');\n      }\n\n      const updatedAnnotation = {\n        ...existingAnnotation,\n        ...updates,\n        updatedAt: new Date(),\n      };\n\n      await annotationStorage.updateAnnotation(updatedAnnotation);\n      \n      setAnnotations(prev => \n        prev.map(a => a.id === annotationId ? updatedAnnotation : a)\n      );\n      \n      message.success('Annotation updated successfully');\n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to update annotation';\n      message.error(errorMessage);\n      return false;\n    }\n  }, [annotations]);\n\n  // Delete annotation\n  const deleteAnnotation = useCallback(async (annotationId: string): Promise<boolean> => {\n    try {\n      await annotationStorage.deleteAnnotation(paperId, annotationId);\n      \n      setAnnotations(prev => prev.filter(a => a.id !== annotationId));\n      \n      if (selectedAnnotation?.id === annotationId) {\n        setSelectedAnnotation(null);\n      }\n      \n      message.success('Annotation deleted successfully');\n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to delete annotation';\n      message.error(errorMessage);\n      return false;\n    }\n  }, [paperId, selectedAnnotation]);\n\n  // Filter annotations\n  const filteredAnnotations = useCallback(async (newFilter: AnnotationFilter) => {\n    try {\n      setFilter(newFilter);\n      const filtered = await annotationStorage.filterAnnotations(paperId, newFilter);\n      return filtered;\n    } catch (err) {\n      console.error('Failed to filter annotations:', err);\n      return annotations;\n    }\n  }, [paperId, annotations]);\n\n  // Get annotation stats\n  const getStats = useCallback(async (): Promise<AnnotationStats | null> => {\n    try {\n      return await annotationStorage.getAnnotationStats(paperId);\n    } catch (err) {\n      console.error('Failed to get annotation stats:', err);\n      return null;\n    }\n  }, [paperId]);\n\n  // Export annotations\n  const exportAnnotations = useCallback(async (): Promise<string | null> => {\n    try {\n      const exported = await annotationStorage.exportAnnotations(paperId);\n      message.success('Annotations exported successfully');\n      return exported;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to export annotations';\n      message.error(errorMessage);\n      return null;\n    }\n  }, [paperId]);\n\n  // Import annotations\n  const importAnnotations = useCallback(async (annotationsJson: string): Promise<boolean> => {\n    try {\n      await annotationStorage.importAnnotations(paperId, annotationsJson);\n      await loadAnnotations(); // Reload annotations\n      message.success('Annotations imported successfully');\n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to import annotations';\n      message.error(errorMessage);\n      return false;\n    }\n  }, [paperId, loadAnnotations]);\n\n  // Sync with server\n  const syncAnnotations = useCallback(async (): Promise<boolean> => {\n    try {\n      await annotationStorage.syncWithServer(paperId);\n      await loadAnnotations(); // Reload annotations\n      message.success('Annotations synced successfully');\n      return true;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to sync annotations';\n      message.error(errorMessage);\n      return false;\n    }\n  }, [paperId, loadAnnotations]);\n\n  // Create highlight annotation\n  const createHighlight = useCallback((\n    selection: TextSelection,\n    color: AnnotationColor = 'yellow'\n  ) => {\n    return createAnnotation('highlight', selection.selectedText, selection, color);\n  }, [createAnnotation]);\n\n  // Create note annotation\n  const createNote = useCallback((\n    content: string,\n    selection?: TextSelection,\n    pageNumber: number = 1\n  ) => {\n    return createAnnotation('note', content, selection || { \n      startOffset: 0, \n      endOffset: 0, \n      selectedText: '', \n      pageNumber \n    });\n  }, [createAnnotation]);\n\n  // Create bookmark annotation\n  const createBookmark = useCallback((\n    pageNumber: number,\n    title: string = `Bookmark - Page ${pageNumber}`\n  ) => {\n    return createAnnotation('bookmark', title, {\n      startOffset: 0,\n      endOffset: 0,\n      selectedText: '',\n      pageNumber,\n    });\n  }, [createAnnotation]);\n\n  // Load annotations on mount\n  useEffect(() => {\n    loadAnnotations();\n  }, [loadAnnotations]);\n\n  return {\n    // State\n    annotations,\n    loading,\n    error,\n    selectedAnnotation,\n    filter,\n    \n    // Actions\n    loadAnnotations,\n    createAnnotation,\n    updateAnnotation,\n    deleteAnnotation,\n    setSelectedAnnotation,\n    \n    // Filtering\n    filteredAnnotations,\n    setFilter,\n    \n    // Stats and utilities\n    getStats,\n    exportAnnotations,\n    importAnnotations,\n    syncAnnotations,\n    \n    // Convenience methods\n    createHighlight,\n    createNote,\n    createBookmark,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAChF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,CAAC;IAExD,mBAAmB;IACnB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,oBAAoB,MAAM,mIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;YACjE,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAQ;IAEZ,oBAAoB;IACpB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACnC,MACA,SACA,WACA;QAEA,IAAI;YACF,MAAM,aAAyB;gBAC7B,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACzE;gBACA;gBACA,YAAY,WAAW,cAAc;gBACrC;gBACA;gBACA;gBACA,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;YAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;YAC/D,eAAe,CAAA,OAAQ;uBAAI;oBAAM;iBAAgB;YAEjD,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,qBAAqB,CAAC;YACtF,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,OAAO;QACT;IACF,GAAG;QAAC;KAAQ;IAEZ,oBAAoB;IACpB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACnC,cACA;QAEA,IAAI;YACF,MAAM,qBAAqB,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC1D,IAAI,CAAC,oBAAoB;gBACvB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,oBAAoB;gBACxB,GAAG,kBAAkB;gBACrB,GAAG,OAAO;gBACV,WAAW,IAAI;YACjB;YAEA,MAAM,mIAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC;YAEzC,eAAe,CAAA,OACb,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,oBAAoB;YAG5D,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,OAAO;QACT;IACF,GAAG;QAAC;KAAY;IAEhB,oBAAoB;IACpB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,IAAI;YACF,MAAM,mIAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC,SAAS;YAElD,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAEjD,IAAI,oBAAoB,OAAO,cAAc;gBAC3C,sBAAsB;YACxB;YAEA,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,OAAO;QACT;IACF,GAAG;QAAC;QAAS;KAAmB;IAEhC,qBAAqB;IACrB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC7C,IAAI;YACF,UAAU;YACV,MAAM,WAAW,MAAM,mIAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC,SAAS;YACpE,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF,GAAG;QAAC;QAAS;KAAY;IAEzB,uBAAuB;IACvB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,IAAI;YACF,OAAO,MAAM,mIAAA,CAAA,oBAAiB,CAAC,kBAAkB,CAAC;QACpD,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF,GAAG;QAAC;KAAQ;IAEZ,qBAAqB;IACrB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI;YACF,MAAM,WAAW,MAAM,mIAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC;YAC3D,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,OAAO;QACT;IACF,GAAG;QAAC;KAAQ;IAEZ,qBAAqB;IACrB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3C,IAAI;YACF,MAAM,mIAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC,SAAS;YACnD,MAAM,mBAAmB,qBAAqB;YAC9C,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,OAAO;QACT;IACF,GAAG;QAAC;QAAS;KAAgB;IAE7B,mBAAmB;IACnB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI;YACF,MAAM,mIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;YACvC,MAAM,mBAAmB,qBAAqB;YAC9C,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd,OAAO;QACT;IACF,GAAG;QAAC;QAAS;KAAgB;IAE7B,8BAA8B;IAC9B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAClC,WACA,QAAyB,QAAQ;QAEjC,OAAO,iBAAiB,aAAa,UAAU,YAAY,EAAE,WAAW;IAC1E,GAAG;QAAC;KAAiB;IAErB,yBAAyB;IACzB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAC7B,SACA,WACA,aAAqB,CAAC;QAEtB,OAAO,iBAAiB,QAAQ,SAAS,aAAa;YACpD,aAAa;YACb,WAAW;YACX,cAAc;YACd;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,6BAA6B;IAC7B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CACjC,YACA,QAAgB,CAAC,gBAAgB,EAAE,YAAY;QAE/C,OAAO,iBAAiB,YAAY,OAAO;YACzC,aAAa;YACb,WAAW;YACX,cAAc;YACd;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QAEA,YAAY;QACZ;QACA;QAEA,sBAAsB;QACtB;QACA;QACA;QACA;QAEA,sBAAsB;QACtB;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 3497, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/hooks/useTextSelection.ts"], "sourcesContent": ["import { useState, useCallback, useEffect } from 'react';\nimport { TextSelection } from '@/lib/annotation-types';\n\nexport function useTextSelection(currentPage: number = 1) {\n  const [selection, setSelection] = useState<TextSelection | null>(null);\n  const [isSelecting, setIsSelecting] = useState(false);\n\n  // Get text selection from the DOM\n  const getTextSelection = useCallback((): TextSelection | null => {\n    const domSelection = window.getSelection();\n    \n    if (!domSelection || domSelection.rangeCount === 0) {\n      return null;\n    }\n\n    const range = domSelection.getRangeAt(0);\n    const selectedText = domSelection.toString().trim();\n    \n    if (!selectedText) {\n      return null;\n    }\n\n    // Find the PDF page element\n    const pdfPageElement = range.commonAncestorContainer.nodeType === Node.TEXT_NODE\n      ? range.commonAncestorContainer.parentElement?.closest('.react-pdf__Page')\n      : (range.commonAncestorContainer as Element).closest('.react-pdf__Page');\n\n    if (!pdfPageElement) {\n      return null;\n    }\n\n    // Get page number from the PDF page element\n    const pageNumber = currentPage; // Use current page as fallback\n\n    // Calculate relative positions within the page\n    const pageRect = pdfPageElement.getBoundingClientRect();\n    const rangeRect = range.getBoundingClientRect();\n\n    const textSelection: TextSelection = {\n      startOffset: range.startOffset,\n      endOffset: range.endOffset,\n      selectedText,\n      pageNumber,\n      boundingRect: rangeRect,\n    };\n\n    return textSelection;\n  }, [currentPage]);\n\n  // Handle text selection\n  const handleTextSelection = useCallback((event?: MouseEvent | React.MouseEvent) => {\n    // Small delay to ensure selection is complete\n    setTimeout(() => {\n      const textSelection = getTextSelection();\n      setSelection(textSelection);\n      setIsSelecting(false);\n    }, 10);\n  }, [getTextSelection]);\n\n  // Handle selection start\n  const handleSelectionStart = useCallback(() => {\n    setIsSelecting(true);\n    setSelection(null);\n  }, []);\n\n  // Clear selection\n  const clearSelection = useCallback(() => {\n    setSelection(null);\n    setIsSelecting(false);\n    \n    // Clear DOM selection\n    const domSelection = window.getSelection();\n    if (domSelection) {\n      domSelection.removeAllRanges();\n    }\n  }, []);\n\n  // Check if there's an active selection\n  const hasSelection = selection !== null && selection.selectedText.length > 0;\n\n  // Get selection position for positioning UI elements\n  const getSelectionPosition = useCallback(() => {\n    if (!selection?.boundingRect) return null;\n\n    const rect = selection.boundingRect;\n    return {\n      x: rect.left + rect.width / 2,\n      y: rect.top,\n      width: rect.width,\n      height: rect.height,\n    };\n  }, [selection]);\n\n  // Restore selection (useful for highlighting)\n  const restoreSelection = useCallback((textSelection: TextSelection) => {\n    try {\n      // This is a simplified version - in a real implementation,\n      // you'd need to find the exact text nodes and recreate the range\n      const textNodes = document.querySelectorAll('.react-pdf__Page__textContent span');\n      \n      for (const node of textNodes) {\n        if (node.textContent?.includes(textSelection.selectedText)) {\n          const range = document.createRange();\n          range.selectNodeContents(node);\n          \n          const domSelection = window.getSelection();\n          if (domSelection) {\n            domSelection.removeAllRanges();\n            domSelection.addRange(range);\n          }\n          break;\n        }\n      }\n    } catch (error) {\n      console.error('Failed to restore selection:', error);\n    }\n  }, []);\n\n  // Listen for selection changes\n  useEffect(() => {\n    const handleSelectionChange = () => {\n      const domSelection = window.getSelection();\n      if (domSelection && domSelection.toString().trim()) {\n        setIsSelecting(false);\n      }\n    };\n\n    document.addEventListener('selectionchange', handleSelectionChange);\n    \n    return () => {\n      document.removeEventListener('selectionchange', handleSelectionChange);\n    };\n  }, []);\n\n  // Listen for mouse events to detect selection\n  useEffect(() => {\n    const handleMouseDown = () => {\n      handleSelectionStart();\n    };\n\n    const handleMouseUp = (event: MouseEvent) => {\n      handleTextSelection(event);\n    };\n\n    // Only listen on PDF content areas\n    const pdfElements = document.querySelectorAll('.react-pdf__Page');\n    \n    pdfElements.forEach(element => {\n      element.addEventListener('mousedown', handleMouseDown);\n      element.addEventListener('mouseup', handleMouseUp);\n    });\n\n    return () => {\n      pdfElements.forEach(element => {\n        element.removeEventListener('mousedown', handleMouseDown);\n        element.removeEventListener('mouseup', handleMouseUp);\n      });\n    };\n  }, [handleTextSelection, handleSelectionStart]);\n\n  return {\n    // State\n    selection,\n    isSelecting,\n    hasSelection,\n    \n    // Actions\n    handleTextSelection,\n    handleSelectionStart,\n    clearSelection,\n    restoreSelection,\n    \n    // Utilities\n    getSelectionPosition,\n    getTextSelection,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,SAAS,iBAAiB,cAAsB,CAAC;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kCAAkC;IAClC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,MAAM,eAAe,OAAO,YAAY;QAExC,IAAI,CAAC,gBAAgB,aAAa,UAAU,KAAK,GAAG;YAClD,OAAO;QACT;QAEA,MAAM,QAAQ,aAAa,UAAU,CAAC;QACtC,MAAM,eAAe,aAAa,QAAQ,GAAG,IAAI;QAEjD,IAAI,CAAC,cAAc;YACjB,OAAO;QACT;QAEA,4BAA4B;QAC5B,MAAM,iBAAiB,MAAM,uBAAuB,CAAC,QAAQ,KAAK,KAAK,SAAS,GAC5E,MAAM,uBAAuB,CAAC,aAAa,EAAE,QAAQ,sBACrD,AAAC,MAAM,uBAAuB,CAAa,OAAO,CAAC;QAEvD,IAAI,CAAC,gBAAgB;YACnB,OAAO;QACT;QAEA,4CAA4C;QAC5C,MAAM,aAAa,aAAa,+BAA+B;QAE/D,+CAA+C;QAC/C,MAAM,WAAW,eAAe,qBAAqB;QACrD,MAAM,YAAY,MAAM,qBAAqB;QAE7C,MAAM,gBAA+B;YACnC,aAAa,MAAM,WAAW;YAC9B,WAAW,MAAM,SAAS;YAC1B;YACA;YACA,cAAc;QAChB;QAEA,OAAO;IACT,GAAG;QAAC;KAAY;IAEhB,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,8CAA8C;QAC9C,WAAW;YACT,MAAM,gBAAgB;YACtB,aAAa;YACb,eAAe;QACjB,GAAG;IACL,GAAG;QAAC;KAAiB;IAErB,yBAAyB;IACzB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,eAAe;QACf,aAAa;IACf,GAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,aAAa;QACb,eAAe;QAEf,sBAAsB;QACtB,MAAM,eAAe,OAAO,YAAY;QACxC,IAAI,cAAc;YAChB,aAAa,eAAe;QAC9B;IACF,GAAG,EAAE;IAEL,uCAAuC;IACvC,MAAM,eAAe,cAAc,QAAQ,UAAU,YAAY,CAAC,MAAM,GAAG;IAE3E,qDAAqD;IACrD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,CAAC,WAAW,cAAc,OAAO;QAErC,MAAM,OAAO,UAAU,YAAY;QACnC,OAAO;YACL,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;YAC5B,GAAG,KAAK,GAAG;YACX,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;QACrB;IACF,GAAG;QAAC;KAAU;IAEd,8CAA8C;IAC9C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI;YACF,2DAA2D;YAC3D,iEAAiE;YACjE,MAAM,YAAY,SAAS,gBAAgB,CAAC;YAE5C,KAAK,MAAM,QAAQ,UAAW;gBAC5B,IAAI,KAAK,WAAW,EAAE,SAAS,cAAc,YAAY,GAAG;oBAC1D,MAAM,QAAQ,SAAS,WAAW;oBAClC,MAAM,kBAAkB,CAAC;oBAEzB,MAAM,eAAe,OAAO,YAAY;oBACxC,IAAI,cAAc;wBAChB,aAAa,eAAe;wBAC5B,aAAa,QAAQ,CAAC;oBACxB;oBACA;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF,GAAG,EAAE;IAEL,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,wBAAwB;YAC5B,MAAM,eAAe,OAAO,YAAY;YACxC,IAAI,gBAAgB,aAAa,QAAQ,GAAG,IAAI,IAAI;gBAClD,eAAe;YACjB;QACF;QAEA,SAAS,gBAAgB,CAAC,mBAAmB;QAE7C,OAAO;YACL,SAAS,mBAAmB,CAAC,mBAAmB;QAClD;IACF,GAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB;QACF;QAEA,MAAM,gBAAgB,CAAC;YACrB,oBAAoB;QACtB;QAEA,mCAAmC;QACnC,MAAM,cAAc,SAAS,gBAAgB,CAAC;QAE9C,YAAY,OAAO,CAAC,CAAA;YAClB,QAAQ,gBAAgB,CAAC,aAAa;YACtC,QAAQ,gBAAgB,CAAC,WAAW;QACtC;QAEA,OAAO;YACL,YAAY,OAAO,CAAC,CAAA;gBAClB,QAAQ,mBAAmB,CAAC,aAAa;gBACzC,QAAQ,mBAAmB,CAAC,WAAW;YACzC;QACF;IACF,GAAG;QAAC;QAAqB;KAAqB;IAE9C,OAAO;QACL,QAAQ;QACR;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QAEA,YAAY;QACZ;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 3656, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/pdf/PDFViewer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef } from 'react';\nimport dynamic from 'next/dynamic';\nimport { Card, Button, Input, Slider, Space, Tooltip, Typography, Spin, message } from 'antd';\nimport { useAnnotations } from '@/hooks/useAnnotations';\nimport { useTextSelection } from '@/hooks/useTextSelection';\nimport AnnotationToolbar from '@/components/annotations/AnnotationToolbar';\nimport AnnotationDisplay from '@/components/annotations/AnnotationDisplay';\nimport {\n  ZoomInOutlined,\n  ZoomOutOutlined,\n  LeftOutlined,\n  RightOutlined,\n  FullscreenOutlined,\n  DownloadOutlined,\n  SearchOutlined,\n  HighlightOutlined,\n  EditOutlined,\n  BookOutlined,\n} from '@ant-design/icons';\nimport { Paper } from '@/lib/types';\nimport { Annotation } from '@/lib/annotation-types';\n\n// Dynamic import for PDF component to avoid SSR issues\nconst PDFDocument = dynamic(() => import('./PDFDocument'), {\n  ssr: false,\n  loading: () => <Spin size=\"large\" />,\n});\n\nconst { Text } = Typography;\n\ninterface PDFViewerProps {\n  paper: Paper;\n  className?: string;\n  onAnnotationCreate?: (annotation: Annotation) => void;\n}\n\nconst PDFViewer: React.FC<PDFViewerProps> = ({\n  paper,\n  className = '',\n  onAnnotationCreate,\n}) => {\n  const [numPages, setNumPages] = useState<number>(0);\n  const [currentPage, setCurrentPage] = useState<number>(1);\n  const [scale, setScale] = useState<number>(1.0);\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [searchText, setSearchText] = useState<string>('');\n  const [showAnnotations, setShowAnnotations] = useState<boolean>(false);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // Annotation hooks\n  const {\n    annotations,\n    loading: annotationsLoading,\n    createHighlight,\n    createNote,\n    createBookmark,\n    updateAnnotation,\n    deleteAnnotation,\n    exportAnnotations,\n    importAnnotations,\n    syncAnnotations,\n  } = useAnnotations(paper.id);\n\n  const {\n    selection,\n    hasSelection,\n    getSelectionPosition,\n    clearSelection,\n    handleTextSelection,\n  } = useTextSelection(currentPage);\n\n  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {\n    setNumPages(numPages);\n    setIsLoading(false);\n    message.success('PDF loaded successfully');\n  };\n\n  const onDocumentLoadError = (error: Error) => {\n    console.error('Error loading PDF:', error);\n    setIsLoading(false);\n    message.error('Failed to load PDF: ' + error.message);\n  };\n\n  const onPageLoadSuccess = () => {\n    // Page loaded successfully\n  };\n\n  const onPageLoadError = (error: Error) => {\n    console.error('Error loading page:', error);\n    message.warning('Failed to load page: ' + error.message);\n  };\n\n  const handlePageChange = (page: number) => {\n    if (page >= 1 && page <= numPages) {\n      setCurrentPage(page);\n    }\n  };\n\n  const handleZoomIn = () => {\n    setScale(prev => Math.min(prev + 0.2, 3.0));\n  };\n\n  const handleZoomOut = () => {\n    setScale(prev => Math.max(prev - 0.2, 0.5));\n  };\n\n  const handleScaleChange = (value: number) => {\n    setScale(value);\n  };\n\n  const handleFullscreen = () => {\n    if (containerRef.current) {\n      if (document.fullscreenElement) {\n        document.exitFullscreen();\n      } else {\n        containerRef.current.requestFullscreen();\n      }\n    }\n  };\n\n  const handleDownload = () => {\n    // In a real implementation, you would download the PDF file\n    const link = document.createElement('a');\n    link.href = paper.filePath;\n    link.download = `${paper.title}.pdf`;\n    link.click();\n  };\n\n  // Annotation handlers\n  const handleCreateHighlight = async (selection: any, color: any) => {\n    const annotation = await createHighlight(selection, color);\n    if (annotation) {\n      onAnnotationCreate?.(annotation);\n      clearSelection();\n    }\n  };\n\n  const handleCreateNote = async (selection: any, content: string) => {\n    const annotation = await createNote(content, selection, currentPage);\n    if (annotation) {\n      onAnnotationCreate?.(annotation);\n      clearSelection();\n    }\n  };\n\n  const handleCreateBookmark = async (pageNumber: number, title: string) => {\n    const annotation = await createBookmark(pageNumber, title);\n    if (annotation) {\n      onAnnotationCreate?.(annotation);\n    }\n  };\n\n  const handleExportAnnotations = async () => {\n    const exported = await exportAnnotations();\n    if (exported) {\n      const blob = new Blob([exported], { type: 'application/json' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${paper.title}_annotations.json`;\n      link.click();\n      URL.revokeObjectURL(url);\n    }\n  };\n\n  const currentPageAnnotations = annotations.filter(a => a.pageNumber === currentPage);\n\n  return (\n    <div className={`pdf-viewer ${className}`} ref={containerRef}>\n      {/* Toolbar */}\n      <Card className=\"mb-4\" styles={{ body: { padding: '12px 16px' } }}>\n        <div className=\"flex items-center justify-between\">\n          <Space>\n            {/* Navigation */}\n            <Button\n              icon={<LeftOutlined />}\n              onClick={() => handlePageChange(currentPage - 1)}\n              disabled={currentPage <= 1}\n            />\n            <Input\n              value={currentPage}\n              onChange={(e) => handlePageChange(parseInt(e.target.value) || 1)}\n              className=\"w-16 text-center\"\n              size=\"small\"\n            />\n            <Text>of {numPages}</Text>\n            <Button\n              icon={<RightOutlined />}\n              onClick={() => handlePageChange(currentPage + 1)}\n              disabled={currentPage >= numPages}\n            />\n          </Space>\n\n          <Space>\n            {/* Zoom Controls */}\n            <Button icon={<ZoomOutOutlined />} onClick={handleZoomOut} />\n            <Slider\n              min={0.5}\n              max={3.0}\n              step={0.1}\n              value={scale}\n              onChange={handleScaleChange}\n              className=\"w-24\"\n            />\n            <Button icon={<ZoomInOutlined />} onClick={handleZoomIn} />\n            <Text className=\"w-12 text-center\">{Math.round(scale * 100)}%</Text>\n          </Space>\n\n          <Space>\n            {/* Annotation Tools */}\n            <Tooltip title=\"Toggle Annotations Panel\">\n              <Button\n                icon={<EditOutlined />}\n                type={showAnnotations ? 'primary' : 'default'}\n                onClick={() => setShowAnnotations(!showAnnotations)}\n              />\n            </Tooltip>\n            <Tooltip title=\"Export Annotations\">\n              <Button\n                icon={<DownloadOutlined />}\n                onClick={handleExportAnnotations}\n              />\n            </Tooltip>\n            <Tooltip title=\"Sync Annotations\">\n              <Button\n                icon={<BookOutlined />}\n                onClick={syncAnnotations}\n                loading={annotationsLoading}\n              />\n            </Tooltip>\n          </Space>\n\n          <Space>\n            {/* Search */}\n            <Input\n              placeholder=\"Search in document...\"\n              prefix={<SearchOutlined />}\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              className=\"w-48\"\n              size=\"small\"\n            />\n            \n            {/* Actions */}\n            <Button icon={<FullscreenOutlined />} onClick={handleFullscreen} />\n            <Button icon={<DownloadOutlined />} onClick={handleDownload} />\n          </Space>\n        </div>\n      </Card>\n\n      {/* PDF Content */}\n      <Card className=\"flex-1 overflow-auto\" styles={{ body: { padding: 0 } }}>\n        {isLoading && (\n          <div className=\"flex items-center justify-center h-64\">\n            <Spin size=\"large\" />\n          </div>\n        )}\n        \n        <div\n          className=\"pdf-content\"\n          onMouseUp={handleTextSelection}\n          style={{\n            transform: `scale(${scale})`,\n            transformOrigin: 'top center',\n            transition: 'transform 0.2s ease',\n          }}\n        >\n          <PDFDocument\n            file={paper.filePath}\n            pageNumber={currentPage}\n            scale={scale}\n            onLoadSuccess={onDocumentLoadSuccess}\n            onLoadError={onDocumentLoadError}\n            onPageLoadSuccess={onPageLoadSuccess}\n            onPageLoadError={onPageLoadError}\n          />\n        </div>\n\n        {/* Annotations Overlay */}\n        {currentPageAnnotations.length > 0 && (\n          <div className=\"absolute top-0 left-0 w-full h-full pointer-events-none\">\n            {currentPageAnnotations.map((annotation) => (\n              <div\n                key={annotation.id}\n                className={`absolute pointer-events-auto ${\n                  annotation.type === 'highlight' \n                    ? 'bg-yellow-200 bg-opacity-50' \n                    : annotation.type === 'note'\n                    ? 'bg-blue-200 bg-opacity-50'\n                    : 'bg-red-200 bg-opacity-50'\n                }`}\n                style={{\n                  left: annotation.position.x,\n                  top: annotation.position.y,\n                  width: annotation.position.width,\n                  height: annotation.position.height,\n                }}\n                title={annotation.content}\n              />\n            ))}\n          </div>\n        )}\n      </Card>\n\n      {/* Status Bar */}\n      <div className=\"flex items-center justify-between mt-2 text-sm text-gray-600\">\n        <div>\n          {paperAnnotations.length} annotations\n        </div>\n        <div>\n          Page {currentPage} of {numPages}\n        </div>\n        <div>\n          {selectedText && `Selected: \"${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}\"`}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PDFViewer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AATA;;;;;;;;AAwBA,uDAAuD;AACvD,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACxB,KAAK;IACL,SAAS,kBAAM,8OAAC,8KAAA,CAAA,OAAI;YAAC,MAAK;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAQ3B,MAAM,YAAsC,CAAC,EAC3C,KAAK,EACL,YAAY,EAAE,EACd,kBAAkB,EACnB;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,mBAAmB;IACnB,MAAM,EACJ,WAAW,EACX,SAAS,kBAAkB,EAC3B,eAAe,EACf,UAAU,EACV,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,EAChB,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,EAAE;IAE3B,MAAM,EACJ,SAAS,EACT,YAAY,EACZ,oBAAoB,EACpB,cAAc,EACd,mBAAmB,EACpB,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;IAErB,MAAM,wBAAwB,CAAC,EAAE,QAAQ,EAAwB;QAC/D,YAAY;QACZ,aAAa;QACb,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,KAAK,CAAC,sBAAsB;QACpC,aAAa;QACb,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,yBAAyB,MAAM,OAAO;IACtD;IAEA,MAAM,oBAAoB;IACxB,2BAA2B;IAC7B;IAEA,MAAM,kBAAkB,CAAC;QACvB,QAAQ,KAAK,CAAC,uBAAuB;QACrC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,0BAA0B,MAAM,OAAO;IACzD;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,KAAK,QAAQ,UAAU;YACjC,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,KAAK;IACxC;IAEA,MAAM,gBAAgB;QACpB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,KAAK;IACxC;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS;IACX;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa,OAAO,EAAE;YACxB,IAAI,SAAS,iBAAiB,EAAE;gBAC9B,SAAS,cAAc;YACzB,OAAO;gBACL,aAAa,OAAO,CAAC,iBAAiB;YACxC;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,4DAA4D;QAC5D,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,MAAM,QAAQ;QAC1B,KAAK,QAAQ,GAAG,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC;QACpC,KAAK,KAAK;IACZ;IAEA,sBAAsB;IACtB,MAAM,wBAAwB,OAAO,WAAgB;QACnD,MAAM,aAAa,MAAM,gBAAgB,WAAW;QACpD,IAAI,YAAY;YACd,qBAAqB;YACrB;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO,WAAgB;QAC9C,MAAM,aAAa,MAAM,WAAW,SAAS,WAAW;QACxD,IAAI,YAAY;YACd,qBAAqB;YACrB;QACF;IACF;IAEA,MAAM,uBAAuB,OAAO,YAAoB;QACtD,MAAM,aAAa,MAAM,eAAe,YAAY;QACpD,IAAI,YAAY;YACd,qBAAqB;QACvB;IACF;IAEA,MAAM,0BAA0B;QAC9B,MAAM,WAAW,MAAM;QACvB,IAAI,UAAU;YACZ,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAS,EAAE;gBAAE,MAAM;YAAmB;YAC7D,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,GAAG,MAAM,KAAK,CAAC,iBAAiB,CAAC;YACjD,KAAK,KAAK;YACV,IAAI,eAAe,CAAC;QACtB;IACF;IAEA,MAAM,yBAAyB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;IAExE,qBACE,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,WAAW;QAAE,KAAK;;0BAE9C,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;gBAAO,QAAQ;oBAAE,MAAM;wBAAE,SAAS;oBAAY;gBAAE;0BAC9D,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gMAAA,CAAA,QAAK;;8CAEJ,8OAAC,kMAAA,CAAA,SAAM;oCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oCACnB,SAAS,IAAM,iBAAiB,cAAc;oCAC9C,UAAU,eAAe;;;;;;8CAE3B,8OAAC,gLAAA,CAAA,QAAK;oCACJ,OAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oCAC9D,WAAU;oCACV,MAAK;;;;;;8CAEP,8OAAC;;wCAAK;wCAAI;;;;;;;8CACV,8OAAC,kMAAA,CAAA,SAAM;oCACL,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;oCACpB,SAAS,IAAM,iBAAiB,cAAc;oCAC9C,UAAU,eAAe;;;;;;;;;;;;sCAI7B,8OAAC,gMAAA,CAAA,QAAK;;8CAEJ,8OAAC,kMAAA,CAAA,SAAM;oCAAC,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;oCAAK,SAAS;;;;;;8CAC5C,8OAAC,kLAAA,CAAA,SAAM;oCACL,KAAK;oCACL,KAAK;oCACL,MAAM;oCACN,OAAO;oCACP,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC,kMAAA,CAAA,SAAM;oCAAC,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;oCAAK,SAAS;;;;;;8CAC3C,8OAAC;oCAAK,WAAU;;wCAAoB,KAAK,KAAK,CAAC,QAAQ;wCAAK;;;;;;;;;;;;;sCAG9D,8OAAC,gMAAA,CAAA,QAAK;;8CAEJ,8OAAC,oLAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,MAAM,kBAAkB,YAAY;wCACpC,SAAS,IAAM,mBAAmB,CAAC;;;;;;;;;;;8CAGvC,8OAAC,oLAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCACvB,SAAS;;;;;;;;;;;8CAGb,8OAAC,oLAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS;wCACT,SAAS;;;;;;;;;;;;;;;;;sCAKf,8OAAC,gMAAA,CAAA,QAAK;;8CAEJ,8OAAC,gLAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,sBAAQ,8OAAC,sNAAA,CAAA,iBAAc;;;;;oCACvB,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;oCACV,MAAK;;;;;;8CAIP,8OAAC,kMAAA,CAAA,SAAM;oCAAC,oBAAM,8OAAC,8NAAA,CAAA,qBAAkB;;;;;oCAAK,SAAS;;;;;;8CAC/C,8OAAC,kMAAA,CAAA,SAAM;oCAAC,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oCAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;gBAAuB,QAAQ;oBAAE,MAAM;wBAAE,SAAS;oBAAE;gBAAE;;oBACnE,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,8KAAA,CAAA,OAAI;4BAAC,MAAK;;;;;;;;;;;kCAIf,8OAAC;wBACC,WAAU;wBACV,WAAW;wBACX,OAAO;4BACL,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;4BAC5B,iBAAiB;4BACjB,YAAY;wBACd;kCAEA,cAAA,8OAAC;4BACC,MAAM,MAAM,QAAQ;4BACpB,YAAY;4BACZ,OAAO;4BACP,eAAe;4BACf,aAAa;4BACb,mBAAmB;4BACnB,iBAAiB;;;;;;;;;;;oBAKpB,uBAAuB,MAAM,GAAG,mBAC/B,8OAAC;wBAAI,WAAU;kCACZ,uBAAuB,GAAG,CAAC,CAAC,2BAC3B,8OAAC;gCAEC,WAAW,CAAC,6BAA6B,EACvC,WAAW,IAAI,KAAK,cAChB,gCACA,WAAW,IAAI,KAAK,SACpB,8BACA,4BACJ;gCACF,OAAO;oCACL,MAAM,WAAW,QAAQ,CAAC,CAAC;oCAC3B,KAAK,WAAW,QAAQ,CAAC,CAAC;oCAC1B,OAAO,WAAW,QAAQ,CAAC,KAAK;oCAChC,QAAQ,WAAW,QAAQ,CAAC,MAAM;gCACpC;gCACA,OAAO,WAAW,OAAO;+BAdpB,WAAW,EAAE;;;;;;;;;;;;;;;;0BAsB5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BACE,iBAAiB,MAAM;4BAAC;;;;;;;kCAE3B,8OAAC;;4BAAI;4BACG;4BAAY;4BAAK;;;;;;;kCAEzB,8OAAC;kCACE,gBAAgB,CAAC,WAAW,EAAE,aAAa,SAAS,CAAC,GAAG,MAAM,aAAa,MAAM,GAAG,KAAK,QAAQ,GAAG,CAAC,CAAC;;;;;;;;;;;;;;;;;;AAKjH;uCAEe", "debugId": null}}, {"offset": {"line": 4178, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/hooks/useAIAnalysis.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { Paper, PaperAnalysis, ChatMessage } from '@/lib/types';\nimport { API_ENDPOINTS, ANALYSIS_TYPES, ERROR_MESSAGES } from '@/lib/constants';\nimport { useAppStore } from '@/store/useAppStore';\n\ninterface UseAIAnalysisReturn {\n  analyzeDocument: (paper: Paper, analysisType: string) => Promise<string | null>;\n  askQuestion: (paper: Paper, question: string) => Promise<ChatMessage | null>;\n  comparePapers: (papers: Paper[]) => Promise<string | null>;\n  isAnalyzing: boolean;\n  error: string | null;\n  clearError: () => void;\n}\n\nexport const useAIAnalysis = (): UseAIAnalysisReturn => {\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  \n  const { addChatMessage, setError: setGlobalError } = useAppStore();\n\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  const analyzeDocument = useCallback(async (\n    paper: Paper, \n    analysisType: string\n  ): Promise<string | null> => {\n    try {\n      setIsAnalyzing(true);\n      setError(null);\n\n      const response = await fetch(API_ENDPOINTS.analysis, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          paperId: paper.id,\n          content: paper.content,\n          analysisType,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      const result = await response.json();\n      \n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      return result.data.result;\n\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      return null;\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [setGlobalError]);\n\n  const askQuestion = useCallback(async (\n    paper: Paper, \n    question: string\n  ): Promise<ChatMessage | null> => {\n    try {\n      setIsAnalyzing(true);\n      setError(null);\n\n      // Create user message\n      const userMessage: ChatMessage = {\n        id: `msg_${Date.now()}_user`,\n        role: 'user',\n        content: question,\n        timestamp: new Date(),\n        paperId: paper.id,\n      };\n\n      addChatMessage(userMessage);\n\n      const response = await fetch(API_ENDPOINTS.chat, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: question,\n          paperId: paper.id,\n          paperContent: paper.content,\n          context: `Paper: ${paper.title}`,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      const result = await response.json();\n      \n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      const assistantMessage: ChatMessage = result.data.message;\n      addChatMessage(assistantMessage);\n\n      return assistantMessage;\n\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      \n      // Add error message to chat\n      const errorChatMessage: ChatMessage = {\n        id: `msg_${Date.now()}_error`,\n        role: 'assistant',\n        content: `I apologize, but I encountered an error: ${errorMessage}`,\n        timestamp: new Date(),\n        paperId: paper.id,\n      };\n      \n      addChatMessage(errorChatMessage);\n      return null;\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [addChatMessage, setGlobalError]);\n\n  const comparePapers = useCallback(async (papers: Paper[]): Promise<string | null> => {\n    try {\n      setIsAnalyzing(true);\n      setError(null);\n\n      if (papers.length < 2) {\n        throw new Error('At least 2 papers are required for comparison');\n      }\n\n      // For now, we'll compare the first two papers\n      // In a full implementation, you might want to handle multiple papers differently\n      const [paper1, paper2] = papers;\n\n      const response = await fetch('/api/comparison', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          paper1: {\n            id: paper1.id,\n            title: paper1.title,\n            content: paper1.content,\n          },\n          paper2: {\n            id: paper2.id,\n            title: paper2.title,\n            content: paper2.content,\n          },\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      const result = await response.json();\n      \n      if (!result.success) {\n        throw new Error(result.error || ERROR_MESSAGES.API_ERROR);\n      }\n\n      return result.data.comparison;\n\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : ERROR_MESSAGES.API_ERROR;\n      setError(errorMessage);\n      setGlobalError(errorMessage);\n      return null;\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [setGlobalError]);\n\n  return {\n    analyzeDocument,\n    askQuestion,\n    comparePapers,\n    isAnalyzing,\n    error,\n    clearError,\n  };\n};\n\n// Hook for streaming chat responses\nexport const useStreamingChat = () => {\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [streamingMessage, setStreamingMessage] = useState('');\n  \n  const { addChatMessage } = useAppStore();\n\n  const streamQuestion = useCallback(async (\n    paper: Paper,\n    question: string,\n    onChunk?: (chunk: string) => void\n  ) => {\n    try {\n      setIsStreaming(true);\n      setStreamingMessage('');\n\n      // Add user message\n      const userMessage: ChatMessage = {\n        id: `msg_${Date.now()}_user`,\n        role: 'user',\n        content: question,\n        timestamp: new Date(),\n        paperId: paper.id,\n      };\n      addChatMessage(userMessage);\n\n      const response = await fetch(\n        `${API_ENDPOINTS.chat}?message=${encodeURIComponent(question)}&content=${encodeURIComponent(paper.content)}`,\n        {\n          method: 'GET',\n          headers: {\n            'Accept': 'text/event-stream',\n          },\n        }\n      );\n\n      if (!response.ok) {\n        throw new Error('Failed to start streaming');\n      }\n\n      const reader = response.body?.getReader();\n      if (!reader) {\n        throw new Error('No response body');\n      }\n\n      const decoder = new TextDecoder();\n      let fullMessage = '';\n\n      while (true) {\n        const { done, value } = await reader.read();\n        \n        if (done) break;\n\n        const chunk = decoder.decode(value);\n        const lines = chunk.split('\\n');\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            \n            if (data === '[DONE]') {\n              // Streaming complete, add final message\n              const assistantMessage: ChatMessage = {\n                id: `msg_${Date.now()}_assistant`,\n                role: 'assistant',\n                content: fullMessage,\n                timestamp: new Date(),\n                paperId: paper.id,\n              };\n              addChatMessage(assistantMessage);\n              return;\n            }\n\n            try {\n              const parsed = JSON.parse(data);\n              if (parsed.chunk) {\n                fullMessage += parsed.chunk;\n                setStreamingMessage(fullMessage);\n                onChunk?.(parsed.chunk);\n              }\n            } catch {\n              // Ignore parsing errors\n            }\n          }\n        }\n      }\n\n    } catch (error) {\n      console.error('Streaming error:', error);\n      // Add error message\n      const errorMessage: ChatMessage = {\n        id: `msg_${Date.now()}_error`,\n        role: 'assistant',\n        content: 'Sorry, I encountered an error while processing your question.',\n        timestamp: new Date(),\n        paperId: paper.id,\n      };\n      addChatMessage(errorMessage);\n    } finally {\n      setIsStreaming(false);\n      setStreamingMessage('');\n    }\n  }, [addChatMessage]);\n\n  return {\n    streamQuestion,\n    isStreaming,\n    streamingMessage,\n  };\n};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;AAWO,MAAM,gBAAgB;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,cAAc,EAAE,UAAU,cAAc,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE/D,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;IACX,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAClC,OACA;QAEA,IAAI;YACF,eAAe;YACf,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,uHAAA,CAAA,gBAAa,CAAC,QAAQ,EAAE;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,MAAM,EAAE;oBACjB,SAAS,MAAM,OAAO;oBACtB;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC7D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC1D;YAEA,OAAO,OAAO,IAAI,CAAC,MAAM;QAE3B,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAClF,SAAS;YACT,eAAe;YACf,OAAO;QACT,SAAU;YACR,eAAe;QACjB;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC9B,OACA;QAEA,IAAI;YACF,eAAe;YACf,SAAS;YAET,sBAAsB;YACtB,MAAM,cAA2B;gBAC/B,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;gBAC5B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;gBACf,SAAS,MAAM,EAAE;YACnB;YAEA,eAAe;YAEf,MAAM,WAAW,MAAM,MAAM,uHAAA,CAAA,gBAAa,CAAC,IAAI,EAAE;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,SAAS,MAAM,EAAE;oBACjB,cAAc,MAAM,OAAO;oBAC3B,SAAS,CAAC,OAAO,EAAE,MAAM,KAAK,EAAE;gBAClC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC7D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC1D;YAEA,MAAM,mBAAgC,OAAO,IAAI,CAAC,OAAO;YACzD,eAAe;YAEf,OAAO;QAET,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAClF,SAAS;YACT,eAAe;YAEf,4BAA4B;YAC5B,MAAM,mBAAgC;gBACpC,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC;gBAC7B,MAAM;gBACN,SAAS,CAAC,yCAAyC,EAAE,cAAc;gBACnE,WAAW,IAAI;gBACf,SAAS,MAAM,EAAE;YACnB;YAEA,eAAe;YACf,OAAO;QACT,SAAU;YACR,eAAe;QACjB;IACF,GAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,IAAI;YACF,eAAe;YACf,SAAS;YAET,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,MAAM,IAAI,MAAM;YAClB;YAEA,8CAA8C;YAC9C,iFAAiF;YACjF,MAAM,CAAC,QAAQ,OAAO,GAAG;YAEzB,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;wBACN,IAAI,OAAO,EAAE;wBACb,OAAO,OAAO,KAAK;wBACnB,SAAS,OAAO,OAAO;oBACzB;oBACA,QAAQ;wBACN,IAAI,OAAO,EAAE;wBACb,OAAO,OAAO,KAAK;wBACnB,SAAS,OAAO,OAAO;oBACzB;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC7D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC1D;YAEA,OAAO,OAAO,IAAI,CAAC,UAAU;QAE/B,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAClF,SAAS;YACT,eAAe;YACf,OAAO;QACT,SAAU;YACR,eAAe;QACjB;IACF,GAAG;QAAC;KAAe;IAEnB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAErC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACjC,OACA,UACA;QAEA,IAAI;YACF,eAAe;YACf,oBAAoB;YAEpB,mBAAmB;YACnB,MAAM,cAA2B;gBAC/B,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;gBAC5B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;gBACf,SAAS,MAAM,EAAE;YACnB;YACA,eAAe;YAEf,MAAM,WAAW,MAAM,MACrB,GAAG,uHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,SAAS,EAAE,mBAAmB,UAAU,SAAS,EAAE,mBAAmB,MAAM,OAAO,GAAG,EAC5G;gBACE,QAAQ;gBACR,SAAS;oBACP,UAAU;gBACZ;YACF;YAGF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,SAAS,IAAI,EAAE;YAC9B,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAU,IAAI;YACpB,IAAI,cAAc;YAElB,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBAEzC,IAAI,MAAM;gBAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC;gBAC7B,MAAM,QAAQ,MAAM,KAAK,CAAC;gBAE1B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;wBAExB,IAAI,SAAS,UAAU;4BACrB,wCAAwC;4BACxC,MAAM,mBAAgC;gCACpC,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,UAAU,CAAC;gCACjC,MAAM;gCACN,SAAS;gCACT,WAAW,IAAI;gCACf,SAAS,MAAM,EAAE;4BACnB;4BACA,eAAe;4BACf;wBACF;wBAEA,IAAI;4BACF,MAAM,SAAS,KAAK,KAAK,CAAC;4BAC1B,IAAI,OAAO,KAAK,EAAE;gCAChB,eAAe,OAAO,KAAK;gCAC3B,oBAAoB;gCACpB,UAAU,OAAO,KAAK;4BACxB;wBACF,EAAE,OAAM;wBACN,wBAAwB;wBAC1B;oBACF;gBACF;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,oBAAoB;YACpB,MAAM,eAA4B;gBAChC,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC;gBAC7B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;gBACf,SAAS,MAAM,EAAE;YACnB;YACA,eAAe;QACjB,SAAU;YACR,eAAe;YACf,oBAAoB;QACtB;IACF,GAAG;QAAC;KAAe;IAEnB,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4509, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/chat/ChatInterface.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Card, Input, Button, Avatar, Typography, Space, Divider, Tooltip, Dropdown } from 'antd';\nimport {\n  SendOutlined,\n  UserOutlined,\n  RobotOutlined,\n  ClearOutlined,\n  DownloadOutlined,\n  CopyOutlined,\n  MoreOutlined,\n  BulbOutlined,\n} from '@ant-design/icons';\nimport { ChatMessage, Paper } from '@/lib/types';\nimport { useAppStore } from '@/store/useAppStore';\nimport { useAIAnalysis } from '@/hooks/useAIAnalysis';\nimport ReactMarkdown from 'react-markdown';\nimport { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';\nimport { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';\n\nconst { TextArea } = Input;\nconst { Text, Paragraph } = Typography;\n\ninterface ChatInterfaceProps {\n  paper: Paper;\n  className?: string;\n}\n\nconst ChatInterface: React.FC<ChatInterfaceProps> = ({\n  paper,\n  className = '',\n}) => {\n  const [inputValue, setInputValue] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<any>(null);\n\n  const { chatMessages, clearChatMessages } = useAppStore();\n  const { askQuestion, isAnalyzing } = useAIAnalysis();\n\n  const paperMessages = chatMessages.filter(msg => msg.paperId === paper.id);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [paperMessages]);\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isAnalyzing) return;\n\n    const userMessage = inputValue.trim();\n    setInputValue('');\n    setIsTyping(true);\n\n    try {\n      await askQuestion(paper, userMessage);\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const handleClearChat = () => {\n    clearChatMessages();\n  };\n\n  const handleCopyMessage = (content: string) => {\n    navigator.clipboard.writeText(content);\n  };\n\n  const handleExportChat = () => {\n    const chatContent = paperMessages\n      .map(msg => `${msg.role.toUpperCase()}: ${msg.content}`)\n      .join('\\n\\n');\n    \n    const blob = new Blob([chatContent], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `chat-${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n\n  const suggestedQuestions = [\n    \"What are the main findings of this paper?\",\n    \"Can you summarize the methodology?\",\n    \"What are the key contributions?\",\n    \"What are the limitations mentioned?\",\n    \"How does this relate to previous work?\",\n  ];\n\n  const handleSuggestedQuestion = (question: string) => {\n    setInputValue(question);\n    inputRef.current?.focus();\n  };\n\n  const chatMenuItems = [\n    {\n      key: 'clear',\n      label: 'Clear Chat',\n      icon: <ClearOutlined />,\n      onClick: handleClearChat,\n    },\n    {\n      key: 'export',\n      label: 'Export Chat',\n      icon: <DownloadOutlined />,\n      onClick: handleExportChat,\n    },\n  ];\n\n  return (\n    <Card \n      className={`chat-container ${className}`}\n      title={\n        <div className=\"flex items-center justify-between\">\n          <Space>\n            <RobotOutlined className=\"text-blue-500\" />\n            <span>AI Assistant</span>\n          </Space>\n          <Dropdown menu={{ items: chatMenuItems }} trigger={['click']}>\n            <Button type=\"text\" icon={<MoreOutlined />} />\n          </Dropdown>\n        </div>\n      }\n      styles={{\n        body: {\n          padding: 0,\n          height: 'calc(100vh - 120px)',\n          display: 'flex',\n          flexDirection: 'column'\n        }\n      }}\n    >\n      {/* Messages Area */}\n      <div className=\"chat-messages flex-1 overflow-y-auto p-4 space-y-4\">\n        {paperMessages.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <RobotOutlined className=\"text-4xl text-gray-400 mb-4\" />\n            <Text type=\"secondary\" className=\"block mb-4\">\n              Ask me anything about this paper!\n            </Text>\n            \n            {/* Suggested Questions */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-center mb-3\">\n                <BulbOutlined className=\"text-yellow-500 mr-2\" />\n                <Text strong>Suggested questions:</Text>\n              </div>\n              {suggestedQuestions.map((question, index) => (\n                <Button\n                  key={index}\n                  type=\"dashed\"\n                  size=\"small\"\n                  className=\"block mx-auto mb-2\"\n                  onClick={() => handleSuggestedQuestion(question)}\n                >\n                  {question}\n                </Button>\n              ))}\n            </div>\n          </div>\n        ) : (\n          paperMessages.map((message) => (\n            <div\n              key={message.id}\n              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n            >\n              <div className={`flex items-start space-x-3 max-w-[80%]`}>\n                {message.role === 'assistant' && (\n                  <Avatar \n                    icon={<RobotOutlined />} \n                    className=\"bg-blue-500 flex-shrink-0\"\n                  />\n                )}\n                \n                <div\n                  className={`message-bubble ${\n                    message.role === 'user' \n                      ? 'message-user bg-blue-500 text-white' \n                      : 'message-assistant bg-gray-100'\n                  }`}\n                >\n                  {message.role === 'assistant' ? (\n                    <div className=\"prose prose-sm max-w-none\">\n                      <ReactMarkdown\n                        components={{\n                          code({ className, children, ...props }: any) {\n                            const match = /language-(\\w+)/.exec(className || '');\n                            return match ? (\n                              <SyntaxHighlighter\n                                style={tomorrow as any}\n                                language={match[1]}\n                                PreTag=\"div\"\n                              >\n                                {String(children).replace(/\\n$/, '')}\n                              </SyntaxHighlighter>\n                            ) : (\n                              <code className={`${className} bg-gray-100 px-2 py-1 rounded text-sm`} {...props}>\n                                {children}\n                              </code>\n                            );\n                          },\n                        }}\n                      >\n                        {message.content}\n                      </ReactMarkdown>\n                    </div>\n                  ) : (\n                    <Paragraph className=\"mb-0 text-inherit\">\n                      {message.content}\n                    </Paragraph>\n                  )}\n                  \n                  <div className=\"flex items-center justify-between mt-2 pt-2 border-t border-gray-200 border-opacity-20\">\n                    <Text \n                      className={`text-xs ${\n                        message.role === 'user' ? 'text-blue-100' : 'text-gray-500'\n                      }`}\n                    >\n                      {new Date(message.timestamp).toLocaleTimeString()}\n                    </Text>\n                    \n                    <Tooltip title=\"Copy message\">\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<CopyOutlined />}\n                        onClick={() => handleCopyMessage(message.content)}\n                        className={message.role === 'user' ? 'text-blue-100 hover:text-white' : ''}\n                      />\n                    </Tooltip>\n                  </div>\n                </div>\n                \n                {message.role === 'user' && (\n                  <Avatar \n                    icon={<UserOutlined />} \n                    className=\"bg-gray-500 flex-shrink-0\"\n                  />\n                )}\n              </div>\n            </div>\n          ))\n        )}\n        \n        {/* Typing Indicator */}\n        {(isAnalyzing || isTyping) && (\n          <div className=\"flex justify-start\">\n            <div className=\"flex items-start space-x-3\">\n              <Avatar \n                icon={<RobotOutlined />} \n                className=\"bg-blue-500\"\n              />\n              <div className=\"message-bubble message-assistant bg-gray-100\">\n                <div className=\"loading-dots\">Thinking...</div>\n              </div>\n            </div>\n          </div>\n        )}\n        \n        <div ref={messagesEndRef} />\n      </div>\n\n      <Divider className=\"m-0\" />\n\n      {/* Input Area */}\n      <div className=\"chat-input p-4\">\n        <div className=\"flex space-x-2\">\n          <TextArea\n            ref={inputRef}\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            onKeyDown={handleKeyPress}\n            placeholder=\"Ask a question about this paper...\"\n            autoSize={{ minRows: 1, maxRows: 4 }}\n            className=\"flex-1\"\n            disabled={isAnalyzing}\n          />\n          <Button\n            type=\"primary\"\n            icon={<SendOutlined />}\n            onClick={handleSendMessage}\n            disabled={!inputValue.trim() || isAnalyzing}\n            loading={isAnalyzing}\n          >\n            Send\n          </Button>\n        </div>\n        \n        <div className=\"flex justify-between items-center mt-2 text-xs text-gray-500\">\n          <span>Press Enter to send, Shift+Enter for new line</span>\n          <span>{paperMessages.length} messages</span>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default ChatInterface;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AAnBA;;;;;;;;;;AAqBA,MAAM,EAAE,QAAQ,EAAE,GAAG,gLAAA,CAAA,QAAK;AAC1B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAOtC,MAAM,gBAA8C,CAAC,EACnD,KAAK,EACL,YAAY,EAAE,EACf;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAE7B,MAAM,EAAE,YAAY,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IACtD,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAEjD,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,OAAO,KAAK,MAAM,EAAE;IAEzE,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,MAAM,aAAa;QAEvC,MAAM,cAAc,WAAW,IAAI;QACnC,cAAc;QACd,YAAY;QAEZ,IAAI;YACF,MAAM,YAAY,OAAO;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,UAAU,SAAS,CAAC,SAAS,CAAC;IAChC;IAEA,MAAM,mBAAmB;QACvB,MAAM,cAAc,cACjB,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,IAAI,OAAO,EAAE,EACtD,IAAI,CAAC;QAER,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAa;QAC1D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,KAAK,EAAE,MAAM,KAAK,CAAC,OAAO,CAAC,eAAe,KAAK,WAAW,GAAG,IAAI,CAAC;QACnF,KAAK,KAAK;QACV,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,0BAA0B,CAAC;QAC/B,cAAc;QACd,SAAS,OAAO,EAAE;IACpB;IAEA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;YACpB,SAAS;QACX;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,SAAS;QACX;KACD;IAED,qBACE,8OAAC,8KAAA,CAAA,OAAI;QACH,WAAW,CAAC,eAAe,EAAE,WAAW;QACxC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,gMAAA,CAAA,QAAK;;sCACJ,8OAAC,oNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;sCAAK;;;;;;;;;;;;8BAER,8OAAC,sLAAA,CAAA,WAAQ;oBAAC,MAAM;wBAAE,OAAO;oBAAc;oBAAG,SAAS;wBAAC;qBAAQ;8BAC1D,cAAA,8OAAC,kMAAA,CAAA,SAAM;wBAAC,MAAK;wBAAO,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;QAI7C,QAAQ;YACN,MAAM;gBACJ,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,eAAe;YACjB;QACF;;0BAGA,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC;gCAAK,MAAK;gCAAY,WAAU;0CAAa;;;;;;0CAK9C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;gDAAK,MAAM;0DAAC;;;;;;;;;;;;oCAEd,mBAAmB,GAAG,CAAC,CAAC,UAAU,sBACjC,8OAAC,kMAAA,CAAA,SAAM;4CAEL,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,wBAAwB;sDAEtC;2CANI;;;;;;;;;;;;;;;;mEAYb,cAAc,GAAG,CAAC,CAAC,wBACjB,8OAAC;4BAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;sCAE9E,cAAA,8OAAC;gCAAI,WAAW,CAAC,sCAAsC,CAAC;;oCACrD,QAAQ,IAAI,KAAK,6BAChB,8OAAC,kLAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;wCACpB,WAAU;;;;;;kDAId,8OAAC;wCACC,WAAW,CAAC,eAAe,EACzB,QAAQ,IAAI,KAAK,SACb,wCACA,iCACJ;;4CAED,QAAQ,IAAI,KAAK,4BAChB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wLAAA,CAAA,UAAa;oDACZ,YAAY;wDACV,MAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY;4DACzC,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;4DACjD,OAAO,sBACL,8OAAC,0MAAA,CAAA,QAAiB;gEAChB,OAAO,mOAAA,CAAA,WAAQ;gEACf,UAAU,KAAK,CAAC,EAAE;gEAClB,QAAO;0EAEN,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;uFAGnC,8OAAC;gEAAK,WAAW,GAAG,UAAU,sCAAsC,CAAC;gEAAG,GAAG,KAAK;0EAC7E;;;;;;wDAGP;oDACF;8DAEC,QAAQ,OAAO;;;;;;;;;;yGAIpB,8OAAC;gDAAU,WAAU;0DAClB,QAAQ,OAAO;;;;;;0DAIpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAW,CAAC,QAAQ,EAClB,QAAQ,IAAI,KAAK,SAAS,kBAAkB,iBAC5C;kEAED,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;kEAGjD,8OAAC,oLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4DACnB,SAAS,IAAM,kBAAkB,QAAQ,OAAO;4DAChD,WAAW,QAAQ,IAAI,KAAK,SAAS,mCAAmC;;;;;;;;;;;;;;;;;;;;;;;oCAM/E,QAAQ,IAAI,KAAK,wBAChB,8OAAC,kLAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,WAAU;;;;;;;;;;;;2BAzEX,QAAQ,EAAE;;;;;oBAkFpB,CAAC,eAAe,QAAQ,mBACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAA,CAAA,SAAM;oCACL,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;oCACpB,WAAU;;;;;;8CAEZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC;wBAAI,KAAK;;;;;;;;;;;;0BAGZ,8OAAC,oLAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BAGnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAW;gCACX,aAAY;gCACZ,UAAU;oCAAE,SAAS;oCAAG,SAAS;gCAAE;gCACnC,WAAU;gCACV,UAAU;;;;;;0CAEZ,8OAAC,kMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACnB,SAAS;gCACT,UAAU,CAAC,WAAW,IAAI,MAAM;gCAChC,SAAS;0CACV;;;;;;;;;;;;kCAKH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;;oCAAM,cAAc,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAKtC;uCAEe", "debugId": null}}, {"offset": {"line": 5059, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/chat/QuickActions.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Card, Button, Space, Typography, Divider, Modal, Spin } from 'antd';\nimport {\n  FileTextOutlined,\n  BulbOutlined,\n  ExperimentOutlined,\n  BookOutlined,\n  LinkOutlined,\n  BarChartOutlined,\n} from '@ant-design/icons';\nimport { Paper } from '@/lib/types';\nimport { useAIAnalysis } from '@/hooks/useAIAnalysis';\nimport { ANALYSIS_TYPES } from '@/lib/constants';\nimport ReactMarkdown from 'react-markdown';\n\nconst { Title, Text } = Typography;\n\ninterface QuickActionsProps {\n  paper: Paper;\n  className?: string;\n}\n\nconst QuickActions: React.FC<QuickActionsProps> = ({\n  paper,\n  className = '',\n}) => {\n  const [analysisResult, setAnalysisResult] = useState<string>('');\n  const [analysisType, setAnalysisType] = useState<string>('');\n  const [modalVisible, setModalVisible] = useState(false);\n  \n  const { analyzeDocument, isAnalyzing } = useAIAnalysis();\n\n  const handleQuickAnalysis = async (type: string, title: string) => {\n    setAnalysisType(title);\n    setModalVisible(true);\n    setAnalysisResult('');\n\n    const result = await analyzeDocument(paper, type);\n    if (result) {\n      setAnalysisResult(result);\n    }\n  };\n\n  const quickActions = [\n    {\n      key: ANALYSIS_TYPES.SUMMARY,\n      title: 'Summarize',\n      description: 'Get a comprehensive summary',\n      icon: <FileTextOutlined />,\n      color: '#1890ff',\n    },\n    {\n      key: ANALYSIS_TYPES.KEY_FINDINGS,\n      title: 'Key Findings',\n      description: 'Extract main discoveries',\n      icon: <BulbOutlined />,\n      color: '#52c41a',\n    },\n    {\n      key: ANALYSIS_TYPES.METHODOLOGY,\n      title: 'Methodology',\n      description: 'Explain research methods',\n      icon: <ExperimentOutlined />,\n      color: '#722ed1',\n    },\n    {\n      key: ANALYSIS_TYPES.CONCEPTS,\n      title: 'Key Concepts',\n      description: 'Define important terms',\n      icon: <BookOutlined />,\n      color: '#fa8c16',\n    },\n    {\n      key: ANALYSIS_TYPES.CITATIONS,\n      title: 'Citations',\n      description: 'Analyze references',\n      icon: <LinkOutlined />,\n      color: '#eb2f96',\n    },\n  ];\n\n  return (\n    <>\n      <Card \n        title=\"Quick Analysis\" \n        className={className}\n        extra={\n          <Text type=\"secondary\" className=\"text-sm\">\n            AI-powered insights\n          </Text>\n        }\n      >\n        <div className=\"space-y-3\">\n          {quickActions.map((action) => (\n            <Button\n              key={action.key}\n              block\n              size=\"large\"\n              className=\"text-left h-auto py-3\"\n              onClick={() => handleQuickAnalysis(action.key, action.title)}\n              loading={isAnalyzing && analysisType === action.title}\n              disabled={isAnalyzing}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div \n                  className=\"flex items-center justify-center w-8 h-8 rounded\"\n                  style={{ backgroundColor: `${action.color}20`, color: action.color }}\n                >\n                  {action.icon}\n                </div>\n                <div className=\"flex-1 text-left\">\n                  <div className=\"font-medium\">{action.title}</div>\n                  <div className=\"text-sm text-gray-500\">{action.description}</div>\n                </div>\n              </div>\n            </Button>\n          ))}\n        </div>\n\n        <Divider />\n\n        <div className=\"text-center\">\n          <Text type=\"secondary\" className=\"text-sm\">\n            Or ask specific questions in the chat above\n          </Text>\n        </div>\n      </Card>\n\n      {/* Analysis Result Modal */}\n      <Modal\n        title={\n          <Space>\n            <BarChartOutlined />\n            {analysisType} - {paper.title}\n          </Space>\n        }\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setModalVisible(false)}>\n            Close\n          </Button>,\n        ]}\n        width={800}\n        className=\"analysis-modal\"\n      >\n        {isAnalyzing ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <Spin size=\"large\" />\n            <Text className=\"ml-3\">Analyzing paper...</Text>\n          </div>\n        ) : analysisResult ? (\n          <div className=\"prose prose-sm max-w-none\">\n            <ReactMarkdown>{analysisResult}</ReactMarkdown>\n          </div>\n        ) : (\n          <div className=\"text-center py-8\">\n            <Text type=\"secondary\">No analysis result available</Text>\n          </div>\n        )}\n      </Modal>\n    </>\n  );\n};\n\nexport default QuickActions;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AAfA;;;;;;;;AAiBA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAOlC,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,YAAY,EAAE,EACf;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAErD,MAAM,sBAAsB,OAAO,MAAc;QAC/C,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAElB,MAAM,SAAS,MAAM,gBAAgB,OAAO;QAC5C,IAAI,QAAQ;YACV,kBAAkB;QACpB;IACF;IAEA,MAAM,eAAe;QACnB;YACE,KAAK,uHAAA,CAAA,iBAAc,CAAC,OAAO;YAC3B,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;QACT;QACA;YACE,KAAK,uHAAA,CAAA,iBAAc,CAAC,YAAY;YAChC,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;QACA;YACE,KAAK,uHAAA,CAAA,iBAAc,CAAC,WAAW;YAC/B,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8NAAA,CAAA,qBAAkB;;;;;YACzB,OAAO;QACT;QACA;YACE,KAAK,uHAAA,CAAA,iBAAc,CAAC,QAAQ;YAC5B,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;QACA;YACE,KAAK,uHAAA,CAAA,iBAAc,CAAC,SAAS;YAC7B,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;KACD;IAED,qBACE;;0BACE,8OAAC,8KAAA,CAAA,OAAI;gBACH,OAAM;gBACN,WAAW;gBACX,qBACE,8OAAC;oBAAK,MAAK;oBAAY,WAAU;8BAAU;;;;;;;kCAK7C,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,kMAAA,CAAA,SAAM;gCAEL,KAAK;gCACL,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,oBAAoB,OAAO,GAAG,EAAE,OAAO,KAAK;gCAC3D,SAAS,eAAe,iBAAiB,OAAO,KAAK;gCACrD,UAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,GAAG,OAAO,KAAK,CAAC,EAAE,CAAC;gDAAE,OAAO,OAAO,KAAK;4CAAC;sDAElE,OAAO,IAAI;;;;;;sDAEd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAe,OAAO,KAAK;;;;;;8DAC1C,8OAAC;oDAAI,WAAU;8DAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;+BAjBzD,OAAO,GAAG;;;;;;;;;;kCAwBrB,8OAAC,oLAAA,CAAA,UAAO;;;;;kCAER,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,MAAK;4BAAY,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAO/C,8OAAC,gLAAA,CAAA,QAAK;gBACJ,qBACE,8OAAC,gMAAA,CAAA,QAAK;;sCACJ,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wBAChB;wBAAa;wBAAI,MAAM,KAAK;;;;;;;gBAGjC,MAAM;gBACN,UAAU,IAAM,gBAAgB;gBAChC,QAAQ;kCACN,8OAAC,kMAAA,CAAA,SAAM;wBAAa,SAAS,IAAM,gBAAgB;kCAAQ;uBAA/C;;;;;iBAGb;gBACD,OAAO;gBACP,WAAU;0BAET,4BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8KAAA,CAAA,OAAI;4BAAC,MAAK;;;;;;sCACX,8OAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;+DAEvB,+BACF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,wLAAA,CAAA,UAAa;kCAAE;;;;;;;;;;6EAGlB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;;;;;;;AAMnC;uCAEe", "debugId": null}}, {"offset": {"line": 5362, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/pdf/AnnotationPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Card, List, Button, Input, Tag, Typography, Space, Popconfirm, Tooltip } from 'antd';\nimport {\n  HighlightOutlined,\n  EditOutlined,\n  BookOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  MessageOutlined,\n} from '@ant-design/icons';\nimport { Annotation } from '@/lib/types';\nimport { useAppStore } from '@/store/useAppStore';\n\nconst { Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface AnnotationPanelProps {\n  paperId: string;\n  currentPage?: number;\n  onAnnotationClick?: (annotation: Annotation) => void;\n  className?: string;\n}\n\nconst AnnotationPanel: React.FC<AnnotationPanelProps> = ({\n  paperId,\n  currentPage,\n  onAnnotationClick,\n  className = '',\n}) => {\n  const { annotations, removeAnnotation, updateAnnotation } = useAppStore();\n  const [editingId, setEditingId] = useState<string | null>(null);\n  const [editContent, setEditContent] = useState<string>('');\n  const [filter, setFilter] = useState<'all' | 'highlight' | 'note' | 'bookmark'>('all');\n\n  const paperAnnotations = annotations.filter(a => a.paperId === paperId);\n  const filteredAnnotations = paperAnnotations.filter(a => \n    filter === 'all' || a.type === filter\n  );\n\n  const handleEdit = (annotation: Annotation) => {\n    setEditingId(annotation.id);\n    setEditContent(annotation.content);\n  };\n\n  const handleSaveEdit = (annotationId: string) => {\n    updateAnnotation(annotationId, { content: editContent });\n    setEditingId(null);\n    setEditContent('');\n  };\n\n  const handleCancelEdit = () => {\n    setEditingId(null);\n    setEditContent('');\n  };\n\n  const handleDelete = (annotationId: string) => {\n    removeAnnotation(annotationId);\n  };\n\n  const getAnnotationIcon = (type: string) => {\n    switch (type) {\n      case 'highlight':\n        return <HighlightOutlined className=\"text-yellow-500\" />;\n      case 'note':\n        return <MessageOutlined className=\"text-blue-500\" />;\n      case 'bookmark':\n        return <BookOutlined className=\"text-red-500\" />;\n      default:\n        return <EditOutlined />;\n    }\n  };\n\n  const getAnnotationColor = (type: string) => {\n    switch (type) {\n      case 'highlight':\n        return 'gold';\n      case 'note':\n        return 'blue';\n      case 'bookmark':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n\n  const filterButtons = [\n    { key: 'all', label: 'All', count: paperAnnotations.length },\n    { key: 'highlight', label: 'Highlights', count: paperAnnotations.filter(a => a.type === 'highlight').length },\n    { key: 'note', label: 'Notes', count: paperAnnotations.filter(a => a.type === 'note').length },\n    { key: 'bookmark', label: 'Bookmarks', count: paperAnnotations.filter(a => a.type === 'bookmark').length },\n  ];\n\n  return (\n    <Card \n      title=\"Annotations\" \n      className={className}\n      extra={\n        <Text type=\"secondary\" className=\"text-sm\">\n          {filteredAnnotations.length} items\n        </Text>\n      }\n    >\n      {/* Filter Buttons */}\n      <div className=\"mb-4 flex flex-wrap gap-2\">\n        {filterButtons.map(({ key, label, count }) => (\n          <Button\n            key={key}\n            size=\"small\"\n            type={filter === key ? 'primary' : 'default'}\n            onClick={() => setFilter(key as any)}\n          >\n            {label} ({count})\n          </Button>\n        ))}\n      </div>\n\n      {/* Annotations List */}\n      <List\n        dataSource={filteredAnnotations}\n        locale={{ emptyText: 'No annotations yet' }}\n        renderItem={(annotation) => (\n          <List.Item\n            className={`${\n              currentPage === annotation.pageNumber ? 'bg-blue-50 border-blue-200' : ''\n            } rounded p-3 mb-2 border transition-colors hover:bg-gray-50`}\n          >\n            <div className=\"w-full\">\n              <div className=\"flex items-start justify-between mb-2\">\n                <div className=\"flex items-center space-x-2\">\n                  {getAnnotationIcon(annotation.type)}\n                  <Tag color={getAnnotationColor(annotation.type)}>\n                    {annotation.type}\n                  </Tag>\n                  <Text type=\"secondary\" className=\"text-xs\">\n                    Page {annotation.pageNumber}\n                  </Text>\n                </div>\n                \n                <Space size=\"small\">\n                  <Tooltip title=\"Go to annotation\">\n                    <Button\n                      type=\"text\"\n                      size=\"small\"\n                      icon={<EyeOutlined />}\n                      onClick={() => onAnnotationClick?.(annotation)}\n                    />\n                  </Tooltip>\n                  <Tooltip title=\"Edit\">\n                    <Button\n                      type=\"text\"\n                      size=\"small\"\n                      icon={<EditOutlined />}\n                      onClick={() => handleEdit(annotation)}\n                    />\n                  </Tooltip>\n                  <Popconfirm\n                    title=\"Delete annotation?\"\n                    description=\"This action cannot be undone.\"\n                    onConfirm={() => handleDelete(annotation.id)}\n                    okText=\"Delete\"\n                    cancelText=\"Cancel\"\n                    okButtonProps={{ danger: true }}\n                  >\n                    <Tooltip title=\"Delete\">\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<DeleteOutlined />}\n                        danger\n                      />\n                    </Tooltip>\n                  </Popconfirm>\n                </Space>\n              </div>\n\n              {editingId === annotation.id ? (\n                <div className=\"space-y-2\">\n                  <TextArea\n                    value={editContent}\n                    onChange={(e) => setEditContent(e.target.value)}\n                    rows={3}\n                    placeholder=\"Edit annotation content...\"\n                  />\n                  <div className=\"flex justify-end space-x-2\">\n                    <Button size=\"small\" onClick={handleCancelEdit}>\n                      Cancel\n                    </Button>\n                    <Button \n                      type=\"primary\" \n                      size=\"small\" \n                      onClick={() => handleSaveEdit(annotation.id)}\n                    >\n                      Save\n                    </Button>\n                  </div>\n                </div>\n              ) : (\n                <Paragraph \n                  className=\"mb-0 text-sm\"\n                  ellipsis={{ rows: 3, expandable: true, symbol: 'more' }}\n                >\n                  {annotation.content}\n                </Paragraph>\n              )}\n\n              <Text type=\"secondary\" className=\"text-xs\">\n                {new Date(annotation.createdAt).toLocaleString()}\n              </Text>\n            </div>\n          </List.Item>\n        )}\n      />\n    </Card>\n  );\n};\n\nexport default AnnotationPanel;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAbA;;;;;;AAeA,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AACtC,MAAM,EAAE,QAAQ,EAAE,GAAG,gLAAA,CAAA,QAAK;AAS1B,MAAM,kBAAkD,CAAC,EACvD,OAAO,EACP,WAAW,EACX,iBAAiB,EACjB,YAAY,EAAE,EACf;IACC,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IAEhF,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;IAC/D,MAAM,sBAAsB,iBAAiB,MAAM,CAAC,CAAA,IAClD,WAAW,SAAS,EAAE,IAAI,KAAK;IAGjC,MAAM,aAAa,CAAC;QAClB,aAAa,WAAW,EAAE;QAC1B,eAAe,WAAW,OAAO;IACnC;IAEA,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,cAAc;YAAE,SAAS;QAAY;QACtD,aAAa;QACb,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,eAAe;IACjB;IAEA,MAAM,eAAe,CAAC;QACpB,iBAAiB;IACnB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,4NAAA,CAAA,oBAAiB;oBAAC,WAAU;;;;;;YACtC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,8OAAC,kNAAA,CAAA,eAAY;;;;;QACxB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB;YAAE,KAAK;YAAO,OAAO;YAAO,OAAO,iBAAiB,MAAM;QAAC;QAC3D;YAAE,KAAK;YAAa,OAAO;YAAc,OAAO,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;QAAC;QAC5G;YAAE,KAAK;YAAQ,OAAO;YAAS,OAAO,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;QAAC;QAC7F;YAAE,KAAK;YAAY,OAAO;YAAa,OAAO,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,MAAM;QAAC;KAC1G;IAED,qBACE,8OAAC,8KAAA,CAAA,OAAI;QACH,OAAM;QACN,WAAW;QACX,qBACE,8OAAC;YAAK,MAAK;YAAY,WAAU;;gBAC9B,oBAAoB,MAAM;gBAAC;;;;;;;;0BAKhC,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,iBACvC,8OAAC,kMAAA,CAAA,SAAM;wBAEL,MAAK;wBACL,MAAM,WAAW,MAAM,YAAY;wBACnC,SAAS,IAAM,UAAU;;4BAExB;4BAAM;4BAAG;4BAAM;;uBALX;;;;;;;;;;0BAWX,8OAAC,8KAAA,CAAA,OAAI;gBACH,YAAY;gBACZ,QAAQ;oBAAE,WAAW;gBAAqB;gBAC1C,YAAY,CAAC,2BACX,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wBACR,WAAW,GACT,gBAAgB,WAAW,UAAU,GAAG,+BAA+B,GACxE,2DAA2D,CAAC;kCAE7D,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,kBAAkB,WAAW,IAAI;8DAClC,8OAAC,4KAAA,CAAA,MAAG;oDAAC,OAAO,mBAAmB,WAAW,IAAI;8DAC3C,WAAW,IAAI;;;;;;8DAElB,8OAAC;oDAAK,MAAK;oDAAY,WAAU;;wDAAU;wDACnC,WAAW,UAAU;;;;;;;;;;;;;sDAI/B,8OAAC,gMAAA,CAAA,QAAK;4CAAC,MAAK;;8DACV,8OAAC,oLAAA,CAAA,UAAO;oDAAC,OAAM;8DACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;wDACL,MAAK;wDACL,MAAK;wDACL,oBAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;wDAClB,SAAS,IAAM,oBAAoB;;;;;;;;;;;8DAGvC,8OAAC,oLAAA,CAAA,UAAO;oDAAC,OAAM;8DACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;wDACL,MAAK;wDACL,MAAK;wDACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wDACnB,SAAS,IAAM,WAAW;;;;;;;;;;;8DAG9B,8OAAC,0LAAA,CAAA,aAAU;oDACT,OAAM;oDACN,aAAY;oDACZ,WAAW,IAAM,aAAa,WAAW,EAAE;oDAC3C,QAAO;oDACP,YAAW;oDACX,eAAe;wDAAE,QAAQ;oDAAK;8DAE9B,cAAA,8OAAC,oLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,8OAAC,kMAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAK;4DACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;4DACrB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAOf,cAAc,WAAW,EAAE,iBAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,MAAM;4CACN,aAAY;;;;;;sDAEd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,SAAM;oDAAC,MAAK;oDAAQ,SAAS;8DAAkB;;;;;;8DAGhD,8OAAC,kMAAA,CAAA,SAAM;oDACL,MAAK;oDACL,MAAK;oDACL,SAAS,IAAM,eAAe,WAAW,EAAE;8DAC5C;;;;;;;;;;;;;;;;;2DAML,8OAAC;oCACC,WAAU;oCACV,UAAU;wCAAE,MAAM;wCAAG,YAAY;wCAAM,QAAQ;oCAAO;8CAErD,WAAW,OAAO;;;;;;8CAIvB,8OAAC;oCAAK,MAAK;oCAAY,WAAU;8CAC9B,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D;uCAEe", "debugId": null}}, {"offset": {"line": 5753, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/views/ReaderView.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Button, Typography, Empty, Tabs } from 'antd';\nimport { BookOutlined, ArrowLeftOutlined, MessageOutlined, HighlightOutlined } from '@ant-design/icons';\nimport { useAppStore } from '@/store/useAppStore';\nimport { VIEW_MODES } from '@/lib/constants';\nimport PDFViewer from '@/components/pdf/PDFViewer';\nimport ChatInterface from '@/components/chat/ChatInterface';\nimport QuickActions from '@/components/chat/QuickActions';\nimport AnnotationPanel from '@/components/pdf/AnnotationPanel';\nimport { Annotation } from '@/lib/types';\n\nconst { Title, Text } = Typography;\n\nconst ReaderView: React.FC = () => {\n  const { currentPaper, setCurrentView } = useAppStore();\n  const [activeTab, setActiveTab] = useState('chat');\n\n  const handleBackToLibrary = () => {\n    setCurrentView(VIEW_MODES.LIBRARY);\n  };\n\n  const handleAnnotationClick = (annotation: Annotation) => {\n    // In a real implementation, you would scroll to the annotation\n    console.log('Navigate to annotation:', annotation);\n  };\n\n  if (!currentPaper) {\n    return (\n      <div className=\"p-6 h-full flex items-center justify-center\">\n        <Empty\n          image={<BookOutlined className=\"text-6xl text-gray-400\" />}\n          description=\"No paper selected\"\n        >\n          <Button type=\"primary\" onClick={handleBackToLibrary}>\n            Go to Library\n          </Button>\n        </Empty>\n      </div>\n    );\n  }\n\n  const sidebarTabs = [\n    {\n      key: 'chat',\n      label: (\n        <span>\n          <MessageOutlined />\n          Chat\n        </span>\n      ),\n      children: (\n        <div className=\"h-full flex flex-col\">\n          <div className=\"flex-1\">\n            <ChatInterface paper={currentPaper} />\n          </div>\n          <div className=\"mt-4\">\n            <QuickActions paper={currentPaper} />\n          </div>\n        </div>\n      ),\n    },\n    {\n      key: 'annotations',\n      label: (\n        <span>\n          <HighlightOutlined />\n          Notes\n        </span>\n      ),\n      children: (\n        <AnnotationPanel\n          paperId={currentPaper.id}\n          onAnnotationClick={handleAnnotationClick}\n          className=\"h-full\"\n        />\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"h-full flex\">\n      {/* PDF Viewer Side */}\n      <div className=\"flex-1 bg-gray-50 p-4\">\n        <div className=\"mb-4\">\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={handleBackToLibrary}\n            className=\"mb-2\"\n          >\n            Back to Library\n          </Button>\n          <Title level={4} className=\"m-0 mb-1\">\n            {currentPaper.title}\n          </Title>\n          <Text type=\"secondary\">\n            by {currentPaper.authors.join(', ')}\n          </Text>\n        </div>\n\n        <PDFViewer\n          paper={currentPaper}\n          className=\"h-full\"\n          onAnnotationCreate={(annotation) => {\n            console.log('New annotation created:', annotation);\n          }}\n        />\n      </div>\n\n      {/* Sidebar */}\n      <div className=\"w-96 border-l bg-white\">\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={sidebarTabs}\n          className=\"h-full\"\n          tabBarStyle={{\n            margin: 0,\n            padding: '0 16px',\n            borderBottom: '1px solid #f0f0f0'\n          }}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default ReaderView;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAaA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,aAAuB;IAC3B,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,sBAAsB;QAC1B,eAAe,uHAAA,CAAA,aAAU,CAAC,OAAO;IACnC;IAEA,MAAM,wBAAwB,CAAC;QAC7B,+DAA+D;QAC/D,QAAQ,GAAG,CAAC,2BAA2B;IACzC;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gLAAA,CAAA,QAAK;gBACJ,qBAAO,8OAAC,kNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;gBAC/B,aAAY;0BAEZ,cAAA,8OAAC,kMAAA,CAAA,SAAM;oBAAC,MAAK;oBAAU,SAAS;8BAAqB;;;;;;;;;;;;;;;;IAM7D;IAEA,MAAM,cAAc;QAClB;YACE,KAAK;YACL,qBACE,8OAAC;;kCACC,8OAAC,wNAAA,CAAA,kBAAe;;;;;oBAAG;;;;;;;YAIvB,wBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2IAAA,CAAA,UAAa;4BAAC,OAAO;;;;;;;;;;;kCAExB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0IAAA,CAAA,UAAY;4BAAC,OAAO;;;;;;;;;;;;;;;;;QAI7B;QACA;YACE,KAAK;YACL,qBACE,8OAAC;;kCACC,8OAAC,4NAAA,CAAA,oBAAiB;;;;;oBAAG;;;;;;;YAIzB,wBACE,8OAAC,4IAAA,CAAA,UAAe;gBACd,SAAS,aAAa,EAAE;gBACxB,mBAAmB;gBACnB,WAAU;;;;;;QAGhB;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,SAAM;gCACL,oBAAM,8OAAC,4NAAA,CAAA,oBAAiB;;;;;gCACxB,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAM,OAAO;gCAAG,WAAU;0CACxB,aAAa,KAAK;;;;;;0CAErB,8OAAC;gCAAK,MAAK;;oCAAY;oCACjB,aAAa,OAAO,CAAC,IAAI,CAAC;;;;;;;;;;;;;kCAIlC,8OAAC,sIAAA,CAAA,UAAS;wBACR,OAAO;wBACP,WAAU;wBACV,oBAAoB,CAAC;4BACnB,QAAQ,GAAG,CAAC,2BAA2B;wBACzC;;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8KAAA,CAAA,OAAI;oBACH,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,WAAU;oBACV,aAAa;wBACX,QAAQ;wBACR,SAAS;wBACT,cAAc;oBAChB;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 6008, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/views/ComparisonView.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { <PERSON>, Typography, Button, Empty } from 'antd';\nimport { SwapOutlined } from '@ant-design/icons';\nimport { useAppStore } from '@/store/useAppStore';\nimport { VIEW_MODES } from '@/lib/constants';\n\nconst { Title } = Typography;\n\nconst ComparisonView: React.FC = () => {\n  const { setCurrentView } = useAppStore();\n\n  const handleGoToLibrary = () => {\n    setCurrentView(VIEW_MODES.LIBRARY);\n  };\n\n  return (\n    <div className=\"p-6 h-full\">\n      <Title level={2} className=\"mb-6\">\n        Paper Comparison\n      </Title>\n      \n      <Card className=\"h-full\">\n        <div className=\"flex items-center justify-center h-full\">\n          <Empty\n            image={<SwapOutlined className=\"text-6xl text-gray-400\" />}\n            description=\"Paper comparison feature coming soon\"\n          >\n            <Button type=\"primary\" onClick={handleGoToLibrary}>\n              Go to Library\n            </Button>\n          </Empty>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default ComparisonView;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;AAQA,MAAM,EAAE,KAAK,EAAE,GAAG,0LAAA,CAAA,aAAU;AAE5B,MAAM,iBAA2B;IAC/B,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAErC,MAAM,oBAAoB;QACxB,eAAe,uHAAA,CAAA,aAAU,CAAC,OAAO;IACnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAM,OAAO;gBAAG,WAAU;0BAAO;;;;;;0BAIlC,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gLAAA,CAAA,QAAK;wBACJ,qBAAO,8OAAC,kNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B,aAAY;kCAEZ,cAAA,8OAAC,kMAAA,CAAA,SAAM;4BAAC,MAAK;4BAAU,SAAS;sCAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D;uCAEe", "debugId": null}}, {"offset": {"line": 6092, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/components/views/AnalysisView.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { <PERSON>, Typo<PERSON>, Button, Empty } from 'antd';\nimport { BarChartOutlined } from '@ant-design/icons';\nimport { useAppStore } from '@/store/useAppStore';\nimport { VIEW_MODES } from '@/lib/constants';\n\nconst { Title } = Typography;\n\nconst AnalysisView: React.FC = () => {\n  const { setCurrentView } = useAppStore();\n\n  const handleGoToLibrary = () => {\n    setCurrentView(VIEW_MODES.LIBRARY);\n  };\n\n  return (\n    <div className=\"p-6 h-full\">\n      <Title level={2} className=\"mb-6\">\n        Paper Analysis\n      </Title>\n      \n      <Card className=\"h-full\">\n        <div className=\"flex items-center justify-center h-full\">\n          <Empty\n            image={<BarChartOutlined className=\"text-6xl text-gray-400\" />}\n            description=\"Advanced analysis features coming soon\"\n          >\n            <Button type=\"primary\" onClick={handleGoToLibrary}>\n              Go to Library\n            </Button>\n          </Empty>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default AnalysisView;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;AAQA,MAAM,EAAE,KAAK,EAAE,GAAG,0LAAA,CAAA,aAAU;AAE5B,MAAM,eAAyB;IAC7B,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAErC,MAAM,oBAAoB;QACxB,eAAe,uHAAA,CAAA,aAAU,CAAC,OAAO;IACnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAM,OAAO;gBAAG,WAAU;0BAAO;;;;;;0BAIlC,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gLAAA,CAAA,QAAK;wBACJ,qBAAO,8OAAC,0NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;wBACnC,aAAY;kCAEZ,cAAA,8OAAC,kMAAA,CAAA,SAAM;4BAAC,MAAK;4BAAU,SAAS;sCAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D;uCAEe", "debugId": null}}, {"offset": {"line": 6176, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport LibraryView from '@/components/views/LibraryView';\nimport ReaderView from '@/components/views/ReaderView';\nimport ComparisonView from '@/components/views/ComparisonView';\nimport AnalysisView from '@/components/views/AnalysisView';\nimport { useAppStore } from '@/store/useAppStore';\nimport { VIEW_MODES } from '@/lib/constants';\n\nexport default function Home() {\n  const { currentView } = useAppStore();\n\n  const renderCurrentView = () => {\n    switch (currentView) {\n      case VIEW_MODES.LIBRARY:\n        return <LibraryView />;\n      case VIEW_MODES.READER:\n        return <ReaderView />;\n      case VIEW_MODES.COMPARISON:\n        return <ComparisonView />;\n      case VIEW_MODES.ANALYSIS:\n        return <AnalysisView />;\n      default:\n        return <LibraryView />;\n    }\n  };\n\n  return (\n    <MainLayout>\n      {renderCurrentView()}\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAElC,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK,uHAAA,CAAA,aAAU,CAAC,OAAO;gBACrB,qBAAO,8OAAC,0IAAA,CAAA,UAAW;;;;;YACrB,KAAK,uHAAA,CAAA,aAAU,CAAC,MAAM;gBACpB,qBAAO,8OAAC,yIAAA,CAAA,UAAU;;;;;YACpB,KAAK,uHAAA,CAAA,aAAU,CAAC,UAAU;gBACxB,qBAAO,8OAAC,6IAAA,CAAA,UAAc;;;;;YACxB,KAAK,uHAAA,CAAA,aAAU,CAAC,QAAQ;gBACtB,qBAAO,8OAAC,2IAAA,CAAA,UAAY;;;;;YACtB;gBACE,qBAAO,8OAAC,0IAAA,CAAA,UAAW;;;;;QACvB;IACF;IAEA,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACR;;;;;;AAGP", "debugId": null}}]}
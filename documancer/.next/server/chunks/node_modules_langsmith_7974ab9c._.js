module.exports = {

"[project]/node_modules/langsmith/dist/singletons/traceable.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AsyncLocalStorageProviderSingleton": ()=>AsyncLocalStorageProviderSingleton,
    "ROOT": ()=>ROOT,
    "getCurrentRunTree": ()=>getCurrentRunTree,
    "isTraceableFunction": ()=>isTraceableFunction,
    "withRunTree": ()=>withRunTree
});
class MockAsyncLocalStorage {
    getStore() {
        return undefined;
    }
    run(_, callback) {
        return callback();
    }
}
const TRACING_ALS_KEY = Symbol.for("ls:tracing_async_local_storage");
const mockAsyncLocalStorage = new MockAsyncLocalStorage();
class AsyncLocalStorageProvider {
    getInstance() {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return globalThis[TRACING_ALS_KEY] ?? mockAsyncLocalStorage;
    }
    initializeGlobalInstance(instance) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        if (globalThis[TRACING_ALS_KEY] === undefined) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            globalThis[TRACING_ALS_KEY] = instance;
        }
    }
}
const AsyncLocalStorageProviderSingleton = new AsyncLocalStorageProvider();
function getCurrentRunTree(permitAbsentRunTree = false) {
    const runTree = AsyncLocalStorageProviderSingleton.getInstance().getStore();
    if (!permitAbsentRunTree && runTree === undefined) {
        throw new Error("Could not get the current run tree.\n\nPlease make sure you are calling this method within a traceable function and that tracing is enabled.");
    }
    return runTree;
}
function withRunTree(runTree, fn) {
    const storage = AsyncLocalStorageProviderSingleton.getInstance();
    return new Promise((resolve, reject)=>{
        storage.run(runTree, ()=>void Promise.resolve(fn()).then(resolve).catch(reject));
    });
}
const ROOT = Symbol.for("langsmith:traceable:root");
function isTraceableFunction(x) {
    return typeof x === "function" && "langsmith:traceable" in x;
}
}),
"[project]/node_modules/langsmith/singletons/traceable.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$traceable$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/singletons/traceable.js [app-route] (ecmascript)");
;
}),
"[project]/node_modules/langsmith/singletons/traceable.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$traceable$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/singletons/traceable.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$singletons$2f$traceable$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/langsmith/singletons/traceable.js [app-route] (ecmascript) <locals>");
}),
"[project]/node_modules/langsmith/dist/experimental/otel/constants.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// OpenTelemetry GenAI semantic convention attribute names
__turbopack_context__.s({
    "AI_SDK_LLM_OPERATIONS": ()=>AI_SDK_LLM_OPERATIONS,
    "AI_SDK_TOOL_OPERATIONS": ()=>AI_SDK_TOOL_OPERATIONS,
    "GENAI_COMPLETION": ()=>GENAI_COMPLETION,
    "GENAI_PROMPT": ()=>GENAI_PROMPT,
    "GEN_AI_ASSISTANT_MESSAGE": ()=>GEN_AI_ASSISTANT_MESSAGE,
    "GEN_AI_CHOICE": ()=>GEN_AI_CHOICE,
    "GEN_AI_OPERATION_NAME": ()=>GEN_AI_OPERATION_NAME,
    "GEN_AI_REQUEST_EXTRA_BODY": ()=>GEN_AI_REQUEST_EXTRA_BODY,
    "GEN_AI_REQUEST_EXTRA_QUERY": ()=>GEN_AI_REQUEST_EXTRA_QUERY,
    "GEN_AI_REQUEST_FREQUENCY_PENALTY": ()=>GEN_AI_REQUEST_FREQUENCY_PENALTY,
    "GEN_AI_REQUEST_MAX_TOKENS": ()=>GEN_AI_REQUEST_MAX_TOKENS,
    "GEN_AI_REQUEST_MODEL": ()=>GEN_AI_REQUEST_MODEL,
    "GEN_AI_REQUEST_PRESENCE_PENALTY": ()=>GEN_AI_REQUEST_PRESENCE_PENALTY,
    "GEN_AI_REQUEST_TEMPERATURE": ()=>GEN_AI_REQUEST_TEMPERATURE,
    "GEN_AI_REQUEST_TOP_P": ()=>GEN_AI_REQUEST_TOP_P,
    "GEN_AI_RESPONSE_FINISH_REASONS": ()=>GEN_AI_RESPONSE_FINISH_REASONS,
    "GEN_AI_RESPONSE_ID": ()=>GEN_AI_RESPONSE_ID,
    "GEN_AI_RESPONSE_MODEL": ()=>GEN_AI_RESPONSE_MODEL,
    "GEN_AI_RESPONSE_SERVICE_TIER": ()=>GEN_AI_RESPONSE_SERVICE_TIER,
    "GEN_AI_RESPONSE_SYSTEM_FINGERPRINT": ()=>GEN_AI_RESPONSE_SYSTEM_FINGERPRINT,
    "GEN_AI_SERIALIZED_DOC": ()=>GEN_AI_SERIALIZED_DOC,
    "GEN_AI_SERIALIZED_NAME": ()=>GEN_AI_SERIALIZED_NAME,
    "GEN_AI_SERIALIZED_SIGNATURE": ()=>GEN_AI_SERIALIZED_SIGNATURE,
    "GEN_AI_SYSTEM": ()=>GEN_AI_SYSTEM,
    "GEN_AI_SYSTEM_MESSAGE": ()=>GEN_AI_SYSTEM_MESSAGE,
    "GEN_AI_USAGE_INPUT_TOKENS": ()=>GEN_AI_USAGE_INPUT_TOKENS,
    "GEN_AI_USAGE_INPUT_TOKEN_DETAILS": ()=>GEN_AI_USAGE_INPUT_TOKEN_DETAILS,
    "GEN_AI_USAGE_OUTPUT_TOKENS": ()=>GEN_AI_USAGE_OUTPUT_TOKENS,
    "GEN_AI_USAGE_OUTPUT_TOKEN_DETAILS": ()=>GEN_AI_USAGE_OUTPUT_TOKEN_DETAILS,
    "GEN_AI_USAGE_TOTAL_TOKENS": ()=>GEN_AI_USAGE_TOTAL_TOKENS,
    "GEN_AI_USER_MESSAGE": ()=>GEN_AI_USER_MESSAGE,
    "LANGSMITH_DOTTED_ORDER": ()=>LANGSMITH_DOTTED_ORDER,
    "LANGSMITH_METADATA": ()=>LANGSMITH_METADATA,
    "LANGSMITH_NAME": ()=>LANGSMITH_NAME,
    "LANGSMITH_PARENT_RUN_ID": ()=>LANGSMITH_PARENT_RUN_ID,
    "LANGSMITH_REFERENCE_EXAMPLE_ID": ()=>LANGSMITH_REFERENCE_EXAMPLE_ID,
    "LANGSMITH_REQUEST_HEADERS": ()=>LANGSMITH_REQUEST_HEADERS,
    "LANGSMITH_REQUEST_STREAMING": ()=>LANGSMITH_REQUEST_STREAMING,
    "LANGSMITH_RUNTIME": ()=>LANGSMITH_RUNTIME,
    "LANGSMITH_RUN_ID": ()=>LANGSMITH_RUN_ID,
    "LANGSMITH_RUN_TYPE": ()=>LANGSMITH_RUN_TYPE,
    "LANGSMITH_SESSION_ID": ()=>LANGSMITH_SESSION_ID,
    "LANGSMITH_SESSION_NAME": ()=>LANGSMITH_SESSION_NAME,
    "LANGSMITH_TAGS": ()=>LANGSMITH_TAGS,
    "LANGSMITH_TRACE_ID": ()=>LANGSMITH_TRACE_ID,
    "LANGSMITH_USAGE_METADATA": ()=>LANGSMITH_USAGE_METADATA
});
const GEN_AI_OPERATION_NAME = "gen_ai.operation.name";
const GEN_AI_SYSTEM = "gen_ai.system";
const GEN_AI_REQUEST_MODEL = "gen_ai.request.model";
const GEN_AI_RESPONSE_MODEL = "gen_ai.response.model";
const GEN_AI_USAGE_INPUT_TOKENS = "gen_ai.usage.input_tokens";
const GEN_AI_USAGE_OUTPUT_TOKENS = "gen_ai.usage.output_tokens";
const GEN_AI_USAGE_TOTAL_TOKENS = "gen_ai.usage.total_tokens";
const GEN_AI_REQUEST_MAX_TOKENS = "gen_ai.request.max_tokens";
const GEN_AI_REQUEST_TEMPERATURE = "gen_ai.request.temperature";
const GEN_AI_REQUEST_TOP_P = "gen_ai.request.top_p";
const GEN_AI_REQUEST_FREQUENCY_PENALTY = "gen_ai.request.frequency_penalty";
const GEN_AI_REQUEST_PRESENCE_PENALTY = "gen_ai.request.presence_penalty";
const GEN_AI_RESPONSE_FINISH_REASONS = "gen_ai.response.finish_reasons";
const GENAI_PROMPT = "gen_ai.prompt";
const GENAI_COMPLETION = "gen_ai.completion";
const GEN_AI_REQUEST_EXTRA_QUERY = "gen_ai.request.extra_query";
const GEN_AI_REQUEST_EXTRA_BODY = "gen_ai.request.extra_body";
const GEN_AI_SERIALIZED_NAME = "gen_ai.serialized.name";
const GEN_AI_SERIALIZED_SIGNATURE = "gen_ai.serialized.signature";
const GEN_AI_SERIALIZED_DOC = "gen_ai.serialized.doc";
const GEN_AI_RESPONSE_ID = "gen_ai.response.id";
const GEN_AI_RESPONSE_SERVICE_TIER = "gen_ai.response.service_tier";
const GEN_AI_RESPONSE_SYSTEM_FINGERPRINT = "gen_ai.response.system_fingerprint";
const GEN_AI_USAGE_INPUT_TOKEN_DETAILS = "gen_ai.usage.input_token_details";
const GEN_AI_USAGE_OUTPUT_TOKEN_DETAILS = "gen_ai.usage.output_token_details";
const LANGSMITH_SESSION_ID = "langsmith.trace.session_id";
const LANGSMITH_SESSION_NAME = "langsmith.trace.session_name";
const LANGSMITH_RUN_TYPE = "langsmith.span.kind";
const LANGSMITH_NAME = "langsmith.trace.name";
const LANGSMITH_METADATA = "langsmith.metadata";
const LANGSMITH_TAGS = "langsmith.span.tags";
const LANGSMITH_RUNTIME = "langsmith.span.runtime";
const LANGSMITH_REQUEST_STREAMING = "langsmith.request.streaming";
const LANGSMITH_REQUEST_HEADERS = "langsmith.request.headers";
const LANGSMITH_RUN_ID = "langsmith.span.id";
const LANGSMITH_TRACE_ID = "langsmith.trace.id";
const LANGSMITH_DOTTED_ORDER = "langsmith.span.dotted_order";
const LANGSMITH_PARENT_RUN_ID = "langsmith.span.parent_id";
const LANGSMITH_USAGE_METADATA = "langsmith.usage_metadata";
const LANGSMITH_REFERENCE_EXAMPLE_ID = "langsmith.reference_example_id";
const GEN_AI_SYSTEM_MESSAGE = "gen_ai.system.message";
const GEN_AI_USER_MESSAGE = "gen_ai.user.message";
const GEN_AI_ASSISTANT_MESSAGE = "gen_ai.assistant.message";
const GEN_AI_CHOICE = "gen_ai.choice";
const AI_SDK_LLM_OPERATIONS = [
    "ai.generateText.doGenerate",
    "ai.streamText.doStream",
    "ai.generateObject.doGenerate",
    "ai.streamObject.doStream"
];
const AI_SDK_TOOL_OPERATIONS = [
    "ai.toolCall"
];
}),
"[project]/node_modules/langsmith/dist/singletons/fetch.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "_getFetchImplementation": ()=>_getFetchImplementation,
    "_globalFetchImplementationIsNodeFetch": ()=>_globalFetchImplementationIsNodeFetch,
    "overrideFetchImplementation": ()=>overrideFetchImplementation
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/env.js [app-route] (ecmascript)");
;
// Wrap the default fetch call due to issues with illegal invocations
// in some environments:
// https://stackoverflow.com/questions/69876859/why-does-bind-fix-failed-to-execute-fetch-on-window-illegal-invocation-err
// @ts-expect-error Broad typing to support a range of fetch implementations
const DEFAULT_FETCH_IMPLEMENTATION = (...args)=>fetch(...args);
const LANGSMITH_FETCH_IMPLEMENTATION_KEY = Symbol.for("ls:fetch_implementation");
const overrideFetchImplementation = (fetch1)=>{
    globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] = fetch1;
};
const _globalFetchImplementationIsNodeFetch = ()=>{
    const fetchImpl = globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY];
    if (!fetchImpl) return false;
    // Check if the implementation has node-fetch specific properties
    return typeof fetchImpl === "function" && "Headers" in fetchImpl && "Request" in fetchImpl && "Response" in fetchImpl;
};
const _getFetchImplementation = (debug)=>{
    return async (...args)=>{
        if (debug || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("DEBUG") === "true") {
            const [url, options] = args;
            console.log(`→ ${options?.method || "GET"} ${url}`);
        }
        const res = await (globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] ?? DEFAULT_FETCH_IMPLEMENTATION)(...args);
        if (debug || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("DEBUG") === "true") {
            console.log(`← ${res.status} ${res.statusText} ${res.url}`);
        }
        return res;
    };
};
}),
"[project]/node_modules/langsmith/dist/utils/project.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getDefaultProjectName": ()=>getDefaultProjectName
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/env.js [app-route] (ecmascript)");
;
const getDefaultProjectName = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("PROJECT") ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("LANGCHAIN_SESSION") ?? // TODO: Deprecate
    "default";
};
}),
"[project]/node_modules/langsmith/dist/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "__version__": ()=>__version__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$client$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/client.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$run_trees$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/run_trees.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/singletons/fetch.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$project$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/project.js [app-route] (ecmascript)");
;
;
;
;
const __version__ = "0.3.46";
}),
"[project]/node_modules/langsmith/dist/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$client$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/client.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$run_trees$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/run_trees.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/singletons/fetch.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$project$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/project.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/index.js [app-route] (ecmascript) <locals>");
}),
"[project]/node_modules/langsmith/dist/utils/env.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Inlined from https://github.com/flexdinesh/browser-or-node
__turbopack_context__.s({
    "getEnv": ()=>getEnv,
    "getEnvironmentVariable": ()=>getEnvironmentVariable,
    "getEnvironmentVariables": ()=>getEnvironmentVariables,
    "getLangChainEnvVars": ()=>getLangChainEnvVars,
    "getLangChainEnvVarsMetadata": ()=>getLangChainEnvVarsMetadata,
    "getLangSmithEnvironmentVariable": ()=>getLangSmithEnvironmentVariable,
    "getRuntimeEnvironment": ()=>getRuntimeEnvironment,
    "getShas": ()=>getShas,
    "isBrowser": ()=>isBrowser,
    "isDeno": ()=>isDeno,
    "isJsDom": ()=>isJsDom,
    "isNode": ()=>isNode,
    "isWebWorker": ()=>isWebWorker,
    "setEnvironmentVariable": ()=>setEnvironmentVariable
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/index.js [app-route] (ecmascript) <locals>");
;
let globalEnv;
const isBrowser = ()=>"undefined" !== "undefined" && typeof window.document !== "undefined";
const isWebWorker = ()=>typeof globalThis === "object" && globalThis.constructor && globalThis.constructor.name === "DedicatedWorkerGlobalScope";
const isJsDom = ()=>"undefined" !== "undefined" && window.name === "nodejs" || typeof navigator !== "undefined" && navigator.userAgent.includes("jsdom");
const isDeno = ()=>typeof Deno !== "undefined";
const isNode = ()=>typeof process !== "undefined" && typeof process.versions !== "undefined" && typeof process.versions.node !== "undefined" && !isDeno();
const getEnv = ()=>{
    if (globalEnv) {
        return globalEnv;
    }
    if (isBrowser()) {
        globalEnv = "browser";
    } else if (isNode()) {
        globalEnv = "node";
    } else if (isWebWorker()) {
        globalEnv = "webworker";
    } else if (isJsDom()) {
        globalEnv = "jsdom";
    } else if (isDeno()) {
        globalEnv = "deno";
    } else {
        globalEnv = "other";
    }
    return globalEnv;
};
let runtimeEnvironment;
function getRuntimeEnvironment() {
    if (runtimeEnvironment === undefined) {
        const env = getEnv();
        const releaseEnv = getShas();
        runtimeEnvironment = {
            library: "langsmith",
            runtime: env,
            sdk: "langsmith-js",
            sdk_version: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["__version__"],
            ...releaseEnv
        };
    }
    return runtimeEnvironment;
}
function getLangChainEnvVars() {
    const allEnvVars = getEnvironmentVariables() || {};
    const envVars = {};
    for (const [key, value] of Object.entries(allEnvVars)){
        if (key.startsWith("LANGCHAIN_") && typeof value === "string") {
            envVars[key] = value;
        }
    }
    for(const key in envVars){
        if ((key.toLowerCase().includes("key") || key.toLowerCase().includes("secret") || key.toLowerCase().includes("token")) && typeof envVars[key] === "string") {
            const value = envVars[key];
            envVars[key] = value.slice(0, 2) + "*".repeat(value.length - 4) + value.slice(-2);
        }
    }
    return envVars;
}
function getLangChainEnvVarsMetadata() {
    const allEnvVars = getEnvironmentVariables() || {};
    const envVars = {};
    const excluded = [
        "LANGCHAIN_API_KEY",
        "LANGCHAIN_ENDPOINT",
        "LANGCHAIN_TRACING_V2",
        "LANGCHAIN_PROJECT",
        "LANGCHAIN_SESSION",
        "LANGSMITH_API_KEY",
        "LANGSMITH_ENDPOINT",
        "LANGSMITH_TRACING_V2",
        "LANGSMITH_PROJECT",
        "LANGSMITH_SESSION"
    ];
    for (const [key, value] of Object.entries(allEnvVars)){
        if ((key.startsWith("LANGCHAIN_") || key.startsWith("LANGSMITH_")) && typeof value === "string" && !excluded.includes(key) && !key.toLowerCase().includes("key") && !key.toLowerCase().includes("secret") && !key.toLowerCase().includes("token")) {
            if (key === "LANGCHAIN_REVISION_ID") {
                envVars["revision_id"] = value;
            } else {
                envVars[key] = value;
            }
        }
    }
    return envVars;
}
function getEnvironmentVariables() {
    try {
        // Check for Node.js environment
        // eslint-disable-next-line no-process-env
        if (typeof process !== "undefined" && process.env) {
            // eslint-disable-next-line no-process-env
            return Object.entries(process.env).reduce((acc, [key, value])=>{
                acc[key] = String(value);
                return acc;
            }, {});
        }
        // For browsers and other environments, we may not have direct access to env variables
        // Return undefined or any other fallback as required.
        return undefined;
    } catch (e) {
        // Catch any errors that might occur while trying to access environment variables
        return undefined;
    }
}
function getEnvironmentVariable(name) {
    // Certain Deno setups will throw an error if you try to access environment variables
    // https://github.com/hwchase17/langchainjs/issues/1412
    try {
        return typeof process !== "undefined" ? process.env?.[name] : undefined;
    } catch (e) {
        return undefined;
    }
}
function getLangSmithEnvironmentVariable(name) {
    return getEnvironmentVariable(`LANGSMITH_${name}`) || getEnvironmentVariable(`LANGCHAIN_${name}`);
}
function setEnvironmentVariable(name, value) {
    if (typeof process !== "undefined") {
        // eslint-disable-next-line no-process-env
        process.env[name] = value;
    }
}
let cachedCommitSHAs;
function getShas() {
    if (cachedCommitSHAs !== undefined) {
        return cachedCommitSHAs;
    }
    const common_release_envs = [
        "VERCEL_GIT_COMMIT_SHA",
        "NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA",
        "COMMIT_REF",
        "RENDER_GIT_COMMIT",
        "CI_COMMIT_SHA",
        "CIRCLE_SHA1",
        "CF_PAGES_COMMIT_SHA",
        "REACT_APP_GIT_SHA",
        "SOURCE_VERSION",
        "GITHUB_SHA",
        "TRAVIS_COMMIT",
        "GIT_COMMIT",
        "BUILD_VCS_NUMBER",
        "bamboo_planRepository_revision",
        "Build.SourceVersion",
        "BITBUCKET_COMMIT",
        "DRONE_COMMIT_SHA",
        "SEMAPHORE_GIT_SHA",
        "BUILDKITE_COMMIT"
    ];
    const shas = {};
    for (const env of common_release_envs){
        const envVar = getEnvironmentVariable(env);
        if (envVar !== undefined) {
            shas[env] = envVar;
        }
    }
    cachedCommitSHAs = shas;
    return shas;
}
}),
"[project]/node_modules/langsmith/dist/singletons/otel.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Should not import any OTEL packages to avoid pulling in optional deps.
__turbopack_context__.s({
    "OTELProviderSingleton": ()=>OTELProviderSingleton,
    "getDefaultOTLPTracerComponents": ()=>getDefaultOTLPTracerComponents,
    "getOTELContext": ()=>getOTELContext,
    "getOTELTrace": ()=>getOTELTrace,
    "setDefaultOTLPTracerComponents": ()=>setDefaultOTLPTracerComponents,
    "setOTELInstances": ()=>setOTELInstances
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/env.js [app-route] (ecmascript)");
;
class MockTracer {
    constructor(){
        Object.defineProperty(this, "hasWarned", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
    }
    startActiveSpan(_name, ...args) {
        if (!this.hasWarned && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("OTEL_ENABLED") === "true") {
            console.warn("You have enabled OTEL export via the `OTEL_ENABLED` environment variable, but have not initialized the required OTEL instances. " + 'Please add:\n```\nimport { initializeOTEL } from "langsmith/experimental/otel/setup";\ninitializeOTEL();\n```\nat the beginning of your code.');
            this.hasWarned = true;
        }
        // Handle different overloads:
        // startActiveSpan(name, fn)
        // startActiveSpan(name, options, fn)
        // startActiveSpan(name, options, context, fn)
        let fn;
        if (args.length === 1 && typeof args[0] === "function") {
            fn = args[0];
        } else if (args.length === 2 && typeof args[1] === "function") {
            fn = args[1];
        } else if (args.length === 3 && typeof args[2] === "function") {
            fn = args[2];
        }
        if (typeof fn === "function") {
            return fn();
        }
        return undefined;
    }
}
class MockOTELTrace {
    constructor(){
        Object.defineProperty(this, "mockTracer", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new MockTracer()
        });
    }
    getTracer(_name, _version) {
        return this.mockTracer;
    }
    getActiveSpan() {
        return undefined;
    }
    setSpan(context, _span) {
        return context;
    }
    getSpan(_context) {
        return undefined;
    }
    setSpanContext(context, _spanContext) {
        return context;
    }
    getTracerProvider() {
        return undefined;
    }
    setGlobalTracerProvider(_tracerProvider) {
        return false;
    }
}
class MockOTELContext {
    active() {
        return {};
    }
    with(_context, fn) {
        return fn();
    }
}
const OTEL_TRACE_KEY = Symbol.for("ls:otel_trace");
const OTEL_CONTEXT_KEY = Symbol.for("ls:otel_context");
const OTEL_GET_DEFAULT_OTLP_TRACER_PROVIDER_KEY = Symbol.for("ls:otel_get_default_otlp_tracer_provider");
const mockOTELTrace = new MockOTELTrace();
const mockOTELContext = new MockOTELContext();
class OTELProvider {
    getTraceInstance() {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return globalThis[OTEL_TRACE_KEY] ?? mockOTELTrace;
    }
    getContextInstance() {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return globalThis[OTEL_CONTEXT_KEY] ?? mockOTELContext;
    }
    initializeGlobalInstances(otel) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        if (globalThis[OTEL_TRACE_KEY] === undefined) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            globalThis[OTEL_TRACE_KEY] = otel.trace;
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        if (globalThis[OTEL_CONTEXT_KEY] === undefined) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            globalThis[OTEL_CONTEXT_KEY] = otel.context;
        }
    }
    setDefaultOTLPTracerComponents(components) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        globalThis[OTEL_GET_DEFAULT_OTLP_TRACER_PROVIDER_KEY] = components;
    }
    getDefaultOTLPTracerComponents() {
        return globalThis[OTEL_GET_DEFAULT_OTLP_TRACER_PROVIDER_KEY] ?? undefined;
    }
}
const OTELProviderSingleton = new OTELProvider();
function getOTELTrace() {
    return OTELProviderSingleton.getTraceInstance();
}
function getOTELContext() {
    return OTELProviderSingleton.getContextInstance();
}
function setOTELInstances(otel) {
    OTELProviderSingleton.initializeGlobalInstances(otel);
}
function setDefaultOTLPTracerComponents(components) {
    OTELProviderSingleton.setDefaultOTLPTracerComponents(components);
}
function getDefaultOTLPTracerComponents() {
    return OTELProviderSingleton.getDefaultOTLPTracerComponents();
}
}),
"[project]/node_modules/langsmith/dist/experimental/otel/translator.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "LangSmithToOTELTranslator": ()=>LangSmithToOTELTranslator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/experimental/otel/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$otel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/singletons/otel.js [app-route] (ecmascript)");
;
;
const WELL_KNOWN_OPERATION_NAMES = {
    llm: "chat",
    tool: "execute_tool",
    retriever: "embeddings",
    embedding: "embeddings",
    prompt: "chat"
};
function getOperationName(runType) {
    return WELL_KNOWN_OPERATION_NAMES[runType] || runType;
}
class LangSmithToOTELTranslator {
    constructor(){
        Object.defineProperty(this, "spans", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new Map()
        });
    }
    exportBatch(operations, otelContextMap) {
        for (const op of operations){
            try {
                if (!op.run) {
                    continue;
                }
                if (op.operation === "post") {
                    const span = this.createSpanForRun(op, op.run, otelContextMap.get(op.id));
                    if (span && !op.run.end_time) {
                        this.spans.set(op.id, span);
                    }
                } else {
                    this.updateSpanForRun(op, op.run);
                }
            } catch (e) {
                console.error(`Error processing operation ${op.id}:`, e);
            }
        }
    }
    createSpanForRun(op, runInfo, otelContext) {
        const activeSpan = otelContext && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$otel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getOTELTrace"])().getSpan(otelContext);
        if (!activeSpan) {
            return;
        }
        try {
            return this.finishSpanSetup(activeSpan, runInfo, op);
        } catch (e) {
            console.error(`Failed to create span for run ${op.id}:`, e);
            return undefined;
        }
    }
    finishSpanSetup(span, runInfo, op) {
        // Set all attributes
        this.setSpanAttributes(span, runInfo, op);
        // Set status based on error
        if (runInfo.error) {
            span.setStatus({
                code: 2
            }); // ERROR status
            span.recordException(new Error(runInfo.error));
        } else {
            span.setStatus({
                code: 1
            }); // OK status
        }
        // End the span if end_time is present
        if (runInfo.end_time) {
            span.end(new Date(runInfo.end_time));
        }
        return span;
    }
    updateSpanForRun(op, runInfo) {
        try {
            const span = this.spans.get(op.id);
            if (!span) {
                console.debug(`No span found for run ${op.id} during update`);
                return;
            }
            // Update attributes
            this.setSpanAttributes(span, runInfo, op);
            // Update status based on error
            if (runInfo.error) {
                span.setStatus({
                    code: 2
                }); // ERROR status
                span.recordException(new Error(runInfo.error));
            } else {
                span.setStatus({
                    code: 1
                }); // OK status
            }
            // End the span if end_time is present
            const endTime = runInfo.end_time;
            if (endTime) {
                span.end(new Date(endTime));
                this.spans.delete(op.id);
            }
        } catch (e) {
            console.error(`Failed to update span for run ${op.id}:`, e);
        }
    }
    extractModelName(runInfo) {
        // Try to get model name from metadata
        if (runInfo.extra?.metadata) {
            const metadata = runInfo.extra.metadata;
            // First check for ls_model_name in metadata
            if (metadata.ls_model_name) {
                return metadata.ls_model_name;
            }
            // Then check invocation_params for model info
            if (metadata.invocation_params) {
                const invocationParams = metadata.invocation_params;
                if (invocationParams.model) {
                    return invocationParams.model;
                } else if (invocationParams.model_name) {
                    return invocationParams.model_name;
                }
            }
        }
        return;
    }
    setSpanAttributes(span, runInfo, op) {
        if ("run_type" in runInfo && runInfo.run_type) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LANGSMITH_RUN_TYPE"], runInfo.run_type);
            // Set GenAI attributes according to OTEL semantic conventions
            const operationName = getOperationName(runInfo.run_type || "chain");
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_OPERATION_NAME"], operationName);
        }
        if ("name" in runInfo && runInfo.name) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LANGSMITH_NAME"], runInfo.name);
        }
        if ("session_id" in runInfo && runInfo.session_id) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LANGSMITH_SESSION_ID"], runInfo.session_id);
        }
        if ("session_name" in runInfo && runInfo.session_name) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LANGSMITH_SESSION_NAME"], runInfo.session_name);
        }
        // Set gen_ai.system
        this.setGenAiSystem(span, runInfo);
        // Set model name if available
        const modelName = this.extractModelName(runInfo);
        if (modelName) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_REQUEST_MODEL"], modelName);
        }
        // Set token usage information
        if ("prompt_tokens" in runInfo && typeof runInfo.prompt_tokens === "number") {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_USAGE_INPUT_TOKENS"], runInfo.prompt_tokens);
        }
        if ("completion_tokens" in runInfo && typeof runInfo.completion_tokens === "number") {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_USAGE_OUTPUT_TOKENS"], runInfo.completion_tokens);
        }
        if ("total_tokens" in runInfo && typeof runInfo.total_tokens === "number") {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_USAGE_TOTAL_TOKENS"], runInfo.total_tokens);
        }
        // Set other parameters from invocation_params
        this.setInvocationParameters(span, runInfo);
        // Set metadata and tags if available
        const metadata = runInfo.extra?.metadata || {};
        for (const [key, value] of Object.entries(metadata)){
            if (value !== null && value !== undefined) {
                span.setAttribute(`${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LANGSMITH_METADATA"]}.${key}`, String(value));
            }
        }
        const tags = runInfo.tags;
        if (tags && Array.isArray(tags)) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LANGSMITH_TAGS"], tags.join(", "));
        } else if (tags) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LANGSMITH_TAGS"], String(tags));
        }
        // Support additional serialized attributes, if present
        if ("serialized" in runInfo && typeof runInfo.serialized === "object") {
            const serialized = runInfo.serialized;
            if (serialized.name) {
                span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_SERIALIZED_NAME"], String(serialized.name));
            }
            if (serialized.signature) {
                span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_SERIALIZED_SIGNATURE"], String(serialized.signature));
            }
            if (serialized.doc) {
                span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_SERIALIZED_DOC"], String(serialized.doc));
            }
        }
        // Set inputs/outputs if available
        this.setIOAttributes(span, op);
    }
    setGenAiSystem(span, runInfo) {
        // Default to "langchain" if we can't determine the system
        let system = "langchain";
        // Extract model name to determine the system
        const modelName = this.extractModelName(runInfo);
        if (modelName) {
            const modelLower = modelName.toLowerCase();
            if (modelLower.includes("anthropic") || modelLower.startsWith("claude")) {
                system = "anthropic";
            } else if (modelLower.includes("bedrock")) {
                system = "aws.bedrock";
            } else if (modelLower.includes("azure") && modelLower.includes("openai")) {
                system = "az.ai.openai";
            } else if (modelLower.includes("azure") && modelLower.includes("inference")) {
                system = "az.ai.inference";
            } else if (modelLower.includes("cohere")) {
                system = "cohere";
            } else if (modelLower.includes("deepseek")) {
                system = "deepseek";
            } else if (modelLower.includes("gemini")) {
                system = "gemini";
            } else if (modelLower.includes("groq")) {
                system = "groq";
            } else if (modelLower.includes("watson") || modelLower.includes("ibm")) {
                system = "ibm.watsonx.ai";
            } else if (modelLower.includes("mistral")) {
                system = "mistral_ai";
            } else if (modelLower.includes("gpt") || modelLower.includes("openai")) {
                system = "openai";
            } else if (modelLower.includes("perplexity") || modelLower.includes("sonar")) {
                system = "perplexity";
            } else if (modelLower.includes("vertex")) {
                system = "vertex_ai";
            } else if (modelLower.includes("xai") || modelLower.includes("grok")) {
                system = "xai";
            }
        }
        span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_SYSTEM"], system);
    }
    setInvocationParameters(span, runInfo) {
        if (!runInfo.extra?.metadata?.invocation_params) {
            return;
        }
        const invocationParams = runInfo.extra.metadata.invocation_params;
        // Set relevant invocation parameters
        if (invocationParams.max_tokens !== undefined) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_REQUEST_MAX_TOKENS"], invocationParams.max_tokens);
        }
        if (invocationParams.temperature !== undefined) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_REQUEST_TEMPERATURE"], invocationParams.temperature);
        }
        if (invocationParams.top_p !== undefined) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_REQUEST_TOP_P"], invocationParams.top_p);
        }
        if (invocationParams.frequency_penalty !== undefined) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_REQUEST_FREQUENCY_PENALTY"], invocationParams.frequency_penalty);
        }
        if (invocationParams.presence_penalty !== undefined) {
            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_REQUEST_PRESENCE_PENALTY"], invocationParams.presence_penalty);
        }
    }
    setIOAttributes(span, op) {
        if (op.run.inputs) {
            try {
                const inputs = op.run.inputs;
                if (typeof inputs === "object" && inputs !== null) {
                    if (inputs.model && Array.isArray(inputs.messages)) {
                        span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_REQUEST_MODEL"], inputs.model);
                    }
                    // Set additional request attributes if available
                    if (inputs.stream !== undefined) {
                        span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LANGSMITH_REQUEST_STREAMING"], inputs.stream);
                    }
                    if (inputs.extra_headers) {
                        span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LANGSMITH_REQUEST_HEADERS"], JSON.stringify(inputs.extra_headers));
                    }
                    if (inputs.extra_query) {
                        span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_REQUEST_EXTRA_QUERY"], JSON.stringify(inputs.extra_query));
                    }
                    if (inputs.extra_body) {
                        span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_REQUEST_EXTRA_BODY"], JSON.stringify(inputs.extra_body));
                    }
                }
                span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GENAI_PROMPT"], JSON.stringify(inputs));
            } catch (e) {
                console.debug(`Failed to process inputs for run ${op.id}`, e);
            }
        }
        if (op.run.outputs) {
            try {
                const outputs = op.run.outputs;
                // Extract token usage from outputs (for LLM runs)
                const tokenUsage = this.getUnifiedRunTokens(outputs);
                if (tokenUsage) {
                    span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_USAGE_INPUT_TOKENS"], tokenUsage[0]);
                    span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_USAGE_OUTPUT_TOKENS"], tokenUsage[1]);
                    span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_USAGE_TOTAL_TOKENS"], tokenUsage[0] + tokenUsage[1]);
                }
                if (outputs && typeof outputs === "object") {
                    if (outputs.model) {
                        span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_RESPONSE_MODEL"], String(outputs.model));
                    }
                    // Extract additional response attributes
                    if (outputs.id) {
                        span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_RESPONSE_ID"], outputs.id);
                    }
                    if (outputs.choices && Array.isArray(outputs.choices)) {
                        const finishReasons = outputs.choices// eslint-disable-next-line @typescript-eslint/no-explicit-any
                        .map((choice)=>choice.finish_reason)// eslint-disable-next-line @typescript-eslint/no-explicit-any
                        .filter((reason)=>reason).map(String);
                        if (finishReasons.length > 0) {
                            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_RESPONSE_FINISH_REASONS"], finishReasons.join(", "));
                        }
                    }
                    if (outputs.service_tier) {
                        span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_RESPONSE_SERVICE_TIER"], outputs.service_tier);
                    }
                    if (outputs.system_fingerprint) {
                        span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_RESPONSE_SYSTEM_FINGERPRINT"], outputs.system_fingerprint);
                    }
                    if (outputs.usage_metadata && typeof outputs.usage_metadata === "object") {
                        const usageMetadata = outputs.usage_metadata;
                        if (usageMetadata.input_token_details) {
                            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_USAGE_INPUT_TOKEN_DETAILS"], JSON.stringify(usageMetadata.input_token_details));
                        }
                        if (usageMetadata.output_token_details) {
                            span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GEN_AI_USAGE_OUTPUT_TOKEN_DETAILS"], JSON.stringify(usageMetadata.output_token_details));
                        }
                    }
                }
                span.setAttribute(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GENAI_COMPLETION"], JSON.stringify(outputs));
            } catch (e) {
                console.debug(`Failed to process outputs for run ${op.id}`, e);
            }
        }
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    getUnifiedRunTokens(outputs) {
        if (!outputs) {
            return null;
        }
        // Search in non-generations lists
        let tokenUsage = this.extractUnifiedRunTokens(outputs.usage_metadata);
        if (tokenUsage) {
            return tokenUsage;
        }
        // Find if direct kwarg in outputs
        const keys = Object.keys(outputs);
        for (const key of keys){
            const haystack = outputs[key];
            if (!haystack || typeof haystack !== "object") {
                continue;
            }
            tokenUsage = this.extractUnifiedRunTokens(haystack.usage_metadata);
            if (tokenUsage) {
                return tokenUsage;
            }
            if (haystack.lc === 1 && haystack.kwargs && typeof haystack.kwargs === "object") {
                tokenUsage = this.extractUnifiedRunTokens(haystack.kwargs.usage_metadata);
                if (tokenUsage) {
                    return tokenUsage;
                }
            }
        }
        // Find in generations
        const generations = outputs.generations || [];
        if (!Array.isArray(generations)) {
            return null;
        }
        const flatGenerations = Array.isArray(generations[0]) ? generations.flat() : generations;
        for (const generation of flatGenerations){
            if (typeof generation === "object" && generation.message && typeof generation.message === "object" && generation.message.kwargs && typeof generation.message.kwargs === "object") {
                tokenUsage = this.extractUnifiedRunTokens(generation.message.kwargs.usage_metadata);
                if (tokenUsage) {
                    return tokenUsage;
                }
            }
        }
        return null;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    extractUnifiedRunTokens(outputs) {
        if (!outputs || typeof outputs !== "object") {
            return null;
        }
        if (typeof outputs.input_tokens !== "number" || typeof outputs.output_tokens !== "number") {
            return null;
        }
        return [
            outputs.input_tokens,
            outputs.output_tokens
        ];
    }
}
}),
"[project]/node_modules/langsmith/dist/utils/async_caller.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AsyncCaller": ()=>AsyncCaller
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$p$2d$retry$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/p-retry/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$p$2d$queue$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/p-queue/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/singletons/fetch.js [app-route] (ecmascript)");
;
;
;
const STATUS_NO_RETRY = [
    400,
    401,
    403,
    404,
    405,
    406,
    407,
    408
];
const STATUS_IGNORE = [
    409
];
class AsyncCaller {
    constructor(params){
        Object.defineProperty(this, "maxConcurrency", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "maxRetries", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "queue", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "onFailedResponseHook", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "debug", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.maxConcurrency = params.maxConcurrency ?? Infinity;
        this.maxRetries = params.maxRetries ?? 6;
        this.debug = params.debug;
        if ("default" in __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$p$2d$queue$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            this.queue = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$p$2d$queue$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].default({
                concurrency: this.maxConcurrency
            });
        } else {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            this.queue = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$p$2d$queue$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
                concurrency: this.maxConcurrency
            });
        }
        this.onFailedResponseHook = params?.onFailedResponseHook;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    call(callable, ...args) {
        const onFailedResponseHook = this.onFailedResponseHook;
        return this.queue.add(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$p$2d$retry$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(()=>callable(...args).catch((error)=>{
                    // eslint-disable-next-line no-instanceof/no-instanceof
                    if (error instanceof Error) {
                        throw error;
                    } else {
                        throw new Error(error);
                    }
                }), {
                async onFailedAttempt (error) {
                    if (error.message.startsWith("Cancel") || error.message.startsWith("TimeoutError") || error.message.startsWith("AbortError")) {
                        throw error;
                    }
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    if (error?.code === "ECONNABORTED") {
                        throw error;
                    }
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const response = error?.response;
                    const status = response?.status;
                    if (status) {
                        if (STATUS_NO_RETRY.includes(+status)) {
                            throw error;
                        } else if (STATUS_IGNORE.includes(+status)) {
                            return;
                        }
                        if (onFailedResponseHook) {
                            await onFailedResponseHook(response);
                        }
                    }
                },
                // If needed we can change some of the defaults here,
                // but they're quite sensible.
                retries: this.maxRetries,
                randomize: true
            }), {
            throwOnTimeout: true
        });
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    callWithOptions(options, callable, ...args) {
        // Note this doesn't cancel the underlying request,
        // when available prefer to use the signal option of the underlying call
        if (options.signal) {
            return Promise.race([
                this.call(callable, ...args),
                new Promise((_, reject)=>{
                    options.signal?.addEventListener("abort", ()=>{
                        reject(new Error("AbortError"));
                    });
                })
            ]);
        }
        return this.call(callable, ...args);
    }
    fetch(...args) {
        return this.call(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug)(...args).then((res)=>res.ok ? res : Promise.reject(res)));
    }
}
}),
"[project]/node_modules/langsmith/dist/utils/messages.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "convertLangChainMessageToExample": ()=>convertLangChainMessageToExample,
    "isLangChainMessage": ()=>isLangChainMessage
});
function isLangChainMessage(// eslint-disable-next-line @typescript-eslint/no-explicit-any
message) {
    return typeof message?._getType === "function";
}
function convertLangChainMessageToExample(message) {
    const converted = {
        type: message._getType(),
        data: {
            content: message.content
        }
    };
    // Check for presence of keys in additional_kwargs
    if (message?.additional_kwargs && Object.keys(message.additional_kwargs).length > 0) {
        converted.data.additional_kwargs = {
            ...message.additional_kwargs
        };
    }
    return converted;
}
}),
"[project]/node_modules/langsmith/dist/utils/_uuid.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Relaxed UUID validation regex (allows any valid UUID format including nil UUIDs)
__turbopack_context__.s({
    "assertUuid": ()=>assertUuid
});
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
function assertUuid(str, which) {
    // Use relaxed regex validation instead of strict uuid.validate()
    // This allows edge cases like nil UUIDs or test UUIDs that might not pass strict validation
    if (!UUID_REGEX.test(str)) {
        const msg = which !== undefined ? `Invalid UUID for ${which}: ${str}` : `Invalid UUID: ${str}`;
        throw new Error(msg);
    }
    return str;
}
}),
"[project]/node_modules/langsmith/dist/utils/warn.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "warnOnce": ()=>warnOnce
});
const warnedMessages = {};
function warnOnce(message) {
    if (!warnedMessages[message]) {
        console.warn(message);
        warnedMessages[message] = true;
    }
}
}),
"[project]/node_modules/langsmith/dist/utils/prompts.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "isVersionGreaterOrEqual": ()=>isVersionGreaterOrEqual,
    "parsePromptIdentifier": ()=>parsePromptIdentifier
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$semver$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/semver/index.js [app-route] (ecmascript)");
;
function isVersionGreaterOrEqual(current_version, target_version) {
    const current = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$semver$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(current_version);
    const target = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$semver$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(target_version);
    if (!current || !target) {
        throw new Error("Invalid version format.");
    }
    return current.compare(target) >= 0;
}
function parsePromptIdentifier(identifier) {
    if (!identifier || identifier.split("/").length > 2 || identifier.startsWith("/") || identifier.endsWith("/") || identifier.split(":").length > 2) {
        throw new Error(`Invalid identifier format: ${identifier}`);
    }
    const [ownerNamePart, commitPart] = identifier.split(":");
    const commit = commitPart || "latest";
    if (ownerNamePart.includes("/")) {
        const [owner, name] = ownerNamePart.split("/", 2);
        if (!owner || !name) {
            throw new Error(`Invalid identifier format: ${identifier}`);
        }
        return [
            owner,
            name,
            commit
        ];
    } else {
        if (!ownerNamePart) {
            throw new Error(`Invalid identifier format: ${identifier}`);
        }
        return [
            "-",
            ownerNamePart,
            commit
        ];
    }
}
}),
"[project]/node_modules/langsmith/dist/utils/error.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ConflictingEndpointsError": ()=>ConflictingEndpointsError,
    "LangSmithConflictError": ()=>LangSmithConflictError,
    "isConflictingEndpointsError": ()=>isConflictingEndpointsError,
    "printErrorStackTrace": ()=>printErrorStackTrace,
    "raiseForStatus": ()=>raiseForStatus
});
function getErrorStackTrace(e) {
    if (typeof e !== "object" || e == null) return undefined;
    if (!("stack" in e) || typeof e.stack !== "string") return undefined;
    let stack = e.stack;
    const prevLine = `${e}`;
    if (stack.startsWith(prevLine)) {
        stack = stack.slice(prevLine.length);
    }
    if (stack.startsWith("\n")) {
        stack = stack.slice(1);
    }
    return stack;
}
function printErrorStackTrace(e) {
    const stack = getErrorStackTrace(e);
    if (stack == null) return;
    console.error(stack);
}
class LangSmithConflictError extends Error {
    constructor(message){
        super(message);
        Object.defineProperty(this, "status", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.name = "LangSmithConflictError";
        this.status = 409;
    }
}
async function raiseForStatus(response, context, consume) {
    // consume the response body to release the connection
    // https://undici.nodejs.org/#/?id=garbage-collection
    let errorBody;
    if (response.ok) {
        if (consume) {
            errorBody = await response.text();
        }
        return;
    }
    errorBody = await response.text();
    const fullMessage = `Failed to ${context}. Received status [${response.status}]: ${response.statusText}. Server response: ${errorBody}`;
    if (response.status === 409) {
        throw new LangSmithConflictError(fullMessage);
    }
    const err = new Error(fullMessage);
    err.status = response.status;
    throw err;
}
const ERR_CONFLICTING_ENDPOINTS = "ERR_CONFLICTING_ENDPOINTS";
class ConflictingEndpointsError extends Error {
    constructor(){
        super("You cannot provide both LANGSMITH_ENDPOINT / LANGCHAIN_ENDPOINT " + "and LANGSMITH_RUNS_ENDPOINTS.");
        Object.defineProperty(this, "code", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: ERR_CONFLICTING_ENDPOINTS
        });
        this.name = "ConflictingEndpointsError"; // helpful in logs
    }
}
function isConflictingEndpointsError(err) {
    return typeof err === "object" && err !== null && err.code === ERR_CONFLICTING_ENDPOINTS;
}
}),
"[project]/node_modules/langsmith/dist/utils/fast-safe-stringify/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/* eslint-disable */ // @ts-nocheck
__turbopack_context__.s({
    "serialize": ()=>serialize
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/env.js [app-route] (ecmascript)");
;
var LIMIT_REPLACE_NODE = "[...]";
var CIRCULAR_REPLACE_NODE = {
    result: "[Circular]"
};
var arr = [];
var replacerStack = [];
const encoder = new TextEncoder();
function defaultOptions() {
    return {
        depthLimit: Number.MAX_SAFE_INTEGER,
        edgesLimit: Number.MAX_SAFE_INTEGER
    };
}
function encodeString(str) {
    return encoder.encode(str);
}
// Shared function to handle well-known types
function serializeWellKnownTypes(val) {
    if (val && typeof val === "object" && val !== null) {
        if (val instanceof Map) {
            return Object.fromEntries(val);
        } else if (val instanceof Set) {
            return Array.from(val);
        } else if (val instanceof Date) {
            return val.toISOString();
        } else if (val instanceof RegExp) {
            return val.toString();
        } else if (val instanceof Error) {
            return {
                name: val.name,
                message: val.message
            };
        }
    } else if (typeof val === "bigint") {
        return val.toString();
    }
    return val;
}
// Default replacer function to handle well-known types
function createDefaultReplacer(userReplacer) {
    return function(key, val) {
        // Apply user replacer first if provided
        if (userReplacer) {
            const userResult = userReplacer.call(this, key, val);
            // If user replacer returned undefined, fall back to our serialization
            if (userResult !== undefined) {
                return userResult;
            }
        }
        // Fall back to our well-known type handling
        return serializeWellKnownTypes(val);
    };
}
function serialize(obj, errorContext, replacer, spacer, options) {
    try {
        const str = JSON.stringify(obj, createDefaultReplacer(replacer), spacer);
        return encodeString(str);
    } catch (e) {
        // Fall back to more complex stringify if circular reference
        if (!e.message?.includes("Converting circular structure to JSON")) {
            console.warn(`[WARNING]: LangSmith received unserializable value.${errorContext ? `\nContext: ${errorContext}` : ""}`);
            return encodeString("[Unserializable]");
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("SUPPRESS_CIRCULAR_JSON_WARNINGS") !== "true" && console.warn(`[WARNING]: LangSmith received circular JSON. This will decrease tracer performance. ${errorContext ? `\nContext: ${errorContext}` : ""}`);
        if (typeof options === "undefined") {
            options = defaultOptions();
        }
        decirc(obj, "", 0, [], undefined, 0, options);
        let res;
        try {
            if (replacerStack.length === 0) {
                res = JSON.stringify(obj, replacer, spacer);
            } else {
                res = JSON.stringify(obj, replaceGetterValues(replacer), spacer);
            }
        } catch (_) {
            return encodeString("[unable to serialize, circular reference is too complex to analyze]");
        } finally{
            while(arr.length !== 0){
                const part = arr.pop();
                if (part.length === 4) {
                    Object.defineProperty(part[0], part[1], part[3]);
                } else {
                    part[0][part[1]] = part[2];
                }
            }
        }
        return encodeString(res);
    }
}
function setReplace(replace, val, k, parent) {
    var propertyDescriptor = Object.getOwnPropertyDescriptor(parent, k);
    if (propertyDescriptor.get !== undefined) {
        if (propertyDescriptor.configurable) {
            Object.defineProperty(parent, k, {
                value: replace
            });
            arr.push([
                parent,
                k,
                val,
                propertyDescriptor
            ]);
        } else {
            replacerStack.push([
                val,
                k,
                replace
            ]);
        }
    } else {
        parent[k] = replace;
        arr.push([
            parent,
            k,
            val
        ]);
    }
}
function decirc(val, k, edgeIndex, stack, parent, depth, options) {
    depth += 1;
    var i;
    if (typeof val === "object" && val !== null) {
        for(i = 0; i < stack.length; i++){
            if (stack[i] === val) {
                setReplace(CIRCULAR_REPLACE_NODE, val, k, parent);
                return;
            }
        }
        if (typeof options.depthLimit !== "undefined" && depth > options.depthLimit) {
            setReplace(LIMIT_REPLACE_NODE, val, k, parent);
            return;
        }
        if (typeof options.edgesLimit !== "undefined" && edgeIndex + 1 > options.edgesLimit) {
            setReplace(LIMIT_REPLACE_NODE, val, k, parent);
            return;
        }
        stack.push(val);
        // Optimize for Arrays. Big arrays could kill the performance otherwise!
        if (Array.isArray(val)) {
            for(i = 0; i < val.length; i++){
                decirc(val[i], i, i, stack, val, depth, options);
            }
        } else {
            // Handle well-known types before Object.keys iteration
            val = serializeWellKnownTypes(val);
            var keys = Object.keys(val);
            for(i = 0; i < keys.length; i++){
                var key = keys[i];
                decirc(val[key], key, i, stack, val, depth, options);
            }
        }
        stack.pop();
    }
}
// Stable-stringify
function compareFunction(a, b) {
    if (a < b) {
        return -1;
    }
    if (a > b) {
        return 1;
    }
    return 0;
}
function deterministicStringify(obj, replacer, spacer, options) {
    if (typeof options === "undefined") {
        options = defaultOptions();
    }
    var tmp = deterministicDecirc(obj, "", 0, [], undefined, 0, options) || obj;
    var res;
    try {
        if (replacerStack.length === 0) {
            res = JSON.stringify(tmp, replacer, spacer);
        } else {
            res = JSON.stringify(tmp, replaceGetterValues(replacer), spacer);
        }
    } catch (_) {
        return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]");
    } finally{
        // Ensure that we restore the object as it was.
        while(arr.length !== 0){
            var part = arr.pop();
            if (part.length === 4) {
                Object.defineProperty(part[0], part[1], part[3]);
            } else {
                part[0][part[1]] = part[2];
            }
        }
    }
    return res;
}
function deterministicDecirc(val, k, edgeIndex, stack, parent, depth, options) {
    depth += 1;
    var i;
    if (typeof val === "object" && val !== null) {
        for(i = 0; i < stack.length; i++){
            if (stack[i] === val) {
                setReplace(CIRCULAR_REPLACE_NODE, val, k, parent);
                return;
            }
        }
        try {
            if (typeof val.toJSON === "function") {
                return;
            }
        } catch (_) {
            return;
        }
        if (typeof options.depthLimit !== "undefined" && depth > options.depthLimit) {
            setReplace(LIMIT_REPLACE_NODE, val, k, parent);
            return;
        }
        if (typeof options.edgesLimit !== "undefined" && edgeIndex + 1 > options.edgesLimit) {
            setReplace(LIMIT_REPLACE_NODE, val, k, parent);
            return;
        }
        stack.push(val);
        // Optimize for Arrays. Big arrays could kill the performance otherwise!
        if (Array.isArray(val)) {
            for(i = 0; i < val.length; i++){
                deterministicDecirc(val[i], i, i, stack, val, depth, options);
            }
        } else {
            // Handle well-known types before Object.keys iteration
            val = serializeWellKnownTypes(val);
            // Create a temporary object in the required way
            var tmp = {};
            var keys = Object.keys(val).sort(compareFunction);
            for(i = 0; i < keys.length; i++){
                var key = keys[i];
                deterministicDecirc(val[key], key, i, stack, val, depth, options);
                tmp[key] = val[key];
            }
            if (typeof parent !== "undefined") {
                arr.push([
                    parent,
                    k,
                    val
                ]);
                parent[k] = tmp;
            } else {
                return tmp;
            }
        }
        stack.pop();
    }
}
// wraps replacer function to handle values we couldn't replace
// and mark them as replaced value
function replaceGetterValues(replacer) {
    replacer = typeof replacer !== "undefined" ? replacer : function(k, v) {
        return v;
    };
    return function(key, val) {
        if (replacerStack.length > 0) {
            for(var i = 0; i < replacerStack.length; i++){
                var part = replacerStack[i];
                if (part[1] === key && part[0] === val) {
                    val = part[2];
                    replacerStack.splice(i, 1);
                    break;
                }
            }
        }
        return replacer.call(this, key, val);
    };
}
}),
"[project]/node_modules/langsmith/dist/client.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AutoBatchQueue": ()=>AutoBatchQueue,
    "Client": ()=>Client,
    "DEFAULT_BATCH_SIZE_LIMIT_BYTES": ()=>DEFAULT_BATCH_SIZE_LIMIT_BYTES,
    "mergeRuntimeEnvIntoRunCreate": ()=>mergeRuntimeEnvIntoRunCreate
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-node/v4.js [app-route] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$translator$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/experimental/otel/translator.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$otel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/singletons/otel.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$async_caller$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/async_caller.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$messages$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/messages.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/_uuid.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$warn$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/warn.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/prompts.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/error.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/singletons/fetch.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/fast-safe-stringify/index.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
function mergeRuntimeEnvIntoRunCreate(run) {
    const runtimeEnv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getRuntimeEnvironment"])();
    const envVars = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangChainEnvVarsMetadata"])();
    const extra = run.extra ?? {};
    const metadata = extra.metadata;
    run.extra = {
        ...extra,
        runtime: {
            ...runtimeEnv,
            ...extra?.runtime
        },
        metadata: {
            ...envVars,
            ...envVars.revision_id || run.revision_id ? {
                revision_id: run.revision_id ?? envVars.revision_id
            } : {},
            ...metadata
        }
    };
    return run;
}
const getTracingSamplingRate = (configRate)=>{
    const samplingRateStr = configRate?.toString() ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("TRACING_SAMPLING_RATE");
    if (samplingRateStr === undefined) {
        return undefined;
    }
    const samplingRate = parseFloat(samplingRateStr);
    if (samplingRate < 0 || samplingRate > 1) {
        throw new Error(`LANGSMITH_TRACING_SAMPLING_RATE must be between 0 and 1 if set. Got: ${samplingRate}`);
    }
    return samplingRate;
};
// utility functions
const isLocalhost = (url)=>{
    const strippedUrl = url.replace("http://", "").replace("https://", "");
    const hostname = strippedUrl.split("/")[0].split(":")[0];
    return hostname === "localhost" || hostname === "127.0.0.1" || hostname === "::1";
};
async function toArray(iterable) {
    const result = [];
    for await (const item of iterable){
        result.push(item);
    }
    return result;
}
function trimQuotes(str) {
    if (str === undefined) {
        return undefined;
    }
    return str.trim().replace(/^"(.*)"$/, "$1").replace(/^'(.*)'$/, "$1");
}
const handle429 = async (response)=>{
    if (response?.status === 429) {
        const retryAfter = parseInt(response.headers.get("retry-after") ?? "30", 10) * 1000;
        if (retryAfter > 0) {
            await new Promise((resolve)=>setTimeout(resolve, retryAfter));
            // Return directly after calling this check
            return true;
        }
    }
    // Fall back to existing status checks
    return false;
};
function _formatFeedbackScore(score) {
    if (typeof score === "number") {
        // Truncate at 4 decimal places
        return Number(score.toFixed(4));
    }
    return score;
}
class AutoBatchQueue {
    constructor(){
        Object.defineProperty(this, "items", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: []
        });
        Object.defineProperty(this, "sizeBytes", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 0
        });
    }
    peek() {
        return this.items[0];
    }
    push(item) {
        let itemPromiseResolve;
        const itemPromise = new Promise((resolve)=>{
            // Setting itemPromiseResolve is synchronous with promise creation:
            // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise
            itemPromiseResolve = resolve;
        });
        const size = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(item.item, `Serializing run with id: ${item.item.id}`).length;
        this.items.push({
            action: item.action,
            payload: item.item,
            otelContext: item.otelContext,
            apiKey: item.apiKey,
            apiUrl: item.apiUrl,
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            itemPromiseResolve: itemPromiseResolve,
            itemPromise,
            size
        });
        this.sizeBytes += size;
        return itemPromise;
    }
    pop(upToSizeBytes) {
        if (upToSizeBytes < 1) {
            throw new Error("Number of bytes to pop off may not be less than 1.");
        }
        const popped = [];
        let poppedSizeBytes = 0;
        // Pop items until we reach or exceed the size limit
        while(poppedSizeBytes + (this.peek()?.size ?? 0) < upToSizeBytes && this.items.length > 0){
            const item = this.items.shift();
            if (item) {
                popped.push(item);
                poppedSizeBytes += item.size;
                this.sizeBytes -= item.size;
            }
        }
        // If there is an item on the queue we were unable to pop,
        // just return it as a single batch.
        if (popped.length === 0 && this.items.length > 0) {
            const item = this.items.shift();
            popped.push(item);
            poppedSizeBytes += item.size;
            this.sizeBytes -= item.size;
        }
        return [
            popped.map((it)=>({
                    action: it.action,
                    item: it.payload,
                    otelContext: it.otelContext,
                    apiKey: it.apiKey,
                    apiUrl: it.apiUrl
                })),
            ()=>popped.forEach((it)=>it.itemPromiseResolve())
        ];
    }
}
const DEFAULT_BATCH_SIZE_LIMIT_BYTES = 20_971_520;
const SERVER_INFO_REQUEST_TIMEOUT = 2500;
const DEFAULT_API_URL = "https://api.smith.langchain.com";
class Client {
    constructor(config = {}){
        Object.defineProperty(this, "apiKey", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "apiUrl", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "webUrl", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "caller", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "batchIngestCaller", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "timeout_ms", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_tenantId", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        Object.defineProperty(this, "hideInputs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "hideOutputs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "tracingSampleRate", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "filteredPostUuids", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new Set()
        });
        Object.defineProperty(this, "autoBatchTracing", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
        Object.defineProperty(this, "autoBatchQueue", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new AutoBatchQueue()
        });
        Object.defineProperty(this, "autoBatchTimeout", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "autoBatchAggregationDelayMs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 250
        });
        Object.defineProperty(this, "batchSizeBytesLimit", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "fetchOptions", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "settings", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "blockOnRootRunFinalization", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("LANGSMITH_TRACING_BACKGROUND") === "false"
        });
        Object.defineProperty(this, "traceBatchConcurrency", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 5
        });
        Object.defineProperty(this, "_serverInfo", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        Object.defineProperty(this, "_getServerInfoPromise", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "manualFlushMode", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "langSmithToOTELTranslator", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "multipartStreamingDisabled", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "debug", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("LANGSMITH_DEBUG") === "true"
        });
        const defaultConfig = Client.getDefaultClientConfig();
        this.tracingSampleRate = getTracingSamplingRate(config.tracingSamplingRate);
        this.apiUrl = trimQuotes(config.apiUrl ?? defaultConfig.apiUrl) ?? "";
        if (this.apiUrl.endsWith("/")) {
            this.apiUrl = this.apiUrl.slice(0, -1);
        }
        this.apiKey = trimQuotes(config.apiKey ?? defaultConfig.apiKey);
        this.webUrl = trimQuotes(config.webUrl ?? defaultConfig.webUrl);
        if (this.webUrl?.endsWith("/")) {
            this.webUrl = this.webUrl.slice(0, -1);
        }
        this.timeout_ms = config.timeout_ms ?? 90_000;
        this.caller = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$async_caller$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AsyncCaller"]({
            ...config.callerOptions ?? {},
            debug: config.debug ?? this.debug
        });
        this.traceBatchConcurrency = config.traceBatchConcurrency ?? this.traceBatchConcurrency;
        if (this.traceBatchConcurrency < 1) {
            throw new Error("Trace batch concurrency must be positive.");
        }
        this.debug = config.debug ?? this.debug;
        this.batchIngestCaller = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$async_caller$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AsyncCaller"]({
            maxRetries: 2,
            maxConcurrency: this.traceBatchConcurrency,
            ...config.callerOptions ?? {},
            onFailedResponseHook: handle429,
            debug: config.debug ?? this.debug
        });
        this.hideInputs = config.hideInputs ?? config.anonymizer ?? defaultConfig.hideInputs;
        this.hideOutputs = config.hideOutputs ?? config.anonymizer ?? defaultConfig.hideOutputs;
        this.autoBatchTracing = config.autoBatchTracing ?? this.autoBatchTracing;
        this.blockOnRootRunFinalization = config.blockOnRootRunFinalization ?? this.blockOnRootRunFinalization;
        this.batchSizeBytesLimit = config.batchSizeBytesLimit;
        this.fetchOptions = config.fetchOptions || {};
        this.manualFlushMode = config.manualFlushMode ?? this.manualFlushMode;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("OTEL_ENABLED") === "true") {
            this.langSmithToOTELTranslator = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$experimental$2f$otel$2f$translator$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LangSmithToOTELTranslator"]();
        }
    }
    static getDefaultClientConfig() {
        const apiKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("API_KEY");
        const apiUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("ENDPOINT") ?? DEFAULT_API_URL;
        const hideInputs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("HIDE_INPUTS") === "true";
        const hideOutputs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("HIDE_OUTPUTS") === "true";
        return {
            apiUrl: apiUrl,
            apiKey: apiKey,
            webUrl: undefined,
            hideInputs: hideInputs,
            hideOutputs: hideOutputs
        };
    }
    getHostUrl() {
        if (this.webUrl) {
            return this.webUrl;
        } else if (isLocalhost(this.apiUrl)) {
            this.webUrl = "http://localhost:3000";
            return this.webUrl;
        } else if (this.apiUrl.endsWith("/api/v1")) {
            this.webUrl = this.apiUrl.replace("/api/v1", "");
            return this.webUrl;
        } else if (this.apiUrl.includes("/api") && !this.apiUrl.split(".", 1)[0].endsWith("api")) {
            this.webUrl = this.apiUrl.replace("/api", "");
            return this.webUrl;
        } else if (this.apiUrl.split(".", 1)[0].includes("dev")) {
            this.webUrl = "https://dev.smith.langchain.com";
            return this.webUrl;
        } else if (this.apiUrl.split(".", 1)[0].includes("eu")) {
            this.webUrl = "https://eu.smith.langchain.com";
            return this.webUrl;
        } else if (this.apiUrl.split(".", 1)[0].includes("beta")) {
            this.webUrl = "https://beta.smith.langchain.com";
            return this.webUrl;
        } else {
            this.webUrl = "https://smith.langchain.com";
            return this.webUrl;
        }
    }
    get headers() {
        const headers = {
            "User-Agent": `langsmith-js/${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["__version__"]}`
        };
        if (this.apiKey) {
            headers["x-api-key"] = `${this.apiKey}`;
        }
        return headers;
    }
    _getPlatformEndpointPath(path) {
        // Check if apiUrl already ends with /v1 or /v1/ to avoid double /v1/v1/ paths
        const needsV1Prefix = this.apiUrl.slice(-3) !== "/v1" && this.apiUrl.slice(-4) !== "/v1/";
        return needsV1Prefix ? `/v1/platform/${path}` : `/platform/${path}`;
    }
    async processInputs(inputs) {
        if (this.hideInputs === false) {
            return inputs;
        }
        if (this.hideInputs === true) {
            return {};
        }
        if (typeof this.hideInputs === "function") {
            return this.hideInputs(inputs);
        }
        return inputs;
    }
    async processOutputs(outputs) {
        if (this.hideOutputs === false) {
            return outputs;
        }
        if (this.hideOutputs === true) {
            return {};
        }
        if (typeof this.hideOutputs === "function") {
            return this.hideOutputs(outputs);
        }
        return outputs;
    }
    async prepareRunCreateOrUpdateInputs(run) {
        const runParams = {
            ...run
        };
        if (runParams.inputs !== undefined) {
            runParams.inputs = await this.processInputs(runParams.inputs);
        }
        if (runParams.outputs !== undefined) {
            runParams.outputs = await this.processOutputs(runParams.outputs);
        }
        return runParams;
    }
    async _getResponse(path, queryParams) {
        const paramsString = queryParams?.toString() ?? "";
        const url = `${this.apiUrl}${path}?${paramsString}`;
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), url, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, `Failed to fetch ${path}`);
        return response;
    }
    async _get(path, queryParams) {
        const response = await this._getResponse(path, queryParams);
        return response.json();
    }
    async *_getPaginated(path, queryParams = new URLSearchParams(), transform) {
        let offset = Number(queryParams.get("offset")) || 0;
        const limit = Number(queryParams.get("limit")) || 100;
        while(true){
            queryParams.set("offset", String(offset));
            queryParams.set("limit", String(limit));
            const url = `${this.apiUrl}${path}?${queryParams}`;
            const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), url, {
                method: "GET",
                headers: this.headers,
                signal: AbortSignal.timeout(this.timeout_ms),
                ...this.fetchOptions
            });
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, `Failed to fetch ${path}`);
            const items = transform ? transform(await response.json()) : await response.json();
            if (items.length === 0) {
                break;
            }
            yield items;
            if (items.length < limit) {
                break;
            }
            offset += items.length;
        }
    }
    async *_getCursorPaginatedList(path, body = null, requestMethod = "POST", dataKey = "runs") {
        const bodyParams = body ? {
            ...body
        } : {};
        while(true){
            const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}${path}`, {
                method: requestMethod,
                headers: {
                    ...this.headers,
                    "Content-Type": "application/json"
                },
                signal: AbortSignal.timeout(this.timeout_ms),
                ...this.fetchOptions,
                body: JSON.stringify(bodyParams)
            });
            const responseBody = await response.json();
            if (!responseBody) {
                break;
            }
            if (!responseBody[dataKey]) {
                break;
            }
            yield responseBody[dataKey];
            const cursors = responseBody.cursors;
            if (!cursors) {
                break;
            }
            if (!cursors.next) {
                break;
            }
            bodyParams.cursor = cursors.next;
        }
    }
    // Allows mocking for tests
    _shouldSample() {
        if (this.tracingSampleRate === undefined) {
            return true;
        }
        return Math.random() < this.tracingSampleRate;
    }
    _filterForSampling(runs, patch = false) {
        if (this.tracingSampleRate === undefined) {
            return runs;
        }
        if (patch) {
            const sampled = [];
            for (const run of runs){
                if (!this.filteredPostUuids.has(run.id)) {
                    sampled.push(run);
                } else {
                    this.filteredPostUuids.delete(run.id);
                }
            }
            return sampled;
        } else {
            // For new runs, sample at trace level to maintain consistency
            const sampled = [];
            for (const run of runs){
                const traceId = run.trace_id ?? run.id;
                // If we've already made a decision about this trace, follow it
                if (this.filteredPostUuids.has(traceId)) {
                    continue;
                }
                // For new traces, apply sampling
                if (run.id === traceId) {
                    if (this._shouldSample()) {
                        sampled.push(run);
                    } else {
                        this.filteredPostUuids.add(traceId);
                    }
                } else {
                    // Child runs follow their trace's sampling decision
                    sampled.push(run);
                }
            }
            return sampled;
        }
    }
    async _getBatchSizeLimitBytes() {
        const serverInfo = await this._ensureServerInfo();
        return this.batchSizeBytesLimit ?? serverInfo.batch_ingest_config?.size_limit_bytes ?? DEFAULT_BATCH_SIZE_LIMIT_BYTES;
    }
    async _getMultiPartSupport() {
        const serverInfo = await this._ensureServerInfo();
        return serverInfo.instance_flags?.dataset_examples_multipart_enabled ?? false;
    }
    drainAutoBatchQueue(batchSizeLimit) {
        const promises = [];
        while(this.autoBatchQueue.items.length > 0){
            const [batch, done] = this.autoBatchQueue.pop(batchSizeLimit);
            if (!batch.length) {
                done();
                break;
            }
            const batchesByDestination = batch.reduce((acc, item)=>{
                const apiUrl = item.apiUrl ?? this.apiUrl;
                const apiKey = item.apiKey ?? this.apiKey;
                const isDefault = item.apiKey === this.apiKey && item.apiUrl === this.apiUrl;
                const batchKey = isDefault ? "default" : `${apiUrl}|${apiKey}`;
                if (!acc[batchKey]) {
                    acc[batchKey] = [];
                }
                acc[batchKey].push(item);
                return acc;
            }, {});
            const batchPromises = [];
            for (const [batchKey, batch] of Object.entries(batchesByDestination)){
                const batchPromise = this._processBatch(batch, {
                    apiUrl: batchKey === "default" ? undefined : batchKey.split("|")[0],
                    apiKey: batchKey === "default" ? undefined : batchKey.split("|")[1]
                });
                batchPromises.push(batchPromise);
            }
            // Wait for all batches to complete, then call the overall done callback
            const allBatchesPromise = Promise.all(batchPromises).finally(done);
            promises.push(allBatchesPromise);
        }
        return Promise.all(promises);
    }
    async _processBatch(batch, options) {
        if (!batch.length) {
            return;
        }
        try {
            if (this.langSmithToOTELTranslator !== undefined) {
                this._sendBatchToOTELTranslator(batch);
            } else {
                const ingestParams = {
                    runCreates: batch.filter((item)=>item.action === "create").map((item)=>item.item),
                    runUpdates: batch.filter((item)=>item.action === "update").map((item)=>item.item)
                };
                const serverInfo = await this._ensureServerInfo();
                if (serverInfo?.batch_ingest_config?.use_multipart_endpoint) {
                    await this.multipartIngestRuns(ingestParams, options);
                } else {
                    await this.batchIngestRuns(ingestParams, options);
                }
            }
        } catch (e) {
            console.error("Error exporting batch:", e);
        }
    }
    _sendBatchToOTELTranslator(batch) {
        if (this.langSmithToOTELTranslator !== undefined) {
            const otelContextMap = new Map();
            const operations = [];
            for (const item of batch){
                if (item.item.id && item.otelContext) {
                    otelContextMap.set(item.item.id, item.otelContext);
                    if (item.action === "create") {
                        operations.push({
                            operation: "post",
                            id: item.item.id,
                            trace_id: item.item.trace_id ?? item.item.id,
                            run: item.item
                        });
                    } else {
                        operations.push({
                            operation: "patch",
                            id: item.item.id,
                            trace_id: item.item.trace_id ?? item.item.id,
                            run: item.item
                        });
                    }
                }
            }
            this.langSmithToOTELTranslator.exportBatch(operations, otelContextMap);
        }
    }
    async processRunOperation(item) {
        clearTimeout(this.autoBatchTimeout);
        this.autoBatchTimeout = undefined;
        if (item.action === "create") {
            item.item = mergeRuntimeEnvIntoRunCreate(item.item);
        }
        const itemPromise = this.autoBatchQueue.push(item);
        if (this.manualFlushMode) {
            // Rely on manual flushing in serverless environments
            return itemPromise;
        }
        const sizeLimitBytes = await this._getBatchSizeLimitBytes();
        if (this.autoBatchQueue.sizeBytes > sizeLimitBytes) {
            void this.drainAutoBatchQueue(sizeLimitBytes);
        }
        if (this.autoBatchQueue.items.length > 0) {
            this.autoBatchTimeout = setTimeout(()=>{
                this.autoBatchTimeout = undefined;
                void this.drainAutoBatchQueue(sizeLimitBytes);
            }, this.autoBatchAggregationDelayMs);
        }
        return itemPromise;
    }
    async _getServerInfo() {
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/info`, {
            method: "GET",
            headers: {
                Accept: "application/json"
            },
            signal: AbortSignal.timeout(SERVER_INFO_REQUEST_TIMEOUT),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "get server info");
        const json = await response.json();
        if (this.debug) {
            console.log("\n=== LangSmith Server Configuration ===\n" + JSON.stringify(json, null, 2) + "\n");
        }
        return json;
    }
    async _ensureServerInfo() {
        if (this._getServerInfoPromise === undefined) {
            this._getServerInfoPromise = (async ()=>{
                if (this._serverInfo === undefined) {
                    try {
                        this._serverInfo = await this._getServerInfo();
                    } catch (e) {
                        console.warn(`[WARNING]: LangSmith failed to fetch info on supported operations with status code ${e.status}. Falling back to batch operations and default limits.`);
                    }
                }
                return this._serverInfo ?? {};
            })();
        }
        return this._getServerInfoPromise.then((serverInfo)=>{
            if (this._serverInfo === undefined) {
                this._getServerInfoPromise = undefined;
            }
            return serverInfo;
        });
    }
    async _getSettings() {
        if (!this.settings) {
            this.settings = this._get("/settings");
        }
        return await this.settings;
    }
    /**
     * Flushes current queued traces.
     */ async flush() {
        const sizeLimitBytes = await this._getBatchSizeLimitBytes();
        await this.drainAutoBatchQueue(sizeLimitBytes);
    }
    _cloneCurrentOTELContext() {
        const otel_trace = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$otel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getOTELTrace"])();
        const otel_context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$otel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getOTELContext"])();
        if (this.langSmithToOTELTranslator !== undefined) {
            const currentSpan = otel_trace.getActiveSpan();
            if (currentSpan) {
                return otel_trace.setSpan(otel_context.active(), currentSpan);
            }
        }
        return undefined;
    }
    async createRun(run, options) {
        if (!this._filterForSampling([
            run
        ]).length) {
            return;
        }
        const headers = {
            ...this.headers,
            "Content-Type": "application/json"
        };
        const session_name = run.project_name;
        delete run.project_name;
        const runCreate = await this.prepareRunCreateOrUpdateInputs({
            session_name,
            ...run,
            start_time: run.start_time ?? Date.now()
        });
        if (this.autoBatchTracing && runCreate.trace_id !== undefined && runCreate.dotted_order !== undefined) {
            const otelContext = this._cloneCurrentOTELContext();
            void this.processRunOperation({
                action: "create",
                item: runCreate,
                otelContext,
                apiKey: options?.apiKey,
                apiUrl: options?.apiUrl
            }).catch(console.error);
            return;
        }
        const mergedRunCreateParam = mergeRuntimeEnvIntoRunCreate(runCreate);
        if (options?.apiKey !== undefined) {
            headers["x-api-key"] = options.apiKey;
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${options?.apiUrl ?? this.apiUrl}/runs`, {
            method: "POST",
            headers,
            body: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(mergedRunCreateParam, `Creating run with id: ${mergedRunCreateParam.id}`),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "create run", true);
    }
    /**
     * Batch ingest/upsert multiple runs in the Langsmith system.
     * @param runs
     */ async batchIngestRuns({ runCreates, runUpdates }, options) {
        if (runCreates === undefined && runUpdates === undefined) {
            return;
        }
        let preparedCreateParams = await Promise.all(runCreates?.map((create)=>this.prepareRunCreateOrUpdateInputs(create)) ?? []);
        let preparedUpdateParams = await Promise.all(runUpdates?.map((update)=>this.prepareRunCreateOrUpdateInputs(update)) ?? []);
        if (preparedCreateParams.length > 0 && preparedUpdateParams.length > 0) {
            const createById = preparedCreateParams.reduce((params, run)=>{
                if (!run.id) {
                    return params;
                }
                params[run.id] = run;
                return params;
            }, {});
            const standaloneUpdates = [];
            for (const updateParam of preparedUpdateParams){
                if (updateParam.id !== undefined && createById[updateParam.id]) {
                    createById[updateParam.id] = {
                        ...createById[updateParam.id],
                        ...updateParam
                    };
                } else {
                    standaloneUpdates.push(updateParam);
                }
            }
            preparedCreateParams = Object.values(createById);
            preparedUpdateParams = standaloneUpdates;
        }
        const rawBatch = {
            post: preparedCreateParams,
            patch: preparedUpdateParams
        };
        if (!rawBatch.post.length && !rawBatch.patch.length) {
            return;
        }
        const batchChunks = {
            post: [],
            patch: []
        };
        for (const k of [
            "post",
            "patch"
        ]){
            const key = k;
            const batchItems = rawBatch[key].reverse();
            let batchItem = batchItems.pop();
            while(batchItem !== undefined){
                // Type is wrong but this is a deprecated code path anyway
                batchChunks[key].push(batchItem);
                batchItem = batchItems.pop();
            }
        }
        if (batchChunks.post.length > 0 || batchChunks.patch.length > 0) {
            const runIds = batchChunks.post.map((item)=>item.id).concat(batchChunks.patch.map((item)=>item.id)).join(",");
            await this._postBatchIngestRuns((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(batchChunks, `Ingesting runs with ids: ${runIds}`), options);
        }
    }
    async _postBatchIngestRuns(body, options) {
        const headers = {
            ...this.headers,
            "Content-Type": "application/json",
            Accept: "application/json"
        };
        if (options?.apiKey !== undefined) {
            headers["x-api-key"] = options.apiKey;
        }
        const response = await this.batchIngestCaller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${options?.apiUrl ?? this.apiUrl}/runs/batch`, {
            method: "POST",
            headers,
            body: body,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "batch create run", true);
    }
    /**
     * Batch ingest/upsert multiple runs in the Langsmith system.
     * @param runs
     */ async multipartIngestRuns({ runCreates, runUpdates }, options) {
        if (runCreates === undefined && runUpdates === undefined) {
            return;
        }
        // transform and convert to dicts
        const allAttachments = {};
        let preparedCreateParams = [];
        for (const create of runCreates ?? []){
            const preparedCreate = await this.prepareRunCreateOrUpdateInputs(create);
            if (preparedCreate.id !== undefined && preparedCreate.attachments !== undefined) {
                allAttachments[preparedCreate.id] = preparedCreate.attachments;
            }
            delete preparedCreate.attachments;
            preparedCreateParams.push(preparedCreate);
        }
        let preparedUpdateParams = [];
        for (const update of runUpdates ?? []){
            preparedUpdateParams.push(await this.prepareRunCreateOrUpdateInputs(update));
        }
        // require trace_id and dotted_order
        const invalidRunCreate = preparedCreateParams.find((runCreate)=>{
            return runCreate.trace_id === undefined || runCreate.dotted_order === undefined;
        });
        if (invalidRunCreate !== undefined) {
            throw new Error(`Multipart ingest requires "trace_id" and "dotted_order" to be set when creating a run`);
        }
        const invalidRunUpdate = preparedUpdateParams.find((runUpdate)=>{
            return runUpdate.trace_id === undefined || runUpdate.dotted_order === undefined;
        });
        if (invalidRunUpdate !== undefined) {
            throw new Error(`Multipart ingest requires "trace_id" and "dotted_order" to be set when updating a run`);
        }
        // combine post and patch dicts where possible
        if (preparedCreateParams.length > 0 && preparedUpdateParams.length > 0) {
            const createById = preparedCreateParams.reduce((params, run)=>{
                if (!run.id) {
                    return params;
                }
                params[run.id] = run;
                return params;
            }, {});
            const standaloneUpdates = [];
            for (const updateParam of preparedUpdateParams){
                if (updateParam.id !== undefined && createById[updateParam.id]) {
                    createById[updateParam.id] = {
                        ...createById[updateParam.id],
                        ...updateParam
                    };
                } else {
                    standaloneUpdates.push(updateParam);
                }
            }
            preparedCreateParams = Object.values(createById);
            preparedUpdateParams = standaloneUpdates;
        }
        if (preparedCreateParams.length === 0 && preparedUpdateParams.length === 0) {
            return;
        }
        // send the runs in multipart requests
        const accumulatedContext = [];
        const accumulatedParts = [];
        for (const [method, payloads] of [
            [
                "post",
                preparedCreateParams
            ],
            [
                "patch",
                preparedUpdateParams
            ]
        ]){
            for (const originalPayload of payloads){
                // collect fields to be sent as separate parts
                const { inputs, outputs, events, attachments, ...payload } = originalPayload;
                const fields = {
                    inputs,
                    outputs,
                    events
                };
                // encode the main run payload
                const stringifiedPayload = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(payload, `Serializing for multipart ingestion of run with id: ${payload.id}`);
                accumulatedParts.push({
                    name: `${method}.${payload.id}`,
                    payload: new Blob([
                        stringifiedPayload
                    ], {
                        type: `application/json; length=${stringifiedPayload.length}`
                    })
                });
                // encode the fields we collected
                for (const [key, value] of Object.entries(fields)){
                    if (value === undefined) {
                        continue;
                    }
                    const stringifiedValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(value, `Serializing ${key} for multipart ingestion of run with id: ${payload.id}`);
                    accumulatedParts.push({
                        name: `${method}.${payload.id}.${key}`,
                        payload: new Blob([
                            stringifiedValue
                        ], {
                            type: `application/json; length=${stringifiedValue.length}`
                        })
                    });
                }
                // encode the attachments
                if (payload.id !== undefined) {
                    const attachments = allAttachments[payload.id];
                    if (attachments) {
                        delete allAttachments[payload.id];
                        for (const [name, attachment] of Object.entries(attachments)){
                            let contentType;
                            let content;
                            if (Array.isArray(attachment)) {
                                [contentType, content] = attachment;
                            } else {
                                contentType = attachment.mimeType;
                                content = attachment.data;
                            }
                            // Validate that the attachment name doesn't contain a '.'
                            if (name.includes(".")) {
                                console.warn(`Skipping attachment '${name}' for run ${payload.id}: Invalid attachment name. ` + `Attachment names must not contain periods ('.'). Please rename the attachment and try again.`);
                                continue;
                            }
                            accumulatedParts.push({
                                name: `attachment.${payload.id}.${name}`,
                                payload: new Blob([
                                    content
                                ], {
                                    type: `${contentType}; length=${content.byteLength}`
                                })
                            });
                        }
                    }
                }
                // compute context
                accumulatedContext.push(`trace=${payload.trace_id},id=${payload.id}`);
            }
        }
        await this._sendMultipartRequest(accumulatedParts, accumulatedContext.join("; "), options);
    }
    async _createNodeFetchBody(parts, boundary) {
        // Create multipart form data manually using Blobs
        const chunks = [];
        for (const part of parts){
            // Add field boundary
            chunks.push(new Blob([
                `--${boundary}\r\n`
            ]));
            chunks.push(new Blob([
                `Content-Disposition: form-data; name="${part.name}"\r\n`,
                `Content-Type: ${part.payload.type}\r\n\r\n`
            ]));
            chunks.push(part.payload);
            chunks.push(new Blob([
                "\r\n"
            ]));
        }
        // Add final boundary
        chunks.push(new Blob([
            `--${boundary}--\r\n`
        ]));
        // Combine all chunks into a single Blob
        const body = new Blob(chunks);
        // Convert Blob to ArrayBuffer for compatibility
        const arrayBuffer = await body.arrayBuffer();
        return arrayBuffer;
    }
    async _createMultipartStream(parts, boundary) {
        const encoder = new TextEncoder();
        // Create a ReadableStream for streaming the multipart data
        // Only do special handling if we're using node-fetch
        const stream = new ReadableStream({
            async start (controller) {
                // Helper function to write a chunk to the stream
                const writeChunk = async (chunk)=>{
                    if (typeof chunk === "string") {
                        controller.enqueue(encoder.encode(chunk));
                    } else {
                        controller.enqueue(chunk);
                    }
                };
                // Write each part to the stream
                for (const part of parts){
                    // Write boundary and headers
                    await writeChunk(`--${boundary}\r\n`);
                    await writeChunk(`Content-Disposition: form-data; name="${part.name}"\r\n`);
                    await writeChunk(`Content-Type: ${part.payload.type}\r\n\r\n`);
                    // Write the payload
                    const payloadStream = part.payload.stream();
                    const reader = payloadStream.getReader();
                    try {
                        let result;
                        while(!(result = await reader.read()).done){
                            controller.enqueue(result.value);
                        }
                    } finally{
                        reader.releaseLock();
                    }
                    await writeChunk("\r\n");
                }
                // Write final boundary
                await writeChunk(`--${boundary}--\r\n`);
                controller.close();
            }
        });
        return stream;
    }
    async _sendMultipartRequest(parts, context, options) {
        // Create multipart form data boundary
        const boundary = "----LangSmithFormBoundary" + Math.random().toString(36).slice(2);
        const isNodeFetch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_globalFetchImplementationIsNodeFetch"])();
        const buildBuffered = ()=>this._createNodeFetchBody(parts, boundary);
        const buildStream = ()=>this._createMultipartStream(parts, boundary);
        const send = async (body)=>{
            const headers = {
                ...this.headers,
                "Content-Type": `multipart/form-data; boundary=${boundary}`
            };
            if (options?.apiKey !== undefined) {
                headers["x-api-key"] = options.apiKey;
            }
            return this.batchIngestCaller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${options?.apiUrl ?? this.apiUrl}/runs/multipart`, {
                method: "POST",
                headers,
                body,
                duplex: "half",
                signal: AbortSignal.timeout(this.timeout_ms),
                ...this.fetchOptions
            });
        };
        try {
            let res;
            let streamedAttempt = false;
            // attempt stream only if not disabled and not using node-fetch
            if (!isNodeFetch && !this.multipartStreamingDisabled) {
                streamedAttempt = true;
                res = await send(await buildStream());
            } else {
                res = await send(await buildBuffered());
            }
            // if stream fails, fallback to buffered body
            if ((!this.multipartStreamingDisabled || streamedAttempt) && res.status === 422 && (options?.apiUrl ?? this.apiUrl) !== DEFAULT_API_URL) {
                console.warn(`Streaming multipart upload to ${options?.apiUrl ?? this.apiUrl}/runs/multipart failed. ` + `This usually means the host does not support chunked uploads. ` + `Retrying with a buffered upload for operation "${context}".`);
                // Disable streaming for future requests
                this.multipartStreamingDisabled = true;
                // retry with fully-buffered body
                res = await send(await buildBuffered());
            }
            // raise if still failing
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(res, "ingest multipart runs", true);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (e) {
            console.warn(`${e.message.trim()}\n\nContext: ${context}`);
        }
    }
    async updateRun(runId, run, options) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(runId);
        if (run.inputs) {
            run.inputs = await this.processInputs(run.inputs);
        }
        if (run.outputs) {
            run.outputs = await this.processOutputs(run.outputs);
        }
        // TODO: Untangle types
        const data = {
            ...run,
            id: runId
        };
        if (!this._filterForSampling([
            data
        ], true).length) {
            return;
        }
        if (this.autoBatchTracing && data.trace_id !== undefined && data.dotted_order !== undefined) {
            const otelContext = this._cloneCurrentOTELContext();
            if (run.end_time !== undefined && data.parent_run_id === undefined && this.blockOnRootRunFinalization && !this.manualFlushMode) {
                // Trigger batches as soon as a root trace ends and wait to ensure trace finishes
                // in serverless environments.
                await this.processRunOperation({
                    action: "update",
                    item: data,
                    otelContext,
                    apiKey: options?.apiKey,
                    apiUrl: options?.apiUrl
                }).catch(console.error);
                return;
            } else {
                void this.processRunOperation({
                    action: "update",
                    item: data,
                    otelContext,
                    apiKey: options?.apiKey,
                    apiUrl: options?.apiUrl
                }).catch(console.error);
            }
            return;
        }
        const headers = {
            ...this.headers,
            "Content-Type": "application/json"
        };
        if (options?.apiKey !== undefined) {
            headers["x-api-key"] = options.apiKey;
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${options?.apiUrl ?? this.apiUrl}/runs/${runId}`, {
            method: "PATCH",
            headers,
            body: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(run, `Serializing payload to update run with id: ${runId}`),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "update run", true);
    }
    async readRun(runId, { loadChildRuns } = {
        loadChildRuns: false
    }) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(runId);
        let run = await this._get(`/runs/${runId}`);
        if (loadChildRuns) {
            run = await this._loadChildRuns(run);
        }
        return run;
    }
    async getRunUrl({ runId, run, projectOpts }) {
        if (run !== undefined) {
            let sessionId;
            if (run.session_id) {
                sessionId = run.session_id;
            } else if (projectOpts?.projectName) {
                sessionId = (await this.readProject({
                    projectName: projectOpts?.projectName
                })).id;
            } else if (projectOpts?.projectId) {
                sessionId = projectOpts?.projectId;
            } else {
                const project = await this.readProject({
                    projectName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("PROJECT") || "default"
                });
                sessionId = project.id;
            }
            const tenantId = await this._getTenantId();
            return `${this.getHostUrl()}/o/${tenantId}/projects/p/${sessionId}/r/${run.id}?poll=true`;
        } else if (runId !== undefined) {
            const run_ = await this.readRun(runId);
            if (!run_.app_path) {
                throw new Error(`Run ${runId} has no app_path`);
            }
            const baseUrl = this.getHostUrl();
            return `${baseUrl}${run_.app_path}`;
        } else {
            throw new Error("Must provide either runId or run");
        }
    }
    async _loadChildRuns(run) {
        const childRuns = await toArray(this.listRuns({
            isRoot: false,
            projectId: run.session_id,
            traceId: run.trace_id
        }));
        const treemap = {};
        const runs = {};
        // TODO: make dotted order required when the migration finishes
        childRuns.sort((a, b)=>(a?.dotted_order ?? "").localeCompare(b?.dotted_order ?? ""));
        for (const childRun of childRuns){
            if (childRun.parent_run_id === null || childRun.parent_run_id === undefined) {
                throw new Error(`Child run ${childRun.id} has no parent`);
            }
            if (childRun.dotted_order?.startsWith(run.dotted_order ?? "") && childRun.id !== run.id) {
                if (!(childRun.parent_run_id in treemap)) {
                    treemap[childRun.parent_run_id] = [];
                }
                treemap[childRun.parent_run_id].push(childRun);
                runs[childRun.id] = childRun;
            }
        }
        run.child_runs = treemap[run.id] || [];
        for(const runId in treemap){
            if (runId !== run.id) {
                runs[runId].child_runs = treemap[runId];
            }
        }
        return run;
    }
    /**
     * List runs from the LangSmith server.
     * @param projectId - The ID of the project to filter by.
     * @param projectName - The name of the project to filter by.
     * @param parentRunId - The ID of the parent run to filter by.
     * @param traceId - The ID of the trace to filter by.
     * @param referenceExampleId - The ID of the reference example to filter by.
     * @param startTime - The start time to filter by.
     * @param isRoot - Indicates whether to only return root runs.
     * @param runType - The run type to filter by.
     * @param error - Indicates whether to filter by error runs.
     * @param id - The ID of the run to filter by.
     * @param query - The query string to filter by.
     * @param filter - The filter string to apply to the run spans.
     * @param traceFilter - The filter string to apply on the root run of the trace.
     * @param treeFilter - The filter string to apply on other runs in the trace.
     * @param limit - The maximum number of runs to retrieve.
     * @returns {AsyncIterable<Run>} - The runs.
     *
     * @example
     * // List all runs in a project
     * const projectRuns = client.listRuns({ projectName: "<your_project>" });
     *
     * @example
     * // List LLM and Chat runs in the last 24 hours
     * const todaysLLMRuns = client.listRuns({
     *   projectName: "<your_project>",
     *   start_time: new Date(Date.now() - 24 * 60 * 60 * 1000),
     *   run_type: "llm",
     * });
     *
     * @example
     * // List traces in a project
     * const rootRuns = client.listRuns({
     *   projectName: "<your_project>",
     *   execution_order: 1,
     * });
     *
     * @example
     * // List runs without errors
     * const correctRuns = client.listRuns({
     *   projectName: "<your_project>",
     *   error: false,
     * });
     *
     * @example
     * // List runs by run ID
     * const runIds = [
     *   "a36092d2-4ad5-4fb4-9c0d-0dba9a2ed836",
     *   "9398e6be-964f-4aa4-8ae9-ad78cd4b7074",
     * ];
     * const selectedRuns = client.listRuns({ run_ids: runIds });
     *
     * @example
     * // List all "chain" type runs that took more than 10 seconds and had `total_tokens` greater than 5000
     * const chainRuns = client.listRuns({
     *   projectName: "<your_project>",
     *   filter: 'and(eq(run_type, "chain"), gt(latency, 10), gt(total_tokens, 5000))',
     * });
     *
     * @example
     * // List all runs called "extractor" whose root of the trace was assigned feedback "user_score" score of 1
     * const goodExtractorRuns = client.listRuns({
     *   projectName: "<your_project>",
     *   filter: 'eq(name, "extractor")',
     *   traceFilter: 'and(eq(feedback_key, "user_score"), eq(feedback_score, 1))',
     * });
     *
     * @example
     * // List all runs that started after a specific timestamp and either have "error" not equal to null or a "Correctness" feedback score equal to 0
     * const complexRuns = client.listRuns({
     *   projectName: "<your_project>",
     *   filter: 'and(gt(start_time, "2023-07-15T12:34:56Z"), or(neq(error, null), and(eq(feedback_key, "Correctness"), eq(feedback_score, 0.0))))',
     * });
     *
     * @example
     * // List all runs where `tags` include "experimental" or "beta" and `latency` is greater than 2 seconds
     * const taggedRuns = client.listRuns({
     *   projectName: "<your_project>",
     *   filter: 'and(or(has(tags, "experimental"), has(tags, "beta")), gt(latency, 2))',
     * });
     */ async *listRuns(props) {
        const { projectId, projectName, parentRunId, traceId, referenceExampleId, startTime, executionOrder, isRoot, runType, error, id, query, filter, traceFilter, treeFilter, limit, select, order } = props;
        let projectIds = [];
        if (projectId) {
            projectIds = Array.isArray(projectId) ? projectId : [
                projectId
            ];
        }
        if (projectName) {
            const projectNames = Array.isArray(projectName) ? projectName : [
                projectName
            ];
            const projectIds_ = await Promise.all(projectNames.map((name)=>this.readProject({
                    projectName: name
                }).then((project)=>project.id)));
            projectIds.push(...projectIds_);
        }
        const default_select = [
            "app_path",
            "completion_cost",
            "completion_tokens",
            "dotted_order",
            "end_time",
            "error",
            "events",
            "extra",
            "feedback_stats",
            "first_token_time",
            "id",
            "inputs",
            "name",
            "outputs",
            "parent_run_id",
            "parent_run_ids",
            "prompt_cost",
            "prompt_tokens",
            "reference_example_id",
            "run_type",
            "session_id",
            "start_time",
            "status",
            "tags",
            "total_cost",
            "total_tokens",
            "trace_id"
        ];
        const body = {
            session: projectIds.length ? projectIds : null,
            run_type: runType,
            reference_example: referenceExampleId,
            query,
            filter,
            trace_filter: traceFilter,
            tree_filter: treeFilter,
            execution_order: executionOrder,
            parent_run: parentRunId,
            start_time: startTime ? startTime.toISOString() : null,
            error,
            id,
            limit,
            trace: traceId,
            select: select ? select : default_select,
            is_root: isRoot,
            order
        };
        let runsYielded = 0;
        for await (const runs of this._getCursorPaginatedList("/runs/query", body)){
            if (limit) {
                if (runsYielded >= limit) {
                    break;
                }
                if (runs.length + runsYielded > limit) {
                    const newRuns = runs.slice(0, limit - runsYielded);
                    yield* newRuns;
                    break;
                }
                runsYielded += runs.length;
                yield* runs;
            } else {
                yield* runs;
            }
        }
    }
    async *listGroupRuns(props) {
        const { projectId, projectName, groupBy, filter, startTime, endTime, limit, offset } = props;
        const sessionId = projectId || (await this.readProject({
            projectName
        })).id;
        const baseBody = {
            session_id: sessionId,
            group_by: groupBy,
            filter,
            start_time: startTime ? startTime.toISOString() : null,
            end_time: endTime ? endTime.toISOString() : null,
            limit: Number(limit) || 100
        };
        let currentOffset = Number(offset) || 0;
        const path = "/runs/group";
        const url = `${this.apiUrl}${path}`;
        while(true){
            const currentBody = {
                ...baseBody,
                offset: currentOffset
            };
            // Remove undefined values from the payload
            const filteredPayload = Object.fromEntries(Object.entries(currentBody).filter(([_, value])=>value !== undefined));
            const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(), url, {
                method: "POST",
                headers: {
                    ...this.headers,
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(filteredPayload),
                signal: AbortSignal.timeout(this.timeout_ms),
                ...this.fetchOptions
            });
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, `Failed to fetch ${path}`);
            const items = await response.json();
            const { groups, total } = items;
            if (groups.length === 0) {
                break;
            }
            for (const thread of groups){
                yield thread;
            }
            currentOffset += groups.length;
            if (currentOffset >= total) {
                break;
            }
        }
    }
    async getRunStats({ id, trace, parentRun, runType, projectNames, projectIds, referenceExampleIds, startTime, endTime, error, query, filter, traceFilter, treeFilter, isRoot, dataSourceType }) {
        let projectIds_ = projectIds || [];
        if (projectNames) {
            projectIds_ = [
                ...projectIds || [],
                ...await Promise.all(projectNames.map((name)=>this.readProject({
                        projectName: name
                    }).then((project)=>project.id)))
            ];
        }
        const payload = {
            id,
            trace,
            parent_run: parentRun,
            run_type: runType,
            session: projectIds_,
            reference_example: referenceExampleIds,
            start_time: startTime,
            end_time: endTime,
            error,
            query,
            filter,
            trace_filter: traceFilter,
            tree_filter: treeFilter,
            is_root: isRoot,
            data_source_type: dataSourceType
        };
        // Remove undefined values from the payload
        const filteredPayload = Object.fromEntries(Object.entries(payload).filter(([_, value])=>value !== undefined));
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/runs/stats`, {
            method: "POST",
            headers: this.headers,
            body: JSON.stringify(filteredPayload),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        const result = await response.json();
        return result;
    }
    async shareRun(runId, { shareId } = {}) {
        const data = {
            run_id: runId,
            share_token: shareId || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"]()
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(runId);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/runs/${runId}/share`, {
            method: "PUT",
            headers: this.headers,
            body: JSON.stringify(data),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        const result = await response.json();
        if (result === null || !("share_token" in result)) {
            throw new Error("Invalid response from server");
        }
        return `${this.getHostUrl()}/public/${result["share_token"]}/r`;
    }
    async unshareRun(runId) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(runId);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/runs/${runId}/share`, {
            method: "DELETE",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "unshare run", true);
    }
    async readRunSharedLink(runId) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(runId);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/runs/${runId}/share`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        const result = await response.json();
        if (result === null || !("share_token" in result)) {
            return undefined;
        }
        return `${this.getHostUrl()}/public/${result["share_token"]}/r`;
    }
    async listSharedRuns(shareToken, { runIds } = {}) {
        const queryParams = new URLSearchParams({
            share_token: shareToken
        });
        if (runIds !== undefined) {
            for (const runId of runIds){
                queryParams.append("id", runId);
            }
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(shareToken);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/public/${shareToken}/runs${queryParams}`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        const runs = await response.json();
        return runs;
    }
    async readDatasetSharedSchema(datasetId, datasetName) {
        if (!datasetId && !datasetName) {
            throw new Error("Either datasetId or datasetName must be given");
        }
        if (!datasetId) {
            const dataset = await this.readDataset({
                datasetName
            });
            datasetId = dataset.id;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(datasetId);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets/${datasetId}/share`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        const shareSchema = await response.json();
        shareSchema.url = `${this.getHostUrl()}/public/${shareSchema.share_token}/d`;
        return shareSchema;
    }
    async shareDataset(datasetId, datasetName) {
        if (!datasetId && !datasetName) {
            throw new Error("Either datasetId or datasetName must be given");
        }
        if (!datasetId) {
            const dataset = await this.readDataset({
                datasetName
            });
            datasetId = dataset.id;
        }
        const data = {
            dataset_id: datasetId
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(datasetId);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets/${datasetId}/share`, {
            method: "PUT",
            headers: this.headers,
            body: JSON.stringify(data),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        const shareSchema = await response.json();
        shareSchema.url = `${this.getHostUrl()}/public/${shareSchema.share_token}/d`;
        return shareSchema;
    }
    async unshareDataset(datasetId) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(datasetId);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets/${datasetId}/share`, {
            method: "DELETE",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "unshare dataset", true);
    }
    async readSharedDataset(shareToken) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(shareToken);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/public/${shareToken}/datasets`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        const dataset = await response.json();
        return dataset;
    }
    /**
     * Get shared examples.
     *
     * @param {string} shareToken The share token to get examples for. A share token is the UUID (or LangSmith URL, including UUID) generated when explicitly marking an example as public.
     * @param {Object} [options] Additional options for listing the examples.
     * @param {string[] | undefined} [options.exampleIds] A list of example IDs to filter by.
     * @returns {Promise<Example[]>} The shared examples.
     */ async listSharedExamples(shareToken, options) {
        const params = {};
        if (options?.exampleIds) {
            params.id = options.exampleIds;
        }
        const urlParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value])=>{
            if (Array.isArray(value)) {
                value.forEach((v)=>urlParams.append(key, v));
            } else {
                urlParams.append(key, value);
            }
        });
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/public/${shareToken}/examples?${urlParams.toString()}`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        const result = await response.json();
        if (!response.ok) {
            if ("detail" in result) {
                throw new Error(`Failed to list shared examples.\nStatus: ${response.status}\nMessage: ${Array.isArray(result.detail) ? result.detail.join("\n") : "Unspecified error"}`);
            }
            throw new Error(`Failed to list shared examples: ${response.status} ${response.statusText}`);
        }
        return result.map((example)=>({
                ...example,
                _hostUrl: this.getHostUrl()
            }));
    }
    async createProject({ projectName, description = null, metadata = null, upsert = false, projectExtra = null, referenceDatasetId = null }) {
        const upsert_ = upsert ? `?upsert=true` : "";
        const endpoint = `${this.apiUrl}/sessions${upsert_}`;
        const extra = projectExtra || {};
        if (metadata) {
            extra["metadata"] = metadata;
        }
        const body = {
            name: projectName,
            extra,
            description
        };
        if (referenceDatasetId !== null) {
            body["reference_dataset_id"] = referenceDatasetId;
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), endpoint, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(body),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "create project");
        const result = await response.json();
        return result;
    }
    async updateProject(projectId, { name = null, description = null, metadata = null, projectExtra = null, endTime = null }) {
        const endpoint = `${this.apiUrl}/sessions/${projectId}`;
        let extra = projectExtra;
        if (metadata) {
            extra = {
                ...extra || {},
                metadata
            };
        }
        const body = {
            name,
            extra,
            description,
            end_time: endTime ? new Date(endTime).toISOString() : null
        };
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), endpoint, {
            method: "PATCH",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(body),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "update project");
        const result = await response.json();
        return result;
    }
    async hasProject({ projectId, projectName }) {
        // TODO: Add a head request
        let path = "/sessions";
        const params = new URLSearchParams();
        if (projectId !== undefined && projectName !== undefined) {
            throw new Error("Must provide either projectName or projectId, not both");
        } else if (projectId !== undefined) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(projectId);
            path += `/${projectId}`;
        } else if (projectName !== undefined) {
            params.append("name", projectName);
        } else {
            throw new Error("Must provide projectName or projectId");
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}${path}?${params}`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        // consume the response body to release the connection
        // https://undici.nodejs.org/#/?id=garbage-collection
        try {
            const result = await response.json();
            if (!response.ok) {
                return false;
            }
            // If it's OK and we're querying by name, need to check the list is not empty
            if (Array.isArray(result)) {
                return result.length > 0;
            }
            // projectId querying
            return true;
        } catch (e) {
            return false;
        }
    }
    async readProject({ projectId, projectName, includeStats }) {
        let path = "/sessions";
        const params = new URLSearchParams();
        if (projectId !== undefined && projectName !== undefined) {
            throw new Error("Must provide either projectName or projectId, not both");
        } else if (projectId !== undefined) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(projectId);
            path += `/${projectId}`;
        } else if (projectName !== undefined) {
            params.append("name", projectName);
        } else {
            throw new Error("Must provide projectName or projectId");
        }
        if (includeStats !== undefined) {
            params.append("include_stats", includeStats.toString());
        }
        const response = await this._get(path, params);
        let result;
        if (Array.isArray(response)) {
            if (response.length === 0) {
                throw new Error(`Project[id=${projectId}, name=${projectName}] not found`);
            }
            result = response[0];
        } else {
            result = response;
        }
        return result;
    }
    async getProjectUrl({ projectId, projectName }) {
        if (projectId === undefined && projectName === undefined) {
            throw new Error("Must provide either projectName or projectId");
        }
        const project = await this.readProject({
            projectId,
            projectName
        });
        const tenantId = await this._getTenantId();
        return `${this.getHostUrl()}/o/${tenantId}/projects/p/${project.id}`;
    }
    async getDatasetUrl({ datasetId, datasetName }) {
        if (datasetId === undefined && datasetName === undefined) {
            throw new Error("Must provide either datasetName or datasetId");
        }
        const dataset = await this.readDataset({
            datasetId,
            datasetName
        });
        const tenantId = await this._getTenantId();
        return `${this.getHostUrl()}/o/${tenantId}/datasets/${dataset.id}`;
    }
    async _getTenantId() {
        if (this._tenantId !== null) {
            return this._tenantId;
        }
        const queryParams = new URLSearchParams({
            limit: "1"
        });
        for await (const projects of this._getPaginated("/sessions", queryParams)){
            this._tenantId = projects[0].tenant_id;
            return projects[0].tenant_id;
        }
        throw new Error("No projects found to resolve tenant.");
    }
    async *listProjects({ projectIds, name, nameContains, referenceDatasetId, referenceDatasetName, referenceFree, metadata } = {}) {
        const params = new URLSearchParams();
        if (projectIds !== undefined) {
            for (const projectId of projectIds){
                params.append("id", projectId);
            }
        }
        if (name !== undefined) {
            params.append("name", name);
        }
        if (nameContains !== undefined) {
            params.append("name_contains", nameContains);
        }
        if (referenceDatasetId !== undefined) {
            params.append("reference_dataset", referenceDatasetId);
        } else if (referenceDatasetName !== undefined) {
            const dataset = await this.readDataset({
                datasetName: referenceDatasetName
            });
            params.append("reference_dataset", dataset.id);
        }
        if (referenceFree !== undefined) {
            params.append("reference_free", referenceFree.toString());
        }
        if (metadata !== undefined) {
            params.append("metadata", JSON.stringify(metadata));
        }
        for await (const projects of this._getPaginated("/sessions", params)){
            yield* projects;
        }
    }
    async deleteProject({ projectId, projectName }) {
        let projectId_;
        if (projectId === undefined && projectName === undefined) {
            throw new Error("Must provide projectName or projectId");
        } else if (projectId !== undefined && projectName !== undefined) {
            throw new Error("Must provide either projectName or projectId, not both");
        } else if (projectId === undefined) {
            projectId_ = (await this.readProject({
                projectName
            })).id;
        } else {
            projectId_ = projectId;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(projectId_);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/sessions/${projectId_}`, {
            method: "DELETE",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, `delete session ${projectId_} (${projectName})`, true);
    }
    async uploadCsv({ csvFile, fileName, inputKeys, outputKeys, description, dataType, name }) {
        const url = `${this.apiUrl}/datasets/upload`;
        const formData = new FormData();
        formData.append("file", csvFile, fileName);
        inputKeys.forEach((key)=>{
            formData.append("input_keys", key);
        });
        outputKeys.forEach((key)=>{
            formData.append("output_keys", key);
        });
        if (description) {
            formData.append("description", description);
        }
        if (dataType) {
            formData.append("data_type", dataType);
        }
        if (name) {
            formData.append("name", name);
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), url, {
            method: "POST",
            headers: this.headers,
            body: formData,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "upload CSV");
        const result = await response.json();
        return result;
    }
    async createDataset(name, { description, dataType, inputsSchema, outputsSchema, metadata } = {}) {
        const body = {
            name,
            description,
            extra: metadata ? {
                metadata
            } : undefined
        };
        if (dataType) {
            body.data_type = dataType;
        }
        if (inputsSchema) {
            body.inputs_schema_definition = inputsSchema;
        }
        if (outputsSchema) {
            body.outputs_schema_definition = outputsSchema;
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets`, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(body),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "create dataset");
        const result = await response.json();
        return result;
    }
    async readDataset({ datasetId, datasetName }) {
        let path = "/datasets";
        // limit to 1 result
        const params = new URLSearchParams({
            limit: "1"
        });
        if (datasetId && datasetName) {
            throw new Error("Must provide either datasetName or datasetId, not both");
        } else if (datasetId) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(datasetId);
            path += `/${datasetId}`;
        } else if (datasetName) {
            params.append("name", datasetName);
        } else {
            throw new Error("Must provide datasetName or datasetId");
        }
        const response = await this._get(path, params);
        let result;
        if (Array.isArray(response)) {
            if (response.length === 0) {
                throw new Error(`Dataset[id=${datasetId}, name=${datasetName}] not found`);
            }
            result = response[0];
        } else {
            result = response;
        }
        return result;
    }
    async hasDataset({ datasetId, datasetName }) {
        try {
            await this.readDataset({
                datasetId,
                datasetName
            });
            return true;
        } catch (e) {
            if (// eslint-disable-next-line no-instanceof/no-instanceof
            e instanceof Error && e.message.toLocaleLowerCase().includes("not found")) {
                return false;
            }
            throw e;
        }
    }
    async diffDatasetVersions({ datasetId, datasetName, fromVersion, toVersion }) {
        let datasetId_ = datasetId;
        if (datasetId_ === undefined && datasetName === undefined) {
            throw new Error("Must provide either datasetName or datasetId");
        } else if (datasetId_ !== undefined && datasetName !== undefined) {
            throw new Error("Must provide either datasetName or datasetId, not both");
        } else if (datasetId_ === undefined) {
            const dataset = await this.readDataset({
                datasetName
            });
            datasetId_ = dataset.id;
        }
        const urlParams = new URLSearchParams({
            from_version: typeof fromVersion === "string" ? fromVersion : fromVersion.toISOString(),
            to_version: typeof toVersion === "string" ? toVersion : toVersion.toISOString()
        });
        const response = await this._get(`/datasets/${datasetId_}/versions/diff`, urlParams);
        return response;
    }
    async readDatasetOpenaiFinetuning({ datasetId, datasetName }) {
        const path = "/datasets";
        if (datasetId !== undefined) {
        // do nothing
        } else if (datasetName !== undefined) {
            datasetId = (await this.readDataset({
                datasetName
            })).id;
        } else {
            throw new Error("Must provide either datasetName or datasetId");
        }
        const response = await this._getResponse(`${path}/${datasetId}/openai_ft`);
        const datasetText = await response.text();
        const dataset = datasetText.trim().split("\n").map((line)=>JSON.parse(line));
        return dataset;
    }
    async *listDatasets({ limit = 100, offset = 0, datasetIds, datasetName, datasetNameContains, metadata } = {}) {
        const path = "/datasets";
        const params = new URLSearchParams({
            limit: limit.toString(),
            offset: offset.toString()
        });
        if (datasetIds !== undefined) {
            for (const id_ of datasetIds){
                params.append("id", id_);
            }
        }
        if (datasetName !== undefined) {
            params.append("name", datasetName);
        }
        if (datasetNameContains !== undefined) {
            params.append("name_contains", datasetNameContains);
        }
        if (metadata !== undefined) {
            params.append("metadata", JSON.stringify(metadata));
        }
        for await (const datasets of this._getPaginated(path, params)){
            yield* datasets;
        }
    }
    /**
     * Update a dataset
     * @param props The dataset details to update
     * @returns The updated dataset
     */ async updateDataset(props) {
        const { datasetId, datasetName, ...update } = props;
        if (!datasetId && !datasetName) {
            throw new Error("Must provide either datasetName or datasetId");
        }
        const _datasetId = datasetId ?? (await this.readDataset({
            datasetName
        })).id;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(_datasetId);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets/${_datasetId}`, {
            method: "PATCH",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(update),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "update dataset");
        return await response.json();
    }
    /**
     * Updates a tag on a dataset.
     *
     * If the tag is already assigned to a different version of this dataset,
     * the tag will be moved to the new version. The as_of parameter is used to
     * determine which version of the dataset to apply the new tags to.
     *
     * It must be an exact version of the dataset to succeed. You can
     * use the "readDatasetVersion" method to find the exact version
     * to apply the tags to.
     * @param params.datasetId The ID of the dataset to update. Must be provided if "datasetName" is not provided.
     * @param params.datasetName The name of the dataset to update. Must be provided if "datasetId" is not provided.
     * @param params.asOf The timestamp of the dataset to apply the new tags to.
     * @param params.tag The new tag to apply to the dataset.
     */ async updateDatasetTag(props) {
        const { datasetId, datasetName, asOf, tag } = props;
        if (!datasetId && !datasetName) {
            throw new Error("Must provide either datasetName or datasetId");
        }
        const _datasetId = datasetId ?? (await this.readDataset({
            datasetName
        })).id;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(_datasetId);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets/${_datasetId}/tags`, {
            method: "PUT",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                as_of: typeof asOf === "string" ? asOf : asOf.toISOString(),
                tag
            }),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "update dataset tags");
    }
    async deleteDataset({ datasetId, datasetName }) {
        let path = "/datasets";
        let datasetId_ = datasetId;
        if (datasetId !== undefined && datasetName !== undefined) {
            throw new Error("Must provide either datasetName or datasetId, not both");
        } else if (datasetName !== undefined) {
            const dataset = await this.readDataset({
                datasetName
            });
            datasetId_ = dataset.id;
        }
        if (datasetId_ !== undefined) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(datasetId_);
            path += `/${datasetId_}`;
        } else {
            throw new Error("Must provide datasetName or datasetId");
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), this.apiUrl + path, {
            method: "DELETE",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, `delete ${path}`);
        await response.json();
    }
    async indexDataset({ datasetId, datasetName, tag }) {
        let datasetId_ = datasetId;
        if (!datasetId_ && !datasetName) {
            throw new Error("Must provide either datasetName or datasetId");
        } else if (datasetId_ && datasetName) {
            throw new Error("Must provide either datasetName or datasetId, not both");
        } else if (!datasetId_) {
            const dataset = await this.readDataset({
                datasetName
            });
            datasetId_ = dataset.id;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(datasetId_);
        const data = {
            tag: tag
        };
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets/${datasetId_}/index`, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(data),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "index dataset");
        await response.json();
    }
    /**
     * Lets you run a similarity search query on a dataset.
     *
     * Requires the dataset to be indexed. Please see the `indexDataset` method to set up indexing.
     *
     * @param inputs      The input on which to run the similarity search. Must have the
     *                    same schema as the dataset.
     *
     * @param datasetId   The dataset to search for similar examples.
     *
     * @param limit       The maximum number of examples to return. Will return the top `limit` most
     *                    similar examples in order of most similar to least similar. If no similar
     *                    examples are found, random examples will be returned.
     *
     * @param filter      A filter string to apply to the search. Only examples will be returned that
     *                    match the filter string. Some examples of filters
     *
     *                    - eq(metadata.mykey, "value")
     *                    - and(neq(metadata.my.nested.key, "value"), neq(metadata.mykey, "value"))
     *                    - or(eq(metadata.mykey, "value"), eq(metadata.mykey, "othervalue"))
     *
     * @returns           A list of similar examples.
     *
     *
     * @example
     * dataset_id = "123e4567-e89b-12d3-a456-426614174000"
     * inputs = {"text": "How many people live in Berlin?"}
     * limit = 5
     * examples = await client.similarExamples(inputs, dataset_id, limit)
     */ async similarExamples(inputs, datasetId, limit, { filter } = {}) {
        const data = {
            limit: limit,
            inputs: inputs
        };
        if (filter !== undefined) {
            data["filter"] = filter;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(datasetId);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets/${datasetId}/search`, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(data),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "fetch similar examples");
        const result = await response.json();
        return result["examples"];
    }
    async createExample(inputsOrUpdate, outputs, options) {
        if (isExampleCreate(inputsOrUpdate)) {
            if (outputs !== undefined || options !== undefined) {
                throw new Error("Cannot provide outputs or options when using ExampleCreate object");
            }
        }
        let datasetId_ = outputs ? options?.datasetId : inputsOrUpdate.dataset_id;
        const datasetName_ = outputs ? options?.datasetName : inputsOrUpdate.dataset_name;
        if (datasetId_ === undefined && datasetName_ === undefined) {
            throw new Error("Must provide either datasetName or datasetId");
        } else if (datasetId_ !== undefined && datasetName_ !== undefined) {
            throw new Error("Must provide either datasetName or datasetId, not both");
        } else if (datasetId_ === undefined) {
            const dataset = await this.readDataset({
                datasetName: datasetName_
            });
            datasetId_ = dataset.id;
        }
        const createdAt_ = (outputs ? options?.createdAt : inputsOrUpdate.created_at) || new Date();
        let data;
        if (!isExampleCreate(inputsOrUpdate)) {
            data = {
                inputs: inputsOrUpdate,
                outputs,
                created_at: createdAt_?.toISOString(),
                id: options?.exampleId,
                metadata: options?.metadata,
                split: options?.split,
                source_run_id: options?.sourceRunId,
                use_source_run_io: options?.useSourceRunIO,
                use_source_run_attachments: options?.useSourceRunAttachments,
                attachments: options?.attachments
            };
        } else {
            data = inputsOrUpdate;
        }
        const response = await this._uploadExamplesMultipart(datasetId_, [
            data
        ]);
        const example = await this.readExample(response.example_ids?.[0] ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"]());
        return example;
    }
    async createExamples(propsOrUploads) {
        if (Array.isArray(propsOrUploads)) {
            if (propsOrUploads.length === 0) {
                return [];
            }
            const uploads = propsOrUploads;
            let datasetId_ = uploads[0].dataset_id;
            const datasetName_ = uploads[0].dataset_name;
            if (datasetId_ === undefined && datasetName_ === undefined) {
                throw new Error("Must provide either datasetName or datasetId");
            } else if (datasetId_ !== undefined && datasetName_ !== undefined) {
                throw new Error("Must provide either datasetName or datasetId, not both");
            } else if (datasetId_ === undefined) {
                const dataset = await this.readDataset({
                    datasetName: datasetName_
                });
                datasetId_ = dataset.id;
            }
            const response = await this._uploadExamplesMultipart(datasetId_, uploads);
            const examples = await Promise.all(response.example_ids.map((id)=>this.readExample(id)));
            return examples;
        }
        const { inputs, outputs, metadata, splits, sourceRunIds, useSourceRunIOs, useSourceRunAttachments, attachments, exampleIds, datasetId, datasetName } = propsOrUploads;
        if (inputs === undefined) {
            throw new Error("Must provide inputs when using legacy parameters");
        }
        let datasetId_ = datasetId;
        const datasetName_ = datasetName;
        if (datasetId_ === undefined && datasetName_ === undefined) {
            throw new Error("Must provide either datasetName or datasetId");
        } else if (datasetId_ !== undefined && datasetName_ !== undefined) {
            throw new Error("Must provide either datasetName or datasetId, not both");
        } else if (datasetId_ === undefined) {
            const dataset = await this.readDataset({
                datasetName: datasetName_
            });
            datasetId_ = dataset.id;
        }
        const formattedExamples = inputs.map((input, idx)=>{
            return {
                dataset_id: datasetId_,
                inputs: input,
                outputs: outputs?.[idx],
                metadata: metadata?.[idx],
                split: splits?.[idx],
                id: exampleIds?.[idx],
                attachments: attachments?.[idx],
                source_run_id: sourceRunIds?.[idx],
                use_source_run_io: useSourceRunIOs?.[idx],
                use_source_run_attachments: useSourceRunAttachments?.[idx]
            };
        });
        const response = await this._uploadExamplesMultipart(datasetId_, formattedExamples);
        const examples = await Promise.all(response.example_ids.map((id)=>this.readExample(id)));
        return examples;
    }
    async createLLMExample(input, generation, options) {
        return this.createExample({
            input
        }, {
            output: generation
        }, options);
    }
    async createChatExample(input, generations, options) {
        const finalInput = input.map((message)=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$messages$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isLangChainMessage"])(message)) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$messages$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["convertLangChainMessageToExample"])(message);
            }
            return message;
        });
        const finalOutput = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$messages$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isLangChainMessage"])(generations) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$messages$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["convertLangChainMessageToExample"])(generations) : generations;
        return this.createExample({
            input: finalInput
        }, {
            output: finalOutput
        }, options);
    }
    async readExample(exampleId) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(exampleId);
        const path = `/examples/${exampleId}`;
        const rawExample = await this._get(path);
        const { attachment_urls, ...rest } = rawExample;
        const example = rest;
        if (attachment_urls) {
            example.attachments = Object.entries(attachment_urls).reduce((acc, [key, value])=>{
                acc[key.slice("attachment.".length)] = {
                    presigned_url: value.presigned_url,
                    mime_type: value.mime_type
                };
                return acc;
            }, {});
        }
        return example;
    }
    async *listExamples({ datasetId, datasetName, exampleIds, asOf, splits, inlineS3Urls, metadata, limit, offset, filter, includeAttachments } = {}) {
        let datasetId_;
        if (datasetId !== undefined && datasetName !== undefined) {
            throw new Error("Must provide either datasetName or datasetId, not both");
        } else if (datasetId !== undefined) {
            datasetId_ = datasetId;
        } else if (datasetName !== undefined) {
            const dataset = await this.readDataset({
                datasetName
            });
            datasetId_ = dataset.id;
        } else {
            throw new Error("Must provide a datasetName or datasetId");
        }
        const params = new URLSearchParams({
            dataset: datasetId_
        });
        const dataset_version = asOf ? typeof asOf === "string" ? asOf : asOf?.toISOString() : undefined;
        if (dataset_version) {
            params.append("as_of", dataset_version);
        }
        const inlineS3Urls_ = inlineS3Urls ?? true;
        params.append("inline_s3_urls", inlineS3Urls_.toString());
        if (exampleIds !== undefined) {
            for (const id_ of exampleIds){
                params.append("id", id_);
            }
        }
        if (splits !== undefined) {
            for (const split of splits){
                params.append("splits", split);
            }
        }
        if (metadata !== undefined) {
            const serializedMetadata = JSON.stringify(metadata);
            params.append("metadata", serializedMetadata);
        }
        if (limit !== undefined) {
            params.append("limit", limit.toString());
        }
        if (offset !== undefined) {
            params.append("offset", offset.toString());
        }
        if (filter !== undefined) {
            params.append("filter", filter);
        }
        if (includeAttachments === true) {
            [
                "attachment_urls",
                "outputs",
                "metadata"
            ].forEach((field)=>params.append("select", field));
        }
        let i = 0;
        for await (const rawExamples of this._getPaginated("/examples", params)){
            for (const rawExample of rawExamples){
                const { attachment_urls, ...rest } = rawExample;
                const example = rest;
                if (attachment_urls) {
                    example.attachments = Object.entries(attachment_urls).reduce((acc, [key, value])=>{
                        acc[key.slice("attachment.".length)] = {
                            presigned_url: value.presigned_url,
                            mime_type: value.mime_type || undefined
                        };
                        return acc;
                    }, {});
                }
                yield example;
                i++;
            }
            if (limit !== undefined && i >= limit) {
                break;
            }
        }
    }
    async deleteExample(exampleId) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(exampleId);
        const path = `/examples/${exampleId}`;
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), this.apiUrl + path, {
            method: "DELETE",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, `delete ${path}`);
        await response.json();
    }
    async updateExample(exampleIdOrUpdate, update) {
        let exampleId;
        if (update) {
            exampleId = exampleIdOrUpdate;
        } else {
            exampleId = exampleIdOrUpdate.id;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(exampleId);
        let updateToUse;
        if (update) {
            updateToUse = {
                id: exampleId,
                ...update
            };
        } else {
            updateToUse = exampleIdOrUpdate;
        }
        let datasetId;
        if (updateToUse.dataset_id !== undefined) {
            datasetId = updateToUse.dataset_id;
        } else {
            const example = await this.readExample(exampleId);
            datasetId = example.dataset_id;
        }
        return this._updateExamplesMultipart(datasetId, [
            updateToUse
        ]);
    }
    async updateExamples(update) {
        // We will naively get dataset id from first example and assume it works for all
        let datasetId;
        if (update[0].dataset_id === undefined) {
            const example = await this.readExample(update[0].id);
            datasetId = example.dataset_id;
        } else {
            datasetId = update[0].dataset_id;
        }
        return this._updateExamplesMultipart(datasetId, update);
    }
    /**
     * Get dataset version by closest date or exact tag.
     *
     * Use this to resolve the nearest version to a given timestamp or for a given tag.
     *
     * @param options The options for getting the dataset version
     * @param options.datasetId The ID of the dataset
     * @param options.datasetName The name of the dataset
     * @param options.asOf The timestamp of the dataset to retrieve
     * @param options.tag The tag of the dataset to retrieve
     * @returns The dataset version
     */ async readDatasetVersion({ datasetId, datasetName, asOf, tag }) {
        let resolvedDatasetId;
        if (!datasetId) {
            const dataset = await this.readDataset({
                datasetName
            });
            resolvedDatasetId = dataset.id;
        } else {
            resolvedDatasetId = datasetId;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(resolvedDatasetId);
        if (asOf && tag || !asOf && !tag) {
            throw new Error("Exactly one of asOf and tag must be specified.");
        }
        const params = new URLSearchParams();
        if (asOf !== undefined) {
            params.append("as_of", typeof asOf === "string" ? asOf : asOf.toISOString());
        }
        if (tag !== undefined) {
            params.append("tag", tag);
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets/${resolvedDatasetId}/version?${params.toString()}`, {
            method: "GET",
            headers: {
                ...this.headers
            },
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "read dataset version");
        return await response.json();
    }
    async listDatasetSplits({ datasetId, datasetName, asOf }) {
        let datasetId_;
        if (datasetId === undefined && datasetName === undefined) {
            throw new Error("Must provide dataset name or ID");
        } else if (datasetId !== undefined && datasetName !== undefined) {
            throw new Error("Must provide either datasetName or datasetId, not both");
        } else if (datasetId === undefined) {
            const dataset = await this.readDataset({
                datasetName
            });
            datasetId_ = dataset.id;
        } else {
            datasetId_ = datasetId;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(datasetId_);
        const params = new URLSearchParams();
        const dataset_version = asOf ? typeof asOf === "string" ? asOf : asOf?.toISOString() : undefined;
        if (dataset_version) {
            params.append("as_of", dataset_version);
        }
        const response = await this._get(`/datasets/${datasetId_}/splits`, params);
        return response;
    }
    async updateDatasetSplits({ datasetId, datasetName, splitName, exampleIds, remove = false }) {
        let datasetId_;
        if (datasetId === undefined && datasetName === undefined) {
            throw new Error("Must provide dataset name or ID");
        } else if (datasetId !== undefined && datasetName !== undefined) {
            throw new Error("Must provide either datasetName or datasetId, not both");
        } else if (datasetId === undefined) {
            const dataset = await this.readDataset({
                datasetName
            });
            datasetId_ = dataset.id;
        } else {
            datasetId_ = datasetId;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(datasetId_);
        const data = {
            split_name: splitName,
            examples: exampleIds.map((id)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(id);
                return id;
            }),
            remove
        };
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets/${datasetId_}/splits`, {
            method: "PUT",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(data),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "update dataset splits", true);
    }
    /**
     * @deprecated This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead.
     */ async evaluateRun(run, evaluator, { sourceInfo, loadChildRuns, referenceExample } = {
        loadChildRuns: false
    }) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$warn$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["warnOnce"])("This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead.");
        let run_;
        if (typeof run === "string") {
            run_ = await this.readRun(run, {
                loadChildRuns
            });
        } else if (typeof run === "object" && "id" in run) {
            run_ = run;
        } else {
            throw new Error(`Invalid run type: ${typeof run}`);
        }
        if (run_.reference_example_id !== null && run_.reference_example_id !== undefined) {
            referenceExample = await this.readExample(run_.reference_example_id);
        }
        const feedbackResult = await evaluator.evaluateRun(run_, referenceExample);
        const [_, feedbacks] = await this._logEvaluationFeedback(feedbackResult, run_, sourceInfo);
        return feedbacks[0];
    }
    async createFeedback(runId, key, { score, value, correction, comment, sourceInfo, feedbackSourceType = "api", sourceRunId, feedbackId, feedbackConfig, projectId, comparativeExperimentId }) {
        if (!runId && !projectId) {
            throw new Error("One of runId or projectId must be provided");
        }
        if (runId && projectId) {
            throw new Error("Only one of runId or projectId can be provided");
        }
        const feedback_source = {
            type: feedbackSourceType ?? "api",
            metadata: sourceInfo ?? {}
        };
        if (sourceRunId !== undefined && feedback_source?.metadata !== undefined && !feedback_source.metadata["__run"]) {
            feedback_source.metadata["__run"] = {
                run_id: sourceRunId
            };
        }
        if (feedback_source?.metadata !== undefined && feedback_source.metadata["__run"]?.run_id !== undefined) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(feedback_source.metadata["__run"].run_id);
        }
        const feedback = {
            id: feedbackId ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"](),
            run_id: runId,
            key,
            score: _formatFeedbackScore(score),
            value,
            correction,
            comment,
            feedback_source: feedback_source,
            comparative_experiment_id: comparativeExperimentId,
            feedbackConfig,
            session_id: projectId
        };
        const url = `${this.apiUrl}/feedback`;
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), url, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(feedback),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "create feedback", true);
        return feedback;
    }
    async updateFeedback(feedbackId, { score, value, correction, comment }) {
        const feedbackUpdate = {};
        if (score !== undefined && score !== null) {
            feedbackUpdate["score"] = _formatFeedbackScore(score);
        }
        if (value !== undefined && value !== null) {
            feedbackUpdate["value"] = value;
        }
        if (correction !== undefined && correction !== null) {
            feedbackUpdate["correction"] = correction;
        }
        if (comment !== undefined && comment !== null) {
            feedbackUpdate["comment"] = comment;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(feedbackId);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/feedback/${feedbackId}`, {
            method: "PATCH",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(feedbackUpdate),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "update feedback", true);
    }
    async readFeedback(feedbackId) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(feedbackId);
        const path = `/feedback/${feedbackId}`;
        const response = await this._get(path);
        return response;
    }
    async deleteFeedback(feedbackId) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(feedbackId);
        const path = `/feedback/${feedbackId}`;
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), this.apiUrl + path, {
            method: "DELETE",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, `delete ${path}`);
        await response.json();
    }
    async *listFeedback({ runIds, feedbackKeys, feedbackSourceTypes } = {}) {
        const queryParams = new URLSearchParams();
        if (runIds) {
            queryParams.append("run", runIds.join(","));
        }
        if (feedbackKeys) {
            for (const key of feedbackKeys){
                queryParams.append("key", key);
            }
        }
        if (feedbackSourceTypes) {
            for (const type of feedbackSourceTypes){
                queryParams.append("source", type);
            }
        }
        for await (const feedbacks of this._getPaginated("/feedback", queryParams)){
            yield* feedbacks;
        }
    }
    /**
     * Creates a presigned feedback token and URL.
     *
     * The token can be used to authorize feedback metrics without
     * needing an API key. This is useful for giving browser-based
     * applications the ability to submit feedback without needing
     * to expose an API key.
     *
     * @param runId The ID of the run.
     * @param feedbackKey The feedback key.
     * @param options Additional options for the token.
     * @param options.expiration The expiration time for the token.
     *
     * @returns A promise that resolves to a FeedbackIngestToken.
     */ async createPresignedFeedbackToken(runId, feedbackKey, { expiration, feedbackConfig } = {}) {
        const body = {
            run_id: runId,
            feedback_key: feedbackKey,
            feedback_config: feedbackConfig
        };
        if (expiration) {
            if (typeof expiration === "string") {
                body["expires_at"] = expiration;
            } else if (expiration?.hours || expiration?.minutes || expiration?.days) {
                body["expires_in"] = expiration;
            }
        } else {
            body["expires_in"] = {
                hours: 3
            };
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/feedback/tokens`, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(body),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        const result = await response.json();
        return result;
    }
    async createComparativeExperiment({ name, experimentIds, referenceDatasetId, createdAt, description, metadata, id }) {
        if (experimentIds.length === 0) {
            throw new Error("At least one experiment is required");
        }
        if (!referenceDatasetId) {
            referenceDatasetId = (await this.readProject({
                projectId: experimentIds[0]
            })).reference_dataset_id;
        }
        if (!referenceDatasetId == null) {
            throw new Error("A reference dataset is required");
        }
        const body = {
            id,
            name,
            experiment_ids: experimentIds,
            reference_dataset_id: referenceDatasetId,
            description,
            created_at: (createdAt ?? new Date())?.toISOString(),
            extra: {}
        };
        if (metadata) body.extra["metadata"] = metadata;
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/datasets/comparative`, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(body),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        return await response.json();
    }
    /**
     * Retrieves a list of presigned feedback tokens for a given run ID.
     * @param runId The ID of the run.
     * @returns An async iterable of FeedbackIngestToken objects.
     */ async *listPresignedFeedbackTokens(runId) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(runId);
        const params = new URLSearchParams({
            run_id: runId
        });
        for await (const tokens of this._getPaginated("/feedback/tokens", params)){
            yield* tokens;
        }
    }
    _selectEvalResults(results) {
        let results_;
        if ("results" in results) {
            results_ = results.results;
        } else if (Array.isArray(results)) {
            results_ = results;
        } else {
            results_ = [
                results
            ];
        }
        return results_;
    }
    async _logEvaluationFeedback(evaluatorResponse, run, sourceInfo) {
        const evalResults = this._selectEvalResults(evaluatorResponse);
        const feedbacks = [];
        for (const res of evalResults){
            let sourceInfo_ = sourceInfo || {};
            if (res.evaluatorInfo) {
                sourceInfo_ = {
                    ...res.evaluatorInfo,
                    ...sourceInfo_
                };
            }
            let runId_ = null;
            if (res.targetRunId) {
                runId_ = res.targetRunId;
            } else if (run) {
                runId_ = run.id;
            }
            feedbacks.push(await this.createFeedback(runId_, res.key, {
                score: res.score,
                value: res.value,
                comment: res.comment,
                correction: res.correction,
                sourceInfo: sourceInfo_,
                sourceRunId: res.sourceRunId,
                feedbackConfig: res.feedbackConfig,
                feedbackSourceType: "model"
            }));
        }
        return [
            evalResults,
            feedbacks
        ];
    }
    async logEvaluationFeedback(evaluatorResponse, run, sourceInfo) {
        const [results] = await this._logEvaluationFeedback(evaluatorResponse, run, sourceInfo);
        return results;
    }
    /**
     * API for managing annotation queues
     */ /**
     * List the annotation queues on the LangSmith API.
     * @param options - The options for listing annotation queues
     * @param options.queueIds - The IDs of the queues to filter by
     * @param options.name - The name of the queue to filter by
     * @param options.nameContains - The substring that the queue name should contain
     * @param options.limit - The maximum number of queues to return
     * @returns An iterator of AnnotationQueue objects
     */ async *listAnnotationQueues(options = {}) {
        const { queueIds, name, nameContains, limit } = options;
        const params = new URLSearchParams();
        if (queueIds) {
            queueIds.forEach((id, i)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(id, `queueIds[${i}]`);
                params.append("ids", id);
            });
        }
        if (name) params.append("name", name);
        if (nameContains) params.append("name_contains", nameContains);
        params.append("limit", (limit !== undefined ? Math.min(limit, 100) : 100).toString());
        let count = 0;
        for await (const queues of this._getPaginated("/annotation-queues", params)){
            yield* queues;
            count++;
            if (limit !== undefined && count >= limit) break;
        }
    }
    /**
     * Create an annotation queue on the LangSmith API.
     * @param options - The options for creating an annotation queue
     * @param options.name - The name of the annotation queue
     * @param options.description - The description of the annotation queue
     * @param options.queueId - The ID of the annotation queue
     * @returns The created AnnotationQueue object
     */ async createAnnotationQueue(options) {
        const { name, description, queueId, rubricInstructions } = options;
        const body = {
            name,
            description,
            id: queueId || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"](),
            rubric_instructions: rubricInstructions
        };
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/annotation-queues`, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(Object.fromEntries(Object.entries(body).filter(([_, v])=>v !== undefined))),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "create annotation queue");
        const data = await response.json();
        return data;
    }
    /**
     * Read an annotation queue with the specified queue ID.
     * @param queueId - The ID of the annotation queue to read
     * @returns The AnnotationQueueWithDetails object
     */ async readAnnotationQueue(queueId) {
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/annotation-queues/${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(queueId, "queueId")}`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "read annotation queue");
        const data = await response.json();
        return data;
    }
    /**
     * Update an annotation queue with the specified queue ID.
     * @param queueId - The ID of the annotation queue to update
     * @param options - The options for updating the annotation queue
     * @param options.name - The new name for the annotation queue
     * @param options.description - The new description for the annotation queue
     */ async updateAnnotationQueue(queueId, options) {
        const { name, description, rubricInstructions } = options;
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/annotation-queues/${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(queueId, "queueId")}`, {
            method: "PATCH",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                name,
                description,
                rubric_instructions: rubricInstructions
            }),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "update annotation queue");
    }
    /**
     * Delete an annotation queue with the specified queue ID.
     * @param queueId - The ID of the annotation queue to delete
     */ async deleteAnnotationQueue(queueId) {
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/annotation-queues/${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(queueId, "queueId")}`, {
            method: "DELETE",
            headers: {
                ...this.headers,
                Accept: "application/json"
            },
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "delete annotation queue");
    }
    /**
     * Add runs to an annotation queue with the specified queue ID.
     * @param queueId - The ID of the annotation queue
     * @param runIds - The IDs of the runs to be added to the annotation queue
     */ async addRunsToAnnotationQueue(queueId, runIds) {
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/annotation-queues/${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(queueId, "queueId")}/runs`, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(runIds.map((id, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(id, `runIds[${i}]`).toString())),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "add runs to annotation queue");
    }
    /**
     * Get a run from an annotation queue at the specified index.
     * @param queueId - The ID of the annotation queue
     * @param index - The index of the run to retrieve
     * @returns A Promise that resolves to a RunWithAnnotationQueueInfo object
     * @throws {Error} If the run is not found at the given index or for other API-related errors
     */ async getRunFromAnnotationQueue(queueId, index) {
        const baseUrl = `/annotation-queues/${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(queueId, "queueId")}/run`;
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}${baseUrl}/${index}`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "get run from annotation queue");
        return await response.json();
    }
    /**
     * Delete a run from an an annotation queue.
     * @param queueId - The ID of the annotation queue to delete the run from
     * @param queueRunId - The ID of the run to delete from the annotation queue
     */ async deleteRunFromAnnotationQueue(queueId, queueRunId) {
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/annotation-queues/${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(queueId, "queueId")}/runs/${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(queueRunId, "queueRunId")}`, {
            method: "DELETE",
            headers: {
                ...this.headers,
                Accept: "application/json"
            },
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "delete run from annotation queue");
    }
    /**
     * Get the size of an annotation queue.
     * @param queueId - The ID of the annotation queue
     */ async getSizeFromAnnotationQueue(queueId) {
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/annotation-queues/${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(queueId, "queueId")}/size`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "get size from annotation queue");
        return await response.json();
    }
    async _currentTenantIsOwner(owner) {
        const settings = await this._getSettings();
        return owner == "-" || settings.tenant_handle === owner;
    }
    async _ownerConflictError(action, owner) {
        const settings = await this._getSettings();
        return new Error(`Cannot ${action} for another tenant.\n
      Current tenant: ${settings.tenant_handle}\n
      Requested tenant: ${owner}`);
    }
    async _getLatestCommitHash(promptOwnerAndName) {
        const res = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/commits/${promptOwnerAndName}/?limit=${1}&offset=${0}`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        const json = await res.json();
        if (!res.ok) {
            const detail = typeof json.detail === "string" ? json.detail : JSON.stringify(json.detail);
            const error = new Error(`Error ${res.status}: ${res.statusText}\n${detail}`);
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            error.statusCode = res.status;
            throw error;
        }
        if (json.commits.length === 0) {
            return undefined;
        }
        return json.commits[0].commit_hash;
    }
    async _likeOrUnlikePrompt(promptIdentifier, like) {
        const [owner, promptName, _] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parsePromptIdentifier"])(promptIdentifier);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/likes/${owner}/${promptName}`, {
            method: "POST",
            body: JSON.stringify({
                like: like
            }),
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, `${like ? "like" : "unlike"} prompt`);
        return await response.json();
    }
    async _getPromptUrl(promptIdentifier) {
        const [owner, promptName, commitHash] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parsePromptIdentifier"])(promptIdentifier);
        if (!await this._currentTenantIsOwner(owner)) {
            if (commitHash !== "latest") {
                return `${this.getHostUrl()}/hub/${owner}/${promptName}/${commitHash.substring(0, 8)}`;
            } else {
                return `${this.getHostUrl()}/hub/${owner}/${promptName}`;
            }
        } else {
            const settings = await this._getSettings();
            if (commitHash !== "latest") {
                return `${this.getHostUrl()}/prompts/${promptName}/${commitHash.substring(0, 8)}?organizationId=${settings.id}`;
            } else {
                return `${this.getHostUrl()}/prompts/${promptName}?organizationId=${settings.id}`;
            }
        }
    }
    async promptExists(promptIdentifier) {
        const prompt = await this.getPrompt(promptIdentifier);
        return !!prompt;
    }
    async likePrompt(promptIdentifier) {
        return this._likeOrUnlikePrompt(promptIdentifier, true);
    }
    async unlikePrompt(promptIdentifier) {
        return this._likeOrUnlikePrompt(promptIdentifier, false);
    }
    async *listCommits(promptOwnerAndName) {
        for await (const commits of this._getPaginated(`/commits/${promptOwnerAndName}/`, new URLSearchParams(), (res)=>res.commits)){
            yield* commits;
        }
    }
    async *listPrompts(options) {
        const params = new URLSearchParams();
        params.append("sort_field", options?.sortField ?? "updated_at");
        params.append("sort_direction", "desc");
        params.append("is_archived", (!!options?.isArchived).toString());
        if (options?.isPublic !== undefined) {
            params.append("is_public", options.isPublic.toString());
        }
        if (options?.query) {
            params.append("query", options.query);
        }
        for await (const prompts of this._getPaginated("/repos", params, (res)=>res.repos)){
            yield* prompts;
        }
    }
    async getPrompt(promptIdentifier) {
        const [owner, promptName, _] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parsePromptIdentifier"])(promptIdentifier);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/repos/${owner}/${promptName}`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        if (response.status === 404) {
            return null;
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "get prompt");
        const result = await response.json();
        if (result.repo) {
            return result.repo;
        } else {
            return null;
        }
    }
    async createPrompt(promptIdentifier, options) {
        const settings = await this._getSettings();
        if (options?.isPublic && !settings.tenant_handle) {
            throw new Error(`Cannot create a public prompt without first\n
        creating a LangChain Hub handle.
        You can add a handle by creating a public prompt at:\n
        https://smith.langchain.com/prompts`);
        }
        const [owner, promptName, _] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parsePromptIdentifier"])(promptIdentifier);
        if (!await this._currentTenantIsOwner(owner)) {
            throw await this._ownerConflictError("create a prompt", owner);
        }
        const data = {
            repo_handle: promptName,
            ...options?.description && {
                description: options.description
            },
            ...options?.readme && {
                readme: options.readme
            },
            ...options?.tags && {
                tags: options.tags
            },
            is_public: !!options?.isPublic
        };
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/repos/`, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(data),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "create prompt");
        const { repo } = await response.json();
        return repo;
    }
    async createCommit(promptIdentifier, object, options) {
        if (!await this.promptExists(promptIdentifier)) {
            throw new Error("Prompt does not exist, you must create it first.");
        }
        const [owner, promptName, _] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parsePromptIdentifier"])(promptIdentifier);
        const resolvedParentCommitHash = options?.parentCommitHash === "latest" || !options?.parentCommitHash ? await this._getLatestCommitHash(`${owner}/${promptName}`) : options?.parentCommitHash;
        const payload = {
            manifest: JSON.parse(JSON.stringify(object)),
            parent_commit: resolvedParentCommitHash
        };
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/commits/${owner}/${promptName}`, {
            method: "POST",
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            body: JSON.stringify(payload),
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "create commit");
        const result = await response.json();
        return this._getPromptUrl(`${owner}/${promptName}${result.commit_hash ? `:${result.commit_hash}` : ""}`);
    }
    /**
     * Update examples with attachments using multipart form data.
     * @param updates List of ExampleUpdateWithAttachments objects to upsert
     * @returns Promise with the update response
     */ async updateExamplesMultipart(datasetId, updates = []) {
        return this._updateExamplesMultipart(datasetId, updates);
    }
    async _updateExamplesMultipart(datasetId, updates = []) {
        if (!await this._getMultiPartSupport()) {
            throw new Error("Your LangSmith deployment does not allow using the multipart examples endpoint, please upgrade your deployment to the latest version.");
        }
        const formData = new FormData();
        for (const example of updates){
            const exampleId = example.id;
            // Prepare the main example body
            const exampleBody = {
                ...example.metadata && {
                    metadata: example.metadata
                },
                ...example.split && {
                    split: example.split
                }
            };
            // Add main example data
            const stringifiedExample = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(exampleBody, `Serializing body for example with id: ${exampleId}`);
            const exampleBlob = new Blob([
                stringifiedExample
            ], {
                type: "application/json"
            });
            formData.append(exampleId, exampleBlob);
            // Add inputs if present
            if (example.inputs) {
                const stringifiedInputs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(example.inputs, `Serializing inputs for example with id: ${exampleId}`);
                const inputsBlob = new Blob([
                    stringifiedInputs
                ], {
                    type: "application/json"
                });
                formData.append(`${exampleId}.inputs`, inputsBlob);
            }
            // Add outputs if present
            if (example.outputs) {
                const stringifiedOutputs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(example.outputs, `Serializing outputs whle updating example with id: ${exampleId}`);
                const outputsBlob = new Blob([
                    stringifiedOutputs
                ], {
                    type: "application/json"
                });
                formData.append(`${exampleId}.outputs`, outputsBlob);
            }
            // Add attachments if present
            if (example.attachments) {
                for (const [name, attachment] of Object.entries(example.attachments)){
                    let mimeType;
                    let data;
                    if (Array.isArray(attachment)) {
                        [mimeType, data] = attachment;
                    } else {
                        mimeType = attachment.mimeType;
                        data = attachment.data;
                    }
                    const attachmentBlob = new Blob([
                        data
                    ], {
                        type: `${mimeType}; length=${data.byteLength}`
                    });
                    formData.append(`${exampleId}.attachment.${name}`, attachmentBlob);
                }
            }
            if (example.attachments_operations) {
                const stringifiedAttachmentsOperations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(example.attachments_operations, `Serializing attachments while updating example with id: ${exampleId}`);
                const attachmentsOperationsBlob = new Blob([
                    stringifiedAttachmentsOperations
                ], {
                    type: "application/json"
                });
                formData.append(`${exampleId}.attachments_operations`, attachmentsOperationsBlob);
            }
        }
        const datasetIdToUse = datasetId ?? updates[0]?.dataset_id;
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}${this._getPlatformEndpointPath(`datasets/${datasetIdToUse}/examples`)}`, {
            method: "PATCH",
            headers: this.headers,
            body: formData
        });
        const result = await response.json();
        return result;
    }
    /**
     * Upload examples with attachments using multipart form data.
     * @param uploads List of ExampleUploadWithAttachments objects to upload
     * @returns Promise with the upload response
     * @deprecated This method is deprecated and will be removed in future LangSmith versions, please use `createExamples` instead
     */ async uploadExamplesMultipart(datasetId, uploads = []) {
        return this._uploadExamplesMultipart(datasetId, uploads);
    }
    async _uploadExamplesMultipart(datasetId, uploads = []) {
        if (!await this._getMultiPartSupport()) {
            throw new Error("Your LangSmith deployment does not allow using the multipart examples endpoint, please upgrade your deployment to the latest version.");
        }
        const formData = new FormData();
        for (const example of uploads){
            const exampleId = (example.id ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"]()).toString();
            // Prepare the main example body
            const exampleBody = {
                created_at: example.created_at,
                ...example.metadata && {
                    metadata: example.metadata
                },
                ...example.split && {
                    split: example.split
                },
                ...example.source_run_id && {
                    source_run_id: example.source_run_id
                },
                ...example.use_source_run_io && {
                    use_source_run_io: example.use_source_run_io
                },
                ...example.use_source_run_attachments && {
                    use_source_run_attachments: example.use_source_run_attachments
                }
            };
            // Add main example data
            const stringifiedExample = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(exampleBody, `Serializing body for uploaded example with id: ${exampleId}`);
            const exampleBlob = new Blob([
                stringifiedExample
            ], {
                type: "application/json"
            });
            formData.append(exampleId, exampleBlob);
            // Add inputs if present
            if (example.inputs) {
                const stringifiedInputs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(example.inputs, `Serializing inputs for uploaded example with id: ${exampleId}`);
                const inputsBlob = new Blob([
                    stringifiedInputs
                ], {
                    type: "application/json"
                });
                formData.append(`${exampleId}.inputs`, inputsBlob);
            }
            // Add outputs if present
            if (example.outputs) {
                const stringifiedOutputs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$fast$2d$safe$2d$stringify$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serialize"])(example.outputs, `Serializing outputs for uploaded example with id: ${exampleId}`);
                const outputsBlob = new Blob([
                    stringifiedOutputs
                ], {
                    type: "application/json"
                });
                formData.append(`${exampleId}.outputs`, outputsBlob);
            }
            // Add attachments if present
            if (example.attachments) {
                for (const [name, attachment] of Object.entries(example.attachments)){
                    let mimeType;
                    let data;
                    if (Array.isArray(attachment)) {
                        [mimeType, data] = attachment;
                    } else {
                        mimeType = attachment.mimeType;
                        data = attachment.data;
                    }
                    const attachmentBlob = new Blob([
                        data
                    ], {
                        type: `${mimeType}; length=${data.byteLength}`
                    });
                    formData.append(`${exampleId}.attachment.${name}`, attachmentBlob);
                }
            }
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}${this._getPlatformEndpointPath(`datasets/${datasetId}/examples`)}`, {
            method: "POST",
            headers: this.headers,
            body: formData
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "upload examples");
        const result = await response.json();
        return result;
    }
    async updatePrompt(promptIdentifier, options) {
        if (!await this.promptExists(promptIdentifier)) {
            throw new Error("Prompt does not exist, you must create it first.");
        }
        const [owner, promptName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parsePromptIdentifier"])(promptIdentifier);
        if (!await this._currentTenantIsOwner(owner)) {
            throw await this._ownerConflictError("update a prompt", owner);
        }
        const payload = {};
        if (options?.description !== undefined) payload.description = options.description;
        if (options?.readme !== undefined) payload.readme = options.readme;
        if (options?.tags !== undefined) payload.tags = options.tags;
        if (options?.isPublic !== undefined) payload.is_public = options.isPublic;
        if (options?.isArchived !== undefined) payload.is_archived = options.isArchived;
        // Check if payload is empty
        if (Object.keys(payload).length === 0) {
            throw new Error("No valid update options provided");
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/repos/${owner}/${promptName}`, {
            method: "PATCH",
            body: JSON.stringify(payload),
            headers: {
                ...this.headers,
                "Content-Type": "application/json"
            },
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "update prompt");
        return response.json();
    }
    async deletePrompt(promptIdentifier) {
        if (!await this.promptExists(promptIdentifier)) {
            throw new Error("Prompt does not exist, you must create it first.");
        }
        const [owner, promptName, _] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parsePromptIdentifier"])(promptIdentifier);
        if (!await this._currentTenantIsOwner(owner)) {
            throw await this._ownerConflictError("delete a prompt", owner);
        }
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/repos/${owner}/${promptName}`, {
            method: "DELETE",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        return await response.json();
    }
    async pullPromptCommit(promptIdentifier, options) {
        const [owner, promptName, commitHash] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parsePromptIdentifier"])(promptIdentifier);
        const response = await this.caller.call((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$fetch$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_getFetchImplementation"])(this.debug), `${this.apiUrl}/commits/${owner}/${promptName}/${commitHash}${options?.includeModel ? "?include_model=true" : ""}`, {
            method: "GET",
            headers: this.headers,
            signal: AbortSignal.timeout(this.timeout_ms),
            ...this.fetchOptions
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["raiseForStatus"])(response, "pull prompt commit");
        const result = await response.json();
        return {
            owner,
            repo: promptName,
            commit_hash: result.commit_hash,
            manifest: result.manifest,
            examples: result.examples
        };
    }
    /**
     * This method should not be used directly, use `import { pull } from "langchain/hub"` instead.
     * Using this method directly returns the JSON string of the prompt rather than a LangChain object.
     * @private
     */ async _pullPrompt(promptIdentifier, options) {
        const promptObject = await this.pullPromptCommit(promptIdentifier, {
            includeModel: options?.includeModel
        });
        const prompt = JSON.stringify(promptObject.manifest);
        return prompt;
    }
    async pushPrompt(promptIdentifier, options) {
        // Create or update prompt metadata
        if (await this.promptExists(promptIdentifier)) {
            if (options && Object.keys(options).some((key)=>key !== "object")) {
                await this.updatePrompt(promptIdentifier, {
                    description: options?.description,
                    readme: options?.readme,
                    tags: options?.tags,
                    isPublic: options?.isPublic
                });
            }
        } else {
            await this.createPrompt(promptIdentifier, {
                description: options?.description,
                readme: options?.readme,
                tags: options?.tags,
                isPublic: options?.isPublic
            });
        }
        if (!options?.object) {
            return await this._getPromptUrl(promptIdentifier);
        }
        // Create a commit with the new manifest
        const url = await this.createCommit(promptIdentifier, options?.object, {
            parentCommitHash: options?.parentCommitHash
        });
        return url;
    }
    /**
     * Clone a public dataset to your own langsmith tenant.
     * This operation is idempotent. If you already have a dataset with the given name,
     * this function will do nothing.
  
     * @param {string} tokenOrUrl The token of the public dataset to clone.
     * @param {Object} [options] Additional options for cloning the dataset.
     * @param {string} [options.sourceApiUrl] The URL of the langsmith server where the data is hosted. Defaults to the API URL of your current client.
     * @param {string} [options.datasetName] The name of the dataset to create in your tenant. Defaults to the name of the public dataset.
     * @returns {Promise<void>}
     */ async clonePublicDataset(tokenOrUrl, options = {}) {
        const { sourceApiUrl = this.apiUrl, datasetName } = options;
        const [parsedApiUrl, tokenUuid] = this.parseTokenOrUrl(tokenOrUrl, sourceApiUrl);
        const sourceClient = new Client({
            apiUrl: parsedApiUrl,
            // Placeholder API key not needed anymore in most cases, but
            // some private deployments may have API key-based rate limiting
            // that would cause this to fail if we provide no value.
            apiKey: "placeholder"
        });
        const ds = await sourceClient.readSharedDataset(tokenUuid);
        const finalDatasetName = datasetName || ds.name;
        try {
            if (await this.hasDataset({
                datasetId: finalDatasetName
            })) {
                console.log(`Dataset ${finalDatasetName} already exists in your tenant. Skipping.`);
                return;
            }
        } catch (_) {
        // `.hasDataset` will throw an error if the dataset does not exist.
        // no-op in that case
        }
        // Fetch examples first, then create the dataset
        const examples = await sourceClient.listSharedExamples(tokenUuid);
        const dataset = await this.createDataset(finalDatasetName, {
            description: ds.description,
            dataType: ds.data_type || "kv",
            inputsSchema: ds.inputs_schema_definition ?? undefined,
            outputsSchema: ds.outputs_schema_definition ?? undefined
        });
        try {
            await this.createExamples({
                inputs: examples.map((e)=>e.inputs),
                outputs: examples.flatMap((e)=>e.outputs ? [
                        e.outputs
                    ] : []),
                datasetId: dataset.id
            });
        } catch (e) {
            console.error(`An error occurred while creating dataset ${finalDatasetName}. ` + "You should delete it manually.");
            throw e;
        }
    }
    parseTokenOrUrl(urlOrToken, apiUrl, numParts = 2, kind = "dataset") {
        // Try parsing as UUID
        try {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$_uuid$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertUuid"])(urlOrToken); // Will throw if it's not a UUID.
            return [
                apiUrl,
                urlOrToken
            ];
        } catch (_) {
        // no-op if it's not a uuid
        }
        // Parse as URL
        try {
            const parsedUrl = new URL(urlOrToken);
            const pathParts = parsedUrl.pathname.split("/").filter((part)=>part !== "");
            if (pathParts.length >= numParts) {
                const tokenUuid = pathParts[pathParts.length - numParts];
                return [
                    apiUrl,
                    tokenUuid
                ];
            } else {
                throw new Error(`Invalid public ${kind} URL: ${urlOrToken}`);
            }
        } catch (error) {
            throw new Error(`Invalid public ${kind} URL or token: ${urlOrToken}`);
        }
    }
    /**
     * Awaits all pending trace batches. Useful for environments where
     * you need to be sure that all tracing requests finish before execution ends,
     * such as serverless environments.
     *
     * @example
     * ```
     * import { Client } from "langsmith";
     *
     * const client = new Client();
     *
     * try {
     *   // Tracing happens here
     *   ...
     * } finally {
     *   await client.awaitPendingTraceBatches();
     * }
     * ```
     *
     * @returns A promise that resolves once all currently pending traces have sent.
     */ async awaitPendingTraceBatches() {
        if (this.manualFlushMode) {
            console.warn("[WARNING]: When tracing in manual flush mode, you must call `await client.flush()` manually to submit trace batches.");
            return Promise.resolve();
        }
        await Promise.all([
            ...this.autoBatchQueue.items.map(({ itemPromise })=>itemPromise),
            this.batchIngestCaller.queue.onIdle()
        ]);
        if (this.langSmithToOTELTranslator !== undefined) {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$otel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDefaultOTLPTracerComponents"])()?.DEFAULT_LANGSMITH_SPAN_PROCESSOR?.forceFlush();
        }
    }
}
function isExampleCreate(input) {
    return "dataset_id" in input || "dataset_name" in input;
}
}),
"[project]/node_modules/langsmith/dist/env.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "isTracingEnabled": ()=>isTracingEnabled
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/env.js [app-route] (ecmascript)");
;
const isTracingEnabled = (tracingEnabled)=>{
    if (tracingEnabled !== undefined) {
        return tracingEnabled;
    }
    const envVars = [
        "TRACING_V2",
        "TRACING"
    ];
    return !!envVars.find((envVar)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])(envVar) === "true");
};
}),
"[project]/node_modules/langsmith/dist/singletons/constants.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "_LC_CONTEXT_VARIABLES_KEY": ()=>_LC_CONTEXT_VARIABLES_KEY
});
const _LC_CONTEXT_VARIABLES_KEY = Symbol.for("lc:context_variables");
}),
"[project]/node_modules/langsmith/dist/run_trees.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "RunTree": ()=>RunTree,
    "convertToDottedOrderFormat": ()=>convertToDottedOrderFormat,
    "isRunTree": ()=>isRunTree,
    "isRunnableConfigLike": ()=>isRunnableConfigLike
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-node/v4.js [app-route] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v5$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v5$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-node/v5.js [app-route] (ecmascript) <export default as v5>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$client$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/client.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/error.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/singletons/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$project$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/project.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$warn$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/utils/warn.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
function stripNonAlphanumeric(input) {
    return input.replace(/[-:.]/g, "");
}
function convertToDottedOrderFormat(epoch, runId, executionOrder = 1) {
    // Date only has millisecond precision, so we use the microseconds to break
    // possible ties, avoiding incorrect run order
    const paddedOrder = executionOrder.toFixed(0).slice(0, 3).padStart(3, "0");
    const microsecondPrecisionDatestring = `${new Date(epoch).toISOString().slice(0, -1)}${paddedOrder}Z`;
    return {
        dottedOrder: stripNonAlphanumeric(microsecondPrecisionDatestring) + runId,
        microsecondPrecisionDatestring
    };
}
/**
 * Baggage header information
 */ class Baggage {
    constructor(metadata, tags, project_name, replicas){
        Object.defineProperty(this, "metadata", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "tags", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "project_name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "replicas", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.metadata = metadata;
        this.tags = tags;
        this.project_name = project_name;
        this.replicas = replicas;
    }
    static fromHeader(value) {
        const items = value.split(",");
        let metadata = {};
        let tags = [];
        let project_name;
        let replicas;
        for (const item of items){
            const [key, uriValue] = item.split("=");
            const value = decodeURIComponent(uriValue);
            if (key === "langsmith-metadata") {
                metadata = JSON.parse(value);
            } else if (key === "langsmith-tags") {
                tags = value.split(",");
            } else if (key === "langsmith-project") {
                project_name = value;
            } else if (key === "langsmith-replicas") {
                replicas = JSON.parse(value);
            }
        }
        return new Baggage(metadata, tags, project_name, replicas);
    }
    toHeader() {
        const items = [];
        if (this.metadata && Object.keys(this.metadata).length > 0) {
            items.push(`langsmith-metadata=${encodeURIComponent(JSON.stringify(this.metadata))}`);
        }
        if (this.tags && this.tags.length > 0) {
            items.push(`langsmith-tags=${encodeURIComponent(this.tags.join(","))}`);
        }
        if (this.project_name) {
            items.push(`langsmith-project=${encodeURIComponent(this.project_name)}`);
        }
        return items.join(",");
    }
}
class RunTree {
    constructor(originalConfig){
        Object.defineProperty(this, "id", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "run_type", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "project_name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "parent_run", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "parent_run_id", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "child_runs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "start_time", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "end_time", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "extra", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "tags", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "error", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "serialized", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "inputs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "outputs", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "reference_example_id", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "client", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "events", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "trace_id", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "dotted_order", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "tracingEnabled", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "execution_order", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "child_execution_order", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * Attachments associated with the run.
         * Each entry is a tuple of [mime_type, bytes]
         */ Object.defineProperty(this, "attachments", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * Projects to replicate this run to with optional updates.
         */ Object.defineProperty(this, "replicas", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_serialized_start_time", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        // If you pass in a run tree directly, return a shallow clone
        if (isRunTree(originalConfig)) {
            Object.assign(this, {
                ...originalConfig
            });
            return;
        }
        const defaultConfig = RunTree.getDefaultConfig();
        const { metadata, ...config } = originalConfig;
        const client = config.client ?? RunTree.getSharedClient();
        const dedupedMetadata = {
            ...metadata,
            ...config?.extra?.metadata
        };
        config.extra = {
            ...config.extra,
            metadata: dedupedMetadata
        };
        Object.assign(this, {
            ...defaultConfig,
            ...config,
            client
        });
        if (!this.trace_id) {
            if (this.parent_run) {
                this.trace_id = this.parent_run.trace_id ?? this.id;
            } else {
                this.trace_id = this.id;
            }
        }
        this.replicas = _ensureWriteReplicas(this.replicas);
        this.execution_order ??= 1;
        this.child_execution_order ??= 1;
        if (!this.dotted_order) {
            const { dottedOrder, microsecondPrecisionDatestring } = convertToDottedOrderFormat(this.start_time, this.id, this.execution_order);
            if (this.parent_run) {
                this.dotted_order = this.parent_run.dotted_order + "." + dottedOrder;
            } else {
                this.dotted_order = dottedOrder;
            }
            this._serialized_start_time = microsecondPrecisionDatestring;
        }
    }
    set metadata(metadata) {
        this.extra = {
            ...this.extra,
            metadata: {
                ...this.extra?.metadata,
                ...metadata
            }
        };
    }
    get metadata() {
        return this.extra?.metadata;
    }
    static getDefaultConfig() {
        return {
            id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"](),
            run_type: "chain",
            project_name: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$project$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDefaultProjectName"])(),
            child_runs: [],
            api_url: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("LANGCHAIN_ENDPOINT") ?? "http://localhost:1984",
            api_key: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("LANGCHAIN_API_KEY"),
            caller_options: {},
            start_time: Date.now(),
            serialized: {},
            inputs: {},
            extra: {}
        };
    }
    static getSharedClient() {
        if (!RunTree.sharedClient) {
            RunTree.sharedClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$client$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Client"]();
        }
        return RunTree.sharedClient;
    }
    createChild(config) {
        const child_execution_order = this.child_execution_order + 1;
        const child = new RunTree({
            ...config,
            parent_run: this,
            project_name: this.project_name,
            replicas: this.replicas,
            client: this.client,
            tracingEnabled: this.tracingEnabled,
            execution_order: child_execution_order,
            child_execution_order: child_execution_order
        });
        // Copy context vars over into the new run tree.
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_LC_CONTEXT_VARIABLES_KEY"] in this) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            child[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_LC_CONTEXT_VARIABLES_KEY"]] = this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$singletons$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["_LC_CONTEXT_VARIABLES_KEY"]];
        }
        const LC_CHILD = Symbol.for("lc:child_config");
        const presentConfig = config.extra?.[LC_CHILD] ?? this.extra[LC_CHILD];
        // tracing for LangChain is defined by the _parentRunId and runMap of the tracer
        if (isRunnableConfigLike(presentConfig)) {
            const newConfig = {
                ...presentConfig
            };
            const callbacks = isCallbackManagerLike(newConfig.callbacks) ? newConfig.callbacks.copy?.() : undefined;
            if (callbacks) {
                // update the parent run id
                Object.assign(callbacks, {
                    _parentRunId: child.id
                });
                // only populate if we're in a newer LC.JS version
                callbacks.handlers?.find(isLangChainTracerLike)?.updateFromRunTree?.(child);
                newConfig.callbacks = callbacks;
            }
            child.extra[LC_CHILD] = newConfig;
        }
        // propagate child_execution_order upwards
        const visited = new Set();
        let current = this;
        while(current != null && !visited.has(current.id)){
            visited.add(current.id);
            current.child_execution_order = Math.max(current.child_execution_order, child_execution_order);
            current = current.parent_run;
        }
        this.child_runs.push(child);
        return child;
    }
    async end(outputs, error, endTime = Date.now(), metadata) {
        this.outputs = this.outputs ?? outputs;
        this.error = this.error ?? error;
        this.end_time = this.end_time ?? endTime;
        if (metadata && Object.keys(metadata).length > 0) {
            this.extra = this.extra ? {
                ...this.extra,
                metadata: {
                    ...this.extra.metadata,
                    ...metadata
                }
            } : {
                metadata
            };
        }
    }
    _convertToCreate(run, runtimeEnv, excludeChildRuns = true) {
        const runExtra = run.extra ?? {};
        // Avoid overwriting the runtime environment if it's already set
        if (runExtra?.runtime?.library === undefined) {
            if (!runExtra.runtime) {
                runExtra.runtime = {};
            }
            if (runtimeEnv) {
                for (const [k, v] of Object.entries(runtimeEnv)){
                    if (!runExtra.runtime[k]) {
                        runExtra.runtime[k] = v;
                    }
                }
            }
        }
        let child_runs;
        let parent_run_id;
        if (!excludeChildRuns) {
            child_runs = run.child_runs.map((child_run)=>this._convertToCreate(child_run, runtimeEnv, excludeChildRuns));
            parent_run_id = undefined;
        } else {
            parent_run_id = run.parent_run?.id ?? run.parent_run_id;
            child_runs = [];
        }
        return {
            id: run.id,
            name: run.name,
            start_time: run._serialized_start_time ?? run.start_time,
            end_time: run.end_time,
            run_type: run.run_type,
            reference_example_id: run.reference_example_id,
            extra: runExtra,
            serialized: run.serialized,
            error: run.error,
            inputs: run.inputs,
            outputs: run.outputs,
            session_name: run.project_name,
            child_runs: child_runs,
            parent_run_id: parent_run_id,
            trace_id: run.trace_id,
            dotted_order: run.dotted_order,
            tags: run.tags,
            attachments: run.attachments,
            events: run.events
        };
    }
    _remapForProject(projectName, runtimeEnv, excludeChildRuns = true) {
        const baseRun = this._convertToCreate(this, runtimeEnv, excludeChildRuns);
        if (projectName === this.project_name) {
            return baseRun;
        }
        // Create a deterministic UUID mapping for this project
        const createRemappedId = (originalId)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v5$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v5$3e$__["v5"](`${originalId}:${projectName}`, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v5$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v5$3e$__["v5"].DNS);
        };
        // Remap the current run's ID
        const newId = createRemappedId(baseRun.id);
        const newTraceId = baseRun.trace_id ? createRemappedId(baseRun.trace_id) : undefined;
        const newParentRunId = baseRun.parent_run_id ? createRemappedId(baseRun.parent_run_id) : undefined;
        let newDottedOrder;
        if (baseRun.dotted_order) {
            const segments = _parseDottedOrder(baseRun.dotted_order);
            const rebuilt = [];
            // Process all segments except the last one
            for(let i = 0; i < segments.length - 1; i++){
                const [timestamp, segmentId] = segments[i];
                const remappedId = createRemappedId(segmentId);
                rebuilt.push(timestamp.toISOString().replace(/[-:]/g, "").replace(".", "") + remappedId);
            }
            // Process the last segment with the new run ID
            const [lastTimestamp] = segments[segments.length - 1];
            rebuilt.push(lastTimestamp.toISOString().replace(/[-:]/g, "").replace(".", "") + newId);
            newDottedOrder = rebuilt.join(".");
        } else {
            newDottedOrder = undefined;
        }
        const remappedRun = {
            ...baseRun,
            id: newId,
            trace_id: newTraceId,
            parent_run_id: newParentRunId,
            dotted_order: newDottedOrder,
            session_name: projectName
        };
        return remappedRun;
    }
    async postRun(excludeChildRuns = true) {
        try {
            const runtimeEnv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getRuntimeEnvironment"])();
            if (this.replicas && this.replicas.length > 0) {
                for (const { projectName, apiKey, apiUrl } of this.replicas){
                    const runCreate = this._remapForProject(projectName ?? this.project_name, runtimeEnv, true);
                    await this.client.createRun(runCreate, {
                        apiKey,
                        apiUrl
                    });
                }
            } else {
                const runCreate = this._convertToCreate(this, runtimeEnv, excludeChildRuns);
                await this.client.createRun(runCreate);
            }
            if (!excludeChildRuns) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$warn$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["warnOnce"])("Posting with excludeChildRuns=false is deprecated and will be removed in a future version.");
                for (const childRun of this.child_runs){
                    await childRun.postRun(false);
                }
            }
        } catch (error) {
            console.error(`Error in postRun for run ${this.id}:`, error);
        }
    }
    async patchRun() {
        if (this.replicas && this.replicas.length > 0) {
            for (const { projectName, apiKey, apiUrl, updates } of this.replicas){
                const runData = this._remapForProject(projectName ?? this.project_name);
                await this.client.updateRun(runData.id, {
                    inputs: runData.inputs,
                    outputs: runData.outputs,
                    error: runData.error,
                    parent_run_id: runData.parent_run_id,
                    session_name: runData.session_name,
                    reference_example_id: runData.reference_example_id,
                    end_time: runData.end_time,
                    dotted_order: runData.dotted_order,
                    trace_id: runData.trace_id,
                    events: runData.events,
                    tags: runData.tags,
                    extra: runData.extra,
                    attachments: this.attachments,
                    ...updates
                }, {
                    apiKey,
                    apiUrl
                });
            }
        } else {
            try {
                const runUpdate = {
                    end_time: this.end_time,
                    error: this.error,
                    inputs: this.inputs,
                    outputs: this.outputs,
                    parent_run_id: this.parent_run?.id ?? this.parent_run_id,
                    reference_example_id: this.reference_example_id,
                    extra: this.extra,
                    events: this.events,
                    dotted_order: this.dotted_order,
                    trace_id: this.trace_id,
                    tags: this.tags,
                    attachments: this.attachments,
                    session_name: this.project_name
                };
                await this.client.updateRun(this.id, runUpdate);
            } catch (error) {
                console.error(`Error in patchRun for run ${this.id}`, error);
            }
        }
    }
    toJSON() {
        return this._convertToCreate(this, undefined, false);
    }
    /**
     * Add an event to the run tree.
     * @param event - A single event or string to add
     */ addEvent(event) {
        if (!this.events) {
            this.events = [];
        }
        if (typeof event === "string") {
            this.events.push({
                name: "event",
                time: new Date().toISOString(),
                message: event
            });
        } else {
            this.events.push({
                ...event,
                time: event.time ?? new Date().toISOString()
            });
        }
    }
    static fromRunnableConfig(parentConfig, props) {
        // We only handle the callback manager case for now
        const callbackManager = parentConfig?.callbacks;
        let parentRun;
        let projectName;
        let client;
        let tracingEnabled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isTracingEnabled"])();
        if (callbackManager) {
            const parentRunId = callbackManager?.getParentRunId?.() ?? "";
            const langChainTracer = callbackManager?.handlers?.find((handler)=>handler?.name == "langchain_tracer");
            parentRun = langChainTracer?.getRun?.(parentRunId);
            projectName = langChainTracer?.projectName;
            client = langChainTracer?.client;
            tracingEnabled = tracingEnabled || !!langChainTracer;
        }
        if (!parentRun) {
            return new RunTree({
                ...props,
                client,
                tracingEnabled,
                project_name: projectName
            });
        }
        const parentRunTree = new RunTree({
            name: parentRun.name,
            id: parentRun.id,
            trace_id: parentRun.trace_id,
            dotted_order: parentRun.dotted_order,
            client,
            tracingEnabled,
            project_name: projectName,
            tags: [
                ...new Set((parentRun?.tags ?? []).concat(parentConfig?.tags ?? []))
            ],
            extra: {
                metadata: {
                    ...parentRun?.extra?.metadata,
                    ...parentConfig?.metadata
                }
            }
        });
        return parentRunTree.createChild(props);
    }
    static fromDottedOrder(dottedOrder) {
        return this.fromHeaders({
            "langsmith-trace": dottedOrder
        });
    }
    static fromHeaders(headers, inheritArgs) {
        const rawHeaders = "get" in headers && typeof headers.get === "function" ? {
            "langsmith-trace": headers.get("langsmith-trace"),
            baggage: headers.get("baggage")
        } : headers;
        const headerTrace = rawHeaders["langsmith-trace"];
        if (!headerTrace || typeof headerTrace !== "string") return undefined;
        const parentDottedOrder = headerTrace.trim();
        const parsedDottedOrder = parentDottedOrder.split(".").map((part)=>{
            const [strTime, uuid] = part.split("Z");
            return {
                strTime,
                time: Date.parse(strTime + "Z"),
                uuid
            };
        });
        const traceId = parsedDottedOrder[0].uuid;
        const config = {
            ...inheritArgs,
            name: inheritArgs?.["name"] ?? "parent",
            run_type: inheritArgs?.["run_type"] ?? "chain",
            start_time: inheritArgs?.["start_time"] ?? Date.now(),
            id: parsedDottedOrder.at(-1)?.uuid,
            trace_id: traceId,
            dotted_order: parentDottedOrder
        };
        if (rawHeaders["baggage"] && typeof rawHeaders["baggage"] === "string") {
            const baggage = Baggage.fromHeader(rawHeaders["baggage"]);
            config.metadata = baggage.metadata;
            config.tags = baggage.tags;
            config.project_name = baggage.project_name;
            config.replicas = baggage.replicas;
        }
        return new RunTree(config);
    }
    toHeaders(headers) {
        const result = {
            "langsmith-trace": this.dotted_order,
            baggage: new Baggage(this.extra?.metadata, this.tags, this.project_name, this.replicas).toHeader()
        };
        if (headers) {
            for (const [key, value] of Object.entries(result)){
                headers.set(key, value);
            }
        }
        return result;
    }
}
Object.defineProperty(RunTree, "sharedClient", {
    enumerable: true,
    configurable: true,
    writable: true,
    value: null
});
function isRunTree(x) {
    return x !== undefined && typeof x.createChild === "function" && typeof x.postRun === "function";
}
function isLangChainTracerLike(x) {
    return typeof x === "object" && x != null && typeof x.name === "string" && x.name === "langchain_tracer";
}
function containsLangChainTracerLike(x) {
    return Array.isArray(x) && x.some((callback)=>isLangChainTracerLike(callback));
}
function isCallbackManagerLike(x) {
    return typeof x === "object" && x != null && Array.isArray(x.handlers);
}
function isRunnableConfigLike(x) {
    // Check that it's an object with a callbacks arg
    // that has either a CallbackManagerLike object with a langchain tracer within it
    // or an array with a LangChainTracerLike object within it
    return x !== undefined && typeof x.callbacks === "object" && // Callback manager with a langchain tracer
    (containsLangChainTracerLike(x.callbacks?.handlers) || // Or it's an array with a LangChainTracerLike object within it
    containsLangChainTracerLike(x.callbacks));
}
function _parseDottedOrder(dottedOrder) {
    const parts = dottedOrder.split(".");
    return parts.map((part)=>{
        const timestampStr = part.slice(0, -36);
        const uuidStr = part.slice(-36);
        // Parse timestamp: "%Y%m%dT%H%M%S%fZ" format
        // Example: "20231215T143045123456Z"
        const year = parseInt(timestampStr.slice(0, 4));
        const month = parseInt(timestampStr.slice(4, 6)) - 1; // JS months are 0-indexed
        const day = parseInt(timestampStr.slice(6, 8));
        const hour = parseInt(timestampStr.slice(9, 11));
        const minute = parseInt(timestampStr.slice(11, 13));
        const second = parseInt(timestampStr.slice(13, 15));
        const microsecond = parseInt(timestampStr.slice(15, 21));
        const timestamp = new Date(year, month, day, hour, minute, second, microsecond / 1000);
        return [
            timestamp,
            uuidStr
        ];
    });
}
function _getWriteReplicasFromEnv() {
    const envVar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEnvironmentVariable"])("LANGSMITH_RUNS_ENDPOINTS");
    if (!envVar) return [];
    try {
        const parsed = JSON.parse(envVar);
        _checkEndpointEnvUnset(parsed);
        return Object.entries(parsed).map(([url, key])=>({
                apiUrl: url.replace(/\/$/, ""),
                apiKey: key
            }));
    } catch (e) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isConflictingEndpointsError"])(e)) {
            throw e;
        }
        console.warn("Invalid LANGSMITH_RUNS_ENDPOINTS – must be valid JSON mapping of url->apiKey");
        return [];
    }
}
function _ensureWriteReplicas(replicas) {
    // If null -> fetch from env
    if (replicas) {
        return replicas.map((replica)=>{
            if (Array.isArray(replica)) {
                return {
                    projectName: replica[0],
                    updates: replica[1]
                };
            }
            return replica;
        });
    }
    return _getWriteReplicasFromEnv();
}
function _checkEndpointEnvUnset(parsed) {
    if (Object.keys(parsed).length > 0 && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLangSmithEnvironmentVariable"])("ENDPOINT")) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$utils$2f$error$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ConflictingEndpointsError"]();
    }
}
}),
"[project]/node_modules/langsmith/run_trees.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$run_trees$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/run_trees.js [app-route] (ecmascript)");
;
}),
"[project]/node_modules/langsmith/run_trees.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$run_trees$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/run_trees.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$run_trees$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/langsmith/run_trees.js [app-route] (ecmascript) <locals>");
}),
"[project]/node_modules/langsmith/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/index.js [app-route] (ecmascript) <module evaluation>");
;
}),
"[project]/node_modules/langsmith/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/langsmith/dist/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$langsmith$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/langsmith/index.js [app-route] (ecmascript) <locals>");
}),

};

//# sourceMappingURL=node_modules_langsmith_7974ab9c._.js.map
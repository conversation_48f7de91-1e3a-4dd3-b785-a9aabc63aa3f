{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/singletons/traceable.js"], "sourcesContent": ["class MockAsyncLocalStorage {\n    getStore() {\n        return undefined;\n    }\n    run(_, callback) {\n        return callback();\n    }\n}\nconst TRACING_ALS_KEY = Symbol.for(\"ls:tracing_async_local_storage\");\nconst mockAsyncLocalStorage = new MockAsyncLocalStorage();\nclass AsyncLocalStorageProvider {\n    getInstance() {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return globalThis[TRACING_ALS_KEY] ?? mockAsyncLocalStorage;\n    }\n    initializeGlobalInstance(instance) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (globalThis[TRACING_ALS_KEY] === undefined) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            globalThis[TRACING_ALS_KEY] = instance;\n        }\n    }\n}\nexport const AsyncLocalStorageProviderSingleton = new AsyncLocalStorageProvider();\nexport function getCurrentRunTree(permitAbsentRunTree = false) {\n    const runTree = AsyncLocalStorageProviderSingleton.getInstance().getStore();\n    if (!permitAbsentRunTree && runTree === undefined) {\n        throw new Error(\"Could not get the current run tree.\\n\\nPlease make sure you are calling this method within a traceable function and that tracing is enabled.\");\n    }\n    return runTree;\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function withRunTree(runTree, fn) {\n    const storage = AsyncLocalStorageProviderSingleton.getInstance();\n    return new Promise((resolve, reject) => {\n        storage.run(runTree, () => void Promise.resolve(fn()).then(resolve).catch(reject));\n    });\n}\nexport const ROOT = Symbol.for(\"langsmith:traceable:root\");\nexport function isTraceableFunction(x\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) {\n    return typeof x === \"function\" && \"langsmith:traceable\" in x;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,MAAM;IACF,WAAW;QACP,OAAO;IACX;IACA,IAAI,CAAC,EAAE,QAAQ,EAAE;QACb,OAAO;IACX;AACJ;AACA,MAAM,kBAAkB,OAAO,GAAG,CAAC;AACnC,MAAM,wBAAwB,IAAI;AAClC,MAAM;IACF,cAAc;QACV,8DAA8D;QAC9D,OAAO,UAAU,CAAC,gBAAgB,IAAI;IAC1C;IACA,yBAAyB,QAAQ,EAAE;QAC/B,8DAA8D;QAC9D,IAAI,UAAU,CAAC,gBAAgB,KAAK,WAAW;YAC3C,8DAA8D;YAC9D,UAAU,CAAC,gBAAgB,GAAG;QAClC;IACJ;AACJ;AACO,MAAM,qCAAqC,IAAI;AAC/C,SAAS,kBAAkB,sBAAsB,KAAK;IACzD,MAAM,UAAU,mCAAmC,WAAW,GAAG,QAAQ;IACzE,IAAI,CAAC,uBAAuB,YAAY,WAAW;QAC/C,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX;AAEO,SAAS,YAAY,OAAO,EAAE,EAAE;IACnC,MAAM,UAAU,mCAAmC,WAAW;IAC9D,OAAO,IAAI,QAAQ,CAAC,SAAS;QACzB,QAAQ,GAAG,CAAC,SAAS,IAAM,KAAK,QAAQ,OAAO,CAAC,MAAM,IAAI,CAAC,SAAS,KAAK,CAAC;IAC9E;AACJ;AACO,MAAM,OAAO,OAAO,GAAG,CAAC;AACxB,SAAS,oBAAoB,CAAC;IAGjC,OAAO,OAAO,MAAM,cAAc,yBAAyB;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/singletons/traceable.js"], "sourcesContent": ["export * from '../dist/singletons/traceable.js'"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/experimental/otel/constants.js"], "sourcesContent": ["// OpenTelemetry GenAI semantic convention attribute names\nexport const GEN_AI_OPERATION_NAME = \"gen_ai.operation.name\";\nexport const GEN_AI_SYSTEM = \"gen_ai.system\";\nexport const GEN_AI_REQUEST_MODEL = \"gen_ai.request.model\";\nexport const GEN_AI_RESPONSE_MODEL = \"gen_ai.response.model\";\nexport const GEN_AI_USAGE_INPUT_TOKENS = \"gen_ai.usage.input_tokens\";\nexport const GEN_AI_USAGE_OUTPUT_TOKENS = \"gen_ai.usage.output_tokens\";\nexport const GEN_AI_USAGE_TOTAL_TOKENS = \"gen_ai.usage.total_tokens\";\nexport const GEN_AI_REQUEST_MAX_TOKENS = \"gen_ai.request.max_tokens\";\nexport const GEN_AI_REQUEST_TEMPERATURE = \"gen_ai.request.temperature\";\nexport const GEN_AI_REQUEST_TOP_P = \"gen_ai.request.top_p\";\nexport const GEN_AI_REQUEST_FREQUENCY_PENALTY = \"gen_ai.request.frequency_penalty\";\nexport const GEN_AI_REQUEST_PRESENCE_PENALTY = \"gen_ai.request.presence_penalty\";\nexport const GEN_AI_RESPONSE_FINISH_REASONS = \"gen_ai.response.finish_reasons\";\nexport const GENAI_PROMPT = \"gen_ai.prompt\";\nexport const GENAI_COMPLETION = \"gen_ai.completion\";\nexport const GEN_AI_REQUEST_EXTRA_QUERY = \"gen_ai.request.extra_query\";\nexport const GEN_AI_REQUEST_EXTRA_BODY = \"gen_ai.request.extra_body\";\nexport const GEN_AI_SERIALIZED_NAME = \"gen_ai.serialized.name\";\nexport const GEN_AI_SERIALIZED_SIGNATURE = \"gen_ai.serialized.signature\";\nexport const GEN_AI_SERIALIZED_DOC = \"gen_ai.serialized.doc\";\nexport const GEN_AI_RESPONSE_ID = \"gen_ai.response.id\";\nexport const GEN_AI_RESPONSE_SERVICE_TIER = \"gen_ai.response.service_tier\";\nexport const GEN_AI_RESPONSE_SYSTEM_FINGERPRINT = \"gen_ai.response.system_fingerprint\";\nexport const GEN_AI_USAGE_INPUT_TOKEN_DETAILS = \"gen_ai.usage.input_token_details\";\nexport const GEN_AI_USAGE_OUTPUT_TOKEN_DETAILS = \"gen_ai.usage.output_token_details\";\n// LangSmith custom attributes\nexport const LANGSMITH_SESSION_ID = \"langsmith.trace.session_id\";\nexport const LANGSMITH_SESSION_NAME = \"langsmith.trace.session_name\";\nexport const LANGSMITH_RUN_TYPE = \"langsmith.span.kind\";\nexport const LANGSMITH_NAME = \"langsmith.trace.name\";\nexport const LANGSMITH_METADATA = \"langsmith.metadata\";\nexport const LANGSMITH_TAGS = \"langsmith.span.tags\";\nexport const LANGSMITH_RUNTIME = \"langsmith.span.runtime\";\nexport const LANGSMITH_REQUEST_STREAMING = \"langsmith.request.streaming\";\nexport const LANGSMITH_REQUEST_HEADERS = \"langsmith.request.headers\";\nexport const LANGSMITH_RUN_ID = \"langsmith.span.id\";\nexport const LANGSMITH_TRACE_ID = \"langsmith.trace.id\";\nexport const LANGSMITH_DOTTED_ORDER = \"langsmith.span.dotted_order\";\nexport const LANGSMITH_PARENT_RUN_ID = \"langsmith.span.parent_id\";\nexport const LANGSMITH_USAGE_METADATA = \"langsmith.usage_metadata\";\nexport const LANGSMITH_REFERENCE_EXAMPLE_ID = \"langsmith.reference_example_id\";\n// GenAI event names\nexport const GEN_AI_SYSTEM_MESSAGE = \"gen_ai.system.message\";\nexport const GEN_AI_USER_MESSAGE = \"gen_ai.user.message\";\nexport const GEN_AI_ASSISTANT_MESSAGE = \"gen_ai.assistant.message\";\nexport const GEN_AI_CHOICE = \"gen_ai.choice\";\nexport const AI_SDK_LLM_OPERATIONS = [\n    \"ai.generateText.doGenerate\",\n    \"ai.streamText.doStream\",\n    \"ai.generateObject.doGenerate\",\n    \"ai.streamObject.doStream\",\n];\nexport const AI_SDK_TOOL_OPERATIONS = [\"ai.toolCall\"];\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACnD,MAAM,wBAAwB;AAC9B,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,wBAAwB;AAC9B,MAAM,4BAA4B;AAClC,MAAM,6BAA6B;AACnC,MAAM,4BAA4B;AAClC,MAAM,4BAA4B;AAClC,MAAM,6BAA6B;AACnC,MAAM,uBAAuB;AAC7B,MAAM,mCAAmC;AACzC,MAAM,kCAAkC;AACxC,MAAM,iCAAiC;AACvC,MAAM,eAAe;AACrB,MAAM,mBAAmB;AACzB,MAAM,6BAA6B;AACnC,MAAM,4BAA4B;AAClC,MAAM,yBAAyB;AAC/B,MAAM,8BAA8B;AACpC,MAAM,wBAAwB;AAC9B,MAAM,qBAAqB;AAC3B,MAAM,+BAA+B;AACrC,MAAM,qCAAqC;AAC3C,MAAM,mCAAmC;AACzC,MAAM,oCAAoC;AAE1C,MAAM,uBAAuB;AAC7B,MAAM,yBAAyB;AAC/B,MAAM,qBAAqB;AAC3B,MAAM,iBAAiB;AACvB,MAAM,qBAAqB;AAC3B,MAAM,iBAAiB;AACvB,MAAM,oBAAoB;AAC1B,MAAM,8BAA8B;AACpC,MAAM,4BAA4B;AAClC,MAAM,mBAAmB;AACzB,MAAM,qBAAqB;AAC3B,MAAM,yBAAyB;AAC/B,MAAM,0BAA0B;AAChC,MAAM,2BAA2B;AACjC,MAAM,iCAAiC;AAEvC,MAAM,wBAAwB;AAC9B,MAAM,sBAAsB;AAC5B,MAAM,2BAA2B;AACjC,MAAM,gBAAgB;AACtB,MAAM,wBAAwB;IACjC;IACA;IACA;IACA;CACH;AACM,MAAM,yBAAyB;IAAC;CAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/singletons/fetch.js"], "sourcesContent": ["import { getLangSmithEnvironmentVariable } from \"../utils/env.js\";\n// Wrap the default fetch call due to issues with illegal invocations\n// in some environments:\n// https://stackoverflow.com/questions/69876859/why-does-bind-fix-failed-to-execute-fetch-on-window-illegal-invocation-err\n// @ts-expect-error Broad typing to support a range of fetch implementations\nconst DEFAULT_FETCH_IMPLEMENTATION = (...args) => fetch(...args);\nconst LANGSMITH_FETCH_IMPLEMENTATION_KEY = Symbol.for(\"ls:fetch_implementation\");\n/**\n * Overrides the fetch implementation used for Lang<PERSON>mith calls.\n * You should use this if you need to use an implementation of fetch\n * other than the default global (e.g. for dealing with proxies).\n * @param fetch The new fetch functino to use.\n */\nexport const overrideFetchImplementation = (fetch) => {\n    globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] = fetch;\n};\nexport const _globalFetchImplementationIsNodeFetch = () => {\n    const fetchImpl = globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY];\n    if (!fetchImpl)\n        return false;\n    // Check if the implementation has node-fetch specific properties\n    return (typeof fetchImpl === \"function\" &&\n        \"Headers\" in fetchImpl &&\n        \"Request\" in fetchImpl &&\n        \"Response\" in fetchImpl);\n};\n/**\n * @internal\n */\nexport const _getFetchImplementation = (debug) => {\n    return async (...args) => {\n        if (debug || getLangSmithEnvironmentVariable(\"DEBUG\") === \"true\") {\n            const [url, options] = args;\n            console.log(`→ ${options?.method || \"GET\"} ${url}`);\n        }\n        const res = await (globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] ??\n            DEFAULT_FETCH_IMPLEMENTATION)(...args);\n        if (debug || getLangSmithEnvironmentVariable(\"DEBUG\") === \"true\") {\n            console.log(`← ${res.status} ${res.statusText} ${res.url}`);\n        }\n        return res;\n    };\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AACA,qEAAqE;AACrE,wBAAwB;AACxB,0HAA0H;AAC1H,4EAA4E;AAC5E,MAAM,+BAA+B,CAAC,GAAG,OAAS,SAAS;AAC3D,MAAM,qCAAqC,OAAO,GAAG,CAAC;AAO/C,MAAM,8BAA8B,CAAC;IACxC,UAAU,CAAC,mCAAmC,GAAG;AACrD;AACO,MAAM,wCAAwC;IACjD,MAAM,YAAY,UAAU,CAAC,mCAAmC;IAChE,IAAI,CAAC,WACD,OAAO;IACX,iEAAiE;IACjE,OAAQ,OAAO,cAAc,cACzB,aAAa,aACb,aAAa,aACb,cAAc;AACtB;AAIO,MAAM,0BAA0B,CAAC;IACpC,OAAO,OAAO,GAAG;QACb,IAAI,SAAS,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,aAAa,QAAQ;YAC9D,MAAM,CAAC,KAAK,QAAQ,GAAG;YACvB,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,SAAS,UAAU,MAAM,CAAC,EAAE,KAAK;QACtD;QACA,MAAM,MAAM,MAAM,CAAC,UAAU,CAAC,mCAAmC,IAC7D,4BAA4B,KAAK;QACrC,IAAI,SAAS,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,aAAa,QAAQ;YAC9D,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE;QAC9D;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/utils/project.js"], "sourcesContent": ["import { getEnvironmentVariable, getLangSmithEnvironmentVariable, } from \"./env.js\";\nexport const getDefaultProjectName = () => {\n    return (getLangSmithEnvironmentVariable(\"PROJECT\") ??\n        getEnvironmentVariable(\"LANGCHAIN_SESSION\") ?? // TODO: Deprecate\n        \"default\");\n};\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,wBAAwB;IACjC,OAAQ,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,cACpC,CAAA,GAAA,mJAAA,CAAA,yBAAsB,AAAD,EAAE,wBAAwB,kBAAkB;IACjE;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/index.js"], "sourcesContent": ["export { Client, } from \"./client.js\";\nexport { RunTree } from \"./run_trees.js\";\nexport { overrideFetchImplementation } from \"./singletons/fetch.js\";\nexport { getDefaultProjectName } from \"./utils/project.js\";\n// Update using yarn bump-version\nexport const __version__ = \"0.3.46\";\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/utils/env.js"], "sourcesContent": ["// Inlined from https://github.com/flexdinesh/browser-or-node\nimport { __version__ } from \"../index.js\";\nlet globalEnv;\nexport const isBrowser = () => typeof window !== \"undefined\" && typeof window.document !== \"undefined\";\nexport const isWebWorker = () => typeof globalThis === \"object\" &&\n    globalThis.constructor &&\n    globalThis.constructor.name === \"DedicatedWorkerGlobalScope\";\nexport const isJsDom = () => (typeof window !== \"undefined\" && window.name === \"nodejs\") ||\n    (typeof navigator !== \"undefined\" && navigator.userAgent.includes(\"jsdom\"));\n// Supabase Edge Function provides a `Deno` global object\n// without `version` property\nexport const isDeno = () => typeof Deno !== \"undefined\";\n// Mark not-as-node if in Supabase Edge Function\nexport const isNode = () => typeof process !== \"undefined\" &&\n    typeof process.versions !== \"undefined\" &&\n    typeof process.versions.node !== \"undefined\" &&\n    !isDeno();\nexport const getEnv = () => {\n    if (globalEnv) {\n        return globalEnv;\n    }\n    if (isBrowser()) {\n        globalEnv = \"browser\";\n    }\n    else if (isNode()) {\n        globalEnv = \"node\";\n    }\n    else if (isWebWorker()) {\n        globalEnv = \"webworker\";\n    }\n    else if (isJsDom()) {\n        globalEnv = \"jsdom\";\n    }\n    else if (isDeno()) {\n        globalEnv = \"deno\";\n    }\n    else {\n        globalEnv = \"other\";\n    }\n    return globalEnv;\n};\nlet runtimeEnvironment;\nexport function getRuntimeEnvironment() {\n    if (runtimeEnvironment === undefined) {\n        const env = getEnv();\n        const releaseEnv = getShas();\n        runtimeEnvironment = {\n            library: \"langsmith\",\n            runtime: env,\n            sdk: \"langsmith-js\",\n            sdk_version: __version__,\n            ...releaseEnv,\n        };\n    }\n    return runtimeEnvironment;\n}\n/**\n * Retrieves the LangChain-specific environment variables from the current runtime environment.\n * Sensitive keys (containing the word \"key\", \"token\", or \"secret\") have their values redacted for security.\n *\n * @returns {Record<string, string>}\n *  - A record of LangChain-specific environment variables.\n */\nexport function getLangChainEnvVars() {\n    const allEnvVars = getEnvironmentVariables() || {};\n    const envVars = {};\n    for (const [key, value] of Object.entries(allEnvVars)) {\n        if (key.startsWith(\"LANGCHAIN_\") && typeof value === \"string\") {\n            envVars[key] = value;\n        }\n    }\n    for (const key in envVars) {\n        if ((key.toLowerCase().includes(\"key\") ||\n            key.toLowerCase().includes(\"secret\") ||\n            key.toLowerCase().includes(\"token\")) &&\n            typeof envVars[key] === \"string\") {\n            const value = envVars[key];\n            envVars[key] =\n                value.slice(0, 2) + \"*\".repeat(value.length - 4) + value.slice(-2);\n        }\n    }\n    return envVars;\n}\n/**\n * Retrieves the LangChain-specific metadata from the current runtime environment.\n *\n * @returns {Record<string, string>}\n *  - A record of LangChain-specific metadata environment variables.\n */\nexport function getLangChainEnvVarsMetadata() {\n    const allEnvVars = getEnvironmentVariables() || {};\n    const envVars = {};\n    const excluded = [\n        \"LANGCHAIN_API_KEY\",\n        \"LANGCHAIN_ENDPOINT\",\n        \"LANGCHAIN_TRACING_V2\",\n        \"LANGCHAIN_PROJECT\",\n        \"LANGCHAIN_SESSION\",\n        \"LANGSMITH_API_KEY\",\n        \"LANGSMITH_ENDPOINT\",\n        \"LANGSMITH_TRACING_V2\",\n        \"LANGSMITH_PROJECT\",\n        \"LANGSMITH_SESSION\",\n    ];\n    for (const [key, value] of Object.entries(allEnvVars)) {\n        if ((key.startsWith(\"LANGCHAIN_\") || key.startsWith(\"LANGSMITH_\")) &&\n            typeof value === \"string\" &&\n            !excluded.includes(key) &&\n            !key.toLowerCase().includes(\"key\") &&\n            !key.toLowerCase().includes(\"secret\") &&\n            !key.toLowerCase().includes(\"token\")) {\n            if (key === \"LANGCHAIN_REVISION_ID\") {\n                envVars[\"revision_id\"] = value;\n            }\n            else {\n                envVars[key] = value;\n            }\n        }\n    }\n    return envVars;\n}\n/**\n * Retrieves the environment variables from the current runtime environment.\n *\n * This function is designed to operate in a variety of JS environments,\n * including Node.js, Deno, browsers, etc.\n *\n * @returns {Record<string, string> | undefined}\n *  - A record of environment variables if available.\n *  - `undefined` if the environment does not support or allows access to environment variables.\n */\nexport function getEnvironmentVariables() {\n    try {\n        // Check for Node.js environment\n        // eslint-disable-next-line no-process-env\n        if (typeof process !== \"undefined\" && process.env) {\n            // eslint-disable-next-line no-process-env\n            return Object.entries(process.env).reduce((acc, [key, value]) => {\n                acc[key] = String(value);\n                return acc;\n            }, {});\n        }\n        // For browsers and other environments, we may not have direct access to env variables\n        // Return undefined or any other fallback as required.\n        return undefined;\n    }\n    catch (e) {\n        // Catch any errors that might occur while trying to access environment variables\n        return undefined;\n    }\n}\nexport function getEnvironmentVariable(name) {\n    // Certain Deno setups will throw an error if you try to access environment variables\n    // https://github.com/hwchase17/langchainjs/issues/1412\n    try {\n        return typeof process !== \"undefined\"\n            ? // eslint-disable-next-line no-process-env\n                process.env?.[name]\n            : undefined;\n    }\n    catch (e) {\n        return undefined;\n    }\n}\nexport function getLangSmithEnvironmentVariable(name) {\n    return (getEnvironmentVariable(`LANGSMITH_${name}`) ||\n        getEnvironmentVariable(`LANGCHAIN_${name}`));\n}\nexport function setEnvironmentVariable(name, value) {\n    if (typeof process !== \"undefined\") {\n        // eslint-disable-next-line no-process-env\n        process.env[name] = value;\n    }\n}\nlet cachedCommitSHAs;\n/**\n * Get the Git commit SHA from common environment variables\n * used by different CI/CD platforms.\n * @returns {string | undefined} The Git commit SHA or undefined if not found.\n */\nexport function getShas() {\n    if (cachedCommitSHAs !== undefined) {\n        return cachedCommitSHAs;\n    }\n    const common_release_envs = [\n        \"VERCEL_GIT_COMMIT_SHA\",\n        \"NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA\",\n        \"COMMIT_REF\",\n        \"RENDER_GIT_COMMIT\",\n        \"CI_COMMIT_SHA\",\n        \"CIRCLE_SHA1\",\n        \"CF_PAGES_COMMIT_SHA\",\n        \"REACT_APP_GIT_SHA\",\n        \"SOURCE_VERSION\",\n        \"GITHUB_SHA\",\n        \"TRAVIS_COMMIT\",\n        \"GIT_COMMIT\",\n        \"BUILD_VCS_NUMBER\",\n        \"bamboo_planRepository_revision\",\n        \"Build.SourceVersion\",\n        \"BITBUCKET_COMMIT\",\n        \"DRONE_COMMIT_SHA\",\n        \"SEMAPHORE_GIT_SHA\",\n        \"BUILDKITE_COMMIT\",\n    ];\n    const shas = {};\n    for (const env of common_release_envs) {\n        const envVar = getEnvironmentVariable(env);\n        if (envVar !== undefined) {\n            shas[env] = envVar;\n        }\n    }\n    cachedCommitSHAs = shas;\n    return shas;\n}\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;;;;;;;;;;;;;;AAC7D;AAAA;;AACA,IAAI;AACG,MAAM,YAAY,IAAM,gBAAkB,eAAe,OAAO,OAAO,QAAQ,KAAK;AACpF,MAAM,cAAc,IAAM,OAAO,eAAe,YACnD,WAAW,WAAW,IACtB,WAAW,WAAW,CAAC,IAAI,KAAK;AAC7B,MAAM,UAAU,IAAM,AAAC,gBAAkB,eAAe,OAAO,IAAI,KAAK,YAC1E,OAAO,cAAc,eAAe,UAAU,SAAS,CAAC,QAAQ,CAAC;AAG/D,MAAM,SAAS,IAAM,OAAO,SAAS;AAErC,MAAM,SAAS,IAAM,OAAO,YAAY,eAC3C,OAAO,QAAQ,QAAQ,KAAK,eAC5B,OAAO,QAAQ,QAAQ,CAAC,IAAI,KAAK,eACjC,CAAC;AACE,MAAM,SAAS;IAClB,IAAI,WAAW;QACX,OAAO;IACX;IACA,IAAI,aAAa;QACb,YAAY;IAChB,OACK,IAAI,UAAU;QACf,YAAY;IAChB,OACK,IAAI,eAAe;QACpB,YAAY;IAChB,OACK,IAAI,WAAW;QAChB,YAAY;IAChB,OACK,IAAI,UAAU;QACf,YAAY;IAChB,OACK;QACD,YAAY;IAChB;IACA,OAAO;AACX;AACA,IAAI;AACG,SAAS;IACZ,IAAI,uBAAuB,WAAW;QAClC,MAAM,MAAM;QACZ,MAAM,aAAa;QACnB,qBAAqB;YACjB,SAAS;YACT,SAAS;YACT,KAAK;YACL,aAAa,4JAAA,CAAA,cAAW;YACxB,GAAG,UAAU;QACjB;IACJ;IACA,OAAO;AACX;AAQO,SAAS;IACZ,MAAM,aAAa,6BAA6B,CAAC;IACjD,MAAM,UAAU,CAAC;IACjB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,YAAa;QACnD,IAAI,IAAI,UAAU,CAAC,iBAAiB,OAAO,UAAU,UAAU;YAC3D,OAAO,CAAC,IAAI,GAAG;QACnB;IACJ;IACA,IAAK,MAAM,OAAO,QAAS;QACvB,IAAI,CAAC,IAAI,WAAW,GAAG,QAAQ,CAAC,UAC5B,IAAI,WAAW,GAAG,QAAQ,CAAC,aAC3B,IAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ,KACnC,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU;YAClC,MAAM,QAAQ,OAAO,CAAC,IAAI;YAC1B,OAAO,CAAC,IAAI,GACR,MAAM,KAAK,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG,KAAK,MAAM,KAAK,CAAC,CAAC;QACxE;IACJ;IACA,OAAO;AACX;AAOO,SAAS;IACZ,MAAM,aAAa,6BAA6B,CAAC;IACjD,MAAM,UAAU,CAAC;IACjB,MAAM,WAAW;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,YAAa;QACnD,IAAI,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,UAAU,CAAC,aAAa,KAC7D,OAAO,UAAU,YACjB,CAAC,SAAS,QAAQ,CAAC,QACnB,CAAC,IAAI,WAAW,GAAG,QAAQ,CAAC,UAC5B,CAAC,IAAI,WAAW,GAAG,QAAQ,CAAC,aAC5B,CAAC,IAAI,WAAW,GAAG,QAAQ,CAAC,UAAU;YACtC,IAAI,QAAQ,yBAAyB;gBACjC,OAAO,CAAC,cAAc,GAAG;YAC7B,OACK;gBACD,OAAO,CAAC,IAAI,GAAG;YACnB;QACJ;IACJ;IACA,OAAO;AACX;AAWO,SAAS;IACZ,IAAI;QACA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAI,OAAO,YAAY,eAAe,QAAQ,GAAG,EAAE;YAC/C,0CAA0C;YAC1C,OAAO,OAAO,OAAO,CAAC,QAAQ,GAAG,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM;gBACxD,GAAG,CAAC,IAAI,GAAG,OAAO;gBAClB,OAAO;YACX,GAAG,CAAC;QACR;QACA,sFAAsF;QACtF,sDAAsD;QACtD,OAAO;IACX,EACA,OAAO,GAAG;QACN,iFAAiF;QACjF,OAAO;IACX;AACJ;AACO,SAAS,uBAAuB,IAAI;IACvC,qFAAqF;IACrF,uDAAuD;IACvD,IAAI;QACA,OAAO,OAAO,YAAY,cAElB,QAAQ,GAAG,EAAE,CAAC,KAAK,GACrB;IACV,EACA,OAAO,GAAG;QACN,OAAO;IACX;AACJ;AACO,SAAS,gCAAgC,IAAI;IAChD,OAAQ,uBAAuB,CAAC,UAAU,EAAE,MAAM,KAC9C,uBAAuB,CAAC,UAAU,EAAE,MAAM;AAClD;AACO,SAAS,uBAAuB,IAAI,EAAE,KAAK;IAC9C,IAAI,OAAO,YAAY,aAAa;QAChC,0CAA0C;QAC1C,QAAQ,GAAG,CAAC,KAAK,GAAG;IACxB;AACJ;AACA,IAAI;AAMG,SAAS;IACZ,IAAI,qBAAqB,WAAW;QAChC,OAAO;IACX;IACA,MAAM,sBAAsB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,MAAM,OAAO,CAAC;IACd,KAAK,MAAM,OAAO,oBAAqB;QACnC,MAAM,SAAS,uBAAuB;QACtC,IAAI,WAAW,WAAW;YACtB,IAAI,CAAC,IAAI,GAAG;QAChB;IACJ;IACA,mBAAmB;IACnB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/singletons/otel.js"], "sourcesContent": ["// Should not import any OTEL packages to avoid pulling in optional deps.\nimport { getEnvironmentVariable } from \"../utils/env.js\";\nclass MockTracer {\n    constructor() {\n        Object.defineProperty(this, \"hasWarned\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n    }\n    startActiveSpan(_name, ...args) {\n        if (!this.hasWarned && getEnvironmentVariable(\"OTEL_ENABLED\") === \"true\") {\n            console.warn(\"You have enabled OTEL export via the `OTEL_ENABLED` environment variable, but have not initialized the required OTEL instances. \" +\n                'Please add:\\n```\\nimport { initializeOTEL } from \"langsmith/experimental/otel/setup\";\\ninitializeOTEL();\\n```\\nat the beginning of your code.');\n            this.hasWarned = true;\n        }\n        // Handle different overloads:\n        // startActiveSpan(name, fn)\n        // startActiveSpan(name, options, fn)\n        // startActiveSpan(name, options, context, fn)\n        let fn;\n        if (args.length === 1 && typeof args[0] === \"function\") {\n            fn = args[0];\n        }\n        else if (args.length === 2 && typeof args[1] === \"function\") {\n            fn = args[1];\n        }\n        else if (args.length === 3 && typeof args[2] === \"function\") {\n            fn = args[2];\n        }\n        if (typeof fn === \"function\") {\n            return fn();\n        }\n        return undefined;\n    }\n}\nclass MockOTELTrace {\n    constructor() {\n        Object.defineProperty(this, \"mockTracer\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new MockTracer()\n        });\n    }\n    getTracer(_name, _version) {\n        return this.mockTracer;\n    }\n    getActiveSpan() {\n        return undefined;\n    }\n    setSpan(context, _span) {\n        return context;\n    }\n    getSpan(_context) {\n        return undefined;\n    }\n    setSpanContext(context, _spanContext) {\n        return context;\n    }\n    getTracerProvider() {\n        return undefined;\n    }\n    setGlobalTracerProvider(_tracerProvider) {\n        return false;\n    }\n}\nclass MockOTELContext {\n    active() {\n        return {};\n    }\n    with(_context, fn) {\n        return fn();\n    }\n}\nconst OTEL_TRACE_KEY = Symbol.for(\"ls:otel_trace\");\nconst OTEL_CONTEXT_KEY = Symbol.for(\"ls:otel_context\");\nconst OTEL_GET_DEFAULT_OTLP_TRACER_PROVIDER_KEY = Symbol.for(\"ls:otel_get_default_otlp_tracer_provider\");\nconst mockOTELTrace = new MockOTELTrace();\nconst mockOTELContext = new MockOTELContext();\nclass OTELProvider {\n    getTraceInstance() {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return globalThis[OTEL_TRACE_KEY] ?? mockOTELTrace;\n    }\n    getContextInstance() {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return globalThis[OTEL_CONTEXT_KEY] ?? mockOTELContext;\n    }\n    initializeGlobalInstances(otel) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (globalThis[OTEL_TRACE_KEY] === undefined) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            globalThis[OTEL_TRACE_KEY] = otel.trace;\n        }\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (globalThis[OTEL_CONTEXT_KEY] === undefined) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            globalThis[OTEL_CONTEXT_KEY] = otel.context;\n        }\n    }\n    setDefaultOTLPTracerComponents(components) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        globalThis[OTEL_GET_DEFAULT_OTLP_TRACER_PROVIDER_KEY] = components;\n    }\n    getDefaultOTLPTracerComponents() {\n        return (globalThis[OTEL_GET_DEFAULT_OTLP_TRACER_PROVIDER_KEY] ??\n            undefined);\n    }\n}\nexport const OTELProviderSingleton = new OTELProvider();\n/**\n * Get the current OTEL trace instance.\n * Returns a mock implementation if OTEL is not available.\n */\nexport function getOTELTrace() {\n    return OTELProviderSingleton.getTraceInstance();\n}\n/**\n * Get the current OTEL context instance.\n * Returns a mock implementation if OTEL is not available.\n */\nexport function getOTELContext() {\n    return OTELProviderSingleton.getContextInstance();\n}\n/**\n * Initialize the global OTEL instances.\n * Should be called once when OTEL packages are available.\n */\nexport function setOTELInstances(otel) {\n    OTELProviderSingleton.initializeGlobalInstances(otel);\n}\n/**\n * Set a getter function for the default OTLP tracer provider.\n * This allows lazy initialization of the tracer provider.\n */\nexport function setDefaultOTLPTracerComponents(components) {\n    OTELProviderSingleton.setDefaultOTLPTracerComponents(components);\n}\n/**\n * Get the default OTLP tracer provider instance.\n * Returns undefined if not set.\n */\nexport function getDefaultOTLPTracerComponents() {\n    return OTELProviderSingleton.getDefaultOTLPTracerComponents();\n}\n"], "names": [], "mappings": "AAAA,yEAAyE;;;;;;;;;AACzE;;AACA,MAAM;IACF,aAAc;QACV,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;IACJ;IACA,gBAAgB,KAAK,EAAE,GAAG,IAAI,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAA,GAAA,mJAAA,CAAA,yBAAsB,AAAD,EAAE,oBAAoB,QAAQ;YACtE,QAAQ,IAAI,CAAC,qIACT;YACJ,IAAI,CAAC,SAAS,GAAG;QACrB;QACA,8BAA8B;QAC9B,4BAA4B;QAC5B,qCAAqC;QACrC,8CAA8C;QAC9C,IAAI;QACJ,IAAI,KAAK,MAAM,KAAK,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,YAAY;YACpD,KAAK,IAAI,CAAC,EAAE;QAChB,OACK,IAAI,KAAK,MAAM,KAAK,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,YAAY;YACzD,KAAK,IAAI,CAAC,EAAE;QAChB,OACK,IAAI,KAAK,MAAM,KAAK,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,YAAY;YACzD,KAAK,IAAI,CAAC,EAAE;QAChB;QACA,IAAI,OAAO,OAAO,YAAY;YAC1B,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,MAAM;IACF,aAAc;QACV,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,IAAI;QACf;IACJ;IACA,UAAU,KAAK,EAAE,QAAQ,EAAE;QACvB,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,gBAAgB;QACZ,OAAO;IACX;IACA,QAAQ,OAAO,EAAE,KAAK,EAAE;QACpB,OAAO;IACX;IACA,QAAQ,QAAQ,EAAE;QACd,OAAO;IACX;IACA,eAAe,OAAO,EAAE,YAAY,EAAE;QAClC,OAAO;IACX;IACA,oBAAoB;QAChB,OAAO;IACX;IACA,wBAAwB,eAAe,EAAE;QACrC,OAAO;IACX;AACJ;AACA,MAAM;IACF,SAAS;QACL,OAAO,CAAC;IACZ;IACA,KAAK,QAAQ,EAAE,EAAE,EAAE;QACf,OAAO;IACX;AACJ;AACA,MAAM,iBAAiB,OAAO,GAAG,CAAC;AAClC,MAAM,mBAAmB,OAAO,GAAG,CAAC;AACpC,MAAM,4CAA4C,OAAO,GAAG,CAAC;AAC7D,MAAM,gBAAgB,IAAI;AAC1B,MAAM,kBAAkB,IAAI;AAC5B,MAAM;IACF,mBAAmB;QACf,8DAA8D;QAC9D,OAAO,UAAU,CAAC,eAAe,IAAI;IACzC;IACA,qBAAqB;QACjB,8DAA8D;QAC9D,OAAO,UAAU,CAAC,iBAAiB,IAAI;IAC3C;IACA,0BAA0B,IAAI,EAAE;QAC5B,8DAA8D;QAC9D,IAAI,UAAU,CAAC,eAAe,KAAK,WAAW;YAC1C,8DAA8D;YAC9D,UAAU,CAAC,eAAe,GAAG,KAAK,KAAK;QAC3C;QACA,8DAA8D;QAC9D,IAAI,UAAU,CAAC,iBAAiB,KAAK,WAAW;YAC5C,8DAA8D;YAC9D,UAAU,CAAC,iBAAiB,GAAG,KAAK,OAAO;QAC/C;IACJ;IACA,+BAA+B,UAAU,EAAE;QACvC,8DAA8D;QAC9D,UAAU,CAAC,0CAA0C,GAAG;IAC5D;IACA,iCAAiC;QAC7B,OAAQ,UAAU,CAAC,0CAA0C,IACzD;IACR;AACJ;AACO,MAAM,wBAAwB,IAAI;AAKlC,SAAS;IACZ,OAAO,sBAAsB,gBAAgB;AACjD;AAKO,SAAS;IACZ,OAAO,sBAAsB,kBAAkB;AACnD;AAKO,SAAS,iBAAiB,IAAI;IACjC,sBAAsB,yBAAyB,CAAC;AACpD;AAKO,SAAS,+BAA+B,UAAU;IACrD,sBAAsB,8BAA8B,CAAC;AACzD;AAKO,SAAS;IACZ,OAAO,sBAAsB,8BAA8B;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/experimental/otel/translator.js"], "sourcesContent": ["import * as constants from \"./constants.js\";\nimport { getOTELTrace } from \"../../singletons/otel.js\";\nconst WELL_KNOWN_OPERATION_NAMES = {\n    llm: \"chat\",\n    tool: \"execute_tool\",\n    retriever: \"embeddings\",\n    embedding: \"embeddings\",\n    prompt: \"chat\",\n};\nfunction getOperationName(runType) {\n    return WELL_KNOWN_OPERATION_NAMES[runType] || runType;\n}\nexport class LangSmithToOTELTranslator {\n    constructor() {\n        Object.defineProperty(this, \"spans\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new Map()\n        });\n    }\n    exportBatch(operations, otelContextMap) {\n        for (const op of operations) {\n            try {\n                if (!op.run) {\n                    continue;\n                }\n                if (op.operation === \"post\") {\n                    const span = this.createSpanForRun(op, op.run, otelContextMap.get(op.id));\n                    if (span && !op.run.end_time) {\n                        this.spans.set(op.id, span);\n                    }\n                }\n                else {\n                    this.updateSpanForRun(op, op.run);\n                }\n            }\n            catch (e) {\n                console.error(`Error processing operation ${op.id}:`, e);\n            }\n        }\n    }\n    createSpanForRun(op, runInfo, otelContext) {\n        const activeSpan = otelContext && getOTELTrace().getSpan(otelContext);\n        if (!activeSpan) {\n            return;\n        }\n        try {\n            return this.finishSpanSetup(activeSpan, runInfo, op);\n        }\n        catch (e) {\n            console.error(`Failed to create span for run ${op.id}:`, e);\n            return undefined;\n        }\n    }\n    finishSpanSetup(span, runInfo, op) {\n        // Set all attributes\n        this.setSpanAttributes(span, runInfo, op);\n        // Set status based on error\n        if (runInfo.error) {\n            span.setStatus({ code: 2 }); // ERROR status\n            span.recordException(new Error(runInfo.error));\n        }\n        else {\n            span.setStatus({ code: 1 }); // OK status\n        }\n        // End the span if end_time is present\n        if (runInfo.end_time) {\n            span.end(new Date(runInfo.end_time));\n        }\n        return span;\n    }\n    updateSpanForRun(op, runInfo) {\n        try {\n            const span = this.spans.get(op.id);\n            if (!span) {\n                console.debug(`No span found for run ${op.id} during update`);\n                return;\n            }\n            // Update attributes\n            this.setSpanAttributes(span, runInfo, op);\n            // Update status based on error\n            if (runInfo.error) {\n                span.setStatus({ code: 2 }); // ERROR status\n                span.recordException(new Error(runInfo.error));\n            }\n            else {\n                span.setStatus({ code: 1 }); // OK status\n            }\n            // End the span if end_time is present\n            const endTime = runInfo.end_time;\n            if (endTime) {\n                span.end(new Date(endTime));\n                this.spans.delete(op.id);\n            }\n        }\n        catch (e) {\n            console.error(`Failed to update span for run ${op.id}:`, e);\n        }\n    }\n    extractModelName(runInfo) {\n        // Try to get model name from metadata\n        if (runInfo.extra?.metadata) {\n            const metadata = runInfo.extra.metadata;\n            // First check for ls_model_name in metadata\n            if (metadata.ls_model_name) {\n                return metadata.ls_model_name;\n            }\n            // Then check invocation_params for model info\n            if (metadata.invocation_params) {\n                const invocationParams = metadata.invocation_params;\n                if (invocationParams.model) {\n                    return invocationParams.model;\n                }\n                else if (invocationParams.model_name) {\n                    return invocationParams.model_name;\n                }\n            }\n        }\n        return;\n    }\n    setSpanAttributes(span, runInfo, op) {\n        if (\"run_type\" in runInfo && runInfo.run_type) {\n            span.setAttribute(constants.LANGSMITH_RUN_TYPE, runInfo.run_type);\n            // Set GenAI attributes according to OTEL semantic conventions\n            const operationName = getOperationName(runInfo.run_type || \"chain\");\n            span.setAttribute(constants.GEN_AI_OPERATION_NAME, operationName);\n        }\n        if (\"name\" in runInfo && runInfo.name) {\n            span.setAttribute(constants.LANGSMITH_NAME, runInfo.name);\n        }\n        if (\"session_id\" in runInfo && runInfo.session_id) {\n            span.setAttribute(constants.LANGSMITH_SESSION_ID, runInfo.session_id);\n        }\n        if (\"session_name\" in runInfo && runInfo.session_name) {\n            span.setAttribute(constants.LANGSMITH_SESSION_NAME, runInfo.session_name);\n        }\n        // Set gen_ai.system\n        this.setGenAiSystem(span, runInfo);\n        // Set model name if available\n        const modelName = this.extractModelName(runInfo);\n        if (modelName) {\n            span.setAttribute(constants.GEN_AI_REQUEST_MODEL, modelName);\n        }\n        // Set token usage information\n        if (\"prompt_tokens\" in runInfo &&\n            typeof runInfo.prompt_tokens === \"number\") {\n            span.setAttribute(constants.GEN_AI_USAGE_INPUT_TOKENS, runInfo.prompt_tokens);\n        }\n        if (\"completion_tokens\" in runInfo &&\n            typeof runInfo.completion_tokens === \"number\") {\n            span.setAttribute(constants.GEN_AI_USAGE_OUTPUT_TOKENS, runInfo.completion_tokens);\n        }\n        if (\"total_tokens\" in runInfo && typeof runInfo.total_tokens === \"number\") {\n            span.setAttribute(constants.GEN_AI_USAGE_TOTAL_TOKENS, runInfo.total_tokens);\n        }\n        // Set other parameters from invocation_params\n        this.setInvocationParameters(span, runInfo);\n        // Set metadata and tags if available\n        const metadata = runInfo.extra?.metadata || {};\n        for (const [key, value] of Object.entries(metadata)) {\n            if (value !== null && value !== undefined) {\n                span.setAttribute(`${constants.LANGSMITH_METADATA}.${key}`, String(value));\n            }\n        }\n        const tags = runInfo.tags;\n        if (tags && Array.isArray(tags)) {\n            span.setAttribute(constants.LANGSMITH_TAGS, tags.join(\", \"));\n        }\n        else if (tags) {\n            span.setAttribute(constants.LANGSMITH_TAGS, String(tags));\n        }\n        // Support additional serialized attributes, if present\n        if (\"serialized\" in runInfo && typeof runInfo.serialized === \"object\") {\n            const serialized = runInfo.serialized;\n            if (serialized.name) {\n                span.setAttribute(constants.GEN_AI_SERIALIZED_NAME, String(serialized.name));\n            }\n            if (serialized.signature) {\n                span.setAttribute(constants.GEN_AI_SERIALIZED_SIGNATURE, String(serialized.signature));\n            }\n            if (serialized.doc) {\n                span.setAttribute(constants.GEN_AI_SERIALIZED_DOC, String(serialized.doc));\n            }\n        }\n        // Set inputs/outputs if available\n        this.setIOAttributes(span, op);\n    }\n    setGenAiSystem(span, runInfo) {\n        // Default to \"langchain\" if we can't determine the system\n        let system = \"langchain\";\n        // Extract model name to determine the system\n        const modelName = this.extractModelName(runInfo);\n        if (modelName) {\n            const modelLower = modelName.toLowerCase();\n            if (modelLower.includes(\"anthropic\") || modelLower.startsWith(\"claude\")) {\n                system = \"anthropic\";\n            }\n            else if (modelLower.includes(\"bedrock\")) {\n                system = \"aws.bedrock\";\n            }\n            else if (modelLower.includes(\"azure\") &&\n                modelLower.includes(\"openai\")) {\n                system = \"az.ai.openai\";\n            }\n            else if (modelLower.includes(\"azure\") &&\n                modelLower.includes(\"inference\")) {\n                system = \"az.ai.inference\";\n            }\n            else if (modelLower.includes(\"cohere\")) {\n                system = \"cohere\";\n            }\n            else if (modelLower.includes(\"deepseek\")) {\n                system = \"deepseek\";\n            }\n            else if (modelLower.includes(\"gemini\")) {\n                system = \"gemini\";\n            }\n            else if (modelLower.includes(\"groq\")) {\n                system = \"groq\";\n            }\n            else if (modelLower.includes(\"watson\") || modelLower.includes(\"ibm\")) {\n                system = \"ibm.watsonx.ai\";\n            }\n            else if (modelLower.includes(\"mistral\")) {\n                system = \"mistral_ai\";\n            }\n            else if (modelLower.includes(\"gpt\") || modelLower.includes(\"openai\")) {\n                system = \"openai\";\n            }\n            else if (modelLower.includes(\"perplexity\") ||\n                modelLower.includes(\"sonar\")) {\n                system = \"perplexity\";\n            }\n            else if (modelLower.includes(\"vertex\")) {\n                system = \"vertex_ai\";\n            }\n            else if (modelLower.includes(\"xai\") || modelLower.includes(\"grok\")) {\n                system = \"xai\";\n            }\n        }\n        span.setAttribute(constants.GEN_AI_SYSTEM, system);\n    }\n    setInvocationParameters(span, runInfo) {\n        if (!runInfo.extra?.metadata?.invocation_params) {\n            return;\n        }\n        const invocationParams = runInfo.extra.metadata.invocation_params;\n        // Set relevant invocation parameters\n        if (invocationParams.max_tokens !== undefined) {\n            span.setAttribute(constants.GEN_AI_REQUEST_MAX_TOKENS, invocationParams.max_tokens);\n        }\n        if (invocationParams.temperature !== undefined) {\n            span.setAttribute(constants.GEN_AI_REQUEST_TEMPERATURE, invocationParams.temperature);\n        }\n        if (invocationParams.top_p !== undefined) {\n            span.setAttribute(constants.GEN_AI_REQUEST_TOP_P, invocationParams.top_p);\n        }\n        if (invocationParams.frequency_penalty !== undefined) {\n            span.setAttribute(constants.GEN_AI_REQUEST_FREQUENCY_PENALTY, invocationParams.frequency_penalty);\n        }\n        if (invocationParams.presence_penalty !== undefined) {\n            span.setAttribute(constants.GEN_AI_REQUEST_PRESENCE_PENALTY, invocationParams.presence_penalty);\n        }\n    }\n    setIOAttributes(span, op) {\n        if (op.run.inputs) {\n            try {\n                const inputs = op.run.inputs;\n                if (typeof inputs === \"object\" && inputs !== null) {\n                    if (inputs.model && Array.isArray(inputs.messages)) {\n                        span.setAttribute(constants.GEN_AI_REQUEST_MODEL, inputs.model);\n                    }\n                    // Set additional request attributes if available\n                    if (inputs.stream !== undefined) {\n                        span.setAttribute(constants.LANGSMITH_REQUEST_STREAMING, inputs.stream);\n                    }\n                    if (inputs.extra_headers) {\n                        span.setAttribute(constants.LANGSMITH_REQUEST_HEADERS, JSON.stringify(inputs.extra_headers));\n                    }\n                    if (inputs.extra_query) {\n                        span.setAttribute(constants.GEN_AI_REQUEST_EXTRA_QUERY, JSON.stringify(inputs.extra_query));\n                    }\n                    if (inputs.extra_body) {\n                        span.setAttribute(constants.GEN_AI_REQUEST_EXTRA_BODY, JSON.stringify(inputs.extra_body));\n                    }\n                }\n                span.setAttribute(constants.GENAI_PROMPT, JSON.stringify(inputs));\n            }\n            catch (e) {\n                console.debug(`Failed to process inputs for run ${op.id}`, e);\n            }\n        }\n        if (op.run.outputs) {\n            try {\n                const outputs = op.run.outputs;\n                // Extract token usage from outputs (for LLM runs)\n                const tokenUsage = this.getUnifiedRunTokens(outputs);\n                if (tokenUsage) {\n                    span.setAttribute(constants.GEN_AI_USAGE_INPUT_TOKENS, tokenUsage[0]);\n                    span.setAttribute(constants.GEN_AI_USAGE_OUTPUT_TOKENS, tokenUsage[1]);\n                    span.setAttribute(constants.GEN_AI_USAGE_TOTAL_TOKENS, tokenUsage[0] + tokenUsage[1]);\n                }\n                if (outputs && typeof outputs === \"object\") {\n                    if (outputs.model) {\n                        span.setAttribute(constants.GEN_AI_RESPONSE_MODEL, String(outputs.model));\n                    }\n                    // Extract additional response attributes\n                    if (outputs.id) {\n                        span.setAttribute(constants.GEN_AI_RESPONSE_ID, outputs.id);\n                    }\n                    if (outputs.choices && Array.isArray(outputs.choices)) {\n                        const finishReasons = outputs.choices\n                            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                            .map((choice) => choice.finish_reason)\n                            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                            .filter((reason) => reason)\n                            .map(String);\n                        if (finishReasons.length > 0) {\n                            span.setAttribute(constants.GEN_AI_RESPONSE_FINISH_REASONS, finishReasons.join(\", \"));\n                        }\n                    }\n                    if (outputs.service_tier) {\n                        span.setAttribute(constants.GEN_AI_RESPONSE_SERVICE_TIER, outputs.service_tier);\n                    }\n                    if (outputs.system_fingerprint) {\n                        span.setAttribute(constants.GEN_AI_RESPONSE_SYSTEM_FINGERPRINT, outputs.system_fingerprint);\n                    }\n                    if (outputs.usage_metadata &&\n                        typeof outputs.usage_metadata === \"object\") {\n                        const usageMetadata = outputs.usage_metadata;\n                        if (usageMetadata.input_token_details) {\n                            span.setAttribute(constants.GEN_AI_USAGE_INPUT_TOKEN_DETAILS, JSON.stringify(usageMetadata.input_token_details));\n                        }\n                        if (usageMetadata.output_token_details) {\n                            span.setAttribute(constants.GEN_AI_USAGE_OUTPUT_TOKEN_DETAILS, JSON.stringify(usageMetadata.output_token_details));\n                        }\n                    }\n                }\n                span.setAttribute(constants.GENAI_COMPLETION, JSON.stringify(outputs));\n            }\n            catch (e) {\n                console.debug(`Failed to process outputs for run ${op.id}`, e);\n            }\n        }\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    getUnifiedRunTokens(outputs) {\n        if (!outputs) {\n            return null;\n        }\n        // Search in non-generations lists\n        let tokenUsage = this.extractUnifiedRunTokens(outputs.usage_metadata);\n        if (tokenUsage) {\n            return tokenUsage;\n        }\n        // Find if direct kwarg in outputs\n        const keys = Object.keys(outputs);\n        for (const key of keys) {\n            const haystack = outputs[key];\n            if (!haystack || typeof haystack !== \"object\") {\n                continue;\n            }\n            tokenUsage = this.extractUnifiedRunTokens(haystack.usage_metadata);\n            if (tokenUsage) {\n                return tokenUsage;\n            }\n            if (haystack.lc === 1 &&\n                haystack.kwargs &&\n                typeof haystack.kwargs === \"object\") {\n                tokenUsage = this.extractUnifiedRunTokens(haystack.kwargs.usage_metadata);\n                if (tokenUsage) {\n                    return tokenUsage;\n                }\n            }\n        }\n        // Find in generations\n        const generations = outputs.generations || [];\n        if (!Array.isArray(generations)) {\n            return null;\n        }\n        const flatGenerations = Array.isArray(generations[0])\n            ? generations.flat()\n            : generations;\n        for (const generation of flatGenerations) {\n            if (typeof generation === \"object\" &&\n                generation.message &&\n                typeof generation.message === \"object\" &&\n                generation.message.kwargs &&\n                typeof generation.message.kwargs === \"object\") {\n                tokenUsage = this.extractUnifiedRunTokens(generation.message.kwargs.usage_metadata);\n                if (tokenUsage) {\n                    return tokenUsage;\n                }\n            }\n        }\n        return null;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    extractUnifiedRunTokens(outputs) {\n        if (!outputs || typeof outputs !== \"object\") {\n            return null;\n        }\n        if (typeof outputs.input_tokens !== \"number\" ||\n            typeof outputs.output_tokens !== \"number\") {\n            return null;\n        }\n        return [outputs.input_tokens, outputs.output_tokens];\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,MAAM,6BAA6B;IAC/B,KAAK;IACL,MAAM;IACN,WAAW;IACX,WAAW;IACX,QAAQ;AACZ;AACA,SAAS,iBAAiB,OAAO;IAC7B,OAAO,0BAA0B,CAAC,QAAQ,IAAI;AAClD;AACO,MAAM;IACT,aAAc;QACV,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,IAAI;QACf;IACJ;IACA,YAAY,UAAU,EAAE,cAAc,EAAE;QACpC,KAAK,MAAM,MAAM,WAAY;YACzB,IAAI;gBACA,IAAI,CAAC,GAAG,GAAG,EAAE;oBACT;gBACJ;gBACA,IAAI,GAAG,SAAS,KAAK,QAAQ;oBACzB,MAAM,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG,GAAG,EAAE,eAAe,GAAG,CAAC,GAAG,EAAE;oBACvE,IAAI,QAAQ,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE;wBAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE;oBAC1B;gBACJ,OACK;oBACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG,GAAG;gBACpC;YACJ,EACA,OAAO,GAAG;gBACN,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE;YAC1D;QACJ;IACJ;IACA,iBAAiB,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE;QACvC,MAAM,aAAa,eAAe,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,IAAI,OAAO,CAAC;QACzD,IAAI,CAAC,YAAY;YACb;QACJ;QACA,IAAI;YACA,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,SAAS;QACrD,EACA,OAAO,GAAG;YACN,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE;YACzD,OAAO;QACX;IACJ;IACA,gBAAgB,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;QAC/B,qBAAqB;QACrB,IAAI,CAAC,iBAAiB,CAAC,MAAM,SAAS;QACtC,4BAA4B;QAC5B,IAAI,QAAQ,KAAK,EAAE;YACf,KAAK,SAAS,CAAC;gBAAE,MAAM;YAAE,IAAI,eAAe;YAC5C,KAAK,eAAe,CAAC,IAAI,MAAM,QAAQ,KAAK;QAChD,OACK;YACD,KAAK,SAAS,CAAC;gBAAE,MAAM;YAAE,IAAI,YAAY;QAC7C;QACA,sCAAsC;QACtC,IAAI,QAAQ,QAAQ,EAAE;YAClB,KAAK,GAAG,CAAC,IAAI,KAAK,QAAQ,QAAQ;QACtC;QACA,OAAO;IACX;IACA,iBAAiB,EAAE,EAAE,OAAO,EAAE;QAC1B,IAAI;YACA,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;YACjC,IAAI,CAAC,MAAM;gBACP,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC;gBAC5D;YACJ;YACA,oBAAoB;YACpB,IAAI,CAAC,iBAAiB,CAAC,MAAM,SAAS;YACtC,+BAA+B;YAC/B,IAAI,QAAQ,KAAK,EAAE;gBACf,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAE,IAAI,eAAe;gBAC5C,KAAK,eAAe,CAAC,IAAI,MAAM,QAAQ,KAAK;YAChD,OACK;gBACD,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAE,IAAI,YAAY;YAC7C;YACA,sCAAsC;YACtC,MAAM,UAAU,QAAQ,QAAQ;YAChC,IAAI,SAAS;gBACT,KAAK,GAAG,CAAC,IAAI,KAAK;gBAClB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE;YAC3B;QACJ,EACA,OAAO,GAAG;YACN,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE;QAC7D;IACJ;IACA,iBAAiB,OAAO,EAAE;QACtB,sCAAsC;QACtC,IAAI,QAAQ,KAAK,EAAE,UAAU;YACzB,MAAM,WAAW,QAAQ,KAAK,CAAC,QAAQ;YACvC,4CAA4C;YAC5C,IAAI,SAAS,aAAa,EAAE;gBACxB,OAAO,SAAS,aAAa;YACjC;YACA,8CAA8C;YAC9C,IAAI,SAAS,iBAAiB,EAAE;gBAC5B,MAAM,mBAAmB,SAAS,iBAAiB;gBACnD,IAAI,iBAAiB,KAAK,EAAE;oBACxB,OAAO,iBAAiB,KAAK;gBACjC,OACK,IAAI,iBAAiB,UAAU,EAAE;oBAClC,OAAO,iBAAiB,UAAU;gBACtC;YACJ;QACJ;QACA;IACJ;IACA,kBAAkB,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;QACjC,IAAI,cAAc,WAAW,QAAQ,QAAQ,EAAE;YAC3C,KAAK,YAAY,CAAC,wKAAA,CAAA,qBAA4B,EAAE,QAAQ,QAAQ;YAChE,8DAA8D;YAC9D,MAAM,gBAAgB,iBAAiB,QAAQ,QAAQ,IAAI;YAC3D,KAAK,YAAY,CAAC,wKAAA,CAAA,wBAA+B,EAAE;QACvD;QACA,IAAI,UAAU,WAAW,QAAQ,IAAI,EAAE;YACnC,KAAK,YAAY,CAAC,wKAAA,CAAA,iBAAwB,EAAE,QAAQ,IAAI;QAC5D;QACA,IAAI,gBAAgB,WAAW,QAAQ,UAAU,EAAE;YAC/C,KAAK,YAAY,CAAC,wKAAA,CAAA,uBAA8B,EAAE,QAAQ,UAAU;QACxE;QACA,IAAI,kBAAkB,WAAW,QAAQ,YAAY,EAAE;YACnD,KAAK,YAAY,CAAC,wKAAA,CAAA,yBAAgC,EAAE,QAAQ,YAAY;QAC5E;QACA,oBAAoB;QACpB,IAAI,CAAC,cAAc,CAAC,MAAM;QAC1B,8BAA8B;QAC9B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,IAAI,WAAW;YACX,KAAK,YAAY,CAAC,wKAAA,CAAA,uBAA8B,EAAE;QACtD;QACA,8BAA8B;QAC9B,IAAI,mBAAmB,WACnB,OAAO,QAAQ,aAAa,KAAK,UAAU;YAC3C,KAAK,YAAY,CAAC,wKAAA,CAAA,4BAAmC,EAAE,QAAQ,aAAa;QAChF;QACA,IAAI,uBAAuB,WACvB,OAAO,QAAQ,iBAAiB,KAAK,UAAU;YAC/C,KAAK,YAAY,CAAC,wKAAA,CAAA,6BAAoC,EAAE,QAAQ,iBAAiB;QACrF;QACA,IAAI,kBAAkB,WAAW,OAAO,QAAQ,YAAY,KAAK,UAAU;YACvE,KAAK,YAAY,CAAC,wKAAA,CAAA,4BAAmC,EAAE,QAAQ,YAAY;QAC/E;QACA,8CAA8C;QAC9C,IAAI,CAAC,uBAAuB,CAAC,MAAM;QACnC,qCAAqC;QACrC,MAAM,WAAW,QAAQ,KAAK,EAAE,YAAY,CAAC;QAC7C,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,UAAW;YACjD,IAAI,UAAU,QAAQ,UAAU,WAAW;gBACvC,KAAK,YAAY,CAAC,GAAG,wKAAA,CAAA,qBAA4B,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO;YACvE;QACJ;QACA,MAAM,OAAO,QAAQ,IAAI;QACzB,IAAI,QAAQ,MAAM,OAAO,CAAC,OAAO;YAC7B,KAAK,YAAY,CAAC,wKAAA,CAAA,iBAAwB,EAAE,KAAK,IAAI,CAAC;QAC1D,OACK,IAAI,MAAM;YACX,KAAK,YAAY,CAAC,wKAAA,CAAA,iBAAwB,EAAE,OAAO;QACvD;QACA,uDAAuD;QACvD,IAAI,gBAAgB,WAAW,OAAO,QAAQ,UAAU,KAAK,UAAU;YACnE,MAAM,aAAa,QAAQ,UAAU;YACrC,IAAI,WAAW,IAAI,EAAE;gBACjB,KAAK,YAAY,CAAC,wKAAA,CAAA,yBAAgC,EAAE,OAAO,WAAW,IAAI;YAC9E;YACA,IAAI,WAAW,SAAS,EAAE;gBACtB,KAAK,YAAY,CAAC,wKAAA,CAAA,8BAAqC,EAAE,OAAO,WAAW,SAAS;YACxF;YACA,IAAI,WAAW,GAAG,EAAE;gBAChB,KAAK,YAAY,CAAC,wKAAA,CAAA,wBAA+B,EAAE,OAAO,WAAW,GAAG;YAC5E;QACJ;QACA,kCAAkC;QAClC,IAAI,CAAC,eAAe,CAAC,MAAM;IAC/B;IACA,eAAe,IAAI,EAAE,OAAO,EAAE;QAC1B,0DAA0D;QAC1D,IAAI,SAAS;QACb,6CAA6C;QAC7C,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,IAAI,WAAW;YACX,MAAM,aAAa,UAAU,WAAW;YACxC,IAAI,WAAW,QAAQ,CAAC,gBAAgB,WAAW,UAAU,CAAC,WAAW;gBACrE,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,YAAY;gBACrC,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,YACzB,WAAW,QAAQ,CAAC,WAAW;gBAC/B,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,YACzB,WAAW,QAAQ,CAAC,cAAc;gBAClC,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,WAAW;gBACpC,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,aAAa;gBACtC,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,WAAW;gBACpC,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,SAAS;gBAClC,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,aAAa,WAAW,QAAQ,CAAC,QAAQ;gBAClE,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,YAAY;gBACrC,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,UAAU,WAAW,QAAQ,CAAC,WAAW;gBAClE,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,iBACzB,WAAW,QAAQ,CAAC,UAAU;gBAC9B,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,WAAW;gBACpC,SAAS;YACb,OACK,IAAI,WAAW,QAAQ,CAAC,UAAU,WAAW,QAAQ,CAAC,SAAS;gBAChE,SAAS;YACb;QACJ;QACA,KAAK,YAAY,CAAC,wKAAA,CAAA,gBAAuB,EAAE;IAC/C;IACA,wBAAwB,IAAI,EAAE,OAAO,EAAE;QACnC,IAAI,CAAC,QAAQ,KAAK,EAAE,UAAU,mBAAmB;YAC7C;QACJ;QACA,MAAM,mBAAmB,QAAQ,KAAK,CAAC,QAAQ,CAAC,iBAAiB;QACjE,qCAAqC;QACrC,IAAI,iBAAiB,UAAU,KAAK,WAAW;YAC3C,KAAK,YAAY,CAAC,wKAAA,CAAA,4BAAmC,EAAE,iBAAiB,UAAU;QACtF;QACA,IAAI,iBAAiB,WAAW,KAAK,WAAW;YAC5C,KAAK,YAAY,CAAC,wKAAA,CAAA,6BAAoC,EAAE,iBAAiB,WAAW;QACxF;QACA,IAAI,iBAAiB,KAAK,KAAK,WAAW;YACtC,KAAK,YAAY,CAAC,wKAAA,CAAA,uBAA8B,EAAE,iBAAiB,KAAK;QAC5E;QACA,IAAI,iBAAiB,iBAAiB,KAAK,WAAW;YAClD,KAAK,YAAY,CAAC,wKAAA,CAAA,mCAA0C,EAAE,iBAAiB,iBAAiB;QACpG;QACA,IAAI,iBAAiB,gBAAgB,KAAK,WAAW;YACjD,KAAK,YAAY,CAAC,wKAAA,CAAA,kCAAyC,EAAE,iBAAiB,gBAAgB;QAClG;IACJ;IACA,gBAAgB,IAAI,EAAE,EAAE,EAAE;QACtB,IAAI,GAAG,GAAG,CAAC,MAAM,EAAE;YACf,IAAI;gBACA,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM;gBAC5B,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;oBAC/C,IAAI,OAAO,KAAK,IAAI,MAAM,OAAO,CAAC,OAAO,QAAQ,GAAG;wBAChD,KAAK,YAAY,CAAC,wKAAA,CAAA,uBAA8B,EAAE,OAAO,KAAK;oBAClE;oBACA,iDAAiD;oBACjD,IAAI,OAAO,MAAM,KAAK,WAAW;wBAC7B,KAAK,YAAY,CAAC,wKAAA,CAAA,8BAAqC,EAAE,OAAO,MAAM;oBAC1E;oBACA,IAAI,OAAO,aAAa,EAAE;wBACtB,KAAK,YAAY,CAAC,wKAAA,CAAA,4BAAmC,EAAE,KAAK,SAAS,CAAC,OAAO,aAAa;oBAC9F;oBACA,IAAI,OAAO,WAAW,EAAE;wBACpB,KAAK,YAAY,CAAC,wKAAA,CAAA,6BAAoC,EAAE,KAAK,SAAS,CAAC,OAAO,WAAW;oBAC7F;oBACA,IAAI,OAAO,UAAU,EAAE;wBACnB,KAAK,YAAY,CAAC,wKAAA,CAAA,4BAAmC,EAAE,KAAK,SAAS,CAAC,OAAO,UAAU;oBAC3F;gBACJ;gBACA,KAAK,YAAY,CAAC,wKAAA,CAAA,eAAsB,EAAE,KAAK,SAAS,CAAC;YAC7D,EACA,OAAO,GAAG;gBACN,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,GAAG,EAAE,EAAE,EAAE;YAC/D;QACJ;QACA,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE;YAChB,IAAI;gBACA,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO;gBAC9B,kDAAkD;gBAClD,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;gBAC5C,IAAI,YAAY;oBACZ,KAAK,YAAY,CAAC,wKAAA,CAAA,4BAAmC,EAAE,UAAU,CAAC,EAAE;oBACpE,KAAK,YAAY,CAAC,wKAAA,CAAA,6BAAoC,EAAE,UAAU,CAAC,EAAE;oBACrE,KAAK,YAAY,CAAC,wKAAA,CAAA,4BAAmC,EAAE,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;gBACxF;gBACA,IAAI,WAAW,OAAO,YAAY,UAAU;oBACxC,IAAI,QAAQ,KAAK,EAAE;wBACf,KAAK,YAAY,CAAC,wKAAA,CAAA,wBAA+B,EAAE,OAAO,QAAQ,KAAK;oBAC3E;oBACA,yCAAyC;oBACzC,IAAI,QAAQ,EAAE,EAAE;wBACZ,KAAK,YAAY,CAAC,wKAAA,CAAA,qBAA4B,EAAE,QAAQ,EAAE;oBAC9D;oBACA,IAAI,QAAQ,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,GAAG;wBACnD,MAAM,gBAAgB,QAAQ,OAAO,AACjC,8DAA8D;yBAC7D,GAAG,CAAC,CAAC,SAAW,OAAO,aAAa,CACrC,8DAA8D;yBAC7D,MAAM,CAAC,CAAC,SAAW,QACnB,GAAG,CAAC;wBACT,IAAI,cAAc,MAAM,GAAG,GAAG;4BAC1B,KAAK,YAAY,CAAC,wKAAA,CAAA,iCAAwC,EAAE,cAAc,IAAI,CAAC;wBACnF;oBACJ;oBACA,IAAI,QAAQ,YAAY,EAAE;wBACtB,KAAK,YAAY,CAAC,wKAAA,CAAA,+BAAsC,EAAE,QAAQ,YAAY;oBAClF;oBACA,IAAI,QAAQ,kBAAkB,EAAE;wBAC5B,KAAK,YAAY,CAAC,wKAAA,CAAA,qCAA4C,EAAE,QAAQ,kBAAkB;oBAC9F;oBACA,IAAI,QAAQ,cAAc,IACtB,OAAO,QAAQ,cAAc,KAAK,UAAU;wBAC5C,MAAM,gBAAgB,QAAQ,cAAc;wBAC5C,IAAI,cAAc,mBAAmB,EAAE;4BACnC,KAAK,YAAY,CAAC,wKAAA,CAAA,mCAA0C,EAAE,KAAK,SAAS,CAAC,cAAc,mBAAmB;wBAClH;wBACA,IAAI,cAAc,oBAAoB,EAAE;4BACpC,KAAK,YAAY,CAAC,wKAAA,CAAA,oCAA2C,EAAE,KAAK,SAAS,CAAC,cAAc,oBAAoB;wBACpH;oBACJ;gBACJ;gBACA,KAAK,YAAY,CAAC,wKAAA,CAAA,mBAA0B,EAAE,KAAK,SAAS,CAAC;YACjE,EACA,OAAO,GAAG;gBACN,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,EAAE,EAAE,EAAE;YAChE;QACJ;IACJ;IACA,8DAA8D;IAC9D,oBAAoB,OAAO,EAAE;QACzB,IAAI,CAAC,SAAS;YACV,OAAO;QACX;QACA,kCAAkC;QAClC,IAAI,aAAa,IAAI,CAAC,uBAAuB,CAAC,QAAQ,cAAc;QACpE,IAAI,YAAY;YACZ,OAAO;QACX;QACA,kCAAkC;QAClC,MAAM,OAAO,OAAO,IAAI,CAAC;QACzB,KAAK,MAAM,OAAO,KAAM;YACpB,MAAM,WAAW,OAAO,CAAC,IAAI;YAC7B,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;gBAC3C;YACJ;YACA,aAAa,IAAI,CAAC,uBAAuB,CAAC,SAAS,cAAc;YACjE,IAAI,YAAY;gBACZ,OAAO;YACX;YACA,IAAI,SAAS,EAAE,KAAK,KAChB,SAAS,MAAM,IACf,OAAO,SAAS,MAAM,KAAK,UAAU;gBACrC,aAAa,IAAI,CAAC,uBAAuB,CAAC,SAAS,MAAM,CAAC,cAAc;gBACxE,IAAI,YAAY;oBACZ,OAAO;gBACX;YACJ;QACJ;QACA,sBAAsB;QACtB,MAAM,cAAc,QAAQ,WAAW,IAAI,EAAE;QAC7C,IAAI,CAAC,MAAM,OAAO,CAAC,cAAc;YAC7B,OAAO;QACX;QACA,MAAM,kBAAkB,MAAM,OAAO,CAAC,WAAW,CAAC,EAAE,IAC9C,YAAY,IAAI,KAChB;QACN,KAAK,MAAM,cAAc,gBAAiB;YACtC,IAAI,OAAO,eAAe,YACtB,WAAW,OAAO,IAClB,OAAO,WAAW,OAAO,KAAK,YAC9B,WAAW,OAAO,CAAC,MAAM,IACzB,OAAO,WAAW,OAAO,CAAC,MAAM,KAAK,UAAU;gBAC/C,aAAa,IAAI,CAAC,uBAAuB,CAAC,WAAW,OAAO,CAAC,MAAM,CAAC,cAAc;gBAClF,IAAI,YAAY;oBACZ,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,8DAA8D;IAC9D,wBAAwB,OAAO,EAAE;QAC7B,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU;YACzC,OAAO;QACX;QACA,IAAI,OAAO,QAAQ,YAAY,KAAK,YAChC,OAAO,QAAQ,aAAa,KAAK,UAAU;YAC3C,OAAO;QACX;QACA,OAAO;YAAC,QAAQ,YAAY;YAAE,QAAQ,aAAa;SAAC;IACxD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/utils/async_caller.js"], "sourcesContent": ["import pRetry from \"p-retry\";\nimport <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from \"p-queue\";\nimport { _getFetchImplementation } from \"../singletons/fetch.js\";\nconst STATUS_NO_RETRY = [\n    400, // Bad Request\n    401, // Unauthorized\n    403, // Forbidden\n    404, // Not Found\n    405, // Method Not Allowed\n    406, // Not Acceptable\n    407, // Proxy Authentication Required\n    408, // Request Timeout\n];\nconst STATUS_IGNORE = [\n    409, // Conflict\n];\n/**\n * A class that can be used to make async calls with concurrency and retry logic.\n *\n * This is useful for making calls to any kind of \"expensive\" external resource,\n * be it because it's rate-limited, subject to network issues, etc.\n *\n * Concurrent calls are limited by the `maxConcurrency` parameter, which defaults\n * to `Infinity`. This means that by default, all calls will be made in parallel.\n *\n * Retries are limited by the `maxRetries` parameter, which defaults to 6. This\n * means that by default, each call will be retried up to 6 times, with an\n * exponential backoff between each attempt.\n */\nexport class AsyncCaller {\n    constructor(params) {\n        Object.defineProperty(this, \"maxConcurrency\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"maxRetries\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"queue\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"onFailedResponseHook\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"debug\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.maxConcurrency = params.maxConcurrency ?? Infinity;\n        this.maxRetries = params.maxRetries ?? 6;\n        this.debug = params.debug;\n        if (\"default\" in PQueueMod) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.queue = new PQueueMod.default({\n                concurrency: this.maxConcurrency,\n            });\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.queue = new PQueueMod({ concurrency: this.maxConcurrency });\n        }\n        this.onFailedResponseHook = params?.onFailedResponseHook;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    call(callable, ...args) {\n        const onFailedResponseHook = this.onFailedResponseHook;\n        return this.queue.add(() => pRetry(() => callable(...args).catch((error) => {\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (error instanceof Error) {\n                throw error;\n            }\n            else {\n                throw new Error(error);\n            }\n        }), {\n            async onFailedAttempt(error) {\n                if (error.message.startsWith(\"Cancel\") ||\n                    error.message.startsWith(\"TimeoutError\") ||\n                    error.message.startsWith(\"AbortError\")) {\n                    throw error;\n                }\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                if (error?.code === \"ECONNABORTED\") {\n                    throw error;\n                }\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                const response = error?.response;\n                const status = response?.status;\n                if (status) {\n                    if (STATUS_NO_RETRY.includes(+status)) {\n                        throw error;\n                    }\n                    else if (STATUS_IGNORE.includes(+status)) {\n                        return;\n                    }\n                    if (onFailedResponseHook) {\n                        await onFailedResponseHook(response);\n                    }\n                }\n            },\n            // If needed we can change some of the defaults here,\n            // but they're quite sensible.\n            retries: this.maxRetries,\n            randomize: true,\n        }), { throwOnTimeout: true });\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    callWithOptions(options, callable, ...args) {\n        // Note this doesn't cancel the underlying request,\n        // when available prefer to use the signal option of the underlying call\n        if (options.signal) {\n            return Promise.race([\n                this.call(callable, ...args),\n                new Promise((_, reject) => {\n                    options.signal?.addEventListener(\"abort\", () => {\n                        reject(new Error(\"AbortError\"));\n                    });\n                }),\n            ]);\n        }\n        return this.call(callable, ...args);\n    }\n    fetch(...args) {\n        return this.call(() => _getFetchImplementation(this.debug)(...args).then((res) => res.ok ? res : Promise.reject(res)));\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,MAAM,kBAAkB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,MAAM,gBAAgB;IAClB;CACH;AAcM,MAAM;IACT,YAAY,MAAM,CAAE;QAChB,OAAO,cAAc,CAAC,IAAI,EAAE,kBAAkB;YAC1C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,wBAAwB;YAChD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,IAAI,CAAC,cAAc,GAAG,OAAO,cAAc,IAAI;QAC/C,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU,IAAI;QACvC,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK;QACzB,IAAI,aAAa,6IAAA,CAAA,UAAS,EAAE;YACxB,8DAA8D;YAC9D,IAAI,CAAC,KAAK,GAAG,IAAI,6IAAA,CAAA,UAAS,CAAC,OAAO,CAAC;gBAC/B,aAAa,IAAI,CAAC,cAAc;YACpC;QACJ,OACK;YACD,8DAA8D;YAC9D,IAAI,CAAC,KAAK,GAAG,IAAI,6IAAA,CAAA,UAAS,CAAC;gBAAE,aAAa,IAAI,CAAC,cAAc;YAAC;QAClE;QACA,IAAI,CAAC,oBAAoB,GAAG,QAAQ;IACxC;IACA,8DAA8D;IAC9D,KAAK,QAAQ,EAAE,GAAG,IAAI,EAAE;QACpB,MAAM,uBAAuB,IAAI,CAAC,oBAAoB;QACtD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAM,CAAA,GAAA,qIAAA,CAAA,UAAM,AAAD,EAAE,IAAM,YAAY,MAAM,KAAK,CAAC,CAAC;oBAC9D,uDAAuD;oBACvD,IAAI,iBAAiB,OAAO;wBACxB,MAAM;oBACV,OACK;wBACD,MAAM,IAAI,MAAM;oBACpB;gBACJ,IAAI;gBACA,MAAM,iBAAgB,KAAK;oBACvB,IAAI,MAAM,OAAO,CAAC,UAAU,CAAC,aACzB,MAAM,OAAO,CAAC,UAAU,CAAC,mBACzB,MAAM,OAAO,CAAC,UAAU,CAAC,eAAe;wBACxC,MAAM;oBACV;oBACA,8DAA8D;oBAC9D,IAAI,OAAO,SAAS,gBAAgB;wBAChC,MAAM;oBACV;oBACA,8DAA8D;oBAC9D,MAAM,WAAW,OAAO;oBACxB,MAAM,SAAS,UAAU;oBACzB,IAAI,QAAQ;wBACR,IAAI,gBAAgB,QAAQ,CAAC,CAAC,SAAS;4BACnC,MAAM;wBACV,OACK,IAAI,cAAc,QAAQ,CAAC,CAAC,SAAS;4BACtC;wBACJ;wBACA,IAAI,sBAAsB;4BACtB,MAAM,qBAAqB;wBAC/B;oBACJ;gBACJ;gBACA,qDAAqD;gBACrD,8BAA8B;gBAC9B,SAAS,IAAI,CAAC,UAAU;gBACxB,WAAW;YACf,IAAI;YAAE,gBAAgB;QAAK;IAC/B;IACA,8DAA8D;IAC9D,gBAAgB,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE;QACxC,mDAAmD;QACnD,wEAAwE;QACxE,IAAI,QAAQ,MAAM,EAAE;YAChB,OAAO,QAAQ,IAAI,CAAC;gBAChB,IAAI,CAAC,IAAI,CAAC,aAAa;gBACvB,IAAI,QAAQ,CAAC,GAAG;oBACZ,QAAQ,MAAM,EAAE,iBAAiB,SAAS;wBACtC,OAAO,IAAI,MAAM;oBACrB;gBACJ;aACH;QACL;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa;IAClC;IACA,MAAM,GAAG,IAAI,EAAE;QACX,OAAO,IAAI,CAAC,IAAI,CAAC,IAAM,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,GAAG,MAAM,QAAQ,MAAM,CAAC;IACpH;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/utils/messages.js"], "sourcesContent": ["export function isLangChainMessage(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nmessage) {\n    return typeof message?._getType === \"function\";\n}\nexport function convertLangChainMessageToExample(message) {\n    const converted = {\n        type: message._getType(),\n        data: { content: message.content },\n    };\n    // Check for presence of keys in additional_kwargs\n    if (message?.additional_kwargs &&\n        Object.keys(message.additional_kwargs).length > 0) {\n        converted.data.additional_kwargs = { ...message.additional_kwargs };\n    }\n    return converted;\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,mBAChB,8DAA8D;AAC9D,OAAO;IACH,OAAO,OAAO,SAAS,aAAa;AACxC;AACO,SAAS,iCAAiC,OAAO;IACpD,MAAM,YAAY;QACd,MAAM,QAAQ,QAAQ;QACtB,MAAM;YAAE,SAAS,QAAQ,OAAO;QAAC;IACrC;IACA,kDAAkD;IAClD,IAAI,SAAS,qBACT,OAAO,IAAI,CAAC,QAAQ,iBAAiB,EAAE,MAAM,GAAG,GAAG;QACnD,UAAU,IAAI,CAAC,iBAAiB,GAAG;YAAE,GAAG,QAAQ,iBAAiB;QAAC;IACtE;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/utils/_uuid.js"], "sourcesContent": ["// Relaxed UUID validation regex (allows any valid UUID format including nil UUIDs)\nconst UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;\nexport function assertUuid(str, which) {\n    // Use relaxed regex validation instead of strict uuid.validate()\n    // This allows edge cases like nil UUIDs or test UUIDs that might not pass strict validation\n    if (!UUID_REGEX.test(str)) {\n        const msg = which !== undefined\n            ? `Invalid UUID for ${which}: ${str}`\n            : `Invalid UUID: ${str}`;\n        throw new Error(msg);\n    }\n    return str;\n}\n"], "names": [], "mappings": "AAAA,mFAAmF;;;;AACnF,MAAM,aAAa;AACZ,SAAS,WAAW,GAAG,EAAE,KAAK;IACjC,iEAAiE;IACjE,4FAA4F;IAC5F,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM;QACvB,MAAM,MAAM,UAAU,YAChB,CAAC,iBAAiB,EAAE,MAAM,EAAE,EAAE,KAAK,GACnC,CAAC,cAAc,EAAE,KAAK;QAC5B,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/utils/warn.js"], "sourcesContent": ["const warnedMessages = {};\nexport function warnOnce(message) {\n    if (!warnedMessages[message]) {\n        console.warn(message);\n        warnedMessages[message] = true;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,iBAAiB,CAAC;AACjB,SAAS,SAAS,OAAO;IAC5B,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;QAC1B,QAAQ,IAAI,CAAC;QACb,cAAc,CAAC,QAAQ,GAAG;IAC9B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/utils/prompts.js"], "sourcesContent": ["import { parse as parseVersion } from \"semver\";\nexport function isVersionGreaterOrEqual(current_version, target_version) {\n    const current = parseVersion(current_version);\n    const target = parseVersion(target_version);\n    if (!current || !target) {\n        throw new Error(\"Invalid version format.\");\n    }\n    return current.compare(target) >= 0;\n}\nexport function parsePromptIdentifier(identifier) {\n    if (!identifier ||\n        identifier.split(\"/\").length > 2 ||\n        identifier.startsWith(\"/\") ||\n        identifier.endsWith(\"/\") ||\n        identifier.split(\":\").length > 2) {\n        throw new Error(`Invalid identifier format: ${identifier}`);\n    }\n    const [ownerNamePart, commitPart] = identifier.split(\":\");\n    const commit = commitPart || \"latest\";\n    if (ownerNamePart.includes(\"/\")) {\n        const [owner, name] = ownerNamePart.split(\"/\", 2);\n        if (!owner || !name) {\n            throw new Error(`Invalid identifier format: ${identifier}`);\n        }\n        return [owner, name, commit];\n    }\n    else {\n        if (!ownerNamePart) {\n            throw new Error(`Invalid identifier format: ${identifier}`);\n        }\n        return [\"-\", ownerNamePart, commit];\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,SAAS,wBAAwB,eAAe,EAAE,cAAc;IACnE,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,QAAY,AAAD,EAAE;IAC7B,MAAM,SAAS,CAAA,GAAA,iIAAA,CAAA,QAAY,AAAD,EAAE;IAC5B,IAAI,CAAC,WAAW,CAAC,QAAQ;QACrB,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,QAAQ,OAAO,CAAC,WAAW;AACtC;AACO,SAAS,sBAAsB,UAAU;IAC5C,IAAI,CAAC,cACD,WAAW,KAAK,CAAC,KAAK,MAAM,GAAG,KAC/B,WAAW,UAAU,CAAC,QACtB,WAAW,QAAQ,CAAC,QACpB,WAAW,KAAK,CAAC,KAAK,MAAM,GAAG,GAAG;QAClC,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,YAAY;IAC9D;IACA,MAAM,CAAC,eAAe,WAAW,GAAG,WAAW,KAAK,CAAC;IACrD,MAAM,SAAS,cAAc;IAC7B,IAAI,cAAc,QAAQ,CAAC,MAAM;QAC7B,MAAM,CAAC,OAAO,KAAK,GAAG,cAAc,KAAK,CAAC,KAAK;QAC/C,IAAI,CAAC,SAAS,CAAC,MAAM;YACjB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,YAAY;QAC9D;QACA,OAAO;YAAC;YAAO;YAAM;SAAO;IAChC,OACK;QACD,IAAI,CAAC,eAAe;YAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,YAAY;QAC9D;QACA,OAAO;YAAC;YAAK;YAAe;SAAO;IACvC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1198, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/utils/error.js"], "sourcesContent": ["function getErrorStackTrace(e) {\n    if (typeof e !== \"object\" || e == null)\n        return undefined;\n    if (!(\"stack\" in e) || typeof e.stack !== \"string\")\n        return undefined;\n    let stack = e.stack;\n    const prevLine = `${e}`;\n    if (stack.startsWith(prevLine)) {\n        stack = stack.slice(prevLine.length);\n    }\n    if (stack.startsWith(\"\\n\")) {\n        stack = stack.slice(1);\n    }\n    return stack;\n}\nexport function printErrorStackTrace(e) {\n    const stack = getErrorStackTrace(e);\n    if (stack == null)\n        return;\n    console.error(stack);\n}\n/**\n * LangSmithConflictError\n *\n * Represents an error that occurs when there's a conflict during an operation,\n * typically corresponding to HTTP 409 status code responses.\n *\n * This error is thrown when an attempt to create or modify a resource conflicts\n * with the current state of the resource on the server. Common scenarios include:\n * - Attempting to create a resource that already exists\n * - Trying to update a resource that has been modified by another process\n * - Violating a uniqueness constraint in the data\n *\n * @extends Error\n *\n * @example\n * try {\n *   await createProject(\"existingProject\");\n * } catch (error) {\n *   if (error instanceof ConflictError) {\n *     console.log(\"A conflict occurred:\", error.message);\n *     // Handle the conflict, e.g., by suggesting a different project name\n *   } else {\n *     // Handle other types of errors\n *   }\n * }\n *\n * @property {string} name - Always set to 'ConflictError' for easy identification\n * @property {string} message - Detailed error message including server response\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/409\n */\nexport class LangSmithConflictError extends Error {\n    constructor(message) {\n        super(message);\n        Object.defineProperty(this, \"status\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.name = \"LangSmithConflictError\";\n        this.status = 409;\n    }\n}\n/**\n * Throws an appropriate error based on the response status and body.\n *\n * @param response - The fetch Response object\n * @param context - Additional context to include in the error message (e.g., operation being performed)\n * @throws {LangSmithConflictError} When the response status is 409\n * @throws {Error} For all other non-ok responses\n */\nexport async function raiseForStatus(response, context, consume) {\n    // consume the response body to release the connection\n    // https://undici.nodejs.org/#/?id=garbage-collection\n    let errorBody;\n    if (response.ok) {\n        if (consume) {\n            errorBody = await response.text();\n        }\n        return;\n    }\n    errorBody = await response.text();\n    const fullMessage = `Failed to ${context}. Received status [${response.status}]: ${response.statusText}. Server response: ${errorBody}`;\n    if (response.status === 409) {\n        throw new LangSmithConflictError(fullMessage);\n    }\n    const err = new Error(fullMessage);\n    err.status = response.status;\n    throw err;\n}\nconst ERR_CONFLICTING_ENDPOINTS = \"ERR_CONFLICTING_ENDPOINTS\";\nexport class ConflictingEndpointsError extends Error {\n    constructor() {\n        super(\"You cannot provide both LANGSMITH_ENDPOINT / LANGCHAIN_ENDPOINT \" +\n            \"and LANGSMITH_RUNS_ENDPOINTS.\");\n        Object.defineProperty(this, \"code\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: ERR_CONFLICTING_ENDPOINTS\n        });\n        this.name = \"ConflictingEndpointsError\"; // helpful in logs\n    }\n}\nexport function isConflictingEndpointsError(err) {\n    return (typeof err === \"object\" &&\n        err !== null &&\n        err.code === ERR_CONFLICTING_ENDPOINTS);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,SAAS,mBAAmB,CAAC;IACzB,IAAI,OAAO,MAAM,YAAY,KAAK,MAC9B,OAAO;IACX,IAAI,CAAC,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE,KAAK,KAAK,UACtC,OAAO;IACX,IAAI,QAAQ,EAAE,KAAK;IACnB,MAAM,WAAW,GAAG,GAAG;IACvB,IAAI,MAAM,UAAU,CAAC,WAAW;QAC5B,QAAQ,MAAM,KAAK,CAAC,SAAS,MAAM;IACvC;IACA,IAAI,MAAM,UAAU,CAAC,OAAO;QACxB,QAAQ,MAAM,KAAK,CAAC;IACxB;IACA,OAAO;AACX;AACO,SAAS,qBAAqB,CAAC;IAClC,MAAM,QAAQ,mBAAmB;IACjC,IAAI,SAAS,MACT;IACJ,QAAQ,KAAK,CAAC;AAClB;AAgCO,MAAM,+BAA+B;IACxC,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC;QACN,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAClB;AACJ;AASO,eAAe,eAAe,QAAQ,EAAE,OAAO,EAAE,OAAO;IAC3D,sDAAsD;IACtD,qDAAqD;IACrD,IAAI;IACJ,IAAI,SAAS,EAAE,EAAE;QACb,IAAI,SAAS;YACT,YAAY,MAAM,SAAS,IAAI;QACnC;QACA;IACJ;IACA,YAAY,MAAM,SAAS,IAAI;IAC/B,MAAM,cAAc,CAAC,UAAU,EAAE,QAAQ,mBAAmB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,SAAS,UAAU,CAAC,mBAAmB,EAAE,WAAW;IACvI,IAAI,SAAS,MAAM,KAAK,KAAK;QACzB,MAAM,IAAI,uBAAuB;IACrC;IACA,MAAM,MAAM,IAAI,MAAM;IACtB,IAAI,MAAM,GAAG,SAAS,MAAM;IAC5B,MAAM;AACV;AACA,MAAM,4BAA4B;AAC3B,MAAM,kCAAkC;IAC3C,aAAc;QACV,KAAK,CAAC,qEACF;QACJ,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,IAAI,CAAC,IAAI,GAAG,6BAA6B,kBAAkB;IAC/D;AACJ;AACO,SAAS,4BAA4B,GAAG;IAC3C,OAAQ,OAAO,QAAQ,YACnB,QAAQ,QACR,IAAI,IAAI,KAAK;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/utils/fast-safe-stringify/index.js"], "sourcesContent": ["/* eslint-disable */\n// @ts-nocheck\nimport { getLangSmithEnvironmentVariable } from \"../../utils/env.js\";\nvar LIMIT_REPLACE_NODE = \"[...]\";\nvar CIRCULAR_REPLACE_NODE = { result: \"[Circular]\" };\nvar arr = [];\nvar replacerStack = [];\nconst encoder = new TextEncoder();\nfunction defaultOptions() {\n    return {\n        depthLimit: Number.MAX_SAFE_INTEGER,\n        edgesLimit: Number.MAX_SAFE_INTEGER,\n    };\n}\nfunction encodeString(str) {\n    return encoder.encode(str);\n}\n// Shared function to handle well-known types\nfunction serializeWellKnownTypes(val) {\n    if (val && typeof val === \"object\" && val !== null) {\n        if (val instanceof Map) {\n            return Object.fromEntries(val);\n        }\n        else if (val instanceof Set) {\n            return Array.from(val);\n        }\n        else if (val instanceof Date) {\n            return val.toISOString();\n        }\n        else if (val instanceof RegExp) {\n            return val.toString();\n        }\n        else if (val instanceof Error) {\n            return {\n                name: val.name,\n                message: val.message,\n            };\n        }\n    }\n    else if (typeof val === \"bigint\") {\n        return val.toString();\n    }\n    return val;\n}\n// Default replacer function to handle well-known types\nfunction createDefaultReplacer(userReplacer) {\n    return function (key, val) {\n        // Apply user replacer first if provided\n        if (userReplacer) {\n            const userResult = userReplacer.call(this, key, val);\n            // If user replacer returned undefined, fall back to our serialization\n            if (userResult !== undefined) {\n                return userResult;\n            }\n        }\n        // Fall back to our well-known type handling\n        return serializeWellKnownTypes(val);\n    };\n}\n// Regular stringify\nexport function serialize(obj, errorContext, replacer, spacer, options) {\n    try {\n        const str = JSON.stringify(obj, createDefaultReplacer(replacer), spacer);\n        return encodeString(str);\n    }\n    catch (e) {\n        // Fall back to more complex stringify if circular reference\n        if (!e.message?.includes(\"Converting circular structure to JSON\")) {\n            console.warn(`[WARNING]: LangSmith received unserializable value.${errorContext ? `\\nContext: ${errorContext}` : \"\"}`);\n            return encodeString(\"[Unserializable]\");\n        }\n        getLangSmithEnvironmentVariable(\"SUPPRESS_CIRCULAR_JSON_WARNINGS\") !==\n            \"true\" &&\n            console.warn(`[WARNING]: LangSmith received circular JSON. This will decrease tracer performance. ${errorContext ? `\\nContext: ${errorContext}` : \"\"}`);\n        if (typeof options === \"undefined\") {\n            options = defaultOptions();\n        }\n        decirc(obj, \"\", 0, [], undefined, 0, options);\n        let res;\n        try {\n            if (replacerStack.length === 0) {\n                res = JSON.stringify(obj, replacer, spacer);\n            }\n            else {\n                res = JSON.stringify(obj, replaceGetterValues(replacer), spacer);\n            }\n        }\n        catch (_) {\n            return encodeString(\"[unable to serialize, circular reference is too complex to analyze]\");\n        }\n        finally {\n            while (arr.length !== 0) {\n                const part = arr.pop();\n                if (part.length === 4) {\n                    Object.defineProperty(part[0], part[1], part[3]);\n                }\n                else {\n                    part[0][part[1]] = part[2];\n                }\n            }\n        }\n        return encodeString(res);\n    }\n}\nfunction setReplace(replace, val, k, parent) {\n    var propertyDescriptor = Object.getOwnPropertyDescriptor(parent, k);\n    if (propertyDescriptor.get !== undefined) {\n        if (propertyDescriptor.configurable) {\n            Object.defineProperty(parent, k, { value: replace });\n            arr.push([parent, k, val, propertyDescriptor]);\n        }\n        else {\n            replacerStack.push([val, k, replace]);\n        }\n    }\n    else {\n        parent[k] = replace;\n        arr.push([parent, k, val]);\n    }\n}\nfunction decirc(val, k, edgeIndex, stack, parent, depth, options) {\n    depth += 1;\n    var i;\n    if (typeof val === \"object\" && val !== null) {\n        for (i = 0; i < stack.length; i++) {\n            if (stack[i] === val) {\n                setReplace(CIRCULAR_REPLACE_NODE, val, k, parent);\n                return;\n            }\n        }\n        if (typeof options.depthLimit !== \"undefined\" &&\n            depth > options.depthLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        if (typeof options.edgesLimit !== \"undefined\" &&\n            edgeIndex + 1 > options.edgesLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        stack.push(val);\n        // Optimize for Arrays. Big arrays could kill the performance otherwise!\n        if (Array.isArray(val)) {\n            for (i = 0; i < val.length; i++) {\n                decirc(val[i], i, i, stack, val, depth, options);\n            }\n        }\n        else {\n            // Handle well-known types before Object.keys iteration\n            val = serializeWellKnownTypes(val);\n            var keys = Object.keys(val);\n            for (i = 0; i < keys.length; i++) {\n                var key = keys[i];\n                decirc(val[key], key, i, stack, val, depth, options);\n            }\n        }\n        stack.pop();\n    }\n}\n// Stable-stringify\nfunction compareFunction(a, b) {\n    if (a < b) {\n        return -1;\n    }\n    if (a > b) {\n        return 1;\n    }\n    return 0;\n}\nfunction deterministicStringify(obj, replacer, spacer, options) {\n    if (typeof options === \"undefined\") {\n        options = defaultOptions();\n    }\n    var tmp = deterministicDecirc(obj, \"\", 0, [], undefined, 0, options) || obj;\n    var res;\n    try {\n        if (replacerStack.length === 0) {\n            res = JSON.stringify(tmp, replacer, spacer);\n        }\n        else {\n            res = JSON.stringify(tmp, replaceGetterValues(replacer), spacer);\n        }\n    }\n    catch (_) {\n        return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\");\n    }\n    finally {\n        // Ensure that we restore the object as it was.\n        while (arr.length !== 0) {\n            var part = arr.pop();\n            if (part.length === 4) {\n                Object.defineProperty(part[0], part[1], part[3]);\n            }\n            else {\n                part[0][part[1]] = part[2];\n            }\n        }\n    }\n    return res;\n}\nfunction deterministicDecirc(val, k, edgeIndex, stack, parent, depth, options) {\n    depth += 1;\n    var i;\n    if (typeof val === \"object\" && val !== null) {\n        for (i = 0; i < stack.length; i++) {\n            if (stack[i] === val) {\n                setReplace(CIRCULAR_REPLACE_NODE, val, k, parent);\n                return;\n            }\n        }\n        try {\n            if (typeof val.toJSON === \"function\") {\n                return;\n            }\n        }\n        catch (_) {\n            return;\n        }\n        if (typeof options.depthLimit !== \"undefined\" &&\n            depth > options.depthLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        if (typeof options.edgesLimit !== \"undefined\" &&\n            edgeIndex + 1 > options.edgesLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        stack.push(val);\n        // Optimize for Arrays. Big arrays could kill the performance otherwise!\n        if (Array.isArray(val)) {\n            for (i = 0; i < val.length; i++) {\n                deterministicDecirc(val[i], i, i, stack, val, depth, options);\n            }\n        }\n        else {\n            // Handle well-known types before Object.keys iteration\n            val = serializeWellKnownTypes(val);\n            // Create a temporary object in the required way\n            var tmp = {};\n            var keys = Object.keys(val).sort(compareFunction);\n            for (i = 0; i < keys.length; i++) {\n                var key = keys[i];\n                deterministicDecirc(val[key], key, i, stack, val, depth, options);\n                tmp[key] = val[key];\n            }\n            if (typeof parent !== \"undefined\") {\n                arr.push([parent, k, val]);\n                parent[k] = tmp;\n            }\n            else {\n                return tmp;\n            }\n        }\n        stack.pop();\n    }\n}\n// wraps replacer function to handle values we couldn't replace\n// and mark them as replaced value\nfunction replaceGetterValues(replacer) {\n    replacer =\n        typeof replacer !== \"undefined\"\n            ? replacer\n            : function (k, v) {\n                return v;\n            };\n    return function (key, val) {\n        if (replacerStack.length > 0) {\n            for (var i = 0; i < replacerStack.length; i++) {\n                var part = replacerStack[i];\n                if (part[1] === key && part[0] === val) {\n                    val = part[2];\n                    replacerStack.splice(i, 1);\n                    break;\n                }\n            }\n        }\n        return replacer.call(this, key, val);\n    };\n}\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB,cAAc;;;;AACd;;AACA,IAAI,qBAAqB;AACzB,IAAI,wBAAwB;IAAE,QAAQ;AAAa;AACnD,IAAI,MAAM,EAAE;AACZ,IAAI,gBAAgB,EAAE;AACtB,MAAM,UAAU,IAAI;AACpB,SAAS;IACL,OAAO;QACH,YAAY,OAAO,gBAAgB;QACnC,YAAY,OAAO,gBAAgB;IACvC;AACJ;AACA,SAAS,aAAa,GAAG;IACrB,OAAO,QAAQ,MAAM,CAAC;AAC1B;AACA,6CAA6C;AAC7C,SAAS,wBAAwB,GAAG;IAChC,IAAI,OAAO,OAAO,QAAQ,YAAY,QAAQ,MAAM;QAChD,IAAI,eAAe,KAAK;YACpB,OAAO,OAAO,WAAW,CAAC;QAC9B,OACK,IAAI,eAAe,KAAK;YACzB,OAAO,MAAM,IAAI,CAAC;QACtB,OACK,IAAI,eAAe,MAAM;YAC1B,OAAO,IAAI,WAAW;QAC1B,OACK,IAAI,eAAe,QAAQ;YAC5B,OAAO,IAAI,QAAQ;QACvB,OACK,IAAI,eAAe,OAAO;YAC3B,OAAO;gBACH,MAAM,IAAI,IAAI;gBACd,SAAS,IAAI,OAAO;YACxB;QACJ;IACJ,OACK,IAAI,OAAO,QAAQ,UAAU;QAC9B,OAAO,IAAI,QAAQ;IACvB;IACA,OAAO;AACX;AACA,uDAAuD;AACvD,SAAS,sBAAsB,YAAY;IACvC,OAAO,SAAU,GAAG,EAAE,GAAG;QACrB,wCAAwC;QACxC,IAAI,cAAc;YACd,MAAM,aAAa,aAAa,IAAI,CAAC,IAAI,EAAE,KAAK;YAChD,sEAAsE;YACtE,IAAI,eAAe,WAAW;gBAC1B,OAAO;YACX;QACJ;QACA,4CAA4C;QAC5C,OAAO,wBAAwB;IACnC;AACJ;AAEO,SAAS,UAAU,GAAG,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO;IAClE,IAAI;QACA,MAAM,MAAM,KAAK,SAAS,CAAC,KAAK,sBAAsB,WAAW;QACjE,OAAO,aAAa;IACxB,EACA,OAAO,GAAG;QACN,4DAA4D;QAC5D,IAAI,CAAC,EAAE,OAAO,EAAE,SAAS,0CAA0C;YAC/D,QAAQ,IAAI,CAAC,CAAC,mDAAmD,EAAE,eAAe,CAAC,WAAW,EAAE,cAAc,GAAG,IAAI;YACrH,OAAO,aAAa;QACxB;QACA,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,uCAC5B,UACA,QAAQ,IAAI,CAAC,CAAC,oFAAoF,EAAE,eAAe,CAAC,WAAW,EAAE,cAAc,GAAG,IAAI;QAC1J,IAAI,OAAO,YAAY,aAAa;YAChC,UAAU;QACd;QACA,OAAO,KAAK,IAAI,GAAG,EAAE,EAAE,WAAW,GAAG;QACrC,IAAI;QACJ,IAAI;YACA,IAAI,cAAc,MAAM,KAAK,GAAG;gBAC5B,MAAM,KAAK,SAAS,CAAC,KAAK,UAAU;YACxC,OACK;gBACD,MAAM,KAAK,SAAS,CAAC,KAAK,oBAAoB,WAAW;YAC7D;QACJ,EACA,OAAO,GAAG;YACN,OAAO,aAAa;QACxB,SACQ;YACJ,MAAO,IAAI,MAAM,KAAK,EAAG;gBACrB,MAAM,OAAO,IAAI,GAAG;gBACpB,IAAI,KAAK,MAAM,KAAK,GAAG;oBACnB,OAAO,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;gBACnD,OACK;oBACD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE;gBAC9B;YACJ;QACJ;QACA,OAAO,aAAa;IACxB;AACJ;AACA,SAAS,WAAW,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM;IACvC,IAAI,qBAAqB,OAAO,wBAAwB,CAAC,QAAQ;IACjE,IAAI,mBAAmB,GAAG,KAAK,WAAW;QACtC,IAAI,mBAAmB,YAAY,EAAE;YACjC,OAAO,cAAc,CAAC,QAAQ,GAAG;gBAAE,OAAO;YAAQ;YAClD,IAAI,IAAI,CAAC;gBAAC;gBAAQ;gBAAG;gBAAK;aAAmB;QACjD,OACK;YACD,cAAc,IAAI,CAAC;gBAAC;gBAAK;gBAAG;aAAQ;QACxC;IACJ,OACK;QACD,MAAM,CAAC,EAAE,GAAG;QACZ,IAAI,IAAI,CAAC;YAAC;YAAQ;YAAG;SAAI;IAC7B;AACJ;AACA,SAAS,OAAO,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;IAC5D,SAAS;IACT,IAAI;IACJ,IAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;QACzC,IAAK,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YAC/B,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;gBAClB,WAAW,uBAAuB,KAAK,GAAG;gBAC1C;YACJ;QACJ;QACA,IAAI,OAAO,QAAQ,UAAU,KAAK,eAC9B,QAAQ,QAAQ,UAAU,EAAE;YAC5B,WAAW,oBAAoB,KAAK,GAAG;YACvC;QACJ;QACA,IAAI,OAAO,QAAQ,UAAU,KAAK,eAC9B,YAAY,IAAI,QAAQ,UAAU,EAAE;YACpC,WAAW,oBAAoB,KAAK,GAAG;YACvC;QACJ;QACA,MAAM,IAAI,CAAC;QACX,wEAAwE;QACxE,IAAI,MAAM,OAAO,CAAC,MAAM;YACpB,IAAK,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;gBAC7B,OAAO,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,OAAO,KAAK,OAAO;YAC5C;QACJ,OACK;YACD,uDAAuD;YACvD,MAAM,wBAAwB;YAC9B,IAAI,OAAO,OAAO,IAAI,CAAC;YACvB,IAAK,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBAC9B,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,OAAO,KAAK,OAAO;YAChD;QACJ;QACA,MAAM,GAAG;IACb;AACJ;AACA,mBAAmB;AACnB,SAAS,gBAAgB,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,GAAG;QACP,OAAO,CAAC;IACZ;IACA,IAAI,IAAI,GAAG;QACP,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,uBAAuB,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO;IAC1D,IAAI,OAAO,YAAY,aAAa;QAChC,UAAU;IACd;IACA,IAAI,MAAM,oBAAoB,KAAK,IAAI,GAAG,EAAE,EAAE,WAAW,GAAG,YAAY;IACxE,IAAI;IACJ,IAAI;QACA,IAAI,cAAc,MAAM,KAAK,GAAG;YAC5B,MAAM,KAAK,SAAS,CAAC,KAAK,UAAU;QACxC,OACK;YACD,MAAM,KAAK,SAAS,CAAC,KAAK,oBAAoB,WAAW;QAC7D;IACJ,EACA,OAAO,GAAG;QACN,OAAO,KAAK,SAAS,CAAC;IAC1B,SACQ;QACJ,+CAA+C;QAC/C,MAAO,IAAI,MAAM,KAAK,EAAG;YACrB,IAAI,OAAO,IAAI,GAAG;YAClB,IAAI,KAAK,MAAM,KAAK,GAAG;gBACnB,OAAO,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;YACnD,OACK;gBACD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE;YAC9B;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,oBAAoB,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;IACzE,SAAS;IACT,IAAI;IACJ,IAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;QACzC,IAAK,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YAC/B,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;gBAClB,WAAW,uBAAuB,KAAK,GAAG;gBAC1C;YACJ;QACJ;QACA,IAAI;YACA,IAAI,OAAO,IAAI,MAAM,KAAK,YAAY;gBAClC;YACJ;QACJ,EACA,OAAO,GAAG;YACN;QACJ;QACA,IAAI,OAAO,QAAQ,UAAU,KAAK,eAC9B,QAAQ,QAAQ,UAAU,EAAE;YAC5B,WAAW,oBAAoB,KAAK,GAAG;YACvC;QACJ;QACA,IAAI,OAAO,QAAQ,UAAU,KAAK,eAC9B,YAAY,IAAI,QAAQ,UAAU,EAAE;YACpC,WAAW,oBAAoB,KAAK,GAAG;YACvC;QACJ;QACA,MAAM,IAAI,CAAC;QACX,wEAAwE;QACxE,IAAI,MAAM,OAAO,CAAC,MAAM;YACpB,IAAK,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;gBAC7B,oBAAoB,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,OAAO,KAAK,OAAO;YACzD;QACJ,OACK;YACD,uDAAuD;YACvD,MAAM,wBAAwB;YAC9B,gDAAgD;YAChD,IAAI,MAAM,CAAC;YACX,IAAI,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC;YACjC,IAAK,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBAC9B,IAAI,MAAM,IAAI,CAAC,EAAE;gBACjB,oBAAoB,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,OAAO,KAAK,OAAO;gBACzD,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;YACvB;YACA,IAAI,OAAO,WAAW,aAAa;gBAC/B,IAAI,IAAI,CAAC;oBAAC;oBAAQ;oBAAG;iBAAI;gBACzB,MAAM,CAAC,EAAE,GAAG;YAChB,OACK;gBACD,OAAO;YACX;QACJ;QACA,MAAM,GAAG;IACb;AACJ;AACA,+DAA+D;AAC/D,kCAAkC;AAClC,SAAS,oBAAoB,QAAQ;IACjC,WACI,OAAO,aAAa,cACd,WACA,SAAU,CAAC,EAAE,CAAC;QACZ,OAAO;IACX;IACR,OAAO,SAAU,GAAG,EAAE,GAAG;QACrB,IAAI,cAAc,MAAM,GAAG,GAAG;YAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;gBAC3C,IAAI,OAAO,aAAa,CAAC,EAAE;gBAC3B,IAAI,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,KAAK;oBACpC,MAAM,IAAI,CAAC,EAAE;oBACb,cAAc,MAAM,CAAC,GAAG;oBACxB;gBACJ;YACJ;QACJ;QACA,OAAO,SAAS,IAAI,CAAC,IAAI,EAAE,KAAK;IACpC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/client.js"], "sourcesContent": ["import * as uuid from \"uuid\";\nimport { LangSmithToOTELTranslator, } from \"./experimental/otel/translator.js\";\nimport { getDefaultOTLPTracerComponents, getOTELTrace, getOTELContext, } from \"./singletons/otel.js\";\nimport { AsyncCaller } from \"./utils/async_caller.js\";\nimport { convertLangChainMessageToExample, isLangChainMessage, } from \"./utils/messages.js\";\nimport { getEnvironmentVariable, getLangChainEnvVarsMetadata, getLangSmithEnvironmentVariable, getRuntimeEnvironment, } from \"./utils/env.js\";\nimport { __version__ } from \"./index.js\";\nimport { assertUuid } from \"./utils/_uuid.js\";\nimport { warnOnce } from \"./utils/warn.js\";\nimport { parsePromptIdentifier } from \"./utils/prompts.js\";\nimport { raiseForStatus } from \"./utils/error.js\";\nimport { _globalFetchImplementationIsNodeFetch, _getFetchImplementation, } from \"./singletons/fetch.js\";\nimport { serialize as serializePayloadForTracing } from \"./utils/fast-safe-stringify/index.js\";\nexport function mergeRuntimeEnvIntoRunCreate(run) {\n    const runtimeEnv = getRuntimeEnvironment();\n    const envVars = getLangChainEnvVarsMetadata();\n    const extra = run.extra ?? {};\n    const metadata = extra.metadata;\n    run.extra = {\n        ...extra,\n        runtime: {\n            ...runtimeEnv,\n            ...extra?.runtime,\n        },\n        metadata: {\n            ...envVars,\n            ...(envVars.revision_id || run.revision_id\n                ? { revision_id: run.revision_id ?? envVars.revision_id }\n                : {}),\n            ...metadata,\n        },\n    };\n    return run;\n}\nconst getTracingSamplingRate = (configRate) => {\n    const samplingRateStr = configRate?.toString() ??\n        getLangSmithEnvironmentVariable(\"TRACING_SAMPLING_RATE\");\n    if (samplingRateStr === undefined) {\n        return undefined;\n    }\n    const samplingRate = parseFloat(samplingRateStr);\n    if (samplingRate < 0 || samplingRate > 1) {\n        throw new Error(`LANGSMITH_TRACING_SAMPLING_RATE must be between 0 and 1 if set. Got: ${samplingRate}`);\n    }\n    return samplingRate;\n};\n// utility functions\nconst isLocalhost = (url) => {\n    const strippedUrl = url.replace(\"http://\", \"\").replace(\"https://\", \"\");\n    const hostname = strippedUrl.split(\"/\")[0].split(\":\")[0];\n    return (hostname === \"localhost\" || hostname === \"127.0.0.1\" || hostname === \"::1\");\n};\nasync function toArray(iterable) {\n    const result = [];\n    for await (const item of iterable) {\n        result.push(item);\n    }\n    return result;\n}\nfunction trimQuotes(str) {\n    if (str === undefined) {\n        return undefined;\n    }\n    return str\n        .trim()\n        .replace(/^\"(.*)\"$/, \"$1\")\n        .replace(/^'(.*)'$/, \"$1\");\n}\nconst handle429 = async (response) => {\n    if (response?.status === 429) {\n        const retryAfter = parseInt(response.headers.get(\"retry-after\") ?? \"30\", 10) * 1000;\n        if (retryAfter > 0) {\n            await new Promise((resolve) => setTimeout(resolve, retryAfter));\n            // Return directly after calling this check\n            return true;\n        }\n    }\n    // Fall back to existing status checks\n    return false;\n};\nfunction _formatFeedbackScore(score) {\n    if (typeof score === \"number\") {\n        // Truncate at 4 decimal places\n        return Number(score.toFixed(4));\n    }\n    return score;\n}\nexport class AutoBatchQueue {\n    constructor() {\n        Object.defineProperty(this, \"items\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: []\n        });\n        Object.defineProperty(this, \"sizeBytes\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 0\n        });\n    }\n    peek() {\n        return this.items[0];\n    }\n    push(item) {\n        let itemPromiseResolve;\n        const itemPromise = new Promise((resolve) => {\n            // Setting itemPromiseResolve is synchronous with promise creation:\n            // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise\n            itemPromiseResolve = resolve;\n        });\n        const size = serializePayloadForTracing(item.item, `Serializing run with id: ${item.item.id}`).length;\n        this.items.push({\n            action: item.action,\n            payload: item.item,\n            otelContext: item.otelContext,\n            apiKey: item.apiKey,\n            apiUrl: item.apiUrl,\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            itemPromiseResolve: itemPromiseResolve,\n            itemPromise,\n            size,\n        });\n        this.sizeBytes += size;\n        return itemPromise;\n    }\n    pop(upToSizeBytes) {\n        if (upToSizeBytes < 1) {\n            throw new Error(\"Number of bytes to pop off may not be less than 1.\");\n        }\n        const popped = [];\n        let poppedSizeBytes = 0;\n        // Pop items until we reach or exceed the size limit\n        while (poppedSizeBytes + (this.peek()?.size ?? 0) < upToSizeBytes &&\n            this.items.length > 0) {\n            const item = this.items.shift();\n            if (item) {\n                popped.push(item);\n                poppedSizeBytes += item.size;\n                this.sizeBytes -= item.size;\n            }\n        }\n        // If there is an item on the queue we were unable to pop,\n        // just return it as a single batch.\n        if (popped.length === 0 && this.items.length > 0) {\n            const item = this.items.shift();\n            popped.push(item);\n            poppedSizeBytes += item.size;\n            this.sizeBytes -= item.size;\n        }\n        return [\n            popped.map((it) => ({\n                action: it.action,\n                item: it.payload,\n                otelContext: it.otelContext,\n                apiKey: it.apiKey,\n                apiUrl: it.apiUrl,\n            })),\n            () => popped.forEach((it) => it.itemPromiseResolve()),\n        ];\n    }\n}\n// 20 MB\nexport const DEFAULT_BATCH_SIZE_LIMIT_BYTES = 20_971_520;\nconst SERVER_INFO_REQUEST_TIMEOUT = 2500;\nconst DEFAULT_API_URL = \"https://api.smith.langchain.com\";\nexport class Client {\n    constructor(config = {}) {\n        Object.defineProperty(this, \"apiKey\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"apiUrl\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"webUrl\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"caller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"batchIngestCaller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"timeout_ms\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"_tenantId\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: null\n        });\n        Object.defineProperty(this, \"hideInputs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"hideOutputs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"tracingSampleRate\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"filteredPostUuids\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new Set()\n        });\n        Object.defineProperty(this, \"autoBatchTracing\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"autoBatchQueue\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new AutoBatchQueue()\n        });\n        Object.defineProperty(this, \"autoBatchTimeout\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"autoBatchAggregationDelayMs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 250\n        });\n        Object.defineProperty(this, \"batchSizeBytesLimit\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"fetchOptions\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"settings\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"blockOnRootRunFinalization\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: getEnvironmentVariable(\"LANGSMITH_TRACING_BACKGROUND\") === \"false\"\n        });\n        Object.defineProperty(this, \"traceBatchConcurrency\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 5\n        });\n        Object.defineProperty(this, \"_serverInfo\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Object.defineProperty(this, \"_getServerInfoPromise\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"manualFlushMode\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        Object.defineProperty(this, \"langSmithToOTELTranslator\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"multipartStreamingDisabled\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        Object.defineProperty(this, \"debug\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: getEnvironmentVariable(\"LANGSMITH_DEBUG\") === \"true\"\n        });\n        const defaultConfig = Client.getDefaultClientConfig();\n        this.tracingSampleRate = getTracingSamplingRate(config.tracingSamplingRate);\n        this.apiUrl = trimQuotes(config.apiUrl ?? defaultConfig.apiUrl) ?? \"\";\n        if (this.apiUrl.endsWith(\"/\")) {\n            this.apiUrl = this.apiUrl.slice(0, -1);\n        }\n        this.apiKey = trimQuotes(config.apiKey ?? defaultConfig.apiKey);\n        this.webUrl = trimQuotes(config.webUrl ?? defaultConfig.webUrl);\n        if (this.webUrl?.endsWith(\"/\")) {\n            this.webUrl = this.webUrl.slice(0, -1);\n        }\n        this.timeout_ms = config.timeout_ms ?? 90_000;\n        this.caller = new AsyncCaller({\n            ...(config.callerOptions ?? {}),\n            debug: config.debug ?? this.debug,\n        });\n        this.traceBatchConcurrency =\n            config.traceBatchConcurrency ?? this.traceBatchConcurrency;\n        if (this.traceBatchConcurrency < 1) {\n            throw new Error(\"Trace batch concurrency must be positive.\");\n        }\n        this.debug = config.debug ?? this.debug;\n        this.batchIngestCaller = new AsyncCaller({\n            maxRetries: 2,\n            maxConcurrency: this.traceBatchConcurrency,\n            ...(config.callerOptions ?? {}),\n            onFailedResponseHook: handle429,\n            debug: config.debug ?? this.debug,\n        });\n        this.hideInputs =\n            config.hideInputs ?? config.anonymizer ?? defaultConfig.hideInputs;\n        this.hideOutputs =\n            config.hideOutputs ?? config.anonymizer ?? defaultConfig.hideOutputs;\n        this.autoBatchTracing = config.autoBatchTracing ?? this.autoBatchTracing;\n        this.blockOnRootRunFinalization =\n            config.blockOnRootRunFinalization ?? this.blockOnRootRunFinalization;\n        this.batchSizeBytesLimit = config.batchSizeBytesLimit;\n        this.fetchOptions = config.fetchOptions || {};\n        this.manualFlushMode = config.manualFlushMode ?? this.manualFlushMode;\n        if (getEnvironmentVariable(\"OTEL_ENABLED\") === \"true\") {\n            this.langSmithToOTELTranslator = new LangSmithToOTELTranslator();\n        }\n    }\n    static getDefaultClientConfig() {\n        const apiKey = getLangSmithEnvironmentVariable(\"API_KEY\");\n        const apiUrl = getLangSmithEnvironmentVariable(\"ENDPOINT\") ?? DEFAULT_API_URL;\n        const hideInputs = getLangSmithEnvironmentVariable(\"HIDE_INPUTS\") === \"true\";\n        const hideOutputs = getLangSmithEnvironmentVariable(\"HIDE_OUTPUTS\") === \"true\";\n        return {\n            apiUrl: apiUrl,\n            apiKey: apiKey,\n            webUrl: undefined,\n            hideInputs: hideInputs,\n            hideOutputs: hideOutputs,\n        };\n    }\n    getHostUrl() {\n        if (this.webUrl) {\n            return this.webUrl;\n        }\n        else if (isLocalhost(this.apiUrl)) {\n            this.webUrl = \"http://localhost:3000\";\n            return this.webUrl;\n        }\n        else if (this.apiUrl.endsWith(\"/api/v1\")) {\n            this.webUrl = this.apiUrl.replace(\"/api/v1\", \"\");\n            return this.webUrl;\n        }\n        else if (this.apiUrl.includes(\"/api\") &&\n            !this.apiUrl.split(\".\", 1)[0].endsWith(\"api\")) {\n            this.webUrl = this.apiUrl.replace(\"/api\", \"\");\n            return this.webUrl;\n        }\n        else if (this.apiUrl.split(\".\", 1)[0].includes(\"dev\")) {\n            this.webUrl = \"https://dev.smith.langchain.com\";\n            return this.webUrl;\n        }\n        else if (this.apiUrl.split(\".\", 1)[0].includes(\"eu\")) {\n            this.webUrl = \"https://eu.smith.langchain.com\";\n            return this.webUrl;\n        }\n        else if (this.apiUrl.split(\".\", 1)[0].includes(\"beta\")) {\n            this.webUrl = \"https://beta.smith.langchain.com\";\n            return this.webUrl;\n        }\n        else {\n            this.webUrl = \"https://smith.langchain.com\";\n            return this.webUrl;\n        }\n    }\n    get headers() {\n        const headers = {\n            \"User-Agent\": `langsmith-js/${__version__}`,\n        };\n        if (this.apiKey) {\n            headers[\"x-api-key\"] = `${this.apiKey}`;\n        }\n        return headers;\n    }\n    _getPlatformEndpointPath(path) {\n        // Check if apiUrl already ends with /v1 or /v1/ to avoid double /v1/v1/ paths\n        const needsV1Prefix = this.apiUrl.slice(-3) !== \"/v1\" && this.apiUrl.slice(-4) !== \"/v1/\";\n        return needsV1Prefix ? `/v1/platform/${path}` : `/platform/${path}`;\n    }\n    async processInputs(inputs) {\n        if (this.hideInputs === false) {\n            return inputs;\n        }\n        if (this.hideInputs === true) {\n            return {};\n        }\n        if (typeof this.hideInputs === \"function\") {\n            return this.hideInputs(inputs);\n        }\n        return inputs;\n    }\n    async processOutputs(outputs) {\n        if (this.hideOutputs === false) {\n            return outputs;\n        }\n        if (this.hideOutputs === true) {\n            return {};\n        }\n        if (typeof this.hideOutputs === \"function\") {\n            return this.hideOutputs(outputs);\n        }\n        return outputs;\n    }\n    async prepareRunCreateOrUpdateInputs(run) {\n        const runParams = { ...run };\n        if (runParams.inputs !== undefined) {\n            runParams.inputs = await this.processInputs(runParams.inputs);\n        }\n        if (runParams.outputs !== undefined) {\n            runParams.outputs = await this.processOutputs(runParams.outputs);\n        }\n        return runParams;\n    }\n    async _getResponse(path, queryParams) {\n        const paramsString = queryParams?.toString() ?? \"\";\n        const url = `${this.apiUrl}${path}?${paramsString}`;\n        const response = await this.caller.call(_getFetchImplementation(this.debug), url, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, `Failed to fetch ${path}`);\n        return response;\n    }\n    async _get(path, queryParams) {\n        const response = await this._getResponse(path, queryParams);\n        return response.json();\n    }\n    async *_getPaginated(path, queryParams = new URLSearchParams(), transform) {\n        let offset = Number(queryParams.get(\"offset\")) || 0;\n        const limit = Number(queryParams.get(\"limit\")) || 100;\n        while (true) {\n            queryParams.set(\"offset\", String(offset));\n            queryParams.set(\"limit\", String(limit));\n            const url = `${this.apiUrl}${path}?${queryParams}`;\n            const response = await this.caller.call(_getFetchImplementation(this.debug), url, {\n                method: \"GET\",\n                headers: this.headers,\n                signal: AbortSignal.timeout(this.timeout_ms),\n                ...this.fetchOptions,\n            });\n            await raiseForStatus(response, `Failed to fetch ${path}`);\n            const items = transform\n                ? transform(await response.json())\n                : await response.json();\n            if (items.length === 0) {\n                break;\n            }\n            yield items;\n            if (items.length < limit) {\n                break;\n            }\n            offset += items.length;\n        }\n    }\n    async *_getCursorPaginatedList(path, body = null, requestMethod = \"POST\", dataKey = \"runs\") {\n        const bodyParams = body ? { ...body } : {};\n        while (true) {\n            const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}${path}`, {\n                method: requestMethod,\n                headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n                signal: AbortSignal.timeout(this.timeout_ms),\n                ...this.fetchOptions,\n                body: JSON.stringify(bodyParams),\n            });\n            const responseBody = await response.json();\n            if (!responseBody) {\n                break;\n            }\n            if (!responseBody[dataKey]) {\n                break;\n            }\n            yield responseBody[dataKey];\n            const cursors = responseBody.cursors;\n            if (!cursors) {\n                break;\n            }\n            if (!cursors.next) {\n                break;\n            }\n            bodyParams.cursor = cursors.next;\n        }\n    }\n    // Allows mocking for tests\n    _shouldSample() {\n        if (this.tracingSampleRate === undefined) {\n            return true;\n        }\n        return Math.random() < this.tracingSampleRate;\n    }\n    _filterForSampling(runs, patch = false) {\n        if (this.tracingSampleRate === undefined) {\n            return runs;\n        }\n        if (patch) {\n            const sampled = [];\n            for (const run of runs) {\n                if (!this.filteredPostUuids.has(run.id)) {\n                    sampled.push(run);\n                }\n                else {\n                    this.filteredPostUuids.delete(run.id);\n                }\n            }\n            return sampled;\n        }\n        else {\n            // For new runs, sample at trace level to maintain consistency\n            const sampled = [];\n            for (const run of runs) {\n                const traceId = run.trace_id ?? run.id;\n                // If we've already made a decision about this trace, follow it\n                if (this.filteredPostUuids.has(traceId)) {\n                    continue;\n                }\n                // For new traces, apply sampling\n                if (run.id === traceId) {\n                    if (this._shouldSample()) {\n                        sampled.push(run);\n                    }\n                    else {\n                        this.filteredPostUuids.add(traceId);\n                    }\n                }\n                else {\n                    // Child runs follow their trace's sampling decision\n                    sampled.push(run);\n                }\n            }\n            return sampled;\n        }\n    }\n    async _getBatchSizeLimitBytes() {\n        const serverInfo = await this._ensureServerInfo();\n        return (this.batchSizeBytesLimit ??\n            serverInfo.batch_ingest_config?.size_limit_bytes ??\n            DEFAULT_BATCH_SIZE_LIMIT_BYTES);\n    }\n    async _getMultiPartSupport() {\n        const serverInfo = await this._ensureServerInfo();\n        return (serverInfo.instance_flags?.dataset_examples_multipart_enabled ?? false);\n    }\n    drainAutoBatchQueue(batchSizeLimit) {\n        const promises = [];\n        while (this.autoBatchQueue.items.length > 0) {\n            const [batch, done] = this.autoBatchQueue.pop(batchSizeLimit);\n            if (!batch.length) {\n                done();\n                break;\n            }\n            const batchesByDestination = batch.reduce((acc, item) => {\n                const apiUrl = item.apiUrl ?? this.apiUrl;\n                const apiKey = item.apiKey ?? this.apiKey;\n                const isDefault = item.apiKey === this.apiKey && item.apiUrl === this.apiUrl;\n                const batchKey = isDefault ? \"default\" : `${apiUrl}|${apiKey}`;\n                if (!acc[batchKey]) {\n                    acc[batchKey] = [];\n                }\n                acc[batchKey].push(item);\n                return acc;\n            }, {});\n            const batchPromises = [];\n            for (const [batchKey, batch] of Object.entries(batchesByDestination)) {\n                const batchPromise = this._processBatch(batch, {\n                    apiUrl: batchKey === \"default\" ? undefined : batchKey.split(\"|\")[0],\n                    apiKey: batchKey === \"default\" ? undefined : batchKey.split(\"|\")[1],\n                });\n                batchPromises.push(batchPromise);\n            }\n            // Wait for all batches to complete, then call the overall done callback\n            const allBatchesPromise = Promise.all(batchPromises).finally(done);\n            promises.push(allBatchesPromise);\n        }\n        return Promise.all(promises);\n    }\n    async _processBatch(batch, options) {\n        if (!batch.length) {\n            return;\n        }\n        try {\n            if (this.langSmithToOTELTranslator !== undefined) {\n                this._sendBatchToOTELTranslator(batch);\n            }\n            else {\n                const ingestParams = {\n                    runCreates: batch\n                        .filter((item) => item.action === \"create\")\n                        .map((item) => item.item),\n                    runUpdates: batch\n                        .filter((item) => item.action === \"update\")\n                        .map((item) => item.item),\n                };\n                const serverInfo = await this._ensureServerInfo();\n                if (serverInfo?.batch_ingest_config?.use_multipart_endpoint) {\n                    await this.multipartIngestRuns(ingestParams, options);\n                }\n                else {\n                    await this.batchIngestRuns(ingestParams, options);\n                }\n            }\n        }\n        catch (e) {\n            console.error(\"Error exporting batch:\", e);\n        }\n    }\n    _sendBatchToOTELTranslator(batch) {\n        if (this.langSmithToOTELTranslator !== undefined) {\n            const otelContextMap = new Map();\n            const operations = [];\n            for (const item of batch) {\n                if (item.item.id && item.otelContext) {\n                    otelContextMap.set(item.item.id, item.otelContext);\n                    if (item.action === \"create\") {\n                        operations.push({\n                            operation: \"post\",\n                            id: item.item.id,\n                            trace_id: item.item.trace_id ?? item.item.id,\n                            run: item.item,\n                        });\n                    }\n                    else {\n                        operations.push({\n                            operation: \"patch\",\n                            id: item.item.id,\n                            trace_id: item.item.trace_id ?? item.item.id,\n                            run: item.item,\n                        });\n                    }\n                }\n            }\n            this.langSmithToOTELTranslator.exportBatch(operations, otelContextMap);\n        }\n    }\n    async processRunOperation(item) {\n        clearTimeout(this.autoBatchTimeout);\n        this.autoBatchTimeout = undefined;\n        if (item.action === \"create\") {\n            item.item = mergeRuntimeEnvIntoRunCreate(item.item);\n        }\n        const itemPromise = this.autoBatchQueue.push(item);\n        if (this.manualFlushMode) {\n            // Rely on manual flushing in serverless environments\n            return itemPromise;\n        }\n        const sizeLimitBytes = await this._getBatchSizeLimitBytes();\n        if (this.autoBatchQueue.sizeBytes > sizeLimitBytes) {\n            void this.drainAutoBatchQueue(sizeLimitBytes);\n        }\n        if (this.autoBatchQueue.items.length > 0) {\n            this.autoBatchTimeout = setTimeout(() => {\n                this.autoBatchTimeout = undefined;\n                void this.drainAutoBatchQueue(sizeLimitBytes);\n            }, this.autoBatchAggregationDelayMs);\n        }\n        return itemPromise;\n    }\n    async _getServerInfo() {\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/info`, {\n            method: \"GET\",\n            headers: { Accept: \"application/json\" },\n            signal: AbortSignal.timeout(SERVER_INFO_REQUEST_TIMEOUT),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"get server info\");\n        const json = await response.json();\n        if (this.debug) {\n            console.log(\"\\n=== LangSmith Server Configuration ===\\n\" +\n                JSON.stringify(json, null, 2) +\n                \"\\n\");\n        }\n        return json;\n    }\n    async _ensureServerInfo() {\n        if (this._getServerInfoPromise === undefined) {\n            this._getServerInfoPromise = (async () => {\n                if (this._serverInfo === undefined) {\n                    try {\n                        this._serverInfo = await this._getServerInfo();\n                    }\n                    catch (e) {\n                        console.warn(`[WARNING]: LangSmith failed to fetch info on supported operations with status code ${e.status}. Falling back to batch operations and default limits.`);\n                    }\n                }\n                return this._serverInfo ?? {};\n            })();\n        }\n        return this._getServerInfoPromise.then((serverInfo) => {\n            if (this._serverInfo === undefined) {\n                this._getServerInfoPromise = undefined;\n            }\n            return serverInfo;\n        });\n    }\n    async _getSettings() {\n        if (!this.settings) {\n            this.settings = this._get(\"/settings\");\n        }\n        return await this.settings;\n    }\n    /**\n     * Flushes current queued traces.\n     */\n    async flush() {\n        const sizeLimitBytes = await this._getBatchSizeLimitBytes();\n        await this.drainAutoBatchQueue(sizeLimitBytes);\n    }\n    _cloneCurrentOTELContext() {\n        const otel_trace = getOTELTrace();\n        const otel_context = getOTELContext();\n        if (this.langSmithToOTELTranslator !== undefined) {\n            const currentSpan = otel_trace.getActiveSpan();\n            if (currentSpan) {\n                return otel_trace.setSpan(otel_context.active(), currentSpan);\n            }\n        }\n        return undefined;\n    }\n    async createRun(run, options) {\n        if (!this._filterForSampling([run]).length) {\n            return;\n        }\n        const headers = {\n            ...this.headers,\n            \"Content-Type\": \"application/json\",\n        };\n        const session_name = run.project_name;\n        delete run.project_name;\n        const runCreate = await this.prepareRunCreateOrUpdateInputs({\n            session_name,\n            ...run,\n            start_time: run.start_time ?? Date.now(),\n        });\n        if (this.autoBatchTracing &&\n            runCreate.trace_id !== undefined &&\n            runCreate.dotted_order !== undefined) {\n            const otelContext = this._cloneCurrentOTELContext();\n            void this.processRunOperation({\n                action: \"create\",\n                item: runCreate,\n                otelContext,\n                apiKey: options?.apiKey,\n                apiUrl: options?.apiUrl,\n            }).catch(console.error);\n            return;\n        }\n        const mergedRunCreateParam = mergeRuntimeEnvIntoRunCreate(runCreate);\n        if (options?.apiKey !== undefined) {\n            headers[\"x-api-key\"] = options.apiKey;\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${options?.apiUrl ?? this.apiUrl}/runs`, {\n            method: \"POST\",\n            headers,\n            body: serializePayloadForTracing(mergedRunCreateParam, `Creating run with id: ${mergedRunCreateParam.id}`),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"create run\", true);\n    }\n    /**\n     * Batch ingest/upsert multiple runs in the Langsmith system.\n     * @param runs\n     */\n    async batchIngestRuns({ runCreates, runUpdates, }, options) {\n        if (runCreates === undefined && runUpdates === undefined) {\n            return;\n        }\n        let preparedCreateParams = await Promise.all(runCreates?.map((create) => this.prepareRunCreateOrUpdateInputs(create)) ?? []);\n        let preparedUpdateParams = await Promise.all(runUpdates?.map((update) => this.prepareRunCreateOrUpdateInputs(update)) ?? []);\n        if (preparedCreateParams.length > 0 && preparedUpdateParams.length > 0) {\n            const createById = preparedCreateParams.reduce((params, run) => {\n                if (!run.id) {\n                    return params;\n                }\n                params[run.id] = run;\n                return params;\n            }, {});\n            const standaloneUpdates = [];\n            for (const updateParam of preparedUpdateParams) {\n                if (updateParam.id !== undefined && createById[updateParam.id]) {\n                    createById[updateParam.id] = {\n                        ...createById[updateParam.id],\n                        ...updateParam,\n                    };\n                }\n                else {\n                    standaloneUpdates.push(updateParam);\n                }\n            }\n            preparedCreateParams = Object.values(createById);\n            preparedUpdateParams = standaloneUpdates;\n        }\n        const rawBatch = {\n            post: preparedCreateParams,\n            patch: preparedUpdateParams,\n        };\n        if (!rawBatch.post.length && !rawBatch.patch.length) {\n            return;\n        }\n        const batchChunks = {\n            post: [],\n            patch: [],\n        };\n        for (const k of [\"post\", \"patch\"]) {\n            const key = k;\n            const batchItems = rawBatch[key].reverse();\n            let batchItem = batchItems.pop();\n            while (batchItem !== undefined) {\n                // Type is wrong but this is a deprecated code path anyway\n                batchChunks[key].push(batchItem);\n                batchItem = batchItems.pop();\n            }\n        }\n        if (batchChunks.post.length > 0 || batchChunks.patch.length > 0) {\n            const runIds = batchChunks.post\n                .map((item) => item.id)\n                .concat(batchChunks.patch.map((item) => item.id))\n                .join(\",\");\n            await this._postBatchIngestRuns(serializePayloadForTracing(batchChunks, `Ingesting runs with ids: ${runIds}`), options);\n        }\n    }\n    async _postBatchIngestRuns(body, options) {\n        const headers = {\n            ...this.headers,\n            \"Content-Type\": \"application/json\",\n            Accept: \"application/json\",\n        };\n        if (options?.apiKey !== undefined) {\n            headers[\"x-api-key\"] = options.apiKey;\n        }\n        const response = await this.batchIngestCaller.call(_getFetchImplementation(this.debug), `${options?.apiUrl ?? this.apiUrl}/runs/batch`, {\n            method: \"POST\",\n            headers,\n            body: body,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"batch create run\", true);\n    }\n    /**\n     * Batch ingest/upsert multiple runs in the Langsmith system.\n     * @param runs\n     */\n    async multipartIngestRuns({ runCreates, runUpdates, }, options) {\n        if (runCreates === undefined && runUpdates === undefined) {\n            return;\n        }\n        // transform and convert to dicts\n        const allAttachments = {};\n        let preparedCreateParams = [];\n        for (const create of runCreates ?? []) {\n            const preparedCreate = await this.prepareRunCreateOrUpdateInputs(create);\n            if (preparedCreate.id !== undefined &&\n                preparedCreate.attachments !== undefined) {\n                allAttachments[preparedCreate.id] = preparedCreate.attachments;\n            }\n            delete preparedCreate.attachments;\n            preparedCreateParams.push(preparedCreate);\n        }\n        let preparedUpdateParams = [];\n        for (const update of runUpdates ?? []) {\n            preparedUpdateParams.push(await this.prepareRunCreateOrUpdateInputs(update));\n        }\n        // require trace_id and dotted_order\n        const invalidRunCreate = preparedCreateParams.find((runCreate) => {\n            return (runCreate.trace_id === undefined || runCreate.dotted_order === undefined);\n        });\n        if (invalidRunCreate !== undefined) {\n            throw new Error(`Multipart ingest requires \"trace_id\" and \"dotted_order\" to be set when creating a run`);\n        }\n        const invalidRunUpdate = preparedUpdateParams.find((runUpdate) => {\n            return (runUpdate.trace_id === undefined || runUpdate.dotted_order === undefined);\n        });\n        if (invalidRunUpdate !== undefined) {\n            throw new Error(`Multipart ingest requires \"trace_id\" and \"dotted_order\" to be set when updating a run`);\n        }\n        // combine post and patch dicts where possible\n        if (preparedCreateParams.length > 0 && preparedUpdateParams.length > 0) {\n            const createById = preparedCreateParams.reduce((params, run) => {\n                if (!run.id) {\n                    return params;\n                }\n                params[run.id] = run;\n                return params;\n            }, {});\n            const standaloneUpdates = [];\n            for (const updateParam of preparedUpdateParams) {\n                if (updateParam.id !== undefined && createById[updateParam.id]) {\n                    createById[updateParam.id] = {\n                        ...createById[updateParam.id],\n                        ...updateParam,\n                    };\n                }\n                else {\n                    standaloneUpdates.push(updateParam);\n                }\n            }\n            preparedCreateParams = Object.values(createById);\n            preparedUpdateParams = standaloneUpdates;\n        }\n        if (preparedCreateParams.length === 0 &&\n            preparedUpdateParams.length === 0) {\n            return;\n        }\n        // send the runs in multipart requests\n        const accumulatedContext = [];\n        const accumulatedParts = [];\n        for (const [method, payloads] of [\n            [\"post\", preparedCreateParams],\n            [\"patch\", preparedUpdateParams],\n        ]) {\n            for (const originalPayload of payloads) {\n                // collect fields to be sent as separate parts\n                const { inputs, outputs, events, attachments, ...payload } = originalPayload;\n                const fields = { inputs, outputs, events };\n                // encode the main run payload\n                const stringifiedPayload = serializePayloadForTracing(payload, `Serializing for multipart ingestion of run with id: ${payload.id}`);\n                accumulatedParts.push({\n                    name: `${method}.${payload.id}`,\n                    payload: new Blob([stringifiedPayload], {\n                        type: `application/json; length=${stringifiedPayload.length}`, // encoding=gzip\n                    }),\n                });\n                // encode the fields we collected\n                for (const [key, value] of Object.entries(fields)) {\n                    if (value === undefined) {\n                        continue;\n                    }\n                    const stringifiedValue = serializePayloadForTracing(value, `Serializing ${key} for multipart ingestion of run with id: ${payload.id}`);\n                    accumulatedParts.push({\n                        name: `${method}.${payload.id}.${key}`,\n                        payload: new Blob([stringifiedValue], {\n                            type: `application/json; length=${stringifiedValue.length}`,\n                        }),\n                    });\n                }\n                // encode the attachments\n                if (payload.id !== undefined) {\n                    const attachments = allAttachments[payload.id];\n                    if (attachments) {\n                        delete allAttachments[payload.id];\n                        for (const [name, attachment] of Object.entries(attachments)) {\n                            let contentType;\n                            let content;\n                            if (Array.isArray(attachment)) {\n                                [contentType, content] = attachment;\n                            }\n                            else {\n                                contentType = attachment.mimeType;\n                                content = attachment.data;\n                            }\n                            // Validate that the attachment name doesn't contain a '.'\n                            if (name.includes(\".\")) {\n                                console.warn(`Skipping attachment '${name}' for run ${payload.id}: Invalid attachment name. ` +\n                                    `Attachment names must not contain periods ('.'). Please rename the attachment and try again.`);\n                                continue;\n                            }\n                            accumulatedParts.push({\n                                name: `attachment.${payload.id}.${name}`,\n                                payload: new Blob([content], {\n                                    type: `${contentType}; length=${content.byteLength}`,\n                                }),\n                            });\n                        }\n                    }\n                }\n                // compute context\n                accumulatedContext.push(`trace=${payload.trace_id},id=${payload.id}`);\n            }\n        }\n        await this._sendMultipartRequest(accumulatedParts, accumulatedContext.join(\"; \"), options);\n    }\n    async _createNodeFetchBody(parts, boundary) {\n        // Create multipart form data manually using Blobs\n        const chunks = [];\n        for (const part of parts) {\n            // Add field boundary\n            chunks.push(new Blob([`--${boundary}\\r\\n`]));\n            chunks.push(new Blob([\n                `Content-Disposition: form-data; name=\"${part.name}\"\\r\\n`,\n                `Content-Type: ${part.payload.type}\\r\\n\\r\\n`,\n            ]));\n            chunks.push(part.payload);\n            chunks.push(new Blob([\"\\r\\n\"]));\n        }\n        // Add final boundary\n        chunks.push(new Blob([`--${boundary}--\\r\\n`]));\n        // Combine all chunks into a single Blob\n        const body = new Blob(chunks);\n        // Convert Blob to ArrayBuffer for compatibility\n        const arrayBuffer = await body.arrayBuffer();\n        return arrayBuffer;\n    }\n    async _createMultipartStream(parts, boundary) {\n        const encoder = new TextEncoder();\n        // Create a ReadableStream for streaming the multipart data\n        // Only do special handling if we're using node-fetch\n        const stream = new ReadableStream({\n            async start(controller) {\n                // Helper function to write a chunk to the stream\n                const writeChunk = async (chunk) => {\n                    if (typeof chunk === \"string\") {\n                        controller.enqueue(encoder.encode(chunk));\n                    }\n                    else {\n                        controller.enqueue(chunk);\n                    }\n                };\n                // Write each part to the stream\n                for (const part of parts) {\n                    // Write boundary and headers\n                    await writeChunk(`--${boundary}\\r\\n`);\n                    await writeChunk(`Content-Disposition: form-data; name=\"${part.name}\"\\r\\n`);\n                    await writeChunk(`Content-Type: ${part.payload.type}\\r\\n\\r\\n`);\n                    // Write the payload\n                    const payloadStream = part.payload.stream();\n                    const reader = payloadStream.getReader();\n                    try {\n                        let result;\n                        while (!(result = await reader.read()).done) {\n                            controller.enqueue(result.value);\n                        }\n                    }\n                    finally {\n                        reader.releaseLock();\n                    }\n                    await writeChunk(\"\\r\\n\");\n                }\n                // Write final boundary\n                await writeChunk(`--${boundary}--\\r\\n`);\n                controller.close();\n            },\n        });\n        return stream;\n    }\n    async _sendMultipartRequest(parts, context, options) {\n        // Create multipart form data boundary\n        const boundary = \"----LangSmithFormBoundary\" + Math.random().toString(36).slice(2);\n        const isNodeFetch = _globalFetchImplementationIsNodeFetch();\n        const buildBuffered = () => this._createNodeFetchBody(parts, boundary);\n        const buildStream = () => this._createMultipartStream(parts, boundary);\n        const send = async (body) => {\n            const headers = {\n                ...this.headers,\n                \"Content-Type\": `multipart/form-data; boundary=${boundary}`,\n            };\n            if (options?.apiKey !== undefined) {\n                headers[\"x-api-key\"] = options.apiKey;\n            }\n            return this.batchIngestCaller.call(_getFetchImplementation(this.debug), `${options?.apiUrl ?? this.apiUrl}/runs/multipart`, {\n                method: \"POST\",\n                headers,\n                body,\n                duplex: \"half\",\n                signal: AbortSignal.timeout(this.timeout_ms),\n                ...this.fetchOptions,\n            });\n        };\n        try {\n            let res;\n            let streamedAttempt = false;\n            // attempt stream only if not disabled and not using node-fetch\n            if (!isNodeFetch && !this.multipartStreamingDisabled) {\n                streamedAttempt = true;\n                res = await send(await buildStream());\n            }\n            else {\n                res = await send(await buildBuffered());\n            }\n            // if stream fails, fallback to buffered body\n            if ((!this.multipartStreamingDisabled || streamedAttempt) &&\n                res.status === 422 &&\n                (options?.apiUrl ?? this.apiUrl) !== DEFAULT_API_URL) {\n                console.warn(`Streaming multipart upload to ${options?.apiUrl ?? this.apiUrl}/runs/multipart failed. ` +\n                    `This usually means the host does not support chunked uploads. ` +\n                    `Retrying with a buffered upload for operation \"${context}\".`);\n                // Disable streaming for future requests\n                this.multipartStreamingDisabled = true;\n                // retry with fully-buffered body\n                res = await send(await buildBuffered());\n            }\n            // raise if still failing\n            await raiseForStatus(res, \"ingest multipart runs\", true);\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        }\n        catch (e) {\n            console.warn(`${e.message.trim()}\\n\\nContext: ${context}`);\n        }\n    }\n    async updateRun(runId, run, options) {\n        assertUuid(runId);\n        if (run.inputs) {\n            run.inputs = await this.processInputs(run.inputs);\n        }\n        if (run.outputs) {\n            run.outputs = await this.processOutputs(run.outputs);\n        }\n        // TODO: Untangle types\n        const data = { ...run, id: runId };\n        if (!this._filterForSampling([data], true).length) {\n            return;\n        }\n        if (this.autoBatchTracing &&\n            data.trace_id !== undefined &&\n            data.dotted_order !== undefined) {\n            const otelContext = this._cloneCurrentOTELContext();\n            if (run.end_time !== undefined &&\n                data.parent_run_id === undefined &&\n                this.blockOnRootRunFinalization &&\n                !this.manualFlushMode) {\n                // Trigger batches as soon as a root trace ends and wait to ensure trace finishes\n                // in serverless environments.\n                await this.processRunOperation({\n                    action: \"update\",\n                    item: data,\n                    otelContext,\n                    apiKey: options?.apiKey,\n                    apiUrl: options?.apiUrl,\n                }).catch(console.error);\n                return;\n            }\n            else {\n                void this.processRunOperation({\n                    action: \"update\",\n                    item: data,\n                    otelContext,\n                    apiKey: options?.apiKey,\n                    apiUrl: options?.apiUrl,\n                }).catch(console.error);\n            }\n            return;\n        }\n        const headers = {\n            ...this.headers,\n            \"Content-Type\": \"application/json\",\n        };\n        if (options?.apiKey !== undefined) {\n            headers[\"x-api-key\"] = options.apiKey;\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${options?.apiUrl ?? this.apiUrl}/runs/${runId}`, {\n            method: \"PATCH\",\n            headers,\n            body: serializePayloadForTracing(run, `Serializing payload to update run with id: ${runId}`),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"update run\", true);\n    }\n    async readRun(runId, { loadChildRuns } = { loadChildRuns: false }) {\n        assertUuid(runId);\n        let run = await this._get(`/runs/${runId}`);\n        if (loadChildRuns) {\n            run = await this._loadChildRuns(run);\n        }\n        return run;\n    }\n    async getRunUrl({ runId, run, projectOpts, }) {\n        if (run !== undefined) {\n            let sessionId;\n            if (run.session_id) {\n                sessionId = run.session_id;\n            }\n            else if (projectOpts?.projectName) {\n                sessionId = (await this.readProject({ projectName: projectOpts?.projectName })).id;\n            }\n            else if (projectOpts?.projectId) {\n                sessionId = projectOpts?.projectId;\n            }\n            else {\n                const project = await this.readProject({\n                    projectName: getLangSmithEnvironmentVariable(\"PROJECT\") || \"default\",\n                });\n                sessionId = project.id;\n            }\n            const tenantId = await this._getTenantId();\n            return `${this.getHostUrl()}/o/${tenantId}/projects/p/${sessionId}/r/${run.id}?poll=true`;\n        }\n        else if (runId !== undefined) {\n            const run_ = await this.readRun(runId);\n            if (!run_.app_path) {\n                throw new Error(`Run ${runId} has no app_path`);\n            }\n            const baseUrl = this.getHostUrl();\n            return `${baseUrl}${run_.app_path}`;\n        }\n        else {\n            throw new Error(\"Must provide either runId or run\");\n        }\n    }\n    async _loadChildRuns(run) {\n        const childRuns = await toArray(this.listRuns({\n            isRoot: false,\n            projectId: run.session_id,\n            traceId: run.trace_id,\n        }));\n        const treemap = {};\n        const runs = {};\n        // TODO: make dotted order required when the migration finishes\n        childRuns.sort((a, b) => (a?.dotted_order ?? \"\").localeCompare(b?.dotted_order ?? \"\"));\n        for (const childRun of childRuns) {\n            if (childRun.parent_run_id === null ||\n                childRun.parent_run_id === undefined) {\n                throw new Error(`Child run ${childRun.id} has no parent`);\n            }\n            if (childRun.dotted_order?.startsWith(run.dotted_order ?? \"\") &&\n                childRun.id !== run.id) {\n                if (!(childRun.parent_run_id in treemap)) {\n                    treemap[childRun.parent_run_id] = [];\n                }\n                treemap[childRun.parent_run_id].push(childRun);\n                runs[childRun.id] = childRun;\n            }\n        }\n        run.child_runs = treemap[run.id] || [];\n        for (const runId in treemap) {\n            if (runId !== run.id) {\n                runs[runId].child_runs = treemap[runId];\n            }\n        }\n        return run;\n    }\n    /**\n     * List runs from the LangSmith server.\n     * @param projectId - The ID of the project to filter by.\n     * @param projectName - The name of the project to filter by.\n     * @param parentRunId - The ID of the parent run to filter by.\n     * @param traceId - The ID of the trace to filter by.\n     * @param referenceExampleId - The ID of the reference example to filter by.\n     * @param startTime - The start time to filter by.\n     * @param isRoot - Indicates whether to only return root runs.\n     * @param runType - The run type to filter by.\n     * @param error - Indicates whether to filter by error runs.\n     * @param id - The ID of the run to filter by.\n     * @param query - The query string to filter by.\n     * @param filter - The filter string to apply to the run spans.\n     * @param traceFilter - The filter string to apply on the root run of the trace.\n     * @param treeFilter - The filter string to apply on other runs in the trace.\n     * @param limit - The maximum number of runs to retrieve.\n     * @returns {AsyncIterable<Run>} - The runs.\n     *\n     * @example\n     * // List all runs in a project\n     * const projectRuns = client.listRuns({ projectName: \"<your_project>\" });\n     *\n     * @example\n     * // List LLM and Chat runs in the last 24 hours\n     * const todaysLLMRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   start_time: new Date(Date.now() - 24 * 60 * 60 * 1000),\n     *   run_type: \"llm\",\n     * });\n     *\n     * @example\n     * // List traces in a project\n     * const rootRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   execution_order: 1,\n     * });\n     *\n     * @example\n     * // List runs without errors\n     * const correctRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   error: false,\n     * });\n     *\n     * @example\n     * // List runs by run ID\n     * const runIds = [\n     *   \"a36092d2-4ad5-4fb4-9c0d-0dba9a2ed836\",\n     *   \"9398e6be-964f-4aa4-8ae9-ad78cd4b7074\",\n     * ];\n     * const selectedRuns = client.listRuns({ run_ids: runIds });\n     *\n     * @example\n     * // List all \"chain\" type runs that took more than 10 seconds and had `total_tokens` greater than 5000\n     * const chainRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   filter: 'and(eq(run_type, \"chain\"), gt(latency, 10), gt(total_tokens, 5000))',\n     * });\n     *\n     * @example\n     * // List all runs called \"extractor\" whose root of the trace was assigned feedback \"user_score\" score of 1\n     * const goodExtractorRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   filter: 'eq(name, \"extractor\")',\n     *   traceFilter: 'and(eq(feedback_key, \"user_score\"), eq(feedback_score, 1))',\n     * });\n     *\n     * @example\n     * // List all runs that started after a specific timestamp and either have \"error\" not equal to null or a \"Correctness\" feedback score equal to 0\n     * const complexRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   filter: 'and(gt(start_time, \"2023-07-15T12:34:56Z\"), or(neq(error, null), and(eq(feedback_key, \"Correctness\"), eq(feedback_score, 0.0))))',\n     * });\n     *\n     * @example\n     * // List all runs where `tags` include \"experimental\" or \"beta\" and `latency` is greater than 2 seconds\n     * const taggedRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   filter: 'and(or(has(tags, \"experimental\"), has(tags, \"beta\")), gt(latency, 2))',\n     * });\n     */\n    async *listRuns(props) {\n        const { projectId, projectName, parentRunId, traceId, referenceExampleId, startTime, executionOrder, isRoot, runType, error, id, query, filter, traceFilter, treeFilter, limit, select, order, } = props;\n        let projectIds = [];\n        if (projectId) {\n            projectIds = Array.isArray(projectId) ? projectId : [projectId];\n        }\n        if (projectName) {\n            const projectNames = Array.isArray(projectName)\n                ? projectName\n                : [projectName];\n            const projectIds_ = await Promise.all(projectNames.map((name) => this.readProject({ projectName: name }).then((project) => project.id)));\n            projectIds.push(...projectIds_);\n        }\n        const default_select = [\n            \"app_path\",\n            \"completion_cost\",\n            \"completion_tokens\",\n            \"dotted_order\",\n            \"end_time\",\n            \"error\",\n            \"events\",\n            \"extra\",\n            \"feedback_stats\",\n            \"first_token_time\",\n            \"id\",\n            \"inputs\",\n            \"name\",\n            \"outputs\",\n            \"parent_run_id\",\n            \"parent_run_ids\",\n            \"prompt_cost\",\n            \"prompt_tokens\",\n            \"reference_example_id\",\n            \"run_type\",\n            \"session_id\",\n            \"start_time\",\n            \"status\",\n            \"tags\",\n            \"total_cost\",\n            \"total_tokens\",\n            \"trace_id\",\n        ];\n        const body = {\n            session: projectIds.length ? projectIds : null,\n            run_type: runType,\n            reference_example: referenceExampleId,\n            query,\n            filter,\n            trace_filter: traceFilter,\n            tree_filter: treeFilter,\n            execution_order: executionOrder,\n            parent_run: parentRunId,\n            start_time: startTime ? startTime.toISOString() : null,\n            error,\n            id,\n            limit,\n            trace: traceId,\n            select: select ? select : default_select,\n            is_root: isRoot,\n            order,\n        };\n        let runsYielded = 0;\n        for await (const runs of this._getCursorPaginatedList(\"/runs/query\", body)) {\n            if (limit) {\n                if (runsYielded >= limit) {\n                    break;\n                }\n                if (runs.length + runsYielded > limit) {\n                    const newRuns = runs.slice(0, limit - runsYielded);\n                    yield* newRuns;\n                    break;\n                }\n                runsYielded += runs.length;\n                yield* runs;\n            }\n            else {\n                yield* runs;\n            }\n        }\n    }\n    async *listGroupRuns(props) {\n        const { projectId, projectName, groupBy, filter, startTime, endTime, limit, offset, } = props;\n        const sessionId = projectId || (await this.readProject({ projectName })).id;\n        const baseBody = {\n            session_id: sessionId,\n            group_by: groupBy,\n            filter,\n            start_time: startTime ? startTime.toISOString() : null,\n            end_time: endTime ? endTime.toISOString() : null,\n            limit: Number(limit) || 100,\n        };\n        let currentOffset = Number(offset) || 0;\n        const path = \"/runs/group\";\n        const url = `${this.apiUrl}${path}`;\n        while (true) {\n            const currentBody = {\n                ...baseBody,\n                offset: currentOffset,\n            };\n            // Remove undefined values from the payload\n            const filteredPayload = Object.fromEntries(Object.entries(currentBody).filter(([_, value]) => value !== undefined));\n            const response = await this.caller.call(_getFetchImplementation(), url, {\n                method: \"POST\",\n                headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n                body: JSON.stringify(filteredPayload),\n                signal: AbortSignal.timeout(this.timeout_ms),\n                ...this.fetchOptions,\n            });\n            await raiseForStatus(response, `Failed to fetch ${path}`);\n            const items = await response.json();\n            const { groups, total } = items;\n            if (groups.length === 0) {\n                break;\n            }\n            for (const thread of groups) {\n                yield thread;\n            }\n            currentOffset += groups.length;\n            if (currentOffset >= total) {\n                break;\n            }\n        }\n    }\n    async getRunStats({ id, trace, parentRun, runType, projectNames, projectIds, referenceExampleIds, startTime, endTime, error, query, filter, traceFilter, treeFilter, isRoot, dataSourceType, }) {\n        let projectIds_ = projectIds || [];\n        if (projectNames) {\n            projectIds_ = [\n                ...(projectIds || []),\n                ...(await Promise.all(projectNames.map((name) => this.readProject({ projectName: name }).then((project) => project.id)))),\n            ];\n        }\n        const payload = {\n            id,\n            trace,\n            parent_run: parentRun,\n            run_type: runType,\n            session: projectIds_,\n            reference_example: referenceExampleIds,\n            start_time: startTime,\n            end_time: endTime,\n            error,\n            query,\n            filter,\n            trace_filter: traceFilter,\n            tree_filter: treeFilter,\n            is_root: isRoot,\n            data_source_type: dataSourceType,\n        };\n        // Remove undefined values from the payload\n        const filteredPayload = Object.fromEntries(Object.entries(payload).filter(([_, value]) => value !== undefined));\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/runs/stats`, {\n            method: \"POST\",\n            headers: this.headers,\n            body: JSON.stringify(filteredPayload),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const result = await response.json();\n        return result;\n    }\n    async shareRun(runId, { shareId } = {}) {\n        const data = {\n            run_id: runId,\n            share_token: shareId || uuid.v4(),\n        };\n        assertUuid(runId);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/runs/${runId}/share`, {\n            method: \"PUT\",\n            headers: this.headers,\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const result = await response.json();\n        if (result === null || !(\"share_token\" in result)) {\n            throw new Error(\"Invalid response from server\");\n        }\n        return `${this.getHostUrl()}/public/${result[\"share_token\"]}/r`;\n    }\n    async unshareRun(runId) {\n        assertUuid(runId);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/runs/${runId}/share`, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"unshare run\", true);\n    }\n    async readRunSharedLink(runId) {\n        assertUuid(runId);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/runs/${runId}/share`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const result = await response.json();\n        if (result === null || !(\"share_token\" in result)) {\n            return undefined;\n        }\n        return `${this.getHostUrl()}/public/${result[\"share_token\"]}/r`;\n    }\n    async listSharedRuns(shareToken, { runIds, } = {}) {\n        const queryParams = new URLSearchParams({\n            share_token: shareToken,\n        });\n        if (runIds !== undefined) {\n            for (const runId of runIds) {\n                queryParams.append(\"id\", runId);\n            }\n        }\n        assertUuid(shareToken);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/public/${shareToken}/runs${queryParams}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const runs = await response.json();\n        return runs;\n    }\n    async readDatasetSharedSchema(datasetId, datasetName) {\n        if (!datasetId && !datasetName) {\n            throw new Error(\"Either datasetId or datasetName must be given\");\n        }\n        if (!datasetId) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId = dataset.id;\n        }\n        assertUuid(datasetId);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets/${datasetId}/share`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const shareSchema = await response.json();\n        shareSchema.url = `${this.getHostUrl()}/public/${shareSchema.share_token}/d`;\n        return shareSchema;\n    }\n    async shareDataset(datasetId, datasetName) {\n        if (!datasetId && !datasetName) {\n            throw new Error(\"Either datasetId or datasetName must be given\");\n        }\n        if (!datasetId) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId = dataset.id;\n        }\n        const data = {\n            dataset_id: datasetId,\n        };\n        assertUuid(datasetId);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets/${datasetId}/share`, {\n            method: \"PUT\",\n            headers: this.headers,\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const shareSchema = await response.json();\n        shareSchema.url = `${this.getHostUrl()}/public/${shareSchema.share_token}/d`;\n        return shareSchema;\n    }\n    async unshareDataset(datasetId) {\n        assertUuid(datasetId);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets/${datasetId}/share`, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"unshare dataset\", true);\n    }\n    async readSharedDataset(shareToken) {\n        assertUuid(shareToken);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/public/${shareToken}/datasets`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const dataset = await response.json();\n        return dataset;\n    }\n    /**\n     * Get shared examples.\n     *\n     * @param {string} shareToken The share token to get examples for. A share token is the UUID (or LangSmith URL, including UUID) generated when explicitly marking an example as public.\n     * @param {Object} [options] Additional options for listing the examples.\n     * @param {string[] | undefined} [options.exampleIds] A list of example IDs to filter by.\n     * @returns {Promise<Example[]>} The shared examples.\n     */\n    async listSharedExamples(shareToken, options) {\n        const params = {};\n        if (options?.exampleIds) {\n            params.id = options.exampleIds;\n        }\n        const urlParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n            if (Array.isArray(value)) {\n                value.forEach((v) => urlParams.append(key, v));\n            }\n            else {\n                urlParams.append(key, value);\n            }\n        });\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/public/${shareToken}/examples?${urlParams.toString()}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const result = await response.json();\n        if (!response.ok) {\n            if (\"detail\" in result) {\n                throw new Error(`Failed to list shared examples.\\nStatus: ${response.status}\\nMessage: ${Array.isArray(result.detail)\n                    ? result.detail.join(\"\\n\")\n                    : \"Unspecified error\"}`);\n            }\n            throw new Error(`Failed to list shared examples: ${response.status} ${response.statusText}`);\n        }\n        return result.map((example) => ({\n            ...example,\n            _hostUrl: this.getHostUrl(),\n        }));\n    }\n    async createProject({ projectName, description = null, metadata = null, upsert = false, projectExtra = null, referenceDatasetId = null, }) {\n        const upsert_ = upsert ? `?upsert=true` : \"\";\n        const endpoint = `${this.apiUrl}/sessions${upsert_}`;\n        const extra = projectExtra || {};\n        if (metadata) {\n            extra[\"metadata\"] = metadata;\n        }\n        const body = {\n            name: projectName,\n            extra,\n            description,\n        };\n        if (referenceDatasetId !== null) {\n            body[\"reference_dataset_id\"] = referenceDatasetId;\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), endpoint, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(body),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"create project\");\n        const result = await response.json();\n        return result;\n    }\n    async updateProject(projectId, { name = null, description = null, metadata = null, projectExtra = null, endTime = null, }) {\n        const endpoint = `${this.apiUrl}/sessions/${projectId}`;\n        let extra = projectExtra;\n        if (metadata) {\n            extra = { ...(extra || {}), metadata };\n        }\n        const body = {\n            name,\n            extra,\n            description,\n            end_time: endTime ? new Date(endTime).toISOString() : null,\n        };\n        const response = await this.caller.call(_getFetchImplementation(this.debug), endpoint, {\n            method: \"PATCH\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(body),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"update project\");\n        const result = await response.json();\n        return result;\n    }\n    async hasProject({ projectId, projectName, }) {\n        // TODO: Add a head request\n        let path = \"/sessions\";\n        const params = new URLSearchParams();\n        if (projectId !== undefined && projectName !== undefined) {\n            throw new Error(\"Must provide either projectName or projectId, not both\");\n        }\n        else if (projectId !== undefined) {\n            assertUuid(projectId);\n            path += `/${projectId}`;\n        }\n        else if (projectName !== undefined) {\n            params.append(\"name\", projectName);\n        }\n        else {\n            throw new Error(\"Must provide projectName or projectId\");\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}${path}?${params}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        // consume the response body to release the connection\n        // https://undici.nodejs.org/#/?id=garbage-collection\n        try {\n            const result = await response.json();\n            if (!response.ok) {\n                return false;\n            }\n            // If it's OK and we're querying by name, need to check the list is not empty\n            if (Array.isArray(result)) {\n                return result.length > 0;\n            }\n            // projectId querying\n            return true;\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    async readProject({ projectId, projectName, includeStats, }) {\n        let path = \"/sessions\";\n        const params = new URLSearchParams();\n        if (projectId !== undefined && projectName !== undefined) {\n            throw new Error(\"Must provide either projectName or projectId, not both\");\n        }\n        else if (projectId !== undefined) {\n            assertUuid(projectId);\n            path += `/${projectId}`;\n        }\n        else if (projectName !== undefined) {\n            params.append(\"name\", projectName);\n        }\n        else {\n            throw new Error(\"Must provide projectName or projectId\");\n        }\n        if (includeStats !== undefined) {\n            params.append(\"include_stats\", includeStats.toString());\n        }\n        const response = await this._get(path, params);\n        let result;\n        if (Array.isArray(response)) {\n            if (response.length === 0) {\n                throw new Error(`Project[id=${projectId}, name=${projectName}] not found`);\n            }\n            result = response[0];\n        }\n        else {\n            result = response;\n        }\n        return result;\n    }\n    async getProjectUrl({ projectId, projectName, }) {\n        if (projectId === undefined && projectName === undefined) {\n            throw new Error(\"Must provide either projectName or projectId\");\n        }\n        const project = await this.readProject({ projectId, projectName });\n        const tenantId = await this._getTenantId();\n        return `${this.getHostUrl()}/o/${tenantId}/projects/p/${project.id}`;\n    }\n    async getDatasetUrl({ datasetId, datasetName, }) {\n        if (datasetId === undefined && datasetName === undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        const dataset = await this.readDataset({ datasetId, datasetName });\n        const tenantId = await this._getTenantId();\n        return `${this.getHostUrl()}/o/${tenantId}/datasets/${dataset.id}`;\n    }\n    async _getTenantId() {\n        if (this._tenantId !== null) {\n            return this._tenantId;\n        }\n        const queryParams = new URLSearchParams({ limit: \"1\" });\n        for await (const projects of this._getPaginated(\"/sessions\", queryParams)) {\n            this._tenantId = projects[0].tenant_id;\n            return projects[0].tenant_id;\n        }\n        throw new Error(\"No projects found to resolve tenant.\");\n    }\n    async *listProjects({ projectIds, name, nameContains, referenceDatasetId, referenceDatasetName, referenceFree, metadata, } = {}) {\n        const params = new URLSearchParams();\n        if (projectIds !== undefined) {\n            for (const projectId of projectIds) {\n                params.append(\"id\", projectId);\n            }\n        }\n        if (name !== undefined) {\n            params.append(\"name\", name);\n        }\n        if (nameContains !== undefined) {\n            params.append(\"name_contains\", nameContains);\n        }\n        if (referenceDatasetId !== undefined) {\n            params.append(\"reference_dataset\", referenceDatasetId);\n        }\n        else if (referenceDatasetName !== undefined) {\n            const dataset = await this.readDataset({\n                datasetName: referenceDatasetName,\n            });\n            params.append(\"reference_dataset\", dataset.id);\n        }\n        if (referenceFree !== undefined) {\n            params.append(\"reference_free\", referenceFree.toString());\n        }\n        if (metadata !== undefined) {\n            params.append(\"metadata\", JSON.stringify(metadata));\n        }\n        for await (const projects of this._getPaginated(\"/sessions\", params)) {\n            yield* projects;\n        }\n    }\n    async deleteProject({ projectId, projectName, }) {\n        let projectId_;\n        if (projectId === undefined && projectName === undefined) {\n            throw new Error(\"Must provide projectName or projectId\");\n        }\n        else if (projectId !== undefined && projectName !== undefined) {\n            throw new Error(\"Must provide either projectName or projectId, not both\");\n        }\n        else if (projectId === undefined) {\n            projectId_ = (await this.readProject({ projectName })).id;\n        }\n        else {\n            projectId_ = projectId;\n        }\n        assertUuid(projectId_);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/sessions/${projectId_}`, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, `delete session ${projectId_} (${projectName})`, true);\n    }\n    async uploadCsv({ csvFile, fileName, inputKeys, outputKeys, description, dataType, name, }) {\n        const url = `${this.apiUrl}/datasets/upload`;\n        const formData = new FormData();\n        formData.append(\"file\", csvFile, fileName);\n        inputKeys.forEach((key) => {\n            formData.append(\"input_keys\", key);\n        });\n        outputKeys.forEach((key) => {\n            formData.append(\"output_keys\", key);\n        });\n        if (description) {\n            formData.append(\"description\", description);\n        }\n        if (dataType) {\n            formData.append(\"data_type\", dataType);\n        }\n        if (name) {\n            formData.append(\"name\", name);\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), url, {\n            method: \"POST\",\n            headers: this.headers,\n            body: formData,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"upload CSV\");\n        const result = await response.json();\n        return result;\n    }\n    async createDataset(name, { description, dataType, inputsSchema, outputsSchema, metadata, } = {}) {\n        const body = {\n            name,\n            description,\n            extra: metadata ? { metadata } : undefined,\n        };\n        if (dataType) {\n            body.data_type = dataType;\n        }\n        if (inputsSchema) {\n            body.inputs_schema_definition = inputsSchema;\n        }\n        if (outputsSchema) {\n            body.outputs_schema_definition = outputsSchema;\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(body),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"create dataset\");\n        const result = await response.json();\n        return result;\n    }\n    async readDataset({ datasetId, datasetName, }) {\n        let path = \"/datasets\";\n        // limit to 1 result\n        const params = new URLSearchParams({ limit: \"1\" });\n        if (datasetId && datasetName) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId) {\n            assertUuid(datasetId);\n            path += `/${datasetId}`;\n        }\n        else if (datasetName) {\n            params.append(\"name\", datasetName);\n        }\n        else {\n            throw new Error(\"Must provide datasetName or datasetId\");\n        }\n        const response = await this._get(path, params);\n        let result;\n        if (Array.isArray(response)) {\n            if (response.length === 0) {\n                throw new Error(`Dataset[id=${datasetId}, name=${datasetName}] not found`);\n            }\n            result = response[0];\n        }\n        else {\n            result = response;\n        }\n        return result;\n    }\n    async hasDataset({ datasetId, datasetName, }) {\n        try {\n            await this.readDataset({ datasetId, datasetName });\n            return true;\n        }\n        catch (e) {\n            if (\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            e instanceof Error &&\n                e.message.toLocaleLowerCase().includes(\"not found\")) {\n                return false;\n            }\n            throw e;\n        }\n    }\n    async diffDatasetVersions({ datasetId, datasetName, fromVersion, toVersion, }) {\n        let datasetId_ = datasetId;\n        if (datasetId_ === undefined && datasetName === undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        else if (datasetId_ !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId_ === undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        const urlParams = new URLSearchParams({\n            from_version: typeof fromVersion === \"string\"\n                ? fromVersion\n                : fromVersion.toISOString(),\n            to_version: typeof toVersion === \"string\" ? toVersion : toVersion.toISOString(),\n        });\n        const response = await this._get(`/datasets/${datasetId_}/versions/diff`, urlParams);\n        return response;\n    }\n    async readDatasetOpenaiFinetuning({ datasetId, datasetName, }) {\n        const path = \"/datasets\";\n        if (datasetId !== undefined) {\n            // do nothing\n        }\n        else if (datasetName !== undefined) {\n            datasetId = (await this.readDataset({ datasetName })).id;\n        }\n        else {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        const response = await this._getResponse(`${path}/${datasetId}/openai_ft`);\n        const datasetText = await response.text();\n        const dataset = datasetText\n            .trim()\n            .split(\"\\n\")\n            .map((line) => JSON.parse(line));\n        return dataset;\n    }\n    async *listDatasets({ limit = 100, offset = 0, datasetIds, datasetName, datasetNameContains, metadata, } = {}) {\n        const path = \"/datasets\";\n        const params = new URLSearchParams({\n            limit: limit.toString(),\n            offset: offset.toString(),\n        });\n        if (datasetIds !== undefined) {\n            for (const id_ of datasetIds) {\n                params.append(\"id\", id_);\n            }\n        }\n        if (datasetName !== undefined) {\n            params.append(\"name\", datasetName);\n        }\n        if (datasetNameContains !== undefined) {\n            params.append(\"name_contains\", datasetNameContains);\n        }\n        if (metadata !== undefined) {\n            params.append(\"metadata\", JSON.stringify(metadata));\n        }\n        for await (const datasets of this._getPaginated(path, params)) {\n            yield* datasets;\n        }\n    }\n    /**\n     * Update a dataset\n     * @param props The dataset details to update\n     * @returns The updated dataset\n     */\n    async updateDataset(props) {\n        const { datasetId, datasetName, ...update } = props;\n        if (!datasetId && !datasetName) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        const _datasetId = datasetId ?? (await this.readDataset({ datasetName })).id;\n        assertUuid(_datasetId);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets/${_datasetId}`, {\n            method: \"PATCH\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(update),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"update dataset\");\n        return (await response.json());\n    }\n    /**\n     * Updates a tag on a dataset.\n     *\n     * If the tag is already assigned to a different version of this dataset,\n     * the tag will be moved to the new version. The as_of parameter is used to\n     * determine which version of the dataset to apply the new tags to.\n     *\n     * It must be an exact version of the dataset to succeed. You can\n     * use the \"readDatasetVersion\" method to find the exact version\n     * to apply the tags to.\n     * @param params.datasetId The ID of the dataset to update. Must be provided if \"datasetName\" is not provided.\n     * @param params.datasetName The name of the dataset to update. Must be provided if \"datasetId\" is not provided.\n     * @param params.asOf The timestamp of the dataset to apply the new tags to.\n     * @param params.tag The new tag to apply to the dataset.\n     */\n    async updateDatasetTag(props) {\n        const { datasetId, datasetName, asOf, tag } = props;\n        if (!datasetId && !datasetName) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        const _datasetId = datasetId ?? (await this.readDataset({ datasetName })).id;\n        assertUuid(_datasetId);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets/${_datasetId}/tags`, {\n            method: \"PUT\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify({\n                as_of: typeof asOf === \"string\" ? asOf : asOf.toISOString(),\n                tag,\n            }),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"update dataset tags\");\n    }\n    async deleteDataset({ datasetId, datasetName, }) {\n        let path = \"/datasets\";\n        let datasetId_ = datasetId;\n        if (datasetId !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetName !== undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        if (datasetId_ !== undefined) {\n            assertUuid(datasetId_);\n            path += `/${datasetId_}`;\n        }\n        else {\n            throw new Error(\"Must provide datasetName or datasetId\");\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), this.apiUrl + path, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, `delete ${path}`);\n        await response.json();\n    }\n    async indexDataset({ datasetId, datasetName, tag, }) {\n        let datasetId_ = datasetId;\n        if (!datasetId_ && !datasetName) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        else if (datasetId_ && datasetName) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (!datasetId_) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        assertUuid(datasetId_);\n        const data = {\n            tag: tag,\n        };\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets/${datasetId_}/index`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"index dataset\");\n        await response.json();\n    }\n    /**\n     * Lets you run a similarity search query on a dataset.\n     *\n     * Requires the dataset to be indexed. Please see the `indexDataset` method to set up indexing.\n     *\n     * @param inputs      The input on which to run the similarity search. Must have the\n     *                    same schema as the dataset.\n     *\n     * @param datasetId   The dataset to search for similar examples.\n     *\n     * @param limit       The maximum number of examples to return. Will return the top `limit` most\n     *                    similar examples in order of most similar to least similar. If no similar\n     *                    examples are found, random examples will be returned.\n     *\n     * @param filter      A filter string to apply to the search. Only examples will be returned that\n     *                    match the filter string. Some examples of filters\n     *\n     *                    - eq(metadata.mykey, \"value\")\n     *                    - and(neq(metadata.my.nested.key, \"value\"), neq(metadata.mykey, \"value\"))\n     *                    - or(eq(metadata.mykey, \"value\"), eq(metadata.mykey, \"othervalue\"))\n     *\n     * @returns           A list of similar examples.\n     *\n     *\n     * @example\n     * dataset_id = \"123e4567-e89b-12d3-a456-************\"\n     * inputs = {\"text\": \"How many people live in Berlin?\"}\n     * limit = 5\n     * examples = await client.similarExamples(inputs, dataset_id, limit)\n     */\n    async similarExamples(inputs, datasetId, limit, { filter, } = {}) {\n        const data = {\n            limit: limit,\n            inputs: inputs,\n        };\n        if (filter !== undefined) {\n            data[\"filter\"] = filter;\n        }\n        assertUuid(datasetId);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets/${datasetId}/search`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"fetch similar examples\");\n        const result = await response.json();\n        return result[\"examples\"];\n    }\n    async createExample(inputsOrUpdate, outputs, options) {\n        if (isExampleCreate(inputsOrUpdate)) {\n            if (outputs !== undefined || options !== undefined) {\n                throw new Error(\"Cannot provide outputs or options when using ExampleCreate object\");\n            }\n        }\n        let datasetId_ = outputs ? options?.datasetId : inputsOrUpdate.dataset_id;\n        const datasetName_ = outputs\n            ? options?.datasetName\n            : inputsOrUpdate.dataset_name;\n        if (datasetId_ === undefined && datasetName_ === undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        else if (datasetId_ !== undefined && datasetName_ !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId_ === undefined) {\n            const dataset = await this.readDataset({ datasetName: datasetName_ });\n            datasetId_ = dataset.id;\n        }\n        const createdAt_ = (outputs ? options?.createdAt : inputsOrUpdate.created_at) || new Date();\n        let data;\n        if (!isExampleCreate(inputsOrUpdate)) {\n            data = {\n                inputs: inputsOrUpdate,\n                outputs,\n                created_at: createdAt_?.toISOString(),\n                id: options?.exampleId,\n                metadata: options?.metadata,\n                split: options?.split,\n                source_run_id: options?.sourceRunId,\n                use_source_run_io: options?.useSourceRunIO,\n                use_source_run_attachments: options?.useSourceRunAttachments,\n                attachments: options?.attachments,\n            };\n        }\n        else {\n            data = inputsOrUpdate;\n        }\n        const response = await this._uploadExamplesMultipart(datasetId_, [data]);\n        const example = await this.readExample(response.example_ids?.[0] ?? uuid.v4());\n        return example;\n    }\n    async createExamples(propsOrUploads) {\n        if (Array.isArray(propsOrUploads)) {\n            if (propsOrUploads.length === 0) {\n                return [];\n            }\n            const uploads = propsOrUploads;\n            let datasetId_ = uploads[0].dataset_id;\n            const datasetName_ = uploads[0].dataset_name;\n            if (datasetId_ === undefined && datasetName_ === undefined) {\n                throw new Error(\"Must provide either datasetName or datasetId\");\n            }\n            else if (datasetId_ !== undefined && datasetName_ !== undefined) {\n                throw new Error(\"Must provide either datasetName or datasetId, not both\");\n            }\n            else if (datasetId_ === undefined) {\n                const dataset = await this.readDataset({ datasetName: datasetName_ });\n                datasetId_ = dataset.id;\n            }\n            const response = await this._uploadExamplesMultipart(datasetId_, uploads);\n            const examples = await Promise.all(response.example_ids.map((id) => this.readExample(id)));\n            return examples;\n        }\n        const { inputs, outputs, metadata, splits, sourceRunIds, useSourceRunIOs, useSourceRunAttachments, attachments, exampleIds, datasetId, datasetName, } = propsOrUploads;\n        if (inputs === undefined) {\n            throw new Error(\"Must provide inputs when using legacy parameters\");\n        }\n        let datasetId_ = datasetId;\n        const datasetName_ = datasetName;\n        if (datasetId_ === undefined && datasetName_ === undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        else if (datasetId_ !== undefined && datasetName_ !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId_ === undefined) {\n            const dataset = await this.readDataset({ datasetName: datasetName_ });\n            datasetId_ = dataset.id;\n        }\n        const formattedExamples = inputs.map((input, idx) => {\n            return {\n                dataset_id: datasetId_,\n                inputs: input,\n                outputs: outputs?.[idx],\n                metadata: metadata?.[idx],\n                split: splits?.[idx],\n                id: exampleIds?.[idx],\n                attachments: attachments?.[idx],\n                source_run_id: sourceRunIds?.[idx],\n                use_source_run_io: useSourceRunIOs?.[idx],\n                use_source_run_attachments: useSourceRunAttachments?.[idx],\n            };\n        });\n        const response = await this._uploadExamplesMultipart(datasetId_, formattedExamples);\n        const examples = await Promise.all(response.example_ids.map((id) => this.readExample(id)));\n        return examples;\n    }\n    async createLLMExample(input, generation, options) {\n        return this.createExample({ input }, { output: generation }, options);\n    }\n    async createChatExample(input, generations, options) {\n        const finalInput = input.map((message) => {\n            if (isLangChainMessage(message)) {\n                return convertLangChainMessageToExample(message);\n            }\n            return message;\n        });\n        const finalOutput = isLangChainMessage(generations)\n            ? convertLangChainMessageToExample(generations)\n            : generations;\n        return this.createExample({ input: finalInput }, { output: finalOutput }, options);\n    }\n    async readExample(exampleId) {\n        assertUuid(exampleId);\n        const path = `/examples/${exampleId}`;\n        const rawExample = await this._get(path);\n        const { attachment_urls, ...rest } = rawExample;\n        const example = rest;\n        if (attachment_urls) {\n            example.attachments = Object.entries(attachment_urls).reduce((acc, [key, value]) => {\n                acc[key.slice(\"attachment.\".length)] = {\n                    presigned_url: value.presigned_url,\n                    mime_type: value.mime_type,\n                };\n                return acc;\n            }, {});\n        }\n        return example;\n    }\n    async *listExamples({ datasetId, datasetName, exampleIds, asOf, splits, inlineS3Urls, metadata, limit, offset, filter, includeAttachments, } = {}) {\n        let datasetId_;\n        if (datasetId !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId !== undefined) {\n            datasetId_ = datasetId;\n        }\n        else if (datasetName !== undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        else {\n            throw new Error(\"Must provide a datasetName or datasetId\");\n        }\n        const params = new URLSearchParams({ dataset: datasetId_ });\n        const dataset_version = asOf\n            ? typeof asOf === \"string\"\n                ? asOf\n                : asOf?.toISOString()\n            : undefined;\n        if (dataset_version) {\n            params.append(\"as_of\", dataset_version);\n        }\n        const inlineS3Urls_ = inlineS3Urls ?? true;\n        params.append(\"inline_s3_urls\", inlineS3Urls_.toString());\n        if (exampleIds !== undefined) {\n            for (const id_ of exampleIds) {\n                params.append(\"id\", id_);\n            }\n        }\n        if (splits !== undefined) {\n            for (const split of splits) {\n                params.append(\"splits\", split);\n            }\n        }\n        if (metadata !== undefined) {\n            const serializedMetadata = JSON.stringify(metadata);\n            params.append(\"metadata\", serializedMetadata);\n        }\n        if (limit !== undefined) {\n            params.append(\"limit\", limit.toString());\n        }\n        if (offset !== undefined) {\n            params.append(\"offset\", offset.toString());\n        }\n        if (filter !== undefined) {\n            params.append(\"filter\", filter);\n        }\n        if (includeAttachments === true) {\n            [\"attachment_urls\", \"outputs\", \"metadata\"].forEach((field) => params.append(\"select\", field));\n        }\n        let i = 0;\n        for await (const rawExamples of this._getPaginated(\"/examples\", params)) {\n            for (const rawExample of rawExamples) {\n                const { attachment_urls, ...rest } = rawExample;\n                const example = rest;\n                if (attachment_urls) {\n                    example.attachments = Object.entries(attachment_urls).reduce((acc, [key, value]) => {\n                        acc[key.slice(\"attachment.\".length)] = {\n                            presigned_url: value.presigned_url,\n                            mime_type: value.mime_type || undefined,\n                        };\n                        return acc;\n                    }, {});\n                }\n                yield example;\n                i++;\n            }\n            if (limit !== undefined && i >= limit) {\n                break;\n            }\n        }\n    }\n    async deleteExample(exampleId) {\n        assertUuid(exampleId);\n        const path = `/examples/${exampleId}`;\n        const response = await this.caller.call(_getFetchImplementation(this.debug), this.apiUrl + path, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, `delete ${path}`);\n        await response.json();\n    }\n    async updateExample(exampleIdOrUpdate, update) {\n        let exampleId;\n        if (update) {\n            exampleId = exampleIdOrUpdate;\n        }\n        else {\n            exampleId = exampleIdOrUpdate.id;\n        }\n        assertUuid(exampleId);\n        let updateToUse;\n        if (update) {\n            updateToUse = { id: exampleId, ...update };\n        }\n        else {\n            updateToUse = exampleIdOrUpdate;\n        }\n        let datasetId;\n        if (updateToUse.dataset_id !== undefined) {\n            datasetId = updateToUse.dataset_id;\n        }\n        else {\n            const example = await this.readExample(exampleId);\n            datasetId = example.dataset_id;\n        }\n        return this._updateExamplesMultipart(datasetId, [updateToUse]);\n    }\n    async updateExamples(update) {\n        // We will naively get dataset id from first example and assume it works for all\n        let datasetId;\n        if (update[0].dataset_id === undefined) {\n            const example = await this.readExample(update[0].id);\n            datasetId = example.dataset_id;\n        }\n        else {\n            datasetId = update[0].dataset_id;\n        }\n        return this._updateExamplesMultipart(datasetId, update);\n    }\n    /**\n     * Get dataset version by closest date or exact tag.\n     *\n     * Use this to resolve the nearest version to a given timestamp or for a given tag.\n     *\n     * @param options The options for getting the dataset version\n     * @param options.datasetId The ID of the dataset\n     * @param options.datasetName The name of the dataset\n     * @param options.asOf The timestamp of the dataset to retrieve\n     * @param options.tag The tag of the dataset to retrieve\n     * @returns The dataset version\n     */\n    async readDatasetVersion({ datasetId, datasetName, asOf, tag, }) {\n        let resolvedDatasetId;\n        if (!datasetId) {\n            const dataset = await this.readDataset({ datasetName });\n            resolvedDatasetId = dataset.id;\n        }\n        else {\n            resolvedDatasetId = datasetId;\n        }\n        assertUuid(resolvedDatasetId);\n        if ((asOf && tag) || (!asOf && !tag)) {\n            throw new Error(\"Exactly one of asOf and tag must be specified.\");\n        }\n        const params = new URLSearchParams();\n        if (asOf !== undefined) {\n            params.append(\"as_of\", typeof asOf === \"string\" ? asOf : asOf.toISOString());\n        }\n        if (tag !== undefined) {\n            params.append(\"tag\", tag);\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets/${resolvedDatasetId}/version?${params.toString()}`, {\n            method: \"GET\",\n            headers: { ...this.headers },\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"read dataset version\");\n        return await response.json();\n    }\n    async listDatasetSplits({ datasetId, datasetName, asOf, }) {\n        let datasetId_;\n        if (datasetId === undefined && datasetName === undefined) {\n            throw new Error(\"Must provide dataset name or ID\");\n        }\n        else if (datasetId !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId === undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        else {\n            datasetId_ = datasetId;\n        }\n        assertUuid(datasetId_);\n        const params = new URLSearchParams();\n        const dataset_version = asOf\n            ? typeof asOf === \"string\"\n                ? asOf\n                : asOf?.toISOString()\n            : undefined;\n        if (dataset_version) {\n            params.append(\"as_of\", dataset_version);\n        }\n        const response = await this._get(`/datasets/${datasetId_}/splits`, params);\n        return response;\n    }\n    async updateDatasetSplits({ datasetId, datasetName, splitName, exampleIds, remove = false, }) {\n        let datasetId_;\n        if (datasetId === undefined && datasetName === undefined) {\n            throw new Error(\"Must provide dataset name or ID\");\n        }\n        else if (datasetId !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId === undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        else {\n            datasetId_ = datasetId;\n        }\n        assertUuid(datasetId_);\n        const data = {\n            split_name: splitName,\n            examples: exampleIds.map((id) => {\n                assertUuid(id);\n                return id;\n            }),\n            remove,\n        };\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets/${datasetId_}/splits`, {\n            method: \"PUT\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"update dataset splits\", true);\n    }\n    /**\n     * @deprecated This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead.\n     */\n    async evaluateRun(run, evaluator, { sourceInfo, loadChildRuns, referenceExample, } = { loadChildRuns: false }) {\n        warnOnce(\"This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead.\");\n        let run_;\n        if (typeof run === \"string\") {\n            run_ = await this.readRun(run, { loadChildRuns });\n        }\n        else if (typeof run === \"object\" && \"id\" in run) {\n            run_ = run;\n        }\n        else {\n            throw new Error(`Invalid run type: ${typeof run}`);\n        }\n        if (run_.reference_example_id !== null &&\n            run_.reference_example_id !== undefined) {\n            referenceExample = await this.readExample(run_.reference_example_id);\n        }\n        const feedbackResult = await evaluator.evaluateRun(run_, referenceExample);\n        const [_, feedbacks] = await this._logEvaluationFeedback(feedbackResult, run_, sourceInfo);\n        return feedbacks[0];\n    }\n    async createFeedback(runId, key, { score, value, correction, comment, sourceInfo, feedbackSourceType = \"api\", sourceRunId, feedbackId, feedbackConfig, projectId, comparativeExperimentId, }) {\n        if (!runId && !projectId) {\n            throw new Error(\"One of runId or projectId must be provided\");\n        }\n        if (runId && projectId) {\n            throw new Error(\"Only one of runId or projectId can be provided\");\n        }\n        const feedback_source = {\n            type: feedbackSourceType ?? \"api\",\n            metadata: sourceInfo ?? {},\n        };\n        if (sourceRunId !== undefined &&\n            feedback_source?.metadata !== undefined &&\n            !feedback_source.metadata[\"__run\"]) {\n            feedback_source.metadata[\"__run\"] = { run_id: sourceRunId };\n        }\n        if (feedback_source?.metadata !== undefined &&\n            feedback_source.metadata[\"__run\"]?.run_id !== undefined) {\n            assertUuid(feedback_source.metadata[\"__run\"].run_id);\n        }\n        const feedback = {\n            id: feedbackId ?? uuid.v4(),\n            run_id: runId,\n            key,\n            score: _formatFeedbackScore(score),\n            value,\n            correction,\n            comment,\n            feedback_source: feedback_source,\n            comparative_experiment_id: comparativeExperimentId,\n            feedbackConfig,\n            session_id: projectId,\n        };\n        const url = `${this.apiUrl}/feedback`;\n        const response = await this.caller.call(_getFetchImplementation(this.debug), url, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(feedback),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"create feedback\", true);\n        return feedback;\n    }\n    async updateFeedback(feedbackId, { score, value, correction, comment, }) {\n        const feedbackUpdate = {};\n        if (score !== undefined && score !== null) {\n            feedbackUpdate[\"score\"] = _formatFeedbackScore(score);\n        }\n        if (value !== undefined && value !== null) {\n            feedbackUpdate[\"value\"] = value;\n        }\n        if (correction !== undefined && correction !== null) {\n            feedbackUpdate[\"correction\"] = correction;\n        }\n        if (comment !== undefined && comment !== null) {\n            feedbackUpdate[\"comment\"] = comment;\n        }\n        assertUuid(feedbackId);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/feedback/${feedbackId}`, {\n            method: \"PATCH\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(feedbackUpdate),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"update feedback\", true);\n    }\n    async readFeedback(feedbackId) {\n        assertUuid(feedbackId);\n        const path = `/feedback/${feedbackId}`;\n        const response = await this._get(path);\n        return response;\n    }\n    async deleteFeedback(feedbackId) {\n        assertUuid(feedbackId);\n        const path = `/feedback/${feedbackId}`;\n        const response = await this.caller.call(_getFetchImplementation(this.debug), this.apiUrl + path, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, `delete ${path}`);\n        await response.json();\n    }\n    async *listFeedback({ runIds, feedbackKeys, feedbackSourceTypes, } = {}) {\n        const queryParams = new URLSearchParams();\n        if (runIds) {\n            queryParams.append(\"run\", runIds.join(\",\"));\n        }\n        if (feedbackKeys) {\n            for (const key of feedbackKeys) {\n                queryParams.append(\"key\", key);\n            }\n        }\n        if (feedbackSourceTypes) {\n            for (const type of feedbackSourceTypes) {\n                queryParams.append(\"source\", type);\n            }\n        }\n        for await (const feedbacks of this._getPaginated(\"/feedback\", queryParams)) {\n            yield* feedbacks;\n        }\n    }\n    /**\n     * Creates a presigned feedback token and URL.\n     *\n     * The token can be used to authorize feedback metrics without\n     * needing an API key. This is useful for giving browser-based\n     * applications the ability to submit feedback without needing\n     * to expose an API key.\n     *\n     * @param runId The ID of the run.\n     * @param feedbackKey The feedback key.\n     * @param options Additional options for the token.\n     * @param options.expiration The expiration time for the token.\n     *\n     * @returns A promise that resolves to a FeedbackIngestToken.\n     */\n    async createPresignedFeedbackToken(runId, feedbackKey, { expiration, feedbackConfig, } = {}) {\n        const body = {\n            run_id: runId,\n            feedback_key: feedbackKey,\n            feedback_config: feedbackConfig,\n        };\n        if (expiration) {\n            if (typeof expiration === \"string\") {\n                body[\"expires_at\"] = expiration;\n            }\n            else if (expiration?.hours || expiration?.minutes || expiration?.days) {\n                body[\"expires_in\"] = expiration;\n            }\n        }\n        else {\n            body[\"expires_in\"] = {\n                hours: 3,\n            };\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/feedback/tokens`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(body),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const result = await response.json();\n        return result;\n    }\n    async createComparativeExperiment({ name, experimentIds, referenceDatasetId, createdAt, description, metadata, id, }) {\n        if (experimentIds.length === 0) {\n            throw new Error(\"At least one experiment is required\");\n        }\n        if (!referenceDatasetId) {\n            referenceDatasetId = (await this.readProject({\n                projectId: experimentIds[0],\n            })).reference_dataset_id;\n        }\n        if (!referenceDatasetId == null) {\n            throw new Error(\"A reference dataset is required\");\n        }\n        const body = {\n            id,\n            name,\n            experiment_ids: experimentIds,\n            reference_dataset_id: referenceDatasetId,\n            description,\n            created_at: (createdAt ?? new Date())?.toISOString(),\n            extra: {},\n        };\n        if (metadata)\n            body.extra[\"metadata\"] = metadata;\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/datasets/comparative`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(body),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        return await response.json();\n    }\n    /**\n     * Retrieves a list of presigned feedback tokens for a given run ID.\n     * @param runId The ID of the run.\n     * @returns An async iterable of FeedbackIngestToken objects.\n     */\n    async *listPresignedFeedbackTokens(runId) {\n        assertUuid(runId);\n        const params = new URLSearchParams({ run_id: runId });\n        for await (const tokens of this._getPaginated(\"/feedback/tokens\", params)) {\n            yield* tokens;\n        }\n    }\n    _selectEvalResults(results) {\n        let results_;\n        if (\"results\" in results) {\n            results_ = results.results;\n        }\n        else if (Array.isArray(results)) {\n            results_ = results;\n        }\n        else {\n            results_ = [results];\n        }\n        return results_;\n    }\n    async _logEvaluationFeedback(evaluatorResponse, run, sourceInfo) {\n        const evalResults = this._selectEvalResults(evaluatorResponse);\n        const feedbacks = [];\n        for (const res of evalResults) {\n            let sourceInfo_ = sourceInfo || {};\n            if (res.evaluatorInfo) {\n                sourceInfo_ = { ...res.evaluatorInfo, ...sourceInfo_ };\n            }\n            let runId_ = null;\n            if (res.targetRunId) {\n                runId_ = res.targetRunId;\n            }\n            else if (run) {\n                runId_ = run.id;\n            }\n            feedbacks.push(await this.createFeedback(runId_, res.key, {\n                score: res.score,\n                value: res.value,\n                comment: res.comment,\n                correction: res.correction,\n                sourceInfo: sourceInfo_,\n                sourceRunId: res.sourceRunId,\n                feedbackConfig: res.feedbackConfig,\n                feedbackSourceType: \"model\",\n            }));\n        }\n        return [evalResults, feedbacks];\n    }\n    async logEvaluationFeedback(evaluatorResponse, run, sourceInfo) {\n        const [results] = await this._logEvaluationFeedback(evaluatorResponse, run, sourceInfo);\n        return results;\n    }\n    /**\n     * API for managing annotation queues\n     */\n    /**\n     * List the annotation queues on the LangSmith API.\n     * @param options - The options for listing annotation queues\n     * @param options.queueIds - The IDs of the queues to filter by\n     * @param options.name - The name of the queue to filter by\n     * @param options.nameContains - The substring that the queue name should contain\n     * @param options.limit - The maximum number of queues to return\n     * @returns An iterator of AnnotationQueue objects\n     */\n    async *listAnnotationQueues(options = {}) {\n        const { queueIds, name, nameContains, limit } = options;\n        const params = new URLSearchParams();\n        if (queueIds) {\n            queueIds.forEach((id, i) => {\n                assertUuid(id, `queueIds[${i}]`);\n                params.append(\"ids\", id);\n            });\n        }\n        if (name)\n            params.append(\"name\", name);\n        if (nameContains)\n            params.append(\"name_contains\", nameContains);\n        params.append(\"limit\", (limit !== undefined ? Math.min(limit, 100) : 100).toString());\n        let count = 0;\n        for await (const queues of this._getPaginated(\"/annotation-queues\", params)) {\n            yield* queues;\n            count++;\n            if (limit !== undefined && count >= limit)\n                break;\n        }\n    }\n    /**\n     * Create an annotation queue on the LangSmith API.\n     * @param options - The options for creating an annotation queue\n     * @param options.name - The name of the annotation queue\n     * @param options.description - The description of the annotation queue\n     * @param options.queueId - The ID of the annotation queue\n     * @returns The created AnnotationQueue object\n     */\n    async createAnnotationQueue(options) {\n        const { name, description, queueId, rubricInstructions } = options;\n        const body = {\n            name,\n            description,\n            id: queueId || uuid.v4(),\n            rubric_instructions: rubricInstructions,\n        };\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/annotation-queues`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(Object.fromEntries(Object.entries(body).filter(([_, v]) => v !== undefined))),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"create annotation queue\");\n        const data = await response.json();\n        return data;\n    }\n    /**\n     * Read an annotation queue with the specified queue ID.\n     * @param queueId - The ID of the annotation queue to read\n     * @returns The AnnotationQueueWithDetails object\n     */\n    async readAnnotationQueue(queueId) {\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/annotation-queues/${assertUuid(queueId, \"queueId\")}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"read annotation queue\");\n        const data = await response.json();\n        return data;\n    }\n    /**\n     * Update an annotation queue with the specified queue ID.\n     * @param queueId - The ID of the annotation queue to update\n     * @param options - The options for updating the annotation queue\n     * @param options.name - The new name for the annotation queue\n     * @param options.description - The new description for the annotation queue\n     */\n    async updateAnnotationQueue(queueId, options) {\n        const { name, description, rubricInstructions } = options;\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/annotation-queues/${assertUuid(queueId, \"queueId\")}`, {\n            method: \"PATCH\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify({\n                name,\n                description,\n                rubric_instructions: rubricInstructions,\n            }),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"update annotation queue\");\n    }\n    /**\n     * Delete an annotation queue with the specified queue ID.\n     * @param queueId - The ID of the annotation queue to delete\n     */\n    async deleteAnnotationQueue(queueId) {\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/annotation-queues/${assertUuid(queueId, \"queueId\")}`, {\n            method: \"DELETE\",\n            headers: { ...this.headers, Accept: \"application/json\" },\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"delete annotation queue\");\n    }\n    /**\n     * Add runs to an annotation queue with the specified queue ID.\n     * @param queueId - The ID of the annotation queue\n     * @param runIds - The IDs of the runs to be added to the annotation queue\n     */\n    async addRunsToAnnotationQueue(queueId, runIds) {\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/annotation-queues/${assertUuid(queueId, \"queueId\")}/runs`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(runIds.map((id, i) => assertUuid(id, `runIds[${i}]`).toString())),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"add runs to annotation queue\");\n    }\n    /**\n     * Get a run from an annotation queue at the specified index.\n     * @param queueId - The ID of the annotation queue\n     * @param index - The index of the run to retrieve\n     * @returns A Promise that resolves to a RunWithAnnotationQueueInfo object\n     * @throws {Error} If the run is not found at the given index or for other API-related errors\n     */\n    async getRunFromAnnotationQueue(queueId, index) {\n        const baseUrl = `/annotation-queues/${assertUuid(queueId, \"queueId\")}/run`;\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}${baseUrl}/${index}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"get run from annotation queue\");\n        return await response.json();\n    }\n    /**\n     * Delete a run from an an annotation queue.\n     * @param queueId - The ID of the annotation queue to delete the run from\n     * @param queueRunId - The ID of the run to delete from the annotation queue\n     */\n    async deleteRunFromAnnotationQueue(queueId, queueRunId) {\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/annotation-queues/${assertUuid(queueId, \"queueId\")}/runs/${assertUuid(queueRunId, \"queueRunId\")}`, {\n            method: \"DELETE\",\n            headers: { ...this.headers, Accept: \"application/json\" },\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"delete run from annotation queue\");\n    }\n    /**\n     * Get the size of an annotation queue.\n     * @param queueId - The ID of the annotation queue\n     */\n    async getSizeFromAnnotationQueue(queueId) {\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/annotation-queues/${assertUuid(queueId, \"queueId\")}/size`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"get size from annotation queue\");\n        return await response.json();\n    }\n    async _currentTenantIsOwner(owner) {\n        const settings = await this._getSettings();\n        return owner == \"-\" || settings.tenant_handle === owner;\n    }\n    async _ownerConflictError(action, owner) {\n        const settings = await this._getSettings();\n        return new Error(`Cannot ${action} for another tenant.\\n\n      Current tenant: ${settings.tenant_handle}\\n\n      Requested tenant: ${owner}`);\n    }\n    async _getLatestCommitHash(promptOwnerAndName) {\n        const res = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/commits/${promptOwnerAndName}/?limit=${1}&offset=${0}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const json = await res.json();\n        if (!res.ok) {\n            const detail = typeof json.detail === \"string\"\n                ? json.detail\n                : JSON.stringify(json.detail);\n            const error = new Error(`Error ${res.status}: ${res.statusText}\\n${detail}`);\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            error.statusCode = res.status;\n            throw error;\n        }\n        if (json.commits.length === 0) {\n            return undefined;\n        }\n        return json.commits[0].commit_hash;\n    }\n    async _likeOrUnlikePrompt(promptIdentifier, like) {\n        const [owner, promptName, _] = parsePromptIdentifier(promptIdentifier);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/likes/${owner}/${promptName}`, {\n            method: \"POST\",\n            body: JSON.stringify({ like: like }),\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, `${like ? \"like\" : \"unlike\"} prompt`);\n        return await response.json();\n    }\n    async _getPromptUrl(promptIdentifier) {\n        const [owner, promptName, commitHash] = parsePromptIdentifier(promptIdentifier);\n        if (!(await this._currentTenantIsOwner(owner))) {\n            if (commitHash !== \"latest\") {\n                return `${this.getHostUrl()}/hub/${owner}/${promptName}/${commitHash.substring(0, 8)}`;\n            }\n            else {\n                return `${this.getHostUrl()}/hub/${owner}/${promptName}`;\n            }\n        }\n        else {\n            const settings = await this._getSettings();\n            if (commitHash !== \"latest\") {\n                return `${this.getHostUrl()}/prompts/${promptName}/${commitHash.substring(0, 8)}?organizationId=${settings.id}`;\n            }\n            else {\n                return `${this.getHostUrl()}/prompts/${promptName}?organizationId=${settings.id}`;\n            }\n        }\n    }\n    async promptExists(promptIdentifier) {\n        const prompt = await this.getPrompt(promptIdentifier);\n        return !!prompt;\n    }\n    async likePrompt(promptIdentifier) {\n        return this._likeOrUnlikePrompt(promptIdentifier, true);\n    }\n    async unlikePrompt(promptIdentifier) {\n        return this._likeOrUnlikePrompt(promptIdentifier, false);\n    }\n    async *listCommits(promptOwnerAndName) {\n        for await (const commits of this._getPaginated(`/commits/${promptOwnerAndName}/`, new URLSearchParams(), (res) => res.commits)) {\n            yield* commits;\n        }\n    }\n    async *listPrompts(options) {\n        const params = new URLSearchParams();\n        params.append(\"sort_field\", options?.sortField ?? \"updated_at\");\n        params.append(\"sort_direction\", \"desc\");\n        params.append(\"is_archived\", (!!options?.isArchived).toString());\n        if (options?.isPublic !== undefined) {\n            params.append(\"is_public\", options.isPublic.toString());\n        }\n        if (options?.query) {\n            params.append(\"query\", options.query);\n        }\n        for await (const prompts of this._getPaginated(\"/repos\", params, (res) => res.repos)) {\n            yield* prompts;\n        }\n    }\n    async getPrompt(promptIdentifier) {\n        const [owner, promptName, _] = parsePromptIdentifier(promptIdentifier);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/repos/${owner}/${promptName}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        if (response.status === 404) {\n            return null;\n        }\n        await raiseForStatus(response, \"get prompt\");\n        const result = await response.json();\n        if (result.repo) {\n            return result.repo;\n        }\n        else {\n            return null;\n        }\n    }\n    async createPrompt(promptIdentifier, options) {\n        const settings = await this._getSettings();\n        if (options?.isPublic && !settings.tenant_handle) {\n            throw new Error(`Cannot create a public prompt without first\\n\n        creating a LangChain Hub handle.\n        You can add a handle by creating a public prompt at:\\n\n        https://smith.langchain.com/prompts`);\n        }\n        const [owner, promptName, _] = parsePromptIdentifier(promptIdentifier);\n        if (!(await this._currentTenantIsOwner(owner))) {\n            throw await this._ownerConflictError(\"create a prompt\", owner);\n        }\n        const data = {\n            repo_handle: promptName,\n            ...(options?.description && { description: options.description }),\n            ...(options?.readme && { readme: options.readme }),\n            ...(options?.tags && { tags: options.tags }),\n            is_public: !!options?.isPublic,\n        };\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/repos/`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"create prompt\");\n        const { repo } = await response.json();\n        return repo;\n    }\n    async createCommit(promptIdentifier, object, options) {\n        if (!(await this.promptExists(promptIdentifier))) {\n            throw new Error(\"Prompt does not exist, you must create it first.\");\n        }\n        const [owner, promptName, _] = parsePromptIdentifier(promptIdentifier);\n        const resolvedParentCommitHash = options?.parentCommitHash === \"latest\" || !options?.parentCommitHash\n            ? await this._getLatestCommitHash(`${owner}/${promptName}`)\n            : options?.parentCommitHash;\n        const payload = {\n            manifest: JSON.parse(JSON.stringify(object)),\n            parent_commit: resolvedParentCommitHash,\n        };\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/commits/${owner}/${promptName}`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(payload),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"create commit\");\n        const result = await response.json();\n        return this._getPromptUrl(`${owner}/${promptName}${result.commit_hash ? `:${result.commit_hash}` : \"\"}`);\n    }\n    /**\n     * Update examples with attachments using multipart form data.\n     * @param updates List of ExampleUpdateWithAttachments objects to upsert\n     * @returns Promise with the update response\n     */\n    async updateExamplesMultipart(datasetId, updates = []) {\n        return this._updateExamplesMultipart(datasetId, updates);\n    }\n    async _updateExamplesMultipart(datasetId, updates = []) {\n        if (!(await this._getMultiPartSupport())) {\n            throw new Error(\"Your LangSmith deployment does not allow using the multipart examples endpoint, please upgrade your deployment to the latest version.\");\n        }\n        const formData = new FormData();\n        for (const example of updates) {\n            const exampleId = example.id;\n            // Prepare the main example body\n            const exampleBody = {\n                ...(example.metadata && { metadata: example.metadata }),\n                ...(example.split && { split: example.split }),\n            };\n            // Add main example data\n            const stringifiedExample = serializePayloadForTracing(exampleBody, `Serializing body for example with id: ${exampleId}`);\n            const exampleBlob = new Blob([stringifiedExample], {\n                type: \"application/json\",\n            });\n            formData.append(exampleId, exampleBlob);\n            // Add inputs if present\n            if (example.inputs) {\n                const stringifiedInputs = serializePayloadForTracing(example.inputs, `Serializing inputs for example with id: ${exampleId}`);\n                const inputsBlob = new Blob([stringifiedInputs], {\n                    type: \"application/json\",\n                });\n                formData.append(`${exampleId}.inputs`, inputsBlob);\n            }\n            // Add outputs if present\n            if (example.outputs) {\n                const stringifiedOutputs = serializePayloadForTracing(example.outputs, `Serializing outputs whle updating example with id: ${exampleId}`);\n                const outputsBlob = new Blob([stringifiedOutputs], {\n                    type: \"application/json\",\n                });\n                formData.append(`${exampleId}.outputs`, outputsBlob);\n            }\n            // Add attachments if present\n            if (example.attachments) {\n                for (const [name, attachment] of Object.entries(example.attachments)) {\n                    let mimeType;\n                    let data;\n                    if (Array.isArray(attachment)) {\n                        [mimeType, data] = attachment;\n                    }\n                    else {\n                        mimeType = attachment.mimeType;\n                        data = attachment.data;\n                    }\n                    const attachmentBlob = new Blob([data], {\n                        type: `${mimeType}; length=${data.byteLength}`,\n                    });\n                    formData.append(`${exampleId}.attachment.${name}`, attachmentBlob);\n                }\n            }\n            if (example.attachments_operations) {\n                const stringifiedAttachmentsOperations = serializePayloadForTracing(example.attachments_operations, `Serializing attachments while updating example with id: ${exampleId}`);\n                const attachmentsOperationsBlob = new Blob([stringifiedAttachmentsOperations], {\n                    type: \"application/json\",\n                });\n                formData.append(`${exampleId}.attachments_operations`, attachmentsOperationsBlob);\n            }\n        }\n        const datasetIdToUse = datasetId ?? updates[0]?.dataset_id;\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}${this._getPlatformEndpointPath(`datasets/${datasetIdToUse}/examples`)}`, {\n            method: \"PATCH\",\n            headers: this.headers,\n            body: formData,\n        });\n        const result = await response.json();\n        return result;\n    }\n    /**\n     * Upload examples with attachments using multipart form data.\n     * @param uploads List of ExampleUploadWithAttachments objects to upload\n     * @returns Promise with the upload response\n     * @deprecated This method is deprecated and will be removed in future LangSmith versions, please use `createExamples` instead\n     */\n    async uploadExamplesMultipart(datasetId, uploads = []) {\n        return this._uploadExamplesMultipart(datasetId, uploads);\n    }\n    async _uploadExamplesMultipart(datasetId, uploads = []) {\n        if (!(await this._getMultiPartSupport())) {\n            throw new Error(\"Your LangSmith deployment does not allow using the multipart examples endpoint, please upgrade your deployment to the latest version.\");\n        }\n        const formData = new FormData();\n        for (const example of uploads) {\n            const exampleId = (example.id ?? uuid.v4()).toString();\n            // Prepare the main example body\n            const exampleBody = {\n                created_at: example.created_at,\n                ...(example.metadata && { metadata: example.metadata }),\n                ...(example.split && { split: example.split }),\n                ...(example.source_run_id && { source_run_id: example.source_run_id }),\n                ...(example.use_source_run_io && {\n                    use_source_run_io: example.use_source_run_io,\n                }),\n                ...(example.use_source_run_attachments && {\n                    use_source_run_attachments: example.use_source_run_attachments,\n                }),\n            };\n            // Add main example data\n            const stringifiedExample = serializePayloadForTracing(exampleBody, `Serializing body for uploaded example with id: ${exampleId}`);\n            const exampleBlob = new Blob([stringifiedExample], {\n                type: \"application/json\",\n            });\n            formData.append(exampleId, exampleBlob);\n            // Add inputs if present\n            if (example.inputs) {\n                const stringifiedInputs = serializePayloadForTracing(example.inputs, `Serializing inputs for uploaded example with id: ${exampleId}`);\n                const inputsBlob = new Blob([stringifiedInputs], {\n                    type: \"application/json\",\n                });\n                formData.append(`${exampleId}.inputs`, inputsBlob);\n            }\n            // Add outputs if present\n            if (example.outputs) {\n                const stringifiedOutputs = serializePayloadForTracing(example.outputs, `Serializing outputs for uploaded example with id: ${exampleId}`);\n                const outputsBlob = new Blob([stringifiedOutputs], {\n                    type: \"application/json\",\n                });\n                formData.append(`${exampleId}.outputs`, outputsBlob);\n            }\n            // Add attachments if present\n            if (example.attachments) {\n                for (const [name, attachment] of Object.entries(example.attachments)) {\n                    let mimeType;\n                    let data;\n                    if (Array.isArray(attachment)) {\n                        [mimeType, data] = attachment;\n                    }\n                    else {\n                        mimeType = attachment.mimeType;\n                        data = attachment.data;\n                    }\n                    const attachmentBlob = new Blob([data], {\n                        type: `${mimeType}; length=${data.byteLength}`,\n                    });\n                    formData.append(`${exampleId}.attachment.${name}`, attachmentBlob);\n                }\n            }\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}${this._getPlatformEndpointPath(`datasets/${datasetId}/examples`)}`, {\n            method: \"POST\",\n            headers: this.headers,\n            body: formData,\n        });\n        await raiseForStatus(response, \"upload examples\");\n        const result = await response.json();\n        return result;\n    }\n    async updatePrompt(promptIdentifier, options) {\n        if (!(await this.promptExists(promptIdentifier))) {\n            throw new Error(\"Prompt does not exist, you must create it first.\");\n        }\n        const [owner, promptName] = parsePromptIdentifier(promptIdentifier);\n        if (!(await this._currentTenantIsOwner(owner))) {\n            throw await this._ownerConflictError(\"update a prompt\", owner);\n        }\n        const payload = {};\n        if (options?.description !== undefined)\n            payload.description = options.description;\n        if (options?.readme !== undefined)\n            payload.readme = options.readme;\n        if (options?.tags !== undefined)\n            payload.tags = options.tags;\n        if (options?.isPublic !== undefined)\n            payload.is_public = options.isPublic;\n        if (options?.isArchived !== undefined)\n            payload.is_archived = options.isArchived;\n        // Check if payload is empty\n        if (Object.keys(payload).length === 0) {\n            throw new Error(\"No valid update options provided\");\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/repos/${owner}/${promptName}`, {\n            method: \"PATCH\",\n            body: JSON.stringify(payload),\n            headers: {\n                ...this.headers,\n                \"Content-Type\": \"application/json\",\n            },\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"update prompt\");\n        return response.json();\n    }\n    async deletePrompt(promptIdentifier) {\n        if (!(await this.promptExists(promptIdentifier))) {\n            throw new Error(\"Prompt does not exist, you must create it first.\");\n        }\n        const [owner, promptName, _] = parsePromptIdentifier(promptIdentifier);\n        if (!(await this._currentTenantIsOwner(owner))) {\n            throw await this._ownerConflictError(\"delete a prompt\", owner);\n        }\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/repos/${owner}/${promptName}`, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        return await response.json();\n    }\n    async pullPromptCommit(promptIdentifier, options) {\n        const [owner, promptName, commitHash] = parsePromptIdentifier(promptIdentifier);\n        const response = await this.caller.call(_getFetchImplementation(this.debug), `${this.apiUrl}/commits/${owner}/${promptName}/${commitHash}${options?.includeModel ? \"?include_model=true\" : \"\"}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await raiseForStatus(response, \"pull prompt commit\");\n        const result = await response.json();\n        return {\n            owner,\n            repo: promptName,\n            commit_hash: result.commit_hash,\n            manifest: result.manifest,\n            examples: result.examples,\n        };\n    }\n    /**\n     * This method should not be used directly, use `import { pull } from \"langchain/hub\"` instead.\n     * Using this method directly returns the JSON string of the prompt rather than a LangChain object.\n     * @private\n     */\n    async _pullPrompt(promptIdentifier, options) {\n        const promptObject = await this.pullPromptCommit(promptIdentifier, {\n            includeModel: options?.includeModel,\n        });\n        const prompt = JSON.stringify(promptObject.manifest);\n        return prompt;\n    }\n    async pushPrompt(promptIdentifier, options) {\n        // Create or update prompt metadata\n        if (await this.promptExists(promptIdentifier)) {\n            if (options && Object.keys(options).some((key) => key !== \"object\")) {\n                await this.updatePrompt(promptIdentifier, {\n                    description: options?.description,\n                    readme: options?.readme,\n                    tags: options?.tags,\n                    isPublic: options?.isPublic,\n                });\n            }\n        }\n        else {\n            await this.createPrompt(promptIdentifier, {\n                description: options?.description,\n                readme: options?.readme,\n                tags: options?.tags,\n                isPublic: options?.isPublic,\n            });\n        }\n        if (!options?.object) {\n            return await this._getPromptUrl(promptIdentifier);\n        }\n        // Create a commit with the new manifest\n        const url = await this.createCommit(promptIdentifier, options?.object, {\n            parentCommitHash: options?.parentCommitHash,\n        });\n        return url;\n    }\n    /**\n     * Clone a public dataset to your own langsmith tenant.\n     * This operation is idempotent. If you already have a dataset with the given name,\n     * this function will do nothing.\n  \n     * @param {string} tokenOrUrl The token of the public dataset to clone.\n     * @param {Object} [options] Additional options for cloning the dataset.\n     * @param {string} [options.sourceApiUrl] The URL of the langsmith server where the data is hosted. Defaults to the API URL of your current client.\n     * @param {string} [options.datasetName] The name of the dataset to create in your tenant. Defaults to the name of the public dataset.\n     * @returns {Promise<void>}\n     */\n    async clonePublicDataset(tokenOrUrl, options = {}) {\n        const { sourceApiUrl = this.apiUrl, datasetName } = options;\n        const [parsedApiUrl, tokenUuid] = this.parseTokenOrUrl(tokenOrUrl, sourceApiUrl);\n        const sourceClient = new Client({\n            apiUrl: parsedApiUrl,\n            // Placeholder API key not needed anymore in most cases, but\n            // some private deployments may have API key-based rate limiting\n            // that would cause this to fail if we provide no value.\n            apiKey: \"placeholder\",\n        });\n        const ds = await sourceClient.readSharedDataset(tokenUuid);\n        const finalDatasetName = datasetName || ds.name;\n        try {\n            if (await this.hasDataset({ datasetId: finalDatasetName })) {\n                console.log(`Dataset ${finalDatasetName} already exists in your tenant. Skipping.`);\n                return;\n            }\n        }\n        catch (_) {\n            // `.hasDataset` will throw an error if the dataset does not exist.\n            // no-op in that case\n        }\n        // Fetch examples first, then create the dataset\n        const examples = await sourceClient.listSharedExamples(tokenUuid);\n        const dataset = await this.createDataset(finalDatasetName, {\n            description: ds.description,\n            dataType: ds.data_type || \"kv\",\n            inputsSchema: ds.inputs_schema_definition ?? undefined,\n            outputsSchema: ds.outputs_schema_definition ?? undefined,\n        });\n        try {\n            await this.createExamples({\n                inputs: examples.map((e) => e.inputs),\n                outputs: examples.flatMap((e) => (e.outputs ? [e.outputs] : [])),\n                datasetId: dataset.id,\n            });\n        }\n        catch (e) {\n            console.error(`An error occurred while creating dataset ${finalDatasetName}. ` +\n                \"You should delete it manually.\");\n            throw e;\n        }\n    }\n    parseTokenOrUrl(urlOrToken, apiUrl, numParts = 2, kind = \"dataset\") {\n        // Try parsing as UUID\n        try {\n            assertUuid(urlOrToken); // Will throw if it's not a UUID.\n            return [apiUrl, urlOrToken];\n        }\n        catch (_) {\n            // no-op if it's not a uuid\n        }\n        // Parse as URL\n        try {\n            const parsedUrl = new URL(urlOrToken);\n            const pathParts = parsedUrl.pathname\n                .split(\"/\")\n                .filter((part) => part !== \"\");\n            if (pathParts.length >= numParts) {\n                const tokenUuid = pathParts[pathParts.length - numParts];\n                return [apiUrl, tokenUuid];\n            }\n            else {\n                throw new Error(`Invalid public ${kind} URL: ${urlOrToken}`);\n            }\n        }\n        catch (error) {\n            throw new Error(`Invalid public ${kind} URL or token: ${urlOrToken}`);\n        }\n    }\n    /**\n     * Awaits all pending trace batches. Useful for environments where\n     * you need to be sure that all tracing requests finish before execution ends,\n     * such as serverless environments.\n     *\n     * @example\n     * ```\n     * import { Client } from \"langsmith\";\n     *\n     * const client = new Client();\n     *\n     * try {\n     *   // Tracing happens here\n     *   ...\n     * } finally {\n     *   await client.awaitPendingTraceBatches();\n     * }\n     * ```\n     *\n     * @returns A promise that resolves once all currently pending traces have sent.\n     */\n    async awaitPendingTraceBatches() {\n        if (this.manualFlushMode) {\n            console.warn(\"[WARNING]: When tracing in manual flush mode, you must call `await client.flush()` manually to submit trace batches.\");\n            return Promise.resolve();\n        }\n        await Promise.all([\n            ...this.autoBatchQueue.items.map(({ itemPromise }) => itemPromise),\n            this.batchIngestCaller.queue.onIdle(),\n        ]);\n        if (this.langSmithToOTELTranslator !== undefined) {\n            await getDefaultOTLPTracerComponents()?.DEFAULT_LANGSMITH_SPAN_PROCESSOR?.forceFlush();\n        }\n    }\n}\nfunction isExampleCreate(input) {\n    return \"dataset_id\" in input || \"dataset_name\" in input;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AACO,SAAS,6BAA6B,GAAG;IAC5C,MAAM,aAAa,CAAA,GAAA,mJAAA,CAAA,wBAAqB,AAAD;IACvC,MAAM,UAAU,CAAA,GAAA,mJAAA,CAAA,8BAA2B,AAAD;IAC1C,MAAM,QAAQ,IAAI,KAAK,IAAI,CAAC;IAC5B,MAAM,WAAW,MAAM,QAAQ;IAC/B,IAAI,KAAK,GAAG;QACR,GAAG,KAAK;QACR,SAAS;YACL,GAAG,UAAU;YACb,GAAG,OAAO,OAAO;QACrB;QACA,UAAU;YACN,GAAG,OAAO;YACV,GAAI,QAAQ,WAAW,IAAI,IAAI,WAAW,GACpC;gBAAE,aAAa,IAAI,WAAW,IAAI,QAAQ,WAAW;YAAC,IACtD,CAAC,CAAC;YACR,GAAG,QAAQ;QACf;IACJ;IACA,OAAO;AACX;AACA,MAAM,yBAAyB,CAAC;IAC5B,MAAM,kBAAkB,YAAY,cAChC,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE;IACpC,IAAI,oBAAoB,WAAW;QAC/B,OAAO;IACX;IACA,MAAM,eAAe,WAAW;IAChC,IAAI,eAAe,KAAK,eAAe,GAAG;QACtC,MAAM,IAAI,MAAM,CAAC,qEAAqE,EAAE,cAAc;IAC1G;IACA,OAAO;AACX;AACA,oBAAoB;AACpB,MAAM,cAAc,CAAC;IACjB,MAAM,cAAc,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,YAAY;IACnE,MAAM,WAAW,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;IACxD,OAAQ,aAAa,eAAe,aAAa,eAAe,aAAa;AACjF;AACA,eAAe,QAAQ,QAAQ;IAC3B,MAAM,SAAS,EAAE;IACjB,WAAW,MAAM,QAAQ,SAAU;QAC/B,OAAO,IAAI,CAAC;IAChB;IACA,OAAO;AACX;AACA,SAAS,WAAW,GAAG;IACnB,IAAI,QAAQ,WAAW;QACnB,OAAO;IACX;IACA,OAAO,IACF,IAAI,GACJ,OAAO,CAAC,YAAY,MACpB,OAAO,CAAC,YAAY;AAC7B;AACA,MAAM,YAAY,OAAO;IACrB,IAAI,UAAU,WAAW,KAAK;QAC1B,MAAM,aAAa,SAAS,SAAS,OAAO,CAAC,GAAG,CAAC,kBAAkB,MAAM,MAAM;QAC/E,IAAI,aAAa,GAAG;YAChB,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YACnD,2CAA2C;YAC3C,OAAO;QACX;IACJ;IACA,sCAAsC;IACtC,OAAO;AACX;AACA,SAAS,qBAAqB,KAAK;IAC/B,IAAI,OAAO,UAAU,UAAU;QAC3B,+BAA+B;QAC/B,OAAO,OAAO,MAAM,OAAO,CAAC;IAChC;IACA,OAAO;AACX;AACO,MAAM;IACT,aAAc;QACV,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,EAAE;QACb;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;IACJ;IACA,OAAO;QACH,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;IACxB;IACA,KAAK,IAAI,EAAE;QACP,IAAI;QACJ,MAAM,cAAc,IAAI,QAAQ,CAAC;YAC7B,mEAAmE;YACnE,mGAAmG;YACnG,qBAAqB;QACzB;QACA,MAAM,OAAO,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,KAAK,IAAI,EAAE,CAAC,yBAAyB,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,EAAE,MAAM;QACrG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACZ,QAAQ,KAAK,MAAM;YACnB,SAAS,KAAK,IAAI;YAClB,aAAa,KAAK,WAAW;YAC7B,QAAQ,KAAK,MAAM;YACnB,QAAQ,KAAK,MAAM;YACnB,oEAAoE;YACpE,oBAAoB;YACpB;YACA;QACJ;QACA,IAAI,CAAC,SAAS,IAAI;QAClB,OAAO;IACX;IACA,IAAI,aAAa,EAAE;QACf,IAAI,gBAAgB,GAAG;YACnB,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,SAAS,EAAE;QACjB,IAAI,kBAAkB;QACtB,oDAAoD;QACpD,MAAO,kBAAkB,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,iBAChD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAG;YACvB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;YAC7B,IAAI,MAAM;gBACN,OAAO,IAAI,CAAC;gBACZ,mBAAmB,KAAK,IAAI;gBAC5B,IAAI,CAAC,SAAS,IAAI,KAAK,IAAI;YAC/B;QACJ;QACA,0DAA0D;QAC1D,oCAAoC;QACpC,IAAI,OAAO,MAAM,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC9C,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;YAC7B,OAAO,IAAI,CAAC;YACZ,mBAAmB,KAAK,IAAI;YAC5B,IAAI,CAAC,SAAS,IAAI,KAAK,IAAI;QAC/B;QACA,OAAO;YACH,OAAO,GAAG,CAAC,CAAC,KAAO,CAAC;oBAChB,QAAQ,GAAG,MAAM;oBACjB,MAAM,GAAG,OAAO;oBAChB,aAAa,GAAG,WAAW;oBAC3B,QAAQ,GAAG,MAAM;oBACjB,QAAQ,GAAG,MAAM;gBACrB,CAAC;YACD,IAAM,OAAO,OAAO,CAAC,CAAC,KAAO,GAAG,kBAAkB;SACrD;IACL;AACJ;AAEO,MAAM,iCAAiC;AAC9C,MAAM,8BAA8B;AACpC,MAAM,kBAAkB;AACjB,MAAM;IACT,YAAY,SAAS,CAAC,CAAC,CAAE;QACrB,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,qBAAqB;YAC7C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa;YACrC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,qBAAqB;YAC7C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,qBAAqB;YAC7C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,IAAI;QACf;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,oBAAoB;YAC5C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,kBAAkB;YAC1C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,IAAI;QACf;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,oBAAoB;YAC5C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,+BAA+B;YACvD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,uBAAuB;YAC/C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YACpC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,8BAA8B;YACtD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,CAAA,GAAA,mJAAA,CAAA,yBAAsB,AAAD,EAAE,oCAAoC;QACtE;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,yBAAyB;YACjD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,8DAA8D;QAC9D,OAAO,cAAc,CAAC,IAAI,EAAE,yBAAyB;YACjD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,mBAAmB;YAC3C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,6BAA6B;YACrD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,8BAA8B;YACtD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO;QACX;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,CAAA,GAAA,mJAAA,CAAA,yBAAsB,AAAD,EAAE,uBAAuB;QACzD;QACA,MAAM,gBAAgB,OAAO,sBAAsB;QACnD,IAAI,CAAC,iBAAiB,GAAG,uBAAuB,OAAO,mBAAmB;QAC1E,IAAI,CAAC,MAAM,GAAG,WAAW,OAAO,MAAM,IAAI,cAAc,MAAM,KAAK;QACnE,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM;YAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;QACxC;QACA,IAAI,CAAC,MAAM,GAAG,WAAW,OAAO,MAAM,IAAI,cAAc,MAAM;QAC9D,IAAI,CAAC,MAAM,GAAG,WAAW,OAAO,MAAM,IAAI,cAAc,MAAM;QAC9D,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,MAAM;YAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;QACxC;QACA,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU,IAAI;QACvC,IAAI,CAAC,MAAM,GAAG,IAAI,4JAAA,CAAA,cAAW,CAAC;YAC1B,GAAI,OAAO,aAAa,IAAI,CAAC,CAAC;YAC9B,OAAO,OAAO,KAAK,IAAI,IAAI,CAAC,KAAK;QACrC;QACA,IAAI,CAAC,qBAAqB,GACtB,OAAO,qBAAqB,IAAI,IAAI,CAAC,qBAAqB;QAC9D,IAAI,IAAI,CAAC,qBAAqB,GAAG,GAAG;YAChC,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK,IAAI,IAAI,CAAC,KAAK;QACvC,IAAI,CAAC,iBAAiB,GAAG,IAAI,4JAAA,CAAA,cAAW,CAAC;YACrC,YAAY;YACZ,gBAAgB,IAAI,CAAC,qBAAqB;YAC1C,GAAI,OAAO,aAAa,IAAI,CAAC,CAAC;YAC9B,sBAAsB;YACtB,OAAO,OAAO,KAAK,IAAI,IAAI,CAAC,KAAK;QACrC;QACA,IAAI,CAAC,UAAU,GACX,OAAO,UAAU,IAAI,OAAO,UAAU,IAAI,cAAc,UAAU;QACtE,IAAI,CAAC,WAAW,GACZ,OAAO,WAAW,IAAI,OAAO,UAAU,IAAI,cAAc,WAAW;QACxE,IAAI,CAAC,gBAAgB,GAAG,OAAO,gBAAgB,IAAI,IAAI,CAAC,gBAAgB;QACxE,IAAI,CAAC,0BAA0B,GAC3B,OAAO,0BAA0B,IAAI,IAAI,CAAC,0BAA0B;QACxE,IAAI,CAAC,mBAAmB,GAAG,OAAO,mBAAmB;QACrD,IAAI,CAAC,YAAY,GAAG,OAAO,YAAY,IAAI,CAAC;QAC5C,IAAI,CAAC,eAAe,GAAG,OAAO,eAAe,IAAI,IAAI,CAAC,eAAe;QACrE,IAAI,CAAA,GAAA,mJAAA,CAAA,yBAAsB,AAAD,EAAE,oBAAoB,QAAQ;YACnD,IAAI,CAAC,yBAAyB,GAAG,IAAI,yKAAA,CAAA,4BAAyB;QAClE;IACJ;IACA,OAAO,yBAAyB;QAC5B,MAAM,SAAS,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE;QAC/C,MAAM,SAAS,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,eAAe;QAC9D,MAAM,aAAa,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,mBAAmB;QACtE,MAAM,cAAc,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,oBAAoB;QACxE,OAAO;YACH,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,YAAY;YACZ,aAAa;QACjB;IACJ;IACA,aAAa;QACT,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO,IAAI,CAAC,MAAM;QACtB,OACK,IAAI,YAAY,IAAI,CAAC,MAAM,GAAG;YAC/B,IAAI,CAAC,MAAM,GAAG;YACd,OAAO,IAAI,CAAC,MAAM;QACtB,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY;YACtC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW;YAC7C,OAAO,IAAI,CAAC,MAAM;QACtB,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAC1B,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ;YAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ;YAC1C,OAAO,IAAI,CAAC,MAAM;QACtB,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ;YACnD,IAAI,CAAC,MAAM,GAAG;YACd,OAAO,IAAI,CAAC,MAAM;QACtB,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO;YAClD,IAAI,CAAC,MAAM,GAAG;YACd,OAAO,IAAI,CAAC,MAAM;QACtB,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS;YACpD,IAAI,CAAC,MAAM,GAAG;YACd,OAAO,IAAI,CAAC,MAAM;QACtB,OACK;YACD,IAAI,CAAC,MAAM,GAAG;YACd,OAAO,IAAI,CAAC,MAAM;QACtB;IACJ;IACA,IAAI,UAAU;QACV,MAAM,UAAU;YACZ,cAAc,CAAC,aAAa,EAAE,4JAAA,CAAA,cAAW,EAAE;QAC/C;QACA,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,OAAO,CAAC,YAAY,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;QAC3C;QACA,OAAO;IACX;IACA,yBAAyB,IAAI,EAAE;QAC3B,8EAA8E;QAC9E,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO;QACnF,OAAO,gBAAgB,CAAC,aAAa,EAAE,MAAM,GAAG,CAAC,UAAU,EAAE,MAAM;IACvE;IACA,MAAM,cAAc,MAAM,EAAE;QACxB,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO;YAC3B,OAAO;QACX;QACA,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM;YAC1B,OAAO,CAAC;QACZ;QACA,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,YAAY;YACvC,OAAO,IAAI,CAAC,UAAU,CAAC;QAC3B;QACA,OAAO;IACX;IACA,MAAM,eAAe,OAAO,EAAE;QAC1B,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO;YAC5B,OAAO;QACX;QACA,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM;YAC3B,OAAO,CAAC;QACZ;QACA,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,YAAY;YACxC,OAAO,IAAI,CAAC,WAAW,CAAC;QAC5B;QACA,OAAO;IACX;IACA,MAAM,+BAA+B,GAAG,EAAE;QACtC,MAAM,YAAY;YAAE,GAAG,GAAG;QAAC;QAC3B,IAAI,UAAU,MAAM,KAAK,WAAW;YAChC,UAAU,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,MAAM;QAChE;QACA,IAAI,UAAU,OAAO,KAAK,WAAW;YACjC,UAAU,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,OAAO;QACnE;QACA,OAAO;IACX;IACA,MAAM,aAAa,IAAI,EAAE,WAAW,EAAE;QAClC,MAAM,eAAe,aAAa,cAAc;QAChD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,cAAc;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK;YAC9E,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAC,gBAAgB,EAAE,MAAM;QACxD,OAAO;IACX;IACA,MAAM,KAAK,IAAI,EAAE,WAAW,EAAE;QAC1B,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM;QAC/C,OAAO,SAAS,IAAI;IACxB;IACA,OAAO,cAAc,IAAI,EAAE,cAAc,IAAI,iBAAiB,EAAE,SAAS,EAAE;QACvE,IAAI,SAAS,OAAO,YAAY,GAAG,CAAC,cAAc;QAClD,MAAM,QAAQ,OAAO,YAAY,GAAG,CAAC,aAAa;QAClD,MAAO,KAAM;YACT,YAAY,GAAG,CAAC,UAAU,OAAO;YACjC,YAAY,GAAG,CAAC,SAAS,OAAO;YAChC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,aAAa;YAClD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK;gBAC9E,QAAQ;gBACR,SAAS,IAAI,CAAC,OAAO;gBACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;gBAC3C,GAAG,IAAI,CAAC,YAAY;YACxB;YACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAC,gBAAgB,EAAE,MAAM;YACxD,MAAM,QAAQ,YACR,UAAU,MAAM,SAAS,IAAI,MAC7B,MAAM,SAAS,IAAI;YACzB,IAAI,MAAM,MAAM,KAAK,GAAG;gBACpB;YACJ;YACA,MAAM;YACN,IAAI,MAAM,MAAM,GAAG,OAAO;gBACtB;YACJ;YACA,UAAU,MAAM,MAAM;QAC1B;IACJ;IACA,OAAO,wBAAwB,IAAI,EAAE,OAAO,IAAI,EAAE,gBAAgB,MAAM,EAAE,UAAU,MAAM,EAAE;QACxF,MAAM,aAAa,OAAO;YAAE,GAAG,IAAI;QAAC,IAAI,CAAC;QACzC,MAAO,KAAM;YACT,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE;gBAClG,QAAQ;gBACR,SAAS;oBAAE,GAAG,IAAI,CAAC,OAAO;oBAAE,gBAAgB;gBAAmB;gBAC/D,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;gBAC3C,GAAG,IAAI,CAAC,YAAY;gBACpB,MAAM,KAAK,SAAS,CAAC;YACzB;YACA,MAAM,eAAe,MAAM,SAAS,IAAI;YACxC,IAAI,CAAC,cAAc;gBACf;YACJ;YACA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;gBACxB;YACJ;YACA,MAAM,YAAY,CAAC,QAAQ;YAC3B,MAAM,UAAU,aAAa,OAAO;YACpC,IAAI,CAAC,SAAS;gBACV;YACJ;YACA,IAAI,CAAC,QAAQ,IAAI,EAAE;gBACf;YACJ;YACA,WAAW,MAAM,GAAG,QAAQ,IAAI;QACpC;IACJ;IACA,2BAA2B;IAC3B,gBAAgB;QACZ,IAAI,IAAI,CAAC,iBAAiB,KAAK,WAAW;YACtC,OAAO;QACX;QACA,OAAO,KAAK,MAAM,KAAK,IAAI,CAAC,iBAAiB;IACjD;IACA,mBAAmB,IAAI,EAAE,QAAQ,KAAK,EAAE;QACpC,IAAI,IAAI,CAAC,iBAAiB,KAAK,WAAW;YACtC,OAAO;QACX;QACA,IAAI,OAAO;YACP,MAAM,UAAU,EAAE;YAClB,KAAK,MAAM,OAAO,KAAM;gBACpB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG;oBACrC,QAAQ,IAAI,CAAC;gBACjB,OACK;oBACD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE;gBACxC;YACJ;YACA,OAAO;QACX,OACK;YACD,8DAA8D;YAC9D,MAAM,UAAU,EAAE;YAClB,KAAK,MAAM,OAAO,KAAM;gBACpB,MAAM,UAAU,IAAI,QAAQ,IAAI,IAAI,EAAE;gBACtC,+DAA+D;gBAC/D,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU;oBACrC;gBACJ;gBACA,iCAAiC;gBACjC,IAAI,IAAI,EAAE,KAAK,SAAS;oBACpB,IAAI,IAAI,CAAC,aAAa,IAAI;wBACtB,QAAQ,IAAI,CAAC;oBACjB,OACK;wBACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;oBAC/B;gBACJ,OACK;oBACD,oDAAoD;oBACpD,QAAQ,IAAI,CAAC;gBACjB;YACJ;YACA,OAAO;QACX;IACJ;IACA,MAAM,0BAA0B;QAC5B,MAAM,aAAa,MAAM,IAAI,CAAC,iBAAiB;QAC/C,OAAQ,IAAI,CAAC,mBAAmB,IAC5B,WAAW,mBAAmB,EAAE,oBAChC;IACR;IACA,MAAM,uBAAuB;QACzB,MAAM,aAAa,MAAM,IAAI,CAAC,iBAAiB;QAC/C,OAAQ,WAAW,cAAc,EAAE,sCAAsC;IAC7E;IACA,oBAAoB,cAAc,EAAE;QAChC,MAAM,WAAW,EAAE;QACnB,MAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,EAAG;YACzC,MAAM,CAAC,OAAO,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;YAC9C,IAAI,CAAC,MAAM,MAAM,EAAE;gBACf;gBACA;YACJ;YACA,MAAM,uBAAuB,MAAM,MAAM,CAAC,CAAC,KAAK;gBAC5C,MAAM,SAAS,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM;gBACzC,MAAM,SAAS,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM;gBACzC,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM;gBAC5E,MAAM,WAAW,YAAY,YAAY,GAAG,OAAO,CAAC,EAAE,QAAQ;gBAC9D,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;oBAChB,GAAG,CAAC,SAAS,GAAG,EAAE;gBACtB;gBACA,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;gBACnB,OAAO;YACX,GAAG,CAAC;YACJ,MAAM,gBAAgB,EAAE;YACxB,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,OAAO,OAAO,CAAC,sBAAuB;gBAClE,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,OAAO;oBAC3C,QAAQ,aAAa,YAAY,YAAY,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE;oBACnE,QAAQ,aAAa,YAAY,YAAY,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE;gBACvE;gBACA,cAAc,IAAI,CAAC;YACvB;YACA,wEAAwE;YACxE,MAAM,oBAAoB,QAAQ,GAAG,CAAC,eAAe,OAAO,CAAC;YAC7D,SAAS,IAAI,CAAC;QAClB;QACA,OAAO,QAAQ,GAAG,CAAC;IACvB;IACA,MAAM,cAAc,KAAK,EAAE,OAAO,EAAE;QAChC,IAAI,CAAC,MAAM,MAAM,EAAE;YACf;QACJ;QACA,IAAI;YACA,IAAI,IAAI,CAAC,yBAAyB,KAAK,WAAW;gBAC9C,IAAI,CAAC,0BAA0B,CAAC;YACpC,OACK;gBACD,MAAM,eAAe;oBACjB,YAAY,MACP,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,UACjC,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;oBAC5B,YAAY,MACP,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,UACjC,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;gBAChC;gBACA,MAAM,aAAa,MAAM,IAAI,CAAC,iBAAiB;gBAC/C,IAAI,YAAY,qBAAqB,wBAAwB;oBACzD,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc;gBACjD,OACK;oBACD,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc;gBAC7C;YACJ;QACJ,EACA,OAAO,GAAG;YACN,QAAQ,KAAK,CAAC,0BAA0B;QAC5C;IACJ;IACA,2BAA2B,KAAK,EAAE;QAC9B,IAAI,IAAI,CAAC,yBAAyB,KAAK,WAAW;YAC9C,MAAM,iBAAiB,IAAI;YAC3B,MAAM,aAAa,EAAE;YACrB,KAAK,MAAM,QAAQ,MAAO;gBACtB,IAAI,KAAK,IAAI,CAAC,EAAE,IAAI,KAAK,WAAW,EAAE;oBAClC,eAAe,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,WAAW;oBACjD,IAAI,KAAK,MAAM,KAAK,UAAU;wBAC1B,WAAW,IAAI,CAAC;4BACZ,WAAW;4BACX,IAAI,KAAK,IAAI,CAAC,EAAE;4BAChB,UAAU,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI,CAAC,EAAE;4BAC5C,KAAK,KAAK,IAAI;wBAClB;oBACJ,OACK;wBACD,WAAW,IAAI,CAAC;4BACZ,WAAW;4BACX,IAAI,KAAK,IAAI,CAAC,EAAE;4BAChB,UAAU,KAAK,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI,CAAC,EAAE;4BAC5C,KAAK,KAAK,IAAI;wBAClB;oBACJ;gBACJ;YACJ;YACA,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,YAAY;QAC3D;IACJ;IACA,MAAM,oBAAoB,IAAI,EAAE;QAC5B,aAAa,IAAI,CAAC,gBAAgB;QAClC,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,KAAK,MAAM,KAAK,UAAU;YAC1B,KAAK,IAAI,GAAG,6BAA6B,KAAK,IAAI;QACtD;QACA,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QAC7C,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,qDAAqD;YACrD,OAAO;QACX;QACA,MAAM,iBAAiB,MAAM,IAAI,CAAC,uBAAuB;QACzD,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,gBAAgB;YAChD,KAAK,IAAI,CAAC,mBAAmB,CAAC;QAClC;QACA,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YACtC,IAAI,CAAC,gBAAgB,GAAG,WAAW;gBAC/B,IAAI,CAAC,gBAAgB,GAAG;gBACxB,KAAK,IAAI,CAAC,mBAAmB,CAAC;YAClC,GAAG,IAAI,CAAC,2BAA2B;QACvC;QACA,OAAO;IACX;IACA,MAAM,iBAAiB;QACnB,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAChG,QAAQ;YACR,SAAS;gBAAE,QAAQ;YAAmB;YACtC,QAAQ,YAAY,OAAO,CAAC;YAC5B,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,QAAQ,GAAG,CAAC,+CACR,KAAK,SAAS,CAAC,MAAM,MAAM,KAC3B;QACR;QACA,OAAO;IACX;IACA,MAAM,oBAAoB;QACtB,IAAI,IAAI,CAAC,qBAAqB,KAAK,WAAW;YAC1C,IAAI,CAAC,qBAAqB,GAAG,CAAC;gBAC1B,IAAI,IAAI,CAAC,WAAW,KAAK,WAAW;oBAChC,IAAI;wBACA,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc;oBAChD,EACA,OAAO,GAAG;wBACN,QAAQ,IAAI,CAAC,CAAC,mFAAmF,EAAE,EAAE,MAAM,CAAC,sDAAsD,CAAC;oBACvK;gBACJ;gBACA,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC;YAChC,CAAC;QACL;QACA,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YACpC,IAAI,IAAI,CAAC,WAAW,KAAK,WAAW;gBAChC,IAAI,CAAC,qBAAqB,GAAG;YACjC;YACA,OAAO;QACX;IACJ;IACA,MAAM,eAAe;QACjB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC9B;QACA,OAAO,MAAM,IAAI,CAAC,QAAQ;IAC9B;IACA;;KAEC,GACD,MAAM,QAAQ;QACV,MAAM,iBAAiB,MAAM,IAAI,CAAC,uBAAuB;QACzD,MAAM,IAAI,CAAC,mBAAmB,CAAC;IACnC;IACA,2BAA2B;QACvB,MAAM,aAAa,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;QAC9B,MAAM,eAAe,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;QAClC,IAAI,IAAI,CAAC,yBAAyB,KAAK,WAAW;YAC9C,MAAM,cAAc,WAAW,aAAa;YAC5C,IAAI,aAAa;gBACb,OAAO,WAAW,OAAO,CAAC,aAAa,MAAM,IAAI;YACrD;QACJ;QACA,OAAO;IACX;IACA,MAAM,UAAU,GAAG,EAAE,OAAO,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC;YAAC;SAAI,EAAE,MAAM,EAAE;YACxC;QACJ;QACA,MAAM,UAAU;YACZ,GAAG,IAAI,CAAC,OAAO;YACf,gBAAgB;QACpB;QACA,MAAM,eAAe,IAAI,YAAY;QACrC,OAAO,IAAI,YAAY;QACvB,MAAM,YAAY,MAAM,IAAI,CAAC,8BAA8B,CAAC;YACxD;YACA,GAAG,GAAG;YACN,YAAY,IAAI,UAAU,IAAI,KAAK,GAAG;QAC1C;QACA,IAAI,IAAI,CAAC,gBAAgB,IACrB,UAAU,QAAQ,KAAK,aACvB,UAAU,YAAY,KAAK,WAAW;YACtC,MAAM,cAAc,IAAI,CAAC,wBAAwB;YACjD,KAAK,IAAI,CAAC,mBAAmB,CAAC;gBAC1B,QAAQ;gBACR,MAAM;gBACN;gBACA,QAAQ,SAAS;gBACjB,QAAQ,SAAS;YACrB,GAAG,KAAK,CAAC,QAAQ,KAAK;YACtB;QACJ;QACA,MAAM,uBAAuB,6BAA6B;QAC1D,IAAI,SAAS,WAAW,WAAW;YAC/B,OAAO,CAAC,YAAY,GAAG,QAAQ,MAAM;QACzC;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,SAAS,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACnH,QAAQ;YACR;YACA,MAAM,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,sBAAsB,CAAC,sBAAsB,EAAE,qBAAqB,EAAE,EAAE;YACzG,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,cAAc;IACjD;IACA;;;KAGC,GACD,MAAM,gBAAgB,EAAE,UAAU,EAAE,UAAU,EAAG,EAAE,OAAO,EAAE;QACxD,IAAI,eAAe,aAAa,eAAe,WAAW;YACtD;QACJ;QACA,IAAI,uBAAuB,MAAM,QAAQ,GAAG,CAAC,YAAY,IAAI,CAAC,SAAW,IAAI,CAAC,8BAA8B,CAAC,YAAY,EAAE;QAC3H,IAAI,uBAAuB,MAAM,QAAQ,GAAG,CAAC,YAAY,IAAI,CAAC,SAAW,IAAI,CAAC,8BAA8B,CAAC,YAAY,EAAE;QAC3H,IAAI,qBAAqB,MAAM,GAAG,KAAK,qBAAqB,MAAM,GAAG,GAAG;YACpE,MAAM,aAAa,qBAAqB,MAAM,CAAC,CAAC,QAAQ;gBACpD,IAAI,CAAC,IAAI,EAAE,EAAE;oBACT,OAAO;gBACX;gBACA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG;gBACjB,OAAO;YACX,GAAG,CAAC;YACJ,MAAM,oBAAoB,EAAE;YAC5B,KAAK,MAAM,eAAe,qBAAsB;gBAC5C,IAAI,YAAY,EAAE,KAAK,aAAa,UAAU,CAAC,YAAY,EAAE,CAAC,EAAE;oBAC5D,UAAU,CAAC,YAAY,EAAE,CAAC,GAAG;wBACzB,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC;wBAC7B,GAAG,WAAW;oBAClB;gBACJ,OACK;oBACD,kBAAkB,IAAI,CAAC;gBAC3B;YACJ;YACA,uBAAuB,OAAO,MAAM,CAAC;YACrC,uBAAuB;QAC3B;QACA,MAAM,WAAW;YACb,MAAM;YACN,OAAO;QACX;QACA,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,KAAK,CAAC,MAAM,EAAE;YACjD;QACJ;QACA,MAAM,cAAc;YAChB,MAAM,EAAE;YACR,OAAO,EAAE;QACb;QACA,KAAK,MAAM,KAAK;YAAC;YAAQ;SAAQ,CAAE;YAC/B,MAAM,MAAM;YACZ,MAAM,aAAa,QAAQ,CAAC,IAAI,CAAC,OAAO;YACxC,IAAI,YAAY,WAAW,GAAG;YAC9B,MAAO,cAAc,UAAW;gBAC5B,0DAA0D;gBAC1D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;gBACtB,YAAY,WAAW,GAAG;YAC9B;QACJ;QACA,IAAI,YAAY,IAAI,CAAC,MAAM,GAAG,KAAK,YAAY,KAAK,CAAC,MAAM,GAAG,GAAG;YAC7D,MAAM,SAAS,YAAY,IAAI,CAC1B,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE,EACrB,MAAM,CAAC,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE,GAC9C,IAAI,CAAC;YACV,MAAM,IAAI,CAAC,oBAAoB,CAAC,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,aAAa,CAAC,yBAAyB,EAAE,QAAQ,GAAG;QACnH;IACJ;IACA,MAAM,qBAAqB,IAAI,EAAE,OAAO,EAAE;QACtC,MAAM,UAAU;YACZ,GAAG,IAAI,CAAC,OAAO;YACf,gBAAgB;YAChB,QAAQ;QACZ;QACA,IAAI,SAAS,WAAW,WAAW;YAC/B,OAAO,CAAC,YAAY,GAAG,QAAQ,MAAM;QACzC;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,SAAS,UAAU,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;YACpI,QAAQ;YACR;YACA,MAAM;YACN,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,oBAAoB;IACvD;IACA;;;KAGC,GACD,MAAM,oBAAoB,EAAE,UAAU,EAAE,UAAU,EAAG,EAAE,OAAO,EAAE;QAC5D,IAAI,eAAe,aAAa,eAAe,WAAW;YACtD;QACJ;QACA,iCAAiC;QACjC,MAAM,iBAAiB,CAAC;QACxB,IAAI,uBAAuB,EAAE;QAC7B,KAAK,MAAM,UAAU,cAAc,EAAE,CAAE;YACnC,MAAM,iBAAiB,MAAM,IAAI,CAAC,8BAA8B,CAAC;YACjE,IAAI,eAAe,EAAE,KAAK,aACtB,eAAe,WAAW,KAAK,WAAW;gBAC1C,cAAc,CAAC,eAAe,EAAE,CAAC,GAAG,eAAe,WAAW;YAClE;YACA,OAAO,eAAe,WAAW;YACjC,qBAAqB,IAAI,CAAC;QAC9B;QACA,IAAI,uBAAuB,EAAE;QAC7B,KAAK,MAAM,UAAU,cAAc,EAAE,CAAE;YACnC,qBAAqB,IAAI,CAAC,MAAM,IAAI,CAAC,8BAA8B,CAAC;QACxE;QACA,oCAAoC;QACpC,MAAM,mBAAmB,qBAAqB,IAAI,CAAC,CAAC;YAChD,OAAQ,UAAU,QAAQ,KAAK,aAAa,UAAU,YAAY,KAAK;QAC3E;QACA,IAAI,qBAAqB,WAAW;YAChC,MAAM,IAAI,MAAM,CAAC,qFAAqF,CAAC;QAC3G;QACA,MAAM,mBAAmB,qBAAqB,IAAI,CAAC,CAAC;YAChD,OAAQ,UAAU,QAAQ,KAAK,aAAa,UAAU,YAAY,KAAK;QAC3E;QACA,IAAI,qBAAqB,WAAW;YAChC,MAAM,IAAI,MAAM,CAAC,qFAAqF,CAAC;QAC3G;QACA,8CAA8C;QAC9C,IAAI,qBAAqB,MAAM,GAAG,KAAK,qBAAqB,MAAM,GAAG,GAAG;YACpE,MAAM,aAAa,qBAAqB,MAAM,CAAC,CAAC,QAAQ;gBACpD,IAAI,CAAC,IAAI,EAAE,EAAE;oBACT,OAAO;gBACX;gBACA,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG;gBACjB,OAAO;YACX,GAAG,CAAC;YACJ,MAAM,oBAAoB,EAAE;YAC5B,KAAK,MAAM,eAAe,qBAAsB;gBAC5C,IAAI,YAAY,EAAE,KAAK,aAAa,UAAU,CAAC,YAAY,EAAE,CAAC,EAAE;oBAC5D,UAAU,CAAC,YAAY,EAAE,CAAC,GAAG;wBACzB,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC;wBAC7B,GAAG,WAAW;oBAClB;gBACJ,OACK;oBACD,kBAAkB,IAAI,CAAC;gBAC3B;YACJ;YACA,uBAAuB,OAAO,MAAM,CAAC;YACrC,uBAAuB;QAC3B;QACA,IAAI,qBAAqB,MAAM,KAAK,KAChC,qBAAqB,MAAM,KAAK,GAAG;YACnC;QACJ;QACA,sCAAsC;QACtC,MAAM,qBAAqB,EAAE;QAC7B,MAAM,mBAAmB,EAAE;QAC3B,KAAK,MAAM,CAAC,QAAQ,SAAS,IAAI;YAC7B;gBAAC;gBAAQ;aAAqB;YAC9B;gBAAC;gBAAS;aAAqB;SAClC,CAAE;YACC,KAAK,MAAM,mBAAmB,SAAU;gBACpC,8CAA8C;gBAC9C,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,SAAS,GAAG;gBAC7D,MAAM,SAAS;oBAAE;oBAAQ;oBAAS;gBAAO;gBACzC,8BAA8B;gBAC9B,MAAM,qBAAqB,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,SAAS,CAAC,oDAAoD,EAAE,QAAQ,EAAE,EAAE;gBAClI,iBAAiB,IAAI,CAAC;oBAClB,MAAM,GAAG,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE;oBAC/B,SAAS,IAAI,KAAK;wBAAC;qBAAmB,EAAE;wBACpC,MAAM,CAAC,yBAAyB,EAAE,mBAAmB,MAAM,EAAE;oBACjE;gBACJ;gBACA,iCAAiC;gBACjC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;oBAC/C,IAAI,UAAU,WAAW;wBACrB;oBACJ;oBACA,MAAM,mBAAmB,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,OAAO,CAAC,YAAY,EAAE,IAAI,yCAAyC,EAAE,QAAQ,EAAE,EAAE;oBACrI,iBAAiB,IAAI,CAAC;wBAClB,MAAM,GAAG,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK;wBACtC,SAAS,IAAI,KAAK;4BAAC;yBAAiB,EAAE;4BAClC,MAAM,CAAC,yBAAyB,EAAE,iBAAiB,MAAM,EAAE;wBAC/D;oBACJ;gBACJ;gBACA,yBAAyB;gBACzB,IAAI,QAAQ,EAAE,KAAK,WAAW;oBAC1B,MAAM,cAAc,cAAc,CAAC,QAAQ,EAAE,CAAC;oBAC9C,IAAI,aAAa;wBACb,OAAO,cAAc,CAAC,QAAQ,EAAE,CAAC;wBACjC,KAAK,MAAM,CAAC,MAAM,WAAW,IAAI,OAAO,OAAO,CAAC,aAAc;4BAC1D,IAAI;4BACJ,IAAI;4BACJ,IAAI,MAAM,OAAO,CAAC,aAAa;gCAC3B,CAAC,aAAa,QAAQ,GAAG;4BAC7B,OACK;gCACD,cAAc,WAAW,QAAQ;gCACjC,UAAU,WAAW,IAAI;4BAC7B;4BACA,0DAA0D;4BAC1D,IAAI,KAAK,QAAQ,CAAC,MAAM;gCACpB,QAAQ,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,UAAU,EAAE,QAAQ,EAAE,CAAC,2BAA2B,CAAC,GACzF,CAAC,4FAA4F,CAAC;gCAClG;4BACJ;4BACA,iBAAiB,IAAI,CAAC;gCAClB,MAAM,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM;gCACxC,SAAS,IAAI,KAAK;oCAAC;iCAAQ,EAAE;oCACzB,MAAM,GAAG,YAAY,SAAS,EAAE,QAAQ,UAAU,EAAE;gCACxD;4BACJ;wBACJ;oBACJ;gBACJ;gBACA,kBAAkB;gBAClB,mBAAmB,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE;YACxE;QACJ;QACA,MAAM,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,mBAAmB,IAAI,CAAC,OAAO;IACtF;IACA,MAAM,qBAAqB,KAAK,EAAE,QAAQ,EAAE;QACxC,kDAAkD;QAClD,MAAM,SAAS,EAAE;QACjB,KAAK,MAAM,QAAQ,MAAO;YACtB,qBAAqB;YACrB,OAAO,IAAI,CAAC,IAAI,KAAK;gBAAC,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC;aAAC;YAC1C,OAAO,IAAI,CAAC,IAAI,KAAK;gBACjB,CAAC,sCAAsC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC;gBACzD,CAAC,cAAc,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC/C;YACD,OAAO,IAAI,CAAC,KAAK,OAAO;YACxB,OAAO,IAAI,CAAC,IAAI,KAAK;gBAAC;aAAO;QACjC;QACA,qBAAqB;QACrB,OAAO,IAAI,CAAC,IAAI,KAAK;YAAC,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC;SAAC;QAC5C,wCAAwC;QACxC,MAAM,OAAO,IAAI,KAAK;QACtB,gDAAgD;QAChD,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,OAAO;IACX;IACA,MAAM,uBAAuB,KAAK,EAAE,QAAQ,EAAE;QAC1C,MAAM,UAAU,IAAI;QACpB,2DAA2D;QAC3D,qDAAqD;QACrD,MAAM,SAAS,IAAI,eAAe;YAC9B,MAAM,OAAM,UAAU;gBAClB,iDAAiD;gBACjD,MAAM,aAAa,OAAO;oBACtB,IAAI,OAAO,UAAU,UAAU;wBAC3B,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;oBACtC,OACK;wBACD,WAAW,OAAO,CAAC;oBACvB;gBACJ;gBACA,gCAAgC;gBAChC,KAAK,MAAM,QAAQ,MAAO;oBACtB,6BAA6B;oBAC7B,MAAM,WAAW,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC;oBACpC,MAAM,WAAW,CAAC,sCAAsC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC;oBAC1E,MAAM,WAAW,CAAC,cAAc,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAC7D,oBAAoB;oBACpB,MAAM,gBAAgB,KAAK,OAAO,CAAC,MAAM;oBACzC,MAAM,SAAS,cAAc,SAAS;oBACtC,IAAI;wBACA,IAAI;wBACJ,MAAO,CAAC,CAAC,SAAS,MAAM,OAAO,IAAI,EAAE,EAAE,IAAI,CAAE;4BACzC,WAAW,OAAO,CAAC,OAAO,KAAK;wBACnC;oBACJ,SACQ;wBACJ,OAAO,WAAW;oBACtB;oBACA,MAAM,WAAW;gBACrB;gBACA,uBAAuB;gBACvB,MAAM,WAAW,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC;gBACtC,WAAW,KAAK;YACpB;QACJ;QACA,OAAO;IACX;IACA,MAAM,sBAAsB,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE;QACjD,sCAAsC;QACtC,MAAM,WAAW,8BAA8B,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC;QAChF,MAAM,cAAc,CAAA,GAAA,0JAAA,CAAA,wCAAqC,AAAD;QACxD,MAAM,gBAAgB,IAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO;QAC7D,MAAM,cAAc,IAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO;QAC7D,MAAM,OAAO,OAAO;YAChB,MAAM,UAAU;gBACZ,GAAG,IAAI,CAAC,OAAO;gBACf,gBAAgB,CAAC,8BAA8B,EAAE,UAAU;YAC/D;YACA,IAAI,SAAS,WAAW,WAAW;gBAC/B,OAAO,CAAC,YAAY,GAAG,QAAQ,MAAM;YACzC;YACA,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,SAAS,UAAU,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE;gBACxH,QAAQ;gBACR;gBACA;gBACA,QAAQ;gBACR,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;gBAC3C,GAAG,IAAI,CAAC,YAAY;YACxB;QACJ;QACA,IAAI;YACA,IAAI;YACJ,IAAI,kBAAkB;YACtB,+DAA+D;YAC/D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBAClD,kBAAkB;gBAClB,MAAM,MAAM,KAAK,MAAM;YAC3B,OACK;gBACD,MAAM,MAAM,KAAK,MAAM;YAC3B;YACA,6CAA6C;YAC7C,IAAI,CAAC,CAAC,IAAI,CAAC,0BAA0B,IAAI,eAAe,KACpD,IAAI,MAAM,KAAK,OACf,CAAC,SAAS,UAAU,IAAI,CAAC,MAAM,MAAM,iBAAiB;gBACtD,QAAQ,IAAI,CAAC,CAAC,8BAA8B,EAAE,SAAS,UAAU,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAClG,CAAC,8DAA8D,CAAC,GAChE,CAAC,+CAA+C,EAAE,QAAQ,EAAE,CAAC;gBACjE,wCAAwC;gBACxC,IAAI,CAAC,0BAA0B,GAAG;gBAClC,iCAAiC;gBACjC,MAAM,MAAM,KAAK,MAAM;YAC3B;YACA,yBAAyB;YACzB,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,yBAAyB;QACnD,8DAA8D;QAClE,EACA,OAAO,GAAG;YACN,QAAQ,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,GAAG,aAAa,EAAE,SAAS;QAC7D;IACJ;IACA,MAAM,UAAU,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE;QACjC,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,IAAI,IAAI,MAAM,EAAE;YACZ,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,MAAM;QACpD;QACA,IAAI,IAAI,OAAO,EAAE;YACb,IAAI,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,OAAO;QACvD;QACA,uBAAuB;QACvB,MAAM,OAAO;YAAE,GAAG,GAAG;YAAE,IAAI;QAAM;QACjC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC;YAAC;SAAK,EAAE,MAAM,MAAM,EAAE;YAC/C;QACJ;QACA,IAAI,IAAI,CAAC,gBAAgB,IACrB,KAAK,QAAQ,KAAK,aAClB,KAAK,YAAY,KAAK,WAAW;YACjC,MAAM,cAAc,IAAI,CAAC,wBAAwB;YACjD,IAAI,IAAI,QAAQ,KAAK,aACjB,KAAK,aAAa,KAAK,aACvB,IAAI,CAAC,0BAA0B,IAC/B,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvB,iFAAiF;gBACjF,8BAA8B;gBAC9B,MAAM,IAAI,CAAC,mBAAmB,CAAC;oBAC3B,QAAQ;oBACR,MAAM;oBACN;oBACA,QAAQ,SAAS;oBACjB,QAAQ,SAAS;gBACrB,GAAG,KAAK,CAAC,QAAQ,KAAK;gBACtB;YACJ,OACK;gBACD,KAAK,IAAI,CAAC,mBAAmB,CAAC;oBAC1B,QAAQ;oBACR,MAAM;oBACN;oBACA,QAAQ,SAAS;oBACjB,QAAQ,SAAS;gBACrB,GAAG,KAAK,CAAC,QAAQ,KAAK;YAC1B;YACA;QACJ;QACA,MAAM,UAAU;YACZ,GAAG,IAAI,CAAC,OAAO;YACf,gBAAgB;QACpB;QACA,IAAI,SAAS,WAAW,WAAW;YAC/B,OAAO,CAAC,YAAY,GAAG,QAAQ,MAAM;QACzC;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,SAAS,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;YAC5H,QAAQ;YACR;YACA,MAAM,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,KAAK,CAAC,2CAA2C,EAAE,OAAO;YAC3F,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,cAAc;IACjD;IACA,MAAM,QAAQ,KAAK,EAAE,EAAE,aAAa,EAAE,GAAG;QAAE,eAAe;IAAM,CAAC,EAAE;QAC/D,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,IAAI,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO;QAC1C,IAAI,eAAe;YACf,MAAM,MAAM,IAAI,CAAC,cAAc,CAAC;QACpC;QACA,OAAO;IACX;IACA,MAAM,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW,EAAG,EAAE;QAC1C,IAAI,QAAQ,WAAW;YACnB,IAAI;YACJ,IAAI,IAAI,UAAU,EAAE;gBAChB,YAAY,IAAI,UAAU;YAC9B,OACK,IAAI,aAAa,aAAa;gBAC/B,YAAY,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC;oBAAE,aAAa,aAAa;gBAAY,EAAE,EAAE,EAAE;YACtF,OACK,IAAI,aAAa,WAAW;gBAC7B,YAAY,aAAa;YAC7B,OACK;gBACD,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;oBACnC,aAAa,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,cAAc;gBAC/D;gBACA,YAAY,QAAQ,EAAE;YAC1B;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;YACxC,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,EAAE,SAAS,YAAY,EAAE,UAAU,GAAG,EAAE,IAAI,EAAE,CAAC,UAAU,CAAC;QAC7F,OACK,IAAI,UAAU,WAAW;YAC1B,MAAM,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YAChC,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,gBAAgB,CAAC;YAClD;YACA,MAAM,UAAU,IAAI,CAAC,UAAU;YAC/B,OAAO,GAAG,UAAU,KAAK,QAAQ,EAAE;QACvC,OACK;YACD,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,MAAM,eAAe,GAAG,EAAE;QACtB,MAAM,YAAY,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC;YAC1C,QAAQ;YACR,WAAW,IAAI,UAAU;YACzB,SAAS,IAAI,QAAQ;QACzB;QACA,MAAM,UAAU,CAAC;QACjB,MAAM,OAAO,CAAC;QACd,+DAA+D;QAC/D,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,GAAG,gBAAgB,EAAE,EAAE,aAAa,CAAC,GAAG,gBAAgB;QAClF,KAAK,MAAM,YAAY,UAAW;YAC9B,IAAI,SAAS,aAAa,KAAK,QAC3B,SAAS,aAAa,KAAK,WAAW;gBACtC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,CAAC,cAAc,CAAC;YAC5D;YACA,IAAI,SAAS,YAAY,EAAE,WAAW,IAAI,YAAY,IAAI,OACtD,SAAS,EAAE,KAAK,IAAI,EAAE,EAAE;gBACxB,IAAI,CAAC,CAAC,SAAS,aAAa,IAAI,OAAO,GAAG;oBACtC,OAAO,CAAC,SAAS,aAAa,CAAC,GAAG,EAAE;gBACxC;gBACA,OAAO,CAAC,SAAS,aAAa,CAAC,CAAC,IAAI,CAAC;gBACrC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG;YACxB;QACJ;QACA,IAAI,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;QACtC,IAAK,MAAM,SAAS,QAAS;YACzB,IAAI,UAAU,IAAI,EAAE,EAAE;gBAClB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,OAAO,CAAC,MAAM;YAC3C;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAiFC,GACD,OAAO,SAAS,KAAK,EAAE;QACnB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAG,GAAG;QACnM,IAAI,aAAa,EAAE;QACnB,IAAI,WAAW;YACX,aAAa,MAAM,OAAO,CAAC,aAAa,YAAY;gBAAC;aAAU;QACnE;QACA,IAAI,aAAa;YACb,MAAM,eAAe,MAAM,OAAO,CAAC,eAC7B,cACA;gBAAC;aAAY;YACnB,MAAM,cAAc,MAAM,QAAQ,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,OAAS,IAAI,CAAC,WAAW,CAAC;oBAAE,aAAa;gBAAK,GAAG,IAAI,CAAC,CAAC,UAAY,QAAQ,EAAE;YACrI,WAAW,IAAI,IAAI;QACvB;QACA,MAAM,iBAAiB;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH;QACD,MAAM,OAAO;YACT,SAAS,WAAW,MAAM,GAAG,aAAa;YAC1C,UAAU;YACV,mBAAmB;YACnB;YACA;YACA,cAAc;YACd,aAAa;YACb,iBAAiB;YACjB,YAAY;YACZ,YAAY,YAAY,UAAU,WAAW,KAAK;YAClD;YACA;YACA;YACA,OAAO;YACP,QAAQ,SAAS,SAAS;YAC1B,SAAS;YACT;QACJ;QACA,IAAI,cAAc;QAClB,WAAW,MAAM,QAAQ,IAAI,CAAC,uBAAuB,CAAC,eAAe,MAAO;YACxE,IAAI,OAAO;gBACP,IAAI,eAAe,OAAO;oBACtB;gBACJ;gBACA,IAAI,KAAK,MAAM,GAAG,cAAc,OAAO;oBACnC,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,QAAQ;oBACtC,OAAO;oBACP;gBACJ;gBACA,eAAe,KAAK,MAAM;gBAC1B,OAAO;YACX,OACK;gBACD,OAAO;YACX;QACJ;IACJ;IACA,OAAO,cAAc,KAAK,EAAE;QACxB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAG,GAAG;QACxF,MAAM,YAAY,aAAa,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC;YAAE;QAAY,EAAE,EAAE,EAAE;QAC3E,MAAM,WAAW;YACb,YAAY;YACZ,UAAU;YACV;YACA,YAAY,YAAY,UAAU,WAAW,KAAK;YAClD,UAAU,UAAU,QAAQ,WAAW,KAAK;YAC5C,OAAO,OAAO,UAAU;QAC5B;QACA,IAAI,gBAAgB,OAAO,WAAW;QACtC,MAAM,OAAO;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM;QACnC,MAAO,KAAM;YACT,MAAM,cAAc;gBAChB,GAAG,QAAQ;gBACX,QAAQ;YACZ;YACA,2CAA2C;YAC3C,MAAM,kBAAkB,OAAO,WAAW,CAAC,OAAO,OAAO,CAAC,aAAa,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU;YACxG,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,KAAK,KAAK;gBACpE,QAAQ;gBACR,SAAS;oBAAE,GAAG,IAAI,CAAC,OAAO;oBAAE,gBAAgB;gBAAmB;gBAC/D,MAAM,KAAK,SAAS,CAAC;gBACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;gBAC3C,GAAG,IAAI,CAAC,YAAY;YACxB;YACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAC,gBAAgB,EAAE,MAAM;YACxD,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;YAC1B,IAAI,OAAO,MAAM,KAAK,GAAG;gBACrB;YACJ;YACA,KAAK,MAAM,UAAU,OAAQ;gBACzB,MAAM;YACV;YACA,iBAAiB,OAAO,MAAM;YAC9B,IAAI,iBAAiB,OAAO;gBACxB;YACJ;QACJ;IACJ;IACA,MAAM,YAAY,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,mBAAmB,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAG,EAAE;QAC5L,IAAI,cAAc,cAAc,EAAE;QAClC,IAAI,cAAc;YACd,cAAc;mBACN,cAAc,EAAE;mBAChB,MAAM,QAAQ,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,OAAS,IAAI,CAAC,WAAW,CAAC;wBAAE,aAAa;oBAAK,GAAG,IAAI,CAAC,CAAC,UAAY,QAAQ,EAAE;aACxH;QACL;QACA,MAAM,UAAU;YACZ;YACA;YACA,YAAY;YACZ,UAAU;YACV,SAAS;YACT,mBAAmB;YACnB,YAAY;YACZ,UAAU;YACV;YACA;YACA;YACA,cAAc;YACd,aAAa;YACb,SAAS;YACT,kBAAkB;QACtB;QACA,2CAA2C;QAC3C,MAAM,kBAAkB,OAAO,WAAW,CAAC,OAAO,OAAO,CAAC,SAAS,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU;QACpG,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;YACtG,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACX;IACA,MAAM,SAAS,KAAK,EAAE,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE;QACpC,MAAM,OAAO;YACT,QAAQ;YACR,aAAa,WAAW,oLAAA,CAAA,KAAO;QACnC;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,EAAE;YAC/G,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,IAAI,WAAW,QAAQ,CAAC,CAAC,iBAAiB,MAAM,GAAG;YAC/C,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,QAAQ,EAAE,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;IACnE;IACA,MAAM,WAAW,KAAK,EAAE;QACpB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,EAAE;YAC/G,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,eAAe;IAClD;IACA,MAAM,kBAAkB,KAAK,EAAE;QAC3B,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,EAAE;YAC/G,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,IAAI,WAAW,QAAQ,CAAC,CAAC,iBAAiB,MAAM,GAAG;YAC/C,OAAO;QACX;QACA,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,QAAQ,EAAE,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;IACnE;IACA,MAAM,eAAe,UAAU,EAAE,EAAE,MAAM,EAAG,GAAG,CAAC,CAAC,EAAE;QAC/C,MAAM,cAAc,IAAI,gBAAgB;YACpC,aAAa;QACjB;QACA,IAAI,WAAW,WAAW;YACtB,KAAK,MAAM,SAAS,OAAQ;gBACxB,YAAY,MAAM,CAAC,MAAM;YAC7B;QACJ;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,KAAK,EAAE,aAAa,EAAE;YACnI,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACX;IACA,MAAM,wBAAwB,SAAS,EAAE,WAAW,EAAE;QAClD,IAAI,CAAC,aAAa,CAAC,aAAa;YAC5B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,WAAW;YACZ,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY;YACrD,YAAY,QAAQ,EAAE;QAC1B;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,EAAE;YACvH,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,cAAc,MAAM,SAAS,IAAI;QACvC,YAAY,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,QAAQ,EAAE,YAAY,WAAW,CAAC,EAAE,CAAC;QAC5E,OAAO;IACX;IACA,MAAM,aAAa,SAAS,EAAE,WAAW,EAAE;QACvC,IAAI,CAAC,aAAa,CAAC,aAAa;YAC5B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,WAAW;YACZ,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY;YACrD,YAAY,QAAQ,EAAE;QAC1B;QACA,MAAM,OAAO;YACT,YAAY;QAChB;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,EAAE;YACvH,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,cAAc,MAAM,SAAS,IAAI;QACvC,YAAY,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,QAAQ,EAAE,YAAY,WAAW,CAAC,EAAE,CAAC;QAC5E,OAAO;IACX;IACA,MAAM,eAAe,SAAS,EAAE;QAC5B,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,EAAE;YACvH,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,mBAAmB;IACtD;IACA,MAAM,kBAAkB,UAAU,EAAE;QAChC,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,SAAS,CAAC,EAAE;YACzH,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,UAAU,MAAM,SAAS,IAAI;QACnC,OAAO;IACX;IACA;;;;;;;KAOC,GACD,MAAM,mBAAmB,UAAU,EAAE,OAAO,EAAE;QAC1C,MAAM,SAAS,CAAC;QAChB,IAAI,SAAS,YAAY;YACrB,OAAO,EAAE,GAAG,QAAQ,UAAU;QAClC;QACA,MAAM,YAAY,IAAI;QACtB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACxC,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACtB,MAAM,OAAO,CAAC,CAAC,IAAM,UAAU,MAAM,CAAC,KAAK;YAC/C,OACK;gBACD,UAAU,MAAM,CAAC,KAAK;YAC1B;QACJ;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,UAAU,EAAE,UAAU,QAAQ,IAAI,EAAE;YACjJ,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,IAAI,YAAY,QAAQ;gBACpB,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC,OAAO,MAAM,IAC9G,OAAO,MAAM,CAAC,IAAI,CAAC,QACnB,qBAAqB;YAC/B;YACA,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC/F;QACA,OAAO,OAAO,GAAG,CAAC,CAAC,UAAY,CAAC;gBAC5B,GAAG,OAAO;gBACV,UAAU,IAAI,CAAC,UAAU;YAC7B,CAAC;IACL;IACA,MAAM,cAAc,EAAE,WAAW,EAAE,cAAc,IAAI,EAAE,WAAW,IAAI,EAAE,SAAS,KAAK,EAAE,eAAe,IAAI,EAAE,qBAAqB,IAAI,EAAG,EAAE;QACvI,MAAM,UAAU,SAAS,CAAC,YAAY,CAAC,GAAG;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS;QACpD,MAAM,QAAQ,gBAAgB,CAAC;QAC/B,IAAI,UAAU;YACV,KAAK,CAAC,WAAW,GAAG;QACxB;QACA,MAAM,OAAO;YACT,MAAM;YACN;YACA;QACJ;QACA,IAAI,uBAAuB,MAAM;YAC7B,IAAI,CAAC,uBAAuB,GAAG;QACnC;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,UAAU;YACnF,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACX;IACA,MAAM,cAAc,SAAS,EAAE,EAAE,OAAO,IAAI,EAAE,cAAc,IAAI,EAAE,WAAW,IAAI,EAAE,eAAe,IAAI,EAAE,UAAU,IAAI,EAAG,EAAE;QACvH,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW;QACvD,IAAI,QAAQ;QACZ,IAAI,UAAU;YACV,QAAQ;gBAAE,GAAI,SAAS,CAAC,CAAC;gBAAG;YAAS;QACzC;QACA,MAAM,OAAO;YACT;YACA;YACA;YACA,UAAU,UAAU,IAAI,KAAK,SAAS,WAAW,KAAK;QAC1D;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,UAAU;YACnF,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACX;IACA,MAAM,WAAW,EAAE,SAAS,EAAE,WAAW,EAAG,EAAE;QAC1C,2BAA2B;QAC3B,IAAI,OAAO;QACX,MAAM,SAAS,IAAI;QACnB,IAAI,cAAc,aAAa,gBAAgB,WAAW;YACtD,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,cAAc,WAAW;YAC9B,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;YACX,QAAQ,CAAC,CAAC,EAAE,WAAW;QAC3B,OACK,IAAI,gBAAgB,WAAW;YAChC,OAAO,MAAM,CAAC,QAAQ;QAC1B,OACK;YACD,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,QAAQ,EAAE;YAC5G,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,sDAAsD;QACtD,qDAAqD;QACrD,IAAI;YACA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,OAAO;YACX;YACA,6EAA6E;YAC7E,IAAI,MAAM,OAAO,CAAC,SAAS;gBACvB,OAAO,OAAO,MAAM,GAAG;YAC3B;YACA,qBAAqB;YACrB,OAAO;QACX,EACA,OAAO,GAAG;YACN,OAAO;QACX;IACJ;IACA,MAAM,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAG,EAAE;QACzD,IAAI,OAAO;QACX,MAAM,SAAS,IAAI;QACnB,IAAI,cAAc,aAAa,gBAAgB,WAAW;YACtD,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,cAAc,WAAW;YAC9B,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;YACX,QAAQ,CAAC,CAAC,EAAE,WAAW;QAC3B,OACK,IAAI,gBAAgB,WAAW;YAChC,OAAO,MAAM,CAAC,QAAQ;QAC1B,OACK;YACD,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,iBAAiB,WAAW;YAC5B,OAAO,MAAM,CAAC,iBAAiB,aAAa,QAAQ;QACxD;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM;QACvC,IAAI;QACJ,IAAI,MAAM,OAAO,CAAC,WAAW;YACzB,IAAI,SAAS,MAAM,KAAK,GAAG;gBACvB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,EAAE,YAAY,WAAW,CAAC;YAC7E;YACA,SAAS,QAAQ,CAAC,EAAE;QACxB,OACK;YACD,SAAS;QACb;QACA,OAAO;IACX;IACA,MAAM,cAAc,EAAE,SAAS,EAAE,WAAW,EAAG,EAAE;QAC7C,IAAI,cAAc,aAAa,gBAAgB,WAAW;YACtD,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YAAE;YAAW;QAAY;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,EAAE,SAAS,YAAY,EAAE,QAAQ,EAAE,EAAE;IACxE;IACA,MAAM,cAAc,EAAE,SAAS,EAAE,WAAW,EAAG,EAAE;QAC7C,IAAI,cAAc,aAAa,gBAAgB,WAAW;YACtD,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YAAE;YAAW;QAAY;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,GAAG,EAAE,SAAS,UAAU,EAAE,QAAQ,EAAE,EAAE;IACtE;IACA,MAAM,eAAe;QACjB,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM;YACzB,OAAO,IAAI,CAAC,SAAS;QACzB;QACA,MAAM,cAAc,IAAI,gBAAgB;YAAE,OAAO;QAAI;QACrD,WAAW,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,aAAa,aAAc;YACvE,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,EAAE,CAAC,SAAS;YACtC,OAAO,QAAQ,CAAC,EAAE,CAAC,SAAS;QAChC;QACA,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,aAAa,EAAE,QAAQ,EAAG,GAAG,CAAC,CAAC,EAAE;QAC7H,MAAM,SAAS,IAAI;QACnB,IAAI,eAAe,WAAW;YAC1B,KAAK,MAAM,aAAa,WAAY;gBAChC,OAAO,MAAM,CAAC,MAAM;YACxB;QACJ;QACA,IAAI,SAAS,WAAW;YACpB,OAAO,MAAM,CAAC,QAAQ;QAC1B;QACA,IAAI,iBAAiB,WAAW;YAC5B,OAAO,MAAM,CAAC,iBAAiB;QACnC;QACA,IAAI,uBAAuB,WAAW;YAClC,OAAO,MAAM,CAAC,qBAAqB;QACvC,OACK,IAAI,yBAAyB,WAAW;YACzC,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBACnC,aAAa;YACjB;YACA,OAAO,MAAM,CAAC,qBAAqB,QAAQ,EAAE;QACjD;QACA,IAAI,kBAAkB,WAAW;YAC7B,OAAO,MAAM,CAAC,kBAAkB,cAAc,QAAQ;QAC1D;QACA,IAAI,aAAa,WAAW;YACxB,OAAO,MAAM,CAAC,YAAY,KAAK,SAAS,CAAC;QAC7C;QACA,WAAW,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,aAAa,QAAS;YAClE,OAAO;QACX;IACJ;IACA,MAAM,cAAc,EAAE,SAAS,EAAE,WAAW,EAAG,EAAE;QAC7C,IAAI;QACJ,IAAI,cAAc,aAAa,gBAAgB,WAAW;YACtD,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,cAAc,aAAa,gBAAgB,WAAW;YAC3D,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,cAAc,WAAW;YAC9B,aAAa,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY,EAAE,EAAE,EAAE;QAC7D,OACK;YACD,aAAa;QACjB;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,EAAE;YAClH,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAC,eAAe,EAAE,WAAW,EAAE,EAAE,YAAY,CAAC,CAAC,EAAE;IACpF;IACA,MAAM,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAG,EAAE;QACxF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC5C,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,SAAS;QACjC,UAAU,OAAO,CAAC,CAAC;YACf,SAAS,MAAM,CAAC,cAAc;QAClC;QACA,WAAW,OAAO,CAAC,CAAC;YAChB,SAAS,MAAM,CAAC,eAAe;QACnC;QACA,IAAI,aAAa;YACb,SAAS,MAAM,CAAC,eAAe;QACnC;QACA,IAAI,UAAU;YACV,SAAS,MAAM,CAAC,aAAa;QACjC;QACA,IAAI,MAAM;YACN,SAAS,MAAM,CAAC,QAAQ;QAC5B;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK;YAC9E,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM;YACN,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACX;IACA,MAAM,cAAc,IAAI,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAG,GAAG,CAAC,CAAC,EAAE;QAC9F,MAAM,OAAO;YACT;YACA;YACA,OAAO,WAAW;gBAAE;YAAS,IAAI;QACrC;QACA,IAAI,UAAU;YACV,KAAK,SAAS,GAAG;QACrB;QACA,IAAI,cAAc;YACd,KAAK,wBAAwB,GAAG;QACpC;QACA,IAAI,eAAe;YACf,KAAK,yBAAyB,GAAG;QACrC;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YACpG,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACX;IACA,MAAM,YAAY,EAAE,SAAS,EAAE,WAAW,EAAG,EAAE;QAC3C,IAAI,OAAO;QACX,oBAAoB;QACpB,MAAM,SAAS,IAAI,gBAAgB;YAAE,OAAO;QAAI;QAChD,IAAI,aAAa,aAAa;YAC1B,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,WAAW;YAChB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;YACX,QAAQ,CAAC,CAAC,EAAE,WAAW;QAC3B,OACK,IAAI,aAAa;YAClB,OAAO,MAAM,CAAC,QAAQ;QAC1B,OACK;YACD,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM;QACvC,IAAI;QACJ,IAAI,MAAM,OAAO,CAAC,WAAW;YACzB,IAAI,SAAS,MAAM,KAAK,GAAG;gBACvB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,UAAU,OAAO,EAAE,YAAY,WAAW,CAAC;YAC7E;YACA,SAAS,QAAQ,CAAC,EAAE;QACxB,OACK;YACD,SAAS;QACb;QACA,OAAO;IACX;IACA,MAAM,WAAW,EAAE,SAAS,EAAE,WAAW,EAAG,EAAE;QAC1C,IAAI;YACA,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;gBAAW;YAAY;YAChD,OAAO;QACX,EACA,OAAO,GAAG;YACN,IACA,uDAAuD;YACvD,aAAa,SACT,EAAE,OAAO,CAAC,iBAAiB,GAAG,QAAQ,CAAC,cAAc;gBACrD,OAAO;YACX;YACA,MAAM;QACV;IACJ;IACA,MAAM,oBAAoB,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAG,EAAE;QAC3E,IAAI,aAAa;QACjB,IAAI,eAAe,aAAa,gBAAgB,WAAW;YACvD,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,eAAe,aAAa,gBAAgB,WAAW;YAC5D,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,eAAe,WAAW;YAC/B,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY;YACrD,aAAa,QAAQ,EAAE;QAC3B;QACA,MAAM,YAAY,IAAI,gBAAgB;YAClC,cAAc,OAAO,gBAAgB,WAC/B,cACA,YAAY,WAAW;YAC7B,YAAY,OAAO,cAAc,WAAW,YAAY,UAAU,WAAW;QACjF;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,WAAW,cAAc,CAAC,EAAE;QAC1E,OAAO;IACX;IACA,MAAM,4BAA4B,EAAE,SAAS,EAAE,WAAW,EAAG,EAAE;QAC3D,MAAM,OAAO;QACb,IAAI,cAAc,WAAW;QACzB,aAAa;QACjB,OACK,IAAI,gBAAgB,WAAW;YAChC,YAAY,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY,EAAE,EAAE,EAAE;QAC5D,OACK;YACD,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,EAAE,UAAU,UAAU,CAAC;QACzE,MAAM,cAAc,MAAM,SAAS,IAAI;QACvC,MAAM,UAAU,YACX,IAAI,GACJ,KAAK,CAAC,MACN,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK,CAAC;QAC9B,OAAO;IACX;IACA,OAAO,aAAa,EAAE,QAAQ,GAAG,EAAE,SAAS,CAAC,EAAE,UAAU,EAAE,WAAW,EAAE,mBAAmB,EAAE,QAAQ,EAAG,GAAG,CAAC,CAAC,EAAE;QAC3G,MAAM,OAAO;QACb,MAAM,SAAS,IAAI,gBAAgB;YAC/B,OAAO,MAAM,QAAQ;YACrB,QAAQ,OAAO,QAAQ;QAC3B;QACA,IAAI,eAAe,WAAW;YAC1B,KAAK,MAAM,OAAO,WAAY;gBAC1B,OAAO,MAAM,CAAC,MAAM;YACxB;QACJ;QACA,IAAI,gBAAgB,WAAW;YAC3B,OAAO,MAAM,CAAC,QAAQ;QAC1B;QACA,IAAI,wBAAwB,WAAW;YACnC,OAAO,MAAM,CAAC,iBAAiB;QACnC;QACA,IAAI,aAAa,WAAW;YACxB,OAAO,MAAM,CAAC,YAAY,KAAK,SAAS,CAAC;QAC7C;QACA,WAAW,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,MAAM,QAAS;YAC3D,OAAO;QACX;IACJ;IACA;;;;KAIC,GACD,MAAM,cAAc,KAAK,EAAE;QACvB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,QAAQ,GAAG;QAC9C,IAAI,CAAC,aAAa,CAAC,aAAa;YAC5B,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,aAAa,aAAa,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC;YAAE;QAAY,EAAE,EAAE,EAAE;QAC5E,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,EAAE;YAClH,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,OAAQ,MAAM,SAAS,IAAI;IAC/B;IACA;;;;;;;;;;;;;;KAcC,GACD,MAAM,iBAAiB,KAAK,EAAE;QAC1B,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;QAC9C,IAAI,CAAC,aAAa,CAAC,aAAa;YAC5B,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,aAAa,aAAa,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC;YAAE;QAAY,EAAE,EAAE,EAAE;QAC5E,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,KAAK,CAAC,EAAE;YACvH,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;gBACjB,OAAO,OAAO,SAAS,WAAW,OAAO,KAAK,WAAW;gBACzD;YACJ;YACA,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;IACnC;IACA,MAAM,cAAc,EAAE,SAAS,EAAE,WAAW,EAAG,EAAE;QAC7C,IAAI,OAAO;QACX,IAAI,aAAa;QACjB,IAAI,cAAc,aAAa,gBAAgB,WAAW;YACtD,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,gBAAgB,WAAW;YAChC,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY;YACrD,aAAa,QAAQ,EAAE;QAC3B;QACA,IAAI,eAAe,WAAW;YAC1B,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;YACX,QAAQ,CAAC,CAAC,EAAE,YAAY;QAC5B,OACK;YACD,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM;YAC7F,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAC,OAAO,EAAE,MAAM;QAC/C,MAAM,SAAS,IAAI;IACvB;IACA,MAAM,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,EAAG,EAAE;QACjD,IAAI,aAAa;QACjB,IAAI,CAAC,cAAc,CAAC,aAAa;YAC7B,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,cAAc,aAAa;YAChC,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,CAAC,YAAY;YAClB,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY;YACrD,aAAa,QAAQ,EAAE;QAC3B;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,OAAO;YACT,KAAK;QACT;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,MAAM,CAAC,EAAE;YACxH,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,SAAS,IAAI;IACvB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6BC,GACD,MAAM,gBAAgB,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,MAAM,EAAG,GAAG,CAAC,CAAC,EAAE;QAC9D,MAAM,OAAO;YACT,OAAO;YACP,QAAQ;QACZ;QACA,IAAI,WAAW,WAAW;YACtB,IAAI,CAAC,SAAS,GAAG;QACrB;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YACxH,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,MAAM,CAAC,WAAW;IAC7B;IACA,MAAM,cAAc,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE;QAClD,IAAI,gBAAgB,iBAAiB;YACjC,IAAI,YAAY,aAAa,YAAY,WAAW;gBAChD,MAAM,IAAI,MAAM;YACpB;QACJ;QACA,IAAI,aAAa,UAAU,SAAS,YAAY,eAAe,UAAU;QACzE,MAAM,eAAe,UACf,SAAS,cACT,eAAe,YAAY;QACjC,IAAI,eAAe,aAAa,iBAAiB,WAAW;YACxD,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,eAAe,aAAa,iBAAiB,WAAW;YAC7D,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,eAAe,WAAW;YAC/B,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE,aAAa;YAAa;YACnE,aAAa,QAAQ,EAAE;QAC3B;QACA,MAAM,aAAa,CAAC,UAAU,SAAS,YAAY,eAAe,UAAU,KAAK,IAAI;QACrF,IAAI;QACJ,IAAI,CAAC,gBAAgB,iBAAiB;YAClC,OAAO;gBACH,QAAQ;gBACR;gBACA,YAAY,YAAY;gBACxB,IAAI,SAAS;gBACb,UAAU,SAAS;gBACnB,OAAO,SAAS;gBAChB,eAAe,SAAS;gBACxB,mBAAmB,SAAS;gBAC5B,4BAA4B,SAAS;gBACrC,aAAa,SAAS;YAC1B;QACJ,OACK;YACD,OAAO;QACX;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY;YAAC;SAAK;QACvE,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,WAAW,EAAE,CAAC,EAAE,IAAI,oLAAA,CAAA,KAAO;QAC3E,OAAO;IACX;IACA,MAAM,eAAe,cAAc,EAAE;QACjC,IAAI,MAAM,OAAO,CAAC,iBAAiB;YAC/B,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC7B,OAAO,EAAE;YACb;YACA,MAAM,UAAU;YAChB,IAAI,aAAa,OAAO,CAAC,EAAE,CAAC,UAAU;YACtC,MAAM,eAAe,OAAO,CAAC,EAAE,CAAC,YAAY;YAC5C,IAAI,eAAe,aAAa,iBAAiB,WAAW;gBACxD,MAAM,IAAI,MAAM;YACpB,OACK,IAAI,eAAe,aAAa,iBAAiB,WAAW;gBAC7D,MAAM,IAAI,MAAM;YACpB,OACK,IAAI,eAAe,WAAW;gBAC/B,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;oBAAE,aAAa;gBAAa;gBACnE,aAAa,QAAQ,EAAE;YAC3B;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY;YACjE,MAAM,WAAW,MAAM,QAAQ,GAAG,CAAC,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,KAAO,IAAI,CAAC,WAAW,CAAC;YACrF,OAAO;QACX;QACA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,uBAAuB,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAG,GAAG;QACxJ,IAAI,WAAW,WAAW;YACtB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,aAAa;QACjB,MAAM,eAAe;QACrB,IAAI,eAAe,aAAa,iBAAiB,WAAW;YACxD,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,eAAe,aAAa,iBAAiB,WAAW;YAC7D,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,eAAe,WAAW;YAC/B,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE,aAAa;YAAa;YACnE,aAAa,QAAQ,EAAE;QAC3B;QACA,MAAM,oBAAoB,OAAO,GAAG,CAAC,CAAC,OAAO;YACzC,OAAO;gBACH,YAAY;gBACZ,QAAQ;gBACR,SAAS,SAAS,CAAC,IAAI;gBACvB,UAAU,UAAU,CAAC,IAAI;gBACzB,OAAO,QAAQ,CAAC,IAAI;gBACpB,IAAI,YAAY,CAAC,IAAI;gBACrB,aAAa,aAAa,CAAC,IAAI;gBAC/B,eAAe,cAAc,CAAC,IAAI;gBAClC,mBAAmB,iBAAiB,CAAC,IAAI;gBACzC,4BAA4B,yBAAyB,CAAC,IAAI;YAC9D;QACJ;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY;QACjE,MAAM,WAAW,MAAM,QAAQ,GAAG,CAAC,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,KAAO,IAAI,CAAC,WAAW,CAAC;QACrF,OAAO;IACX;IACA,MAAM,iBAAiB,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE;QAC/C,OAAO,IAAI,CAAC,aAAa,CAAC;YAAE;QAAM,GAAG;YAAE,QAAQ;QAAW,GAAG;IACjE;IACA,MAAM,kBAAkB,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE;QACjD,MAAM,aAAa,MAAM,GAAG,CAAC,CAAC;YAC1B,IAAI,CAAA,GAAA,wJAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;gBAC7B,OAAO,CAAA,GAAA,wJAAA,CAAA,mCAAgC,AAAD,EAAE;YAC5C;YACA,OAAO;QACX;QACA,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,qBAAkB,AAAD,EAAE,eACjC,CAAA,GAAA,wJAAA,CAAA,mCAAgC,AAAD,EAAE,eACjC;QACN,OAAO,IAAI,CAAC,aAAa,CAAC;YAAE,OAAO;QAAW,GAAG;YAAE,QAAQ;QAAY,GAAG;IAC9E;IACA,MAAM,YAAY,SAAS,EAAE;QACzB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,OAAO,CAAC,UAAU,EAAE,WAAW;QACrC,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,CAAC;QACnC,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,GAAG;QACrC,MAAM,UAAU;QAChB,IAAI,iBAAiB;YACjB,QAAQ,WAAW,GAAG,OAAO,OAAO,CAAC,iBAAiB,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM;gBAC3E,GAAG,CAAC,IAAI,KAAK,CAAC,cAAc,MAAM,EAAE,GAAG;oBACnC,eAAe,MAAM,aAAa;oBAClC,WAAW,MAAM,SAAS;gBAC9B;gBACA,OAAO;YACX,GAAG,CAAC;QACR;QACA,OAAO;IACX;IACA,OAAO,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAG,GAAG,CAAC,CAAC,EAAE;QAC/I,IAAI;QACJ,IAAI,cAAc,aAAa,gBAAgB,WAAW;YACtD,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,cAAc,WAAW;YAC9B,aAAa;QACjB,OACK,IAAI,gBAAgB,WAAW;YAChC,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY;YACrD,aAAa,QAAQ,EAAE;QAC3B,OACK;YACD,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,SAAS,IAAI,gBAAgB;YAAE,SAAS;QAAW;QACzD,MAAM,kBAAkB,OAClB,OAAO,SAAS,WACZ,OACA,MAAM,gBACV;QACN,IAAI,iBAAiB;YACjB,OAAO,MAAM,CAAC,SAAS;QAC3B;QACA,MAAM,gBAAgB,gBAAgB;QACtC,OAAO,MAAM,CAAC,kBAAkB,cAAc,QAAQ;QACtD,IAAI,eAAe,WAAW;YAC1B,KAAK,MAAM,OAAO,WAAY;gBAC1B,OAAO,MAAM,CAAC,MAAM;YACxB;QACJ;QACA,IAAI,WAAW,WAAW;YACtB,KAAK,MAAM,SAAS,OAAQ;gBACxB,OAAO,MAAM,CAAC,UAAU;YAC5B;QACJ;QACA,IAAI,aAAa,WAAW;YACxB,MAAM,qBAAqB,KAAK,SAAS,CAAC;YAC1C,OAAO,MAAM,CAAC,YAAY;QAC9B;QACA,IAAI,UAAU,WAAW;YACrB,OAAO,MAAM,CAAC,SAAS,MAAM,QAAQ;QACzC;QACA,IAAI,WAAW,WAAW;YACtB,OAAO,MAAM,CAAC,UAAU,OAAO,QAAQ;QAC3C;QACA,IAAI,WAAW,WAAW;YACtB,OAAO,MAAM,CAAC,UAAU;QAC5B;QACA,IAAI,uBAAuB,MAAM;YAC7B;gBAAC;gBAAmB;gBAAW;aAAW,CAAC,OAAO,CAAC,CAAC,QAAU,OAAO,MAAM,CAAC,UAAU;QAC1F;QACA,IAAI,IAAI;QACR,WAAW,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,aAAa,QAAS;YACrE,KAAK,MAAM,cAAc,YAAa;gBAClC,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,GAAG;gBACrC,MAAM,UAAU;gBAChB,IAAI,iBAAiB;oBACjB,QAAQ,WAAW,GAAG,OAAO,OAAO,CAAC,iBAAiB,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM;wBAC3E,GAAG,CAAC,IAAI,KAAK,CAAC,cAAc,MAAM,EAAE,GAAG;4BACnC,eAAe,MAAM,aAAa;4BAClC,WAAW,MAAM,SAAS,IAAI;wBAClC;wBACA,OAAO;oBACX,GAAG,CAAC;gBACR;gBACA,MAAM;gBACN;YACJ;YACA,IAAI,UAAU,aAAa,KAAK,OAAO;gBACnC;YACJ;QACJ;IACJ;IACA,MAAM,cAAc,SAAS,EAAE;QAC3B,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,OAAO,CAAC,UAAU,EAAE,WAAW;QACrC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM;YAC7F,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAC,OAAO,EAAE,MAAM;QAC/C,MAAM,SAAS,IAAI;IACvB;IACA,MAAM,cAAc,iBAAiB,EAAE,MAAM,EAAE;QAC3C,IAAI;QACJ,IAAI,QAAQ;YACR,YAAY;QAChB,OACK;YACD,YAAY,kBAAkB,EAAE;QACpC;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,IAAI;QACJ,IAAI,QAAQ;YACR,cAAc;gBAAE,IAAI;gBAAW,GAAG,MAAM;YAAC;QAC7C,OACK;YACD,cAAc;QAClB;QACA,IAAI;QACJ,IAAI,YAAY,UAAU,KAAK,WAAW;YACtC,YAAY,YAAY,UAAU;QACtC,OACK;YACD,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,YAAY,QAAQ,UAAU;QAClC;QACA,OAAO,IAAI,CAAC,wBAAwB,CAAC,WAAW;YAAC;SAAY;IACjE;IACA,MAAM,eAAe,MAAM,EAAE;QACzB,gFAAgF;QAChF,IAAI;QACJ,IAAI,MAAM,CAAC,EAAE,CAAC,UAAU,KAAK,WAAW;YACpC,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;YACnD,YAAY,QAAQ,UAAU;QAClC,OACK;YACD,YAAY,MAAM,CAAC,EAAE,CAAC,UAAU;QACpC;QACA,OAAO,IAAI,CAAC,wBAAwB,CAAC,WAAW;IACpD;IACA;;;;;;;;;;;KAWC,GACD,MAAM,mBAAmB,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAG,EAAE;QAC7D,IAAI;QACJ,IAAI,CAAC,WAAW;YACZ,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY;YACrD,oBAAoB,QAAQ,EAAE;QAClC,OACK;YACD,oBAAoB;QACxB;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,IAAI,AAAC,QAAQ,OAAS,CAAC,QAAQ,CAAC,KAAM;YAClC,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,WAAW;YACpB,OAAO,MAAM,CAAC,SAAS,OAAO,SAAS,WAAW,OAAO,KAAK,WAAW;QAC7E;QACA,IAAI,QAAQ,WAAW;YACnB,OAAO,MAAM,CAAC,OAAO;QACzB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,kBAAkB,SAAS,EAAE,OAAO,QAAQ,IAAI,EAAE;YACtJ,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;YAAC;YAC3B,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,OAAO,MAAM,SAAS,IAAI;IAC9B;IACA,MAAM,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,EAAG,EAAE;QACvD,IAAI;QACJ,IAAI,cAAc,aAAa,gBAAgB,WAAW;YACtD,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,cAAc,aAAa,gBAAgB,WAAW;YAC3D,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,cAAc,WAAW;YAC9B,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY;YACrD,aAAa,QAAQ,EAAE;QAC3B,OACK;YACD,aAAa;QACjB;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,SAAS,IAAI;QACnB,MAAM,kBAAkB,OAClB,OAAO,SAAS,WACZ,OACA,MAAM,gBACV;QACN,IAAI,iBAAiB;YACjB,OAAO,MAAM,CAAC,SAAS;QAC3B;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,WAAW,OAAO,CAAC,EAAE;QACnE,OAAO;IACX;IACA,MAAM,oBAAoB,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,KAAK,EAAG,EAAE;QAC1F,IAAI;QACJ,IAAI,cAAc,aAAa,gBAAgB,WAAW;YACtD,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,cAAc,aAAa,gBAAgB,WAAW;YAC3D,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,cAAc,WAAW;YAC9B,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;gBAAE;YAAY;YACrD,aAAa,QAAQ,EAAE;QAC3B,OACK;YACD,aAAa;QACjB;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,OAAO;YACT,YAAY;YACZ,UAAU,WAAW,GAAG,CAAC,CAAC;gBACtB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;gBACX,OAAO;YACX;YACA;QACJ;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,OAAO,CAAC,EAAE;YACzH,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,yBAAyB;IAC5D;IACA;;KAEC,GACD,MAAM,YAAY,GAAG,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE,gBAAgB,EAAG,GAAG;QAAE,eAAe;IAAM,CAAC,EAAE;QAC3G,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;QACT,IAAI;QACJ,IAAI,OAAO,QAAQ,UAAU;YACzB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK;gBAAE;YAAc;QACnD,OACK,IAAI,OAAO,QAAQ,YAAY,QAAQ,KAAK;YAC7C,OAAO;QACX,OACK;YACD,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,OAAO,KAAK;QACrD;QACA,IAAI,KAAK,oBAAoB,KAAK,QAC9B,KAAK,oBAAoB,KAAK,WAAW;YACzC,mBAAmB,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,oBAAoB;QACvE;QACA,MAAM,iBAAiB,MAAM,UAAU,WAAW,CAAC,MAAM;QACzD,MAAM,CAAC,GAAG,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,MAAM;QAC/E,OAAO,SAAS,CAAC,EAAE;IACvB;IACA,MAAM,eAAe,KAAK,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,qBAAqB,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,uBAAuB,EAAG,EAAE;QAC1L,IAAI,CAAC,SAAS,CAAC,WAAW;YACtB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,SAAS,WAAW;YACpB,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,kBAAkB;YACpB,MAAM,sBAAsB;YAC5B,UAAU,cAAc,CAAC;QAC7B;QACA,IAAI,gBAAgB,aAChB,iBAAiB,aAAa,aAC9B,CAAC,gBAAgB,QAAQ,CAAC,QAAQ,EAAE;YACpC,gBAAgB,QAAQ,CAAC,QAAQ,GAAG;gBAAE,QAAQ;YAAY;QAC9D;QACA,IAAI,iBAAiB,aAAa,aAC9B,gBAAgB,QAAQ,CAAC,QAAQ,EAAE,WAAW,WAAW;YACzD,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,gBAAgB,QAAQ,CAAC,QAAQ,CAAC,MAAM;QACvD;QACA,MAAM,WAAW;YACb,IAAI,cAAc,oLAAA,CAAA,KAAO;YACzB,QAAQ;YACR;YACA,OAAO,qBAAqB;YAC5B;YACA;YACA;YACA,iBAAiB;YACjB,2BAA2B;YAC3B;YACA,YAAY;QAChB;QACA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QACrC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK;YAC9E,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,mBAAmB;QAClD,OAAO;IACX;IACA,MAAM,eAAe,UAAU,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAG,EAAE;QACrE,MAAM,iBAAiB,CAAC;QACxB,IAAI,UAAU,aAAa,UAAU,MAAM;YACvC,cAAc,CAAC,QAAQ,GAAG,qBAAqB;QACnD;QACA,IAAI,UAAU,aAAa,UAAU,MAAM;YACvC,cAAc,CAAC,QAAQ,GAAG;QAC9B;QACA,IAAI,eAAe,aAAa,eAAe,MAAM;YACjD,cAAc,CAAC,aAAa,GAAG;QACnC;QACA,IAAI,YAAY,aAAa,YAAY,MAAM;YAC3C,cAAc,CAAC,UAAU,GAAG;QAChC;QACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,EAAE;YAClH,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,mBAAmB;IACtD;IACA,MAAM,aAAa,UAAU,EAAE;QAC3B,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,OAAO,CAAC,UAAU,EAAE,YAAY;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC;QACjC,OAAO;IACX;IACA,MAAM,eAAe,UAAU,EAAE;QAC7B,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,OAAO,CAAC,UAAU,EAAE,YAAY;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM;YAC7F,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAC,OAAO,EAAE,MAAM;QAC/C,MAAM,SAAS,IAAI;IACvB;IACA,OAAO,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,mBAAmB,EAAG,GAAG,CAAC,CAAC,EAAE;QACrE,MAAM,cAAc,IAAI;QACxB,IAAI,QAAQ;YACR,YAAY,MAAM,CAAC,OAAO,OAAO,IAAI,CAAC;QAC1C;QACA,IAAI,cAAc;YACd,KAAK,MAAM,OAAO,aAAc;gBAC5B,YAAY,MAAM,CAAC,OAAO;YAC9B;QACJ;QACA,IAAI,qBAAqB;YACrB,KAAK,MAAM,QAAQ,oBAAqB;gBACpC,YAAY,MAAM,CAAC,UAAU;YACjC;QACJ;QACA,WAAW,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,aAAa,aAAc;YACxE,OAAO;QACX;IACJ;IACA;;;;;;;;;;;;;;KAcC,GACD,MAAM,6BAA6B,KAAK,EAAE,WAAW,EAAE,EAAE,UAAU,EAAE,cAAc,EAAG,GAAG,CAAC,CAAC,EAAE;QACzF,MAAM,OAAO;YACT,QAAQ;YACR,cAAc;YACd,iBAAiB;QACrB;QACA,IAAI,YAAY;YACZ,IAAI,OAAO,eAAe,UAAU;gBAChC,IAAI,CAAC,aAAa,GAAG;YACzB,OACK,IAAI,YAAY,SAAS,YAAY,WAAW,YAAY,MAAM;gBACnE,IAAI,CAAC,aAAa,GAAG;YACzB;QACJ,OACK;YACD,IAAI,CAAC,aAAa,GAAG;gBACjB,OAAO;YACX;QACJ;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE;YAC3G,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACX;IACA,MAAM,4BAA4B,EAAE,IAAI,EAAE,aAAa,EAAE,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,EAAE,EAAG,EAAE;QAClH,IAAI,cAAc,MAAM,KAAK,GAAG;YAC5B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,oBAAoB;YACrB,qBAAqB,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC;gBACzC,WAAW,aAAa,CAAC,EAAE;YAC/B,EAAE,EAAE,oBAAoB;QAC5B;QACA,IAAI,CAAC,sBAAsB,MAAM;YAC7B,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,OAAO;YACT;YACA;YACA,gBAAgB;YAChB,sBAAsB;YACtB;YACA,YAAY,CAAC,aAAa,IAAI,MAAM,GAAG;YACvC,OAAO,CAAC;QACZ;QACA,IAAI,UACA,KAAK,KAAK,CAAC,WAAW,GAAG;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,EAAE;YAChH,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,OAAO,MAAM,SAAS,IAAI;IAC9B;IACA;;;;KAIC,GACD,OAAO,4BAA4B,KAAK,EAAE;QACtC,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;QACX,MAAM,SAAS,IAAI,gBAAgB;YAAE,QAAQ;QAAM;QACnD,WAAW,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,oBAAoB,QAAS;YACvE,OAAO;QACX;IACJ;IACA,mBAAmB,OAAO,EAAE;QACxB,IAAI;QACJ,IAAI,aAAa,SAAS;YACtB,WAAW,QAAQ,OAAO;QAC9B,OACK,IAAI,MAAM,OAAO,CAAC,UAAU;YAC7B,WAAW;QACf,OACK;YACD,WAAW;gBAAC;aAAQ;QACxB;QACA,OAAO;IACX;IACA,MAAM,uBAAuB,iBAAiB,EAAE,GAAG,EAAE,UAAU,EAAE;QAC7D,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;QAC5C,MAAM,YAAY,EAAE;QACpB,KAAK,MAAM,OAAO,YAAa;YAC3B,IAAI,cAAc,cAAc,CAAC;YACjC,IAAI,IAAI,aAAa,EAAE;gBACnB,cAAc;oBAAE,GAAG,IAAI,aAAa;oBAAE,GAAG,WAAW;gBAAC;YACzD;YACA,IAAI,SAAS;YACb,IAAI,IAAI,WAAW,EAAE;gBACjB,SAAS,IAAI,WAAW;YAC5B,OACK,IAAI,KAAK;gBACV,SAAS,IAAI,EAAE;YACnB;YACA,UAAU,IAAI,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,GAAG,EAAE;gBACtD,OAAO,IAAI,KAAK;gBAChB,OAAO,IAAI,KAAK;gBAChB,SAAS,IAAI,OAAO;gBACpB,YAAY,IAAI,UAAU;gBAC1B,YAAY;gBACZ,aAAa,IAAI,WAAW;gBAC5B,gBAAgB,IAAI,cAAc;gBAClC,oBAAoB;YACxB;QACJ;QACA,OAAO;YAAC;YAAa;SAAU;IACnC;IACA,MAAM,sBAAsB,iBAAiB,EAAE,GAAG,EAAE,UAAU,EAAE;QAC5D,MAAM,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,KAAK;QAC5E,OAAO;IACX;IACA;;KAEC,GACD;;;;;;;;KAQC,GACD,OAAO,qBAAqB,UAAU,CAAC,CAAC,EAAE;QACtC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG;QAChD,MAAM,SAAS,IAAI;QACnB,IAAI,UAAU;YACV,SAAS,OAAO,CAAC,CAAC,IAAI;gBAClB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;gBAC/B,OAAO,MAAM,CAAC,OAAO;YACzB;QACJ;QACA,IAAI,MACA,OAAO,MAAM,CAAC,QAAQ;QAC1B,IAAI,cACA,OAAO,MAAM,CAAC,iBAAiB;QACnC,OAAO,MAAM,CAAC,SAAS,CAAC,UAAU,YAAY,KAAK,GAAG,CAAC,OAAO,OAAO,GAAG,EAAE,QAAQ;QAClF,IAAI,QAAQ;QACZ,WAAW,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,sBAAsB,QAAS;YACzE,OAAO;YACP;YACA,IAAI,UAAU,aAAa,SAAS,OAChC;QACR;IACJ;IACA;;;;;;;KAOC,GACD,MAAM,sBAAsB,OAAO,EAAE;QACjC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,EAAE,GAAG;QAC3D,MAAM,OAAO;YACT;YACA;YACA,IAAI,WAAW,oLAAA,CAAA,KAAO;YACtB,qBAAqB;QACzB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAAE;YAC7G,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC,OAAO,WAAW,CAAC,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,GAAK,MAAM;YACtF,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACX;IACA;;;;KAIC,GACD,MAAM,oBAAoB,OAAO,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,SAAS,YAAY,EAAE;YAC/I,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACX;IACA;;;;;;KAMC,GACD,MAAM,sBAAsB,OAAO,EAAE,OAAO,EAAE;QAC1C,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,kBAAkB,EAAE,GAAG;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,SAAS,YAAY,EAAE;YAC/I,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;gBACjB;gBACA;gBACA,qBAAqB;YACzB;YACA,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;IACnC;IACA;;;KAGC,GACD,MAAM,sBAAsB,OAAO,EAAE;QACjC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,SAAS,YAAY,EAAE;YAC/I,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,QAAQ;YAAmB;YACvD,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;IACnC;IACA;;;;KAIC,GACD,MAAM,yBAAyB,OAAO,EAAE,MAAM,EAAE;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,SAAS,WAAW,KAAK,CAAC,EAAE;YACpJ,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,IAAM,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,QAAQ;YAClF,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;IACnC;IACA;;;;;;KAMC,GACD,MAAM,0BAA0B,OAAO,EAAE,KAAK,EAAE;QAC5C,MAAM,UAAU,CAAC,mBAAmB,EAAE,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,SAAS,WAAW,IAAI,CAAC;QAC1E,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,EAAE,OAAO,EAAE;YAC9G,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,OAAO,MAAM,SAAS,IAAI;IAC9B;IACA;;;;KAIC,GACD,MAAM,6BAA6B,OAAO,EAAE,UAAU,EAAE;QACpD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,SAAS,WAAW,MAAM,EAAE,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,YAAY,eAAe,EAAE;YAC5L,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,QAAQ;YAAmB;YACvD,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;IACnC;IACA;;;KAGC,GACD,MAAM,2BAA2B,OAAO,EAAE;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,SAAS,WAAW,KAAK,CAAC,EAAE;YACpJ,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,OAAO,MAAM,SAAS,IAAI;IAC9B;IACA,MAAM,sBAAsB,KAAK,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,SAAS,OAAO,SAAS,aAAa,KAAK;IACtD;IACA,MAAM,oBAAoB,MAAM,EAAE,KAAK,EAAE;QACrC,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO;sBACpB,EAAE,SAAS,aAAa,CAAC;wBACvB,EAAE,OAAO;IAC7B;IACA,MAAM,qBAAqB,kBAAkB,EAAE;QAC3C,MAAM,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,mBAAmB,QAAQ,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE;YAC5I,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,IAAI,CAAC,IAAI,EAAE,EAAE;YACT,MAAM,SAAS,OAAO,KAAK,MAAM,KAAK,WAChC,KAAK,MAAM,GACX,KAAK,SAAS,CAAC,KAAK,MAAM;YAChC,MAAM,QAAQ,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,CAAC,EAAE,EAAE,QAAQ;YAC3E,8DAA8D;YAC9D,MAAM,UAAU,GAAG,IAAI,MAAM;YAC7B,MAAM;QACV;QACA,IAAI,KAAK,OAAO,CAAC,MAAM,KAAK,GAAG;YAC3B,OAAO;QACX;QACA,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,WAAW;IACtC;IACA,MAAM,oBAAoB,gBAAgB,EAAE,IAAI,EAAE;QAC9C,MAAM,CAAC,OAAO,YAAY,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,wBAAqB,AAAD,EAAE;QACrD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,YAAY,EAAE;YACxH,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,MAAM;YAAK;YAClC,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,GAAG,OAAO,SAAS,SAAS,OAAO,CAAC;QACnE,OAAO,MAAM,SAAS,IAAI;IAC9B;IACA,MAAM,cAAc,gBAAgB,EAAE;QAClC,MAAM,CAAC,OAAO,YAAY,WAAW,GAAG,CAAA,GAAA,uJAAA,CAAA,wBAAqB,AAAD,EAAE;QAC9D,IAAI,CAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAS;YAC5C,IAAI,eAAe,UAAU;gBACzB,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,EAAE,MAAM,CAAC,EAAE,WAAW,CAAC,EAAE,WAAW,SAAS,CAAC,GAAG,IAAI;YAC1F,OACK;gBACD,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,EAAE,MAAM,CAAC,EAAE,YAAY;YAC5D;QACJ,OACK;YACD,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;YACxC,IAAI,eAAe,UAAU;gBACzB,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,SAAS,EAAE,WAAW,CAAC,EAAE,WAAW,SAAS,CAAC,GAAG,GAAG,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACnH,OACK;gBACD,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,SAAS,EAAE,WAAW,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACrF;QACJ;IACJ;IACA,MAAM,aAAa,gBAAgB,EAAE;QACjC,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS,CAAC;QACpC,OAAO,CAAC,CAAC;IACb;IACA,MAAM,WAAW,gBAAgB,EAAE;QAC/B,OAAO,IAAI,CAAC,mBAAmB,CAAC,kBAAkB;IACtD;IACA,MAAM,aAAa,gBAAgB,EAAE;QACjC,OAAO,IAAI,CAAC,mBAAmB,CAAC,kBAAkB;IACtD;IACA,OAAO,YAAY,kBAAkB,EAAE;QACnC,WAAW,MAAM,WAAW,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC,EAAE,IAAI,mBAAmB,CAAC,MAAQ,IAAI,OAAO,EAAG;YAC5H,OAAO;QACX;IACJ;IACA,OAAO,YAAY,OAAO,EAAE;QACxB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,CAAC,cAAc,SAAS,aAAa;QAClD,OAAO,MAAM,CAAC,kBAAkB;QAChC,OAAO,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,UAAU,EAAE,QAAQ;QAC7D,IAAI,SAAS,aAAa,WAAW;YACjC,OAAO,MAAM,CAAC,aAAa,QAAQ,QAAQ,CAAC,QAAQ;QACxD;QACA,IAAI,SAAS,OAAO;YAChB,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK;QACxC;QACA,WAAW,MAAM,WAAW,IAAI,CAAC,aAAa,CAAC,UAAU,QAAQ,CAAC,MAAQ,IAAI,KAAK,EAAG;YAClF,OAAO;QACX;IACJ;IACA,MAAM,UAAU,gBAAgB,EAAE;QAC9B,MAAM,CAAC,OAAO,YAAY,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,wBAAqB,AAAD,EAAE;QACrD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,YAAY,EAAE;YACxH,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,IAAI,SAAS,MAAM,KAAK,KAAK;YACzB,OAAO;QACX;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,IAAI,OAAO,IAAI,EAAE;YACb,OAAO,OAAO,IAAI;QACtB,OACK;YACD,OAAO;QACX;IACJ;IACA,MAAM,aAAa,gBAAgB,EAAE,OAAO,EAAE;QAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY;QACxC,IAAI,SAAS,YAAY,CAAC,SAAS,aAAa,EAAE;YAC9C,MAAM,IAAI,MAAM,CAAC;;;2CAGc,CAAC;QACpC;QACA,MAAM,CAAC,OAAO,YAAY,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,wBAAqB,AAAD,EAAE;QACrD,IAAI,CAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAS;YAC5C,MAAM,MAAM,IAAI,CAAC,mBAAmB,CAAC,mBAAmB;QAC5D;QACA,MAAM,OAAO;YACT,aAAa;YACb,GAAI,SAAS,eAAe;gBAAE,aAAa,QAAQ,WAAW;YAAC,CAAC;YAChE,GAAI,SAAS,UAAU;gBAAE,QAAQ,QAAQ,MAAM;YAAC,CAAC;YACjD,GAAI,SAAS,QAAQ;gBAAE,MAAM,QAAQ,IAAI;YAAC,CAAC;YAC3C,WAAW,CAAC,CAAC,SAAS;QAC1B;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;YAClG,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,SAAS,IAAI;QACpC,OAAO;IACX;IACA,MAAM,aAAa,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE;QAClD,IAAI,CAAE,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAoB;YAC9C,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,CAAC,OAAO,YAAY,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,wBAAqB,AAAD,EAAE;QACrD,MAAM,2BAA2B,SAAS,qBAAqB,YAAY,CAAC,SAAS,mBAC/E,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,MAAM,CAAC,EAAE,YAAY,IACxD,SAAS;QACf,MAAM,UAAU;YACZ,UAAU,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;YACpC,eAAe;QACnB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,YAAY,EAAE;YAC1H,QAAQ;YACR,SAAS;gBAAE,GAAG,IAAI,CAAC,OAAO;gBAAE,gBAAgB;YAAmB;YAC/D,MAAM,KAAK,SAAS,CAAC;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,EAAE,aAAa,OAAO,WAAW,GAAG,CAAC,CAAC,EAAE,OAAO,WAAW,EAAE,GAAG,IAAI;IAC3G;IACA;;;;KAIC,GACD,MAAM,wBAAwB,SAAS,EAAE,UAAU,EAAE,EAAE;QACnD,OAAO,IAAI,CAAC,wBAAwB,CAAC,WAAW;IACpD;IACA,MAAM,yBAAyB,SAAS,EAAE,UAAU,EAAE,EAAE;QACpD,IAAI,CAAE,MAAM,IAAI,CAAC,oBAAoB,IAAK;YACtC,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,WAAW,IAAI;QACrB,KAAK,MAAM,WAAW,QAAS;YAC3B,MAAM,YAAY,QAAQ,EAAE;YAC5B,gCAAgC;YAChC,MAAM,cAAc;gBAChB,GAAI,QAAQ,QAAQ,IAAI;oBAAE,UAAU,QAAQ,QAAQ;gBAAC,CAAC;gBACtD,GAAI,QAAQ,KAAK,IAAI;oBAAE,OAAO,QAAQ,KAAK;gBAAC,CAAC;YACjD;YACA,wBAAwB;YACxB,MAAM,qBAAqB,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,aAAa,CAAC,sCAAsC,EAAE,WAAW;YACvH,MAAM,cAAc,IAAI,KAAK;gBAAC;aAAmB,EAAE;gBAC/C,MAAM;YACV;YACA,SAAS,MAAM,CAAC,WAAW;YAC3B,wBAAwB;YACxB,IAAI,QAAQ,MAAM,EAAE;gBAChB,MAAM,oBAAoB,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,QAAQ,MAAM,EAAE,CAAC,wCAAwC,EAAE,WAAW;gBAC3H,MAAM,aAAa,IAAI,KAAK;oBAAC;iBAAkB,EAAE;oBAC7C,MAAM;gBACV;gBACA,SAAS,MAAM,CAAC,GAAG,UAAU,OAAO,CAAC,EAAE;YAC3C;YACA,yBAAyB;YACzB,IAAI,QAAQ,OAAO,EAAE;gBACjB,MAAM,qBAAqB,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,QAAQ,OAAO,EAAE,CAAC,mDAAmD,EAAE,WAAW;gBACxI,MAAM,cAAc,IAAI,KAAK;oBAAC;iBAAmB,EAAE;oBAC/C,MAAM;gBACV;gBACA,SAAS,MAAM,CAAC,GAAG,UAAU,QAAQ,CAAC,EAAE;YAC5C;YACA,6BAA6B;YAC7B,IAAI,QAAQ,WAAW,EAAE;gBACrB,KAAK,MAAM,CAAC,MAAM,WAAW,IAAI,OAAO,OAAO,CAAC,QAAQ,WAAW,EAAG;oBAClE,IAAI;oBACJ,IAAI;oBACJ,IAAI,MAAM,OAAO,CAAC,aAAa;wBAC3B,CAAC,UAAU,KAAK,GAAG;oBACvB,OACK;wBACD,WAAW,WAAW,QAAQ;wBAC9B,OAAO,WAAW,IAAI;oBAC1B;oBACA,MAAM,iBAAiB,IAAI,KAAK;wBAAC;qBAAK,EAAE;wBACpC,MAAM,GAAG,SAAS,SAAS,EAAE,KAAK,UAAU,EAAE;oBAClD;oBACA,SAAS,MAAM,CAAC,GAAG,UAAU,YAAY,EAAE,MAAM,EAAE;gBACvD;YACJ;YACA,IAAI,QAAQ,sBAAsB,EAAE;gBAChC,MAAM,mCAAmC,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,QAAQ,sBAAsB,EAAE,CAAC,wDAAwD,EAAE,WAAW;gBAC1K,MAAM,4BAA4B,IAAI,KAAK;oBAAC;iBAAiC,EAAE;oBAC3E,MAAM;gBACV;gBACA,SAAS,MAAM,CAAC,GAAG,UAAU,uBAAuB,CAAC,EAAE;YAC3D;QACJ;QACA,MAAM,iBAAiB,aAAa,OAAO,CAAC,EAAE,EAAE;QAChD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,SAAS,EAAE,eAAe,SAAS,CAAC,GAAG,EAAE;YAClK,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM;QACV;QACA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACX;IACA;;;;;KAKC,GACD,MAAM,wBAAwB,SAAS,EAAE,UAAU,EAAE,EAAE;QACnD,OAAO,IAAI,CAAC,wBAAwB,CAAC,WAAW;IACpD;IACA,MAAM,yBAAyB,SAAS,EAAE,UAAU,EAAE,EAAE;QACpD,IAAI,CAAE,MAAM,IAAI,CAAC,oBAAoB,IAAK;YACtC,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,WAAW,IAAI;QACrB,KAAK,MAAM,WAAW,QAAS;YAC3B,MAAM,YAAY,CAAC,QAAQ,EAAE,IAAI,oLAAA,CAAA,KAAO,EAAE,EAAE,QAAQ;YACpD,gCAAgC;YAChC,MAAM,cAAc;gBAChB,YAAY,QAAQ,UAAU;gBAC9B,GAAI,QAAQ,QAAQ,IAAI;oBAAE,UAAU,QAAQ,QAAQ;gBAAC,CAAC;gBACtD,GAAI,QAAQ,KAAK,IAAI;oBAAE,OAAO,QAAQ,KAAK;gBAAC,CAAC;gBAC7C,GAAI,QAAQ,aAAa,IAAI;oBAAE,eAAe,QAAQ,aAAa;gBAAC,CAAC;gBACrE,GAAI,QAAQ,iBAAiB,IAAI;oBAC7B,mBAAmB,QAAQ,iBAAiB;gBAChD,CAAC;gBACD,GAAI,QAAQ,0BAA0B,IAAI;oBACtC,4BAA4B,QAAQ,0BAA0B;gBAClE,CAAC;YACL;YACA,wBAAwB;YACxB,MAAM,qBAAqB,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,aAAa,CAAC,+CAA+C,EAAE,WAAW;YAChI,MAAM,cAAc,IAAI,KAAK;gBAAC;aAAmB,EAAE;gBAC/C,MAAM;YACV;YACA,SAAS,MAAM,CAAC,WAAW;YAC3B,wBAAwB;YACxB,IAAI,QAAQ,MAAM,EAAE;gBAChB,MAAM,oBAAoB,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,QAAQ,MAAM,EAAE,CAAC,iDAAiD,EAAE,WAAW;gBACpI,MAAM,aAAa,IAAI,KAAK;oBAAC;iBAAkB,EAAE;oBAC7C,MAAM;gBACV;gBACA,SAAS,MAAM,CAAC,GAAG,UAAU,OAAO,CAAC,EAAE;YAC3C;YACA,yBAAyB;YACzB,IAAI,QAAQ,OAAO,EAAE;gBACjB,MAAM,qBAAqB,CAAA,GAAA,kLAAA,CAAA,YAA0B,AAAD,EAAE,QAAQ,OAAO,EAAE,CAAC,kDAAkD,EAAE,WAAW;gBACvI,MAAM,cAAc,IAAI,KAAK;oBAAC;iBAAmB,EAAE;oBAC/C,MAAM;gBACV;gBACA,SAAS,MAAM,CAAC,GAAG,UAAU,QAAQ,CAAC,EAAE;YAC5C;YACA,6BAA6B;YAC7B,IAAI,QAAQ,WAAW,EAAE;gBACrB,KAAK,MAAM,CAAC,MAAM,WAAW,IAAI,OAAO,OAAO,CAAC,QAAQ,WAAW,EAAG;oBAClE,IAAI;oBACJ,IAAI;oBACJ,IAAI,MAAM,OAAO,CAAC,aAAa;wBAC3B,CAAC,UAAU,KAAK,GAAG;oBACvB,OACK;wBACD,WAAW,WAAW,QAAQ;wBAC9B,OAAO,WAAW,IAAI;oBAC1B;oBACA,MAAM,iBAAiB,IAAI,KAAK;wBAAC;qBAAK,EAAE;wBACpC,MAAM,GAAG,SAAS,SAAS,EAAE,KAAK,UAAU,EAAE;oBAClD;oBACA,SAAS,MAAM,CAAC,GAAG,UAAU,YAAY,EAAE,MAAM,EAAE;gBACvD;YACJ;QACJ;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,SAAS,EAAE,UAAU,SAAS,CAAC,GAAG,EAAE;YAC7J,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM;QACV;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACX;IACA,MAAM,aAAa,gBAAgB,EAAE,OAAO,EAAE;QAC1C,IAAI,CAAE,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAoB;YAC9C,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,CAAC,OAAO,WAAW,GAAG,CAAA,GAAA,uJAAA,CAAA,wBAAqB,AAAD,EAAE;QAClD,IAAI,CAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAS;YAC5C,MAAM,MAAM,IAAI,CAAC,mBAAmB,CAAC,mBAAmB;QAC5D;QACA,MAAM,UAAU,CAAC;QACjB,IAAI,SAAS,gBAAgB,WACzB,QAAQ,WAAW,GAAG,QAAQ,WAAW;QAC7C,IAAI,SAAS,WAAW,WACpB,QAAQ,MAAM,GAAG,QAAQ,MAAM;QACnC,IAAI,SAAS,SAAS,WAClB,QAAQ,IAAI,GAAG,QAAQ,IAAI;QAC/B,IAAI,SAAS,aAAa,WACtB,QAAQ,SAAS,GAAG,QAAQ,QAAQ;QACxC,IAAI,SAAS,eAAe,WACxB,QAAQ,WAAW,GAAG,QAAQ,UAAU;QAC5C,4BAA4B;QAC5B,IAAI,OAAO,IAAI,CAAC,SAAS,MAAM,KAAK,GAAG;YACnC,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,YAAY,EAAE;YACxH,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;YACrB,SAAS;gBACL,GAAG,IAAI,CAAC,OAAO;gBACf,gBAAgB;YACpB;YACA,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,OAAO,SAAS,IAAI;IACxB;IACA,MAAM,aAAa,gBAAgB,EAAE;QACjC,IAAI,CAAE,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAoB;YAC9C,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,CAAC,OAAO,YAAY,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,wBAAqB,AAAD,EAAE;QACrD,IAAI,CAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAS;YAC5C,MAAM,MAAM,IAAI,CAAC,mBAAmB,CAAC,mBAAmB;QAC5D;QACA,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,YAAY,EAAE;YACxH,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,OAAO,MAAM,SAAS,IAAI;IAC9B;IACA,MAAM,iBAAiB,gBAAgB,EAAE,OAAO,EAAE;QAC9C,MAAM,CAAC,OAAO,YAAY,WAAW,GAAG,CAAA,GAAA,uJAAA,CAAA,wBAAqB,AAAD,EAAE;QAC9D,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,GAAA,0JAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,eAAe,wBAAwB,IAAI,EAAE;YAC7L,QAAQ;YACR,SAAS,IAAI,CAAC,OAAO;YACrB,QAAQ,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU;YAC3C,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,MAAM,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QAC/B,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;YACH;YACA,MAAM;YACN,aAAa,OAAO,WAAW;YAC/B,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;QAC7B;IACJ;IACA;;;;KAIC,GACD,MAAM,YAAY,gBAAgB,EAAE,OAAO,EAAE;QACzC,MAAM,eAAe,MAAM,IAAI,CAAC,gBAAgB,CAAC,kBAAkB;YAC/D,cAAc,SAAS;QAC3B;QACA,MAAM,SAAS,KAAK,SAAS,CAAC,aAAa,QAAQ;QACnD,OAAO;IACX;IACA,MAAM,WAAW,gBAAgB,EAAE,OAAO,EAAE;QACxC,mCAAmC;QACnC,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAmB;YAC3C,IAAI,WAAW,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,MAAQ,QAAQ,WAAW;gBACjE,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB;oBACtC,aAAa,SAAS;oBACtB,QAAQ,SAAS;oBACjB,MAAM,SAAS;oBACf,UAAU,SAAS;gBACvB;YACJ;QACJ,OACK;YACD,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB;gBACtC,aAAa,SAAS;gBACtB,QAAQ,SAAS;gBACjB,MAAM,SAAS;gBACf,UAAU,SAAS;YACvB;QACJ;QACA,IAAI,CAAC,SAAS,QAAQ;YAClB,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;QACpC;QACA,wCAAwC;QACxC,MAAM,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,kBAAkB,SAAS,QAAQ;YACnE,kBAAkB,SAAS;QAC/B;QACA,OAAO;IACX;IACA;;;;;;;;;;KAUC,GACD,MAAM,mBAAmB,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE;QAC/C,MAAM,EAAE,eAAe,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,GAAG;QACpD,MAAM,CAAC,cAAc,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY;QACnE,MAAM,eAAe,IAAI,OAAO;YAC5B,QAAQ;YACR,4DAA4D;YAC5D,gEAAgE;YAChE,wDAAwD;YACxD,QAAQ;QACZ;QACA,MAAM,KAAK,MAAM,aAAa,iBAAiB,CAAC;QAChD,MAAM,mBAAmB,eAAe,GAAG,IAAI;QAC/C,IAAI;YACA,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC;gBAAE,WAAW;YAAiB,IAAI;gBACxD,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,iBAAiB,yCAAyC,CAAC;gBAClF;YACJ;QACJ,EACA,OAAO,GAAG;QACN,mEAAmE;QACnE,qBAAqB;QACzB;QACA,gDAAgD;QAChD,MAAM,WAAW,MAAM,aAAa,kBAAkB,CAAC;QACvD,MAAM,UAAU,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB;YACvD,aAAa,GAAG,WAAW;YAC3B,UAAU,GAAG,SAAS,IAAI;YAC1B,cAAc,GAAG,wBAAwB,IAAI;YAC7C,eAAe,GAAG,yBAAyB,IAAI;QACnD;QACA,IAAI;YACA,MAAM,IAAI,CAAC,cAAc,CAAC;gBACtB,QAAQ,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;gBACpC,SAAS,SAAS,OAAO,CAAC,CAAC,IAAO,EAAE,OAAO,GAAG;wBAAC,EAAE,OAAO;qBAAC,GAAG,EAAE;gBAC9D,WAAW,QAAQ,EAAE;YACzB;QACJ,EACA,OAAO,GAAG;YACN,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,iBAAiB,EAAE,CAAC,GAC1E;YACJ,MAAM;QACV;IACJ;IACA,gBAAgB,UAAU,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,OAAO,SAAS,EAAE;QAChE,sBAAsB;QACtB,IAAI;YACA,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,aAAa,iCAAiC;YACzD,OAAO;gBAAC;gBAAQ;aAAW;QAC/B,EACA,OAAO,GAAG;QACN,2BAA2B;QAC/B;QACA,eAAe;QACf,IAAI;YACA,MAAM,YAAY,IAAI,IAAI;YAC1B,MAAM,YAAY,UAAU,QAAQ,CAC/B,KAAK,CAAC,KACN,MAAM,CAAC,CAAC,OAAS,SAAS;YAC/B,IAAI,UAAU,MAAM,IAAI,UAAU;gBAC9B,MAAM,YAAY,SAAS,CAAC,UAAU,MAAM,GAAG,SAAS;gBACxD,OAAO;oBAAC;oBAAQ;iBAAU;YAC9B,OACK;gBACD,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,KAAK,MAAM,EAAE,YAAY;YAC/D;QACJ,EACA,OAAO,OAAO;YACV,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,KAAK,eAAe,EAAE,YAAY;QACxE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;KAoBC,GACD,MAAM,2BAA2B;QAC7B,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,QAAQ,IAAI,CAAC;YACb,OAAO,QAAQ,OAAO;QAC1B;QACA,MAAM,QAAQ,GAAG,CAAC;eACX,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,GAAK;YACtD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM;SACtC;QACD,IAAI,IAAI,CAAC,yBAAyB,KAAK,WAAW;YAC9C,MAAM,CAAA,GAAA,yJAAA,CAAA,iCAA8B,AAAD,KAAK,kCAAkC;QAC9E;IACJ;AACJ;AACA,SAAS,gBAAgB,KAAK;IAC1B,OAAO,gBAAgB,SAAS,kBAAkB;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5176, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/env.js"], "sourcesContent": ["import { getLangSmithEnvironmentVariable } from \"./utils/env.js\";\nexport const isTracingEnabled = (tracingEnabled) => {\n    if (tracingEnabled !== undefined) {\n        return tracingEnabled;\n    }\n    const envVars = [\"TRACING_V2\", \"TRACING\"];\n    return !!envVars.find((envVar) => getLangSmithEnvironmentVariable(envVar) === \"true\");\n};\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAC;IAC7B,IAAI,mBAAmB,WAAW;QAC9B,OAAO;IACX;IACA,MAAM,UAAU;QAAC;QAAc;KAAU;IACzC,OAAO,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,SAAW,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,YAAY;AAClF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5195, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/singletons/constants.js"], "sourcesContent": ["export const _LC_CONTEXT_VARIABLES_KEY = Symbol.for(\"lc:context_variables\");\n"], "names": [], "mappings": ";;;AAAO,MAAM,4BAA4B,OAAO,GAAG,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5203, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/dist/run_trees.js"], "sourcesContent": ["import * as uuid from \"uuid\";\nimport { Client } from \"./client.js\";\nimport { isTracingEnabled } from \"./env.js\";\nimport { isConflictingEndpointsError, ConflictingEndpointsError, } from \"./utils/error.js\";\nimport { _LC_CONTEXT_VARIABLES_KEY } from \"./singletons/constants.js\";\nimport { getEnvironmentVariable, getRuntimeEnvironment, } from \"./utils/env.js\";\nimport { getDefaultProjectName } from \"./utils/project.js\";\nimport { getLangSmithEnvironmentVariable } from \"./utils/env.js\";\nimport { warnOnce } from \"./utils/warn.js\";\nfunction stripNonAlphanumeric(input) {\n    return input.replace(/[-:.]/g, \"\");\n}\nexport function convertToDottedOrderFormat(epoch, runId, executionOrder = 1) {\n    // Date only has millisecond precision, so we use the microseconds to break\n    // possible ties, avoiding incorrect run order\n    const paddedOrder = executionOrder.toFixed(0).slice(0, 3).padStart(3, \"0\");\n    const microsecondPrecisionDatestring = `${new Date(epoch)\n        .toISOString()\n        .slice(0, -1)}${paddedOrder}Z`;\n    return {\n        dottedOrder: stripNonAlphanumeric(microsecondPrecisionDatestring) + runId,\n        microsecondPrecisionDatestring,\n    };\n}\n/**\n * Baggage header information\n */\nclass Baggage {\n    constructor(metadata, tags, project_name, replicas) {\n        Object.defineProperty(this, \"metadata\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"tags\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"project_name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"replicas\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.metadata = metadata;\n        this.tags = tags;\n        this.project_name = project_name;\n        this.replicas = replicas;\n    }\n    static fromHeader(value) {\n        const items = value.split(\",\");\n        let metadata = {};\n        let tags = [];\n        let project_name;\n        let replicas;\n        for (const item of items) {\n            const [key, uriValue] = item.split(\"=\");\n            const value = decodeURIComponent(uriValue);\n            if (key === \"langsmith-metadata\") {\n                metadata = JSON.parse(value);\n            }\n            else if (key === \"langsmith-tags\") {\n                tags = value.split(\",\");\n            }\n            else if (key === \"langsmith-project\") {\n                project_name = value;\n            }\n            else if (key === \"langsmith-replicas\") {\n                replicas = JSON.parse(value);\n            }\n        }\n        return new Baggage(metadata, tags, project_name, replicas);\n    }\n    toHeader() {\n        const items = [];\n        if (this.metadata && Object.keys(this.metadata).length > 0) {\n            items.push(`langsmith-metadata=${encodeURIComponent(JSON.stringify(this.metadata))}`);\n        }\n        if (this.tags && this.tags.length > 0) {\n            items.push(`langsmith-tags=${encodeURIComponent(this.tags.join(\",\"))}`);\n        }\n        if (this.project_name) {\n            items.push(`langsmith-project=${encodeURIComponent(this.project_name)}`);\n        }\n        return items.join(\",\");\n    }\n}\nexport class RunTree {\n    constructor(originalConfig) {\n        Object.defineProperty(this, \"id\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"run_type\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"project_name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"parent_run\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"parent_run_id\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"child_runs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"start_time\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"end_time\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"extra\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"tags\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"error\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"serialized\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"inputs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"outputs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"reference_example_id\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"events\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"trace_id\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"dotted_order\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"tracingEnabled\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"execution_order\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"child_execution_order\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * Attachments associated with the run.\n         * Each entry is a tuple of [mime_type, bytes]\n         */\n        Object.defineProperty(this, \"attachments\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * Projects to replicate this run to with optional updates.\n         */\n        Object.defineProperty(this, \"replicas\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"_serialized_start_time\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        // If you pass in a run tree directly, return a shallow clone\n        if (isRunTree(originalConfig)) {\n            Object.assign(this, { ...originalConfig });\n            return;\n        }\n        const defaultConfig = RunTree.getDefaultConfig();\n        const { metadata, ...config } = originalConfig;\n        const client = config.client ?? RunTree.getSharedClient();\n        const dedupedMetadata = {\n            ...metadata,\n            ...config?.extra?.metadata,\n        };\n        config.extra = { ...config.extra, metadata: dedupedMetadata };\n        Object.assign(this, { ...defaultConfig, ...config, client });\n        if (!this.trace_id) {\n            if (this.parent_run) {\n                this.trace_id = this.parent_run.trace_id ?? this.id;\n            }\n            else {\n                this.trace_id = this.id;\n            }\n        }\n        this.replicas = _ensureWriteReplicas(this.replicas);\n        this.execution_order ??= 1;\n        this.child_execution_order ??= 1;\n        if (!this.dotted_order) {\n            const { dottedOrder, microsecondPrecisionDatestring } = convertToDottedOrderFormat(this.start_time, this.id, this.execution_order);\n            if (this.parent_run) {\n                this.dotted_order = this.parent_run.dotted_order + \".\" + dottedOrder;\n            }\n            else {\n                this.dotted_order = dottedOrder;\n            }\n            this._serialized_start_time = microsecondPrecisionDatestring;\n        }\n    }\n    set metadata(metadata) {\n        this.extra = {\n            ...this.extra,\n            metadata: {\n                ...this.extra?.metadata,\n                ...metadata,\n            },\n        };\n    }\n    get metadata() {\n        return this.extra?.metadata;\n    }\n    static getDefaultConfig() {\n        return {\n            id: uuid.v4(),\n            run_type: \"chain\",\n            project_name: getDefaultProjectName(),\n            child_runs: [],\n            api_url: getEnvironmentVariable(\"LANGCHAIN_ENDPOINT\") ?? \"http://localhost:1984\",\n            api_key: getEnvironmentVariable(\"LANGCHAIN_API_KEY\"),\n            caller_options: {},\n            start_time: Date.now(),\n            serialized: {},\n            inputs: {},\n            extra: {},\n        };\n    }\n    static getSharedClient() {\n        if (!RunTree.sharedClient) {\n            RunTree.sharedClient = new Client();\n        }\n        return RunTree.sharedClient;\n    }\n    createChild(config) {\n        const child_execution_order = this.child_execution_order + 1;\n        const child = new RunTree({\n            ...config,\n            parent_run: this,\n            project_name: this.project_name,\n            replicas: this.replicas,\n            client: this.client,\n            tracingEnabled: this.tracingEnabled,\n            execution_order: child_execution_order,\n            child_execution_order: child_execution_order,\n        });\n        // Copy context vars over into the new run tree.\n        if (_LC_CONTEXT_VARIABLES_KEY in this) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            child[_LC_CONTEXT_VARIABLES_KEY] =\n                this[_LC_CONTEXT_VARIABLES_KEY];\n        }\n        const LC_CHILD = Symbol.for(\"lc:child_config\");\n        const presentConfig = config.extra?.[LC_CHILD] ??\n            this.extra[LC_CHILD];\n        // tracing for LangChain is defined by the _parentRunId and runMap of the tracer\n        if (isRunnableConfigLike(presentConfig)) {\n            const newConfig = { ...presentConfig };\n            const callbacks = isCallbackManagerLike(newConfig.callbacks)\n                ? newConfig.callbacks.copy?.()\n                : undefined;\n            if (callbacks) {\n                // update the parent run id\n                Object.assign(callbacks, { _parentRunId: child.id });\n                // only populate if we're in a newer LC.JS version\n                callbacks.handlers\n                    ?.find(isLangChainTracerLike)\n                    ?.updateFromRunTree?.(child);\n                newConfig.callbacks = callbacks;\n            }\n            child.extra[LC_CHILD] = newConfig;\n        }\n        // propagate child_execution_order upwards\n        const visited = new Set();\n        let current = this;\n        while (current != null && !visited.has(current.id)) {\n            visited.add(current.id);\n            current.child_execution_order = Math.max(current.child_execution_order, child_execution_order);\n            current = current.parent_run;\n        }\n        this.child_runs.push(child);\n        return child;\n    }\n    async end(outputs, error, endTime = Date.now(), metadata) {\n        this.outputs = this.outputs ?? outputs;\n        this.error = this.error ?? error;\n        this.end_time = this.end_time ?? endTime;\n        if (metadata && Object.keys(metadata).length > 0) {\n            this.extra = this.extra\n                ? { ...this.extra, metadata: { ...this.extra.metadata, ...metadata } }\n                : { metadata };\n        }\n    }\n    _convertToCreate(run, runtimeEnv, excludeChildRuns = true) {\n        const runExtra = run.extra ?? {};\n        // Avoid overwriting the runtime environment if it's already set\n        if (runExtra?.runtime?.library === undefined) {\n            if (!runExtra.runtime) {\n                runExtra.runtime = {};\n            }\n            if (runtimeEnv) {\n                for (const [k, v] of Object.entries(runtimeEnv)) {\n                    if (!runExtra.runtime[k]) {\n                        runExtra.runtime[k] = v;\n                    }\n                }\n            }\n        }\n        let child_runs;\n        let parent_run_id;\n        if (!excludeChildRuns) {\n            child_runs = run.child_runs.map((child_run) => this._convertToCreate(child_run, runtimeEnv, excludeChildRuns));\n            parent_run_id = undefined;\n        }\n        else {\n            parent_run_id = run.parent_run?.id ?? run.parent_run_id;\n            child_runs = [];\n        }\n        return {\n            id: run.id,\n            name: run.name,\n            start_time: run._serialized_start_time ?? run.start_time,\n            end_time: run.end_time,\n            run_type: run.run_type,\n            reference_example_id: run.reference_example_id,\n            extra: runExtra,\n            serialized: run.serialized,\n            error: run.error,\n            inputs: run.inputs,\n            outputs: run.outputs,\n            session_name: run.project_name,\n            child_runs: child_runs,\n            parent_run_id: parent_run_id,\n            trace_id: run.trace_id,\n            dotted_order: run.dotted_order,\n            tags: run.tags,\n            attachments: run.attachments,\n            events: run.events,\n        };\n    }\n    _remapForProject(projectName, runtimeEnv, excludeChildRuns = true) {\n        const baseRun = this._convertToCreate(this, runtimeEnv, excludeChildRuns);\n        if (projectName === this.project_name) {\n            return baseRun;\n        }\n        // Create a deterministic UUID mapping for this project\n        const createRemappedId = (originalId) => {\n            return uuid.v5(`${originalId}:${projectName}`, uuid.v5.DNS);\n        };\n        // Remap the current run's ID\n        const newId = createRemappedId(baseRun.id);\n        const newTraceId = baseRun.trace_id\n            ? createRemappedId(baseRun.trace_id)\n            : undefined;\n        const newParentRunId = baseRun.parent_run_id\n            ? createRemappedId(baseRun.parent_run_id)\n            : undefined;\n        let newDottedOrder;\n        if (baseRun.dotted_order) {\n            const segments = _parseDottedOrder(baseRun.dotted_order);\n            const rebuilt = [];\n            // Process all segments except the last one\n            for (let i = 0; i < segments.length - 1; i++) {\n                const [timestamp, segmentId] = segments[i];\n                const remappedId = createRemappedId(segmentId);\n                rebuilt.push(timestamp.toISOString().replace(/[-:]/g, \"\").replace(\".\", \"\") +\n                    remappedId);\n            }\n            // Process the last segment with the new run ID\n            const [lastTimestamp] = segments[segments.length - 1];\n            rebuilt.push(lastTimestamp.toISOString().replace(/[-:]/g, \"\").replace(\".\", \"\") +\n                newId);\n            newDottedOrder = rebuilt.join(\".\");\n        }\n        else {\n            newDottedOrder = undefined;\n        }\n        const remappedRun = {\n            ...baseRun,\n            id: newId,\n            trace_id: newTraceId,\n            parent_run_id: newParentRunId,\n            dotted_order: newDottedOrder,\n            session_name: projectName,\n        };\n        return remappedRun;\n    }\n    async postRun(excludeChildRuns = true) {\n        try {\n            const runtimeEnv = getRuntimeEnvironment();\n            if (this.replicas && this.replicas.length > 0) {\n                for (const { projectName, apiKey, apiUrl } of this.replicas) {\n                    const runCreate = this._remapForProject(projectName ?? this.project_name, runtimeEnv, true);\n                    await this.client.createRun(runCreate, {\n                        apiKey,\n                        apiUrl,\n                    });\n                }\n            }\n            else {\n                const runCreate = this._convertToCreate(this, runtimeEnv, excludeChildRuns);\n                await this.client.createRun(runCreate);\n            }\n            if (!excludeChildRuns) {\n                warnOnce(\"Posting with excludeChildRuns=false is deprecated and will be removed in a future version.\");\n                for (const childRun of this.child_runs) {\n                    await childRun.postRun(false);\n                }\n            }\n        }\n        catch (error) {\n            console.error(`Error in postRun for run ${this.id}:`, error);\n        }\n    }\n    async patchRun() {\n        if (this.replicas && this.replicas.length > 0) {\n            for (const { projectName, apiKey, apiUrl, updates } of this.replicas) {\n                const runData = this._remapForProject(projectName ?? this.project_name);\n                await this.client.updateRun(runData.id, {\n                    inputs: runData.inputs,\n                    outputs: runData.outputs,\n                    error: runData.error,\n                    parent_run_id: runData.parent_run_id,\n                    session_name: runData.session_name,\n                    reference_example_id: runData.reference_example_id,\n                    end_time: runData.end_time,\n                    dotted_order: runData.dotted_order,\n                    trace_id: runData.trace_id,\n                    events: runData.events,\n                    tags: runData.tags,\n                    extra: runData.extra,\n                    attachments: this.attachments,\n                    ...updates,\n                }, {\n                    apiKey,\n                    apiUrl,\n                });\n            }\n        }\n        else {\n            try {\n                const runUpdate = {\n                    end_time: this.end_time,\n                    error: this.error,\n                    inputs: this.inputs,\n                    outputs: this.outputs,\n                    parent_run_id: this.parent_run?.id ?? this.parent_run_id,\n                    reference_example_id: this.reference_example_id,\n                    extra: this.extra,\n                    events: this.events,\n                    dotted_order: this.dotted_order,\n                    trace_id: this.trace_id,\n                    tags: this.tags,\n                    attachments: this.attachments,\n                    session_name: this.project_name,\n                };\n                await this.client.updateRun(this.id, runUpdate);\n            }\n            catch (error) {\n                console.error(`Error in patchRun for run ${this.id}`, error);\n            }\n        }\n    }\n    toJSON() {\n        return this._convertToCreate(this, undefined, false);\n    }\n    /**\n     * Add an event to the run tree.\n     * @param event - A single event or string to add\n     */\n    addEvent(event) {\n        if (!this.events) {\n            this.events = [];\n        }\n        if (typeof event === \"string\") {\n            this.events.push({\n                name: \"event\",\n                time: new Date().toISOString(),\n                message: event,\n            });\n        }\n        else {\n            this.events.push({\n                ...event,\n                time: event.time ?? new Date().toISOString(),\n            });\n        }\n    }\n    static fromRunnableConfig(parentConfig, props) {\n        // We only handle the callback manager case for now\n        const callbackManager = parentConfig?.callbacks;\n        let parentRun;\n        let projectName;\n        let client;\n        let tracingEnabled = isTracingEnabled();\n        if (callbackManager) {\n            const parentRunId = callbackManager?.getParentRunId?.() ?? \"\";\n            const langChainTracer = callbackManager?.handlers?.find((handler) => handler?.name == \"langchain_tracer\");\n            parentRun = langChainTracer?.getRun?.(parentRunId);\n            projectName = langChainTracer?.projectName;\n            client = langChainTracer?.client;\n            tracingEnabled = tracingEnabled || !!langChainTracer;\n        }\n        if (!parentRun) {\n            return new RunTree({\n                ...props,\n                client,\n                tracingEnabled,\n                project_name: projectName,\n            });\n        }\n        const parentRunTree = new RunTree({\n            name: parentRun.name,\n            id: parentRun.id,\n            trace_id: parentRun.trace_id,\n            dotted_order: parentRun.dotted_order,\n            client,\n            tracingEnabled,\n            project_name: projectName,\n            tags: [\n                ...new Set((parentRun?.tags ?? []).concat(parentConfig?.tags ?? [])),\n            ],\n            extra: {\n                metadata: {\n                    ...parentRun?.extra?.metadata,\n                    ...parentConfig?.metadata,\n                },\n            },\n        });\n        return parentRunTree.createChild(props);\n    }\n    static fromDottedOrder(dottedOrder) {\n        return this.fromHeaders({ \"langsmith-trace\": dottedOrder });\n    }\n    static fromHeaders(headers, inheritArgs) {\n        const rawHeaders = \"get\" in headers && typeof headers.get === \"function\"\n            ? {\n                \"langsmith-trace\": headers.get(\"langsmith-trace\"),\n                baggage: headers.get(\"baggage\"),\n            }\n            : headers;\n        const headerTrace = rawHeaders[\"langsmith-trace\"];\n        if (!headerTrace || typeof headerTrace !== \"string\")\n            return undefined;\n        const parentDottedOrder = headerTrace.trim();\n        const parsedDottedOrder = parentDottedOrder.split(\".\").map((part) => {\n            const [strTime, uuid] = part.split(\"Z\");\n            return { strTime, time: Date.parse(strTime + \"Z\"), uuid };\n        });\n        const traceId = parsedDottedOrder[0].uuid;\n        const config = {\n            ...inheritArgs,\n            name: inheritArgs?.[\"name\"] ?? \"parent\",\n            run_type: inheritArgs?.[\"run_type\"] ?? \"chain\",\n            start_time: inheritArgs?.[\"start_time\"] ?? Date.now(),\n            id: parsedDottedOrder.at(-1)?.uuid,\n            trace_id: traceId,\n            dotted_order: parentDottedOrder,\n        };\n        if (rawHeaders[\"baggage\"] && typeof rawHeaders[\"baggage\"] === \"string\") {\n            const baggage = Baggage.fromHeader(rawHeaders[\"baggage\"]);\n            config.metadata = baggage.metadata;\n            config.tags = baggage.tags;\n            config.project_name = baggage.project_name;\n            config.replicas = baggage.replicas;\n        }\n        return new RunTree(config);\n    }\n    toHeaders(headers) {\n        const result = {\n            \"langsmith-trace\": this.dotted_order,\n            baggage: new Baggage(this.extra?.metadata, this.tags, this.project_name, this.replicas).toHeader(),\n        };\n        if (headers) {\n            for (const [key, value] of Object.entries(result)) {\n                headers.set(key, value);\n            }\n        }\n        return result;\n    }\n}\nObject.defineProperty(RunTree, \"sharedClient\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: null\n});\nexport function isRunTree(x) {\n    return (x !== undefined &&\n        typeof x.createChild === \"function\" &&\n        typeof x.postRun === \"function\");\n}\nfunction isLangChainTracerLike(x) {\n    return (typeof x === \"object\" &&\n        x != null &&\n        typeof x.name === \"string\" &&\n        x.name === \"langchain_tracer\");\n}\nfunction containsLangChainTracerLike(x) {\n    return (Array.isArray(x) && x.some((callback) => isLangChainTracerLike(callback)));\n}\nfunction isCallbackManagerLike(x) {\n    return (typeof x === \"object\" &&\n        x != null &&\n        Array.isArray(x.handlers));\n}\nexport function isRunnableConfigLike(x) {\n    // Check that it's an object with a callbacks arg\n    // that has either a CallbackManagerLike object with a langchain tracer within it\n    // or an array with a LangChainTracerLike object within it\n    return (x !== undefined &&\n        typeof x.callbacks === \"object\" &&\n        // Callback manager with a langchain tracer\n        (containsLangChainTracerLike(x.callbacks?.handlers) ||\n            // Or it's an array with a LangChainTracerLike object within it\n            containsLangChainTracerLike(x.callbacks)));\n}\nfunction _parseDottedOrder(dottedOrder) {\n    const parts = dottedOrder.split(\".\");\n    return parts.map((part) => {\n        const timestampStr = part.slice(0, -36);\n        const uuidStr = part.slice(-36);\n        // Parse timestamp: \"%Y%m%dT%H%M%S%fZ\" format\n        // Example: \"20231215T143045123456Z\"\n        const year = parseInt(timestampStr.slice(0, 4));\n        const month = parseInt(timestampStr.slice(4, 6)) - 1; // JS months are 0-indexed\n        const day = parseInt(timestampStr.slice(6, 8));\n        const hour = parseInt(timestampStr.slice(9, 11));\n        const minute = parseInt(timestampStr.slice(11, 13));\n        const second = parseInt(timestampStr.slice(13, 15));\n        const microsecond = parseInt(timestampStr.slice(15, 21));\n        const timestamp = new Date(year, month, day, hour, minute, second, microsecond / 1000);\n        return [timestamp, uuidStr];\n    });\n}\nfunction _getWriteReplicasFromEnv() {\n    const envVar = getEnvironmentVariable(\"LANGSMITH_RUNS_ENDPOINTS\");\n    if (!envVar)\n        return [];\n    try {\n        const parsed = JSON.parse(envVar);\n        _checkEndpointEnvUnset(parsed);\n        return Object.entries(parsed).map(([url, key]) => ({\n            apiUrl: url.replace(/\\/$/, \"\"),\n            apiKey: key,\n        }));\n    }\n    catch (e) {\n        if (isConflictingEndpointsError(e)) {\n            throw e;\n        }\n        console.warn(\"Invalid LANGSMITH_RUNS_ENDPOINTS – must be valid JSON mapping of url->apiKey\");\n        return [];\n    }\n}\nfunction _ensureWriteReplicas(replicas) {\n    // If null -> fetch from env\n    if (replicas) {\n        return replicas.map((replica) => {\n            if (Array.isArray(replica)) {\n                return {\n                    projectName: replica[0],\n                    updates: replica[1],\n                };\n            }\n            return replica;\n        });\n    }\n    return _getWriteReplicasFromEnv();\n}\nfunction _checkEndpointEnvUnset(parsed) {\n    if (Object.keys(parsed).length > 0 &&\n        getLangSmithEnvironmentVariable(\"ENDPOINT\")) {\n        throw new ConflictingEndpointsError();\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;AACA,SAAS,qBAAqB,KAAK;IAC/B,OAAO,MAAM,OAAO,CAAC,UAAU;AACnC;AACO,SAAS,2BAA2B,KAAK,EAAE,KAAK,EAAE,iBAAiB,CAAC;IACvE,2EAA2E;IAC3E,8CAA8C;IAC9C,MAAM,cAAc,eAAe,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG;IACtE,MAAM,iCAAiC,GAAG,IAAI,KAAK,OAC9C,WAAW,GACX,KAAK,CAAC,GAAG,CAAC,KAAK,YAAY,CAAC,CAAC;IAClC,OAAO;QACH,aAAa,qBAAqB,kCAAkC;QACpE;IACJ;AACJ;AACA;;CAEC,GACD,MAAM;IACF,YAAY,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,CAAE;QAChD,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YACpC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YACpC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,OAAO,WAAW,KAAK,EAAE;QACrB,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,IAAI,WAAW,CAAC;QAChB,IAAI,OAAO,EAAE;QACb,IAAI;QACJ,IAAI;QACJ,KAAK,MAAM,QAAQ,MAAO;YACtB,MAAM,CAAC,KAAK,SAAS,GAAG,KAAK,KAAK,CAAC;YACnC,MAAM,QAAQ,mBAAmB;YACjC,IAAI,QAAQ,sBAAsB;gBAC9B,WAAW,KAAK,KAAK,CAAC;YAC1B,OACK,IAAI,QAAQ,kBAAkB;gBAC/B,OAAO,MAAM,KAAK,CAAC;YACvB,OACK,IAAI,QAAQ,qBAAqB;gBAClC,eAAe;YACnB,OACK,IAAI,QAAQ,sBAAsB;gBACnC,WAAW,KAAK,KAAK,CAAC;YAC1B;QACJ;QACA,OAAO,IAAI,QAAQ,UAAU,MAAM,cAAc;IACrD;IACA,WAAW;QACP,MAAM,QAAQ,EAAE;QAChB,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG;YACxD,MAAM,IAAI,CAAC,CAAC,mBAAmB,EAAE,mBAAmB,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI;QACxF;QACA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;YACnC,MAAM,IAAI,CAAC,CAAC,eAAe,EAAE,mBAAmB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;QAC1E;QACA,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,MAAM,IAAI,CAAC,CAAC,kBAAkB,EAAE,mBAAmB,IAAI,CAAC,YAAY,GAAG;QAC3E;QACA,OAAO,MAAM,IAAI,CAAC;IACtB;AACJ;AACO,MAAM;IACT,YAAY,cAAc,CAAE;QACxB,OAAO,cAAc,CAAC,IAAI,EAAE,MAAM;YAC9B,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YACpC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,iBAAiB;YACzC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YACpC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ;YAChC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS;YACjC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,cAAc;YACtC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;YACnC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,wBAAwB;YAChD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YACpC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,gBAAgB;YACxC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,kBAAkB;YAC1C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,mBAAmB;YAC3C,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,yBAAyB;YACjD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA;;;SAGC,GACD,OAAO,cAAc,CAAC,IAAI,EAAE,eAAe;YACvC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA;;SAEC,GACD,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YACpC,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,OAAO,cAAc,CAAC,IAAI,EAAE,0BAA0B;YAClD,YAAY;YACZ,cAAc;YACd,UAAU;YACV,OAAO,KAAK;QAChB;QACA,6DAA6D;QAC7D,IAAI,UAAU,iBAAiB;YAC3B,OAAO,MAAM,CAAC,IAAI,EAAE;gBAAE,GAAG,cAAc;YAAC;YACxC;QACJ;QACA,MAAM,gBAAgB,QAAQ,gBAAgB;QAC9C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,GAAG;QAChC,MAAM,SAAS,OAAO,MAAM,IAAI,QAAQ,eAAe;QACvD,MAAM,kBAAkB;YACpB,GAAG,QAAQ;YACX,GAAG,QAAQ,OAAO,QAAQ;QAC9B;QACA,OAAO,KAAK,GAAG;YAAE,GAAG,OAAO,KAAK;YAAE,UAAU;QAAgB;QAC5D,OAAO,MAAM,CAAC,IAAI,EAAE;YAAE,GAAG,aAAa;YAAE,GAAG,MAAM;YAAE;QAAO;QAC1D,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE;YACvD,OACK;gBACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE;YAC3B;QACJ;QACA,IAAI,CAAC,QAAQ,GAAG,qBAAqB,IAAI,CAAC,QAAQ;QAClD,IAAI,CAAC,eAAe,KAAK;QACzB,IAAI,CAAC,qBAAqB,KAAK;QAC/B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,MAAM,EAAE,WAAW,EAAE,8BAA8B,EAAE,GAAG,2BAA2B,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe;YACjI,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,MAAM;YAC7D,OACK;gBACD,IAAI,CAAC,YAAY,GAAG;YACxB;YACA,IAAI,CAAC,sBAAsB,GAAG;QAClC;IACJ;IACA,IAAI,SAAS,QAAQ,EAAE;QACnB,IAAI,CAAC,KAAK,GAAG;YACT,GAAG,IAAI,CAAC,KAAK;YACb,UAAU;gBACN,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ;gBACvB,GAAG,QAAQ;YACf;QACJ;IACJ;IACA,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,KAAK,EAAE;IACvB;IACA,OAAO,mBAAmB;QACtB,OAAO;YACH,IAAI,oLAAA,CAAA,KAAO;YACX,UAAU;YACV,cAAc,CAAA,GAAA,uJAAA,CAAA,wBAAqB,AAAD;YAClC,YAAY,EAAE;YACd,SAAS,CAAA,GAAA,mJAAA,CAAA,yBAAsB,AAAD,EAAE,yBAAyB;YACzD,SAAS,CAAA,GAAA,mJAAA,CAAA,yBAAsB,AAAD,EAAE;YAChC,gBAAgB,CAAC;YACjB,YAAY,KAAK,GAAG;YACpB,YAAY,CAAC;YACb,QAAQ,CAAC;YACT,OAAO,CAAC;QACZ;IACJ;IACA,OAAO,kBAAkB;QACrB,IAAI,CAAC,QAAQ,YAAY,EAAE;YACvB,QAAQ,YAAY,GAAG,IAAI,6IAAA,CAAA,SAAM;QACrC;QACA,OAAO,QAAQ,YAAY;IAC/B;IACA,YAAY,MAAM,EAAE;QAChB,MAAM,wBAAwB,IAAI,CAAC,qBAAqB,GAAG;QAC3D,MAAM,QAAQ,IAAI,QAAQ;YACtB,GAAG,MAAM;YACT,YAAY,IAAI;YAChB,cAAc,IAAI,CAAC,YAAY;YAC/B,UAAU,IAAI,CAAC,QAAQ;YACvB,QAAQ,IAAI,CAAC,MAAM;YACnB,gBAAgB,IAAI,CAAC,cAAc;YACnC,iBAAiB;YACjB,uBAAuB;QAC3B;QACA,gDAAgD;QAChD,IAAI,8JAAA,CAAA,4BAAyB,IAAI,IAAI,EAAE;YACnC,8DAA8D;YAC9D,KAAK,CAAC,8JAAA,CAAA,4BAAyB,CAAC,GAC5B,IAAI,CAAC,8JAAA,CAAA,4BAAyB,CAAC;QACvC;QACA,MAAM,WAAW,OAAO,GAAG,CAAC;QAC5B,MAAM,gBAAgB,OAAO,KAAK,EAAE,CAAC,SAAS,IAC1C,IAAI,CAAC,KAAK,CAAC,SAAS;QACxB,gFAAgF;QAChF,IAAI,qBAAqB,gBAAgB;YACrC,MAAM,YAAY;gBAAE,GAAG,aAAa;YAAC;YACrC,MAAM,YAAY,sBAAsB,UAAU,SAAS,IACrD,UAAU,SAAS,CAAC,IAAI,OACxB;YACN,IAAI,WAAW;gBACX,2BAA2B;gBAC3B,OAAO,MAAM,CAAC,WAAW;oBAAE,cAAc,MAAM,EAAE;gBAAC;gBAClD,kDAAkD;gBAClD,UAAU,QAAQ,EACZ,KAAK,wBACL,oBAAoB;gBAC1B,UAAU,SAAS,GAAG;YAC1B;YACA,MAAM,KAAK,CAAC,SAAS,GAAG;QAC5B;QACA,0CAA0C;QAC1C,MAAM,UAAU,IAAI;QACpB,IAAI,UAAU,IAAI;QAClB,MAAO,WAAW,QAAQ,CAAC,QAAQ,GAAG,CAAC,QAAQ,EAAE,EAAG;YAChD,QAAQ,GAAG,CAAC,QAAQ,EAAE;YACtB,QAAQ,qBAAqB,GAAG,KAAK,GAAG,CAAC,QAAQ,qBAAqB,EAAE;YACxE,UAAU,QAAQ,UAAU;QAChC;QACA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACrB,OAAO;IACX;IACA,MAAM,IAAI,OAAO,EAAE,KAAK,EAAE,UAAU,KAAK,GAAG,EAAE,EAAE,QAAQ,EAAE;QACtD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI;QAC/B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI;QAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI;QACjC,IAAI,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,GAAG;YAC9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GACjB;gBAAE,GAAG,IAAI,CAAC,KAAK;gBAAE,UAAU;oBAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ;oBAAE,GAAG,QAAQ;gBAAC;YAAE,IACnE;gBAAE;YAAS;QACrB;IACJ;IACA,iBAAiB,GAAG,EAAE,UAAU,EAAE,mBAAmB,IAAI,EAAE;QACvD,MAAM,WAAW,IAAI,KAAK,IAAI,CAAC;QAC/B,gEAAgE;QAChE,IAAI,UAAU,SAAS,YAAY,WAAW;YAC1C,IAAI,CAAC,SAAS,OAAO,EAAE;gBACnB,SAAS,OAAO,GAAG,CAAC;YACxB;YACA,IAAI,YAAY;gBACZ,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,CAAC,YAAa;oBAC7C,IAAI,CAAC,SAAS,OAAO,CAAC,EAAE,EAAE;wBACtB,SAAS,OAAO,CAAC,EAAE,GAAG;oBAC1B;gBACJ;YACJ;QACJ;QACA,IAAI;QACJ,IAAI;QACJ,IAAI,CAAC,kBAAkB;YACnB,aAAa,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,YAAc,IAAI,CAAC,gBAAgB,CAAC,WAAW,YAAY;YAC5F,gBAAgB;QACpB,OACK;YACD,gBAAgB,IAAI,UAAU,EAAE,MAAM,IAAI,aAAa;YACvD,aAAa,EAAE;QACnB;QACA,OAAO;YACH,IAAI,IAAI,EAAE;YACV,MAAM,IAAI,IAAI;YACd,YAAY,IAAI,sBAAsB,IAAI,IAAI,UAAU;YACxD,UAAU,IAAI,QAAQ;YACtB,UAAU,IAAI,QAAQ;YACtB,sBAAsB,IAAI,oBAAoB;YAC9C,OAAO;YACP,YAAY,IAAI,UAAU;YAC1B,OAAO,IAAI,KAAK;YAChB,QAAQ,IAAI,MAAM;YAClB,SAAS,IAAI,OAAO;YACpB,cAAc,IAAI,YAAY;YAC9B,YAAY;YACZ,eAAe;YACf,UAAU,IAAI,QAAQ;YACtB,cAAc,IAAI,YAAY;YAC9B,MAAM,IAAI,IAAI;YACd,aAAa,IAAI,WAAW;YAC5B,QAAQ,IAAI,MAAM;QACtB;IACJ;IACA,iBAAiB,WAAW,EAAE,UAAU,EAAE,mBAAmB,IAAI,EAAE;QAC/D,MAAM,UAAU,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,YAAY;QACxD,IAAI,gBAAgB,IAAI,CAAC,YAAY,EAAE;YACnC,OAAO;QACX;QACA,uDAAuD;QACvD,MAAM,mBAAmB,CAAC;YACtB,OAAO,oLAAA,CAAA,KAAO,CAAC,GAAG,WAAW,CAAC,EAAE,aAAa,EAAE,oLAAA,CAAA,KAAO,CAAC,GAAG;QAC9D;QACA,6BAA6B;QAC7B,MAAM,QAAQ,iBAAiB,QAAQ,EAAE;QACzC,MAAM,aAAa,QAAQ,QAAQ,GAC7B,iBAAiB,QAAQ,QAAQ,IACjC;QACN,MAAM,iBAAiB,QAAQ,aAAa,GACtC,iBAAiB,QAAQ,aAAa,IACtC;QACN,IAAI;QACJ,IAAI,QAAQ,YAAY,EAAE;YACtB,MAAM,WAAW,kBAAkB,QAAQ,YAAY;YACvD,MAAM,UAAU,EAAE;YAClB,2CAA2C;YAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,GAAG,GAAG,IAAK;gBAC1C,MAAM,CAAC,WAAW,UAAU,GAAG,QAAQ,CAAC,EAAE;gBAC1C,MAAM,aAAa,iBAAiB;gBACpC,QAAQ,IAAI,CAAC,UAAU,WAAW,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,KAAK,MACnE;YACR;YACA,+CAA+C;YAC/C,MAAM,CAAC,cAAc,GAAG,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;YACrD,QAAQ,IAAI,CAAC,cAAc,WAAW,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,KAAK,MACvE;YACJ,iBAAiB,QAAQ,IAAI,CAAC;QAClC,OACK;YACD,iBAAiB;QACrB;QACA,MAAM,cAAc;YAChB,GAAG,OAAO;YACV,IAAI;YACJ,UAAU;YACV,eAAe;YACf,cAAc;YACd,cAAc;QAClB;QACA,OAAO;IACX;IACA,MAAM,QAAQ,mBAAmB,IAAI,EAAE;QACnC,IAAI;YACA,MAAM,aAAa,CAAA,GAAA,mJAAA,CAAA,wBAAqB,AAAD;YACvC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC3C,KAAK,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAE;oBACzD,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,eAAe,IAAI,CAAC,YAAY,EAAE,YAAY;oBACtF,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW;wBACnC;wBACA;oBACJ;gBACJ;YACJ,OACK;gBACD,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,YAAY;gBAC1D,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YAChC;YACA,IAAI,CAAC,kBAAkB;gBACnB,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;gBACT,KAAK,MAAM,YAAY,IAAI,CAAC,UAAU,CAAE;oBACpC,MAAM,SAAS,OAAO,CAAC;gBAC3B;YACJ;QACJ,EACA,OAAO,OAAO;YACV,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC1D;IACJ;IACA,MAAM,WAAW;QACb,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC3C,KAAK,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAE;gBAClE,MAAM,UAAU,IAAI,CAAC,gBAAgB,CAAC,eAAe,IAAI,CAAC,YAAY;gBACtE,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE;oBACpC,QAAQ,QAAQ,MAAM;oBACtB,SAAS,QAAQ,OAAO;oBACxB,OAAO,QAAQ,KAAK;oBACpB,eAAe,QAAQ,aAAa;oBACpC,cAAc,QAAQ,YAAY;oBAClC,sBAAsB,QAAQ,oBAAoB;oBAClD,UAAU,QAAQ,QAAQ;oBAC1B,cAAc,QAAQ,YAAY;oBAClC,UAAU,QAAQ,QAAQ;oBAC1B,QAAQ,QAAQ,MAAM;oBACtB,MAAM,QAAQ,IAAI;oBAClB,OAAO,QAAQ,KAAK;oBACpB,aAAa,IAAI,CAAC,WAAW;oBAC7B,GAAG,OAAO;gBACd,GAAG;oBACC;oBACA;gBACJ;YACJ;QACJ,OACK;YACD,IAAI;gBACA,MAAM,YAAY;oBACd,UAAU,IAAI,CAAC,QAAQ;oBACvB,OAAO,IAAI,CAAC,KAAK;oBACjB,QAAQ,IAAI,CAAC,MAAM;oBACnB,SAAS,IAAI,CAAC,OAAO;oBACrB,eAAe,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,aAAa;oBACxD,sBAAsB,IAAI,CAAC,oBAAoB;oBAC/C,OAAO,IAAI,CAAC,KAAK;oBACjB,QAAQ,IAAI,CAAC,MAAM;oBACnB,cAAc,IAAI,CAAC,YAAY;oBAC/B,UAAU,IAAI,CAAC,QAAQ;oBACvB,MAAM,IAAI,CAAC,IAAI;oBACf,aAAa,IAAI,CAAC,WAAW;oBAC7B,cAAc,IAAI,CAAC,YAAY;gBACnC;gBACA,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE;YACzC,EACA,OAAO,OAAO;gBACV,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE;YAC1D;QACJ;IACJ;IACA,SAAS;QACL,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,WAAW;IAClD;IACA;;;KAGC,GACD,SAAS,KAAK,EAAE;QACZ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,IAAI,CAAC,MAAM,GAAG,EAAE;QACpB;QACA,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACb,MAAM;gBACN,MAAM,IAAI,OAAO,WAAW;gBAC5B,SAAS;YACb;QACJ,OACK;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACb,GAAG,KAAK;gBACR,MAAM,MAAM,IAAI,IAAI,IAAI,OAAO,WAAW;YAC9C;QACJ;IACJ;IACA,OAAO,mBAAmB,YAAY,EAAE,KAAK,EAAE;QAC3C,mDAAmD;QACnD,MAAM,kBAAkB,cAAc;QACtC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,iBAAiB,CAAA,GAAA,0IAAA,CAAA,mBAAgB,AAAD;QACpC,IAAI,iBAAiB;YACjB,MAAM,cAAc,iBAAiB,sBAAsB;YAC3D,MAAM,kBAAkB,iBAAiB,UAAU,KAAK,CAAC,UAAY,SAAS,QAAQ;YACtF,YAAY,iBAAiB,SAAS;YACtC,cAAc,iBAAiB;YAC/B,SAAS,iBAAiB;YAC1B,iBAAiB,kBAAkB,CAAC,CAAC;QACzC;QACA,IAAI,CAAC,WAAW;YACZ,OAAO,IAAI,QAAQ;gBACf,GAAG,KAAK;gBACR;gBACA;gBACA,cAAc;YAClB;QACJ;QACA,MAAM,gBAAgB,IAAI,QAAQ;YAC9B,MAAM,UAAU,IAAI;YACpB,IAAI,UAAU,EAAE;YAChB,UAAU,UAAU,QAAQ;YAC5B,cAAc,UAAU,YAAY;YACpC;YACA;YACA,cAAc;YACd,MAAM;mBACC,IAAI,IAAI,CAAC,WAAW,QAAQ,EAAE,EAAE,MAAM,CAAC,cAAc,QAAQ,EAAE;aACrE;YACD,OAAO;gBACH,UAAU;oBACN,GAAG,WAAW,OAAO,QAAQ;oBAC7B,GAAG,cAAc,QAAQ;gBAC7B;YACJ;QACJ;QACA,OAAO,cAAc,WAAW,CAAC;IACrC;IACA,OAAO,gBAAgB,WAAW,EAAE;QAChC,OAAO,IAAI,CAAC,WAAW,CAAC;YAAE,mBAAmB;QAAY;IAC7D;IACA,OAAO,YAAY,OAAO,EAAE,WAAW,EAAE;QACrC,MAAM,aAAa,SAAS,WAAW,OAAO,QAAQ,GAAG,KAAK,aACxD;YACE,mBAAmB,QAAQ,GAAG,CAAC;YAC/B,SAAS,QAAQ,GAAG,CAAC;QACzB,IACE;QACN,MAAM,cAAc,UAAU,CAAC,kBAAkB;QACjD,IAAI,CAAC,eAAe,OAAO,gBAAgB,UACvC,OAAO;QACX,MAAM,oBAAoB,YAAY,IAAI;QAC1C,MAAM,oBAAoB,kBAAkB,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;YACxD,MAAM,CAAC,SAAS,KAAK,GAAG,KAAK,KAAK,CAAC;YACnC,OAAO;gBAAE;gBAAS,MAAM,KAAK,KAAK,CAAC,UAAU;gBAAM;YAAK;QAC5D;QACA,MAAM,UAAU,iBAAiB,CAAC,EAAE,CAAC,IAAI;QACzC,MAAM,SAAS;YACX,GAAG,WAAW;YACd,MAAM,aAAa,CAAC,OAAO,IAAI;YAC/B,UAAU,aAAa,CAAC,WAAW,IAAI;YACvC,YAAY,aAAa,CAAC,aAAa,IAAI,KAAK,GAAG;YACnD,IAAI,kBAAkB,EAAE,CAAC,CAAC,IAAI;YAC9B,UAAU;YACV,cAAc;QAClB;QACA,IAAI,UAAU,CAAC,UAAU,IAAI,OAAO,UAAU,CAAC,UAAU,KAAK,UAAU;YACpE,MAAM,UAAU,QAAQ,UAAU,CAAC,UAAU,CAAC,UAAU;YACxD,OAAO,QAAQ,GAAG,QAAQ,QAAQ;YAClC,OAAO,IAAI,GAAG,QAAQ,IAAI;YAC1B,OAAO,YAAY,GAAG,QAAQ,YAAY;YAC1C,OAAO,QAAQ,GAAG,QAAQ,QAAQ;QACtC;QACA,OAAO,IAAI,QAAQ;IACvB;IACA,UAAU,OAAO,EAAE;QACf,MAAM,SAAS;YACX,mBAAmB,IAAI,CAAC,YAAY;YACpC,SAAS,IAAI,QAAQ,IAAI,CAAC,KAAK,EAAE,UAAU,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ;QACpG;QACA,IAAI,SAAS;YACT,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;gBAC/C,QAAQ,GAAG,CAAC,KAAK;YACrB;QACJ;QACA,OAAO;IACX;AACJ;AACA,OAAO,cAAc,CAAC,SAAS,gBAAgB;IAC3C,YAAY;IACZ,cAAc;IACd,UAAU;IACV,OAAO;AACX;AACO,SAAS,UAAU,CAAC;IACvB,OAAQ,MAAM,aACV,OAAO,EAAE,WAAW,KAAK,cACzB,OAAO,EAAE,OAAO,KAAK;AAC7B;AACA,SAAS,sBAAsB,CAAC;IAC5B,OAAQ,OAAO,MAAM,YACjB,KAAK,QACL,OAAO,EAAE,IAAI,KAAK,YAClB,EAAE,IAAI,KAAK;AACnB;AACA,SAAS,4BAA4B,CAAC;IAClC,OAAQ,MAAM,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,WAAa,sBAAsB;AAC3E;AACA,SAAS,sBAAsB,CAAC;IAC5B,OAAQ,OAAO,MAAM,YACjB,KAAK,QACL,MAAM,OAAO,CAAC,EAAE,QAAQ;AAChC;AACO,SAAS,qBAAqB,CAAC;IAClC,iDAAiD;IACjD,iFAAiF;IACjF,0DAA0D;IAC1D,OAAQ,MAAM,aACV,OAAO,EAAE,SAAS,KAAK,YACvB,2CAA2C;IAC3C,CAAC,4BAA4B,EAAE,SAAS,EAAE,aACtC,+DAA+D;IAC/D,4BAA4B,EAAE,SAAS,CAAC;AACpD;AACA,SAAS,kBAAkB,WAAW;IAClC,MAAM,QAAQ,YAAY,KAAK,CAAC;IAChC,OAAO,MAAM,GAAG,CAAC,CAAC;QACd,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC;QACpC,MAAM,UAAU,KAAK,KAAK,CAAC,CAAC;QAC5B,6CAA6C;QAC7C,oCAAoC;QACpC,MAAM,OAAO,SAAS,aAAa,KAAK,CAAC,GAAG;QAC5C,MAAM,QAAQ,SAAS,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG,0BAA0B;QAChF,MAAM,MAAM,SAAS,aAAa,KAAK,CAAC,GAAG;QAC3C,MAAM,OAAO,SAAS,aAAa,KAAK,CAAC,GAAG;QAC5C,MAAM,SAAS,SAAS,aAAa,KAAK,CAAC,IAAI;QAC/C,MAAM,SAAS,SAAS,aAAa,KAAK,CAAC,IAAI;QAC/C,MAAM,cAAc,SAAS,aAAa,KAAK,CAAC,IAAI;QACpD,MAAM,YAAY,IAAI,KAAK,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,cAAc;QACjF,OAAO;YAAC;YAAW;SAAQ;IAC/B;AACJ;AACA,SAAS;IACL,MAAM,SAAS,CAAA,GAAA,mJAAA,CAAA,yBAAsB,AAAD,EAAE;IACtC,IAAI,CAAC,QACD,OAAO,EAAE;IACb,IAAI;QACA,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,uBAAuB;QACvB,OAAO,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,GAAK,CAAC;gBAC/C,QAAQ,IAAI,OAAO,CAAC,OAAO;gBAC3B,QAAQ;YACZ,CAAC;IACL,EACA,OAAO,GAAG;QACN,IAAI,CAAA,GAAA,qJAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI;YAChC,MAAM;QACV;QACA,QAAQ,IAAI,CAAC;QACb,OAAO,EAAE;IACb;AACJ;AACA,SAAS,qBAAqB,QAAQ;IAClC,4BAA4B;IAC5B,IAAI,UAAU;QACV,OAAO,SAAS,GAAG,CAAC,CAAC;YACjB,IAAI,MAAM,OAAO,CAAC,UAAU;gBACxB,OAAO;oBACH,aAAa,OAAO,CAAC,EAAE;oBACvB,SAAS,OAAO,CAAC,EAAE;gBACvB;YACJ;YACA,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,SAAS,uBAAuB,MAAM;IAClC,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,KAC7B,CAAA,GAAA,mJAAA,CAAA,kCAA+B,AAAD,EAAE,aAAa;QAC7C,MAAM,IAAI,qJAAA,CAAA,4BAAyB;IACvC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5976, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/run_trees.js"], "sourcesContent": ["export * from './dist/run_trees.js'"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5990, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/node_modules/langsmith/index.js"], "sourcesContent": ["export * from './dist/index.js'"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}]}
module.exports = {

"[project]/.next-internal/server/app/api/analysis/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/src/lib/langchain.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PROMPTS": ()=>PROMPTS,
    "analyzeCitations": ()=>analyzeCitations,
    "answerQuestion": ()=>answerQuestion,
    "comparePapers": ()=>comparePapers,
    "createAnalysisChain": ()=>createAnalysisChain,
    "createDeepSeekModel": ()=>createDeepSeekModel,
    "explainMethodology": ()=>explainMethodology,
    "extractConcepts": ()=>extractConcepts,
    "extractKeyFindings": ()=>extractKeyFindings,
    "summarizePaper": ()=>summarizePaper
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/openai/dist/chat_models.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$prompts$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/prompts.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/prompts/prompt.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$output_parsers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/output_parsers.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$string$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/output_parsers/string.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$runnables$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/runnables.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$runnables$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@langchain/core/dist/runnables/base.js [app-route] (ecmascript)");
;
;
;
;
const createDeepSeekModel = ()=>{
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$openai$2f$dist$2f$chat_models$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ChatOpenAI"]({
        modelName: 'deepseek-chat',
        openAIApiKey: process.env.DEEPSEEK_API_KEY,
        configuration: {
            baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1'
        },
        temperature: 0.1,
        maxTokens: 4000
    });
};
const PROMPTS = {
    SUMMARIZE: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    You are an expert academic paper analyst. Please provide a comprehensive summary of the following research paper.

    Paper Content:
    {content}

    Please provide:
    1. A concise abstract summary (2-3 sentences)
    2. Main research question and objectives
    3. Key methodology used
    4. Primary findings and contributions
    5. Significance and implications

    Format your response in clear, structured sections.
  `),
    EXTRACT_KEY_FINDINGS: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Analyze the following research paper and extract the key findings and contributions.

    Paper Content:
    {content}

    Please identify and list:
    1. Main research findings (numbered list)
    2. Novel contributions to the field
    3. Experimental results and their significance
    4. Theoretical insights or frameworks introduced
    5. Practical applications or implications

    Be specific and cite relevant sections when possible.
  `),
    EXPLAIN_METHODOLOGY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Analyze the methodology section of this research paper and provide a clear explanation.

    Paper Content:
    {content}

    Please explain:
    1. Research design and approach
    2. Data collection methods
    3. Analysis techniques used
    4. Tools and technologies employed
    5. Experimental setup (if applicable)
    6. Validation methods
    7. Limitations of the methodology

    Make the explanation accessible to researchers in related fields.
  `),
    EXTRACT_CONCEPTS: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Identify and explain the key concepts, terms, and technical vocabulary from this research paper.

    Paper Content:
    {content}

    For each important concept, provide:
    1. Term/Concept name
    2. Clear definition in context
    3. Importance level (High/Medium/Low)
    4. Related terms or concepts
    5. How it's used in this specific paper

    Focus on domain-specific terminology and novel concepts introduced.
  `),
    ANSWER_QUESTION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    You are an AI assistant helping researchers understand academic papers. Answer the following question based on the paper content provided.

    Paper Content:
    {content}

    Question: {question}

    Please provide a comprehensive answer that:
    1. Directly addresses the question
    2. References specific sections of the paper when relevant
    3. Provides context and background if needed
    4. Mentions any limitations or uncertainties
    5. Suggests related questions or areas for further exploration

    If the question cannot be answered from the provided content, clearly state this and explain why.
  `),
    COMPARE_PAPERS: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Compare and analyze the following research papers. Identify similarities, differences, and relationships between them.

    Paper 1:
    {paper1}

    Paper 2:
    {paper2}

    Please provide:
    1. Common themes and research areas
    2. Methodological similarities and differences
    3. Complementary findings or conflicting results
    4. How the papers build upon or relate to each other
    5. Gaps that could be addressed by combining insights
    6. Recommendations for researchers interested in this area

    Structure your comparison clearly with specific examples from both papers.
  `),
    ANALYZE_CITATIONS: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$prompts$2f$prompt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PromptTemplate"].fromTemplate(`
    Analyze the citations and references in this research paper to understand its academic context.

    Paper Content:
    {content}

    Please identify:
    1. Key foundational works cited
    2. Recent developments referenced
    3. Main research communities or schools of thought
    4. Gaps in the literature identified by the authors
    5. How this work positions itself relative to existing research
    6. Potential future research directions suggested

    Focus on understanding the academic landscape and research trajectory.
  `)
};
const createAnalysisChain = (promptTemplate)=>{
    const model = createDeepSeekModel();
    const outputParser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$output_parsers$2f$string$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["StringOutputParser"]();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$langchain$2f$core$2f$dist$2f$runnables$2f$base$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RunnableSequence"].from([
        promptTemplate,
        model,
        outputParser
    ]);
};
const summarizePaper = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.SUMMARIZE);
    return await chain.invoke({
        content
    });
};
const extractKeyFindings = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.EXTRACT_KEY_FINDINGS);
    return await chain.invoke({
        content
    });
};
const explainMethodology = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.EXPLAIN_METHODOLOGY);
    return await chain.invoke({
        content
    });
};
const extractConcepts = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.EXTRACT_CONCEPTS);
    return await chain.invoke({
        content
    });
};
const answerQuestion = async (content, question)=>{
    const chain = createAnalysisChain(PROMPTS.ANSWER_QUESTION);
    return await chain.invoke({
        content,
        question
    });
};
const comparePapers = async (paper1, paper2)=>{
    const chain = createAnalysisChain(PROMPTS.COMPARE_PAPERS);
    return await chain.invoke({
        paper1,
        paper2
    });
};
const analyzeCitations = async (content)=>{
    const chain = createAnalysisChain(PROMPTS.ANALYZE_CITATIONS);
    return await chain.invoke({
        content
    });
};
}),
"[project]/src/lib/constants.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Application constants
__turbopack_context__.s({
    "ANALYSIS_TYPES": ()=>ANALYSIS_TYPES,
    "ANNOTATION_TYPES": ()=>ANNOTATION_TYPES,
    "API_ENDPOINTS": ()=>API_ENDPOINTS,
    "APP_CONFIG": ()=>APP_CONFIG,
    "BREAKPOINTS": ()=>BREAKPOINTS,
    "COLORS": ()=>COLORS,
    "ERROR_MESSAGES": ()=>ERROR_MESSAGES,
    "LOADING_MESSAGES": ()=>LOADING_MESSAGES,
    "MESSAGE_TYPES": ()=>MESSAGE_TYPES,
    "PAPER_FORMATS": ()=>PAPER_FORMATS,
    "ROUTES": ()=>ROUTES,
    "VIEW_MODES": ()=>VIEW_MODES
});
const APP_CONFIG = {
    name: 'DocuMancer',
    version: '1.0.0',
    description: 'AI-Powered Academic Paper Reading Assistant',
    maxFileSize: 50 * 1024 * 1024,
    allowedFileTypes: [
        '.pdf'
    ],
    supportedFormats: [
        'PDF'
    ]
};
const COLORS = {
    primary: '#1890ff',
    secondary: '#722ed1',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    text: {
        primary: '#262626',
        secondary: '#595959',
        disabled: '#bfbfbf'
    },
    background: {
        primary: '#ffffff',
        secondary: '#fafafa',
        tertiary: '#f5f5f5'
    },
    border: '#d9d9d9'
};
const BREAKPOINTS = {
    xs: 480,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1600
};
const ROUTES = {
    home: '/',
    library: '/library',
    reader: '/reader',
    comparison: '/comparison',
    analysis: '/analysis',
    settings: '/settings'
};
const API_ENDPOINTS = {
    papers: '/api/papers',
    upload: '/api/upload',
    chat: '/api/chat',
    analysis: '/api/analysis',
    search: '/api/search',
    comparison: '/api/comparison'
};
const PAPER_FORMATS = {
    ARXIV: 'arXiv',
    IEEE: 'IEEE',
    ACM: 'ACM',
    SPRINGER: 'Springer',
    ELSEVIER: 'Elsevier',
    GENERIC: 'Generic'
};
const ANALYSIS_TYPES = {
    SUMMARY: 'summary',
    KEY_FINDINGS: 'key_findings',
    METHODOLOGY: 'methodology',
    CONCEPTS: 'concepts',
    CITATIONS: 'citations',
    COMPARISON: 'comparison'
};
const MESSAGE_TYPES = {
    USER: 'user',
    ASSISTANT: 'assistant',
    SYSTEM: 'system'
};
const ANNOTATION_TYPES = {
    HIGHLIGHT: 'highlight',
    NOTE: 'note',
    BOOKMARK: 'bookmark'
};
const VIEW_MODES = {
    READER: 'reader',
    LIBRARY: 'library',
    COMPARISON: 'comparison',
    ANALYSIS: 'analysis'
};
const LOADING_MESSAGES = [
    'Processing your document...',
    'Extracting text content...',
    'Analyzing paper structure...',
    'Generating insights...',
    'Almost ready...'
];
const ERROR_MESSAGES = {
    FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',
    INVALID_FILE_TYPE: 'Only PDF files are supported',
    UPLOAD_FAILED: 'Failed to upload file. Please try again.',
    PROCESSING_FAILED: 'Failed to process the document',
    API_ERROR: 'An error occurred while communicating with the server',
    NETWORK_ERROR: 'Network error. Please check your connection.',
    GENERIC_ERROR: 'An unexpected error occurred'
};
}),
"[project]/src/app/api/analysis/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/langchain.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { paperId, content, analysisType } = body;
        if (!content) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Paper content is required'
            }, {
                status: 400
            });
        }
        if (!analysisType) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Analysis type is required'
            }, {
                status: 400
            });
        }
        let result;
        switch(analysisType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"].SUMMARY:
                result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["summarizePaper"])(content);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"].KEY_FINDINGS:
                result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractKeyFindings"])(content);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"].METHODOLOGY:
                result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["explainMethodology"])(content);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"].CONCEPTS:
                result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractConcepts"])(content);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"].CITATIONS:
                result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$langchain$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["analyzeCitations"])(content);
                break;
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: 'Invalid analysis type'
                }, {
                    status: 400
                });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                paperId,
                analysisType,
                result,
                timestamp: new Date().toISOString()
            }
        });
    } catch (error) {
        console.error('Analysis error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].API_ERROR,
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function GET(request) {
    const { searchParams } = new URL(request.url);
    const paperId = searchParams.get('paperId');
    if (!paperId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Paper ID is required'
        }, {
            status: 400
        });
    }
    // In a real implementation, you would fetch cached analysis results from a database
    // For now, return available analysis types
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: true,
        data: {
            paperId,
            availableAnalyses: Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"]),
            supportedTypes: [
                {
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"].SUMMARY,
                    name: 'Paper Summary',
                    description: 'Comprehensive overview of the paper including objectives, methodology, and findings'
                },
                {
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"].KEY_FINDINGS,
                    name: 'Key Findings',
                    description: 'Main research findings and contributions extracted from the paper'
                },
                {
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"].METHODOLOGY,
                    name: 'Methodology Analysis',
                    description: 'Detailed explanation of research methods and experimental setup'
                },
                {
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"].CONCEPTS,
                    name: 'Concept Extraction',
                    description: 'Key terms, concepts, and technical vocabulary with definitions'
                },
                {
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ANALYSIS_TYPES"].CITATIONS,
                    name: 'Citation Analysis',
                    description: 'Analysis of references and academic context of the paper'
                }
            ]
        }
    });
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__ec81e05b._.js.map
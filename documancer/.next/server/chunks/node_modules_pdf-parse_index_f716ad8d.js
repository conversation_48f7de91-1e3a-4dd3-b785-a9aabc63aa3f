module.exports = {

"[project]/node_modules/pdf-parse/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const Fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
const Pdf = __turbopack_context__.r("[project]/node_modules/pdf-parse/lib/pdf-parse.js [app-route] (ecmascript)");
module.exports = Pdf;
let isDebugMode = !module.parent;
//process.env.AUTO_KENT_DEBUG
//for testing purpose
if (isDebugMode) {
    let PDF_FILE = './test/data/05-versions-space.pdf';
    let dataBuffer = Fs.readFileSync(PDF_FILE);
    Pdf(dataBuffer).then(function(data) {
        Fs.writeFileSync(`${PDF_FILE}.txt`, data.text, {
            encoding: 'utf8',
            flag: 'w'
        });
        debugger;
    }).catch(function(err) {
        debugger;
    });
}
}}),

};

//# sourceMappingURL=node_modules_pdf-parse_index_f716ad8d.js.map
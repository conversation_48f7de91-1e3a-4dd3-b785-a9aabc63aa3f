{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/langchain.ts"], "sourcesContent": ["import { ChatOpenAI } from '@langchain/openai';\nimport { PromptTemplate } from '@langchain/core/prompts';\nimport { StringOutputParser } from '@langchain/core/output_parsers';\nimport { RunnableSequence } from '@langchain/core/runnables';\n\n// Initialize DeepSeek model (using OpenAI-compatible API)\nexport const createDeepSeekModel = () => {\n  return new ChatOpenAI({\n    modelName: 'deepseek-chat',\n    openAIApiKey: process.env.DEEPSEEK_API_KEY,\n    configuration: {\n      baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1',\n    },\n    temperature: 0.1,\n    maxTokens: 4000,\n  });\n};\n\n// Prompt templates for different analysis types\nexport const PROMPTS = {\n  SUMMARIZE: PromptTemplate.fromTemplate(`\n    You are an expert academic paper analyst. Please provide a comprehensive summary of the following research paper.\n\n    Paper Content:\n    {content}\n\n    Please provide:\n    1. A concise abstract summary (2-3 sentences)\n    2. Main research question and objectives\n    3. Key methodology used\n    4. Primary findings and contributions\n    5. Significance and implications\n\n    Format your response in clear, structured sections.\n  `),\n\n  EXTRACT_KEY_FINDINGS: PromptTemplate.fromTemplate(`\n    Analyze the following research paper and extract the key findings and contributions.\n\n    Paper Content:\n    {content}\n\n    Please identify and list:\n    1. Main research findings (numbered list)\n    2. Novel contributions to the field\n    3. Experimental results and their significance\n    4. Theoretical insights or frameworks introduced\n    5. Practical applications or implications\n\n    Be specific and cite relevant sections when possible.\n  `),\n\n  EXPLAIN_METHODOLOGY: PromptTemplate.fromTemplate(`\n    Analyze the methodology section of this research paper and provide a clear explanation.\n\n    Paper Content:\n    {content}\n\n    Please explain:\n    1. Research design and approach\n    2. Data collection methods\n    3. Analysis techniques used\n    4. Tools and technologies employed\n    5. Experimental setup (if applicable)\n    6. Validation methods\n    7. Limitations of the methodology\n\n    Make the explanation accessible to researchers in related fields.\n  `),\n\n  EXTRACT_CONCEPTS: PromptTemplate.fromTemplate(`\n    Identify and explain the key concepts, terms, and technical vocabulary from this research paper.\n\n    Paper Content:\n    {content}\n\n    For each important concept, provide:\n    1. Term/Concept name\n    2. Clear definition in context\n    3. Importance level (High/Medium/Low)\n    4. Related terms or concepts\n    5. How it's used in this specific paper\n\n    Focus on domain-specific terminology and novel concepts introduced.\n  `),\n\n  ANSWER_QUESTION: PromptTemplate.fromTemplate(`\n    You are an AI assistant helping researchers understand academic papers. Answer the following question based on the paper content provided.\n\n    Paper Content:\n    {content}\n\n    Question: {question}\n\n    Please provide a comprehensive answer that:\n    1. Directly addresses the question\n    2. References specific sections of the paper when relevant\n    3. Provides context and background if needed\n    4. Mentions any limitations or uncertainties\n    5. Suggests related questions or areas for further exploration\n\n    If the question cannot be answered from the provided content, clearly state this and explain why.\n  `),\n\n  COMPARE_PAPERS: PromptTemplate.fromTemplate(`\n    Compare and analyze the following research papers. Identify similarities, differences, and relationships between them.\n\n    Paper 1:\n    {paper1}\n\n    Paper 2:\n    {paper2}\n\n    Please provide:\n    1. Common themes and research areas\n    2. Methodological similarities and differences\n    3. Complementary findings or conflicting results\n    4. How the papers build upon or relate to each other\n    5. Gaps that could be addressed by combining insights\n    6. Recommendations for researchers interested in this area\n\n    Structure your comparison clearly with specific examples from both papers.\n  `),\n\n  ANALYZE_CITATIONS: PromptTemplate.fromTemplate(`\n    Analyze the citations and references in this research paper to understand its academic context.\n\n    Paper Content:\n    {content}\n\n    Please identify:\n    1. Key foundational works cited\n    2. Recent developments referenced\n    3. Main research communities or schools of thought\n    4. Gaps in the literature identified by the authors\n    5. How this work positions itself relative to existing research\n    6. Potential future research directions suggested\n\n    Focus on understanding the academic landscape and research trajectory.\n  `),\n};\n\n// Create analysis chains\nexport const createAnalysisChain = (promptTemplate: PromptTemplate) => {\n  const model = createDeepSeekModel();\n  const outputParser = new StringOutputParser();\n\n  return RunnableSequence.from([\n    promptTemplate,\n    model,\n    outputParser,\n  ]);\n};\n\n// Specific analysis functions\nexport const summarizePaper = async (content: string) => {\n  const chain = createAnalysisChain(PROMPTS.SUMMARIZE);\n  return await chain.invoke({ content });\n};\n\nexport const extractKeyFindings = async (content: string) => {\n  const chain = createAnalysisChain(PROMPTS.EXTRACT_KEY_FINDINGS);\n  return await chain.invoke({ content });\n};\n\nexport const explainMethodology = async (content: string) => {\n  const chain = createAnalysisChain(PROMPTS.EXPLAIN_METHODOLOGY);\n  return await chain.invoke({ content });\n};\n\nexport const extractConcepts = async (content: string) => {\n  const chain = createAnalysisChain(PROMPTS.EXTRACT_CONCEPTS);\n  return await chain.invoke({ content });\n};\n\nexport const answerQuestion = async (content: string, question: string) => {\n  const chain = createAnalysisChain(PROMPTS.ANSWER_QUESTION);\n  return await chain.invoke({ content, question });\n};\n\nexport const comparePapers = async (paper1: string, paper2: string) => {\n  const chain = createAnalysisChain(PROMPTS.COMPARE_PAPERS);\n  return await chain.invoke({ paper1, paper2 });\n};\n\nexport const analyzeCitations = async (content: string) => {\n  const chain = createAnalysisChain(PROMPTS.ANALYZE_CITATIONS);\n  return await chain.invoke({ content });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAGO,MAAM,sBAAsB;IACjC,OAAO,IAAI,8JAAA,CAAA,aAAU,CAAC;QACpB,WAAW;QACX,cAAc,QAAQ,GAAG,CAAC,gBAAgB;QAC1C,eAAe;YACb,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAAI;QAC5C;QACA,aAAa;QACb,WAAW;IACb;AACF;AAGO,MAAM,UAAU;IACrB,WAAW,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;EAcxC,CAAC;IAED,sBAAsB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;EAcnD,CAAC;IAED,qBAAqB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;;;EAgBlD,CAAC;IAED,kBAAkB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;EAc/C,CAAC;IAED,iBAAiB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;;;EAgB9C,CAAC;IAED,gBAAgB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;;;;;EAkB7C,CAAC;IAED,mBAAmB,kKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,CAAC;;;;;;;;;;;;;;;EAehD,CAAC;AACH;AAGO,MAAM,sBAAsB,CAAC;IAClC,MAAM,QAAQ;IACd,MAAM,eAAe,IAAI,yKAAA,CAAA,qBAAkB;IAE3C,OAAO,kKAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC;QAC3B;QACA;QACA;KACD;AACH;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,QAAQ,oBAAoB,QAAQ,SAAS;IACnD,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;IAAQ;AACtC;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,QAAQ,oBAAoB,QAAQ,oBAAoB;IAC9D,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;IAAQ;AACtC;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,QAAQ,oBAAoB,QAAQ,mBAAmB;IAC7D,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;IAAQ;AACtC;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,QAAQ,oBAAoB,QAAQ,gBAAgB;IAC1D,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;IAAQ;AACtC;AAEO,MAAM,iBAAiB,OAAO,SAAiB;IACpD,MAAM,QAAQ,oBAAoB,QAAQ,eAAe;IACzD,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;QAAS;IAAS;AAChD;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,MAAM,QAAQ,oBAAoB,QAAQ,cAAc;IACxD,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;QAAQ;IAAO;AAC7C;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,QAAQ,oBAAoB,QAAQ,iBAAiB;IAC3D,OAAO,MAAM,MAAM,MAAM,CAAC;QAAE;IAAQ;AACtC", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/constants.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_CONFIG = {\n  name: 'DocuMancer',\n  version: '1.0.0',\n  description: 'AI-Powered Academic Paper Reading Assistant',\n  maxFileSize: 50 * 1024 * 1024, // 50MB\n  allowedFileTypes: ['.pdf'],\n  supportedFormats: ['PDF'],\n} as const;\n\nexport const COLORS = {\n  primary: '#1890ff',\n  secondary: '#722ed1',\n  success: '#52c41a',\n  warning: '#faad14',\n  error: '#ff4d4f',\n  text: {\n    primary: '#262626',\n    secondary: '#595959',\n    disabled: '#bfbfbf',\n  },\n  background: {\n    primary: '#ffffff',\n    secondary: '#fafafa',\n    tertiary: '#f5f5f5',\n  },\n  border: '#d9d9d9',\n} as const;\n\nexport const BREAKPOINTS = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600,\n} as const;\n\nexport const ROUTES = {\n  home: '/',\n  library: '/library',\n  reader: '/reader',\n  comparison: '/comparison',\n  analysis: '/analysis',\n  settings: '/settings',\n} as const;\n\nexport const API_ENDPOINTS = {\n  papers: '/api/papers',\n  upload: '/api/upload',\n  chat: '/api/chat',\n  analysis: '/api/analysis',\n  search: '/api/search',\n  comparison: '/api/comparison',\n} as const;\n\nexport const PAPER_FORMATS = {\n  ARXIV: 'arXiv',\n  IEEE: 'IEEE',\n  ACM: 'ACM',\n  SPRINGER: 'Springer',\n  ELSEVIER: 'Elsevier',\n  GENERIC: 'Generic',\n} as const;\n\nexport const ANALYSIS_TYPES = {\n  SUMMARY: 'summary',\n  KEY_FINDINGS: 'key_findings',\n  METHODOLOGY: 'methodology',\n  CONCEPTS: 'concepts',\n  CITATIONS: 'citations',\n  COMPARISON: 'comparison',\n} as const;\n\nexport const MESSAGE_TYPES = {\n  USER: 'user',\n  ASSISTANT: 'assistant',\n  SYSTEM: 'system',\n} as const;\n\nexport const ANNOTATION_TYPES = {\n  HIGHLIGHT: 'highlight',\n  NOTE: 'note',\n  BOOKMARK: 'bookmark',\n} as const;\n\nexport const VIEW_MODES = {\n  READER: 'reader',\n  LIBRARY: 'library',\n  COMPARISON: 'comparison',\n  ANALYSIS: 'analysis',\n} as const;\n\nexport const LOADING_MESSAGES = [\n  'Processing your document...',\n  'Extracting text content...',\n  'Analyzing paper structure...',\n  'Generating insights...',\n  'Almost ready...',\n] as const;\n\nexport const ERROR_MESSAGES = {\n  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',\n  INVALID_FILE_TYPE: 'Only PDF files are supported',\n  UPLOAD_FAILED: 'Failed to upload file. Please try again.',\n  PROCESSING_FAILED: 'Failed to process the document',\n  API_ERROR: 'An error occurred while communicating with the server',\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  GENERIC_ERROR: 'An unexpected error occurred',\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;;;;AAEjB,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,aAAa;IACb,aAAa,KAAK,OAAO;IACzB,kBAAkB;QAAC;KAAO;IAC1B,kBAAkB;QAAC;KAAM;AAC3B;AAEO,MAAM,SAAS;IACpB,SAAS;IACT,WAAW;IACX,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;QACJ,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,YAAY;QACV,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,QAAQ;AACV;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;AACP;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,SAAS;IACT,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,UAAU;AACZ;AAEO,MAAM,gBAAgB;IAC3B,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,UAAU;IACV,QAAQ;IACR,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,OAAO;IACP,MAAM;IACN,KAAK;IACL,UAAU;IACV,UAAU;IACV,SAAS;AACX;AAEO,MAAM,iBAAiB;IAC5B,SAAS;IACT,cAAc;IACd,aAAa;IACb,UAAU;IACV,WAAW;IACX,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,MAAM;IACN,WAAW;IACX,QAAQ;AACV;AAEO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,UAAU;AACZ;AAEO,MAAM,aAAa;IACxB,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,UAAU;AACZ;AAEO,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,mBAAmB;IACnB,eAAe;IACf,mBAAmB;IACnB,WAAW;IACX,eAAe;IACf,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { answerQuestion } from '@/lib/langchain';\nimport { ChatMessage } from '@/lib/types';\nimport { ERROR_MESSAGES } from '@/lib/constants';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { message, paperId, paperContent, context } = body;\n\n    if (!message) {\n      return NextResponse.json(\n        { success: false, error: 'Message is required' },\n        { status: 400 }\n      );\n    }\n\n    if (!paperContent) {\n      return NextResponse.json(\n        { success: false, error: 'Paper content is required for analysis' },\n        { status: 400 }\n      );\n    }\n\n    // Generate response using LangChain\n    const response = await answerQuestion(paperContent, message);\n\n    // Create response message\n    const responseMessage: ChatMessage = {\n      id: `msg_${Date.now()}_assistant`,\n      role: 'assistant',\n      content: response,\n      timestamp: new Date(),\n      paperId,\n      context,\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        message: responseMessage,\n      },\n    });\n\n  } catch (error) {\n    console.error('Chat error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: ERROR_MESSAGES.API_ERROR,\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// Handle streaming responses for real-time chat\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const message = searchParams.get('message');\n  const paperContent = searchParams.get('content');\n\n  if (!message || !paperContent) {\n    return NextResponse.json(\n      { success: false, error: 'Message and content are required' },\n      { status: 400 }\n    );\n  }\n\n  try {\n    // Create a streaming response\n    const encoder = new TextEncoder();\n    const stream = new ReadableStream({\n      async start(controller) {\n        try {\n          const response = await answerQuestion(paperContent, message);\n          \n          // Simulate streaming by sending chunks\n          const chunks = response.split(' ');\n          for (let i = 0; i < chunks.length; i++) {\n            const chunk = chunks[i] + (i < chunks.length - 1 ? ' ' : '');\n            controller.enqueue(encoder.encode(`data: ${JSON.stringify({ chunk })}\\n\\n`));\n            \n            // Add small delay to simulate streaming\n            await new Promise(resolve => setTimeout(resolve, 50));\n          }\n          \n          controller.enqueue(encoder.encode('data: [DONE]\\n\\n'));\n          controller.close();\n        } catch (error) {\n          controller.error(error);\n        }\n      },\n    });\n\n    return new Response(stream, {\n      headers: {\n        'Content-Type': 'text/event-stream',\n        'Cache-Control': 'no-cache',\n        'Connection': 'keep-alive',\n      },\n    });\n\n  } catch (error) {\n    console.error('Streaming chat error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: ERROR_MESSAGES.API_ERROR,\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG;QAEpD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAsB,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyC,GAClE;gBAAE,QAAQ;YAAI;QAElB;QAEA,oCAAoC;QACpC,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc;QAEpD,0BAA0B;QAC1B,MAAM,kBAA+B;YACnC,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,UAAU,CAAC;YACjC,MAAM;YACN,SAAS;YACT,WAAW,IAAI;YACf;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,yHAAA,CAAA,iBAAc,CAAC,SAAS;YAC/B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;IACjC,MAAM,eAAe,aAAa,GAAG,CAAC;IAEtC,IAAI,CAAC,WAAW,CAAC,cAAc;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAmC,GAC5D;YAAE,QAAQ;QAAI;IAElB;IAEA,IAAI;QACF,8BAA8B;QAC9B,MAAM,UAAU,IAAI;QACpB,MAAM,SAAS,IAAI,eAAe;YAChC,MAAM,OAAM,UAAU;gBACpB,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc;oBAEpD,uCAAuC;oBACvC,MAAM,SAAS,SAAS,KAAK,CAAC;oBAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;wBACtC,MAAM,QAAQ,MAAM,CAAC,EAAE,GAAG,CAAC,IAAI,OAAO,MAAM,GAAG,IAAI,MAAM,EAAE;wBAC3D,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;4BAAE;wBAAM,GAAG,IAAI,CAAC;wBAE1E,wCAAwC;wBACxC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACnD;oBAEA,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;oBAClC,WAAW,KAAK;gBAClB,EAAE,OAAO,OAAO;oBACd,WAAW,KAAK,CAAC;gBACnB;YACF;QACF;QAEA,OAAO,IAAI,SAAS,QAAQ;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;gBACjB,cAAc;YAChB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,yHAAA,CAAA,iBAAc,CAAC,SAAS;YAC/B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
module.exports = {

"[project]/node_modules/node-ensure/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
/**
 *  Just run the callback through setImmediate, so that it appears
 *  asynchronous, even when unnecessary.
 */ function ensure(modules, callback) {
    setImmediate(callback);
}
module.exports = ensure;
}}),

};

//# sourceMappingURL=node_modules_node-ensure_index_7f950531.js.map
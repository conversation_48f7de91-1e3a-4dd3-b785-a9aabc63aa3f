{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/lib/constants.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_CONFIG = {\n  name: 'DocuMancer',\n  version: '1.0.0',\n  description: 'AI-Powered Academic Paper Reading Assistant',\n  maxFileSize: 50 * 1024 * 1024, // 50MB\n  allowedFileTypes: ['.pdf'],\n  supportedFormats: ['PDF'],\n} as const;\n\nexport const COLORS = {\n  primary: '#1890ff',\n  secondary: '#722ed1',\n  success: '#52c41a',\n  warning: '#faad14',\n  error: '#ff4d4f',\n  text: {\n    primary: '#262626',\n    secondary: '#595959',\n    disabled: '#bfbfbf',\n  },\n  background: {\n    primary: '#ffffff',\n    secondary: '#fafafa',\n    tertiary: '#f5f5f5',\n  },\n  border: '#d9d9d9',\n} as const;\n\nexport const BREAKPOINTS = {\n  xs: 480,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1600,\n} as const;\n\nexport const ROUTES = {\n  home: '/',\n  library: '/library',\n  reader: '/reader',\n  comparison: '/comparison',\n  analysis: '/analysis',\n  settings: '/settings',\n} as const;\n\nexport const API_ENDPOINTS = {\n  papers: '/api/papers',\n  upload: '/api/upload',\n  chat: '/api/chat',\n  analysis: '/api/analysis',\n  search: '/api/search',\n  comparison: '/api/comparison',\n} as const;\n\nexport const PAPER_FORMATS = {\n  ARXIV: 'arXiv',\n  IEEE: 'IEEE',\n  ACM: 'ACM',\n  SPRINGER: 'Springer',\n  ELSEVIER: 'Elsevier',\n  GENERIC: 'Generic',\n} as const;\n\nexport const ANALYSIS_TYPES = {\n  SUMMARY: 'summary',\n  KEY_FINDINGS: 'key_findings',\n  METHODOLOGY: 'methodology',\n  CONCEPTS: 'concepts',\n  CITATIONS: 'citations',\n  COMPARISON: 'comparison',\n} as const;\n\nexport const MESSAGE_TYPES = {\n  USER: 'user',\n  ASSISTANT: 'assistant',\n  SYSTEM: 'system',\n} as const;\n\nexport const ANNOTATION_TYPES = {\n  HIGHLIGHT: 'highlight',\n  NOTE: 'note',\n  BOOKMARK: 'bookmark',\n} as const;\n\nexport const VIEW_MODES = {\n  READER: 'reader',\n  LIBRARY: 'library',\n  COMPARISON: 'comparison',\n  ANALYSIS: 'analysis',\n} as const;\n\nexport const LOADING_MESSAGES = [\n  'Processing your document...',\n  'Extracting text content...',\n  'Analyzing paper structure...',\n  'Generating insights...',\n  'Almost ready...',\n] as const;\n\nexport const ERROR_MESSAGES = {\n  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',\n  INVALID_FILE_TYPE: 'Only PDF files are supported',\n  UPLOAD_FAILED: 'Failed to upload file. Please try again.',\n  PROCESSING_FAILED: 'Failed to process the document',\n  API_ERROR: 'An error occurred while communicating with the server',\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  GENERIC_ERROR: 'An unexpected error occurred',\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;;;;AAEjB,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,aAAa;IACb,aAAa,KAAK,OAAO;IACzB,kBAAkB;QAAC;KAAO;IAC1B,kBAAkB;QAAC;KAAM;AAC3B;AAEO,MAAM,SAAS;IACpB,SAAS;IACT,WAAW;IACX,SAAS;IACT,SAAS;IACT,OAAO;IACP,MAAM;QACJ,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,YAAY;QACV,SAAS;QACT,WAAW;QACX,UAAU;IACZ;IACA,QAAQ;AACV;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;AACP;AAEO,MAAM,SAAS;IACpB,MAAM;IACN,SAAS;IACT,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,UAAU;AACZ;AAEO,MAAM,gBAAgB;IAC3B,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,UAAU;IACV,QAAQ;IACR,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,OAAO;IACP,MAAM;IACN,KAAK;IACL,UAAU;IACV,UAAU;IACV,SAAS;AACX;AAEO,MAAM,iBAAiB;IAC5B,SAAS;IACT,cAAc;IACd,aAAa;IACb,UAAU;IACV,WAAW;IACX,YAAY;AACd;AAEO,MAAM,gBAAgB;IAC3B,MAAM;IACN,WAAW;IACX,QAAQ;AACV;AAEO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,UAAU;AACZ;AAEO,MAAM,aAAa;IACxB,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,UAAU;AACZ;AAEO,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,mBAAmB;IACnB,eAAe;IACf,mBAAmB;IACnB,WAAW;IACX,eAAe;IACf,eAAe;AACjB", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/project/DocuMancer/new/documancer/src/app/api/papers/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { readdir, readFile, stat } from 'fs/promises';\nimport { join } from 'path';\nimport { Paper } from '@/lib/types';\nimport { ERROR_MESSAGES } from '@/lib/constants';\n\n// In a real application, you would use a database\n// For this demo, we'll simulate with file system operations\nconst PAPERS_STORAGE_PATH = join(process.cwd(), 'data', 'papers.json');\n\nasync function loadPapers(): Promise<Paper[]> {\n  try {\n    const data = await readFile(PAPERS_STORAGE_PATH, 'utf-8');\n    return JSON.parse(data);\n  } catch {\n    return [];\n  }\n}\n\nasync function savePapers(papers: Paper[]): Promise<void> {\n  try {\n    const dir = join(process.cwd(), 'data');\n    await readdir(dir).catch(() => {\n      // Directory doesn't exist, create it\n      require('fs').mkdirSync(dir, { recursive: true });\n    });\n    \n    await require('fs').promises.writeFile(\n      PAPERS_STORAGE_PATH, \n      JSON.stringify(papers, null, 2)\n    );\n  } catch (error) {\n    console.error('Error saving papers:', error);\n    throw error;\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const paperId = searchParams.get('id');\n    const search = searchParams.get('search');\n    const limit = parseInt(searchParams.get('limit') || '50');\n    const offset = parseInt(searchParams.get('offset') || '0');\n\n    const papers = await loadPapers();\n\n    if (paperId) {\n      // Get specific paper\n      const paper = papers.find(p => p.id === paperId);\n      if (!paper) {\n        return NextResponse.json(\n          { success: false, error: 'Paper not found' },\n          { status: 404 }\n        );\n      }\n      \n      // Update last accessed time\n      paper.lastAccessedAt = new Date();\n      await savePapers(papers);\n      \n      return NextResponse.json({\n        success: true,\n        data: paper,\n      });\n    }\n\n    // Filter papers based on search query\n    let filteredPapers = papers;\n    if (search) {\n      const searchLower = search.toLowerCase();\n      filteredPapers = papers.filter(paper => \n        paper.title.toLowerCase().includes(searchLower) ||\n        paper.authors.some(author => author.toLowerCase().includes(searchLower)) ||\n        paper.abstract.toLowerCase().includes(searchLower) ||\n        paper.tags.some(tag => tag.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Sort by last accessed (most recent first)\n    filteredPapers.sort((a, b) => \n      new Date(b.lastAccessedAt).getTime() - new Date(a.lastAccessedAt).getTime()\n    );\n\n    // Apply pagination\n    const paginatedPapers = filteredPapers.slice(offset, offset + limit);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        papers: paginatedPapers,\n        total: filteredPapers.length,\n        offset,\n        limit,\n        hasMore: offset + limit < filteredPapers.length,\n      },\n    });\n\n  } catch (error) {\n    console.error('Error fetching papers:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: ERROR_MESSAGES.API_ERROR,\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const paper: Paper = body;\n\n    if (!paper.id || !paper.title) {\n      return NextResponse.json(\n        { success: false, error: 'Paper ID and title are required' },\n        { status: 400 }\n      );\n    }\n\n    const papers = await loadPapers();\n    \n    // Check if paper already exists\n    const existingIndex = papers.findIndex(p => p.id === paper.id);\n    if (existingIndex >= 0) {\n      // Update existing paper\n      papers[existingIndex] = { ...papers[existingIndex], ...paper };\n    } else {\n      // Add new paper\n      papers.push(paper);\n    }\n\n    await savePapers(papers);\n\n    return NextResponse.json({\n      success: true,\n      data: paper,\n      message: existingIndex >= 0 ? 'Paper updated successfully' : 'Paper added successfully',\n    });\n\n  } catch (error) {\n    console.error('Error saving paper:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: ERROR_MESSAGES.API_ERROR,\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const paperId = searchParams.get('id');\n\n    if (!paperId) {\n      return NextResponse.json(\n        { success: false, error: 'Paper ID is required' },\n        { status: 400 }\n      );\n    }\n\n    const papers = await loadPapers();\n    const paperIndex = papers.findIndex(p => p.id === paperId);\n\n    if (paperIndex === -1) {\n      return NextResponse.json(\n        { success: false, error: 'Paper not found' },\n        { status: 404 }\n      );\n    }\n\n    // Remove paper from array\n    const deletedPaper = papers.splice(paperIndex, 1)[0];\n    await savePapers(papers);\n\n    // In a real implementation, you might also want to delete the associated file\n    // and clean up any related data\n\n    return NextResponse.json({\n      success: true,\n      data: deletedPaper,\n      message: 'Paper deleted successfully',\n    });\n\n  } catch (error) {\n    console.error('Error deleting paper:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: ERROR_MESSAGES.API_ERROR,\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;AAEA,kDAAkD;AAClD,4DAA4D;AAC5D,MAAM,sBAAsB,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI,QAAQ;AAExD,eAAe;IACb,IAAI;QACF,MAAM,OAAO,MAAM,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE,qBAAqB;QACjD,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAEA,eAAe,WAAW,MAAe;IACvC,IAAI;QACF,MAAM,MAAM,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI;QAChC,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAO,AAAD,EAAE,KAAK,KAAK,CAAC;YACvB,qCAAqC;YACrC,+DAAc,SAAS,CAAC,KAAK;gBAAE,WAAW;YAAK;QACjD;QAEA,MAAM,+DAAc,QAAQ,CAAC,SAAS,CACpC,qBACA,KAAK,SAAS,CAAC,QAAQ,MAAM;IAEjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,SAAS,aAAa,GAAG,CAAC,aAAa;QAEtD,MAAM,SAAS,MAAM;QAErB,IAAI,SAAS;YACX,qBAAqB;YACrB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACxC,IAAI,CAAC,OAAO;gBACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAkB,GAC3C;oBAAE,QAAQ;gBAAI;YAElB;YAEA,4BAA4B;YAC5B,MAAM,cAAc,GAAG,IAAI;YAC3B,MAAM,WAAW;YAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF;QAEA,sCAAsC;QACtC,IAAI,iBAAiB;QACrB,IAAI,QAAQ;YACV,MAAM,cAAc,OAAO,WAAW;YACtC,iBAAiB,OAAO,MAAM,CAAC,CAAA,QAC7B,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACnC,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,WAAW,GAAG,QAAQ,CAAC,iBAC3D,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACtC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;QAEtD;QAEA,4CAA4C;QAC5C,eAAe,IAAI,CAAC,CAAC,GAAG,IACtB,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO;QAG3E,mBAAmB;QACnB,MAAM,kBAAkB,eAAe,KAAK,CAAC,QAAQ,SAAS;QAE9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,QAAQ;gBACR,OAAO,eAAe,MAAM;gBAC5B;gBACA;gBACA,SAAS,SAAS,QAAQ,eAAe,MAAM;YACjD;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,yHAAA,CAAA,iBAAc,CAAC,SAAS;YAC/B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,QAAe;QAErB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,KAAK,EAAE;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkC,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,MAAM;QAErB,gCAAgC;QAChC,MAAM,gBAAgB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;QAC7D,IAAI,iBAAiB,GAAG;YACtB,wBAAwB;YACxB,MAAM,CAAC,cAAc,GAAG;gBAAE,GAAG,MAAM,CAAC,cAAc;gBAAE,GAAG,KAAK;YAAC;QAC/D,OAAO;YACL,gBAAgB;YAChB,OAAO,IAAI,CAAC;QACd;QAEA,MAAM,WAAW;QAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS,iBAAiB,IAAI,+BAA+B;QAC/D;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,yHAAA,CAAA,iBAAc,CAAC,SAAS;YAC/B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAuB,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,MAAM;QACrB,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAElD,IAAI,eAAe,CAAC,GAAG;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkB,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,0BAA0B;QAC1B,MAAM,eAAe,OAAO,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE;QACpD,MAAM,WAAW;QAEjB,8EAA8E;QAC9E,gCAAgC;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,yHAAA,CAAA,iBAAc,CAAC,SAAS;YAC/B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
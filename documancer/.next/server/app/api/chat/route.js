(()=>{var a={};a.id=276,a.ids=[276],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7626:(a,b,c)=>{"use strict";c.d(b,{JV:()=>p,Nb:()=>o,ik:()=>j,jw:()=>m,lV:()=>l,pt:()=>n,rA:()=>k});var d=c(52525),e=c(31887),f=c(47998),g=c(34405);let h={SUMMARIZE:e.Hh.fromTemplate(`
    You are an expert academic paper analyst. Please provide a comprehensive summary of the following research paper.

    Paper Content:
    {content}

    Please provide:
    1. A concise abstract summary (2-3 sentences)
    2. Main research question and objectives
    3. Key methodology used
    4. Primary findings and contributions
    5. Significance and implications

    Format your response in clear, structured sections.
  `),EXTRACT_KEY_FINDINGS:e.Hh.fromTemplate(`
    Analyze the following research paper and extract the key findings and contributions.

    Paper Content:
    {content}

    Please identify and list:
    1. Main research findings (numbered list)
    2. Novel contributions to the field
    3. Experimental results and their significance
    4. Theoretical insights or frameworks introduced
    5. Practical applications or implications

    Be specific and cite relevant sections when possible.
  `),EXPLAIN_METHODOLOGY:e.Hh.fromTemplate(`
    Analyze the methodology section of this research paper and provide a clear explanation.

    Paper Content:
    {content}

    Please explain:
    1. Research design and approach
    2. Data collection methods
    3. Analysis techniques used
    4. Tools and technologies employed
    5. Experimental setup (if applicable)
    6. Validation methods
    7. Limitations of the methodology

    Make the explanation accessible to researchers in related fields.
  `),EXTRACT_CONCEPTS:e.Hh.fromTemplate(`
    Identify and explain the key concepts, terms, and technical vocabulary from this research paper.

    Paper Content:
    {content}

    For each important concept, provide:
    1. Term/Concept name
    2. Clear definition in context
    3. Importance level (High/Medium/Low)
    4. Related terms or concepts
    5. How it's used in this specific paper

    Focus on domain-specific terminology and novel concepts introduced.
  `),ANSWER_QUESTION:e.Hh.fromTemplate(`
    You are an AI assistant helping researchers understand academic papers. Answer the following question based on the paper content provided.

    Paper Content:
    {content}

    Question: {question}

    Please provide a comprehensive answer that:
    1. Directly addresses the question
    2. References specific sections of the paper when relevant
    3. Provides context and background if needed
    4. Mentions any limitations or uncertainties
    5. Suggests related questions or areas for further exploration

    If the question cannot be answered from the provided content, clearly state this and explain why.
  `),COMPARE_PAPERS:e.Hh.fromTemplate(`
    Compare and analyze the following research papers. Identify similarities, differences, and relationships between them.

    Paper 1:
    {paper1}

    Paper 2:
    {paper2}

    Please provide:
    1. Common themes and research areas
    2. Methodological similarities and differences
    3. Complementary findings or conflicting results
    4. How the papers build upon or relate to each other
    5. Gaps that could be addressed by combining insights
    6. Recommendations for researchers interested in this area

    Structure your comparison clearly with specific examples from both papers.
  `),ANALYZE_CITATIONS:e.Hh.fromTemplate(`
    Analyze the citations and references in this research paper to understand its academic context.

    Paper Content:
    {content}

    Please identify:
    1. Key foundational works cited
    2. Recent developments referenced
    3. Main research communities or schools of thought
    4. Gaps in the literature identified by the authors
    5. How this work positions itself relative to existing research
    6. Potential future research directions suggested

    Focus on understanding the academic landscape and research trajectory.
  `)},i=a=>{let b=new d.Db({modelName:"deepseek-chat",openAIApiKey:process.env.DEEPSEEK_API_KEY,configuration:{baseURL:process.env.DEEPSEEK_BASE_URL||"https://api.deepseek.com/v1"},temperature:.1,maxTokens:4e3}),c=new f.oG;return g.zZ.from([a,b,c])},j=async a=>{let b=i(h.SUMMARIZE);return await b.invoke({content:a})},k=async a=>{let b=i(h.EXTRACT_KEY_FINDINGS);return await b.invoke({content:a})},l=async a=>{let b=i(h.EXPLAIN_METHODOLOGY);return await b.invoke({content:a})},m=async a=>{let b=i(h.EXTRACT_CONCEPTS);return await b.invoke({content:a})},n=async(a,b)=>{let c=i(h.ANSWER_QUESTION);return await c.invoke({content:a,question:b})},o=async(a,b)=>{let c=i(h.COMPARE_PAPERS);return await c.invoke({paper1:a,paper2:b})},p=async a=>{let b=i(h.ANALYZE_CITATIONS);return await b.invoke({content:a})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77598:a=>{"use strict";a.exports=require("node:crypto")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86890:(a,b,c)=>{"use strict";c.d(b,{UU:()=>f,rz:()=>e,vQ:()=>d});let d={name:"DocuMancer",version:"1.0.0",description:"AI-Powered Academic Paper Reading Assistant",maxFileSize:0x3200000,allowedFileTypes:[".pdf"],supportedFormats:["PDF"]},e={SUMMARY:"summary",KEY_FINDINGS:"key_findings",METHODOLOGY:"methodology",CONCEPTS:"concepts",CITATIONS:"citations",COMPARISON:"comparison"},f={FILE_TOO_LARGE:"File size exceeds the maximum limit of 50MB",INVALID_FILE_TYPE:"Only PDF files are supported",UPLOAD_FAILED:"Failed to upload file. Please try again.",PROCESSING_FAILED:"Failed to process the document",API_ERROR:"An error occurred while communicating with the server",NETWORK_ERROR:"Network error. Please check your connection.",GENERIC_ERROR:"An unexpected error occurred"}},88847:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>E,patchFetch:()=>D,routeModule:()=>z,serverHooks:()=>C,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>B});var d={};c.r(d),c.d(d,{GET:()=>y,POST:()=>x});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(7626),w=c(86890);async function x(a){try{let{message:b,paperId:c,paperContent:d,context:e}=await a.json();if(!b)return u.NextResponse.json({success:!1,error:"Message is required"},{status:400});if(!d)return u.NextResponse.json({success:!1,error:"Paper content is required for analysis"},{status:400});let f=await (0,v.pt)(d,b),g={id:`msg_${Date.now()}_assistant`,role:"assistant",content:f,timestamp:new Date,paperId:c,context:e};return u.NextResponse.json({success:!0,data:{message:g}})}catch(a){return console.error("Chat error:",a),u.NextResponse.json({success:!1,error:w.UU.API_ERROR,details:a instanceof Error?a.message:"Unknown error"},{status:500})}}async function y(a){let{searchParams:b}=new URL(a.url),c=b.get("message"),d=b.get("content");if(!c||!d)return u.NextResponse.json({success:!1,error:"Message and content are required"},{status:400});try{let a=new TextEncoder,b=new ReadableStream({async start(b){try{let e=(await (0,v.pt)(d,c)).split(" ");for(let c=0;c<e.length;c++){let d=e[c]+(c<e.length-1?" ":"");b.enqueue(a.encode(`data: ${JSON.stringify({chunk:d})}

`)),await new Promise(a=>setTimeout(a,50))}b.enqueue(a.encode("data: [DONE]\n\n")),b.close()}catch(a){b.error(a)}}});return new Response(b,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"}})}catch(a){return console.error("Streaming chat error:",a),u.NextResponse.json({success:!1,error:w.UU.API_ERROR,details:a instanceof Error?a.message:"Unknown error"},{status:500})}}let z=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/chat/route",pathname:"/api/chat",filename:"route",bundlePath:"app/api/chat/route"},distDir:".next",projectDir:"",resolvedPagePath:"/home/<USER>/project/DocuMancer/new/documancer/src/app/api/chat/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:A,workUnitAsyncStorage:B,serverHooks:C}=z;function D(){return(0,g.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:B})}async function E(a,b,c){var d;let e="/api/chat/route";"/index"===e&&(e="/");let g=await z.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||z.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===z.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>z.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>z.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await z.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await z.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await z.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55,95],()=>b(b.s=88847));module.exports=c})();
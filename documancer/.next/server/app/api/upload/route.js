(()=>{var a={};a.id=413,a.ids=[413],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53783:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>G,patchFetch:()=>F,routeModule:()=>B,serverHooks:()=>E,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>D});var d={};c.r(d),c.d(d,{GET:()=>A,POST:()=>z});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(79748),w=c(33873);class x{static async processPDF(a){try{let b=(await c.e(188).then(c.t.bind(c,36188,23))).default,d=await b(a),e={title:d.info?.Title||void 0,author:d.info?.Author||void 0,subject:d.info?.Subject||void 0,creator:d.info?.Creator||void 0,producer:d.info?.Producer||void 0,creationDate:d.info?.CreationDate?new Date(d.info.CreationDate):void 0,modificationDate:d.info?.ModDate?new Date(d.info.ModDate):void 0,pages:d.numpages},f=this.splitTextIntoPages(d.text,d.numpages);return{text:d.text,metadata:e,pages:f}}catch(a){throw console.error("Error processing PDF:",a),Error("Failed to process PDF file")}}static splitTextIntoPages(a,b){let c=a.split("\n"),d=Math.ceil(c.length/b),e=[];for(let a=0;a<b;a++){let b=a*d,f=Math.min((a+1)*d,c.length),g=c.slice(b,f).join("\n");e.push({pageNumber:a+1,text:g})}return e}static extractPaperMetadata(a,b){let c=a.split("\n").filter(a=>a.trim().length>0),d=b.replace(".pdf","");for(let a of c.slice(0,10))if(a.length>20&&a.length<200&&!a.includes("@")&&!a.includes("http")){d=a.trim();break}let e=this.extractAuthors(a);return{title:d,authors:e,abstract:this.extractAbstract(a),tags:this.extractKeywords(a)}}static extractAuthors(a){let b=[],c=a.split("\n");for(let a=0;a<Math.min(50,c.length);a++){let d=c[a].trim();if(d.match(/^[A-Z][a-z]+ [A-Z][a-z]+(\s*,\s*[A-Z][a-z]+ [A-Z][a-z]+)*$/)){let a=d.split(",").map(a=>a.trim());b.push(...a);break}}return b.length>0?b:["Unknown Author"]}static extractAbstract(a){let b=a.match(/(?:ABSTRACT|Abstract)\s*:?\s*([\s\S]*?)(?:\n\s*\n|\n\s*(?:1\.|I\.|INTRODUCTION|Introduction))/i);if(b)return b[1].trim().substring(0,1e3);for(let b of a.split("\n\n"))if(b.length>100&&b.length<1e3)return b.trim();return"No abstract found"}static extractKeywords(a){let b=[],c=a.match(/(?:Keywords|KEYWORDS|Key words)\s*:?\s*(.*?)(?:\n|$)/i);if(c){let a=c[1].split(/[,;]/).map(a=>a.trim()).filter(a=>a.length>0);b.push(...a)}for(let c of["machine learning","deep learning","neural network","artificial intelligence","natural language processing","computer vision","data mining","algorithm","optimization","classification","regression","clustering","reinforcement learning"])a.toLowerCase().includes(c)&&b.push(c);return[...new Set(b)].slice(0,10)}static async validatePDF(a){try{if(!a.subarray(0,5).toString().startsWith("%PDF-"))return!1;let b=(await c.e(188).then(c.t.bind(c,36188,23))).default;return await b(a),!0}catch{return!1}}static getFileInfo(a){return{size:a.length,type:"application/pdf"}}}var y=c(86890);async function z(a){try{let b=(await a.formData()).get("file");if(!b)return u.NextResponse.json({success:!1,error:"No file provided"},{status:400});if(!b.name.toLowerCase().endsWith(".pdf"))return u.NextResponse.json({success:!1,error:y.UU.INVALID_FILE_TYPE},{status:400});if(b.size>y.vQ.maxFileSize)return u.NextResponse.json({success:!1,error:y.UU.FILE_TOO_LARGE},{status:400});let c=await b.arrayBuffer(),d=Buffer.from(c);if(!await x.validatePDF(d))return u.NextResponse.json({success:!1,error:"Invalid PDF file"},{status:400});let e=(0,w.join)(process.cwd(),"uploads");try{await (0,v.mkdir)(e,{recursive:!0})}catch(a){}let f=Date.now(),g=`${f}_${b.name}`,h=(0,w.join)(e,g);await (0,v.writeFile)(h,d);let i=await x.processPDF(d),j=x.extractPaperMetadata(i.text,b.name),k={id:`paper_${f}`,title:j.title||b.name.replace(".pdf",""),authors:j.authors||["Unknown Author"],abstract:j.abstract||"No abstract available",content:i.text,filePath:h,uploadedAt:new Date,lastAccessedAt:new Date,tags:j.tags||[]};return u.NextResponse.json({success:!0,data:{paper:k,metadata:i.metadata,pages:i.pages.length},message:"File uploaded and processed successfully"})}catch(a){return console.error("Upload error:",a),u.NextResponse.json({success:!1,error:y.UU.UPLOAD_FAILED,details:a instanceof Error?a.message:"Unknown error"},{status:500})}}async function A(){return u.NextResponse.json({success:!1,error:"Method not allowed. Use POST to upload files."},{status:405})}let B=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},distDir:".next",projectDir:"",resolvedPagePath:"/home/<USER>/project/DocuMancer/new/documancer/src/app/api/upload/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:C,workUnitAsyncStorage:D,serverHooks:E}=B;function F(){return(0,g.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:D})}async function G(a,b,c){var d;let e="/api/upload/route";"/index"===e&&(e="/");let g=await B.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||B.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===B.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>B.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>B.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await B.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await B.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await B.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79551:a=>{"use strict";a.exports=require("url")},79748:a=>{"use strict";a.exports=require("fs/promises")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86890:(a,b,c)=>{"use strict";c.d(b,{UU:()=>f,rz:()=>e,vQ:()=>d});let d={name:"DocuMancer",version:"1.0.0",description:"AI-Powered Academic Paper Reading Assistant",maxFileSize:0x3200000,allowedFileTypes:[".pdf"],supportedFormats:["PDF"]},e={SUMMARY:"summary",KEY_FINDINGS:"key_findings",METHODOLOGY:"methodology",CONCEPTS:"concepts",CITATIONS:"citations",COMPARISON:"comparison"},f={FILE_TOO_LARGE:"File size exceeds the maximum limit of 50MB",INVALID_FILE_TYPE:"Only PDF files are supported",UPLOAD_FAILED:"Failed to upload file. Please try again.",PROCESSING_FAILED:"Failed to process the document",API_ERROR:"An error occurred while communicating with the server",NETWORK_ERROR:"Network error. Please check your connection.",GENERIC_ERROR:"An unexpected error occurred"}},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=53783));module.exports=c})();
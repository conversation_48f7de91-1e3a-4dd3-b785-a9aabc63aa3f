(()=>{var a={};a.id=202,a.ids=[202],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57771:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>J,patchFetch:()=>I,routeModule:()=>E,serverHooks:()=>H,workAsyncStorage:()=>F,workUnitAsyncStorage:()=>G});var d={};c.r(d),c.d(d,{GET:()=>C,POST:()=>D});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(79748),w=c(33873),x=c(86890);let y=(0,w.join)(process.cwd(),"data","papers.json");async function z(){try{let a=await (0,v.readFile)(y,"utf-8");return JSON.parse(a)}catch{return[]}}function A(a,b,c){let d=0,e=b.toLowerCase();return a.title.toLowerCase().includes(e)&&(d+=10),a.authors.some(a=>a.toLowerCase().includes(e))&&(d+=8),a.abstract.toLowerCase().includes(e)&&(d+=6),a.tags.some(a=>a.toLowerCase().includes(e))&&(d+=5),a.content&&a.content.toLowerCase().includes(e)&&(d+=3),c.forEach(b=>{switch(b.type){case"author":a.authors.some(a=>a.toLowerCase().includes(b.value.toLowerCase()))&&(d+=15);break;case"tag":a.tags.some(a=>a.toLowerCase().includes(b.value.toLowerCase()))&&(d+=12);break;case"year":new Date(a.uploadedAt).getFullYear().toString()===b.value&&(d+=10)}}),d}function B(a,b){let c=b.toLowerCase();if(a.title.toLowerCase().includes(c))return a.title;if(a.abstract.toLowerCase().includes(c)){let d=a.abstract.toLowerCase().indexOf(c),e=Math.max(0,d-50),f=Math.min(a.abstract.length,d+b.length+50);return"..."+a.abstract.slice(e,f)+"..."}if(a.content&&a.content.toLowerCase().includes(c)){let d=a.content.toLowerCase().indexOf(c),e=Math.max(0,d-100),f=Math.min(a.content.length,d+b.length+100);return"..."+a.content.slice(e,f)+"..."}return a.abstract.slice(0,100)+"..."}async function C(a){try{let{searchParams:b}=new URL(a.url),c=b.get("q")||"",d=b.get("filters"),e=parseInt(b.get("limit")||"20"),f=parseInt(b.get("offset")||"0"),g=[];if(d)try{g=JSON.parse(d)}catch{g=[]}let h=await z();if(!c&&0===g.length){let a=h.slice(f,f+e);return u.NextResponse.json({success:!0,data:{results:a.map(a=>({paperId:a.id,title:a.title,relevanceScore:1,matchedContent:a.abstract.slice(0,100)+"..."})),total:h.length,query:c,filters:g,offset:f,limit:e}})}let i=[];h.forEach(a=>{let b=A(a,c,g);b>0&&i.push({paperId:a.id,title:a.title,relevanceScore:b,matchedContent:B(a,c)})}),i.sort((a,b)=>b.relevanceScore-a.relevanceScore);let j=i.slice(f,f+e);return u.NextResponse.json({success:!0,data:{results:j,total:i.length,query:c,filters:g,offset:f,limit:e,hasMore:f+e<i.length}})}catch(a){return console.error("Search error:",a),u.NextResponse.json({success:!1,error:x.UU.API_ERROR,details:a instanceof Error?a.message:"Unknown error"},{status:500})}}async function D(a){try{let{query:b,filters:c=[],options:d={}}=await a.json();if(!b)return u.NextResponse.json({success:!1,error:"Search query is required"},{status:400});let e=await z(),f=[];e.forEach(a=>{let d=A(a,b,c);d>0&&f.push({paperId:a.id,title:a.title,relevanceScore:d,matchedContent:B(a,b)})}),f.sort((a,b)=>b.relevanceScore-a.relevanceScore);let g=d.limit||20,h=d.offset||0,i=f.slice(h,h+g);return u.NextResponse.json({success:!0,data:{results:i,total:f.length,query:b,filters:c,options:d}})}catch(a){return console.error("Search error:",a),u.NextResponse.json({success:!1,error:x.UU.API_ERROR,details:a instanceof Error?a.message:"Unknown error"},{status:500})}}let E=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/search/route",pathname:"/api/search",filename:"route",bundlePath:"app/api/search/route"},distDir:".next",projectDir:"",resolvedPagePath:"/home/<USER>/project/DocuMancer/new/documancer/src/app/api/search/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:F,workUnitAsyncStorage:G,serverHooks:H}=E;function I(){return(0,g.patchFetch)({workAsyncStorage:F,workUnitAsyncStorage:G})}async function J(a,b,c){var d;let e="/api/search/route";"/index"===e&&(e="/");let g=await E.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,D=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[D]||y.routes[C]);if(F&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||E.isDev||x||(G="/index"===(G=C)?"/":G);let H=!0===E.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>E.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>E.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await E.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await E.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await E.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79748:a=>{"use strict";a.exports=require("fs/promises")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86890:(a,b,c)=>{"use strict";c.d(b,{UU:()=>f,rz:()=>e,vQ:()=>d});let d={name:"DocuMancer",version:"1.0.0",description:"AI-Powered Academic Paper Reading Assistant",maxFileSize:0x3200000,allowedFileTypes:[".pdf"],supportedFormats:["PDF"]},e={SUMMARY:"summary",KEY_FINDINGS:"key_findings",METHODOLOGY:"methodology",CONCEPTS:"concepts",CITATIONS:"citations",COMPARISON:"comparison"},f={FILE_TOO_LARGE:"File size exceeds the maximum limit of 50MB",INVALID_FILE_TYPE:"Only PDF files are supported",UPLOAD_FAILED:"Failed to upload file. Please try again.",PROCESSING_FAILED:"Failed to process the document",API_ERROR:"An error occurred while communicating with the server",NETWORK_ERROR:"Network error. Please check your connection.",GENERIC_ERROR:"An unexpected error occurred"}},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=57771));module.exports=c})();
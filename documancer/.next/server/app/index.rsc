1:"$Sreact.fragment"
2:I[36090,["685","static/chunks/685-ff30a61ec0964427.js","743","static/chunks/743-b850b6273668bc53.js","177","static/chunks/app/layout-5a96f1d30b777854.js"],"default"]
3:I[22306,["685","static/chunks/685-ff30a61ec0964427.js","743","static/chunks/743-b850b6273668bc53.js","177","static/chunks/app/layout-5a96f1d30b777854.js"],"ConfigProvider"]
4:I[87555,[],""]
5:I[31295,[],""]
6:I[90894,[],"ClientPageRoot"]
7:I[38294,["685","static/chunks/685-ff30a61ec0964427.js","970","static/chunks/970-e7a21ad1a3fca30d.js","974","static/chunks/app/page-13d66bec357f581a.js"],"default"]
a:I[59665,[],"OutletBoundary"]
c:I[74911,[],"AsyncMetadataOutlet"]
e:I[59665,[],"ViewportBoundary"]
10:I[59665,[],"MetadataBoundary"]
11:"$Sreact.suspense"
13:I[28393,[],""]
:HL["/_next/static/media/569ce4b8f30dc480-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/93f479601ee12b01-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/f26164f9805769e9.css","style"]
0:{"P":null,"b":"zQimb0PBddNtx20VpTdoh","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/f26164f9805769e9.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"theme":{"token":{"colorPrimary":"#1890ff","colorSuccess":"#52c41a","colorWarning":"#faad14","colorError":"#ff4d4f","colorInfo":"#1890ff","borderRadius":8,"fontFamily":"-apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", sans-serif"},"components":{"Layout":{"headerBg":"#ffffff","siderBg":"#ffffff","bodyBg":"#fafafa"},"Menu":{"itemBg":"transparent","itemSelectedBg":"rgba(24, 144, 255, 0.1)","itemHoverBg":"rgba(0, 0, 0, 0.04)"},"Button":{"primaryShadow":"0 2px 8px rgba(24, 144, 255, 0.2)"},"Card":{"boxShadowTertiary":"0 2px 8px rgba(0, 0, 0, 0.06)"}}},"children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L6",null,{"Component":"$7","searchParams":{},"params":{},"promises":["$@8","$@9"]}],null,["$","$La",null,{"children":["$Lb",["$","$Lc",null,{"promise":"$@d"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$Le",null,{"children":"$Lf"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$L10",null,{"children":["$","div",null,{"hidden":true,"children":["$","$11",null,{"fallback":null,"children":"$L12"}]}]}]]}],false]],"m":"$undefined","G":["$13",[]],"s":false,"S":true}
8:{}
9:"$0:f:0:1:2:children:1:props:children:0:props:params"
f:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
14:I[38175,[],"IconMark"]
d:{"metadata":[["$","title","0",{"children":"DocuMancer - AI-Powered Academic Paper Reading Assistant"}],["$","meta","1",{"name":"description","content":"Sophisticated paper reading assistant with AI-powered analysis, summarization, and Q&A capabilities"}],["$","meta","2",{"name":"author","content":"DocuMancer Team"}],["$","meta","3",{"name":"keywords","content":"academic papers, AI assistant, research, PDF reader, paper analysis"}],["$","link","4",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L14","5",{}]],"error":null,"digest":"$undefined"}
12:"$d:metadata"

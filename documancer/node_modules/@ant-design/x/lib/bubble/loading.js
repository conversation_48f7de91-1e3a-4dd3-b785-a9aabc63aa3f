"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = _interopRequireDefault(require("react"));
const Loading = ({
  prefixCls
}) => /*#__PURE__*/_react.default.createElement("span", {
  className: `${prefixCls}-dot`
}, /*#__PURE__*/_react.default.createElement("i", {
  className: `${prefixCls}-dot-item`,
  key: `item-${1}`
}), /*#__PURE__*/_react.default.createElement("i", {
  className: `${prefixCls}-dot-item`,
  key: `item-${2}`
}), /*#__PURE__*/_react.default.createElement("i", {
  className: `${prefixCls}-dot-item`,
  key: `item-${3}`
}));
var _default = exports.default = Loading;
import * as React from 'react';
import { BubbleContentType } from '../interface';
/**
 * Return typed content and typing status when typing is enabled.
 * Or return content directly.
 */
declare const useTypedEffect: (content: BubbleContentType, typingEnabled: boolean, typingStep: number, typingInterval: number) => [typedContent: React.ReactNode | object, isTyping: boolean];
export default useTypedEffect;

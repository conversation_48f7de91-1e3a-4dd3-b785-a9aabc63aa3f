"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _icons = require("@ant-design/icons");
var React = _interopRequireWildcard(require("react"));
var _ActionButton = _interopRequireDefault(require("./ActionButton"));
function SendButton(props, ref) {
  return /*#__PURE__*/React.createElement(_ActionButton.default, (0, _extends2.default)({
    icon: /*#__PURE__*/React.createElement(_icons.ArrowUpOutlined, null),
    type: "primary",
    shape: "circle"
  }, props, {
    action: "onSend",
    ref: ref
  }));
}
var _default = exports.default = /*#__PURE__*/React.forwardRef(SendButton);
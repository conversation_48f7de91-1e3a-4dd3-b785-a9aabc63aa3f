## [2.0.1](https://github.com/nilzona/path2d-polyfill/compare/v2.0.0...v2.0.1) (2023-01-22)

### Bug Fixes

- add browser and main fields in package.json ([82e8522](https://github.com/nilzona/path2d-polyfill/commit/82e8522b17c750cc06dbade1e7022ffea312fd5d))

## [2.0.0](https://github.com/nilzona/path2d-polyfill/compare/v1.2.3...v2.0.0) (2023-01-22)

### ⚠ BREAKING CHANGES

- target build now has the name `dist/path2d-polyfill.min.js`

- chore: null Path2D in example index.html

- Update tsconfig.json

Co-authored-by: <PERSON><PERSON> <<EMAIL>>

- chore: build for node and browsers

- docs: update doc example

Co-authored-by: <PERSON><PERSON> <<EMAIL>>

### Features

- node support with roundRect polyfill ([#43](https://github.com/nilzona/path2d-polyfill/issues/43)) ([2fffe15](https://github.com/nilzona/path2d-polyfill/commit/2fffe15a76e7adac50beacc0f9587d5b3cc71485))

## [1.2.3](https://github.com/nilzona/path2d-polyfill/compare/v1.2.2...v1.2.3) (2022-10-11)

## [1.2.2](https://github.com/nilzona/path2d-polyfill/compare/v1.2.1...v1.2.2) (2022-07-23)

undefined

### [1.2.1](https://github.com/nilzona/path2d-polyfill/compare/v1.2.0...v1.2.1) (2022-03-06)## [1.2.0](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2021-10-30)

### Bug Fixes

- fixup lint and build ([94be3cd](https://github.com/nilzona/path2d-polyfill/commit/94be3cd38a58f83efc403a58380ff3c23b4f18bf))
- sync version in package.json with tags ([e12e852](https://github.com/nilzona/path2d-polyfill/commit/e12e852ffb13034fa13efd5112c59b6b13a46013))

### [1.1.2](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2021-05-04)

### [1.1.1](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2020-11-16)

### Features

- support for isPointInPath ([605f5d1](https://github.com/nilzona/path2d-polyfill/commit/605f5d188812e472575ccbaba351b9bbc58d3677))

### Bug Fixes

- documentation readme ([5a0abaa](https://github.com/nilzona/path2d-polyfill/commit/5a0abaab6976f10c57ac83e6f6481ed425ac940e))

### [1.0.2](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2020-08-27)

### Bug Fixes

- remove unused css rule ([54e5174](https://github.com/nilzona/path2d-polyfill/commit/54e5174c64f6e84ef891d8830c828efc45c73b93))
- update badge in README.md ([483afe2](https://github.com/nilzona/path2d-polyfill/commit/483afe2b3440083bbb0c3dae74510959055d0f86))

## [1.0.0](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2020-05-27)

### [0.4.2](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2019-08-28)

### [0.4.1](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2019-04-15)

## [0.4.0](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2019-04-12)

### Features

- support ellipse commands ([#7](https://github.com/nilzona/path2d-polyfill/issues/7)) ([02af6d5](https://github.com/nilzona/path2d-polyfill/commit/02af6d55365b8ef70b8eecad88b913e71ae191b2))

### Bug Fixes

- move current position when a path is closed ([#9](https://github.com/nilzona/path2d-polyfill/issues/9)) ([ff6d2f7](https://github.com/nilzona/path2d-polyfill/commit/ff6d2f770f9895be2ac2e947697b8f76e488cb37))

### [0.3.1](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2018-09-26)

### Bug Fixes

- better checks if polyfill is needed ([bd65468](https://github.com/nilzona/path2d-polyfill/commit/bd654681662530d19a0958db22c5990424fb77f2))

## [0.3.0](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2018-09-25)

### [0.2.2](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2018-09-04)

### [0.2.1](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2018-06-05)

## [0.2.0](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2018-06-04)

### [0.1.3](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2018-05-22)

### Bug Fixes

- Avoid reference to global window ([9d3e742](https://github.com/nilzona/path2d-polyfill/commit/9d3e74241eb0652afe22d34523f10f3a3837e179))
- Remove wrong removeChild call ([123db85](https://github.com/nilzona/path2d-polyfill/commit/123db8571ff8b1b6c2fa917b513c74f8215acc37))
- Update README.md ([bcb01e1](https://github.com/nilzona/path2d-polyfill/commit/bcb01e153add2dccf95adaced13646e871086a3a))

### [0.1.1](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2018-05-18)

## [0.1.0](https://github.com/nilzona/path2d-polyfill/compare/v1.1.8...v1.2.0) (2018-05-18)

### Features

- Added examples ([261165c](https://github.com/nilzona/path2d-polyfill/commit/261165cfb0ea3a9910624f719a34a3596f1791d0))
- First version of Path2D polyfill ([a660f19](https://github.com/nilzona/path2d-polyfill/commit/a660f194f416785a40d8d7f5ad451bb54cec15c3))
- Implement addPath ([72d4228](https://github.com/nilzona/path2d-polyfill/commit/72d4228bd6c4dda64fa957ab80e49bd7c7677d8f))

{"name": "react-pdf", "version": "9.1.1", "description": "Display PDFs in your React app as easily as if they were images.", "type": "module", "sideEffects": ["*.css"], "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "source": "./src/index.ts", "types": "./dist/cjs/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "./dist/Page/AnnotationLayer.css": {"import": "./dist/esm/Page/AnnotationLayer.css", "require": "./dist/cjs/Page/AnnotationLayer.css"}, "./dist/Page/TextLayer.css": {"import": "./dist/esm/Page/TextLayer.css", "require": "./dist/cjs/Page/TextLayer.css"}, "./*": "./*"}, "scripts": {"build": "yarn build-js && yarn copy-styles", "build-js": "yarn build-js-esm && yarn build-js-cjs && yarn build-js-cjs-package", "build-js-esm": "tsc --project tsconfig.build.json --outDir dist/esm", "build-js-cjs": "tsc --project tsconfig.build.json --outDir dist/cjs --module commonjs --moduleResolution node --verbatimModuleSyntax false", "build-js-cjs-package": "echo '{\n  \"type\": \"commonjs\"\n}' > dist/cjs/package.json", "clean": "<PERSON><PERSON><PERSON> dist", "copy-styles": "cpy 'src/**/*.css' dist/esm && cpy 'src/**/*.css' dist/cjs", "format": "biome format", "lint": "biome lint", "prepack": "yarn clean && yarn build", "test": "yarn lint && yarn tsc && yarn format && yarn unit", "tsc": "tsc", "unit": "vitest", "watch": "yarn build-js-esm --watch & yarn build-js-cjs --watch & node --eval \"fs.watch('src', () => child_process.exec('yarn copy-styles'))\""}, "keywords": ["pdf", "pdf-viewer", "react"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"clsx": "^2.0.0", "dequal": "^2.0.3", "make-cancellable-promise": "^1.3.1", "make-event-props": "^1.6.0", "merge-refs": "^1.3.0", "pdfjs-dist": "4.4.168", "tiny-invariant": "^1.0.0", "warning": "^4.0.0"}, "devDependencies": {"@biomejs/biome": "1.9.0", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^16.0.0", "@types/node": "*", "@types/react": "*", "@types/react-dom": "*", "@types/warning": "^3.0.0", "canvas": "^2.11.2", "core-js": "^3.37.0", "cpy-cli": "^5.0.0", "jsdom": "^24.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^6.0.0", "typescript": "^5.5.2", "vitest": "^2.0.5"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "publishConfig": {"access": "public", "provenance": true}, "files": ["dist", "src"], "repository": {"type": "git", "url": "git+https://github.com/wojtekmaj/react-pdf.git", "directory": "packages/react-pdf"}, "funding": "https://github.com/wojtekmaj/react-pdf?sponsor=1"}
export declare const PDF_ROLE_TO_HTML_ROLE: {
    Document: null;
    DocumentFragment: null;
    Part: string;
    Sect: string;
    Div: string;
    Aside: string;
    NonStruct: string;
    P: null;
    H: string;
    Title: null;
    FENote: string;
    Sub: string;
    Lbl: null;
    Span: null;
    Em: null;
    Strong: null;
    Link: string;
    Annot: string;
    Form: string;
    Ruby: null;
    RB: null;
    RT: null;
    RP: null;
    Warichu: null;
    WT: null;
    WP: null;
    L: string;
    LI: string;
    LBody: null;
    Table: string;
    TR: string;
    TH: string;
    TD: string;
    THead: string;
    TBody: null;
    TFoot: null;
    Caption: null;
    Figure: string;
    Formula: null;
    Artifact: null;
};
export declare const HEADING_PATTERN: RegExp;
